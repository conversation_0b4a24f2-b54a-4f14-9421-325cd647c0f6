#!/bin/bash

SERVER="************"
USER="root"
PASSWORD="Sinomis123"
REMOTE_DIR="/home/<USER>/html/ybs_h5"
LOCAL_DIR="./dist"

GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}开始部署到服务器 $SERVER...${NC}"

# 删除远程 static 文件夹
echo -e "${GREEN}正在删除远程服务器上的 static 文件夹...${NC}"
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USER@$SERVER" "rm -rf $REMOTE_DIR/static"

# 上传文件，使用 rsync 显示进度条
echo -e "${GREEN}正在上传文件，请稍候...${NC}"
sshpass -p "$PASSWORD" rsync -avh --progress "$LOCAL_DIR/static" "$USER@$SERVER:$REMOTE_DIR/"
sshpass -p "$PASSWORD" rsync -avh --progress "$LOCAL_DIR/index.html" "$USER@$SERVER:$REMOTE_DIR/"

# 检查是否成功
if [ $? -eq 0 ]; then
  echo -e "${GREEN}部署成功！${NC}"
else
  echo -e "${RED}部署失败，请检查错误信息！${NC}"
fi
