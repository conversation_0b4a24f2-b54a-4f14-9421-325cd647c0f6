# wechat

> A Vue.js project

## Build Setup

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report
```

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).

# 杨昌伟交接部分

##注意事项
	1、偶先退出登录
		排查过一轮问题，暂未修改，如修改需要后台配合
		问题点：IOS cookies存储内存不够，部分字节没有存储，安卓可能出现改隐患，目前还没出现该问题
			建议：不把用户信息，全部存入缓存，利用微信授权机制的openID token等从后台获取登录信息，登录状态等
	2、日期组件 daterangepicker组件，修改源码实现，不能自动npm安装，版本更新后，需要手动替换
	3、医帮手，天坛家两个公众号，工单处理流程功能代码一致，可以复用，开单页面，综合服务页面等，页面需要独立开发
	4、综合服务页面，只有自定义工单是写活状态，其他工单页面入口都是写死的，考虑到登录状态，权限控制，这一块整体比较乱，需要重新规划。
	5、我的页面累计菜单维保，资产、医疗盘点，问卷、医废等入口，加上一站式之前的接口，渐渐出臃肿的状态，建议规划菜单，灵活可配置。版本监测入口如不需要，可以屏蔽。
	6、公众号维保隐患：所有的维保用的userId 取的是 SSO 中的 staffId
	7、我的页面，手机号等信息修改，这一块功能建议修改调整
	...