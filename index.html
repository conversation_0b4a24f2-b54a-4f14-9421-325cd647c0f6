<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1.0" />
  <title>后勤服务</title>
  <style>
    html,
    body {
      height: 100%;
    }

    .ybs-icon-comment {
      /* margin: 3vw auto;
        display: block;
        width: 8vw;
        height: 8vw; */
      margin: 1.5vw auto;
      display: block;
      width: 24px;
      height: 24px;
    }

    .icon-comment-success {
      background-image: url(./src/assets/images/iconCommon/icon-toast-success.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
    }

    .icon-comment-warning {
      background-image: url(./src/assets/images/image/iconCommon/icon-toast-warning.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
    }

    .icon-comment-error {
      background-image: url(./src/assets/images/image/iconCommon/icon-toast-error.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
    }

    .icon-common-loading {
      border-radius: 50%;
      border: 3px solid #666666;
      border-left: 3px solid #fafbfc;
      -webkit-animation: circle 3s infinite linear;
    }

    .ybs-toast {
      background: rgba(0, 0, 0, 0.8);
      text-align: center;
      border-radius: 4px;
      color: #ffffff;
      font-family: PingFang SC-Regular, PingFang SC;
      font-size: 16px;
      position: fixed;
      z-index: 3;
      top: 45%;
      left: 50%;
      /* padding: 7vw 10vw; */
      padding: 3vw 5vw;
      transform: translateX(-50%);
      margin-top: -4vw;
      display: none;
    }

    .ybs-toast-content {
      font-size: 14px;
    }

    @-webkit-keyframes circle {
      0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    .loading-home {
      position: absolute;
      /* z-index: 10000; */
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;
    }

    .loading-home .loading {
      position: fixed;
      top: 45%;
      left: 50%;
      z-index: 99999;
      width: 35vw;
      height: 35vw;
      transform: translate(-50%, -50%);
      /* background-image: url(/loading.gif);
        background-size: 100% 100%;
        background-repeat: no-repeat; */
    }

    .loading-home .loading img {
      width: 100%;
      height: 100%;
    }
  </style>
    <% if (htmlWebpackPlugin.options.nodeEnv=='development' ) { %>
      <script src="/static/eruda.js"></script>
    <% } %>
    <% if (htmlWebpackPlugin.options.nodeEnv=='production' ) { %>
      <script src="./static/eruda.js"></script>
    <% } %>
  <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
</head>

<body>
  <div id="app">
    <div class="loading-home">
      <div class="loading">
        <% if (htmlWebpackPlugin.options.nodeEnv=='development' ) { %>
          <img src="/static/loading.gif" alt="" />
          <% } %>
            <% if (htmlWebpackPlugin.options.nodeEnv=='production' ) { %>
              <img src="./static/loading.gif" alt="" />
              <% } %>
      </div>
    </div>
  </div>
  <script>
    eruda.init();
    // 默认设置eruda为隐藏
    document.getElementById('eruda').style.display = 'none'
  </script>
  <script>
    var __PATH;
    var systemType;
    // 能耗账号密码
    var intAccount = {
      username: "gy_energy",
      password: "Gy12345678!1",
    };
    // 能耗默认查询的空间id主院区
    var emodelId = "1574997196833566721";

    var unifiedServer = "http://************:20007";
    // var unifiedServer = "http://**************:9799";
    // var unifiedServer = "http://*************:8025";
    // var unifiedServer = "http://**************:33333";
    // var unifiedServer = "http://**************:6006";
    // var unifiedServer = "http://*************:9301";
    // var unifiedServer = "http://*************:10008";
    // var unifiedServer = "http://*************:20007";
    // 获取当前页面的域名
    if (`<%= process.env.NODE_ENV %>` == "production") {
      // 深圳肿瘤订餐特殊处理
      if (window.location.host == 'szzlhwu.logimis.com') {
        var unifiedServer = window.location.protocol + "//" + 'szzldcybs.logimis.com'
      } else {
        // 获取当前url的协议、域名、端口号
        var unifiedServer = window.location.protocol + "//" + window.location.host;
      }
    }
    __PATH = {
      BASE_PATH: unifiedServer,
      ONESTOP: unifiedServer + "/oneLogistics", // 一站式
      IPSM_URL: unifiedServer + "/inspection", // 巡检
      // IPSM_URL: 'http://*************:8198', // 巡检
      IPSM_URL2: unifiedServer + "/ipsm", // 双预防
      AUTH_WEB: unifiedServer + "/sinomis-authweb", // auth-web
      IMWS_API: unifiedServer + "/medicalWaste", // 医废
      BASE_API: unifiedServer + "/base_info", // 基础信息
      ALARM: unifiedServer + "/sinomis-alarm", // 报警
      WJ_API: unifiedServer + "/sinomis-service-external", // 问卷
      IEMC_API: unifiedServer + "/sinomis-aircondition", // iemc
      Linen_API: unifiedServer + "/linen", // 布草
      NEWS_API: unifiedServer + "/service-message", // 消息公告
      KGCE_API: "http://ihcrssjt.logimis.com:10006/api", // 能耗
      SPACE_API: unifiedServer + "/sinomis-service-manage", // 空间
      DORM: unifiedServer + "/dorm", // 宿舍，抄表
      FILEPREFIX: unifiedServer + "/minio", // minio
      IMEM_API: unifiedServer + "/imem", // 工程
      PLAN_API: unifiedServer + "/preplan", // 演练预案
      LAB_API: 'http://*************:10086/lab', //企业微信
      APPKEY: 'ww744a27875b156135',
      LAB_COURSE: unifiedServer + '/courseLabApi/',
      SPORADIC_API: unifiedServer + '/sinomis-sporadic', // 零星工程
      PREVIEW_URL: unifiedServer + "/preview/onlinePreview?url=", // 文档查看
      BASEURL: unifiedServer + "/labApi", // 245环境
      IMGURL:'http://************:9000/',//本地预览图片
      SINOMIS_CHEMICALWEB: unifiedServer + "/sinomis-chemicalWeb", // 化学品管理
    };

    // 深圳肿瘤医院的ip 用于切换能耗账号及服务ip
    if (unifiedServer.includes("*************")) {
      intAccount = {
        username: "gy_functiom",
        password: "Xm12345678!",
      };
      emodelId = "1724753192417259521";
      __PATH.KGCE_API = "http://*************:20008/sinomis-energy/api";
    }
    // APP登录信息存储.
    apiready = function () {
      console.log("apiready");
      localStorage.clear();
      systemType = api.systemType;
      var userInfo = api.getPrefs({
        sync: true,
        key: "unifiedUserInfo",
      });
      var picPrefix = api.getPrefs({
        sync: true,
        key: "picPrefix",
      });
      userInfo = JSON.parse(userInfo);
      console.log("userInfo", userInfo);
      var unifiedServer = api.getPrefs({
        sync: true,
        key: "unifiedServer",
      });
      console.log("unifiedServer", unifiedServer);
      var oneLogisticInfo = api.getPrefs({
        sync: true,
        key: "oneLogisticuserInfo",
      });
      var fontSizePreference = api.getPrefs({
        sync: true,
        key: "fontSizePreference",
      });
      console.log("fontSizePreference", fontSizePreference);
      __PATH = {
        BASE_PATH: unifiedServer,
        ONESTOP: unifiedServer + "/oneLogistics", // 一站式
        IPSM_URL: unifiedServer + "/inspection", // 巡检
        IPSM_URL2: unifiedServer + "/ipsm", // 双预防
        AUTH_WEB: unifiedServer + "/sinomis-authweb", // auth-web
        IMWS_API: unifiedServer + "/medicalWaste", // 医废
        BASE_API: unifiedServer + "/base_info", // 基础信息
        ALARM: unifiedServer + "/sinomis-alarm", // 报警
        WJ_API: unifiedServer + "/sinomis-service-external", // 问卷
        IEMC_API: unifiedServer + "/sinomis-aircondition", // iemc
        Linen_API: unifiedServer + "/linen", // 布草
        NEWS_API: unifiedServer + "/service-message", // 消息公告
        KGCE_API: "http://ihcrssjt.logimis.com:10006/api", // 能耗
        SPACE_API: unifiedServer + "/sinomis-service-manage", // 空间
        DORM: unifiedServer + "/dorm", // 宿舍，抄表
        FILEPREFIX: unifiedServer + "/minio", // minio
        IMEM_API: unifiedServer + "/imem", // 工程
        PLAN_API: unifiedServer + "/preplan", // 演练预案
        LAB_COURSE: unifiedServer + '/courseLabApi/',
        SPORADIC_API: unifiedServer + '/sinomis-sporadic', // 零星工程
        PREVIEW_URL: unifiedServer + "/preview/onlinePreview?url=", // 文档查看
        BASEURL: unifiedServer + "/labApi", // 245环境
        SINOMIS_CHEMICALWEB: unifiedServer + "/sinomis-chemicalWeb", // 化学品管理
      };
      // 深圳肿瘤医院的ip 用于切换能耗账号及ip
      if (unifiedServer.includes("*************")) {
        intAccount = {
          username: "gy_functiom",
          password: "Xm12345678!",
        };
        emodelId = "1724753192417259521";
        __PATH.KGCE_API = "http://*************:20008/sinomis-energy/api";
        // __PATH.KGCE_API = unifiedServer + "/sinomis-energy/api";
      }
      if (userInfo.user && userInfo.user.id) {
        localStorage.setItem("loginInfo", JSON.stringify(userInfo.user));
        localStorage.setItem("token", userInfo.token);
        localStorage.setItem("oneLogisticInfo", JSON.stringify(oneLogisticInfo));
      }
      if (picPrefix) {
        localStorage.setItem("picPrefix", picPrefix);
      }
      if (fontSizePreference) {
        localStorage.setItem("fontSizePreference", fontSizePreference);
      }
    };
  </script>
</body>

</html>
