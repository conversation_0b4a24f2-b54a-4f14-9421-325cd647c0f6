import detector from "detector";

function getBrowserInfo() {
  switch (detector.browser.name) {
    case "micromessenger":
      return "weixin";
      break;
    case "chrome":
      return "chrome";
      break;
    case "safari":
      return "apple";
      break;
    default:
      break;
  }
}

function pathTransfer() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (window.api) {
        let loginInfo = api.getPrefs({ sync: true, key: "userInfo" });
        if (loginInfo) return resolve(loginInfo);
        else reject("The userInfo is empty");
      } else {
        reject("Environment Error");
      }
    }, 1000);
  });
}

function preView(imgArr, type) {
  let blobArr = [];
  type = type ? type : "image";
  if (imgArr.length > 0) {
    for (let j = 0; j < imgArr.length; j++) {
      let bytes = window.atob(imgArr[j].split(",")[1]);
      let mime = imgArr[j].split(",")[0].match(/:(.*?);/)[1];
      var ab = new ArrayBuffer(bytes.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      //blobArr.push(URL.createObjectURL(new Blob([ab] ,{type : 'image/png'})));
      if (type == "image") {
        blobArr.push(new File([ia], "image" + j + ".png", { type: mime }));
      } else if (type == "audio") {
        blobArr.push(
          new File([ia], "audio" + j + ".amr", {
            type: mime
          })
        );
      }
    }
  }
  return blobArr;
}
function _getUriBlob(url, cb) {
  var xhr = new XMLHttpRequest();
  xhr.open("get", url, true);
  xhr.responseType = "blob";
  xhr.onload = function() {
    if (this.status == 200) {
      if (cb) cb(this.response);
    }
  };
  xhr.send();
}
function urlToBase64(url) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader();
    _getUriBlob(url, function(blob) {
      reader.readAsDataURL(blob);
    });
    reader.onload = function(e) {
      return resolve({
        base64Data: e.target.result,
        filedata: preView([e.target.result], "audio")
      });
    };
  });
}
/**
 * @param {Function} fun 目标函数
 * @param {Number} delay 延迟执行毫秒数
 * @description 防抖函数
 */
function debounce(fun, delay) {
  return function (args) {
      let that = this
      let _args = args
      clearTimeout(fun.id)
      fun.id = setTimeout(function () {
          fun.call(that, _args)
      }, delay)
  }
}
export { getBrowserInfo, pathTransfer, preView, urlToBase64, debounce };
