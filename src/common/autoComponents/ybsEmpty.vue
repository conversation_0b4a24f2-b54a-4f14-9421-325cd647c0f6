<!--
 * @Author: hedd
 * @Date: 2023-05-19 14:35:40
 * @LastEditTime: 2023-05-22 11:24:10
 * @FilePath: \ybs_h5\src\common\autoComponents\ybsEmpty.vue
 * @Description:
    在使用的页面中设置高度
    例：style="height: calc(90vh - 0.15rem)"
-->
<template>
  <div class="ybs-empty" :style="{height: height}">
    <div class="ybs-empty__image" :style="imageStyle">
      <img v-if="image" :src="image" ondragstart="return false" />
      <img v-else :src="imageUrl" ondragstart="return false" />
    </div>
    <div class="ybs-empty__description">
      <slot v-if="$slots.description" name="description"></slot>
      <p v-else>{{ emptyDescription }}</p>
    </div>
    <div v-if="$slots.default" class="ybs-empty__bottom">
      <slot></slot>
    </div>
  </div>
</template>
<script>
const contentError = require("@/assets/images/noDataDefault/content-error.png"); // 页面异常
const loadingError = require("@/assets/images/noDataDefault/loading-error.png"); // 数据加载失败
const onlineError = require("@/assets/images/noDataDefault/online-error.png"); // 服务器异常
const serverError = require("@/assets/images/noDataDefault/server-error.png"); // 网络异常
const messageEmpty = require("@/assets/images/noDataDefault/message-empty.png"); // 暂无消息！
const chartEmpty = require("@/assets/images/noDataDefault/chart-empty.png"); // 数据为空（暂无echarts图表）
const searchEmpty = require("@/assets/images/noDataDefault/search-empty.png"); // 搜索无结果
export default {
  name: "ybsEmpty",
  props: {
    image: {
      type: String,
      default: ""
    },
    imageSize: Number, // 仅支持px
    description: {
      type: String,
      default: ""
    },
    imageType: {
      type: String,
      default: ""
    },
    height: {
      type: String,
      required: false,
      default: ""
    }
  },
  data() {
    const imageList = {
      contentError: {
        image: contentError,
        description: "页面异常"
      },
      loadingError: {
        image: loadingError,
        description: "数据加载失败"
      },
      onlineError: {
        image: onlineError,
        description: "服务器异常"
      },
      serverError: {
        image: serverError,
        description: "网络异常"
      },
      messageEmpty: {
        image: messageEmpty,
        description: "暂无消息！"
      },
      chartEmpty: {
        image: chartEmpty,
        description: "数据为空"
      },
      searchEmpty: {
        image: searchEmpty,
        description: "搜索无结果"
      }
    };
    return {
      imageList: Object.freeze(imageList)
    };
  },
  computed: {
    imageUrl() {
      return this.imageType
        ? this.imageList[this.imageType].image
        : searchEmpty;
    },
    emptyDescription() {
      return (
        this.description ||
        (this.imageType
          ? this.imageList[this.imageType].description
          : "搜索无数据")
      );
    },
    imageStyle() {
      return {
        width: this.imageSize ? `${this.imageSize}px` : ""
      };
    }
  }
};
</script>
<style lang="scss" scoped>
.ybs-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  box-sizing: border-box;
  background: #fff;
  padding-bottom: 35%;
}
.ybs-empty__image {
  width: 3rem;
}
.ybs-empty__image img {
  user-select: none;
  width: 100%;
  height: 100%;
  vertical-align: top;
  object-fit: contain;
}
.ybs-empty__description {
  margin-top: 0px;
}
.ybs-empty__description p {
  margin: 10px;
  font-size: 0.3rem;
  color: #4e5969;
}
.ybs-empty__bottom {
  margin-top: 20px;
}
</style>
