<template>
  <div class="content-box">
    <div class="header">
      <span class="left-icon" @click="handleBack" v-if="showLeftBtn">
        <van-icon name="arrow-left" />
      </span>
      <span class="title" @click="handleTitleClick">{{ title }}</span>
      <span class="right-icon" v-if="showRightBtn" @click="scanCode">
        <slot></slot>
        <van-icon v-if="rightIcon" :name="rightIcon" />
      </span>
    </div>
    <div class="place-box"></div>
  </div>
</template>

<script>
export default {
  name: "Header",
  props: {
    title: {
      type: String,
      default: ""
    },
    leftIcon: {
      type: String,
      default: "icon-back"
    },
    rightIcon: {
      type: String,
      default: ""
    },
    rightText: {
      type: String,
      default: ""
    },
    leftText: {
      type: String,
      default: "返回"
    },
    bgColor: {
      type: String,
      default: "#00CAC8"
    },
    showLeftBtn: {
      type: <PERSON>olean,
      default: () => true
    },
    showRightBtn: {
      type: Boolean,
      default: () => true
    },

  },
  data() {
    return {
      clickCount: 0,
      lastClickTime: 0
    };
  },
  methods: {
    handleTitleClick() {
      const currentTime = new Date().getTime();
      if (currentTime - this.lastClickTime < 300) {
        this.clickCount++;
        if (this.clickCount === 8) {
          this.handleEruda(true);
          this.clickCount = 0;
        }
      } else {
        this.handleEruda(false);
        this.clickCount = 1;
      }
      this.lastClickTime = currentTime;
    },
    handleEruda(flag) {
      console.log(flag);
      document.getElementById('eruda').style.display = flag ? 'block' : 'none'
      return
      if (typeof eruda !== 'undefined' && eruda) {
        eruda.init();
      } else {
        console.warn('Eruda 未定义或不可用');
      }
    },
    handleBack() {
      this.$emit("backFun");
    },
    handleMore() {
      this.$emit("moreFun");
    },
    scanCode() {
      this.$emit("taskScanCode");
    },
  },
  mounted() {
    if (systemType === "ios") {
      document.querySelector(".header").style.height = "12vh";
      document.querySelector(".place-box").style.height = "12vh";
      document.querySelector(".left-icon").style.top = "68%";
      document.querySelector(".title").style.lineHeight = "16vh";
    }
    // document.querySelector(".header").style.height = "12vh";
    // document.querySelector(".place-box").style.height = "12vh";
    // document.querySelector(".left-icon").style.top = "68%";
    // document.querySelector(".title").style.lineHeight = "16vh";
  },
  watch: {
    $route: {
      handler() {
        if (systemType === "ios") {
          document.querySelector(".header").style.height = "12vh";
          document.querySelector(".place-box").style.height = "12vh";
          document.querySelector(".left-icon").style.top = "68%";
          document.querySelector(".title").style.lineHeight = "16vh";
        }
        // document.querySelector(".header").style.height = "12vh";
        // document.querySelector(".place-box").style.height = "12vh";
        // document.querySelector(".left-icon").style.top = "68%";
        // document.querySelector(".title").style.lineHeight = "16vh";
      },
      deep: true
    }
  }
};
</script>
<style lang="scss" scoped>
.header {
  width: 100%;
  height: 10vh;
  background-color: #fff;
  text-align: center;
  position: fixed;
  top: 0;
  z-index: 99999;
}
.van-icon {
  font-size: 20px;
}
.title {
  font-size: 18px;
  line-height: 12vh;
}
.left-icon {
  position: absolute;
  left: 0;
  width: 10%;
  top: 60%;
  transform: translateY(-50%);
}
.right-icon {
  position: absolute;
  right: 5%;
  top: 60%;
  transform: translateY(-50%);
}
.place-box {
  height: 10vh;
}
// [data-layout="iframe"] {
//   .header {
//     height: 60px;
//   }
//   .place-box {
//     height: 60px;
//   }
//   .title {
//     font-size: 18px;
//     line-height: 65px;
//   }
// }
</style>
