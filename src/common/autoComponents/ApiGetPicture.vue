<!--
 * @Description:
-->
<template>
  <div class="get-picture">
    <div class="fixedImg" @click="openSheet"></div>
    <van-action-sheet v-model="showSheet" :closeable="false">
      <div class="content">
        <div @click="uploadFile('camera')">拍照</div>
        <div @click="uploadFile">从手机相册选择</div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
export default {
  name: "ApiGetPicture",
  props: {
    limited: {
      type: Number,
      default: 9
    }
  },
  data() {
    return {
      showSheet: false,
    };
  },
  methods: {
  
    openSheet() {
      if (this.limited >= 9) {
        this.$toast.fail("已达到最大上传数量限制");
      } else {
        this.showSheet = true
      }
    },
    uploadFile(type) {
      this.showSheet = false
      let that = this;
      api.getPicture(
        {
          sourceType: type ? type : 'album',
          mediaValue: "pic",
          destinationType: "base64"
        },
        function (ret, err) {
          if (ret) {
            if (!ret.data) {
              //如果不选择图片，则退出
              return;
            }
            console.log(ret.data);
            const fileLocal = ret.data.split("/");
            var blob = that.$YBS.dataURLtoBlob(ret.base64Data);
            var file = that.$YBS.blobToFile(blob, fileLocal[fileLocal.length - 1]);
            const fileData = {
              base64Data: ret.base64Data,
              file
            };
            that.$emit("submitFile", fileData);
            // console.log(JSON.stringify(ret));
          }
        }
      );
    },
    handleBack() {
      this.$emit("backFun");
    }
  },
  mounted() { }
};
</script>
<style lang="scss" scoped>
.get-picture {
  .fixedImg {
    width: 60px;
    height: 60px;
    // height: 100%;
    // aspect-ratio: 1;
    background: url("../../assets/images/icon/upload-img.png") no-repeat;
    background-size: 100% 100%;
  }
  .content {
    padding: 10px 20px;
    line-height: 1.08rem;
  }
}
</style>

