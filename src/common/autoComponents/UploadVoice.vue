<template>
  <div>
    <div class="btn">
      <button
        class="weui-btn weui-btn_primary"
        :class="{ no_allow_touch: canTalk, 'weui-btn_primary': !canTalk }"
        @touchstart="startTalk"
        @touchend="endTalk"
        @touchcancel="endTalk"
        v-if="hasVoice == 'unvoice'"
        :disabled="canTalk"
      >
        <span class="iconfont">&#58962;</span>
        <span v-if="canTalk">录音功能加载中...</span>
        <span v-else>语音输入</span>
      </button>
      <button class="weui-btn weui-btn_primary play-btn" @click="handlePlayingClick" v-else-if="hasVoice == 'recorded'">
        <img src="@/assets/images/icon_wify2.png" class="play-img" />
        <span>点击播放</span>
      </button>
      <button class="weui-btn weui-btn_primary recording-btn play-btn" v-else-if="hasVoice == 'playRecording'" @click="handlePauseVoiceClick">
        <img src="@/assets/images/icon_wify1.gif" class="play-img" />
        <span>正在播放</span>
      </button>
      <img src="@/assets/images/delete.png" class="delete-voice" v-show="delBtn" @click="handleDelVoiceClick" />
    </div>

    <div class="recording" v-if="recoding">
      <img src="@/assets/images/recording.gif" alt="正在录音中" />
    </div>
  </div>
</template>

<script>
// import wx from "weixin-js-sdk";
let wx = parent.wx;
export default {
  name: "UploadVoice",
  data() {
    return {
      START: 0,
      END: 0,
      recordTimer: null,
      voiceLocalId: "",
      voiceServerId: "",
      hasVoice: "unvoice",
      recoding: false,
      delBtn: false,
      canTalk: true,
      flag: true,
    };
  },
  computed: {},
  mounted() {
    setTimeout(() => {
      this.canTalk = false;
    }, 1500);
  },
  activated() {
    setTimeout(() => {
      this.canTalk = false;
    }, 1500);
  },
  destroyed() {
    wx.stopRecord({});
  },
  methods: {
    /**
     * 开始录音
     */
    startTalk(event) {
      if (!this.canTalk) {
        event.preventDefault();
        this.START = new Date().getTime();
        let _this = this;
        this.recordTimer = setTimeout(function () {
          if (_this.detector.browser.name != "micromessenger") {
            //判断是否为微信浏览器
            _this.$toast.fail("暂不支持录音功能");
            return;
          } else {
            wx.stopRecord({});
            wx.startRecord({
              success: function () {
                _this.recoding = true;
              },
              fail: function (e) {
                _this.$toast.fail(e, "录音功能初始化失败,请刷新后重试");
                console.log("失败回调");
              },
            });
          }
        }, 300);
        // 监听录音自动停止
        if (_this.detector.browser.name == "micromessenger") {
          wx.onVoiceRecordEnd({
            complete: function (res) {
              console.log(res, "已经超过一分钟了！");
              _this.voiceLocalId = res.localId;
              _this.recoding = false;
              _this.hasVoice = "recorded";
              _this.delBtn = true;
              _this.$toast.fail("录音时间已超过一分钟");
            },
          });
        }
      } else {
        return;
      }
    },
    /**
     * 录音结束
     */
    endTalk() {
      console.log("录音结束了~！");
      this.recoding = false;
      if (!this.canTalk) {
        this.END = new Date().getTime();
        if (this.END - this.START < 300) {
          this.END = 0;
          this.START = 0;
          //小于300ms，不录音

          this.hasVoice = "unvoice";
          this.delBtn = false;
          clearTimeout(this.recordTimer);
        } else {
          let _this = this;
          wx.stopRecord({
            success: function (res) {
              console.log(res, "res结束录音停止");
              _this.voiceLocalId = res.localId;
              // 将res.localId传递给父组件
              _this.$emit("getVoiceLocalId", res.localId);
              _this.recoding = false;
              _this.hasVoice = "recorded";
              _this.delBtn = true;
              // _this.$emit("voiceFlag", "true");
            },
            fail: function (res) {
              console.log(JSON.stringify(res), "录音结束失败的结果");
            },
          });
        }
      } else {
        return;
      }
    },
    /**
     * 播放录音
     */
    handlePlayingClick() {
      let _this = this;
      wx.playVoice({
        localId: this.voiceLocalId, // 需要播放的音频的本地ID，由stopRecord接口获得
      });
      this.hasVoice = "playRecording";
      this.delBtn = false;
      wx.onVoicePlayEnd({
        success: function (res) {
          let localId = res.localId; // 返回音频的本地ID
          _this.hasVoice = "recorded";
          _this.delBtn = true;
        },
      });
    },
    /**
     * 暂停播放录音
     */
    handlePauseVoiceClick() {
      wx.stopVoice({
        localId: this.voiceLocalId, // 需要暂停的音频的本地ID，由stopRecord接口获得
      });
      this.hasVoice = "recorded";
      this.delBtn = true;
    },
    /**
     * 保存录音
     */
    saveVoice() {
      $.showLoading("录音上传中…");
      let _this = this;
      //上传语音接口
      wx.uploadVoice({
        localId: _this.voiceLocalId, // 需要上传的音频的本地ID，由stopRecord接口获得
        isShowProgressTips: 0, // 默认为1，显示进度提示
        success: function (res) {
          $.hideLoading();
          _this.voiceServerId = res.serverId; // 返回音频的服务器端ID
          console.log(res, "保存音频----");
          _this.$emit("getvVoiceParams", res.serverId);
        },
        fail: function (res) {
          $.hideLoading();
          console.log("录音上传失败");
        },
      });
    },
    /**
     * 删除录音文件
     */
    handleDelVoiceClick() {
      this.$emit("voiceFlag");
      this.voiceLocalId = "";
      this.voiceServerId = "";
      this.hasVoice = "unvoice";
      this.delBtn = false;
    },
    /**
     * jssdk 参数配置
     * @param res
     */
    setConfig(data) {
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: data.appId, // 必填，公众号的唯一标识
        timestamp: data.timestamp, // 必填，生成签名的时间戳
        nonceStr: data.nonceStr, // 必填，生成签名的随机串
        signature: data.signature, // 必填，签名,
        jsApiList: [
          "scanQRCode",
          "startRecord",
          "stopRecord",
          "onVoiceRecordEnd",
          "playVoice",
          "downloadVoice",
          "stopVoice",
          "onVoicePlayEnd",
          "uploadVoice",
          "chooseImage",
          "uploadImage",
          "downloadImage",
        ], // 必填，需要使用的JS接口列表
      });
      this.configWeiXinVoice();
    },
    /**
     * 微信js ready方法
     */
    configWeiXinVoice() {
      wx.ready(() => {
        wx.checkJsApi({
          jsApiList: [
            "scanQRCode",
            "startRecord",
            "downloadVoice",
            "stopRecord",
            "onVoiceRecordEnd",
            "playVoice",
            "stopVoice",
            "onVoicePlayEnd",
            "uploadVoice",
            "chooseImage",
            "uploadImage",
            "downloadImage",
          ],
          success: (res) => {
            wx.stopRecord({});
            if (!localStorage.WeiXinconf) {
              //消除第一次录音是弹出的录音授权弹窗对用户体验问题
              wx.startRecord({
                success: function () {
                  localStorage.WeiXinconf = true;
                  setTimeout(() => {
                    wx.stopRecord({});
                  }, 500);
                },
                fail: function () {
                  console.log("失败回调");
                },
                complete: function () {
                  console.log("完成回调");
                },
              });
            }
          },
        });
      });
      wx.error((res) => {
        console.log("你的微信版本太低，不支持微信JS接口，请升级到最新的微信版本！");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.weui-btn {
  line-height: 0.7rem;
}
.weui-btn_primary {
  background-color: #3562db;
}
.weui-btn_primary:not(.weui-btn_disabled):active {
  background-color: #3562db;
}
.no_allow_touch {
  background-color: rgb(211, 211, 211) !important;
}
.wrapper {
  // background-color: $bgColor;

  .er-level {
    display: flex;
    align-items: center;

    .weui-cells_checkbox {
      display: flex;
      align-items: center;
      justify-content: center;
      justify-content: space-between;
      width: 100%;

      .redio-content {
        display: flex;
      }
    }
  }

  .desc-form {
    margin-top: 0.2rem;

    .desc {
      padding: 0.32rem 0.32rem 0;
      font-size: 0.28rem;
    }
  }
}

.btn {
  padding: 0 1rem 0.2rem;
  position: relative;
  user-select: none;

  .delete-voice {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.4rem;
  }

  .play-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
    }
  }
}

.recording {
  position: fixed;
  z-index: 2;
  top: 4rem;
  left: 50%;
  margin-left: -1.2rem;

  img {
    width: 2.4rem;
    opacity: 0.7;
  }
}

.bottom {
  margin-bottom: 90px;
}

.title-content {
  margin-top: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .ipt-content {
    text-align: right;
    //   color: $textColor;
  }
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;

  .img-wrapper {
    flex: 1;
    width: 0;
    margin: 0.2rem;
    position: relative;

    img {
      width: 100%;
    }

    .icon-img {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 999;
}

.play-void-img {
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
}
</style>
