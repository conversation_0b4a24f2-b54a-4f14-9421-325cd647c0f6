<template>
  <div class="instructions" style="border: none">
    <div class="explain" style="justify-content: space-between">
      <span style="color: #333" :class="{ star: isRequired }">上传图片</span>
      <p class="upload-img" style="color: #646566">注：上传本地图片，最多上传三张</p>
    </div>
    <div style="width: 95%; margin: 0 auto">
      <van-uploader
        :max-size="maxSize * 1024 * 1024"
        @oversize="onOversize"
        ref="uplodImg"
        accept="image/*"
        v-model="fileList"
        :after-read="afterRead"
        @click-upload="clickUpload"
        @delete="deleteImg"
        :before-read="beforeRead"
        :max-count="3"
        :capture="captureOnly ? 'camera' : undefined"
      />
    </div>
  </div>
</template>

<script>
import detector from "detector";
import { mapState } from "vuex";
import Compressor from "compressorjs";
import YBS from "@/assets/utils/utils.js";
export default {
  name: "UploadImg",
  props: {
    //调用该组件需要传过来允许上传图片的最大数量的大小写
    num: {
      type: Object
    },
    isRequired: {
      type: Boolean,
      default: false
    },
    captureOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      maxSize: 20, // 允许上传图片的最大大小 20mb
      attachmentUrl: "",
      recordList: []
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  methods: {
    emptyImg() {
      console.log("清空缓存");
      this.fileList = [];
    },
    lookDown(file) {
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;

        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function (ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function (ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function (ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      }
    },
    power() {
      // 类似于storage和camera相同权限存在重复校验建议增加hasPermission包裹判断
      // if (!YBS.hasPermission("storage")) {
      //   YBS.monitorDevicePermissions("storage", {}, function() {
      //     YBS.monitorDevicePermissions("camera", {})
      //   })
      // } else {
      //   YBS.monitorDevicePermissions("camera", {})
      // }
      if (!YBS.hasPermission("storage")) {
        let strogePageParam = {
          title: "读取照片使用说明",
          cont: "用于选择、上传头像或巡检报修实勘图等场景"
        };
        YBS.openCustomDialog(strogePageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
                let pageParam = {
                  title: "摄像机权限使用说明",
                  cont: "用于扫描巡检码、空间码、拍照上传等场景"
                };
                YBS.openCustomDialog(pageParam, function () {
                  YBS.reqPermission(["camera"], function (ret) {
                    if (ret && ret.list.length > 0 && ret.list[0].granted) {
                    }
                  });
                });
                return;
              } else {
              }
            }
          });
        });
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        });
        return;
      }
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    clickUpload() {},

    // 文件上传前
    beforeRead(file) {
      // 判断不能超过20m
      if (file.size / 1024 / 1024 > this.maxSize) {
        this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
        return false;
      }
      return true;
    },

    // 上传
    async afterRead(files) {
      console.log("files", files);
      this.fileList.forEach(i => {
        return (i.status = "uploading");
      });
      console.log("压缩前文件大小:", (files.file.size / 1024).toFixed(2), "KB");

      try {
        let fileToUpload = files.file;
        // 判断文件类型，只有图片才压缩
        if (fileToUpload.type.startsWith("image/")) {
          // 走压缩方法
          fileToUpload = await this.compress(fileToUpload);
          console.log("压缩后文件大小:", (fileToUpload.size / 1024).toFixed(2), "KB");
        } else {
          // 压缩视频
          fileToUpload = await this.compressVideo(fileToUpload);
        }

        // 使用formdata上传
        const params = new FormData();
        params.append("file", fileToUpload); // 确保上传的是压缩后的文件或原始文件
        this.subImg(params);
      } catch (error) {
        this.$toast.fail("文件压缩失败，请重试");
      }
    },

    // 压缩视频方法
    async compressVideo(file) {
      return file;
    },

    // 压缩图片方法
    async compress(file) {
      // 异步
      return new Promise((resolve, reject) => {
        // 检查是否为PNG格式
        if (file.type === "image/png") {
          // 创建一个canvas元素用于转换
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          const img = new Image();

          img.onload = () => {
            // 设置canvas尺寸与图片一致
            canvas.width = img.width;
            canvas.height = img.height;

            // 绘制图片到canvas（白色背景）
            ctx.fillStyle = "#FFFFFF";
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);

            // 将canvas转换为blob
            canvas.toBlob(
              blob => {
                // 创建新的File对象（JPG格式）
                const jpgFile = new File([blob], file.name.replace(/\.png$/i, ".jpg"), { type: "image/jpeg", lastModified: Date.now() });

                // 使用转换后的JPG文件进行压缩
                this.compressImage(jpgFile, resolve, reject);
              },
              "image/jpeg",
              0.9
            );
          };

          img.onerror = () => {
            console.error("PNG转JPG失败，使用原始文件压缩");
            this.compressImage(file, resolve, reject);
          };

          // 从File对象创建URL
          img.src = URL.createObjectURL(file);
        } else {
          // 非PNG文件直接压缩
          this.compressImage(file, resolve, reject);
        }
      });
    },

    // 实际执行压缩的方法
    compressImage(file, resolve, reject) {
      new Compressor(file, {
        quality: 0.6, // 压缩质量，0.0到1.0之间，值越小质量越低
        maxWidth: 1920, // 最大宽度
        maxHeight: 1080, // 最大高度
        success(result) {
          // 将 Blob 转换为 File
          const compressedFile = new File([result], file.name, {
            type: result.type,
            lastModified: Date.now()
          });
          resolve(compressedFile);
        },
        error(err) {
          console.error("压缩失败:", err);
          reject(err);
        }
      });
    },
    //删除图片
    deleteImg(e) {
      console.log(e, "eee");
      // this.attachmentUrl = this.attachmentUrl.filter((i) => i.name != e.file.name);
      // this.recordList.forEach(item => {
      //   if (item.name == e.file.name) {
      //     console.log(item.url,'item.url');
      //     this.$emit("delImg", item.url);
      //   }
      // });
      // 找到要删除的图片记录
      const deletedItem = this.recordList.find(item => {
        // 处理可能的 png 转 jpg 情况
        const itemBaseName = item.name.replace(/\.(png|jpg|jpeg)$/i, "");
        const fileBaseName = e.file.name.replace(/\.(png|jpg|jpeg)$/i, "");

        // 比较不带扩展名的文件名部分
        return itemBaseName === fileBaseName || item.name === e.file.name;
      });
      if (deletedItem) {
        console.log("deletedItem", deletedItem.url);
        this.$emit("delImg", deletedItem.url);
        this.recordList = this.recordList.filter(item => item !== deletedItem);
      }
    },
    subImg(params) {
      // params.hospitalCode = this.hospitalCode;
      this.axios
        .post(__PATH.ONESTOP + "/minio/upload", params, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          console.log("uploadres", res);
          if ((res.data.code = "200")) {
            const item = res.data.data.picUrl;
            // this.form.fileList.push(item);
            this.$emit("getImg", item);
            this.recordList.push({
              name: res.data.data.name,
              url: item
            });
            this.fileList.forEach(i => {
              return (i.status = "done");
            });
          }
        });
    }
  },
  created() {
    setTimeout(() => {
      this.power();
    }, 1000);
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
.instructions {
  border-bottom: 4px solid #ebedf0;
  background-color: #fff;
  margin-bottom: 12px;
}

.instructions .explain {
  position: relative;
  background-color: #fff;
  min-height: 48px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.instructions .explain span {
  font-size: 14px;
}

/deep/ .van-uploader__upload-icon {
  color: #86909C;
  transform: translateY(-20%);
}

/deep/ .van-uploader__upload::after {
  content: '添加照片';
  position: absolute;
  top: 68%;
  font-size: 12px;
  color: #86909C;
  width: 100%;
  text-align: center;
}

/deep/ .van-uploader__preview-delete {
  top: 3px;
  right: 3px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.5);
}

/deep/ .van-uploader__preview-delete-icon {
  transform: scale(0.8);
  top: 2px;
  left: 2px;
  font-weight: bold;
}

.upload-img {
  font-size: 12px;
}

.star::before {
  position: absolute;
  left: 7px;
  top: 40%;
  content: '*';
  color: red;
}
</style>
