<template>
  <div>
    <div class="upload-wrapper">
      <div class="title-content">
        <!--<div class="weui-cell__hd">-->
          <label class="title">上传附件
            <span>注：照片最多上传{{capital}}张</span></label>
      </div>
      <div class="img-content">
        <div class="img-wrapper" ref="imgWrapper" v-for="(item,index) of imgLocalIds" :key="index">
          <div class="img-box">
            <img class="img" :src="item" @click="handleGetBigImg(item,index)" ref="image">
          </div>
          <img src="~images/delete.png" class="icon-img" @click="handelDelImg(item,index)">
        </div>
        <div class="img-wrapper" v-if="imgLocalIds.length == 0 || imgLocalIds.length < quantity">
          <div class="img-box">
            <img class="img" src="~images/upload-img/btn-add.png" @click="handleSelectImg">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
// import wx from "weixin-js-sdk";
import detector from "detector";
import { mapState } from "vuex"
export default {
  name: "UploadImg",
  props: {
    num: {
      type: Object, 
    }
  },
  data() {
    return {
      contentNum: 0,
      imgLocalIds: [],
      imgServerId: [],
      imgLocalId: [],
      flag: true, //防止一次打开多个相册
      capital:"" ,//大写数字
      quantity:3,//小写数字
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  methods: {
    /**
     * 选择图片
     */
    handleSelectImg() {
      if (this.flag) {
        //this.flag = true;
        let baseArr = [];
        if (this.imgLocalIds.length >= this.quantity) {
          $.toast(`最多上传${this.quantity}张图片`, "text");
        } else {
          let _this = this;
          let imgNum = this.imgLocalIds.length;
          let countNum=this.quantity-imgNum 
          if(countNum>=9){
            countNum=9
          }
          if (detector.browser.name == "micromessenger") {
            wx.chooseImage({
              count: countNum , // 默认9
              sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
              sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有
              success: function(res) {
                let curImgLocalIds = _this.imgLocalIds;
                _this.imgLocalIds = curImgLocalIds.concat(res.localIds); // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                _this.$emit("imgFlag", "true");
                let uploadCount = 0;
                let newLoaclId = baseArr.concat(res.localIds); //每一遍选择的所有图片
                let localIdLength = newLoaclId.length; //每一遍选择的所有图片

                let upload = function() {
                  wx.uploadImage({
                    localId: newLoaclId[uploadCount], // 需要上传的图片的本地ID，由chooseImage接口获得
                    isShowProgressTips: 1, // 默认为1，显示进度提示
                    success: function(res) {
                      _this.imgServerId.push(res.serverId); // 返回图片的服务器端ID
                      _this.imgLocalId.push(res.localId)
                      // _this.imgIndex.push(res.localId)
                      uploadCount++;
                      if (uploadCount < localIdLength) {
                        upload();
                      } else {
                        setTimeout(() => {
                          _this.$emit("getImgParams", _this.imgServerId,_this.imgLocalId);
                        }, 500);
                      }
                    },
                    fail: function() {
                      $.toast("请稍后重试", "text");
                    }
                  });
                };
                upload();
              },
              complete: function() {
                _this.flag = true;
              }
            });
          } else {
          }
        }
      }
    },
    /**
     * 图片预览
     * @param currentSrc
     */
    handleGetBigImg(currentSrc, index) {
      let _this = this;
      if (detector.browser.name == "micromessenger") {
        wx.previewImage({
          current: currentSrc, // 当前显示图片的http链接
          urls: _this.imgLocalIds // 需要预览的图片http链接列表
        });
      } else {
        //图片浏览
        this.$createImagePreview({
          imgs: _this.imgLocalIds,
          initialIndex: index,
          loop: false,
          speed: 500
        }).show();
        this.contentNum = index;
      }
    },
    fatherComponentDel(itemImgLocalId,idx){
      let arr = this.imgLocalIds;
      arr.splice(arr.indexOf(itemImgLocalId), 1);
      this.imgLocalIds = arr;
      if (arr.length == 0) {
        this.$emit("imgFlag", false);
      }
    },
    /**
     * 点击右上角x删除图片
     * @param itemImgLocalId
     */
    handelDelImg(itemImgLocalId, idx) {
      let arrId = this.imgServerId;
      arrId.splice(idx, 1);
      this.imgServerId = arrId;
      let tempIndex = this.imgLocalId.findIndex(item=>{
        return item == itemImgLocalId
      });
      tempIndex = tempIndex > 0 ? tempIndex : idx;
      this.imgLocalId.splice(tempIndex,1)
      this.imgLocalIds.splice(tempIndex,1)
      this.$emit("deleteImg",itemImgLocalId,idx);
    },
    /**
     * jssdk参数配置
     * @param res
     */
    setConfig(res) {
      if (res.data.code == 200) {
        let data = res.data.data;
        wx.config({
          debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
          appId: data.appId, // 必填，公众号的唯一标识
          timestamp: data.timestamp, // 必填，生成签名的时间戳
          nonceStr: data.nonceStr, // 必填，生成签名的随机串
          signature: data.signature, // 必填，签名
          jsApiList: [
            "scanQRCode",
            "startRecord",
            "stopRecord",
            "onVoiceRecordEnd",
            "playVoice",
            "downloadVoice",
            "stopVoice",
            "onVoicePlayEnd",
            "uploadVoice",
            "chooseImage",
            "uploadImage",
            "downloadImage"
          ] // 必填，需要使用的JS接口列表
        });
        this.configWeiXinImg();
      } else {
        $.toast(res.data.data.message, "text");
      }
    },
    /**
     * 微信js ready方法
     */
    configWeiXinImg() {
      wx.ready(() => {
        wx.checkJsApi({
          jsApiList: [
            "scanQRCode",
            "startRecord",
            "stopRecord",
            "onVoiceRecordEnd",
            "playVoice",
            "downloadVoice",
            "stopVoice",
            "onVoicePlayEnd",
            "uploadVoice",
            "chooseImage",
            "uploadImage",
            "downloadImage"
          ],
          success: res => {
            console.log("当前客户端版本支持指定JS接口");
          }
        });
      });
      wx.error(res => {
        console.log(
          "你的微信版本太低，不支持微信JS接口，请升级到最新的微信版本！"
        );
      });
    },
    limited(value){
      this.capital=value.capital;
      this.quantity=value.quantity;
    }
  },
  mounted(){
    console.log(this.num);
    this.limited(this.num)      
  }

};
</script>


<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.title-content {
  itemBaseStyle();
  width: calc(100% - .64rem)
  display: flex;
  align-items: center;
  justify-content: space-between;
  span {
    float: right
    color: $textColor
    font-size: .28rem
  }
  .ipt-content {
    text-align: right;
    color: $textColor;
  }
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  overflow : auto;
  display: flex;
  marginBottom20()

  .img-wrapper {
    flex-shrink:0;
    width: 30%;
    height: 1.6rem;
    margin: 0.2rem;
    position: relative; 

    .img-box {
      height: 100%;
      overflow: hidden;

      .img {
        width: 100%;
      }
    }

    .icon-img {
      position: absolute;
      top: -0.1rem;
      right: -0.1rem;
      width: 0.5rem;
    }
  }
}

.title {
  font-size: 0.32rem;
  width: 100%
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  margin-top: 0.2rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
}

</style>
