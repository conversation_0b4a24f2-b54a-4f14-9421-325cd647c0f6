<template>
  <div class="flow-wrapper">
    <van-steps direction="vertical" :active="flows.length" active-color="#3562DB">
      <template v-for="(item, index) in flows">
        <van-step v-if="!['candidate', 'sequenceFlow'].includes(item.historyActivityType)" :key="index">
          <div class="flow-card">
            <p>
              {{ item.assigneeName }} 在 [{{ item.createTime }}] 开始处理 [{{ item.historyActivityType == "endEvent" ? "结束" : item.historyActivityName || "未命名" }}] 环节
            </p>
            <p v-if="item.historyActivityDurationTime">任务历时 [{{ item.historyActivityDurationTime }}]</p>
            <template v-if="item.attachments && item.attachments.length > 0">
                <div v-if="getSignList(item.attachments, true).length">
                  <div style="display: flex; justify-content: space-between">
                    <span>签字:</span>
                    <div style="flex: 1; margin: 0 0 0 4px">
                      <yx-upload :value="getSignList(item.attachments, true)" :config="{ disabled: true }"  :props="{ label: 'name', value: 'url' }"></yx-upload>
                    </div>
                  </div>
                </div>
                <div v-else style="display: flex; justify-content: space-between">
                  <span>附件:</span>
                  <div style="flex: 1; margin: -15px 0 0 4px">
                    <yx-upload :value="getSignList(item.attachments, false)" :config="{ disabled: true }" :props="{ label: 'name', value: 'url' }"></yx-upload>
                  </div>
                </div>
              </template>
            <template v-if="item.comments && item.comments.length > 0">
              <p v-for="(comment, idx) in item.comments.filter(c => c.action === 'AddComment')" :key="idx">
                <template v-if="idx < 1 || isExpanded">
                  <p><span v-if="comment.type === 'comment'">审批结果: [通过]</span> <span v-if="comment.type === 'rollbackComment'">审批结果: [驳回]</span></p>
                  <span v-if="commentMap[comment.type]">{{ commentMap[comment.type] }}: [{{ comment.fullMessage }}]</span>
                  <span v-if="comment.time">[{{ comment.time }}]</span>
                </template>
              </p>

              <van-button v-if="item.comments.filter(c => c.action === 'AddComment').length > 1" size="small" type="primary" plain @click="isExpanded = !isExpanded">
                {{ isExpanded ? "收起" : "展开" }}
              </van-button>
            </template>

            <p v-if="item.endTime">结束时间: [{{ item.endTime }}]</p>
          </div>
        </van-step>
      </template>
    </van-steps>
  </div>
</template>

<script>
import YxUpload from "./workFlowForm/formComp/upload.vue";
export default {
  name: 'FlowInfo',
  components: {
    YxUpload
  },
  props: {
    flows: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isExpanded: false,
      commentMap: {
        assigneeComment: "变更审核人",
        dispatchComment: "调度",
        transferComment: "转办",
        delegateComment: "委托",
        rollbackComment: "驳回意见",
        terminateComment: "终止意见",
        addMultiInstanceComment: "加签",
        deleteMultiInstanceComment: "减签",
        withdrawComment: "撤销",
        recallComment: "撤回",
        deleteProcessComment: "删除流程",
        comment: "备注"
      }
    }
  },
  methods: {
    // 获取签字列表，isSign为true时，只返回签字列表，为false时，返回所有附件列表
    getSignList(attachments, isSign) {
      return attachments.filter(e => isSign ? e.type == 'shouqian' : e.type != 'shouqian')
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-wrapper {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.sign-img {
  display: flex;
  span {
    flex-shrink: 0;
  }
}
</style>
