import { DIC_PROPS, typeList, CHILDREN_LIST } from './variable.js'

/**
 * 判断是否为空
 */
export function validateNull(val) {
  // 特殊判断
  if (val && parseInt(val) === 0) return false
  const list = ['$parent']
  if (val instanceof Date || typeof val === 'boolean' || typeof val === 'number') return false
  if (val instanceof Array) {
    if (val.length === 0) return true
  } else if (val instanceof Object) {
    val = deepClone(val)
    list.forEach((ele) => {
      delete val[ele]
    })
    for (var o in val) {
      return false
    }
    return true
  } else {
    if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '') {
      return true
    }
    return false
  }
  return false
}

/**
 * 对象深拷贝
 */
export const deepClone = (data) => {
  var type = getObjType(data)
  var obj
  if (type === 'array') obj = []
  else if (type === 'object') obj = {}
  else return data
  if (type === 'array') {
    for (var i = 0, len = data.length; i < len; i++) {
      data[i] = (() => {
        if (data[i] === 0) {
          return data[i]
        }
        return data[i]
      })()
      if (data[i]) {
        delete data[i].$parent
      }
      obj.push(deepClone(data[i]))
    }
  } else if (type === 'object') {
    for (var key in data) {
      if (data) {
        delete data.$parent
      }
      obj[key] = deepClone(data[key])
    }
  }
  return obj
}

/**
 * 验证是否存在true/false
 */
export const validData = (val, def) => {
  if (typeof val === 'boolean') {
    return val
  }
  return !validateNull(val) ? val : def
}

export const getObjType = (obj) => {
  var toString = Object.prototype.toString
  var map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object'
  }
  return map[toString.call(obj)]
}

export function findObject(list = [], value, prop = 'prop') {
  let result
  result = findNode(list, { value: prop }, value)
  if (!result) {
    for (let i = 0; i < list.length; i++) {
      let ele = list[i]
      if (ele.type === 'group') {
        result = findGroup(ele.children, value, prop, true)
        if (result && result != -1) return result
      } else {
        if (ele.column) {
          result = findNode(ele.column, { value: prop }, value)
          if (result && result != -1) return result
        } else if (ele.children && CHILDREN_LIST.includes(ele.type)) {
          result = findNode(ele.children.column, { value: prop }, value)
          if (result && result != -1) return result
        }
      }
    }
  }
  return result
}

export const findGroup = (dic, value, valueKey, obj) => {
  for (let i = 0; i < dic.length; i++) {
    let children = dic[i]
    for (let j = 0; j < children.column.length; j++) {
      let col = children.column[j]
      if (col[valueKey] === value) {
        return obj ? col : j
      }
    }
  }
  return -1
}

/**
 * 根据值查找对应的序号
 */
export const findArray = (dic, value, valueKey, obj) => {
  valueKey = valueKey || DIC_PROPS.value
  for (let i = 0; i < dic.length; i++) {
    if (dic[i][valueKey] === value) {
      return obj ? dic[i] : i
    }
  }
  return -1
}

export const findNode = (list = [], props = {}, value) => {
  let valueKey = props.value || DIC_PROPS.value
  let childrenKey = props.children || DIC_PROPS.children
  for (let i = 0; i < list.length; i++) {
    const ele = list[i]
    if (ele[valueKey] == value) {
      return ele
    } else if (ele[childrenKey] && Array.isArray(ele[childrenKey])) {
      let node = findNode(ele[childrenKey], props, value)
      if (node) return node
    }
  }
}

/**
 * 字符串数据类型转化
 */
export const detailDataType = (value, type) => {
  if (validateNull(value)) return value
  if (type === 'number') {
    return Number(value)
  } else if (type === 'string') {
    return value + ''
  } else {
    return value
  }
}

export function getAsVal(obj, bind = '') {
  let result = deepClone(obj)
  if (validateNull(bind)) return result
  bind.split('.').forEach((ele) => {
    result = !validateNull(result[ele]) ? result[ele] : ''
  })
  return result
}

export function createObj(obj, bind) {
  let list = bind.split('.')
  let first = list.splice(0, 1)[0]
  let deep = {}
  deep[first] = {}
  if (list.length >= 2) {
    let start = '{'
    let end = '}'
    let result = ''
    list.forEach((ele) => {
      result = `${result}${start}"${ele}":`
    })
    result = `${result}""`
    for (let i = 0; i < list.length; i++) {
      result = `${result}${end}`
    }
    result = JSON.parse(result)
    deep[first] = result
  }
  obj = extend(true, obj, deep)
  return obj
}

export function extend() {
  var target = arguments[0] || {}
  var deep = false
  var arr = Array.prototype.slice.call(arguments)
  var i = 1
  var options, src, key, copy
  var isArray = false
  if (typeof target === 'boolean') {
    deep = target
    i++
    target = arguments[1]
  }
  for (; i < arr.length; i++) {
    // 循环传入的对象数组
    if ((options = arr[i]) != null) {
      // 如果当前值不是null，如果是null不做处理
      for (key in options) {
        // for in循环对象中key
        copy = options[key]
        src = target[key]
        // 如果对象中value值任然是一个引用类型
        if (deep && (toString.call(copy) === '[object Object]' || (isArray = toString.call(copy) == '[object Array]'))) {
          if (isArray) {
            // 如果引用类型是数组
            // 如果目标对象target存在当前key，且数据类型是数组，那就还原此值，如果不是就定义成一个空数组;
            src = toString.call(src) === '[object Array]' ? src : []
          } else {
            // 如果目标对象target存在当前key，且数据类型是对象，那就还原此值，如果不是就定义成一个空对象;
            src = toString.call(src) === '[object Object]' ? src : {}
          }
          // 引用类型就再次调用extend，递归，直到此时copy是一个基本类型的值。
          target[key] = extend(deep, src, copy)
        } else if (copy !== undefined && copy !== src) {
          // 如果这个值是基本值类型，且不是undefined
          target[key] = copy
        }
      }
    }
  }
  return target
}

/**
 * 设置px像素
 */
export const setPx = (val, defval = '') => {
  if (validateNull(val)) val = defval
  if (validateNull(val)) return ''
  val = val + ''
  if (val.indexOf('%') === -1) {
    val = val + 'px'
  }
  return val
}

export const loadScript = (type = 'js', url, dom = 'body') => {
  let flag = false
  return new Promise((resolve) => {
    const head = dom == 'head' ? document.getElementsByTagName('head')[0] : document.body
    for (let i = 0; i < head.children.length; i++) {
      let ele = head.children[i]
      if ((ele.src || '').indexOf(url) !== -1) {
        flag = true
        resolve()
      }
    }
    if (flag) return
    let script
    if (type === 'js') {
      script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = url
    } else if (type === 'css') {
      script = document.createElement('link')
      script.rel = 'stylesheet'
      script.type = 'text/css'
      script.href = url
    }
    head.appendChild(script)
    script.onload = function () {
      resolve()
    }
  })
}

export const isMediaType = (url, type) => {
  if (validateNull(url)) return
  if (typeList.audio.test(url) || type == 'audio') {
    return 'audio'
  } else if (typeList.video.test(url) || type == 'video') {
    return 'video'
  } else if (typeList.img.test(url) || type == 'img') {
    return 'img'
  }
  return
}

/**
 * 根据字典的value显示label
 */
export const getDicValue = (list, value, props = {}) => {
  if (validateNull(list) || validateNull(value)) return value
  let isArray = Array.isArray(value)
  value = isArray ? value : [value]
  let result = []
  let labelKey = props[DIC_PROPS.label] || DIC_PROPS.label
  let groupsKey = props[DIC_PROPS.groups] || DIC_PROPS.groups
  let dic = deepClone(list)
  dic.forEach((ele) => {
    if (ele[groupsKey]) {
      dic = dic.concat(ele[groupsKey])
      delete ele[groupsKey]
    }
  })
  value.forEach((val) => {
    if (Array.isArray(val)) {
      let array_result = []
      val.forEach((array_val) => {
        let obj = findNode(dic, props, array_val) || {}
        array_result.push(obj[labelKey] || array_val)
      })
      result.push(array_result)
    } else {
      let obj = findNode(dic, props, val) || {}
      result.push(obj[labelKey] || val)
    }
  })
  if (isArray) {
    return result
  } else {
    return result.join('')
  }
}

export const getDicLabel = (list, value, props = {}, row, prop) => {
  if (validateNull(list) || validateNull(value)) return value
  const label = getDicValue(list, value, props)
  if (!row[`$${prop}`]) {
    row[`$${prop}`] = label
  }
  return label
}

/**
 * 获取图片url
 */
export const getImageUrl = (value, props) => {
  if (validateNull(value)) return []
  let isArray = Array.isArray(value)
  let result = []
  let valueKey = props[DIC_PROPS.value] || DIC_PROPS.value
  if (isArray) {
    value.forEach((val) => {
      if (typeof val === 'string') {
        result.push(val)
      } else if (typeof val === 'object') {
        result.push(val[valueKey])
      }
    })
  } else result = (value + '').split(',')
  return result
}

/**
 * 过滤字典数据
 */
export const filterParams = (form, list = ['', '$'], deep = true) => {
  let data = deep ? deepClone(form) : form
  for (let o in data) {
    if (list.includes('')) {
      if (validateNull(data[o])) delete data[o]
    }
    if (list.includes('$')) {
      if (o.indexOf('$') !== -1) delete data[o]
    }
  }
  return data
}

/**
 * 转换空间位置tree
 * @param {a} a 数据源
 * @param {idStr} idStr 文件名称
 * @param {pidStr} pidStr
 * @param {chindrenStr} chindrenStr
 */
export function transData(a, idStr, pidStr, chindrenStr, extraParameter) {
  let r = [],
    hash = {},
    id = idStr,
    pid = pidStr,
    children = chindrenStr,
    i = 0,
    j = 0,
    len = a.length
  for (; i < len; i++) {
    hash[a[i][id]] = a[i]
  }
  for (; j < len; j++) {
    let aVal = a[j],
      hashVP = hash[aVal[pid]]
    if (hashVP) {
      !hashVP[children] && (hashVP[children] = [])
      hashVP[children].push(aVal)
    } else {
      r.push(aVal)
    }
    //            查找已部署节点id集
    if (extraParameter && aVal.state == '1') extraParameter.push(aVal.id)
  }
  return r
}
