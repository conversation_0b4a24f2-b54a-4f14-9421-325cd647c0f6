export const KEY_COMPONENT_NAME = 'yx-'

export const DIC_PROPS = {
  nodeKey: 'id',
  label: 'label',
  value: 'value',
  desc: 'desc',
  groups: 'options',
  title: 'title',
  leaf: 'leaf',
  children: 'children',
  labelText: '名称',
  disabled: 'disabled'
};
export const DIC_HTTP_PROPS = {
  name: 'name',
  url: 'url',
  fileName: 'file',
  res: ''
};
export const DATE_LIST = [
  'year',
  'month',
  'date',
  'dates',
  'datetime',
  'week',
  'datetimerange',
  'daterange',
  'monthrange',
];
export const CHILDREN_LIST = ['table', 'dynamic']
export const ARRAY_LIST = ['img', 'array', 'url'];
export const MULTIPLE_LIST = ['cascader', 'tree', 'select'];
export const RANGE_LIST = ['slider']
export const ARRAY_VALUE_LIST = ARRAY_LIST.concat(['upload', 'dynamic', 'map', 'checkbox', 'cascader', 'dynamic', 'timerange', 'monthrange', 'daterange', 'datetimerange', 'dates']);
export const SELECT_LIST = DATE_LIST.concat(['select', 'checkbox', 'radio', 'cascader', 'tree', 'color', 'icon', 'table', 'map', 'time']);
export const DIC_LIST = ['select', 'checkbox', 'radio', 'cascader', 'tree', 'switch'];
export const DIC_SHOW_SPLIT = ' | ';
export const DIC_SPLIT = ',';
export const typeList = {
  img: /\.(gif|jpg|jpeg|png|webp|svg|GIF|JPG|JPEG|PNG|WEBP|SVG)/,
  video: /\.(swf|avi|flv|mpg|rm|mov|wav|asf|3gp|mkv|rmvb|ogg|mp4)/,
  audio: /\.(mp3|wav|MP3|WAV)/,
}
export const VIRTUAL_LIST = ['select', 'tree']
