import { validateNull, createObj, detailDataType } from "./index.js";
import { KEY_COMPONENT_NAME, ARRAY_LIST, ARRAY_VALUE_LIST, MULTIPLE_LIST, SELECT_LIST, RANGE_LIST, DATE_LIST, DIC_SPLIT, VIRTUAL_LIST } from './variable.js';

/**
 * 初始化数据格式
 */
export const initVal = (value, column) => {
  if (!column) return null
  let { type, multiple, dataType, separator = DIC_SPLIT, alone, emitPath, range, mode, treeCheckable } = column
  let list = value;
  if (
    (MULTIPLE_LIST.includes(type) && ((multiple == true || treeCheckable == true) || ['multiple', 'tags'].includes(mode))) ||
    (ARRAY_VALUE_LIST.includes(type) && emitPath !== false) ||
    (RANGE_LIST.includes(type) && range == true)
  ) {
    if (!Array.isArray(list)) {
      if (validateNull(list)) {
        list = [];
      } else {
        list = (list + '').split(separator) || [];
      }
    }
    // 数据转化
    list.forEach((ele, index) => {
      list[index] = detailDataType(ele, dataType);
    });
    if (ARRAY_LIST.includes(type) && validateNull(list) && alone) list = [''];
  } else {
    list = detailDataType(list, dataType)
  }
  return VIRTUAL_LIST.includes(type) ? (list === '' ? null : list) : list
};

/**
 * 表格初始化值
 */
export const initFormValue = (list = [], init = true) => {
  let form = {}
  for (let i = 0; i < list.length; i++) {
    const ele = list[i]
    if (!ele.prop) continue
    if (
      ARRAY_VALUE_LIST.includes(ele.type) ||
      (MULTIPLE_LIST.includes(ele.type) && ele.multiple) || ele.dataType === 'array'
    ) {
      form[ele.prop] = []
    } else if (RANGE_LIST.includes(ele.type) && ele.range == true) {
      form[ele.prop] = [0, 0]
    } else if (
      ['rate', 'slider', 'number'].includes(ele.type) ||
      ele.dataType === 'number'
    ) {
      form[ele.prop] = undefined
    } else if (['table'].includes(ele.type)) {
      form[ele.prop] = {}
    } else {
      form[ele.prop] = ''
    }
    if (ele.bind) {
      form = createObj(form, ele.bind)
    }
    // 表单默认值设置
    if (init && !validateNull(ele.value)) {
      form[ele.prop] = ele.value
    }
  }
  return form
}

/**
 * 动态获取组件
 */
export const getComponent = (type, component) => {
  let result = type || 'input'
  if (!validateNull(component)) {
    return component
  } else if (ARRAY_LIST.includes(type)) {
    result = 'array'
  } else if (['time', 'timerange'].includes(type)) {
    result = 'time'
  } else if (DATE_LIST.includes(type)) {
    result = 'date'
  } else if (['password', 'textarea', 'search'].includes(type)) {
    result = 'input'
  } else if ('tree' == type) {
    result = 'tree-select'
  }
  return KEY_COMPONENT_NAME + result
}

/**
 * 获取占位符
 */
export const getPlaceholder = (column, type) => {
  const placeholder = column.placeholder
  const label = column.label
  if (type === 'search') {
    const searchPlaceholder = column.searchPlaceholder
    if (!validateNull(searchPlaceholder)) {
      return searchPlaceholder
    } else {
      return label
    }
  } else if (validateNull(placeholder)) {
    if (SELECT_LIST.includes(column.type)) {
      return `请选择 ${label}`
    } else {
      return `请输入 ${label}`
    }
    return label
  }

  return placeholder
}