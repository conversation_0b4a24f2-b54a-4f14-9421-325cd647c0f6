<!--
 * @Description: 日期范围选择组件（仅年月日）
-->
<template>
  <div class="daterange-field">
    <van-field
      :value="displayValue"
      :name="config.prop"
      :label="config.label"
      :disabled="option.disabled || option.detail || config.disabled || config.detail"
      :readonly="true"
      :required="option.disabled || option.detail || config.disabled || config.detail ? false : config.required || option.required"
      :placeholder="config.placeholder || ''"
      :rules="config.rules"
      @click="handleFieldClick"
    />
    
    <van-popup v-model="showPicker" position="bottom" round>
      <div class="daterange-popup">
        <div class="daterange-header">
          <div class="daterange-title">{{ config.label }}</div>
          <div class="daterange-actions">
            <van-button size="small" @click.prevent.stop="cancelPicker">取消</van-button>
            <van-button type="primary" color="#3562db" size="small" @click.prevent.stop="onConfirm">确定</van-button>
          </div>
        </div>
        
        <div class="daterange-content">
          <div class="daterange-section">
            <div class="daterange-section-title">开始日期</div>
            <van-datetime-picker
              v-model="startDate"
              type="date"
              :min-date="minDate"
              :max-date="maxDate"
              :show-toolbar="false"
            />
          </div>
          
          <div class="daterange-section">
            <div class="daterange-section-title">结束日期</div>
            <van-datetime-picker
              v-model="endDate"
              type="date"
              :min-date="startDate"
              :max-date="maxDate"
              :show-toolbar="false"
            />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import moment from "moment";
import { Button } from "vant";

export default {
  name: 'yx-daterange',
  components: {
    "van-button": Button
  },
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    option: Object,
    config: {
      type: Object,
      default() {
        return {
          required: false,
          prop: "",
          label: "日期范围",
          placeholder: "请选择",
          rules: [],
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD'
        };
      },
    },
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPicker: false,
      startDate: new Date(),
      endDate: new Date(moment().add(1, 'day').valueOf()),
      minDate: new Date(2020, 0, 1),
      maxDate: new Date(2025, 11, 31),
    };
  },
  computed: {
    displayValue() {
      if (!this.value || !Array.isArray(this.value) || this.value.length < 2) {
        return '';
      }
      
      // 处理日期显示
      const startStr = this.value[0] ? this.formatDateValue(this.value[0]) : '';
      const endStr = this.value[1] ? this.formatDateValue(this.value[1]) : '';
      
      if (!startStr || !endStr) {
        return '';
      }
      
      return `${startStr} 至 ${endStr}`;
    }
  },
  watch: {
    // 确保结束日期不会早于开始日期
    startDate(val) {
      if (this.endDate < val) {
        this.endDate = val;
      }
    }
  },
  methods: {
    formatDateValue(dateValue) {
      // 统一格式化日期值
      return moment(dateValue).format(this.config.format);
    },
    handleFieldClick() {
      if (this.option.disabled || this.option.detail || this.config.disabled || this.config.detail || this.disabled) {
        return;
      }
      this.showPicker = true;
    },
    cancelPicker(e) {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      this.showPicker = false;
      return false;
    },
    onConfirm(e) {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      this.showPicker = false;
      
      const startFormatted = moment(this.startDate).format(this.config.valueFormat);
      const endFormatted = moment(this.endDate).format(this.config.valueFormat);
      
      this.$emit('input', [startFormatted, endFormatted]);
      
      return false;
    }
  },
  mounted() {
    if (this.value && Array.isArray(this.value) && this.value.length >= 2) {
      if (this.value[0]) {
        this.startDate = moment(this.value[0]).toDate();
      }
      
      if (this.value[1]) {
        this.endDate = moment(this.value[1]).toDate();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.daterange-field {
  width: 100%;
}

.daterange-popup {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.daterange-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebedf0;
}

.daterange-title {
  font-size: 16px;
  font-weight: 500;
}

.daterange-actions {
  display: flex;
  gap: 8px;
}

.daterange-content {
  flex: 1;
  overflow: auto;
}

.daterange-section {
  padding-bottom: 8px;
  
  &:not(:last-child) {
    border-bottom: 1px solid #ebedf0;
  }
}

.daterange-section-title {
  padding: 10px 16px;
  color: #969799;
  font-size: 14px;
  background-color: #f7f8fa;
}
</style> 