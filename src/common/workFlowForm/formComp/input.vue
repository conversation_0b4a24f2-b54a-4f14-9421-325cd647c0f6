<template>
  <van-field
    :value="value"
    :name="config.prop"
    :label="config.label"
    :maxlength="config.maxlength?config.maxlength:config.props&&config.props.maxlength?config.props.maxlength:type==='textarea'?200:undefined"
    :disabled="option.disabled || option.detail || config.disabled || config.detail || disabled"
    :readonly="config.readonly || option.readonly"
    :size="config.size || option.size"
    :required="option.disabled || option.detail || config.disabled || config.detail ? false : config.required || option.required"
    clearable
    :placeholder="config.placeholder"
    :rules="config.rules"
    :type="type"
    :autosize="autosize"
    border
    :show-word-limit="config.maxlength?!!config.maxlength:config.props&&config.props.maxlength?!!config.props.maxlength:type==='textarea'?true:false"
    @input="inputFn"
    @change="changeFn"
  />
</template>
<script>
export default {
  name: 'yx-input',
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    option: Object,
    config: {
      type: Object,
      default() {
        return {
          required: false,
          prop: "",
          label: "输入框",
          placeholder: "请输入",
          rules: [],
          props: {},
          type: 'text',
        };
      },
    },
    value: {
      type: String | Number,
      default() {
        return "";
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      autosize:  { maxHeight: 200, minHeight: 100 }
    };
  },
  mounted() {
    console.log(this.disabled, 'this.disabled');
    if(this.config.value) this.inputFn(this.config.value);
  },
  methods: {
    inputFn(val) {
      this.$emit('input', typeof val === 'string' ? val.trim() : val)
    },
    changeFn(e) {
      const val = e.target.value;
      this.$emit('change', typeof val === 'string' ? val.trim() : val)
    }
  },
  computed: {
    type() {
      if (this.config.type && this.config.type!== 'input') {
        return this.config.type
      } else {
        return "text";
      }
    },
  },
};
</script>

