<!-- <yx-form ref="yxForm" :option="formOption" v-model="formData" :useFormSubmit="false" @formSkipView="skipView" /> -->
<template>
	<div class="yx-dynamic">
		<div class="yx-dynamic__header">
			<div class="yx-dynamic__header-title">{{ config.label }}</div>
		</div>

		<div class="yx-dynamic__content">
			<div class="yx-dynamic__item" v-for="(item, index) in text" :key="index" v-show="item.$display">
				<div class="yx-dynamic__form">
					<!-- 遍历渲染每个字段 -->
					<div v-for="(field, fieldIndex) in children.column" :key="fieldIndex" class="yx-dynamic__field">
						<!-- 直接使用各种表单组件 -->
						<yx-input 
							v-if="typeEnum[field.type] === 'input'" 
							v-model="item[field.prop]" 
							:option="option" 
							:config="field" 
							:disabled="disabled"
						></yx-input>
            
						<yx-select
							v-else-if="field.type === 'select'"
							:value.sync="item[field.prop]"
							:name.sync="item[`$${field.prop}`]"
							:option="option"
							:config="field"
							:disabled="disabled"
						></yx-select>
            
						<yx-checkbox
							v-else-if="['checkbox', 'radio'].includes(field.type)"
							:value.sync="item[field.prop]"
							:name.sync="item[`$${field.prop}`]"
							:option="option"
							:config="field"
							:disabled="disabled"
						></yx-checkbox>
            
						<yx-datetime 
							v-else-if="field.type === 'datetime'" 
							v-model="item[field.prop]" 
							:option="option" 
							:config="field" 
							:disabled="disabled"
						></yx-datetime>
            
						<yx-datetimerange 
							v-else-if="field.type === 'datetimerange'" 
							v-model="item[field.prop]" 
							:option="option" 
							:config="field" 
							:disabled="disabled"
						></yx-datetimerange>
            
						<yx-timerange 
							v-else-if="field.type === 'timerange'" 
							v-model="item[field.prop]" 
							:option="option" 
							:config="field" 
							:disabled="disabled"
						></yx-timerange>
            
						<yx-daterange 
							v-else-if="field.type === 'daterange'" 
							v-model="item[field.prop]" 
							:option="option" 
							:config="field" 
							:disabled="disabled"
						></yx-daterange>
            
						<yx-upload 
							v-else-if="field.type === 'upload'" 
							v-model="item[field.prop]" 
							:option="option" 
							:config="field" 
							:props="field.props || option.props" 
							:disabled="disabled"
						></yx-upload>
					</div>
				</div>
				<div class="yx-dynamic__actions" v-if="children.delBtn">
					<van-button 
						size="small" 
						type="danger" 
						class="yx-dynamic__del-btn" 
						@click.prevent.stop="handleRowDel(index, formType)" 
						:disabled="disabled"
					>
						删除
					</van-button>
				</div>
				<van-divider />
			</div>
		</div>

		<div class="yx-dynamic__add-btn" v-if="children.addBtn">
			<div class="add-btn-wrapper">
				<van-button 
					type="default" 
					plain 
					size="small" 
					icon="plus" 
					@click.prevent.stop="handleRowAdd" 
					:disabled="disabled"
					class="add-text-btn"
				>
					新增
				</van-button>
			</div>
		</div>
        
		<!-- 当没有数据且不可添加时显示空状态 -->
		<div class="yx-dynamic__empty" v-if="(!text || text.length === 0) && disabled">
			<van-empty description="暂无数据" />
		</div>
	</div>
</template>
<script>
import YxInput from "./input.vue";
import YxSelect from "./select.vue";
import YxCheckbox from "./checkbox.vue";
import YxDatetime from "./datetime.vue";
import YxDatetimerange from "./datetimerange.vue";
import YxTimerange from "./timerange.vue";
import YxDaterange from "./daterange.vue";
import YxUpload from "./upload.vue";
import { Button as VanButton, Divider, Empty } from "vant";

export default {
  name: "yx-dynamic",
  model: {
    prop: "value",
    event: "input",
  },
  components: {
    YxInput,
    YxSelect,
    YxCheckbox,
    YxDatetime,
    YxDatetimerange,
    YxTimerange,
    YxDaterange,
    YxUpload,
    VanButton,
    "van-divider": Divider,
    "van-empty": Empty
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    children: {
      type: Object,
      default: () => ({}),
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    props: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // 表单类型（是否为form类型）
    formType() {
      return this.children && this.children.type ? this.children.type : "default";
    }
  },
  data() {
    return {
      text: [], // 表单数据数组
      isInit: false, // 是否已初始化
      typeEnum: {
        digit: "input",
        tel: "input",
        textarea: "input",
        input: "input",
        password: "input",
        number: "input"
      }
    };
  },
  mounted() {
    // 初始化表单数据
    this.text = Array.isArray(this.value) ? [...this.value] : [];
    this.initData();
  },
  watch: {
    // 监听外部传入的value变化
    value: {
      handler(val) {
        // 避免无限循环，只有当值真正变化时才更新
        if (JSON.stringify(val) !== JSON.stringify(this.text)) {
          this.text = Array.isArray(val) ? [...val] : [];
          this.initData();
        }
      },
      deep: true,
    },
    // 监听内部表单数据变化
    text: {
      handler() {
        // 向上发射更新后的数据
        this.$emit("input", [...this.text]);
      },
      deep: true
    }
  },
  methods: {
    /**
     * 初始化表单数据
     */
    initData() {
      if (!this.text || this.text.length === 0) {
        // 如果没有数据，且允许添加，则默认创建一条
        if (this.children && this.children.addBtn && !this.disabled) {
          this.handleRowAdd();
        }
      } else {
        // 处理已有数据，过滤掉隐藏的数据并设置索引
        for (let i = 0; i < this.text.length; i++) {
          let ele = this.text[i];
          if (ele.$display === false) {
            this.text.splice(i, 1);
            i--;
          } else {
            this.text[i] = Object.assign({}, ele, {
              $index: i,
              $display: true,
            });
          }
        }
      }
      this.isInit = true;
    },
    
    /**
     * 处理表单项变更
     */
    handleItemCheck(value) {
      this.$emit("check", value);
      // 表单数据变化时触发input事件，通知外部更新数据
      this.$emit("input", [...this.text]);
    },
    
    /**
     * 处理跳转视图
     */
    handleSkipView(value) {
      this.$emit("skipView", value);
    },
    
    /**
     * 添加新行
     */
    handleRowAdd(e) {
      // 阻止事件冒泡和默认行为
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      let newItem = {
        $index: this.text.length,
        $display: true,
      };
      
      // 向新行数据中添加表单项的默认值
      if (this.children && this.children.column && this.children.column.length > 0) {
        this.children.column.forEach(col => {
          if (col.prop) {
            // 针对不同类型字段设置适当的默认值
            if (col.type === 'datetimerange') {
              // 日期时间范围类型初始化为包含两个空字符串的数组
              newItem[col.prop] = ['', ''];
            } else if (col.type === 'upload') {
              // 上传类型初始化为空数组
              newItem[col.prop] = [];
            } else {
              // 使用配置的默认值，或空字符串
              newItem[col.prop] = col.value !== undefined ? col.value : '';
            }
          }
        });
      }
      
      this.text.push(newItem);
      // 添加行后触发input事件，通知外部更新数据
      this.$emit("input", [...this.text]);
      
      // 返回false阻止默认行为
      return false;
    },
    
    /**
     * 删除行
     * @param {Number} index - 行索引
     * @param {String} type - 表单类型
     */
    handleRowDel(index, type, e) {
      // 阻止事件冒泡和默认行为
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      if (index < 0 || index >= this.text.length) {
        return;
      }
      
      if (type === "form") {
        // form类型不真正删除，只是标记为不显示
        this.text[index].$display = false;
      } else {
        // 直接从数组中移除
        this.text.splice(index, 1);
        
        // 更新剩余项的索引
        for (let i = 0; i < this.text.length; i++) {
          this.text[i].$index = i;
        }
      }
      
      // 删除行后触发input事件，通知外部更新数据
      this.$emit("input", [...this.text]);
      
      // 返回false阻止默认行为
      return false;
    },
    
    /**
     * 获取表单数据
     * @returns {Array} 表单数据
     */
    getData() {
      return this.text.filter(item => item.$display !== false);
    },
    
    /**
     * 验证表单
     * @returns {Boolean} 表单是否有效
     */
    validate() {
      // 如果没有数据，直接返回校验失败
      if (!this.text || this.text.length === 0) {
        return false;
      }
      
      // 遍历所有字段检查必填项
      let valid = true;
      
      // 检查每一行数据
      for (let i = 0; i < this.text.length; i++) {
        const row = this.text[i];
        if (!row.$display) continue;
        
        // 检查每一个字段
        for (const col of this.children.column) {
          if (col.required && (row[col.prop] === undefined || row[col.prop] === '' || 
             (Array.isArray(row[col.prop]) && row[col.prop].length === 0))) {
            valid = false;
            break;
          }
        }
        
        if (!valid) break;
      }
      
      return valid;
    }
  },
};
</script>
<style lang="scss" scoped>
.yx-dynamic {
	width: 100%;
  margin-bottom: 10px;
	
	&__header {
		padding: 8px 0;
		
		&-title {
			font-size: 16px;
			font-weight: bold;
			color: #333;
		}
	}
	
	&__content {
		width: 100%;
	}
	
	&__item {
		position: relative;
		padding: 10px;
		background-color: #fff;
		border-radius: 4px;
	}
	
	&__form {
		display: flex;
		flex-direction: column;
		width: 100%;
	}
	
	&__field {
		margin-bottom: 12px;
		width: 100%;
	}
	
	&__actions {
		display: flex;
		justify-content: flex-end;
		margin-top: 10px;
	}
	
	&__add-btn {
		text-align: center;
		
		.add-btn-wrapper {
			display: inline-block;
      width: 100%;
      background-color: #fff;
		}
		
		.add-text-btn {
			color: #3562db;
			border: none;
			
			&::before {
				border: none;
			}
			
			.van-icon {
				color: #3562db;
				font-weight: bold;
			}
		}
	}
	
	&__empty {
		margin: 20px 0;
		padding: 20px;
		background-color: #f8f8f8;
		border-radius: 4px;
		text-align: center;
	}
}
</style>

