<template>
  <div :style="config.styles" @click="handleClick" class="form-title">
    {{ config.textValue }}
  </div>
</template>

<script>
  export default {
    name: 'yx-title',
    props: {
      config: {
        type: Object,
        default () {
          return {
            textValue: '',
            prop: '',
            span: 24
          };
        },
      }
    },
    methods: {
      handleClick(event) {
        if (this.config.click && typeof this.config.click == 'function') {
          this.config.click(event);
        } else {
          console.log('未提供点击处理函数');
        }
      }
    }
  };

</script>

<style lang="scss" scoped>
  .form-title {
    padding: 10px 16px;
    font-size: 14px;
    color: #323233;
    font-weight: bold;
  }

</style>
