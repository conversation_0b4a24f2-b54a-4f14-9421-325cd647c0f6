<!--
 * @Description:
-->
<template>
  <div class="datetime-field">
    <van-field
      :value="displayValue"
      :name="config.prop"
      :label="config.label"
      :disabled="option.disabled || option.detail || config.disabled || config.detail"
      :readonly="true"
      :required="option.disabled || option.detail || config.disabled || config.detail ? false : config.required || option.required"
      :placeholder="config.placeholder || '请选择时间'"
      :rules="config.rules"
      @click="handleFieldClick"
    />
    <van-popup v-model="showPicker" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="datetime"
        :title="config.label"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
    </van-popup>
  </div>
</template>

<script>
import moment from "moment";
import { formatDate } from '@/utils/date'; // 需要创建日期格式化工具

export default {
  name: 'yx-datetime',
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    option: Object,
    config: {
      type: Object,
      default() {
        return {
          required: false,
          prop: "",
          label: "日期时间",
          placeholder: "请选择",
          rules: [],
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss'
        };
      },
    },
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPicker: false,
      currentDate: new Date(),
      minDate: new Date(2020, 0, 1),
      maxDate: new Date(2025, 11, 31),
    };
  },
  computed: {
    displayValue() {
      return this.value ? moment(this.value).format(this.config.format) : '';
    }
  },
  methods: {
    handleFieldClick() {
      if (this.option.disabled || this.option.detail || this.config.disabled || this.config.detail || this.disabled) {
        return;
      }
      this.showPicker = true;
    },
    onConfirm(date, e) {
      // 阻止事件冒泡和默认行为
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      this.showPicker = false;
      const formattedDate = moment(date).format(this.config.valueFormat);
      this.$emit('input', formattedDate);
      
      return false;
    },
    onCancel(e) {
      // 阻止事件冒泡和默认行为
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      this.showPicker = false;
      
      return false;
    }
  },
  mounted() {
    if(this.value) {
      this.currentDate = moment(this.value).toDate();
    }
  }
};
</script>
