<!--
 * @Description: 时间范围选择组件（仅时分秒）
-->
<template>
  <div class="timerange-field">
    <van-field
      :value="displayValue"
      :name="config.prop"
      :label="config.label"
      :disabled="option.disabled || option.detail || config.disabled || config.detail"
      :readonly="true"
      :required="option.disabled || option.detail || config.disabled || config.detail ? false : config.required || option.required"
      :placeholder="config.placeholder || ''"
      :rules="config.rules"
      @click="handleFieldClick"
    />
    
    <van-popup v-model="showPicker" position="bottom" round>
      <div class="timerange-popup">
        <div class="timerange-header">
          <div class="timerange-title">{{ config.label }}</div>
          <div class="timerange-actions">
            <van-button size="small" @click.prevent.stop="cancelPicker">取消</van-button>
            <van-button type="primary" color="#3562db" size="small" @click.prevent.stop="onConfirm">确定</van-button>
          </div>
        </div>
        
        <div class="timerange-content">
          <div class="timerange-section">
            <div class="timerange-section-title">开始时间</div>
            <van-datetime-picker
              v-model="startDate"
              type="time"
              :show-toolbar="false"
            />
          </div>
          
          <div class="timerange-section">
            <div class="timerange-section-title">结束时间</div>
            <van-datetime-picker
              v-model="endDate"
              type="time"
              :show-toolbar="false"
            />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import moment from "moment";
import { Button } from "vant";

export default {
  name: 'yx-timerange',
  components: {
    "van-button": Button
  },
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    option: Object,
    config: {
      type: Object,
      default() {
        return {
          required: false,
          prop: "",
          label: "时间范围",
          placeholder: "请选择",
          rules: [],
          format: 'HH:mm:ss',
          valueFormat: 'HH:mm:ss'
        };
      },
    },
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPicker: false,
      startDate: new Date(),
      endDate: new Date(moment().add(1, 'hour').valueOf()),
    };
  },
  computed: {
    displayValue() {
      if (!this.value || !Array.isArray(this.value) || this.value.length < 2) {
        return '';
      }
      
      // 处理只有时分秒的情况
      const startStr = this.value[0] ? this.formatTimeValue(this.value[0]) : '';
      const endStr = this.value[1] ? this.formatTimeValue(this.value[1]) : '';
      
      if (!startStr || !endStr) {
        return '';
      }
      
      return `${startStr} 至 ${endStr}`;
    }
  },
  methods: {
    formatTimeValue(timeValue) {
      // 如果是完整的日期时间格式，仅提取时分秒部分
      if (timeValue.includes('T') || timeValue.includes('-')) {
        return moment(timeValue).format(this.config.format);
      }
      // 如果已经是HH:mm:ss格式，直接返回
      return timeValue;
    },
    handleFieldClick() {
      if (this.option.disabled || this.option.detail || this.config.disabled || this.config.detail || this.disabled) {
        return;
      }
      this.showPicker = true;
    },
    cancelPicker(e) {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      this.showPicker = false;
      return false;
    },
    onConfirm(e) {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      this.showPicker = false;
      
      const startFormatted = moment(this.startDate).format(this.config.valueFormat);
      const endFormatted = moment(this.endDate).format(this.config.valueFormat);
      
      this.$emit('input', [startFormatted, endFormatted]);
      
      return false;
    }
  },
  mounted() {
    const now = new Date();
    
    if (this.value && Array.isArray(this.value) && this.value.length >= 2) {
      if (this.value[0]) {
        // 处理只有时分秒的情况，设置今天的日期和传入的时间
        if (this.value[0].includes(':') && !this.value[0].includes('-')) {
          const [hours, minutes, seconds] = this.value[0].split(':');
          const date = new Date();
          date.setHours(hours || 0, minutes || 0, seconds || 0);
          this.startDate = date;
        } else {
          this.startDate = moment(this.value[0]).toDate();
        }
      }
      
      if (this.value[1]) {
        // 处理只有时分秒的情况，设置今天的日期和传入的时间
        if (this.value[1].includes(':') && !this.value[1].includes('-')) {
          const [hours, minutes, seconds] = this.value[1].split(':');
          const date = new Date();
          date.setHours(hours || 0, minutes || 0, seconds || 0);
          this.endDate = date;
        } else {
          this.endDate = moment(this.value[1]).toDate();
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.timerange-field {
  width: 100%;
}

.timerange-popup {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.timerange-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebedf0;
}

.timerange-title {
  font-size: 16px;
  font-weight: 500;
}

.timerange-actions {
  display: flex;
  gap: 8px;
}

.timerange-content {
  flex: 1;
  overflow: auto;
}

.timerange-section {
  padding-bottom: 8px;
  
  &:not(:last-child) {
    border-bottom: 1px solid #ebedf0;
  }
}

.timerange-section-title {
  padding: 10px 16px;
  color: #969799;
  font-size: 14px;
  background-color: #f7f8fa;
}
</style> 