<template>
  <div>
    <van-field v-show="false" :value="value" :name="config.prop" disabled readonly border type="text" />
    <van-field
      :value="name"
      :name="config.prop"
      :label="config.label"
      :disabled="option.disabled || option.detail || config.disabled || config.detail"
      :size="config.size || option.size"
      :required="option.disabled || option.detail || config.disabled || config.detail ? false : config.required || option.required"
      clearable
      readonly
      border
      :placeholder="config.placeholder"
      :right-icon="option.disabled || option.detail || config.disabled || config.detail ? (config.showIconClick ? 'arrow' : '') : value ? 'clear' : 'arrow'"
      @click-right-icon.stop="clearFn"
      :rules="config.rules"
      type="text"
      @click="option.disabled || option.detail || config.disabled || config.detail || disabled ? (config.showIconClick ? onShowSkipView() : null) : onShowPicker()"
    />
    <!-- 下拉框 -->
    <van-popup v-model="showPicker" round position="bottom" safe-area-inset-bottom>
      <van-picker show-toolbar :columns="columns" @cancel="onCancel" @confirm="onConfirm" ref="picker">
        <template #default>
          <div class="default-box">
            <div class="default-cancel" @click="onCancel($event, 'cancel')">取消</div>
            <van-search v-model="fuzzyQueryValue" placeholder="请输入搜索关键词" @search="onSearch" @clear="onClear" />
            <div class="default-confirm" @click="onConfirm($event, 'confirm')">确定</div>
          </div>
        </template>
      </van-picker>
    </van-popup>
  </div>
</template>
<script>
import { getAxiosData, getOptionListData } from "../formFormat";

export default {
  name: "yx-select",
  props: {
    option: Object,
    config: {
      type: Object,
      default() {
        return {
          required: false,
          prop: "",
          label: "输入框",
          placeholder: "请输入",
          rules: [],
          props: {}
        };
      }
    },
    value: {
      type: String | Number,
      default() {
        return "";
      }
    },
    name: {
      type: String,
      default() {
        return "";
      }
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPicker: false,
      columns: [],
      columnsAll: [],
      fuzzyQueryValue: ""
    };
  },
  created() {
    this.getColumns();
  },
  methods: {
    // 校验那些字段需要picker组件支持模糊查询
    checkSelect(val) {
      let arr = ["applicant_department", "construction_unit", "site_responsible_person", "camera"];
      return arr.includes(val);
    },
    // 下拉框模糊查询
    onSearch() {
      this.columns = this.fuzzyQueryValue ? this.autoSearch(this.columnsAll, this.fuzzyQueryValue) : this.columnsAll;
    },
    onClear(){
      this.columns =this.columnsAll;
    },
    autoSearch(arr, keyword) {
      const lowerKeyword = keyword.toLowerCase();
      // 提取连续数字部分和非数字部分
      const numberParts = lowerKeyword.match(/\d+/g) || [];
      const nonNumberPart = lowerKeyword.replace(/\d+/g, "");
      return arr.filter(item => {
        // 直接定位 text 属性
        const textValue = item.text || "";
        const lowerText = textValue.toString().toLowerCase();
        // 检查所有数字部分是否都存在
        const numberMatch = numberParts.every(num => lowerText.includes(num));
        // 检查非数字部分是否存在
        const nonNumberMatch = nonNumberPart === "" || lowerText.includes(nonNumberPart);
        return numberMatch && nonNumberMatch;
      });
    },
    clearFn() {
      if (this.config.showIconClick) {
        // 判断当前组件是否需要跳转
        this.$emit("skipView", this.config.prop);
        return;
      }
      if (this.option.disabled || this.option.detail || this.config.disabled || this.config.detail) return;
      if (this.value) this.onConfirm(null);
      else this.onShowPicker();
    },
    onCheck() {
      setTimeout(() => this.$emit("check", this.config.prop), 200);
    },
    getColumns() {
      if (this.config.dicData && this.config.dicData.length) {
        this.columns = getOptionListData(this.config, this.config.dicData);
        this.columnsAll = getOptionListData(this.config, this.config.dicData);
        if (this.value) {
          const nameObj = this.columns.find(item => item.value === this.value);
          this.$emit("update:name", nameObj.text);
        }
      }
      if (this.config.dicUrl && this.config.dicMethod) {
        getAxiosData(this.config, list => {
          if (list.length) {
            this.columns = getOptionListData(this.config, list);
            this.columnsAll = getOptionListData(this.config, list);
          }
          if (this.value) {
            const nameObj = this.columns.find(item => item.value === this.value);
            this.$emit("update:name", nameObj && nameObj.text ? nameObj.text : "");
          }
        });
      }
    },
    onConfirm(val, type) {
      let info = null;
      // type 成立表示 自定义组件顶部并自定义的点击事件 取值
      if (this.$refs.picker && val !== null && type) {
        let indexes = this.$refs.picker.getIndexes();
        info = this.columns[indexes[0]];
      } else {
        info = val;
      }
      this.showPicker = false;
      this.$emit("change", info && info.value ? info.value : null, this.config.prop, info);
      this.$emit("update:value", info && info.value ? info.value : "");
      this.$emit("update:name", info && info.text ? info.text : "");
      if (this.config.onChange) {
        this.config.onChange(info && info.value ? info.value : null, this.config.prop, info);
      }
      this.onCheck();
    },
    onCancel() {
      this.showPicker = false;
      this.onCheck();
    },
    onShowPicker() {
      this.showPicker = true;
    }
  }
};
</script>
<style lang="scss" scoped>
.default-box {
  width: 100vw;
  height: 44px;
  box-sizing: border-box;
  padding: 0px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .default-cancel {
    color: #969799;
    flex-shrink: 0;
  }
  .van-search {
    width: calc(100vw - 110px);
    flex-shrink: 0;
    border-radius: 34px;
    padding: 0px;
    .van-search__content {
      border-radius: 34px;
    }
  }
  .default-confirm {
    color: #576b95;
    flex-shrink: 0;
  }
}
</style>
