<!--
 * @Author: hedd heding<PERSON>@sinomis.com
 * @Date: 2025-04-09 16:00:11
 * @LastEditors: hedd heding<PERSON>@sinomis.com
 * @LastEditTime: 2025-04-09 17:46:45
 * @FilePath: \ybs_h5\src\common\workFlowForm\formComp\formItem.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <yx-input v-if="typeEnum[config.type] === 'input'" v-model="form[config.prop]" v-bind="getBind(config)" :option="option" :config="config" :disabled="disabled"> </yx-input>
    <yx-select
      v-if="config.type === 'select'"
      v-bind="getBind(config)"
      :value.sync="form[config.prop]"
      :name.sync="form[`$${config.prop}`]"
      :option="option"
      :config="config"
      :disabled="disabled"
      @check="handleCheck"
      @skipView="handleSkipView"
    ></yx-select>
    <yx-checkbox
      v-if="['checkbox', 'radio'].includes(config.type)"
      v-bind="getBind(config)"
      :value.sync="form[config.prop]"
      :name.sync="form[`$${config.prop}`]"
      :option="option"
      :config="config"
      :disabled="disabled"
      @check="handleCheck"
    ></yx-checkbox>
    <yx-title v-if="config.type === 'title'" v-bind="getBind(config)" :config="config"></yx-title>
    <yx-datetime v-if="config.type === 'datetime'" v-bind="getBind(config)" v-model="form[config.prop]" :option="option" :config="config" :disabled="disabled"> </yx-datetime>
    <yx-datetimerange v-if="config.type === 'datetimerange'" v-bind="getBind(config)" v-model="form[config.prop]" :option="option" :config="config" :disabled="disabled"> </yx-datetimerange>
    <yx-upload v-if="config.type === 'upload'" v-bind="getBind(config)" v-model="form[config.prop]" :option="option" :config="config" :props="config.props || option.props" :disabled="disabled"> </yx-upload>
    <yx-dynamic v-if="config.type === 'dynamic'" v-bind="getBind(config)" v-model="form[config.prop]" :option="option" :config="config" :props="config.props || option.props" :disabled="disabled"></yx-dynamic>
  </div>
</template>

<script>
import YxInput from "./input.vue";
import YxSelect from "./select.vue";
import YxCheckbox from "./checkbox.vue";
import YxTitle from "./title.vue";
import YxDatetime from "./datetime.vue";
import YxDatetimerange from "./datetimerange.vue";
import YxUpload from "./upload.vue";
import YxDynamic from "./dynamic.vue";

export default {
  name: "yx-form-item",
  components: {
    YxInput,
    YxSelect,
    YxCheckbox,
    YxTitle,
    YxDatetime,
    YxDatetimerange,
    YxUpload,
    YxDynamic
  },
  props: {
    option: {
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      typeEnum: {
        digit: "input",
        tel: "input",
        textarea: "input",
        input: "input",
        password: "input",
        number: "input"
      }
    };
  },
  methods: {
    getBind(column) {
      delete column.value
      return column
    },
    handleCheck(value) {
      this.$emit("check", value);
    },
    handleSkipView(value) {
      this.$emit("skipView", value);
    }
  }
};
</script>
