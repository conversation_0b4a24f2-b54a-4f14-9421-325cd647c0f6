<!--
 * @Description: 日期时间范围选择组件
-->
<template>
  <div class="datetimerange-field">
    <van-field
      :value="displayValue"
      :name="config.prop"
      :label="config.label"
      :disabled="option.disabled || option.detail || config.disabled || config.detail"
      :readonly="true"
      :required="option.disabled || option.detail || config.disabled || config.detail ? false : config.required || option.required"
      :placeholder="config.placeholder || ''"
      :rules="config.rules"
      @click="handleFieldClick"
    />
    
    <van-popup v-model="showPicker" position="bottom" round>
      <div class="datetimerange-popup">
        <div class="datetimerange-header">
          <div class="datetimerange-title">{{ config.label }}</div>
          <div class="datetimerange-actions">
            <van-button size="small" @click.prevent.stop="cancelPicker">取消</van-button>
            <van-button type="primary" color="#3562db" size="small" @click.prevent.stop="onConfirm">确定</van-button>
          </div>
        </div>
        
        <div class="datetimerange-content">
          <div class="datetimerange-section">
            <div class="datetimerange-section-title">开始时间</div>
            <van-datetime-picker
              v-model="startDate"
              type="datetime"
              :min-date="minDate"
              :max-date="maxDate"
              :show-toolbar="false"
            />
          </div>
          
          <div class="datetimerange-section">
            <div class="datetimerange-section-title">结束时间</div>
            <van-datetime-picker
              v-model="endDate"
              type="datetime"
              :min-date="startDate"
              :max-date="maxDate"
              :show-toolbar="false"
            />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import moment from "moment";
import { Button } from "vant";

export default {
  name: 'yx-datetimerange',
  components: {
    "van-button": Button
  },
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    option: Object,
    config: {
      type: Object,
      default() {
        return {
          required: false,
          prop: "",
          label: "时间范围",
          placeholder: "请选择",
          rules: [],
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss'
        };
      },
    },
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPicker: false,
      startDate: new Date(),
      endDate: new Date(moment().add(1, 'day').valueOf()),
      minDate: new Date(2020, 0, 1),
      maxDate: new Date(2025, 11, 31),
    };
  },
  computed: {
    displayValue() {
      if (!this.value || !Array.isArray(this.value) || this.value.length < 2) {
        return '';
      }
      
      const startStr = this.value[0] ? moment(this.value[0]).format(this.config.format) : '';
      const endStr = this.value[1] ? moment(this.value[1]).format(this.config.format) : '';
      
      if (!startStr || !endStr) {
        return '';
      }
      
      return `${startStr} 至 ${endStr}`;
    }
  },
  methods: {
    handleFieldClick() {
      if (this.option.disabled || this.option.detail || this.config.disabled || this.config.detail || this.disabled) {
        return;
      }
      this.showPicker = true;
    },
    cancelPicker(e) {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      this.showPicker = false;
      return false;
    },
    onConfirm(e) {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      this.showPicker = false;
      
      const startFormatted = moment(this.startDate).format(this.config.valueFormat);
      const endFormatted = moment(this.endDate).format(this.config.valueFormat);
      
      this.$emit('input', [startFormatted, endFormatted]);
      
      return false;
    }
  },
  mounted() {
    if (this.value && Array.isArray(this.value) && this.value.length >= 2) {
      if (this.value[0]) {
        this.startDate = moment(this.value[0]).toDate();
      }
      
      if (this.value[1]) {
        this.endDate = moment(this.value[1]).toDate();
      }
    }
  },
  watch: {
    startDate(val) {
      // 确保结束时间不早于开始时间
      if (moment(this.endDate).isBefore(val)) {
        this.endDate = moment(val).add(1, 'hour').toDate();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.datetimerange-field {
  width: 100%;
}

.datetimerange-popup {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.datetimerange-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebedf0;
}

.datetimerange-title {
  font-size: 16px;
  font-weight: 500;
}

.datetimerange-actions {
  display: flex;
  gap: 8px;
}

.datetimerange-content {
  flex: 1;
  overflow: auto;
}

.datetimerange-section {
  padding-bottom: 8px;
  
  &:not(:last-child) {
    border-bottom: 1px solid #ebedf0;
  }
}

.datetimerange-section-title {
  padding: 10px 16px;
  color: #969799;
  font-size: 14px;
  background-color: #f7f8fa;
}
</style> 