<template>
  <div>
    <van-field v-show="false" :value="value" :name="config.prop" disabled readonly border type="text" />
    <van-field :name="config.prop" :label="config.label" :rules="config.rules" :required="option.disabled || option.detail || config.disabled || config.detail ? false : config.required || option.required">
      <template #input>
        <div class="upload-container">
          <van-uploader
            v-model="fileList"
            :max-count="config.limit || 1"
            :multiple="config.multiple"
            :max-size="fileSize * 1024 * 1024"
            :accept="config.accept || '*'"
            :disabled="option.disabled || option.detail || config.disabled || config.detail || disabled"
            :deletable="!(option.disabled || option.detail || config.disabled || config.detail)"
            @oversize="onOversize"
            :after-read="afterRead"
            @delete="onDelete"
            :before-read="beforeRead"
          >
            <template #preview-cover="{ index }">
              <div class="preview-actions">
                <div class="preview-btn" @click.stop="previewFile(index)">预览</div>
              </div>
            </template>
          </van-uploader>
        </div>
      </template>
    </van-field>
    <van-popup v-model="showPreview" position="bottom" closeable style="border-radius: 0px" :close-on-click-overlay="false" @close="closePreview">
      <div class="preview-content">
        <iframe
          :src="iframeUrl"
          style="width: 100%; height: 100%"
          id="iframe1"
          frameborder="no"
          border="0"
          marginwidth="0"
          marginheight="0"
          scrolling="yes"
          allowtransparency="yes"
        ></iframe>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Compressor from "compressorjs";
import axios from "axios";
import { getAsVal } from '@/utils/index'
import { Base64 } from "js-base64";

export default {
  name: "yx-upload",
  props: {
    value: {
      type: [Array, String, Number],
      default: () => []
    },
    option: {
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object,
      default: () => ({
        required: false,
        prop: "",
        label: "输入框",
        placeholder: "请输入",
        rules: []
      })
    },
    props: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fileList: [],
      showPreview: false,
      previewIndex: 0,
      previewImages: [],
      text: [],
      iframeUrl: ""
    };
  },
  computed: {
    labelKey() {
        return this.props.label || 'label'
      },
      valueKey() {
        return this.props.value || 'value'
      },
      fileSize() {
        return this.config.fileSize || 20
      }
  },
  mounted() {
    this.text = this.value || [];
    this.getFileList();
  },
  watch: {
    value: {
      handler(newVal) {
        this.text = newVal || [];
        this.getFileList();
      },
      deep: true
    }
  },
  methods: {
    getFileList() {
      this.fileList = (this.value || []).map((ele, index) => {
        if (ele) {
          let name = ele[this.labelKey === 'label' ? 'name' : this.labelKey]
          let url = ele[this.valueKey === 'value' ? 'url' : this.valueKey]
          return {
            uid: index + '',
            status: 'done',
            name: name,
            url: this.$YBS.imgUrlTranslation(url),
            index: index,
            file: {
              name: name
            }
          }
        }
      })
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.fileSize}M`);
    },

    beforeRead(file) {
      if (file.size / 1024 / 1024 > this.fileSize) {
        this.$toast.fail(`文件大小不能超过${this.fileSize}M`);
        return false;
      }
      return true;
    },

    async afterRead(files) {
      if (!this.config.action) {
        let message = "未配置上传地址";
        this.$toast.fail(message);
        return;
      }
      this.fileList.forEach(i => {
        i.status = "uploading";
      });
      try {
        let fileToUpload = files.file;
        // 设置文件名
        const fileName = fileToUpload.name;
        if (fileToUpload.type.startsWith("image/")) {
          fileToUpload = await this.compress(fileToUpload);
          // 保持原始文件名
          fileToUpload = new File([fileToUpload], fileName, {
            type: fileToUpload.type,
            lastModified: Date.now()
          });
        } else if (fileToUpload.type.startsWith("video/")) {
          fileToUpload = await this.compressVideo(fileToUpload);
        }
        const params = new FormData();
        params.append("file", fileToUpload);
        await this.uploadFile(params, fileName);
      } catch (error) {
        this.$toast.fail("文件处理失败，请检查文件格式");
        files.status = "failed";
        console.error("文件处理错误:", error);
      }
    },

    // 压缩图片方法
    async compress(file) {
      return new Promise((resolve, reject) => {
        if (file.type === "image/png") {
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          const img = new Image();

          img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;

            ctx.fillStyle = "#FFFFFF";
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);

            canvas.toBlob(
              blob => {
                const jpgFile = new File([blob], file.name.replace(/\.png$/i, ".jpg"), { type: "image/jpeg", lastModified: Date.now() });
                this.compressImage(jpgFile, resolve, reject);
              },
              "image/jpeg",
              0.9
            );
          };

          img.onerror = () => {
            this.compressImage(file, resolve, reject);
          };
          img.src = URL.createObjectURL(file);
        } else {
          this.compressImage(file, resolve, reject);
        }
      });
    },

    compressImage(file, resolve, reject) {
      new Compressor(file, {
        quality: 0.6,
        maxWidth: 1920,
        maxHeight: 1080,
        success(result) {
          const compressedFile = new File([result], file.name, {
            type: result.type,
            lastModified: Date.now()
          });
          resolve(compressedFile);
        },
        error(err) {
          reject(err);
        }
      });
    },

    // 视频压缩方法（如果需要的话可以实现）
    async compressVideo(file) {
      return file; // 目前直接返回原文件，如果需要可以添加视频压缩逻辑
    },
    uploadFile(params, fileName) {
      try {
        axios
          .post(__PATH.BASE_PATH + this.config.action, params, {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          })
          .then(res => {
            let obj = res.data;
            if (obj.code == 200) {
              if (this.config.action.startsWith("/sinomis-authweb")) {
                obj = {
                  code: 200,
                  data: {
                    [this.config.propsHttp.name || "fileName"]: fileName,
                    [this.config.propsHttp.url || "fileUrl"]: res.data.data
                  },
                  msg: "操作成功",
                  success: true
                };
              }
              const data = getAsVal(obj, this.config.propsHttp.res);
              let textObj = {}
              textObj[this.labelKey === 'label' ? 'name' : this.labelKey] = data[this.config.propsHttp.name]
              textObj[this.valueKey === 'value' ? 'url' : this.valueKey] = data[this.config.propsHttp.url]

              // 更新 text 数组
              const newText = [...this.text, textObj];
              this.text = newText;
              // 触发 v-model 更新
              this.$emit("input", newText);
              this.$emit("change", newText, this.config.prop, newText);

              // 更新文件列表显示
              this.fileList = this.fileList.map(file => {
                if (file.status === "uploading") {
                  return {
                    ...file,
                    status: "done",
                    url: this.$YBS.imgUrlTranslation(data[this.config.propsHttp.url]),
                    index: this.text.length - 1,
                    name: data[this.config.propsHttp.name]
                  };
                }
                return file;
              });
            } else {
              this.$toast.fail(obj.msg);
            }
          });
      } catch (error) {
        this.$toast.fail("上传失败，请重试");
        this.fileList.forEach(i => {
          i.status = "failed";
        });
      }
    },

    onDelete(file) {
      const index = this.fileList.indexOf(file);
      if (index !== -1) {
        const newText = [...this.text];
        newText.splice(index, 1);
        this.text = newText;

        // 根据 props 的类型发送适当的数据
        const emitValue = this.config.limit === 1 ? (newText[0] || '') : newText;

        this.$emit("input", emitValue);
        this.$emit("change", emitValue, this.config.prop, emitValue);

        // 更新索引
        this.fileList.forEach((item, idx) => {
          if (idx >= index) {
            item.index = idx;
          }
        });
      }
      this.$emit("delete", file);
    },

    previewFile(index) {
      console.log('预览索引:', index);
      // 通过索引获取原始数据
      const originalData = this.text[index];
      
      if (originalData) {
        const fileUrl = originalData[this.valueKey === 'value' ? 'url' : this.valueKey];
        console.log('原始文件URL:', fileUrl);
        
        if (fileUrl) {
          this.iframeUrl = `${__PATH.PREVIEW_URL}${encodeURIComponent(Base64.encode(this.$YBS.imgUrlTranslation(decodeURIComponent(fileUrl))))}`;
          console.log(this.iframeUrl, 'this.iframeUrl')
          this.showPreview = true;
        } else {
          this.$toast.fail("无法预览此文件，找不到文件URL");
        }
      } else {
        this.$toast.fail("无法预览此文件，找不到原始数据");
      }
    },

    closePreview() {
      this.showPreview = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.upload-container {
  .preview-cover {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.3);
    padding: 4px;
    text-align: center;

    .filename {
      color: #fff;
      font-size: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: block;
    }
  }
    
  .preview-actions {
    height: 16px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    padding: 5px 0;
    
    .preview-btn {
      line-height: 16px;
      color: #fff;
      font-size: 12px;
      cursor: pointer;
    }
  }
}

.preview-desc {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  padding: 8px;
  text-align: center;
  font-size: 14px;
}

.preview-content {
  width: 100vw;
  height: 90vh;
  box-sizing: border-box;
}

/deep/ .van-uploader__upload-icon {
  color: #86909c;
  transform: translateY(-20%);
}

/deep/ .van-uploader__preview-delete {
  top: 3px;
  right: 3px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.5);
}

/deep/ .van-uploader__preview-delete-icon {
  transform: scale(0.8);
  top: 2px;
  left: 2px;
  font-weight: bold;
}

/deep/ .van-uploader__preview {
  position: relative;
}

/deep/ .van-uploader__preview-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  img {
    width: 100%;
    height: auto;
  }
}
</style>
