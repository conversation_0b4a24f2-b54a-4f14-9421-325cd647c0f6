<template>
  <div class="xyCheckBox">
    <van-field
      v-show="false"
      :value="Array.isArray(name) ? name.join(',') : name"
      :name="config.prop"
      disabled
      readonly
      border
      type="text"
    />
<!--    <div class="labelText">-->
<!--      {{config.label}}-->
<!--    </div>-->
    <van-field
      :value="value"
      :name="config.prop"
      :label="config.label"
      label-width="unset"
      input-align="left"
      error-message-align="left"
      :disabled="option.disabled || option.detail || config.disabled || config.detail"
      :readonly="config.readonly || option.readonly"
      :size="config.size || option.size"
      :required="option.disabled || option.detail || config.disabled || config.detail ? false : config.required || option.required"
      :rules="config.rules"
      border
      type="text"
    >
      <template #input>
        <van-checkbox-group v-model="checkboxValue" @change="changeValueFn" :disabled="option.disabled || option.detail || config.disabled || config.detail || disabled" :max="config.type==='radio'?1:0" direction="horizontal">
          <van-checkbox v-for="(item,index) in columns" :key="index" :name="item.value" :shape="config.type==='radio'?'round':'square'" @click="()=>checkboxClickFn(item.value)">{{ item.text }}</van-checkbox>
        </van-checkbox-group>
      </template>
    </van-field>
  </div>
</template>
<script>

import { getAxiosData, getOptionListData } from "../formFormat";
import props from "../mixins/props";
export default {
  name: "yx-checkbox",
  mixins: [props()],
  props: {
    option: Object,
    config: {
      type: Object,
      default() {
        return {};
      },
    },
    value: {
      type: [String, Number, Array],
      default() {
        return [];
      },
    },
    name: {
      type: [String, Array],
      default() {
        return [];
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    checkboxValue: {
      handler(val) {
        this.changeValueFn(val)
        this.handleChange(val)
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      columns: [],
      checkboxValue: [],
    };
  },
  computed: {
    // checkboxValue: {
    //   set(val) {
    //     console.log(val);
    //     this.$emit("input", val.join(','));
    //   },
    //   get() {
    //     console.log(this.value, '------------this.value');
    //     return this.value ? this.value.split(",") : [];
    //   }
    // }
  },
  created() {
    this.getColumns();
  },
  methods: {
    changeValueFn(val) {
      const name = []
      this.columns.map(item => {
        if(val.includes(item.value)) name.push(item.text);
      });
      this.$emit("change", val.length ? val : []);
      this.$emit("update:value", val.length ? val : []);
      this.$emit("update:name", name.length ? name : []);
    },
    checkboxClickFn(val) {
      if(this.config.type==='radio') {
        if(this.checkboxValue[0] !== val) this.checkboxValue = [val]
      }
    },
    setDefaultName() {
      const val = Array.isArray(this.value) ? this.value : (this.value ? this.value.split(',') : [])
      const name = []
      this.columns.map(item => {
        if(val.includes(item.value)) name.push(item.text);
      });
      this.$emit("update:name", name);
      this.checkboxValue = val
    },
    getColumns() {
      if(this.config.dicData && this.config.dicData.length) {
        this.columns = getOptionListData(this.config,this.config.dicData)
        if(this.value) this.setDefaultName()
      }
      if(this.config.dicUrl && this.config.dicMethod ) {
        getAxiosData(this.config,(list) => {
          if(list.length) this.columns = getOptionListData(this.config,list)
          if(this.value) this.setDefaultName()
        })
      }
    },
  },
};
</script>
<style scoped lang="scss">
.labelText {
  padding: 15px 0 5px 16px;
  background: #fff;
}
</style>
<style lang="scss">
.xyCheckBox {
  .van-cell {
    flex-direction: column !important;
  }
  .van-cell__title {
    margin-bottom: 6px;
  }
  .van-checkbox-group {
    display: grid;
    grid-template-columns:auto auto;
    grid-gap: 10px;
  }
}
</style>
