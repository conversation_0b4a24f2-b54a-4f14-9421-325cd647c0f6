<template>
  <van-form
    @submit="onSubmit"
    @failed="onFailed"
    ref="formRef"
    :disabled="option.disabled"
    scroll-to-error
    :label-width="option.labelWidth || '100px'"
    input-align="left"
    error-message-align="left"
  >
    <div class="formItem" v-for="(item, index) in option.column" :key="index">
      <template v-if="item.display !== false">
        <yx-form-item
          :config="item"
          :option="option"
          :form="form"
          :disabled="disabled"
          @check="checkSelect"
          @skipView="skipView"
        ></yx-form-item>
      </template>
    </div>
    <div class="form-footer" v-if="option.submitBtn && useFormSubmit">
      <van-button round block type="info" native-type="submit">{{ option.submitText }}</van-button>
    </div>
  </van-form>
</template>
<script>
import YxFormItem from "./formComp/formItem.vue";
import { Toast } from "vant";

export default {
  name: "yx-form",
  model: {
    prop: "form",
    event: "change"
  },
  props: {
    useFormSubmit: Boolean,
    option: {
      type: Object,
      default() {
        return {};
      }
    },
    form: {
      type: Object,
      default() {
        return {};
      }
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    YxFormItem
  },
  data() {
    return {
      errorInfo: null
    };
  },
  watch: {
    option: {
      handler(newVal) {
        console.log(newVal, '---option');
      },
      deep: true
    }
  },
  methods: {
    onSubmit(values) {
      this.errorInfo = null;
      this.$emit("submit", values);
    },
    onFailed(errorInfo) {
      errorInfo &&
        errorInfo.errors &&
        errorInfo.errors[0] &&
        Toast({
          message: errorInfo.errors[0].message,
          position: "bottom"
        });
      this.errorInfo = errorInfo;
    },
    checkSelect(vl) {
      vl && this.$refs.formRef.validate(vl);
    },
    skipView(val) {
      this.$emit("formSkipView", { prop: val, id: this.form[val], name: this.form[`$${val}`] });
    },
    validateForm(callback) {
      this.$refs.formRef.submit();
      setTimeout(() => callback(this.errorInfo), 20);
    }
  },
  mounted() {
    console.log(this.disabled, '---mounted--disabled');
  }
};
</script>
<style lang="scss">
.form-footer {
  position: fixed;
  padding: 10px;
  box-sizing: border-box;
  bottom: 0;
  left: 0;
  width: 100%;
}

.formItem {
  .van-field--disabled .van-field__label {
    color: #4e5969;
  }

  .van-cell {
    font-size: 16px;
    color: #4e5969;
  }

  textarea {
    padding: 0 !important;
  }
}
</style>
