import axios from "axios";

// 引入需要的工具方法
import store from '@/store'

export const formFormat = "{\"process\":{\"copyUserName\":\"\",\"formKey\":\"operation\",\"hideExamine\":true,\"icon\":\"\",\"hideCopy\":true,\"version\":-1,\"platform\":\"\",\"formUrl\":\"\",\"appFormUrl\":\"\",\"deploymentId\":\"\",\"scope\":false,\"tenantId\":\"000000\",\"name\":\"零星工程\",\"copyUser\":\"\",\"id\":\"49ba455c-92b3-11ef-8cd7-ee5aa6e825aa\",\"deployTime\":\"\",\"category\":\"1818127680776704002\",\"key\":\"process_RWGZT3YJKW4QBS7Bjc7pCtCMjFdrt3Nj\",\"status\":1},\"appForm\":\"{\\\"column\\\":[{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"基本信息\\\",\\\"prop\\\":\\\"a172164087218141887\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"项目名称\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"project_name\\\",\\\"placeholder\\\":\\\"隐藏表单 业务传值\\\",\\\"required\\\":false,\\\"readonly\\\":false,\\\"disabled\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"项目编号\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"project_id\\\",\\\"placeholder\\\":\\\"隐藏表单 业务传值\\\",\\\"required\\\":false,\\\"readonly\\\":false,\\\"disabled\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"工程类型\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"project_type\\\",\\\"placeholder\\\":\\\"隐藏表单 业务传值\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"供应商\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"supplier\\\",\\\"required\\\":false,\\\"placeholder\\\":\\\"隐藏表单 业务传值\\\"},{\\\"type\\\":\\\"select\\\",\\\"label\\\":\\\"供应商负责人\\\",\\\"cascader\\\":[],\\\"span\\\":12,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"staffName\\\",\\\"value\\\":\\\"id\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"supplier_leader\\\",\\\"dicFormatter\\\":\\\"(res) => {\\\\r\\\\n  return res.data.data.records\\\\r\\\\n}\\\",\\\"dicUrl\\\":\\\"/base_info/hospitalStaff/hospital-staff/list\\\",\\\"dicMethod\\\":\\\"post\\\",\\\"dicQuery\\\":{\\\"current\\\":\\\"1\\\",\\\"size\\\":\\\"999\\\",\\\"hospitalCode\\\":\\\"SINOMIS\\\",\\\"unitCode\\\":\\\"SINOMIS\\\"}},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"项目位置\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"project_location\\\",\\\"placeholder\\\":\\\"隐藏表单 业务传值\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"具体描述\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"project_description\\\",\\\"placeholder\\\":\\\"隐藏表单 业务传值\\\",\\\"required\\\":false},{\\\"type\\\":\\\"select\\\",\\\"label\\\":\\\"立项科室\\\",\\\"cascader\\\":[],\\\"span\\\":12,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"deptName\\\",\\\"value\\\":\\\"id\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"project_department\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"立项科室必须填写\\\"}],\\\"dicFormatter\\\":\\\"(res) => {\\\\r\\\\n  return res.data.data.records\\\\r\\\\n}\\\",\\\"dicUrl\\\":\\\"/base_info/departmentManager/department-manager/selectByPage\\\",\\\"dicMethod\\\":\\\"post\\\",\\\"dicQuery\\\":{\\\"current\\\":\\\"1\\\",\\\"size\\\":\\\"999\\\",\\\"hospitalCode\\\":\\\"SINOMIS\\\",\\\"unitCode\\\":\\\"SINOMIS\\\"},\\\"filterable\\\":true},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"报告编号\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"report_id\\\",\\\"placeholder\\\":\\\"请输入OA单号\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"报告编号必须填写\\\"}]},{\\\"type\\\":\\\"select\\\",\\\"label\\\":\\\"主管领导\\\",\\\"cascader\\\":[],\\\"span\\\":12,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"staffName\\\",\\\"value\\\":\\\"id\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"leader_name\\\",\\\"dicFormatter\\\":\\\"(res) => {\\\\r\\\\n  return res.data.data.records\\\\r\\\\n}\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"主管领导必须填写\\\"}],\\\"dicUrl\\\":\\\"/base_info/hospitalStaff/hospital-staff/list\\\",\\\"dicMethod\\\":\\\"post\\\",\\\"dicQuery\\\":{\\\"current\\\":\\\"1\\\",\\\"size\\\":\\\"999\\\",\\\"hospitalCode\\\":\\\"SINOMIS\\\",\\\"unitCode\\\":\\\"SINOMIS\\\"},\\\"filterable\\\":true},{\\\"type\\\":\\\"select\\\",\\\"label\\\":\\\"领导职位\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"处长\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"副处长\\\",\\\"value\\\":\\\"1\\\"}],\\\"cascader\\\":[],\\\"span\\\":12,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"leader_position\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"领导职位必须填写\\\"}],\\\"dataType\\\":\\\"number\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"专管员\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"special_officer\\\",\\\"value\\\":\\\"${this.$store.state.userInfo.staffName}\\\",\\\"readonly\\\":true,\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"专管员必须填写\\\"}],\\\"placeholder\\\":\\\"当前操作人\\\",\\\"disabled\\\":true},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"工程概况及委托内容\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"project_overview\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"工程概况及委托内容必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"现场是否具备条件(如水、电、场地条件等)\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"conditions_met\\\",\\\"labelWidth\\\":\\\"300\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"现场是否具备条件(如水、电、场地条件等)必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"是否需要备案\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"nd_Filed\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"是否需要备案必须填写\\\"}]},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"施工方案\\\",\\\"prop\\\":\\\"a17219821302448305\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"地点\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"loc\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"地点必须填写\\\"}]},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"面积\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"area\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"面积必须填写\\\"}]},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"内容\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"cont\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"内容必须填写\\\"}]},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"工期\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"dur\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"工期必须填写\\\"}]},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"工艺\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"proc\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"工艺必须填写\\\"}]},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"材料\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"mat\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"材料必须填写\\\"}]},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"安全措施\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"safe\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"安全措施必须填写\\\"}]},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"其他\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"misc\\\",\\\"placeholder\\\":\\\"请输入\\\"},{\\\"label\\\":\\\"图纸文件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"dwg_f\\\",\\\"required\\\":false},{\\\"label\\\":\\\"预算文件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"budg_f\\\",\\\"required\\\":false},{\\\"label\\\":\\\"其他文件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"misc_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"处内预算信息\\\",\\\"prop\\\":\\\"a172164088391243108\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"预算送审金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"sbudg_cost\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"预算送审金额必须填写\\\"}],\\\"placeholder\\\":\\\"请输入\\\",\\\"append\\\":\\\"元\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"处内审定金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"budg_cost\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"处内审定金额必须填写\\\"}],\\\"append\\\":\\\"元\\\"},{\\\"label\\\":\\\"预算文件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"budg_cost_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"预算文件必须填写\\\"}]},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"送审资料信息\\\",\\\"prop\\\":\\\"a172198219163914044\\\"},{\\\"label\\\":\\\"OA附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"oa_f\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"立项批复（如院内请示报告等）\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"audit_Index1\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"立项批复（如院内请示报告等）必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"如通过招标方式确定，则需提供招标文件、中标通知书等\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"audit_Index2\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"如通过招标方式确定，则需提供招标文件、中标通知书等必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"经报审单位签字确认的施工图纸及说明（纸质及电子版）\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"audit_Index3\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"经报审单位签字确认的施工图纸及说明（纸质及电子版）必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"经报审单位签字确认的工程量清单及预算（纸质及电子版）\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"audit_Index4\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"经报审单位签字确认的工程量清单及预算（纸质及电子版）必须填写\\\"}]},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"审计预算初审\\\",\\\"prop\\\":\\\"a172285666078288032\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"初审意见\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"audit_opin\\\"},{\\\"label\\\":\\\"审核文件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"audit_opin_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"审核文件必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"是否通过\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"audit_appr\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"是否通过必须填写\\\"}]},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"出具初审金额\\\",\\\"prop\\\":\\\"a172603657461819308\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"初审金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"audit_cost\\\",\\\"showWordLimit\\\":false,\\\"append\\\":\\\"元\\\"},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"audit_cost_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"确认金额\\\",\\\"prop\\\":\\\"a172603451721784217\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"是否同意\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"audit_cost_appr\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"是否同意必须填写\\\"}]},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"达成一致时间\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"audit_agree_date\\\"},{\\\"type\\\":\\\"checkbox\\\",\\\"label\\\":\\\"施工单位已确认\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"}],\\\"span\\\":12,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"audit_b_con\\\",\\\"labelWidth\\\":\\\"140\\\"},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"audit_disagree_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"修改初审金额\\\",\\\"prop\\\":\\\"a172603694321321883\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"修改初审金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"audit_cost_fix\\\",\\\"append\\\":\\\"元\\\"},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"audit_cost_fix_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"外审意见\\\",\\\"prop\\\":\\\"a17260370213493663\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"外审意见\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"ext_audit_opin\\\"},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"ext_audit_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"移交定案表\\\",\\\"prop\\\":\\\"a172603706119489660\\\"},{\\\"type\\\":\\\"datetime\\\",\\\"label\\\":\\\"接收时间\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD HH:mm:ss\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD HH:mm:ss\\\",\\\"prop\\\":\\\"audit_rec_time\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"定案表盖章\\\",\\\"prop\\\":\\\"a172603715267816528\\\"},{\\\"type\\\":\\\"datetime\\\",\\\"label\\\":\\\"转出时间\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD HH:mm:ss\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD HH:mm:ss\\\",\\\"prop\\\":\\\"audit_xfer_time\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"上传定案表\\\",\\\"prop\\\":\\\"a172603718019022829\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"最终预算定案金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"final_audit_cost\\\",\\\"append\\\":\\\"元\\\",\\\"readonly\\\":false,\\\"placeholder\\\":\\\"请输入\\\",\\\"labelWidth\\\":\\\"150\\\"},{\\\"label\\\":\\\"定案表附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"audit_tbl_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"复核确认\\\",\\\"prop\\\":\\\"a172603725973211845\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"是否复核\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"audit_rchk\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"是否复核必须填写\\\"}]},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"复核人意见\\\",\\\"prop\\\":\\\"a17260372958828974\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"复核意见\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"audit_rchk_opin\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"处内意见\\\",\\\"prop\\\":\\\"a172603731685577319\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"处内意见\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"audit_finalopin\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"备案资料\\\",\\\"prop\\\":\\\"a172164093200033343\\\"},{\\\"label\\\":\\\"公示牌附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"sign_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"公示牌附件必须填写\\\"}]},{\\\"label\\\":\\\"其他附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"rec_f\\\",\\\"required\\\":false},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"开工准备\\\",\\\"prop\\\":\\\"a172164097323484910\\\"},{\\\"label\\\":\\\"开工单\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"sorder_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"开工单必须填写\\\"}]},{\\\"label\\\":\\\"拆除单\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"dorder_f\\\",\\\"required\\\":false},{\\\"label\\\":\\\"现场照片\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"sphoto_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"现场照片必须填写\\\"}]},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"预计开工日期\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"estart_date\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"开工确认\\\",\\\"prop\\\":\\\"a172164105926540727\\\"},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"实际开工日期\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"astart_date\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"完工申请\\\",\\\"prop\\\":\\\"a172164114068332208\\\"},{\\\"label\\\":\\\"现场照片\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"fphoto_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"现场照片必须填写\\\"}]},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"土建验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"cst_stat\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"土建验收结论\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"cst_cncl\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"给排水验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"plmb_stat\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"给排水验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"plmb_cncl\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"暖通验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"hvac_stat\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"暖通验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"hvac_cncl\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"电气验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"elec_stat\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"电气验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"elec_cncl\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"弱电验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"lowv_stat\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"弱电验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"lowv_cncl\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"消防验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"fire_stat\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"消防验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"fire_cncl\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"气体验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"gass_stat\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"气体验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"gass_cncl\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"物业验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"prop_stat\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"物业验收情况\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"prop\\\":\\\"prop_cncl\\\",\\\"required\\\":false},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"综合验收结论\\\",\\\"span\\\":24,\\\"display\\\":false,\\\"prop\\\":\\\"final_cncl\\\",\\\"required\\\":false},{\\\"label\\\":\\\"验收单\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"finish_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"验收单必须填写\\\"}]},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"预计完工日期\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"efinfsh_date\\\"},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"实际完工日期\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"afinish_date\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"结算申请\\\",\\\"prop\\\":\\\"a172164161989596901\\\"},{\\\"label\\\":\\\"结算文件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"payreq_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"结算文件必须填写\\\"}]},{\\\"label\\\":\\\"工程量确认单\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"workconf_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"现场照片必须填写\\\"}]},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"申请结算金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"payreq_cost\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"申请结算金额必须填写\\\"}],\\\"append\\\":\\\"元\\\"},{\\\"label\\\":\\\"其他附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"payreq_other_f\\\",\\\"required\\\":false,\\\"labelTip\\\":\\\"若有变更则需要上传\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"专管员确认\\\",\\\"prop\\\":\\\"a172164167024235980\\\"},{\\\"label\\\":\\\"上传附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"payreq_con_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"处内审核\\\",\\\"prop\\\":\\\"a172170287125164193\\\"},{\\\"label\\\":\\\"结算文件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"paydep_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"结算文件必须填写\\\"}]},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"送审计金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"paydep_cost\\\",\\\"placeholder\\\":\\\"请输入\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"送审计金额必须填写\\\"}],\\\"append\\\":\\\"元\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"是否有洽商签证\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"paydep_con\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"是否有洽商签证必须填写\\\"}]},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"paydep_con_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"送审资料信息\\\",\\\"prop\\\":\\\"a172959590081252579\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"经报审单位确认的竣工验收报告/工程验收单\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index1\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"经报审单位确认的竣工验收报告/工程验收单必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"实行监理审核的要提供监理审核意见(须提供监理审核完的广联达软件版)\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index2\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"实行监理审核的要提供监理审核意见(须提供监理审核完的广联达软件版)必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"工程招标文件、招标答疑文件及其他有关文件\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index3\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"工程招标文件、招标答疑文件及其他有关文件必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"招标图纸及竣工图纸(图如有)(纸质版及电子版)\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index4\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"招标图纸及竣工图纸(图如有)(纸质版及电子版)必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"施工投标文件(含技术标及经济标纸质及软件版)及澄清、承诺文件\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index5\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"施工投标文件(含技术标及经济标纸质及软件版)及澄清、承诺文件必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"经报审部门初步复核结算文件中的图纸、治商实施的结算书(纸质版及软件电子版)\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index6\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"经报审部门初步复核结算文件中的图纸、治商实施的结算书(纸质版及软件电子版)必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"设计变更单及工程洽商记录、现场签证单\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index7\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"设计变更单及工程洽商记录、现场签证单必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"特殊工程需要有相关部门出具的检测报告\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index8\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"特殊工程需要有相关部门出具的检测报告必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"施工合同及补充协议\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index9\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"施工合同及补充协议必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"经报审单位初步审核确认的材料、设备认价单等\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index10\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"经报审单位初步审核确认的材料、设备认价单等必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"开工报告\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index11\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"开工报告必须填写\\\"}]},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"如果涉及到有拆除工程需要现场签证单及相关影像资料\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"有\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"无\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_Index12\\\",\\\"labelWidth\\\":\\\"400\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"如果涉及到有拆除工程需要现场签证单及相关影像资料必须填写\\\"}]},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"结算审核方式\\\",\\\"prop\\\":\\\"a172603803641648390\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"审核方式\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"内审\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"外审\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_way\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"审核方式必须填写\\\"}]},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"结算初审\\\",\\\"prop\\\":\\\"a172603806961165016\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"初审意见\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"pay_opin\\\"},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"pay_opin_f\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"是否通过\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_appr\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"是否通过必须填写\\\"}]},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"出具结算金额（外审）\\\",\\\"prop\\\":\\\"a172603897796222186\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"初审结算金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"pay_cost\\\",\\\"append\\\":\\\"元\\\"},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"pay_cost_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"确认金额\\\",\\\"prop\\\":\\\"a172603930943298602\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"是否同意\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_cost_appr\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"是否同意必须填写\\\"}]},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"达成一致时间\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"apy_agree_date\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"达成一致时间必须填写\\\"}]},{\\\"type\\\":\\\"checkbox\\\",\\\"label\\\":\\\"施工单位已确认\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"}],\\\"span\\\":12,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_b_con\\\",\\\"labelWidth\\\":\\\"150\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"施工单位已确认必须填写\\\"}]},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"pay_disagree_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"外审修改结算金额\\\",\\\"prop\\\":\\\"a172603962086210644\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"修改结算金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"pay_cost_fix\\\",\\\"append\\\":\\\"元\\\"},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"pay_cost_fix_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"外审意见\\\",\\\"prop\\\":\\\"a172604002839985551\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"外审意见\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"ext_pay_opin\\\"},{\\\"label\\\":\\\"附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":24,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"ext_pay_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"移交定案表\\\",\\\"prop\\\":\\\"a172604013473316990\\\"},{\\\"type\\\":\\\"datetime\\\",\\\"label\\\":\\\"接收时间\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD HH:mm:ss\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD HH:mm:ss\\\",\\\"prop\\\":\\\"pay_rec_time\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"定案表盖章\\\",\\\"prop\\\":\\\"a172604016200391615\\\"},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"转出时间\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"pay_xfer_time\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"上传定案表\\\",\\\"prop\\\":\\\"a172604018688959411\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"最终结算定案金额\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"prop\\\":\\\"final_final_cost\\\",\\\"labelWidth\\\":\\\"150\\\",\\\"placeholder\\\":\\\"最终结算定案金额\\\"},{\\\"label\\\":\\\"定案表附件\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"pay_tbl_f\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"复核确认\\\",\\\"prop\\\":\\\"a172604028293394382\\\"},{\\\"type\\\":\\\"radio\\\",\\\"label\\\":\\\"是否复核\\\",\\\"dicData\\\":[{\\\"label\\\":\\\"是\\\",\\\"value\\\":\\\"0\\\"},{\\\"label\\\":\\\"否\\\",\\\"value\\\":\\\"1\\\"}],\\\"span\\\":24,\\\"display\\\":true,\\\"props\\\":{\\\"label\\\":\\\"label\\\",\\\"value\\\":\\\"value\\\",\\\"desc\\\":\\\"desc\\\"},\\\"prop\\\":\\\"pay_rchk\\\",\\\"required\\\":false},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"复核意见\\\",\\\"prop\\\":\\\"a172604030928850171\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"复核意见\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"pay_rchk_opin\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"处内意见\\\",\\\"prop\\\":\\\"a172604032350983708\\\"},{\\\"type\\\":\\\"textarea\\\",\\\"label\\\":\\\"处内意见\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"prop\\\":\\\"pay_finalopin\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"首次结算确认\\\",\\\"prop\\\":\\\"a172164171351591224\\\"},{\\\"label\\\":\\\"付款确认单\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"porder_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"付款确认单必须填写\\\"}]},{\\\"label\\\":\\\"发票\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"pinvoice_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"付款资料必须填写\\\"}]},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"付款日期\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"pay_date\\\"},{\\\"label\\\":\\\"付款请示单\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":true,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"prorder_f\\\",\\\"required\\\":true,\\\"rules\\\":[{\\\"required\\\":true,\\\"message\\\":\\\"付款请示单必须填写\\\"}]},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"质保金支付日期\\\",\\\"span\\\":12,\\\"display\\\":true,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"warrPay_date\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"质保金付款申请\\\",\\\"prop\\\":\\\"a172164173877582490\\\"},{\\\"type\\\":\\\"input\\\",\\\"label\\\":\\\"质保金金额\\\",\\\"span\\\":24,\\\"display\\\":false,\\\"prop\\\":\\\"qa_cost\\\",\\\"append\\\":\\\"元\\\"},{\\\"type\\\":\\\"title\\\",\\\"span\\\":24,\\\"display\\\":true,\\\"labelWidth\\\":\\\"0px\\\",\\\"textValue\\\":\\\"质保金结算确认\\\",\\\"prop\\\":\\\"a17219825091606025\\\"},{\\\"label\\\":\\\"付款确认单\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":false,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"qaorder_f\\\",\\\"required\\\":false},{\\\"label\\\":\\\"发票\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":false,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"qainvoice_f\\\",\\\"required\\\":false},{\\\"type\\\":\\\"date\\\",\\\"label\\\":\\\"付款日期\\\",\\\"span\\\":12,\\\"display\\\":false,\\\"format\\\":\\\"YYYY-MM-DD\\\",\\\"valueFormat\\\":\\\"YYYY-MM-DD\\\",\\\"prop\\\":\\\"qapay_date\\\"},{\\\"label\\\":\\\"付款请示单\\\",\\\"type\\\":\\\"upload\\\",\\\"propsHttp\\\":{\\\"res\\\":\\\"data\\\",\\\"url\\\":\\\"fileUrl\\\",\\\"name\\\":\\\"fileName\\\"},\\\"action\\\":\\\"/sinomis-authweb/SysMenu/upload\\\",\\\"display\\\":false,\\\"span\\\":12,\\\"showFileList\\\":true,\\\"multiple\\\":true,\\\"limit\\\":10,\\\"prop\\\":\\\"qarorder_f\\\",\\\"required\\\":false}],\\\"labelPosition\\\":\\\"left\\\",\\\"labelSuffix\\\":\\\"：\\\",\\\"labelWidth\\\":120,\\\"gutter\\\":0,\\\"menuBtn\\\":false,\\\"submitBtn\\\":true,\\\"submitText\\\":\\\"提交\\\",\\\"emptyBtn\\\":true,\\\"emptyText\\\":\\\"清空\\\",\\\"menuPosition\\\":\\\"center\\\"}\",\"form\":\"{column:[{type:'title',span:24,display:true,labelWidth:'0px',textValue:'基本信息',prop:'a172164087218141887'},{type:'input',label:'项目名称',span:12,display:true,prop:'project_name',placeholder:'隐藏表单 业务传值',required:false,readonly:false,disabled:false},{type:'input',label:'项目编号',span:12,display:true,prop:'project_id',placeholder:'隐藏表单 业务传值',required:false,readonly:false,disabled:false},{type:'input',label:'工程类型',span:12,display:true,prop:'project_type',placeholder:'隐藏表单 业务传值'},{type:'input',label:'供应商',span:12,display:true,prop:'supplier',required:false,placeholder:'隐藏表单 业务传值'},{type:'select',label:'供应商负责人',cascader:[],span:12,display:true,props:{label:'staffName',value:'id',desc:'desc'},prop:'supplier_leader',dicFormatter:(res) => {\\r\\n  return res.data.data.records\\r\\n},dicUrl:'/base_info/hospitalStaff/hospital-staff/list',dicMethod:'post',dicQuery:{current:'1',size:'999',hospitalCode:'SINOMIS',unitCode:'SINOMIS'}},{type:'input',label:'项目位置',span:12,display:true,prop:'project_location',placeholder:'隐藏表单 业务传值'},{type:'input',label:'具体描述',span:12,display:true,prop:'project_description',placeholder:'隐藏表单 业务传值',required:false},{type:'select',label:'立项科室',cascader:[],span:12,display:true,props:{label:'deptName',value:'id',desc:'desc'},prop:'project_department',required:true,rules:[{required:true,message:'立项科室必须填写'}],dicFormatter:(res) => {\\r\\n  return res.data.data.records\\r\\n},dicUrl:'/base_info/departmentManager/department-manager/selectByPage',dicMethod:'post',dicQuery:{current:'1',size:'999',hospitalCode:'SINOMIS',unitCode:'SINOMIS'},filterable:true},{type:'input',label:'报告编号',span:12,display:true,prop:'report_id',placeholder:'请输入OA单号',required:true,rules:[{required:true,message:'报告编号必须填写'}]},{type:'select',label:'主管领导',cascader:[],span:12,display:true,props:{label:'staffName',value:'id',desc:'desc'},prop:'leader_name',dicFormatter:(res) => {\\r\\n  return res.data.data.records\\r\\n},required:true,rules:[{required:true,message:'主管领导必须填写'}],dicUrl:'/base_info/hospitalStaff/hospital-staff/list',dicMethod:'post',dicQuery:{current:'1',size:'999',hospitalCode:'SINOMIS',unitCode:'SINOMIS'},filterable:true},{type:'select',label:'领导职位',dicData:[{label:'处长',value:'0'},{label:'副处长',value:'1'}],cascader:[],span:12,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'leader_position',required:true,rules:[{required:true,message:'领导职位必须填写'}],dataType:'number'},{type:'input',label:'专管员',span:12,display:true,prop:'special_officer',value:'${this.$store.state.userInfo.staffName}',readonly:true,required:true,rules:[{required:true,message:'专管员必须填写'}],placeholder:'当前操作人',disabled:true},{type:'textarea',label:'工程概况及委托内容',span:24,display:true,prop:'project_overview',placeholder:'请输入',required:true,rules:[{required:true,message:'工程概况及委托内容必须填写'}]},{type:'radio',label:'现场是否具备条件(如水、电、场地条件等)',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'conditions_met',labelWidth:'300',required:true,rules:[{required:true,message:'现场是否具备条件(如水、电、场地条件等)必须填写'}]},{type:'radio',label:'是否需要备案',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'nd_Filed',required:true,rules:[{required:true,message:'是否需要备案必须填写'}]},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'施工方案',prop:'a17219821302448305'},{type:'textarea',label:'地点',span:24,display:true,prop:'loc',placeholder:'请输入',required:true,rules:[{required:true,message:'地点必须填写'}]},{type:'textarea',label:'面积',span:24,display:true,prop:'area',placeholder:'请输入',required:true,rules:[{required:true,message:'面积必须填写'}]},{type:'textarea',label:'内容',span:24,display:true,prop:'cont',placeholder:'请输入',required:true,rules:[{required:true,message:'内容必须填写'}]},{type:'textarea',label:'工期',span:24,display:true,prop:'dur',placeholder:'请输入',required:true,rules:[{required:true,message:'工期必须填写'}]},{type:'textarea',label:'工艺',span:24,display:true,prop:'proc',placeholder:'请输入',required:true,rules:[{required:true,message:'工艺必须填写'}]},{type:'textarea',label:'材料',span:24,display:true,prop:'mat',placeholder:'请输入',required:true,rules:[{required:true,message:'材料必须填写'}]},{type:'textarea',label:'安全措施',span:24,display:true,prop:'safe',placeholder:'请输入',required:true,rules:[{required:true,message:'安全措施必须填写'}]},{type:'textarea',label:'其他',span:24,display:true,prop:'misc',placeholder:'请输入'},{label:'图纸文件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'dwg_f',required:false},{label:'预算文件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'budg_f',required:false},{label:'其他文件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'misc_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'处内预算信息',prop:'a172164088391243108'},{type:'input',label:'预算送审金额',span:12,display:true,prop:'sbudg_cost',required:true,rules:[{required:true,message:'预算送审金额必须填写'}],placeholder:'请输入',append:'元'},{type:'input',label:'处内审定金额',span:12,display:true,prop:'budg_cost',placeholder:'请输入',required:true,rules:[{required:true,message:'处内审定金额必须填写'}],append:'元'},{label:'预算文件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'budg_cost_f',required:true,rules:[{required:true,message:'预算文件必须填写'}]},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'送审资料信息',prop:'a172198219163914044'},{label:'OA附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'oa_f'},{type:'radio',label:'立项批复（如院内请示报告等）',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'audit_Index1',labelWidth:'400',required:true,rules:[{required:true,message:'立项批复（如院内请示报告等）必须填写'}]},{type:'radio',label:'如通过招标方式确定，则需提供招标文件、中标通知书等',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'audit_Index2',labelWidth:'400',required:true,rules:[{required:true,message:'如通过招标方式确定，则需提供招标文件、中标通知书等必须填写'}]},{type:'radio',label:'经报审单位签字确认的施工图纸及说明（纸质及电子版）',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'audit_Index3',labelWidth:'400',required:true,rules:[{required:true,message:'经报审单位签字确认的施工图纸及说明（纸质及电子版）必须填写'}]},{type:'radio',label:'经报审单位签字确认的工程量清单及预算（纸质及电子版）',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'audit_Index4',labelWidth:'400',required:true,rules:[{required:true,message:'经报审单位签字确认的工程量清单及预算（纸质及电子版）必须填写'}]},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'审计预算初审',prop:'a172285666078288032'},{type:'textarea',label:'初审意见',span:24,display:true,prop:'audit_opin'},{label:'审核文件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'audit_opin_f',required:true,rules:[{required:true,message:'审核文件必须填写'}]},{type:'radio',label:'是否通过',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'audit_appr',required:true,rules:[{required:true,message:'是否通过必须填写'}]},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'出具初审金额',prop:'a172603657461819308'},{type:'input',label:'初审金额',span:12,display:true,prop:'audit_cost',showWordLimit:false,append:'元'},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'audit_cost_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'确认金额',prop:'a172603451721784217'},{type:'radio',label:'是否同意',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'audit_cost_appr',required:true,rules:[{required:true,message:'是否同意必须填写'}]},{type:'date',label:'达成一致时间',span:12,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'audit_agree_date'},{type:'checkbox',label:'施工单位已确认',dicData:[{label:'是',value:'0'}],span:12,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'audit_b_con',labelWidth:'140'},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'audit_disagree_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'修改初审金额',prop:'a172603694321321883'},{type:'input',label:'修改初审金额',span:12,display:true,prop:'audit_cost_fix',append:'元'},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'audit_cost_fix_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'外审意见',prop:'a17260370213493663'},{type:'textarea',label:'外审意见',span:24,display:true,prop:'ext_audit_opin'},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'ext_audit_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'移交定案表',prop:'a172603706119489660'},{type:'datetime',label:'接收时间',span:24,display:true,format:'YYYY-MM-DD HH:mm:ss',valueFormat:'YYYY-MM-DD HH:mm:ss',prop:'audit_rec_time'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'定案表盖章',prop:'a172603715267816528'},{type:'datetime',label:'转出时间',span:24,display:true,format:'YYYY-MM-DD HH:mm:ss',valueFormat:'YYYY-MM-DD HH:mm:ss',prop:'audit_xfer_time'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'上传定案表',prop:'a172603718019022829'},{type:'input',label:'最终预算定案金额',span:12,display:true,prop:'final_audit_cost',append:'元',readonly:false,placeholder:'请输入',labelWidth:'150'},{label:'定案表附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'audit_tbl_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'复核确认',prop:'a172603725973211845'},{type:'radio',label:'是否复核',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'audit_rchk',required:true,rules:[{required:true,message:'是否复核必须填写'}]},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'复核人意见',prop:'a17260372958828974'},{type:'textarea',label:'复核意见',span:24,display:true,prop:'audit_rchk_opin'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'处内意见',prop:'a172603731685577319'},{type:'textarea',label:'处内意见',span:24,display:true,prop:'audit_finalopin'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'备案资料',prop:'a172164093200033343'},{label:'公示牌附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'sign_f',required:true,rules:[{required:true,message:'公示牌附件必须填写'}]},{label:'其他附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'rec_f',required:false},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'开工准备',prop:'a172164097323484910'},{label:'开工单',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'sorder_f',required:true,rules:[{required:true,message:'开工单必须填写'}]},{label:'拆除单',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'dorder_f',required:false},{label:'现场照片',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'sphoto_f',required:true,rules:[{required:true,message:'现场照片必须填写'}]},{type:'date',label:'预计开工日期',span:12,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'estart_date'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'开工确认',prop:'a172164105926540727'},{type:'date',label:'实际开工日期',span:12,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'astart_date'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'完工申请',prop:'a172164114068332208'},{label:'现场照片',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'fphoto_f',required:true,rules:[{required:true,message:'现场照片必须填写'}]},{type:'input',label:'土建验收情况',span:12,display:false,prop:'cst_stat',required:false},{type:'input',label:'土建验收结论',span:12,display:false,prop:'cst_cncl',required:false},{type:'input',label:'给排水验收情况',span:12,display:false,prop:'plmb_stat',required:false},{type:'input',label:'给排水验收情况',span:12,display:false,prop:'plmb_cncl',required:false},{type:'input',label:'暖通验收情况',span:12,display:false,prop:'hvac_stat',required:false},{type:'input',label:'暖通验收情况',span:12,display:false,prop:'hvac_cncl',required:false},{type:'input',label:'电气验收情况',span:12,display:false,prop:'elec_stat',required:false},{type:'input',label:'电气验收情况',span:12,display:false,prop:'elec_cncl',required:false},{type:'input',label:'弱电验收情况',span:12,display:false,prop:'lowv_stat',required:false},{type:'input',label:'弱电验收情况',span:12,display:false,prop:'lowv_cncl',required:false},{type:'input',label:'消防验收情况',span:12,display:false,prop:'fire_stat',required:false},{type:'input',label:'消防验收情况',span:12,display:false,prop:'fire_cncl',required:false},{type:'input',label:'气体验收情况',span:12,display:false,prop:'gass_stat',required:false},{type:'input',label:'气体验收情况',span:12,display:false,prop:'gass_cncl',required:false},{type:'input',label:'物业验收情况',span:12,display:false,prop:'prop_stat',required:false},{type:'input',label:'物业验收情况',span:12,display:false,prop:'prop_cncl',required:false},{type:'input',label:'综合验收结论',span:24,display:false,prop:'final_cncl',required:false},{label:'验收单',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'finish_f',required:true,rules:[{required:true,message:'验收单必须填写'}]},{type:'date',label:'预计完工日期',span:12,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'efinfsh_date'},{type:'date',label:'实际完工日期',span:12,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'afinish_date'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'结算申请',prop:'a172164161989596901'},{label:'结算文件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'payreq_f',required:true,rules:[{required:true,message:'结算文件必须填写'}]},{label:'工程量确认单',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'workconf_f',required:true,rules:[{required:true,message:'现场照片必须填写'}]},{type:'input',label:'申请结算金额',span:12,display:true,prop:'payreq_cost',placeholder:'请输入',required:true,rules:[{required:true,message:'申请结算金额必须填写'}],append:'元'},{label:'其他附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'payreq_other_f',required:false,labelTip:'若有变更则需要上传'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'专管员确认',prop:'a172164167024235980'},{label:'上传附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'payreq_con_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'处内审核',prop:'a172170287125164193'},{label:'结算文件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'paydep_f',required:true,rules:[{required:true,message:'结算文件必须填写'}]},{type:'input',label:'送审计金额',span:12,display:true,prop:'paydep_cost',placeholder:'请输入',required:true,rules:[{required:true,message:'送审计金额必须填写'}],append:'元'},{type:'radio',label:'是否有洽商签证',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'paydep_con',required:true,rules:[{required:true,message:'是否有洽商签证必须填写'}]},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'paydep_con_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'送审资料信息',prop:'a172959590081252579'},{type:'radio',label:'经报审单位确认的竣工验收报告/工程验收单',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index1',labelWidth:'400',required:true,rules:[{required:true,message:'经报审单位确认的竣工验收报告/工程验收单必须填写'}]},{type:'radio',label:'实行监理审核的要提供监理审核意见(须提供监理审核完的广联达软件版)',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index2',labelWidth:'400',required:true,rules:[{required:true,message:'实行监理审核的要提供监理审核意见(须提供监理审核完的广联达软件版)必须填写'}]},{type:'radio',label:'工程招标文件、招标答疑文件及其他有关文件',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index3',labelWidth:'400',required:true,rules:[{required:true,message:'工程招标文件、招标答疑文件及其他有关文件必须填写'}]},{type:'radio',label:'招标图纸及竣工图纸(图如有)(纸质版及电子版)',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index4',labelWidth:'400',required:true,rules:[{required:true,message:'招标图纸及竣工图纸(图如有)(纸质版及电子版)必须填写'}]},{type:'radio',label:'施工投标文件(含技术标及经济标纸质及软件版)及澄清、承诺文件',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index5',labelWidth:'400',required:true,rules:[{required:true,message:'施工投标文件(含技术标及经济标纸质及软件版)及澄清、承诺文件必须填写'}]},{type:'radio',label:'经报审部门初步复核结算文件中的图纸、治商实施的结算书(纸质版及软件电子版)',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index6',labelWidth:'400',required:true,rules:[{required:true,message:'经报审部门初步复核结算文件中的图纸、治商实施的结算书(纸质版及软件电子版)必须填写'}]},{type:'radio',label:'设计变更单及工程洽商记录、现场签证单',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index7',labelWidth:'400',required:true,rules:[{required:true,message:'设计变更单及工程洽商记录、现场签证单必须填写'}]},{type:'radio',label:'特殊工程需要有相关部门出具的检测报告',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index8',labelWidth:'400',required:true,rules:[{required:true,message:'特殊工程需要有相关部门出具的检测报告必须填写'}]},{type:'radio',label:'施工合同及补充协议',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index9',labelWidth:'400',required:true,rules:[{required:true,message:'施工合同及补充协议必须填写'}]},{type:'radio',label:'经报审单位初步审核确认的材料、设备认价单等',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index10',labelWidth:'400',required:true,rules:[{required:true,message:'经报审单位初步审核确认的材料、设备认价单等必须填写'}]},{type:'radio',label:'开工报告',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index11',labelWidth:'400',required:true,rules:[{required:true,message:'开工报告必须填写'}]},{type:'radio',label:'如果涉及到有拆除工程需要现场签证单及相关影像资料',dicData:[{label:'有',value:'0'},{label:'无',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_Index12',labelWidth:'400',required:true,rules:[{required:true,message:'如果涉及到有拆除工程需要现场签证单及相关影像资料必须填写'}]},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'结算审核方式',prop:'a172603803641648390'},{type:'radio',label:'审核方式',dicData:[{label:'内审',value:'0'},{label:'外审',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_way',required:true,rules:[{required:true,message:'审核方式必须填写'}]},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'结算初审',prop:'a172603806961165016'},{type:'textarea',label:'初审意见',span:24,display:true,prop:'pay_opin'},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'pay_opin_f'},{type:'radio',label:'是否通过',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_appr',required:true,rules:[{required:true,message:'是否通过必须填写'}]},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'出具结算金额（外审）',prop:'a172603897796222186'},{type:'input',label:'初审结算金额',span:12,display:true,prop:'pay_cost',append:'元'},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'pay_cost_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'确认金额',prop:'a172603930943298602'},{type:'radio',label:'是否同意',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_cost_appr',required:true,rules:[{required:true,message:'是否同意必须填写'}]},{type:'date',label:'达成一致时间',span:12,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'apy_agree_date',required:true,rules:[{required:true,message:'达成一致时间必须填写'}]},{type:'checkbox',label:'施工单位已确认',dicData:[{label:'是',value:'0'}],span:12,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_b_con',labelWidth:'150',required:true,rules:[{required:true,message:'施工单位已确认必须填写'}]},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'pay_disagree_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'外审修改结算金额',prop:'a172603962086210644'},{type:'input',label:'修改结算金额',span:12,display:true,prop:'pay_cost_fix',append:'元'},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'pay_cost_fix_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'外审意见',prop:'a172604002839985551'},{type:'textarea',label:'外审意见',span:24,display:true,prop:'ext_pay_opin'},{label:'附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:24,showFileList:true,multiple:true,limit:10,prop:'ext_pay_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'移交定案表',prop:'a172604013473316990'},{type:'datetime',label:'接收时间',span:24,display:true,format:'YYYY-MM-DD HH:mm:ss',valueFormat:'YYYY-MM-DD HH:mm:ss',prop:'pay_rec_time'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'定案表盖章',prop:'a172604016200391615'},{type:'date',label:'转出时间',span:24,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'pay_xfer_time'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'上传定案表',prop:'a172604018688959411'},{type:'input',label:'最终结算定案金额',span:12,display:true,prop:'final_final_cost',labelWidth:'150',placeholder:'最终结算定案金额'},{label:'定案表附件',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'pay_tbl_f'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'复核确认',prop:'a172604028293394382'},{type:'radio',label:'是否复核',dicData:[{label:'是',value:'0'},{label:'否',value:'1'}],span:24,display:true,props:{label:'label',value:'value',desc:'desc'},prop:'pay_rchk',required:false},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'复核意见',prop:'a172604030928850171'},{type:'textarea',label:'复核意见',span:24,display:true,prop:'pay_rchk_opin'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'处内意见',prop:'a172604032350983708'},{type:'textarea',label:'处内意见',span:24,display:true,prop:'pay_finalopin'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'首次结算确认',prop:'a172164171351591224'},{label:'付款确认单',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'porder_f',required:true,rules:[{required:true,message:'付款确认单必须填写'}]},{label:'发票',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'pinvoice_f',required:true,rules:[{required:true,message:'付款资料必须填写'}]},{type:'date',label:'付款日期',span:12,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'pay_date'},{label:'付款请示单',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:true,span:12,showFileList:true,multiple:true,limit:10,prop:'prorder_f',required:true,rules:[{required:true,message:'付款请示单必须填写'}]},{type:'date',label:'质保金支付日期',span:12,display:true,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'warrPay_date'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'质保金付款申请',prop:'a172164173877582490'},{type:'input',label:'质保金金额',span:24,display:false,prop:'qa_cost',append:'元'},{type:'title',span:24,display:true,labelWidth:'0px',textValue:'质保金结算确认',prop:'a17219825091606025'},{label:'付款确认单',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:false,span:12,showFileList:true,multiple:true,limit:10,prop:'qaorder_f',required:false},{label:'发票',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:false,span:12,showFileList:true,multiple:true,limit:10,prop:'qainvoice_f',required:false},{type:'date',label:'付款日期',span:12,display:false,format:'YYYY-MM-DD',valueFormat:'YYYY-MM-DD',prop:'qapay_date'},{label:'付款请示单',type:'upload',propsHttp:{res:'data',url:'fileUrl',name:'fileName'},action:'/sinomis-authweb/SysMenu/upload',display:false,span:12,showFileList:true,multiple:true,limit:10,prop:'qarorder_f',required:false}],labelPosition:'left',labelSuffix:'：',labelWidth:120,gutter:0,menuBtn:false,submitBtn:true,submitText:'提交',emptyBtn:true,emptyText:'清空',menuPosition:'center'}\",\"startForm\":[{\"readable\":true,\"name\":\"立项科室\",\"id\":\"project_department\",\"type\":{},\"value\":\"\",\"required\":true,\"writable\":true},{\"readable\":true,\"name\":\"报告编号\",\"id\":\"report_id\",\"type\":{},\"value\":\"\",\"required\":true,\"writable\":true},{\"readable\":true,\"name\":\"主管领导\",\"id\":\"leader_name\",\"type\":{},\"value\":\"\",\"required\":true,\"writable\":true},{\"readable\":true,\"name\":\"领导职位\",\"id\":\"leader_position\",\"type\":{},\"value\":\"\",\"required\":true,\"writable\":true},{\"readable\":true,\"name\":\"专管员\",\"id\":\"special_officer\",\"type\":{},\"value\":\"\",\"required\":false,\"writable\":false},{\"readable\":true,\"name\":\"工程概况及委托内容\",\"id\":\"project_overview\",\"type\":{},\"value\":\"\",\"required\":true,\"writable\":true},{\"readable\":true,\"name\":\"现场是否具备条件(如水、电、场地条件等)\",\"id\":\"conditions_met\",\"type\":{},\"value\":\"\",\"required\":true,\"writable\":true},{\"readable\":true,\"name\":\"是否需要备案\",\"id\":\"nd_Filed\",\"type\":{},\"value\":\"\",\"required\":true,\"writable\":true}]}"
// 根据可读可写，过滤column
export function filterAvueColumn(
  column,
  taskForm,
  isExForm = false,
  props = {
    label: 'label',
    prop: 'prop'
  },
  _this
) {
  if (!column || column.length == 0)
    return {
      column,
      vars: []
    }
  const values = []
  const vars = []
  const versionFlag106 = '1.0.6'
  column.forEach((col) => {
    const c = taskForm.find((s) => s.id == col[props.prop])
    if (c && c.readable) {
      if (!isExForm) {
        // 非外置表单 处理事件
        const event = ['change', 'blur', 'click', 'focus', 'lazyLoad', 'onLoad']
        event.forEach((e) => {
          if (col[e]) col[e] = eval((col[e] + '').replace(/this/g, '_this'))
        })
        if (col.event) Object.keys(col.event).forEach((key) => (col.event[key] = eval((col.event[key] + '').replace(/this/g, '_this'))))
      }
      if (c.writable) {
        // 可写，记录需要提交的字段、处理字段默认值
        vars.push(col[props.prop])
        if (col.value) col.value = getDefaultValues(col.value)
      } else {
        // 不可写，清除校验、默认值
        if (col.type == 'dynamic') {
          col.children.addBtn = false
          col.children.delBtn = false

          // 子表单中有字段可读可写，记录主字段可提交
          col.children.column.forEach((cc) => {
            const child = taskForm.find((t) => t.id == cc[props.prop])
            if (child && child.writable && child.readable) vars.push(col[props.prop])
          })
        } else {
          col.readonly = true
          col.disabled = true
        }
        delete col.rules
        if (col.value) {
          col.value = getDefaultValues(col.value)
        } else delete col.value
        // delete col.event
        // event.forEach(e => delete col[e])
      }
      if (col.type == 'dynamic') {
        // 处理子表单
        col.children.column = filterAvueColumn(col.children.column, taskForm, isExForm, '', _this).column
      }
      if (col.rules && col.pattern) {
        // 处理正则
        col.rules.forEach((c) => {
          if (c.pattern) c.pattern = new RegExp(col.pattern)
        })
      }
      if (col.display !== false) col.display = true

      // 处理字段必填
      let required = false
      if (versionFlag106) {
        // 1.0.6及版本以下，required默认为true
        required = true
      } else {
        // 1.0.7流程设计器表单配置支持required属性
        required = c.required
      }
      if (!required && col.rules) {
        const index = col.rules.findIndex((c) => c.required)
        if (index != -1) col.rules.splice(index, 1)
      }
    } else {
      col.display = false
    }
    if (col.value) {
      col.value = getDefaultValues(col.value)
    }
    values.push(col)
  })
  return {
    column: values,
    vars
  }
}

export function getDefaultValues(value) {
  if (value.includes('${') && value.includes('}')) {
    try {
      return new Function(`with(this) { return \`${value}\` }`)
        .call(getContext())
    } catch (err) {
      console.warn('getDefaultValues error:', err)
      return value
    }
  } else {
    return value
  }
}

// 统一管理所有可用的工具方法
const getContext = () => ({
  // 给 context 添加 this 引用
  get this() {
    return this
  },
  flowGenericUtils(type, format) {
    if (type === 'dateFormat') {
      const date = new Date()
      const map = {
        'YYYY': date.getFullYear(),
        'MM': String(date.getMonth() + 1).padStart(2, '0'),
        'DD': String(date.getDate()).padStart(2, '0'),
        'HH': String(date.getHours()).padStart(2, '0'),
        'mm': String(date.getMinutes()).padStart(2, '0'),
        'ss': String(date.getSeconds()).padStart(2, '0')
      }
      return Object.keys(map).reduce((result, key) =>
        result.replace(key, map[key]), format)
    } else if (type === 'userInfo') {
      return store.state.loginInfo[format]
    } else if (type === 'getTime') {
      return new Date().getTime()
    }
    // 在这里可以方便地添加其他类型的处理
    return ''
  }
  // 可以在这里添加更多工具方法
})

export function getAxiosData(config,callback) {
  axios.request({
    url: config.dicUrl,
    method: config.dicMethod,
    baseURL: __PATH.BASE_PATH,
    headers: {
      hospitalcode: 'SINOMIS',
      unitcode: 'SINOMIS',
      authorization: 'Bearer ' + localStorage.getItem('token'),
      hospitalSth: localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
    },
    params: config.dicMethod ==='get' ? {...config.dicQuery} : undefined,
    data: config.dicMethod ==='post' ? {...config.dicQuery} : undefined,
    timeout: 60000,
  }).then(res=>{
    let list = []
    if(res.data.code != 200) return callback(list)
    if(config.dicFormatter) list = config.dicFormatter(res) ? config.dicFormatter(res) : []
    else list = res.data.data ? res.data.data : []
    callback(list)
  })
}

export function getOptionListData(config,list) {
  return list.map(item => {
    const flag = config.props && config.props.label && config.props.value
    return {
      text: flag ? item[config.props.label] : item.label,
      value: flag ? item[config.props.value] : item.value,
    }
  })
}


export const getObjType = (obj) => {
  var toString = Object.prototype.toString
  var map = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object'
  }
  return map[toString.call(obj)]
}
export const deepClone = (data) => {
  var type = getObjType(data)
  var obj
  if (type === 'array') obj = []
  else if (type === 'object') obj = {}
  else return data
  if (type === 'array') {
    for (var i = 0, len = data.length; i < len; i++) {
      data[i] = (() => {
        if (data[i] === 0) {
          return data[i]
        }
        return data[i]
      })()
      if (data[i]) {
        delete data[i].$parent
      }
      obj.push(deepClone(data[i]))
    }
  } else if (type === 'object') {
    for (var key in data) {
      if (data) {
        delete data.$parent
      }
      obj[key] = deepClone(data[key])
    }
  }
  return obj
}
export function validateNull(val) {
  // 特殊判断
  if (val && parseInt(val) === 0) return false
  const list = ['$parent']
  if (val instanceof Date || typeof val === 'boolean' || typeof val === 'number') return false
  if (val instanceof Array) {
    if (val.length === 0) return true
  } else if (val instanceof Object) {
    val = deepClone(val)
    list.forEach((ele) => {
      delete val[ele]
    })
    for (var o in val) {
      return false
    }
    return true
  } else {
    if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '') {
      return true
    }
    return false
  }
  return false
}
