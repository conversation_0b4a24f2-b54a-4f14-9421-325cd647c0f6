import { DIC_PROPS, DIC_SPLIT, DIC_HTTP_PROPS } from '../utils/variable.js'
import event from './event.js'
export default function () {
  return {
    mixins: [event()],
    emits: ['input', 'click', 'focus', 'blur', 'change'],
    data() {
      return {
        stringMode: false,
        // name: '',
        text: undefined
      }
    },
    props: {
      blur: Function,
      focus: Function,
      change: Function,
      click: Function,
      typeformat: Function,
      control: Function,
      separator: {
        type: String,
        default: DIC_SPLIT
      },
      params: {
        type: Object,
        default: () => {
          return {}
        }
      },
      listType: {
        type: String
      },
      modelValue: {},
      column: {
        type: Object,
        default: () => {
          return {}
        }
      },
      label: {
        type: String,
        default: ''
      },
      readonly: {
        type: Boolean,
        default: false
      },
      size: {
        type: String,
        default: ''
      },
      tip: {
        type: String,
        default: ''
      },
      disabled: {
        type: Boolean,
        default: false
      },
      dataType: {
        type: String
      },
      clearable: {
        type: Boolean,
        default: true
      },
      type: {
        type: String,
        default: ''
      },
      dicUrl: {
        type: String,
        default: ''
      },
      dicMethod: {
        type: String,
        default: ''
      },
      buttonText: {
        type: String,
        default: '点击上传'
      },
      dicFormatter: Function,
      dicQuery: {
        type: Object,
        default: () => {
          return {}
        }
      },
      dic: {
        type: Array,
        default: () => {
          return []
        }
      },
      placeholder: {
        type: String,
        default: ''
      },
      rules: {
        type: Array
      },
      min: {
        type: Number
      },
      max: {
        type: Number
      },
      multiple: {
        type: Boolean,
        default: false
      },
      button: {
        type: Boolean,
        default: false
      },
      row: {
        type: Boolean,
        default: false
      },
      prop: {
        type: String,
        default: ''
      },
      border: {
        type: Boolean,
        default: false
      },
      popperClass: {
        type: String
      },
      propsHttp: {
        type: Object,
        default: () => DIC_HTTP_PROPS
      },
      props: {
        type: Object,
        default: () => {
          return {}
        }
      },
      dynamicIndex: {
        type: Number
      }
    },
    watch: {
      text: {
        handler(val) {
          this.handleChange(val)
          this.handleTextValue && this.handleTextValue(val)
        },
        deep: true
      },
      modelValue: {
        handler(val) {
          this.initVal()
          this.handleModelValue && this.handleModelValue(val)
        }
      }
    },
    computed: {
      clearableVal() {
        return this.disabled ? false : this.clearable
      },
      // componentName() {
      //   const type = 'el'
      //   const result = `${type}-${this.name}${this.button ? '-button' : ''}`
      //   return result
      // },
      required() {
        return !this.validateNull(this.rules)
      },
      isGroup() {
        let result = false
        this.dic.forEach((ele) => {
          if (ele[this.groupsKey]) {
            result = true
          }
        })
        return result
      },
      isArray() {
        return this.dataType === 'array'
      },
      isString() {
        return this.dataType === 'string'
      },
      isNumber() {
        return this.dataType === 'number'
      },
      nameKey() {
        return this.propsHttp.name || DIC_HTTP_PROPS.name
      },
      urlKey() {
        return this.propsHttp.url || DIC_HTTP_PROPS.url
      },
      resKey() {
        return this.propsHttp.res || DIC_HTTP_PROPS.res
      },
      groupsKey() {
        return this.props.groups || DIC_PROPS.groups
      },
      valueKey() {
        return this.props.value || DIC_PROPS.value
      },
      descKey() {
        return this.props.desc || DIC_PROPS.desc
      },
      leafKey() {
        return this.props.leaf || DIC_PROPS.leaf
      },
      labelKey() {
        return this.props.label || DIC_PROPS.label
      },
      childrenKey() {
        return this.props.children || DIC_PROPS.children
      },
      disabledKey() {
        return this.props.disabled || DIC_PROPS.disabled
      },
      idKey() {
        return this.props.id || DIC_PROPS.id
      }
    },
    created() {
      this.initVal()
    }
  }
}