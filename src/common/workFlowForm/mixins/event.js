/*
 * @Author: hedd <EMAIL>
 * @Date: 2025-04-09 17:04:22
 * @LastEditors: hedd <EMAIL>
 * @LastEditTime: 2025-04-09 17:44:26
 * @FilePath: \ybs_h5\src\common\workFlowForm\mixins\event.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// import { initVal } from '../utils/data-format.js'
import { DIC_SPLIT } from '../utils/variable.js'
function bindEvent(safe, name, event) {
  typeof safe[name] === 'function' && safe[name]({ value: safe.value, column: safe.column, index: safe.dynamicIndex })
  safe.$emit(name, safe.value, event)
}
export default function () {
  return {
    data() {
      return {
        mapKeyCacheList: {}
      }
    },
    mounted() {
      this.mapKeyCacheList = {}
    },
    methods: {
      initVal(value) {
        this.stringMode = typeof value == 'string'
        // this.stringMode = typeof this.value == 'string'
        // this.text = initVal(this.modelValue, this.column)
      },
      getLabelText(item) {
        if (this.validateNull(item)) return ''
        if (typeof this.typeformat === 'function') {
          return this.typeformat(item, this.labelKey, this.valueKey)
        }
        return item[this.labelKey]
      },
      handleFocus(event) {
        bindEvent(this, 'focus', event)
      },
      handleBlur(event) {
        bindEvent(this, 'blur', event)
      },
      handleClick(event) {
        bindEvent(this, 'click', event)
      },
      handleChange(value) {
        let result = value
        let flag = this.isString || this.isNumber || this.initVal(value) || this.listType === 'picture-img'
        if (flag && Array.isArray(value)) result = value.join(DIC_SPLIT)
        let selectInfo = {}
        if (this.column.type === 'select' && this.options && this.options.length) {
          const key = this.column.props && this.column.props.value ? this.column.props.value : 'id'
          selectInfo = this.options.find((item) => item[key] === value)
        }
        if (typeof this.change === 'function' && this.column.cell !== true) {
          this.change({ value: result, column: this.column, index: this.dynamicIndex }, selectInfo)
        }
        if (['upload', 'cascader'].includes(this.column.type) && this.column.rules && this.column.rules[0].required) {
          // 第一次框架做了校验，处理成不做校验
          if (Array.isArray(result) ? !!result.length : !!result) {
            this.mapKeyCacheList[this.column.type] = result
          }
          if (this.mapKeyCacheList[this.column.type]) this.$emit('clearRules', [this.column.prop])
        }
        this.$emit('input', result)
        this.$emit('change', result, selectInfo)
        // this.$emit('dynamicChange', result)
      }
    }
  }
}
