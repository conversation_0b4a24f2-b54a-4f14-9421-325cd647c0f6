<template>
  <div id="wrapper">
    <div class="signature">
      <div class="signature-title">
        <div></div>
        <div class="clear" ref="clearCanvas" @click="clear">
          <img class="clear-img" src="../assets/images/ic-resign.png" />
          <span>重新书写</span>
        </div>
      </div>
      <div class="canvas-wrapper">
        <img class="img" v-show="imgif" :src="imgurl" alt="" />
        <canvas id="canvas" v-show="!imgif"></canvas>
      </div>
    </div>
    <div class="btns">
      <button class="sure-btn" ref="saveCanvas" @click="close">取消</button>
      <button class="sure-btn" ref="saveCanvas" @click="save" :disabled="imgurl ? true : false">确定</button>
    </div>
  </div>
</template>
  
  <script type=text/ecmascript-6>
import wx from "weixin-js-sdk";

let flag = 0;
let draw, width, height;
let preHandler = function (e) {
  e.preventDefault();
};
width = document.documentElement.clientWidth - 40;
height = document.documentElement.clientHeight * 1 - 130;
class Draw {
  constructor(el) {
    this.el = el;
    this.canvas = document.getElementById(this.el);
    this.cxt = this.canvas.getContext("2d");
    this.canvas.width = width;
    this.canvas.height = height;
    this.wen = "lalala";
    this.stage_info = canvas.getBoundingClientRect();
    this.path = {
      beginX: 0,
      beginY: 0,
      endX: 0,
      endY: 0
    };
  }
  text(name) {
    console.log(name);

    this.cxt.font = "38px sans-serif";
    this.cxt.fillText(name, 100, 150);
    this.save();
  }
  init(btn) {
    let that = this;

    this.canvas.addEventListener("touchstart", function (event) {
      document.addEventListener("touchstart", preHandler, true);
      that.drawBegin(event);
    });
    this.canvas.addEventListener("touchend", function (event) {
      document.addEventListener("touchend", preHandler, true);
      that.drawEnd();
    });
    this.clear(btn);
  }
  isDrawFlag() {
    return flag;
  }
  drawBegin(e) {
    flag = 1;
    let that = this;
    window.getSelection() ? window.getSelection().removeAllRanges() : document.selection.empty();
    this.cxt.strokeStyle = "#000";

    this.cxt.beginPath();
    this.cxt.moveTo(e.changedTouches[0].clientX - this.stage_info.left, e.changedTouches[0].clientY - this.stage_info.top);
    this.path.beginX = e.changedTouches[0].clientX - this.stage_info.left;
    this.path.beginY = e.changedTouches[0].clientY - this.stage_info.top;
    canvas.addEventListener("touchmove", function () {
      that.drawing(event);
    });
  }
  drawing(e) {
    this.cxt.lineTo(e.changedTouches[0].clientX - this.stage_info.left, e.changedTouches[0].clientY - this.stage_info.top);
    this.path.endX = e.changedTouches[0].clientX - this.stage_info.left;
    this.path.endY = e.changedTouches[0].clientY - this.stage_info.top;
    this.cxt.stroke();
  }
  drawEnd() {
    document.removeEventListener("touchstart", preHandler, true);
    document.removeEventListener("touchend", preHandler, true);
    document.removeEventListener("touchmove", preHandler, true);
  }
  clear(btn) {
    this.cxt.clearRect(0, 0, width, height);
    flag = 0;
    this.guide = true;
  }
  // 新增旋转处理方法
  rotateImage(srcCanvas) {
    const orientation = window.orientation;
    if (Math.abs(orientation) !== 90) return srcCanvas;

    const tempCanvas = document.createElement("canvas");
    const ctx = tempCanvas.getContext("2d");
    tempCanvas.width = srcCanvas.height;
    tempCanvas.height = srcCanvas.width;

    ctx.translate(tempCanvas.width / 2, tempCanvas.height / 2);
    ctx.rotate(((orientation > 0 ? 1 : -1) * Math.PI) / 2);
    ctx.drawImage(srcCanvas, -srcCanvas.width / 2, -srcCanvas.height / 2);

    return tempCanvas;
  }
  //保存图片并添加背景色
  save(backgroundColor) {
    // 创建临时画布
    const tempCanvas = document.createElement("canvas");
    const tempCtx = tempCanvas.getContext("2d");
    tempCanvas.width = this.canvas.width;
    tempCanvas.height = this.canvas.height;
    tempCtx.drawImage(this.canvas, 0, 0);

    // 处理背景
    if (backgroundColor) {
      tempCtx.globalCompositeOperation = "destination-over";
      tempCtx.fillStyle = backgroundColor;
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
    }

    // 旋转处理
    const rotatedCanvas = this.rotateImage(tempCanvas);
    return rotatedCanvas.toDataURL("image/png");
  }
}
export default {
  name: "SignaturePage",
  data() {
    return {
      yfUnitCode: "", //医废项目中的上级单位编号
      yfHospitalCode: "", //医废项目中的医院编号
      yfId: "", //医废项目中的科室医废ID
      imgBase: null,
      info: {},
      list: [],
      guide: true,
      officeId: "",
      month: "",
      imgurl: "",
      imgif: false,
      officePrincipal: "",
      currentOrientation: 0
    };
  },
  mounted() {
    // draw = new Draw("canvas");
    // draw.init();
    this.initCanvas();
    //解决微信浏览器下拉露底的问题
    document.getElementById("wrapper").addEventListener(
      "touchmove",
      e => {
        e.preventDefault();
      },
      { passive: false }
    );
    window.addEventListener("orientationchange", this.handleOrientation);
  },
  beforeDestroy() {
    window.removeEventListener("orientationchange", this.handleOrientation);
  },
  methods: {
    /** 返回 */
    close(){
      this.$emit('close')
    },
    initCanvas() {
      draw = new Draw("canvas");
      draw.init();
    },
    handleOrientation() {
      this.currentOrientation = window.orientation;
      this.$nextTick(() => {
        this.initCanvas();
        this.clear();
      });
    },
    isguide() {
      this.guide = false;
    },
    /**
     * 清空画布
     */
    clear: function () {
      draw.clear();
    },
    /**
     * 保存图片
     */
    save: function () {
      let drawFlag = draw.isDrawFlag();
      if (drawFlag == 1) {
        //在画布上绘画了
        let data = draw.save("#fff");
        this.$emit("saveImg", data);
      }
      if (drawFlag == 0) {
        //没在画布上绘画
        this.$emit("saveImg", "");
      }
    },
    imgshow() {
      if (this.imgurl) {
        this.imgif = true;
      } else {
        this.imgif = false;
      }
    }
  }
};
</script>
  
  <style rel="stylesheet/stylus" lang="stylus" scoped>
  @import '~styles/varibles.styl';
  @import '~styles/mixins.styl';

  /* 新增样式 */
  #canvas {
    touch-action: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .canvas-wrapper {
    transform: rotate(0deg);
    transition: transform 0.3s;
  }

  @media screen and (orientation: landscape) {
    .canvas-wrapper {
      transform: rotate(0deg);
    }
  }

  #wrapper {
    background-color: #fff;
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    z-index: 99999;
    position: fixed;
    top: 0;

    .title {
      height: 0.55rem;
      display: flex;
      justify-content: center;
      align-items: center;

      .line {
        width: 2.5rem;
      }

      .title-content {
        position: absolute;
        padding: 0 7px;
        background: $bgColor;
        font-size: 14px;
        color: $textColor;
      }
    }

    .canvas-wrapper {
      display: flex;
      justify-content: center;
      position: relative;

      // margin-bottom: .98rem
      #canvas {
        background-color: #fff;
        // border-radius: 5px
        background: url('../assets/images/timg.png') no-repeat center center;
        background-size: 100% 100%;
      }

      .img {
        width: 90%;
        text-align: center;
        border: 1px solid #e5e5e5;
      }
    }

    .btns {
      width: 100%;
      background: #fff;
      padding: 0.22rem 0;
      text-align: center;
      display: flex;
      justify-content: space-around;

      .rewrite-btn {
        background-color: transparent;
        color: $color;
      }

      .sure-btn {
        width: calc((100% / 2) - 20px);
        height: 0.88rem;
        border-radius: 5px;
        font-size: 18px;
        font-family: unset;
        background-color: $color;
        color: #fff;
      }
    }
  }

  .check {
    width: 100%;
    background: #fff;
    margin-bottom: 0.2rem;

    .collection {
      padding-left: 0.32rem;
      height: 0.98rem;
      line-height: 0.98rem;
      border-bottom: 1px solid #eff0f4;
    }

    .signature-quantity {
      padding-left: 0.32rem;
      padding-top: 0.35rem;
      display: flex;
    }
  }

  .count {
    margin-right: 0.5rem;
  }

  .signature-divst {
    width: 2.5rem;
    position: relative;
    top: -0.15 rem;
    // left: 3rem
    padding: 0.22rem 0.13rem 0.2rem 0.3rem;
    border-radius: 4px;
    font-size: 0.26rem;
    color: #5B5B74;
    background: #f3f3f8;
    margin-bottom: 0.1rem;

    .wastelist {
      display: block;
      width: 2rem;
      line-height: 0.4rem;
    }

    .bor {
      position: absolute;
      top: 0.16rem;
      left: -0.19rem;
    }
  }

  .contentColor {
    color: $contentColor;
    font-size: 0.28rem;
  }

  .titleColor {
    color: #353535;
    font-size: 0.3rem;
    margin-right: 0.3rem;
  }

  .signature {
    background: #fff;
    // padding-bottom:.5rem
    z-index: 999999;

    .signature-title {
      display: flex;
      justify-content: space-between;
      line-height: 0.98rem;
      font-size: 0.3rem;
      padding-left: 0.32rem;
      padding-right: 0.36rem;

      .clear {
        color: $btnColor;

        .clear-img {
          width: 0.28rem;
          margin-top: -0.04rem;
        }
      }
    }
  }

  .guide {
    width: 374px;
    height: 336px;
    position: absolute;
    top: 0;
    left: 0.32rem;
    text-align: center;
    line-height: 336px;
    z-index: 100;
    font-size: 1rem;
    color: #ebebef;
  }

  .canvas_bgp {
    width: calc(100% - 40px);
    height: 100%;
    position: absolute;
    top: 0;
    left: 20px;
  }
</style>
  