<template>
  <div id="education" class="weui-popup__container popup-bottom">
    <div class="weui-popup__overlay" @touchmove.prevent></div>
    <div class="weui-popup__modal list"><!-- ref="list"-->
      <div class="weui-cells weui-cells_radio">
        <div class="title">请选择{{dataInfo.title}}</div>
          <label
              v-for="(item,index) of dataInfo.data"
              class="weui-cell weui-check__label close-popup"
              :for="index"
              :key="index"
          >
            <div class="weui-cell__bd" v-if="nameAndCode">
              <p>{{item.name}}</p>
            </div>
            <div class="weui-cell__bd" v-else>
              <p>{{item}}</p>
            </div>
            <div class="weui-cell__ft">
              <input
                  type="radio"
                  name="radio1"
                  class="weui-check"
                  :id="index"
                  :value="item"
                  v-model="emitData.resData"
                  @click.once="getVal"
              >
              <span class="weui-icon-checked"></span>
            </div>
          </label>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import BScroll from 'better-scroll'
  export default {
    name: "PopBottom",
    props:["dataInfo"],
    data () {
      return {
        emitData:{
          resData:'',
          flag:''
        },
        nameAndCode:false
      }
    },
    methods:{
      getVal () {
        setTimeout(() => {
          this.$emit('setVal',this.emitData)
        },20)
      }
    },
    updated () {
      this.emitData.flag = this.dataInfo.flag
      this.nameAndCode = this.dataInfo.nameAndCode
    },
    mounted () {
      // this.scroll = new BScroll(this.$refs.list,{click:true})
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
/*#education >>> .close-popup*/
  /*padding 4px 14px*/
  /*font-size 16px*/
  .list
    overflow: scroll
    max-height: 6.67rem
    background-color: #fff
    .title
      color: #5c5c5c
      background-color: #ebebeb
      font-size: .3rem
      height: .6rem
      line-height: .6rem
      padding-left: 5px
</style>
