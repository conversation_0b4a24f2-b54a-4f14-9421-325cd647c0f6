<template>
  <cube-slide
    ref="slide"
    :data="swiperData"
    class="home-swiper"
    :initialIndex="initialIndex"
    :loop="loop"
    :showDots="showDots"
    :autoPlay="autoPlay"
    :interval="interval"
    :direction="direction"
  >
    <cube-slide-item v-for="(item, index) in swiperData" :key="index">
      <img :src="item.image">
    </cube-slide-item>
  </cube-slide>
</template>
<script type="text/ecmascript-6">
export default {
  name: "serveSilde",
  props: {
    swiperData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    initialIndex: {
      type: Number,
      default: 0
    },
    loop: {
      type: Boolean,
      default: true
    },
    showDots: {
      type: Boolean,
      default: true
    },
    autoPlay: {
      type: Boolean,
      default: true
    },
    interval: {
      type: Number,
      default: 4000
    },
    direction: {
      type: String,
      default: "horizontal"
    }
  }
};
</script>
<style lang="stylus" type="stylesheet/stylus">
</style>