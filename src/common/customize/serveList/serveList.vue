<template>
  <div class="serve-list">
    <div class="serve-title" v-if="title">
      <div class="title-icon title-icon-left">
        <span :style="bgStyle"></span>
      </div>
      <div class="title-text" :style="textStyle">
        <span>{{title}}</span>
      </div>
      <div class="title-icon title-icon-right">
        <span :style="bgStyle"></span>
      </div>
    </div>
    <div class="list-content" v-for="(item1,index1) in serveList" :key="index1">
      <div class="type-title">{{item1.title}}</div>
      <div class="list">
        <div class="list-item" v-for="(item,index) in item1.list" :key="index"  v-if="!item.hidden">
          <div class="item-text" v-if="item.mobile" @click="changeLink(item)">
            <a class="aTag" :href="'tel:'+ item.mobile">
              <img class="text-box-img" :src="item.icon" alt />
              <span class="text-box-label">{{item.text}}</span>
            </a>
          </div>
          <div class="item-text" v-else @click="changeLink(item)">
            <img class="text-box-img" :src="item.icon" alt />
            <span class="text-box-label">{{item.text}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script type=text/ecmascript-6>
import { mapState } from "vuex";
export default {
  props: {
    serveList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    title: {
      type: String,
      default: ""
    },
    iconBg: {
      type: String,
      default: ""
    },
    textStyle: {
      type: Object,
      default: () => {
        return {};
      }
    },
    bgStyle: {
      type: Object,
      default: () => {
        return {};
      }
    },
    hospitalCode: { // //如果有值则为天坛后勤公众号
      type: String,
      default: () => {
        return "";
      }
    }
  },
  computed: {
    ...mapState(["isLogin"])
  },
  methods: {
    changeLink(option) {
      console.log(option, process.env.LINEM_WX);
      let link = option.path;
      if (link === '/Linen') {
        location.href = process.env.LINEM_WX
        return
      } else if (link === '/Assets') {
        return
      }
      if (this.hospitalCode) {
        //  //如果有值则为天坛后勤公众号
        //正式环境只需要判断是否有值就行,这里以ZXYSHJ代替测试天坛医院
        let code = this.hospitalCode;
        if (link.includes("complain")) {
          location.href = `${link}&hospitalCode=${code}`;
        } else {
          location.href = `${link}?hospitalCode=${code}`;
        }
      } else {
        if (option.source == "custom") {//自定义工单
          // this.$router.push({
          //   path:link,
          //   query:{
          //     isItem:option.isItem,
          //     workTypeName:option.workTypeName,
          //     workTypeCode:option.workTypeCode,
          //     type:option.type,
          //   }
          // })

            link = option.template == '3' ?   "/transport": option.path  // 自定义工单运送类跳转运送页面, 其他跳转自定义工单
            location.href = `${link}?isItem=${option.isItem}&workTypeName=${option.workTypeName}&workTypeCode=${option.workTypeCode}&type=${option.type}`

        } else {
          if(option.workTypeCode == '3'){  //运送类需要传参数
            location.href = `${link}?workTypeName=${option.text}&workTypeCode=${option.workTypeCode}`
          } else {
            location.href = link;
          }
        }
      }
      //        location.href = link
    }
  }
};
</script>
<style rel="stylesheet/stylus" lang="stylus" type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.list-content {
  flex-direction: column;
  border-radius: 5px;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;

  .list {
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid #E5E5E5;
    padding: 0 0.14rem;

    .list-item {
      width: 1.78rem;

      .aTag {
        align-items: center;
        display: flex;
        flex-direction: column;
        color: #353535;
      }
    }
  }
}

.serve-title {
  height: 30px;
  display: flex;
}

.title-icon {
  display: flex;
  align-items: center;
  flex: 2;

  span {
    bg-image: '~images/right';
    background-size: 100% 100%;
    display: inline-block;
    width: 0.24rem;
    height: 0.28rem;
  }
}

.title-text {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.3rem;
  font-family: NotoSansHans-Medium;
  font-weight: 500;

  .title-img {
    width: 0.72rem;
  }
}

.item-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.3rem;
  font-family: NotoSansHans-Medium;
  font-weight: 500;
  color: rgba(53, 53, 53, 1);
  background-color: #fff;
  border-radius: 0.08rem;
  flex-direction: column;

  span {
    min-height: 12px;
  }

  .text-box-label {
    padding: 0.23rem 0 0.47rem 0;
    font-size: 0.28rem;
    display: inline-block;
    width: 90px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    text-align :center
  }

  .text-box-img {
    width: 0.72rem;
  }
}

.title-icon.title-icon-left {
  justify-content: flex-end;
}

.type-title {
  position: relative;
  font-weight: 500;
  font-size: 0.3rem;
  font-family: 'Noto Sans S Chinese';
  font-weight: bold;
  color: rgba(53, 53, 53, 1);
  box-sizing: border-box;
  padding: 0.36rem 0.32rem;
}
</style>
