<template>
  <div class="order-list">
    <div class="order-title" v-if="title">
      <div class="title-text" :style="textStyle">
        <i :style="bgStyle"></i>
        <span>{{title}}</span>
        <i :style="bgStyle"></i>
      </div>
    </div>
    <div class="order-content">
        <div class="order-content-card" v-for="(item,index) in orderList" :key="index">
            <div class="card-title">
                <i></i>
                <span>{{item.title}}</span>
                <b v-if="item.isback" class="iconfont arrow icon-arrow-right" style="color:rgba(229,229,229,1);float: right;padding-right: .2rem;"></b>
            </div>
            <div class="card-task">
                <div class="task task-untreated" v-for="(res,index) in item.taskNum" :key="index">
                    <span class="task-number">{{res.taskCount <= 99?res.taskCount:'99+'}}</span>
                    <span class="task-text">{{res.taskName}}</span>
                </div>
               
            </div>
        </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
    export default{
        name:"orderList",
        props:{
            orderList:{
                type:Array,
                default : ()=>{
                    return []
                }
            },
            title:{
                type:String,
                default:""
            },
            textStyle:{
                type:Object,
                default:()=>{
                    return {}
                }
            },
            bgStyle:{
                type:Object,
                default:()=>{
                    return {}
                }
            }
        }
    }
</script>
<style lang="stylus" type="stylesheet/stylus">
   @import "~styles/varibles.styl"
   @import "~styles/mixins.styl"
.order-list
    margin-bottom 18px
    .order-title
        height 30px
        display flex
        .title-text
            flex 1
            display flex
            justify-content center
            align-items center
            font-size:15px;
            font-family:NotoSansHans-Medium;
            font-weight:500;
            i 
               bg-image("~images/right")
               background-size: 100% 100%;
               display inline-block
               width 12px
               height 14px
            span 
               padding: 10px
    .order-content
        background #ffffff
        margin-bottom 18px
        .card-title
            height:40px;
            line-height 40px;
            font-size:16px;
            font-family:NotoSansHans-Regular;
            font-weight:400;
            color:rgba(53,53,53,1);
            span 
                padding-left 0.4rem
            i 
                display inline-block
                width 4px
                height 16px
                position: relative;
                left: 0.3rem;
                top:3px
                background $color
        .card-task
            height 74px
            border-bottom-1px()
            .task
                display inline-block
                width 25%
                .task-number
                    font-size:20px;
                    font-family:NotoSansHans-Medium;
                    font-weight:500;
                    color:rgba(53,53,53,1);
                    display block
                    text-align center
                    padding-top .2rem
                .task-text
                    font-size:15px;
                    font-family:PingFangSC-Regular;
                    font-weight:400;
                    color:rgba(153,153,153,1);
                    display block
                    text-align center
                    padding-top .35rem
.title-icon.title-icon-left
    justify-content: flex-end
</style>