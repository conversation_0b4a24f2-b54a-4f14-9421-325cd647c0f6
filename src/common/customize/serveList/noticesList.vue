<template>
    <div class="notices-list">
        <!-- <div class="notices-content">
            <div class="notices-logo">
                <img src="~images/tip.png" alt="">
            </div>
            <div class="notices-swiper">
                <div class="swiper-content">
                    <div class="swiper-item" v-for="(item,index) in noticesTestList" :key="index">
                        <p><span class="list-style">·</span> <span class="list-text">{{item.text}}</span></p>
                    </div>
                </div>
            </div>
            <div class="notices-more">
                <span class="iconfont arrow">&#xe646;</span>
            </div>
        </div> -->
    <div class="notices-content-2">
      <div class="notices-content-header">
        <div class="notices-content-headerimg"></div>
        <div class="notices-content-headertext" @click="openToList">
          <span>全部</span>
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <div class="notices-swiper">
        <div class="swiper-content">
          <div
            class="swiper-item"
            v-for="(item,index) in noticesTestList"
            :key="index"
            @click="openToNoticeDetail(item)"
          >
            <p>
              <span class="list-style"></span>
              <span class="list-text">{{item.title}}</span>
            </p>
            <span class=" time " v-text="moment(item.createTime)"></span>
          </div>
        </div>
      </div>
    </div>
    </div>
</template>
<script type="text/ecmascript-6">
let moment = require("moment");



export default {
  props: {
    noticesTestList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  methods: {
    openToNoticeDetail(option) {
      this.$router.push({
        path: "/noticeDetail",
        query: {
          id: option.id
        }
      });
    },
    moment(time){
          time=moment(time).format('YYYY-MM-DD')
          return time
    },
    openToList() {
      this.$router.push({
        path: "/noticeDetailList",
        query: {
          // id: option.id
        }
      });
    },
  },
  mounted() {
    setInterval(() => {
      let firstEle = this.noticesTestList[0];
      this.noticesTestList.shift();
      this.noticesTestList.push(firstEle);
    }, 3000);
  }
};
</script>
<style lang="stylus" type="stylesheet/stylus">
.notices-list
    background: #ffffff;
    margin-top: 5px;
    .notices-content
        height 100%
        display flex
        .notices-logo
            display flex
            justify-content center
            align-items center 
            flex 1
            img 
                width 62%
        .notices-swiper
            display flex
            justify-content center
            align-items center
            flex 4
            .swiper-content
                height: 40px;
                margin: 10px 5px;
                overflow: hidden;
                .swiper-item
                    padding: 4px 5px;
                    p
                        width 240px
                        font-size:13px;
                        font-family:PingFang-SC-Regular;
                        font-weight:400;
                        color:rgba(102,102,102,1);
                        white-space: nowrap;
                        .list-style
                            color rgba(255,62,89,1);
                        .list-text 
                            display: inline-block;
                            width: 100%;
                            font-family:PingFang-SC-Regular;
                            font-weight:400;
                            overflow: hidden;
                            text-overflow:ellipsis;
                            white-space: nowrap;
        .notices-more
            flex 1
            display flex
            justify-content center
            align-items center
            span 
                font-size: 21px;
                color: #cecece;
.notices-content-header{
  display :flex;
  justify-content :space-between
}
.notices-content-headerimg {
  // background-image: url('../../../assets/images/carousel/news.png');
  background-size: 100%;
  background-repeat: no-repeat;
  height: 30px;
  width :80%;
}
.notices-content-headertext{
  padding: 5px 0px;
  padding-right :16px;
  color :#353535;
  font-size :13px;
  font-weight:400;
}

.notices-content-2 {
  min-height: 80px;

  .notices-swiper {
    .swiper-content {
      height: 45px;
      margin: 5px;
      overflow: hidden;

      .swiper-item {
        position: relative;
        .time{
          display :inline-block;
          text-align :left;
          position: absolute;
          top: 25%;
          // right: 16px;
          left:80%;
          color:#A6A6B1
          font-size:12px;
        }
        .iconfont {
          position: absolute;
          top: 25%;
          right: 2px;
        }

        p {
          height: 22.5px;
          line-height: 22.5px;
          padding-left: 16px;
          position: relative;
          font-size: 14px;
          font-weight: 300;
          color: rgba(136, 136, 136, 1);
          white-space: nowrap;

          .list-style {
            position: absolute;
            top: 40%;
            display: inline-block;
            width: 5px;
            height: 5px;
            background: rgba(0, 86, 142, 1);
            border-radius: 50%;
          }

          .list-text {
            display: inline-block;
            width: 73%;
            font-family: PingFang-SC-Regular;
            font-weight: 400;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-left: 15px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
        }
      }
    }
  }
}
</style>