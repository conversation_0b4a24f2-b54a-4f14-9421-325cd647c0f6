<template>
  <div class="home-header">
    <div class="home-header-title">
      <!--<div class="home-header-scan">
        <i class="iconfont arrow icon-saomiao" @click="scan"></i>
      </div>-->
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
    import logo from './logo.png'
    export default{
        name:"serveHeader",
        props:{
            logo:{
                type:String,
                default:logo
            }
        },
        methods: {
            scan(){
                //扫描二维码
                this.$router.push("TaskType")
            }
        },
    }
</script>
<style rel="stylesheet/stylus" lang="stylus" type="stylesheet/stylus">
@import "~styles/varibles.styl"
@import "~styles/mixins.styl"
.home-header
  height: 3.8rem
  bg-image('~images/bg');
  background-size: 100% 100%;

  .home-header-title
    display: flex;
    height: 48px;
    padding-top: 20px;

    .home-header-scan
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      i
        color: #fff;
        font-size: 30px;

</style>
