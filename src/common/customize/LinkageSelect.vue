<template>
  <div class="area-wrapper" v-if="show">
    <div class="area-content">
      <div class="title">
        <p>请选择服务区域</p>
        <span class="iconfont" @click="closePop">&#xe61f;</span>
      </div>
      <div class="tab-title">
        <div
         :class="{tabItemActive:curArea === 'region',disClick:isClick}"
         @click="switchOper('region')"
        >区域</div>
        <div
         :class="{tabItemActive:curArea === 'building',disClick:isClick1}"
         @click="switchOper('building')"
        >楼宇</div>
        <div
         :class="{tabItemActive:curArea === 'floor',disClick:isClick2}"
         @click="switchOper('floor')"
        >楼层</div>
      </div>

      <div class="select-area">
        <div class="weui-cells_radio region" v-if="curArea === 'region'">
          <label
           class="weui-cell weui-check__label"
           :for="index"
           v-for="(item,index) of areaData"
           :key="item.id"
           @click.prevent="switchOper('building',item.gridName,item.id)"
          >
            <div class="weui-cell__bd">
              <p>{{item.gridName}}</p>
            </div>
            <div class="weui-cell__ft">
              <input type="radio" class="weui-check" name="radio1" :id="index">
              <span class="weui-icon-checked"></span>
            </div>
          </label>
        </div>
        <div class="weui-cells_radio building" v-if="curArea === 'building'">
          <label
              class="weui-cell weui-check__label"
              :for="index"
              v-for="(item,index) of buildingData"
              :key="item.id"
              @click.prevent="switchOper('floor',item.gridName,item.id)"
          >
            <div class="weui-cell__bd">
              <p>{{item.gridName}}</p>
            </div>
            <div class="weui-cell__ft">
              <input type="radio" class="weui-check" name="radio1" :id="index">
              <span class="weui-icon-checked"></span>
            </div>
          </label>
        </div>
        <div class="weui-cells_radio floor" v-if="curArea === 'floor'">
          <label
              class="weui-cell weui-check__label"
              for="noSelect"
              @click.stop="submitAreaInfo()"
          >
            <div class="weui-cell__bd">
              <p>暂不选择</p>
            </div>
            <div class="weui-cell__ft">
              <input type="radio" class="weui-check" name="radio1" id="noSelect">
              <span class="weui-icon-checked"></span>
            </div>
          </label>
          <label
              class="weui-cell weui-check__label"
              :for="index"
              v-for="(item,index) of floorData"
              :key="item.id"
              @click="submitAreaInfo(item.gridName,item.id)"
          >
            <div class="weui-cell__bd">
              <p>{{item.gridName}}</p>
            </div>
            <div class="weui-cell__ft">
              <input type="radio" class="weui-check" name="radio1" :id="index">
              <span class="weui-icon-checked"></span>
            </div>
          </label>
        </div>
      </div>

    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import {mapState} from 'vuex'
  export default {
    name: "LinkageSelectArea",
    computed: {
      ...mapState(['loginInfo','serviceAreaTreeData'])
    },
    data () {
      return {
        show:false,
        curArea: 'region',//当前选项
        isClick: true,
        isClick1: true,
        isClick2:true,
        areaData: [],
        buildingData: [],
        floorData: [],
        resNameData:[],
        resCodeData:[],
        levelDataList: []
      }
    },
    methods: {
      /**
       * 区域联动请求函数
       */
      getLinkageRes () {
        // $.showLoading()
        // this.$api.cascadingQueryHospitalGridInfo({
        //   unitCode: this.loginInfo.userOffice[0].unitCode,
        //   hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
        //   gridId:id,
        // }).then(this.showVal)
        const treeData = this.serviceAreaTreeData
        console.log('@',treeData)
        this.levelDataList.push(this.getLevelData(2, treeData), this.getLevelData(3, treeData), this.getLevelData(4, treeData), this.getLevelData(5, treeData))
        this.setShowLocationData()
      },
      setShowLocationData(id) {
        if (this.curArea == "region") {
          this.areaData = this.levelDataList[0];
        }
        if (this.curArea == "building") {
          this.buildingData = this.levelDataList[1].filter(e=> e.parentId == id);
          if (this.buildingData.length === 0) {
            this.nullstyle = true;
          } else {
            this.nullstyle = false;
          }
        }
        if (this.curArea == "floor") {
          this.floorData = this.levelDataList[2].filter(e=> e.parentId == id);
        }
      },
      getLevelData(level, data) {
        const filterData = data.filter((e) => e.ssmType === level)
        if (filterData.length) {
          return filterData
        } else {
          return []
        }
      },
      /**
       * 返回数据展示
       */
      showVal (res, id) {
        // $.hideLoading()
        if (this.curArea == 'region'){
          this.areaData = res
        }
        if (this.curArea == 'building'){
          this.buildingData = res
        }
        if (this.curArea == 'floor'){
          this.floorData = res
        }
      },
      /**
       * 点击每一项后进入下一组数据
       * @param par
       */
      switchOper (style,name,code) {
        this.curArea = style
        if(code){
          // this.getLinkageRes(code)
          this.setShowLocationData(code)
        }
        if (style == 'building'){
          this.isClick = false
          this.isClick1 = false
          if(name){
            this.resNameData[0] = name
            this.resCodeData[0] = code
          }
        }
        if (style == 'floor'){
          this.isClick1 = false
          this.isClick2 = false
          this.resNameData[1] = name
          this.resCodeData[1] = code
        }
      },
      /**
       * 点击楼层关闭弹窗并携带回数据
       * @param name 楼层名称
       * @param code 楼层code
       */
      submitAreaInfo (name,code) {
        if(name == undefined && code == undefined){
          this.resNameData[2] = ''
          this.resCodeData[2] = ''
          this.$parent.isExist = false
        }else{
          this.resNameData[2] = name
          this.resCodeData[2] = code
          this.$parent.isExist = true
        }
        this.show = false
        this.body.setAttribute("style","");
        this.$emit('getAreaVal',{name:this.resNameData,code:this.resCodeData})
      },
      /**
       * 点击x关闭
       */
      closePop () {
        this.show = false
        this.body.setAttribute("style","");
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  .area-wrapper
    width: 100%
    height: 100%
    background: rgba(0, 0, 0, .5)
    position: fixed
    top: 0
    bottom: 0
    left: 0
    right: 0
    z-index: 2
    .area-content
      display: block
      height: 7rem
      position: absolute
      bottom: 0
      background: #fff
      width: 100%
      .title
        position: relative
        padding: .3rem
        p
          text-align: center
        span
          position: absolute
          top: .3rem
          right: .3rem
          color: #e5e5e5
      .tab-title
        display: flex
        justify-content: space-around
        font-size: .32rem
        margin-bottom: .3rem
        div
          padding: .12rem
        .disClick
          pointer-events: none
        .tabItemActive
          border-bottom: 2px solid $color
      .select-area
        width: 100%
        height: 5.5rem
        overflow: scroll
        .region
        .building
        .floor
          width: 100%
          label
            line-height: .86rem
            color: #888
            padding: 0 .32rem
            font-size: .3rem

  @keyframes show {
    0% {
      opacity: 0;
      left: 100%;
    }
    100% {
      opacity: 1;
      left: 0;
    }
  }

  @keyframes hide {
    0% {
      opacity: 1;
      left: 0;
    }
    100% {
      opacity: 0;
      left: -100%;
    }
  }

  .show-enter-active {
    animation: show 1s;
  }

  .show-leave-active {
    animation: hide 1s;
  }

  .show-enter, .show-leave-to {
    opacity: 0;
  }
</style>
