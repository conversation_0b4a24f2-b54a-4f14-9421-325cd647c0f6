<template>
  <div class="wrap" @click="handleCancelPop">
    <div class="content" @click.stop>
      <div class="title border-bottom item">已选耗材（共{{selectedCount}}件）</div>
      <div class="lists">
        <div class="list" ref="lists" v-for="(item,index) of selectedItem">
          <div class="name">{{item.depThreeTypeName}}</div>
          <div class="count">
            <span class="iconfont less" @click="handleLessNumClick(index,item.depThreeTypeName)">&#xe693;</span>
            <span class="num">{{item.num}}</span>
            <span class="iconfont add" @click="handleAddNumClick(index)">&#xe691;</span>
          </div>
        </div>
      </div>
    </div>
    <div class="line"></div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "PollupBox",
    props: {
      selectedItem: Array,
      selectedNum: Number
    },
    data () {
      return {
        selectedCount: 0
      }
    },
    methods: {
      /**
       *点击减少耗材-
       * @param index
       */
      handleLessNumClick (index, name) {
        let num = this.selectedItem[index].num
        let selectedNum = this.selectedCount
        if (num > 1) {
          this.selectedItem[index].num = --num
        } else {
          let lists = this.selectedItem
          this.$emit('cancelStyle', name)
          lists.splice(index, 1)
        }
        selectedNum--
        this.selectedCount = selectedNum
        this.$emit('handleLessCount')
      },
      /**
       * 点击添加耗材+
       * @param index
       */
      handleAddNumClick (index) {
        let num = this.selectedItem[index].num
        let selectedNum = this.selectedCount
        this.selectedItem[index].num = ++num
        selectedNum++
        this.selectedCount = selectedNum
        this.$emit('handleAddCount')
      },
      /**
       * 点击空白处，关闭弹窗
       * @param event
       */
      handleCancelPop (event) {
        event.stopPropagation()
        this.$emit('closePop')
      }
    },
    mounted () {
      this.selectedCount = this.selectedNum
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .wrap
    wrapper()
    bottom: 58px
    overflow: hidden
    background-color: rgba(0, 0, 0, .5)
    .content
      max-height: 4.75rem
      background-color: #fff
      position: absolute
      bottom: 10px
      left: 0
      right: 0
      .title
        itemBaseStyle()
        font-size: .32rem
      .lists
        max-height: 3.77rem
        overflow: auto
        .list
          display: flex
          justify-content: space-between
          font-size: .30rem
          itemBaseStyle()
          .count
            .less
              color: #d1d1d1
            .add
              color: $btnColor
            .num
              display: inline-block
              text-align: center
              width: 50px
    .line
      width:100%
      height:10px
      background:$bgColor
      position: absolute
      bottom:0
</style>