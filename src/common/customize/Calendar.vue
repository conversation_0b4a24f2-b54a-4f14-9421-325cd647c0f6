<template>
  <div id="calendarWrapper">
    <div class="bjui-pageContent">
      <div class="form-group d_t_dater">
        <label class="col-sm-3 control-label"></label>
        <div class="col-sm-12">
          <div class="input-group">
            <button type="button" class="btn btn-default" id="daterange-btn">
                    <span></span>
              <i class="icon iconfont icon-danxian-youjiantou-copy"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import moment from "moment"
  import daterangepicker from "daterangepicker"
  export default {
    name: "CalendarPeriod",
    methods:{
      init (start, end,label) {
        //label:通过它来知道用户选择的是什么，传给后台进行相应的展示
        //      console.log(label)
        if(label=='全部'){
          $('#daterange-btn span').html('全部');
        }else if(label=='今天'){
          $('#daterange-btn span').html(end.format('YYYY/MM/DD'));
        }else if(label=='明天'){
          $('#daterange-btn span').html(start.format('YYYY/MM/DD'));
        }else if(label=='未来七天'){
          $('#daterange-btn span').html(start.format('YYYY/MM/DD')+'-'+end.format('YYYY/MM/DD'));
        }else if(label=='未来30天'){
          $('#daterange-btn span').html(start.format('YYYY/MM/DD')+'-'+end.format('YYYY/MM/DD'));
        }else if(label=='未来60天'){
          $('#daterange-btn span').html(start.format('YYYY/MM/DD')+'-'+end.format('YYYY/MM/DD'));
        }

      }
    },
    mounted () {
      $('.ranges_1 ul').remove();
      $('#daterange-btn').daterangepicker({
        startDate: moment(),
        endDate: moment()
      })
      this.init()
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>

</style>