<template>
  <div class="matters-wrapper">
    <Header title="选择房间" @backFun="goback"></Header>
    <div class="ipt-header header-two">
      <div class="search-ipt">
        <span class="iconfont">&#xe60a;</span>
        <input
          id="search"
          type="text"
          maxlength="50"
          placeholder="请输入关键字"
          v-model="value"
        />
        <span class="line"></span>
        <span class="cancle" @click="clearContent">取消</span>
      </div>
    </div>

    <div class="header">
      <div>
        <div id="gosearch">
          <div class="ipt-area">
            <span class="value" v-if="searchBox">
              <span class="iconfont">&#xe60a;</span>
              <span>搜索</span>
            </span>
            <span class="value1" ref="value1">{{ value }}</span>
          </div>
        </div>
      </div>
    </div>
    <div id="letter"></div>
    <div class="sort_box-two"></div>

    <div class="matters-content">
      <div class="weui-cells_radio matters-lists sort_box" id="matters-lists">
        <label
          class="weui-cell weui-check__label sort_list"
          :for="index"
          :key="index"
          v-for="(item, index) of list"
        >
          <div class="weui-cell__bd">
            <p class="num_name">{{ item.ssmName }}</p>
          </div>
          <div class="weui-cell__ft">
            <input
              type="radio"
              class="weui-check"
              name="radio1"
              :id="index"
              :value="item.id"
              v-model="checkedValue"
            />
            <span class="weui-icon-checked"></span>
            <!--<div class="iconfont" v-if="item.id == gridNameId">&#xeaf1;</div>-->
          </div>
        </label>
        <div class="initials">
          <ul></ul>
        </div>
      </div>
    </div>
    <div class="button-wrapper relevance-box">
      <div class="btn" @click="submitVal">确定</div>
    </div>

    <div class="show-default-img" v-if="noData">
      <img src="~images/noDataDefault/search-empty.png" alt="" />
    </div>
  </div>
</template>

<script>
import global from "@/utils/Global";
import SearchInput from "./components/List";
import SearchList from "./components/Search";
import SearchAlphabet from "./components/Alphabet";
import { mapMutations } from "vuex";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "SearchCompontent",
  mixins: [fontSizeMixin],
  components: {
    SearchInput,
    SearchList,
    SearchAlphabet
  },
  data() {
    return {
      list: [],
      locationInfo: {},
      transLocationData: {},
      letter: "",
      lastUrl: "",
      style: "",
      searchBox: true,
      value: "",
      checkedValue: "",
      searchId: ""
      //        noData: false
    };
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    /**
     * 接收字母列表点击的内容A,B,C...
     *  @param letter
     */
    handelChangeLetter(letter) {
      this.letter = letter;
    },
    ...mapMutations([
      "changeLocation",
      "transLocationStart",
      "transLocationEnd",
      "changeLocationEnd"
    ]),
    //以下为更改后新加的方法
    /**
     * 监控搜索栏隐藏
     */
    watchSearchBoxHidden() {
      this.searchBox = false;
      this.relance = false;
    },
    /**
     * 回显到搜索栏
     */
    setSearchValue(resName, resId) {
      // this.value = resName
      // this.relance = true
      this.checkedValue = resName;
      this.searchId = resId;
      // 直接跳转到页面
      this.submitVal();
    },
    /**
     * 单项选择清空
     */
    clearRadio() {
      this.checkedValue = "";
      this.n = -1;
    },
    /**
     * 点击取消
     */
    clearContent() {
      this.searchBox = !this.searchBox;
      this.value = "";
    },
    /**
     * 点击确定
     */
    submitVal() {
      if (this.value == "") {
        //点击选择列表中的选项
        this.filterResulter(this.checkedValue);
      } else {
        //通过搜索中进行选择的
        this.filterResulter(this.searchId);
      }
      if (this.style == 3) {
        this.transLocationStart(this.transLocationData); //运输(起点3)
      } else if (this.style == 4) {
        this.transLocationEnd(this.transLocationData); //运输(终点4)
      } else {
        if (this.$route.query.isEnd) {
          this.changeLocationEnd(this.locationInfo);
        } else {
          this.changeLocation(this.locationInfo);
        }
      }
      if (this.lastUrl.includes("isExist")) {
        this.lastUrl = this.lastUrl.split("?")[0];
      }
      sessionStorage.setItem("url", this.lastUrl);
      this.$router.replace({
        path: this.lastUrl,
        query: {
          cache: true
        }
      });
    },
    /**
     * 根据所选项的名称筛选出其他相关信息
     */
    filterResulter(resultId) {
      for (let i = 0; i < this.list.length; i++) {
        if (this.list[i].id == $.trim(resultId)) {
          if (this.style == 3) {
            this.transLocationData.startPlaceCode = this.list[i].id;
            this.transLocationData.startPlaceName = this.list[i].ssmName;
            this.transLocationData.style = this.style;
          } else if (this.style == 4) {
            this.transLocationData.endPlaceCode = this.list[i].id;
            this.transLocationData.endPlaceName = this.list[i].ssmName;
            this.transLocationData.style = this.style;
          } else {
            this.locationInfo.localtionPlaceCode = this.list[i].id;
            this.locationInfo.localtionPlaceName = this.list[i].ssmName;
          }
        }
      }
    }
  },
  computed: {
    noData() {
      let flag = false;
      if (this.list.length == 0) {
        flag = true;
      } else {
        flag = false;
      }
      return flag;
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    global.pyList(
      this.watchSearchBoxHidden,
      this.setSearchValue,
      this.clearRadio
    );
    if (this.$route.params.list) {
      this.list = this.$route.params.list;
      this.style = this.$route.params.style;
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.lastUrl = from.fullPath;
    });
  }
};
</script>
<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/varibles.styl"
@import "~styles/mixins.styl"
.matters-wrapper >>> .sort_letter
  padding-left: 5px
.matters-wrapper >>> .sort_box
  padding-top: 1.2rem
  padding-bottom: 1.3rem
.matters-wrapper >>> .sort_letter
  background-color: $bgColor
.matters-wrapper >>> .weui-cells
  background-color: transparent
.matters-wrapper >>> .sort_box-two
 .sort_list-two
   padding: 10px 0.4rem
   background-color: #fff
.sort_list
  padding-right: 45px
  // padding-left: 16px
  background-color: #fff
  font-size: calc(16px * var(--font-scale))!important;
  // margin-left: -15px
.sort_letter
  background-color: $bgColor
.matters-wrapper
  min-height: 100%
  background-color: $bgColor
  .ipt-header
    height: .78rem
    line-height: .78rem
    padding: .2rem .32rem
    background-color: #fff
    .search-ipt
      position: relative
      .iconfont
        position: absolute
        left: .28rem
        top: .05rem
        color: $textColor
      #search
        background-color: $bgColor
        padding-left: .8rem
        box-sizing: border-box
        height: .78rem
        display: inline-block
        width: 100%
        border-radius: 5px
      .line
        width: 1px
        height: .3rem
        background-color: #D3D3D5
        display: inline-block
        position: absolute
        right: 1.1rem
        top: .24rem
      .cancle
        position: absolute
        right: 5px
        top: 0
        width: 1rem
        text-align: center
  .header
    #gosearch
      background-color: #fff
      padding: .2rem
      position: fixed
      width: 100%
      z-index: 3
      box-sizing: border-box
      .ipt-area
        height: .78rem
        background-color: $bgColor
        border-radius: 5px
        display: flex !important
        align-items: center
        justify-content: center
        color: $contentColor
        .iconfont
          padding-right: 7px
  .matters-content
    position: relative
    .initials
      position: fixed
      right: 0
      bottom: 0rem
      z-index: 10
      width: 25px
      display: flex !important
      justify-content: center
      align-items: center
    .matters-title
      height: .56rem
      line-height: .56rem
      text-align: center
      position: relative
      overflow: hidden
      .line
        width: 2.5rem
        height: 1px
        margin: 0 auto
        margin-top: .28rem
        background-color: $textColor
      .title
        position: absolute
        left: 50%
        width: 70px
        background: $bgColor
        margin-left: -35px
        top: 0
    .matters-lists
      margin-top: 0
      line-height: 1.47058824
      font-size: 17px
      overflow: hidden
      position: relative
      .list
        font-size: .32rem
        line-height: .88rem
        padding-left: .2rem
        background: #fff
        color: $darkTextColor
  .button-wrapper
    width: 100%
    height: 1.3rem
    padding: .22rem .32rem
    box-sizing: border-box
    background-color: #fff
    position: fixed
    bottom: 0
    border-top: 1px solid $bgColor
    z-index: 2
    .btn
      background-color: $btnColor
      color: #fff
      border-radius: 3px
      text-align: center
      height: .88rem
      line-height: .88rem
      font-size: .36rem
  .show-default-img
    position: fixed
    top: 100px
    bottom: 100px
    left: 0
    right: 0
    text-align: center
    display: flex
    justify-content: center
    align-items: center
    img
      width: 50%
</style>
