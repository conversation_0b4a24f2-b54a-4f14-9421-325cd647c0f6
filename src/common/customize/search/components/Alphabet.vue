<template>
  <ul class="list">
    <!--刚开始，没定义Letters之前-->
    <!--<li
        class="item"
        v-for="(item,key) of list"
        :key="key"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @click="handleLetterClick"
    >{{key}}
    </li>-->
    <li
        class="item"
        v-for="(item) of letters"
        :key="item"
        :ref="item"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @click="handleLetterClick"
    >{{item}}</li>
  </ul>
</template>

<script type=text/ecmascript-6>
  export default {
    name: 'SearchAlphabet',
    props: ['list'],
    computed: {
      letters(){
        const letters = []
        for (let i in this.list) {
          letters.push(i)
        }
        return letters  //这样就构建除了一个letters的属性,返回结果样子['A','B','c']
      }
    },
    data () {
      return {
        touchStatus: false, //目的：只有touchstart才可以touchmove
        startY: 0,
        timer: null
      }
    },
    mounted () {
      this.startY = this.$refs['A'][0].offsetTop
    },
    methods: {
      handleLetterClick (e) {
        let change = e.target.innerText //为点击的内容，如A,B,C,D，将这个值传递给List.vue
        this.$emit('changeLetter',change)
      },
      handleTouchStart () {
        this.touchStatus = true
      },
      handleTouchMove (e) {
        if (this.touchStatus) {
          if (this.timer) {
            clearTimeout(this.timer)
          }
          this.timer = setTimeout(()=> {
            //A距离顶部的高度，= A的offsetTop（搜索框下面，好像是组件的最顶部）
            //const startY = this.$refs['A'][0].offsetTop,放到mounted为了提高性能
            const touchY = e.touches[0].clientY - 59  //手指距离最顶部的距离，浏览器的最顶部
            //59    为搜索框区域的高度
            const index = Math.floor((touchY - this.startY) / 20)  //当前字母对应的下标
            if (index >= 0 && index < this.letters.length) {
              this.$emit('changeLetter', this.letters[index])//最终还是传给list兄弟组件
            }
          }, 16)//函数截流，提高性能

        }
      },
      handleTouchEnd () {
        this.touchStatus = false
      },
    }
  }
</script>
<style rel="stylesheet/stylus" lang="stylus" scoped>
  .list
    display: flex
    flex-direction: column
    justify-content: center
    position: absolute
    top: 1.18rem
    right: 0
    bottom: 0
    width: .4rem
    .item
      line-height: .4rem
      text-align: center
      color: #f40

</style>