<template>
  <div class="wrapper">
    <div class="search">
      <span class="iconfont">&#xe60a;</span>
      <input v-model="keyword" class="search-input" type="text" placeholder="输入关键字" />
      <div class="search-btn" @click="clearInputContent">取消</div>
    </div>
    <div
        class="search-content"
        ref="search"
        v-show="keyword"
    >
      <ul>
        <li
            class="search-item border-bottom"
            v-for="(item,index) of lists"
            :key="index"
            @click="handleSubmitClick(item.gridName,item.id)"
        >
          {{item.gridName}}
        </li>
      </ul>
      <p class="hasNoData" v-show="hasNoData">
        — 没有找到匹配数据 —
      </p>
    </div>

  </div>
</template>

<script type=text/ecmascript-6>
  import Bscroll from 'better-scroll'
  import { mapMutations } from 'vuex'
  export default {
    name: "Search",
    props:['list','styleFlag','lastUrlName'],
    data () {
      return {
        keyword: '',
        lists: [],
        timer: null
      }
    },
    computed: {
      hasNoData () {
        return !this.lists.length
      }
    },
    methods: {
      /**
       * 点击取消按钮，清空内容
       */
      clearInputContent () {
        this.keyword = ''
      },
      /**
       * 在搜索中选择，直接返回上一页
       */
      handleSubmitClick (name,id) {
        this.changeLocation({
          localtionPlaceName: name,
          localtionPlaceCode: id,
        })
        this.transLocation({
          localtionPlaceName: name,
          localtionPlaceCode: id,
          style: this.styleFlag,
        })  //运输(起点3或终点4)
        this.$router.push({
          name:this.lastUrlName,
        })
      },
      ...mapMutations(['changeLocation','transLocation'])
    },
    watch:{
      keyword () {
        if(this.timer){
          clearTimeout()
        }
        if(!this.keyword){
          this.lists = []
          return
        }
        this.timer = setTimeout(()=>{
          const result = []
//          for(let i in this.list){
            this.list.forEach((value)=>{ //value为每一项[],值
              /*if(value.spell.indexOf(this.keyword) > -1 || value.name.indexOf(this.keyword > -1)){  对象形式，拼音可查
                //可以搜索到关键词
                result.push(value)
              }*/
              if(value.gridName.indexOf(this.keyword) > -1){ // 数组形式，只可汉字搜索
                //可以搜索到关键词
                result.push(value)
              }
            })
//          }
          this.lists = result   //存储了包含关键词的项目
        },100)
      }
    },
    mounted () {
      this.scroll = new Bscroll(this.$refs.search,{click:true})
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .wrapper
    padding: .2rem
    .search
      position: relative
      background: $bgColor
      border-radius: 5px
      .iconfont
        position: absolute
        top: .23rem
        left: .27rem
        font-weight: 700
        color: $textColor
      .search-input
        box-sizing: border-box
        width: 85%
        height: .78rem
        line-height: .78rem
        padding-left: 0.7rem
        border-radius: .1rem
        color: #666
        background-color: transparent
      .search-btn
        float: right
        height: 0.3rem
        line-height: 0.3rem
        font-size: .3rem
        font-weight: 700
        color: #353535
        border-left: 1px solid #D3D3D5
        padding-left: 10px
        margin: .24rem 0 .24rem 0
        width: 15%
        box-sizing: border-box
    .search-content
      z-index: 1
      overflow: hidden
      position: absolute
      top: 1.18rem
      left: 0
      right: 0
      bottom: 0
      background: #eee
      .search-item
        font-size: .32rem
        line-height: .88rem
        padding-left: .2rem
        background: #fff
        color: $darkTextColor
    .hasNoData
      position: fixed
      top: 1.5rem
      width: 100%
      text-align: center
      z-index: 10
      color: #ccc
</style>