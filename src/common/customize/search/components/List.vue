<template>
  <div class="list" ref="wrapper">
    <div>
      <div class="area" v-for="(item,index) of list" :key="index" :ref="index">  <!--为单纯的数组格式，没有大写字母-->
        <div class="item-list">
          <div
            class="item"
            @click="selectLetter"
         >{{ item.gridName }}
            <div class="iconfont" v-if="localtionPlaceName == item.gridName">&#xeaf1;</div>
         </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import Bscroll from 'better-scroll'
  export default {
    name: "ContentList",
    props:['list','letter'],//letter 为Alphbate通过父组件在传过来的，为点击的内容A,B,C,D
    data () {
      return {
        localtionPlaceName:'',
        localtionPlaceCode:'',
        style:'',  //运输(起点3或终点4)
      }
    },
    methods:{
      /**
       * 选择选中选项操作
       * @param e
       */
      selectLetter (e) {
        this.localtionPlaceName = e.target.innerText
        for (let i = 0; i < this.list.length; i++) {
          if(this.list[i].gridName == this.localtionPlaceName){
            this.localtionPlaceCode = this.list[i].id
          }
        }
        this.localtionPlaceCode = e.target.innerText
        //this.$emit('localtionInfo',{style:this.style,localtionPlaceName:this.localtionPlaceName,localtionPlaceCode:this.localtionPlaceCode})
      }
    },
    mounted () {
      this.scroll = new Bscroll(this.$refs.wrapper,{click:true})//之前是页面定位后overflow:hidden了，不能滚动，加上这个就可以了
    },
    watch:{
      letter () { //监听letter的变化
        if(this.letter){//不为空
          const element = this.$refs[this.letter][0]  //使用循环出来的ref是数组形式
          this.scroll.scrollToElement(element)//Better-scroll的方法，自动滚动到某个元素上
        }
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .border-topbottom
    &:before
      border-color: #ccc
    &:after
      border-color: #ccc
  .list
    overflow: hidden
    position: absolute
    top: 1.18rem    /*搜索框的高度*/
    right: 0
    bottom: 0
    left: 0
    .area
      &:last-child
        padding-bottom: 66px
        box-sizing: border-box
      .title
        line-height: .44rem
        background: $bgColor
        padding-left: .2rem
        color: #666
        font-size: .26rem
      .button-list
        overflow: hidden
        padding: .1rem .6rem .1rem .1rem
        .button-wrapper
          float: left
          width: 33%
          .button
            margin: .1rem
            padding: .1rem 0
            text-align: center
            border: .2rem solid #ccc
            border-radius: .06rem
      .item-list
        .item
          line-height: .88rem
          color: #666
          padding-left: .2rem
          .iconfont
            float: right
            padding-right: .8rem
            color: $color
</style>