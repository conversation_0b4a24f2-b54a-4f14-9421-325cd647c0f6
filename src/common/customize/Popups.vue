<template>
  <div class="popups" @click.prevent="close">
    <slot name="popups"></slot>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "Popups",
    data () {
      return {

      }
    },
    methods:{
      close () {
        this.$emit("close")
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
.popups
  position fixed
  top 0
  right 0
  left 0
  bottom 0
  background rgba(0,0,0,.5)
  z-index 2000
  display flex
  justify-content center
  align-items center

</style>
