<template>
  <div class="wrap" @click="handleCancelPop" ref="wrap">
    <div class="content" @click.stop>
      <div class="title item border-bottom">
        <span>请选择{{title}}</span>
        <span class="sure-btn" @click="handleCancelPop">确定</span>
      </div>


      <div class="scroll-wrapper">
        <pull-to :bottom-load-method="refresh">
          <div class="lists weui-cells weui-cells_checkbox" ref="lists">
            <div
                class="list"
                ref="lists"
                v-for="(item,index) of lists"
                :key="index"
            >
              <label class="weui-cell weui-check__label" :for="index">
                <div class="weui-cell__hd">
                  <input
                      type="checkbox"
                      class="weui-check"
                      name="checkbox1"
                      :value="item.designatePersonCode"
                      v-model="checkedNames"
                      :id="index"
                      checked="checked"
                  >
                  <i class="weui-icon-checked"></i>
                </div>
                <div class="weui-cell__bd">
                  <p>{{item.designatePersonName}}</p>
                </div>
              </label>
            </div>

          </div>
        </pull-to>
      </div>


    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import PullTo from 'vue-pull-to'
  export default {
    name: "PopupCheckbox",
    props: {
      lists: Array,
      title: String,
      flag: Boolean
    },
    components: {
      PullTo
    },
    data () {
      return {
        checkedNames: [],
        selectedCheckboxs: [],
        noData: false,
      }
    },
    methods: {
      /**
       * 上拉加载更多
       */
      refresh(loaded){
        if (this.flag) {
          this.$parent.handleDesignatePersonnel()
          loaded('done')
          this.$parent.flag = false
        }else{
          $.toast("没有更多数据啦","text")
          loaded('done')
        }
      },
      /**
       * 关闭弹窗
       * @param event
       */
      handleCancelPop (event) {
        event.stopPropagation()
        for (let i = 0; i < this.checkedNames.length; i++) {
          let item = this.checkedNames[i]
          for (let j = 0; j < this.lists.length; j++) {
            if (this.lists[j].designatePersonCode == item) {
              this.selectedCheckboxs.push(this.lists[j])
            }
          }
        }
        this.$emit('closePop', this.selectedCheckboxs)
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .weui-cells:after
  .weui-cells:before
    border: 0

  .weui-cells
    margin: 0

  .wrap
    wrapper()
    position: fixed
    overflow: hidden
    z-index 2
    background-color: rgba(0, 0, 0, .5)
    .content
      max-height: 6rem
      background-color: #fff
      position: absolute
      bottom: 0
      left: 0
      right: 0
      .title
        itemBaseStyle()
        font-size: .32rem
        .sure-btn
          float: right
          color: $color
      .scroll-wrapper
        overflow: hidden
      .lists
        max-height: 5rem
        margin: 0
        overflow: auto
        .list
          display: flex
          justify-content: space-between
          font-size: .30rem
          itemBaseStyle()

  .loading-icon
    height: 30px
    font-size: 12px
    color: $textColor
    text-align: center
    margin: 20px auto
</style>