<template>
  <div class="weui-cell weui-cell_vcode border-topbottom">
    <div class="weui-cell__hd" v-if="isShowPhoneText">
      <label class="weui-label">手机号</label>
    </div>
    <div class="weui-cell__bd">
      <input
        class="weui-input"
        type="text"
        v-model="phone"
        oninput="if(value.length>11)value=value.slice(0,11)"
        placeholder="请填入手机号"
      >
    </div>
    <div class="weui-cell__ft">
      <button class="weui-vcode-btn" type="button" :disabled="disabled" @click="sendcode">{{btntxt}}</button>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "PhoneText",
    props:['isShowPhoneText'],
    data: function () {
      return {
        disabled:false,
        time:0,
        btntxt:"获取验证码",
        phone:''
      }
    },
    methods:{
      /**
       * 验证手机号码部分
       */
      sendcode(){
        if(this.phone==''){
          $.toast("请输入手机号","text")
        }else if(this.phone.length<11){
          $.toast("请输入正确的手机号","text")
        }else{
          this.time=60;
          this.disabled=true;
          this.timer();
          this.axios.get(process.env.API_HOST + '/userController/getSMSCode',{
            params:{
              phone:this.phone
            }
          })
              .then(this.sendPhone)
        }
      },
      // sendcode(){
      //   var reg= /^((13|14|15|17|18)[0-9]{9})$/;
      //   if(this.phone==''){
      //     $.toast("请输入手机号","text")
      //   }else if(!reg.test(this.phone)){
      //     $.toast("请输入正确的手机号","text")
      //   }else{
      //     this.time=60;
      //     this.disabled=true;
      //     this.timer();
      //     this.axios.get(process.env.API_HOST + '/userController/getSMSCode',{
      //       params:{
      //         phone:this.phone
      //       }
      //     })
      //         .then(this.sendPhone)
      //   }
      // },
      sendPhone (response) {
        let data = response.data
        if (data.code = 200) {
          if (!data.data.smsCode) {
            $.toptip(data.message, 'error')
          } else {
            $.toptip(data.message, 'success')
            this.$emit("getSmsCode",data.data.smsCode,this.phone)
          }
        }
        if (data = 500) {
          $.toptip(data.message, 'error');
        }
      },
      timer() {
        if (this.time > 0) {
          this.time--;
          this.btntxt=this.time+"s后重新获取";
          setTimeout(this.timer, 1000);
        } else{
          this.time=0;
          this.btntxt="获取验证码";
          this.disabled=false;
        }
      }
    },
    watch: {
      phone (val) {
        this.phone = this.phone.replace(/[^0-9]+/g,'');
        this.$emit('getPhoneNum',val)
      },
      
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  .weui-cell_select-before .weui-cell__bd
    padding-left: 5px
  .border-topbottom
    &::after
      border-color: #fff
    &::before
      border-color: #fff
  .weui-vcode-btn
    color:#999
    height: 20px
    line-height:20px



</style>
