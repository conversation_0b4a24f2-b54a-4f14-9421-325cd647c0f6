// import global from "@/utils/Global.js";

export default {
  changeLogin(state, data) {
    state.isLogin = data;
    try {
      localStorage.isLogin = data;
      // global.setCookie("isLogin", "1", 365);
    } catch (e) {}
  },
  setOpenId(state, data) {
    state.openId = data;
    try {
      localStorage.openId = data;
      // global.setCookie("openId", data, 365);
    } catch (e) {}
  },
  setUnionId(state, data) {
    state.unionid = data;
    try {
      localStorage.unionid = data;
      // global.setCookie("unionid", data, 365);
    } catch (e) {}
  },
  changeHosp(state, hositals) {
    state.hospitalInfo = hositals;
  },
  changeDept(state, deptInfo) {
    state.deptInfo = deptInfo;
  },
  changeTeam(state, teamInfo) {
    state.teamInfo = teamInfo;
  },
  // 设置科室
  setDepartments(state, teamInfo) {
    state.departments = teamInfo;
  },
  //服务科室
   setDepartmentsCopy(state, teamInfo) {
    state.selectedOfficeNames = teamInfo;
  },
  // 服务房间
  setLocaltionPlaceName(state, teamInfo) {
    state.localtionPlaceName = teamInfo;
  },
  // 终点房间
  setTransportEndRoomName(state, teamInfo) {
    state.transportEndRoomName = teamInfo;
  },
  changeOutsourcedCo(state, outsourcedCo) {
    state.outsourcedCo = outsourcedCo;
  },
  changehUserInfo(state, userInfo) {
    state.userInfo = userInfo;
  },
  setUserNamePsw(state, userNamePsw) {
    state.userNamePsw = userNamePsw;
  },
  setGenderInfo(state, genderInfo) {
    state.genderInfo = genderInfo;
  },
  setEmailInfo(state, emailInfo) {
    state.emailInfo = emailInfo;
  },
  changeLocation(state, changeLocation) {
    //除运输以外的地点获取
    state.changeLocation = changeLocation;
  },
  changeLocationEnd(state, changeLocationEnd) {
    state.changeLocationEnd = changeLocationEnd;
  },
  transLocationStart(state, transLocationStart) {
    //运输起点获取
    state.transLocationStart = transLocationStart;
  },
  transLocationEnd(state, transLocationEnd) {
    //运输终点获取
    state.transLocationEnd = transLocationEnd;
  },
  addDesignatePersonnel(state, addDesignatePersonnel) {
    //指派人员列表
    state.addDesignatePersonnel = addDesignatePersonnel;
  },
  saveLeaguerType(state, saveLeaguerType) {
    //职工角色
    state.saveLeaguerType = saveLeaguerType;
  },
  // revise ServiceMatters
  //
  reviseServiceMatters(state, serviceMatters) {
    state.serviceMatters = serviceMatters;
  },
  //reviseServiceArea
  reviseServiceArea(state, serviceArea) {
    state.serviceArea = serviceArea;
  },
  reviseServiceAreaEnd(state, serviceAreaEnd) {
    state.serviceAreaEnd = serviceAreaEnd;
  },
  // 审批注册登录信息
  setLoginInfo(state, loginInfo) {
    state.loginInfo = loginInfo;
    try {
      localStorage.loginInfo = JSON.stringify(loginInfo);
      // delete loginInfo.tempPassword;
      // delete loginInfo.qqCode;
      // global.setCookie("loginInfo", JSON.stringify(loginInfo), 365);
    } catch (e) {}
  },
  // 基础数据职工信息
  setStaffInfo(state, staffInfo) {
    state.staffInfo = staffInfo;
    try {
      localStorage.staffInfo = JSON.stringify(staffInfo);
      // delete staffInfo.birthdate;
      // delete staffInfo.certificate;
      // delete staffInfo.hireBeginDate;
      // delete staffInfo.hireEndDate;
      // delete staffInfo.identityCard;
      // delete staffInfo.weight;
      // delete staffInfo.qq;
      // delete staffInfo.sex;
      // delete staffInfo.skill;
      // delete staffInfo.footwearSize;
      // delete staffInfo.contactAddress;
      // global.setCookie("staffInfo", JSON.stringify(staffInfo), 365);
    } catch (e) {}
  },
  // 区域树结构
  setServiceAreaTreeData(state, serviceAreaTreeData) {
    state.serviceAreaTreeData = serviceAreaTreeData;
  },
  // 空间登记
  setSpaceRegistration(state, data) {
    state.spaceRegistration = data
  },
  // 设置当前h5所属框架 iframe apicloud
  setH5Mode(state, data) {
    state.h5Mode = data
    localStorage.h5Mode = data;
  },
  setMaterialData(state, data) {
    state.materialData = data;
  }
};
