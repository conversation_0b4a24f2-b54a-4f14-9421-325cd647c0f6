import global from "@/utils/Global.js";

let defaultObj = null;
let staffInfoObj = null;
let defaultLoginStr = null; //未登录状态的状态码为0，登录后为1
let setOpenIdStr = null;
let setUnionIdStr = null;
let h5Mode = 'apicloud';

defaultObj = localStorage.loginInfo
  ? JSON.parse(localStorage.loginInfo)
  : // : global.getCookie("loginInfo")
    // ? JSON.parse(global.getCookie("loginInfo"))
    "";

staffInfoObj = localStorage.staffInfo
  ? JSON.parse(localStorage.staffInfo)
  : // : global.getCookie("staffInfo")
    // ? JSON.parse(global.getCookie("staffInfo"))
    "";

defaultLoginStr = localStorage.isLogin
  ? localStorage.isLogin
  : // : global.getCookie("isLogin")
    // ? global.getCookie("isLogin")
    undefined;

setOpenIdStr = localStorage.openId
  ? localStorage.openId
  : // : global.getCookie("openId")
    // ? global.getCookie("openId")
    undefined;

setUnionIdStr = localStorage.unionid
  ? localStorage.unionid
  : // : global.getCookie("unionid")
    // ? global.getCookie("unionid")
    undefined;

h5Mode = localStorage.h5Mode ? localStorage.h5Mode : 'apicloud';

export default {
  hospitalInfo: {
    unitName: "朝阳医管局",
    unitCode: "BJSYGJ",
    hospitalName: "世纪坛医院",
    hospitalCode: "BJSJTYY",
  },
  deptInfo: {
    deptCode: "",
    deptName: "",
  },
  outsourcedCo: {
    companyName: "",
    companyCode: "",
    id: "",
    legalPerson: "",
    principal: "",
    principalPhone: "",
  },
  userInfo: {
    name: "",
    jobNum: "",
    phone: "",
  },
  userNamePsw: {
    userName: "",
    password: "",
  },
  genderInfo: {
    sex: "",
    birthdate: "",
    phoneOA: "",
  },
  emailInfo: {
    email: "",
    education: "",
    mentorType: "",
  },
  changeLocation: {
    localtionPlaceName: "",
    localtionPlaceCode: "",
  },
  changeLocationEnd: {
    localtionPlaceName: "",
    localtionPlaceCode: "",
  },
  transLocationStart: {
    style: "",
    startPlaceCode: "",
    startPlaceName: "",
  },
  transLocationEnd: {
    style: "",
    endPlaceCode: "",
    endPlaceName: "",
  },
  saveLeaguerType: "", //职工角色
  serviceMatters: {},
  serviceArea: {},
  serviceAreaEnd: {},
  addDesignatePersonnel: [],
  // 审批注册登录信息
  loginInfo: defaultObj,
  // 基础数据职工信息
  staffInfo: staffInfoObj,
  isLogin: defaultLoginStr,
  openId: setOpenIdStr,
  unionid: setUnionIdStr,
  departments: null,
  selectedOfficeNames: null,
  localtionPlaceName: "",
  transportEndRoomName: "",
  serviceAreaTreeData: [],
  spaceRegistration: {},
  materialData: []
};
