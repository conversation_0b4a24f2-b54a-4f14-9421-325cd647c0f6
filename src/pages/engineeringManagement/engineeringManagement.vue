<template>
  <div class="wrapper">
    <Header title="工程管理" @backFun="goback"></Header>
    <div class="content">
      <div class="card-item" v-for="item in menuList" :key="item.label" @click="goPage(item.url)">
        <van-badge :content="item.count" max="99">
          <img :src="item.icon" />
        </van-badge>
        <span>{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import YBS from "@/assets/utils/utils.js";
import { mapState } from "vuex";
export default {
  name: "orderIndex",
  data() {
    return {
      power: false,
      loginInfo: {},
      staffInfo: {},
      timer: "",
      sum: "",
      menuList: [
        {
          icon: require("@/assets/images/工程管理-审批.png"),
          label: "审批",
          url: "examineApprove",
          count: ""
        },
        {
          icon: require("@/assets/images/工程管理-项目管理.png"),
          label: "项目管理",
          url: "projectManagement",
          count: ""
        },
        {
          icon: require("@/assets/images/工程管理-合同管理.png"),
          label: "合同管理",
          url: "contractManagement",
          count: ""
        },
        {
          icon: require("@/assets/images/工程管理-质检.png"),
          label: "质量检查",
          url: "qualityInspection",
          count: ""
        },
        {
          icon: require("@/assets/images/工程管理-隐患整改.png"),
          label: "隐患整改",
          url: "hiddenTroubleRectification",
          count: ""
        },
        {
          icon: require("@/assets/images/工程管理-中间验收.png"),
          label: "中间验收",
          url: "intermediateAcceptance",
          count: ""
        }
      ]
    };
  },

  created() {
    this.checkUserExists();
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.staffInfo = JSON.parse(localStorage.getItem("staffInfo"));
    // 获取储存、相机权限
    if (!YBS.hasPermission("storage")) {
      YBS.reqPermission(["storage"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          if (!YBS.hasPermission("camera")) {
            YBS.reqPermission(["camera"], function(ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              }
            });
            return;
          } else {
          }
        }
      });
      return;
    }
    if (!YBS.hasPermission("camera")) {
      YBS.reqPermission(["camera"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
        }
      });
      return;
    }
  },
  mounted() {},
  methods: {
    checkUserExists() {
      this.$api
        .checkUserExists({})
        .then(res => {
          this.getExamineApproveList();
          this.power = true;
        })
        .catch(res => {
          this.power = false;
        });
    },
    getExamineApproveList() {
      let data = {
        page: 1,
        limit: 99999,
        status: 0
      };
      this.$api
        .examineApproveList(data)
        .then(res => {
          this.menuList[0].count = res.total;
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    goPage(url) {
      if (!this.power) return this.$toast.fail("抱歉，该账号无权限访问");
      if (!url) return;
      this.$router.push({ path: url });
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  background-color: #f2f4f9;
  height: 100vh;
}
.content {
  display: flex;
  flex-wrap: wrap;
  padding: 6px;
}
.card-item {
  width: 29vw;
  height: 32vw;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: calc((100% - 29vw * 3) / 3 / 2);
  // margin-bottom: 16px;
}
.card-item img {
  width: 40px;
}
.card-item span {
  transform: translateY(12px);
}
</style>
