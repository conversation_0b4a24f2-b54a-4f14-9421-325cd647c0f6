<template>
  <div class="inner">
    <Header title="质量整改审批" @backFun="goBack"></Header>
    <div class="essential">
      <div class="txt"><i class="public"></i>基础信息</div>
      <div class="txt">
        <div class="txt_l">待办编号</div>
        <div>{{ formItem.sysGtasks.number }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">待办标题</div>
        <div>{{ formItem.sysGtasks.title }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起人</div>
        <div>{{ formItem.sysGtasks.createName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起时间</div>
        <div>{{ formItem.sysGtasks.createTime }}</div>
      </div>
    </div>
    <div class="rectify">
      <div class="txt"><i class="public"></i>整改信息</div>
      <div class="txt">
        <div class="txt_l">整改部门</div>
        <div>{{ formItem.inspectionPart.rectificationDepartmentName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改责任人</div>
        <div>{{ formItem.inspectionPart.rectificationPeopleName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">联系电话</div>
        <div>{{ formItem.inspectionPart.rectificationPeoplePhone }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">隐患等级</div>
        {{ getDangerGradeListString(formItem.inspectionPart.dangerGrade) }}
      </div>
      <div class="txt">
        <div class="txt_l">整改建议</div>
        <div>{{ formItem.inspectionPart.dangerAdvise }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改期限</div>
        <div>{{ formItem.inspectionPart.rectificationTime }}</div>
      </div>
    </div>
    <div class="hTphotos">
      <div class="photos">
        <div class="title">
          <div>隐患照片</div>
        </div>
        <div v-for="(rowitem, index) in formItem.inspectionPart.attachments" :key="index">
          <van-image width="100" height="100" @click="dkImg($YBS.imgUrlTranslation('imem/' + rowitem.fileUrl))" :src="$YBS.imgUrlTranslation('imem/' + rowitem.fileUrl)" />
        </div>
      </div>
    </div>
    <div class="suggestion">
      <div class="txt">
        <div class="txt_l">整改完成时间</div>
        <div>{{ formItem.rectificationVO.rectificationCompleteTime }}</div>
      </div>
      <div>整改措施说明</div>
      <van-field readonly v-model="formItem.rectificationVO.rectificationExplain" rows="2" autosize label="" type="textarea" maxlength="500" />
      <div class="uplod">
        <div>整改图片</div>
        <div>
          <div v-for="(rowitem, index) in formItem.rectificationVO.attachments" :key="index">
            <van-image width="100" height="100" @click="dkImg($YBS.imgUrlTranslation('imem/' + rowitem.fileUrl))" :src="$YBS.imgUrlTranslation('imem/' + rowitem.fileUrl)" />
          </div>
        </div>
      </div>
    </div>
    <div class="suggestion">
      <div class="uplod">
        <div>上传附件</div>
        <van-uploader
          :after-read="afterRead"
          v-model="fileListCurrent"
          :max-size="maxSize * 1024 * 1024"
          @oversize="onOversize"
          :before-delete="beforeDel"
          accept="image/*,video/*"
          :disabled="fileMessage == '上传中' || formItem.sysGtasks.status == 1"
          max-count="5"
        >
          <template v-if="fileMessage == '上传成功'" #preview-cover="{ file }">
            <div class="preview-cover van-ellipsis">上传成功</div>
          </template>
        </van-uploader>
        <div class="upload-tip">注：上传格式图片、视频，单张大小≤50M</div>
      </div>

      <van-field
        v-model="formItem.approval.comment"
        :readonly="formItem.sysGtasks.status == 1"
        rows="2"
        required
        autosize
        label="审批意见"
        type="textarea"
        maxlength="500"
        placeholder="请输入审批意见,字数限制500字以内"
        show-word-limit
        :rules="[{ required: true }]"
      />
    </div>
    <div class="table_sty" style="height:130px;">
      <van-field v-if="formItem.sysGtasks.status == 0" readonly v-model="dealingName" label="指定协办人" right-icon="arrow" input-align="left" @click="showDealing = true" />
      <van-field v-else readonly v-model="dealingName" label="指定协办人" right-icon="arrow" input-align="left" />
      <van-popup v-model="showDealing" position="bottom">
        <van-picker value-key="userName" show-toolbar :columns="dealingList" @confirm="onDealing" @cancel="showDealing = false" />
      </van-popup>
      <div class="txt"><i class="public"></i>指定记录</div>
      <el-table :data="formItem.assignRecordList" border stripe>
        <el-table-column prop="createName" label="指定人" align="center"></el-table-column>
        <el-table-column prop="disposeName" label="协办人" align="center"></el-table-column>
        <el-table-column prop="createTime" label="指定时间" align="center"></el-table-column>
      </el-table>
    </div>
    <div class="table_sty_2">
      <div class="txt"><i class="public"></i>审批记录</div>
      <el-table :data="approvalList" border stripe>
        <el-table-column prop="fileApprovalPersonName" label="审批人" align="center"></el-table-column>
        <el-table-column prop="comment" label="审批意见" align="center"></el-table-column>
        <el-table-column prop="status" label="审批结果" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.status == 1 ? "通过" : "驳回" }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="btn" v-if="formItem.sysGtasks.status == 0">
      <van-button style="width:45%" color="#3562db" @click="handleSubmit(-1)">驳回</van-button>
      <van-button style="width:45%" color="#3562db" @click="handleSubmit(1)">提交</van-button>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import YBS from "@/assets/utils/utils.js";
import Vue from "vue";
import { Image as VanImage } from "vant";
import { ImagePreview } from "vant";
Vue.use(VanImage);

export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  components: {},
  data() {
    return {
      fileListCurrent: [],
      maxSize: 50, //
      fileMessage: "",
      showDealing: false,
      approvalList: [],
      dealingList: [], //指定协办人
      dealingName: "",
      formItem: {
        id: "", //主键
        gtasksId: "", //代办id
        approval: {}, //审批对象
        status: "", //状态  1 提交  2 指定代办人
        rectificationVO: {
          inspectionPartId: "", //检查部位id
          qualityInspectionId: "", //质量检查id
          rectificationCompleteTime: "", //整改完成时间
          rectificationExplain: "", //整改说明
          attachment: [] //整改附件
        },
        userVOS: [], // 指定用户集合
        //其余内容
        sysGtasks: {}, //待办对象
        assignRecordList: [],
        inspectionPart: {}
      },
      dangerGradeList: [
        { dataLabel: "一级", dataValue: 1 },
        { dataLabel: "二级", dataValue: 2 },
        { dataLabel: "三级", dataValue: 3 }
      ],
      tableData: [],
      tel: "",
      active: "0"
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    // 获取储存、相机权限
    if (!YBS.hasPermission("storage")) {
      YBS.reqPermission(["storage"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          if (!YBS.hasPermission("camera")) {
            YBS.reqPermission(["camera"], function(ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              }
            });
            return;
          } else {
          }
        }
      });
      return;
    }
    if (!YBS.hasPermission("camera")) {
      YBS.reqPermission(["camera"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
        }
      });
      return;
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getApprovalByType();
  },
  methods: {
    handleSubmit(status) {
      if (this.formItem.approval.comment == "") return this.$toast.fail("请填写审批意见");
      let self = this;

      //取消验证
      if (this.formItem.userVOS && this.formItem.userVOS.length > 0) {
        this.ruleValidate = {};
      }

      var params = this.formItem.approval;
      if (params.comment == "") {
        this.$toast.fail("请输入审批意见");
        return;
      }
      params.status = status;
      params.userVOS = this.formItem.userVOS;
      if (params.status != -1 && params.userVOS && params.userVOS.length > 0) {
        params.status = 2;
      }

      this.$api
        .getAppr(params)
        .then(res => {
          this.$toast.success(res);
          this.goBack();
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    afterRead(file2) {
      this.fileMessage = "上传中";
      var fileSize = parseFloat(parseInt(file2.file["size"]) / 1024 / 1024).toFixed(2);
      const { file } = file2;

      file2.status = "uploading";
      file2.message = "上传中...";
      this.handleUploadImg(file, file2);
    },

    handleUploadImg(file, file2) {
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.IMEM_API + "/api/file/uploadFile",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          userId: JSON.parse(localStorage.getItem("loginInfo")).staffId
        }
      }).then(res => {
        if (res.data.code == 0) {
          file2.status = "success";
          file2.message = "上传成功";
          this.fileMessage = "上传成功";
          this.formItem.approval.attachments.push(res.data.data);
        } else {
          file2.status = "failed";
          file2.message = "上传失败";
          this.fileMessage = "上传失败";
        }
      });
    },
    // 删除图片 之前
    beforeDel(file, index) {
      console.log(index, "index");
      // 上传到服务器失败
      this.formItem.approval.attachments.splice(index, 1);
      this.fileListCurrent.splice(index, 1);
    },
    onOversize() {
      this.$toast.fail(`大小不能超过${this.maxSize}M`);
    },
    getApprovalByType() {
      let self = this;

      this.$api
        .getApprovalByType({ id: this.$route.query.sysGtaskId })
        .then(res => {
          self.formItem.approval = res.approval;

          self.formItem.rectificationVO.qualityInspectionId = res.inspectionPart.qualityInspectionId;
          self.formItem.rectificationVO.projectId = res.inspectionPart.projectId;
          self.formItem.rectificationVO.inspectionPartId = res.inspectionPart.id;

          self.formItem.rectificationVO.rectificationCompleteTime = res.rectification.rectificationCompleteTime;
          self.formItem.rectificationVO.rectificationExplain = res.rectification.rectificationExplain;
          self.formItem.rectificationVO.attachments = res.rectification.attachments;

          self.formItem.inspectionPart = res.inspectionPart;
          self.formItem.sysGtasks = res.sysGtasks;
          self.approvalList = res.approvalList;
          self.formItem.assignRecordList = res.assignRecordList;
          this.getByProjectId(self.formItem.rectificationVO.projectId);
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    getByProjectId(id) {
      this.$api.getByProjectId({ id: id }).then(res => {
        this.dealingList = res;
      });
    },
    onDealing(value) {
      this.dealingName = value.userName;
      this.formItem.userVOS.push(value);
      this.showDealing = false;
    },
    getDangerGradeListString(value) {
      return this.selectDictLabel(value, this.dangerGradeList);
    },
    selectDictLabel(value, datas) {
      var actions = [];
      Object.keys(datas).some(key => {
        if (datas[key].dataValue == "" + value) {
          actions.push(datas[key].dataLabel);
          return true;
        }
      });

      return actions.join("");
    },
    dkImg(img) {
      ImagePreview([img]);
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    height: 200px;
    background-color: #fff;
    margin-bottom: 10px;
  }
}
.inspect {
  width: 100%;
  height: 270px;
  background-color: #fff;
  margin-bottom: 10px;
}
.rectify {
  width: 100%;
  height: 270px;
  background-color: #fff;
  margin-bottom: 10px;
}
.txt {
  display: flex;
  padding: 11px 10px;
  .txt_l {
    width: 150px;
    color: #4e5969;
    font-weight: 300;
  }
}

.btn {
  z-index: 999;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.suggestion {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
  .uplod {
    background-color: #fff;
    line-height: 50px;
    width: 95%;
    margin-bottom: 10px;
    padding: 0 10px;
  }
  > div {
    padding: 11px 10px;
  }
}
.hTphotos {
  width: 100%;
  min-height: 50px;
  background-color: #fff;
  margin-bottom: 10px;
  .photos {
    padding: 10px;
    .title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }
}
.table_sty {
  width: 100%;
  margin-bottom: 10px;
  background-color: #fff;
  .txt {
    display: flex;
    padding: 11px 10px;
  }
}
.table_sty_2 {
  width: 100%;
  margin-top: 69px;
  margin-bottom: 59px;
  background-color: #fff;
  .txt {
    display: flex;
    padding: 11px 10px;
  }
}
/deep/ .el-table__header th {
  background-color: #e6effc; /* 设置表头背景颜色 */
  color: #1d2129; /* 设置表头文字颜色 */

  font-weight: 400;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
/deep/ .van-cell {
  padding: 11px 15px !important;
}
.upload-tip {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #696969;
  line-height: 20px;
}
.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  height: 20px;
  line-height: 15px;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
