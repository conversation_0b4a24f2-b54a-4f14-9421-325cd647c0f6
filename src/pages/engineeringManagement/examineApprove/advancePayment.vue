<template>
  <div class="inner">
    <Header title="预收付款审批" @backFun="goBack"></Header>
    <div class="essential">
      <div class="txt"><i class="public"></i>基础信息</div>
      <div class="txt">
        <div class="txt_l">待办编号</div>
        <div>{{ formItem.sysGtasks.number }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">待办标题</div>
        <div>{{ formItem.sysGtasks.title }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起人</div>
        <div>{{ formItem.sysGtasks.createName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起时间</div>
        <div>{{ formItem.sysGtasks.createTime }}</div>
      </div>
    </div>

    <div class="rectify">
      <div class="txt"><i class="public"></i>项目信息</div>
      <div class="txt">
        <div class="txt_l">所属项目</div>
        <div>{{ formItem.projectName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">所属合同</div>
        <div>{{ formItem.contractName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">甲方单位</div>
        <div>{{ formItem.partyAUnit }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">乙方单位</div>
        <div>{{ formItem.partyBUnit }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">合同金额(万元)</div>
        <div>{{ formItem.contractAmount }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">预收/付款(万元)</div>
        <div>{{ formItem.advances }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">收付类型</div>
        <div>{{ formItem.receiptsPaymentType == "collection" ? "收" : "付" }}</div>
      </div>
    </div>
    <van-form @submit="onSubmit">
      <div class="suggestion">
        <van-field
          :readonly="formItem.sysGtasks.status == 1"
          required
          :rules="[{ required: true }]"
          v-model="formItem.actualAmount"
          label="实收金额"
          placeholder="请填写实收金额"
        />
        <van-field
          v-model="formItem.receiptsPaymentTime"
          label="收/付时间"
          @click="showBeginDate"
          right-icon="arrow"
          input-align="left"
          readonly
          required
          placeholder="请选择收/付时间"
          :rules="[{ required: true }]"
        />
        <van-popup v-model="showPickerDate" position="bottom">
          <van-datetime-picker v-model="currentDate" type="date" @cancel="onCancelDate" @confirm="onConfirmDate" :min-date="minDate" :max-date="maxDate" :formatter="formatter" />
        </van-popup>
        <div>备注</div>
        <van-field v-model="formItem.comment" rows="2" autosize label="" type="textarea" maxlength="500" placeholder="请输入备注,字数限制500字以内" show-word-limit />
        <div class="uplod">
          <div>附件</div>
          <van-uploader
            :after-read="afterRead"
            v-model="fileListCurrent"
            :max-size="maxSize * 1024 * 1024"
            @oversize="onOversize"
            :before-delete="beforeDel"
            accept="image/*,video/*"
            :disabled="fileMessage == '上传中' || formItem.sysGtasks.status == 1"
            max-count="5"
          >
            <template v-if="fileMessage == '上传成功'" #preview-cover="{ file }">
              <div class="preview-cover van-ellipsis">上传成功</div>
            </template>
          </van-uploader>
          <div class="upload-tip">注：上传格式图片、视频，单张大小≤50M</div>
        </div>
      </div>
      <div class="table_sty" style="height:130px;">
        <van-field v-if="formItem.sysGtasks.status == 0" readonly v-model="dealingName" label="指定协办人" right-icon="arrow" input-align="left" @click="showDealing = true" />
        <van-field v-else readonly v-model="dealingName" label="指定协办人" right-icon="arrow" input-align="left" />
        <van-popup v-model="showDealing" position="bottom">
          <van-picker value-key="userName" show-toolbar :columns="dealingList" @confirm="onDealing" @cancel="showDealing = false" />
        </van-popup>
        <div class="txt"><i class="public"></i>指定记录</div>
        <el-table :data="formItem.assignRecordList" border stripe>
          <el-table-column prop="createName" label="指定人" align="center"></el-table-column>
          <el-table-column prop="disposeName" label="协办人" align="center"></el-table-column>
          <el-table-column prop="createTime" label="指定时间" align="center"></el-table-column>
        </el-table>
      </div>

      <div class="btn" v-if="formItem.sysGtasks.status == 0"><van-button style="width:90%" color="#3562db" type="info" native-type="submit">提交</van-button></div>
    </van-form>
  </div>
</template>

<script>
import axios from "axios";
import YBS from "@/assets/utils/utils.js";

export default {
  components: {},
  data() {
    return {
      fileListCurrent: [],
      maxSize: 50, //
      fileMessage: "",
      showPickerDate: false,
      currentDate: new Date(),
      value: "",
      value2: "",
      value3: "",
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      dealingName: "",
      showDealing: false,
      dealingList: [], //指定协办人
      formItem: {
        gtasksId: "",
        status: 1,
        sysGtasks: {}, //待办对象
        assignRecordList: [], //指定待办记录
        userVOS: [],
        contract: {},
        contractPaymentReceivedVO: {},

        //
        receiptPaymentUnitName: "", //收付单位
        receiptPaymentUnitId: "", //收付单位id
        receiptPaymentNumber: "", //收付款单号
        projectId: "", //项目id
        contractId: "", //合同id
        comment: "", //备注
        actualAmount: "", //实际金额
        receiptsPaymentType: "", //收付类型  payment:付款   collection:收款
        paymentNodeId: "", //付款节点id
        receiptsPaymentTime: "", //收付时间
        paymentNodesList: [],
        attachments: [], //附件
        partyAUnit: "", //甲方
        partyBUnit: "" //
      },
      tableData: [],
      tel: ""
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    // 获取储存、相机权限
    if (!YBS.hasPermission("storage")) {
      YBS.reqPermission(["storage"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          if (!YBS.hasPermission("camera")) {
            YBS.reqPermission(["camera"], function(ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              }
            });
            return;
          } else {
          }
        }
      });
      return;
    }
    if (!YBS.hasPermission("camera")) {
      YBS.reqPermission(["camera"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
        }
      });
      return;
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getApprovalByType();
  },
  methods: {
    onSubmit() {
      let self = this;

      let params = this.formItem;
      //如果有指定协办人
      if (params.userVOS.length > 0) {
        params.status = 2;
      }
      let temcpVO = {};

      temcpVO.receiptPaymentUnitName = self.formItem.receiptPaymentUnitName; //收付单位
      temcpVO.receiptPaymentNumber = self.formItem.receiptPaymentNumber; //收付款单号
      temcpVO.projectId = self.formItem.projectId; //项目id
      temcpVO.contractId = self.formItem.contractId; //合同id
      temcpVO.contractName = self.formItem.contractName;
      temcpVO.projectName = self.formItem.projectName;
      temcpVO.comment = self.formItem.comment; //备注
      temcpVO.actualAmount = self.formItem.actualAmount; //实际金额
      temcpVO.contractAmount = self.formItem.contractAmount; //合同金额
      temcpVO.receiptsPaymentType = self.formItem.receiptsPaymentType; // 收付类型 payment:付款   collection:收款
      temcpVO.paymentNodeId = self.formItem.paymentNodeId; //付款节点id
      temcpVO.receiptsPaymentTime = self.formItem.receiptsPaymentTime; //收付时间
      temcpVO.attachments = self.formItem.attachments;
      temcpVO.type = 0; //0 计划 付款节点  1 合同预收款

      self.formItem.contractPaymentReceivedVO = temcpVO;
      this.$api
        .getContractSubmit(params)
        .then(res => {
          this.$toast.success(res);
          this.goBack();
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    afterRead(file2) {
      this.fileMessage = "上传中";
      var fileSize = parseFloat(parseInt(file2.file["size"]) / 1024 / 1024).toFixed(2);
      const { file } = file2;

      file2.status = "uploading";
      file2.message = "上传中...";
      this.handleUploadImg(file, file2);
    },

    handleUploadImg(file, file2) {
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.IMEM_API + "/api/file/uploadFile",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          userId: JSON.parse(localStorage.getItem("loginInfo")).staffId
        }
      }).then(res => {
        if (res.data.code == 0) {
          file2.status = "success";
          file2.message = "上传成功";
          this.fileMessage = "上传成功";
          this.formItem.approval.attachments.push(res.data.data);
        } else {
          file2.status = "failed";
          file2.message = "上传失败";
          this.fileMessage = "上传失败";
        }
      });
    },
    // 删除图片 之前
    beforeDel(file, index) {
      console.log(index, "index");
      // 上传到服务器失败
      this.formItem.approval.attachments.splice(index, 1);
      this.fileListCurrent.splice(index, 1);
    },
    onOversize() {
      this.$toast.fail(`大小不能超过${this.maxSize}M`);
    },
    formatter(type, value) {
      if (type === "year") {
        this.value1 = value; // 可以拿到当前点击的数值
        return `${value}年`;
      } else if (type === "month") {
        this.value2 = value;
        return `${value}月`;
      }
      this.value3 = value;
      return `${value}日`;
    },
    onConfirmDate() {
      this.formItem.receiptsPaymentTime = `${this.value1}-${this.value2}-${this.value3}`; // 字符串拼接 结果入2020-07-03
      this.onCancelDate();
    },
    showBeginDate() {
      if (this.formItem.sysGtasks.status == 0) {
        this.showPickerDate = true;
      }
    },
    onCancelDate() {
      this.showPickerDate = false;
    },
    onDealing(value) {
      this.dealingName = value.userName;
      this.formItem.userVOS.push(value);
      this.showDealing = false;
    },
    getApprovalByType() {
      let self = this;
      this.$api
        .getApprovalByType({ id: this.$route.query.sysGtaskId })
        .then(res => {
          self.formItem.gtasksId = res.sysGtasks.id;
          self.formItem.sysGtasks = res.sysGtasks;
          self.formItem.contractPaymentReceived = res.contractPaymentReceived;
          self.formItem.assignRecordList = res.assignRecordList;

          let contract = res.contract;

          self.formItem.paymentNodesList = contract.contractPaymentNodeList;

          self.formItem.receiptPaymentUnitName = contract.partyAUnit; //收付单位
          self.formItem.partyAUnit = contract.partyAUnit; //甲方单位
          self.formItem.partyBUnit = contract.partyBUnit; //乙方单位
          self.formItem.receiptPaymentNumber = ""; //收付款单号
          self.formItem.projectId = contract.projectId; //项目id
          this.getByProjectId(self.formItem.projectId);
          self.formItem.contractId = contract.id; //合同id
          self.formItem.contractName = contract.contractName;
          self.formItem.projectName = contract.projectName;
          self.formItem.comment = ""; //备注
          self.formItem.actualAmount = ""; //实际金额
          self.formItem.advances = contract.advances; //预付金额
          self.formItem.contractAmount = contract.contractPrice; //合同金额
          //contract.rpType collection 收款  payment 付款
          if (contract.rpType == "collection") {
            self.formItem.receiptsPaymentType = "collection"; //  收付类型 0 收 1 付
          }
          if (contract.rpType == "payment") {
            self.formItem.receiptsPaymentType = "payment"; //  收付类型 0 收 1 付
          }

          self.formItem.paymentNodeId = ""; //付款节点id
          self.formItem.receiptsPaymentTime = ""; //收付时间

          /*          self.$api.baseApilist.contract.get(self.formItem.contract.contractId).then(res => {
                    });*/
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    getByProjectId(id) {
      this.$api.getByProjectId({ id: id }).then(res => {
        this.dealingList = res;
      });
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
  }
}
.inspect {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
}
.rectify {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
}
.txt {
  display: flex;
  padding: 11px 10px;
  .txt_l {
    width: 150px;
    color: #4e5969;
    font-weight: 300;
  }
}

.btn {
  z-index: 999;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.suggestion {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
  .uplod {
    background-color: #fff;
    line-height: 50px;
    width: 95%;
    margin-bottom: 10px;
    padding: 0 10px;
  }
  > div {
    padding: 11px 15px;
  }
}

.table_sty {
  width: 100%;
  margin-bottom: 120px;

  background-color: #fff;
  .txt {
    display: flex;
    padding: 11px 10px;
  }
}
/deep/ .el-table__header th {
  background-color: #e6effc; /* 设置表头背景颜色 */
  color: #1d2129; /* 设置表头文字颜色 */

  font-weight: 400;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
.upload-tip {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #696969;
  line-height: 20px;
}
.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  height: 20px;
  line-height: 15px;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
