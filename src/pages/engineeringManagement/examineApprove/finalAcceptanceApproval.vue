<template>
  <div class="inner">
    <Header title="竣工验收审批" @backFun="goBack"></Header>
    <div class="essential">
      <div class="txt"><i class="public"></i>基础信息</div>
      <div class="txt">
        <div class="txt_l">待办编号</div>
        <div>{{ formItem.sysGtasks.number }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">待办标题</div>
        <div>{{ formItem.sysGtasks.title }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起人</div>
        <div>{{ formItem.sysGtasks.createName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起时间</div>
        <div>{{ formItem.sysGtasks.createTime }}</div>
      </div>
    </div>
    <div class="inspect">
      <div class="txt"><i class="public"></i>检查信息</div>
      <div class="txt">
        <div class="txt_l">项目名称</div>
        <div>{{ formItem.projectApproval.name }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">验收部位</div>
        <div>{{ completionAcceptanceManagement.acceptancePosition }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">施工单位</div>
        <div>{{ completionAcceptanceManagement.constructionUnit }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">验收人</div>
        <div>{{ formItem.acceptanceItems.acceptor }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">检查编号</div>
        <div>{{ completionAcceptanceManagement.acceptanceNumber }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">验收时间</div>
        <div>{{ completionAcceptanceManagement.acceptanceDate }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">验收事项</div>
        <div>{{ formItem.acceptanceItems.acceptanceItems }}</div>
      </div>
    </div>
    <div class="rectify">
      <div class="txt"><i class="public"></i>整改明细</div>
      <div class="txt">
        <div class="txt_l">整改部门</div>
        <div>{{ formItem.acceptanceItems.rectificationDepartmentName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改责任人</div>
        <div>{{ formItem.acceptanceItems.rectificationPeopleName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改期限</div>
        <div>{{ formItem.acceptanceItems.rectificationTime }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">隐患等级</div>
        <div>{{ formItem.acceptanceItems.dangerGrade }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">责任人电话</div>
        <div>{{ formItem.acceptanceItems.rectificationPeoplePhone }}</div>
      </div>
    </div>

    <div class="suggestion">
      <div>审批意见</div>
      <van-field
        :readonly="formItem.sysGtasks.status == 1"
        v-model="formItem.approval.comment"
        rows="3"
        autosize
        label=""
        type="textarea"
        maxlength="500"
        placeholder="请输入审批意见,需驳回时，此项必填，字数限制500字以内"
        show-word-limit
      />
    </div>
    <div v-if="formItem.sysGtasks.status == 0" class="btn">
      <van-button style="width:40%" color="#3562db" @click="handleSubmit(-1)">驳回</van-button
      ><van-button style="width:40%" color="#3562db" @click="handleSubmit(1)">通过</van-button>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      completionAcceptanceManagement: {},
      projectname: "", //项目名称
      formItem: {
        id: "", //主键
        gtasksId: "", //代办id
        approval: {}, //审批对象
        status: "", //状态  1 提交  2 指定代办人
        rectificationVO: {
          rectificationCompleteTime: "", //整改完成时间
          rectificationExplain: "", //整改说明
          attachment: [] //整改附件
        },
        userVOS: [], // 指定用户集合
        //其余内容
        projectApproval: {},
        sysGtasks: {}, //待办对象
        approvalList: [],
        assignRecordList: [],
        acceptanceItems: {}
      }
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getApprovalByType();
  },
  methods: {
    handleSubmit(status) {
      if (status == -1 && this.formItem.approval.comment == "") return this.$toast.fail("请填写审批意见");

      var params = this.formItem.approval;
      params.status = status;
      params.userVOS = this.formItem.userVOS;
      if (params.status != -1 && params.userVOS && params.userVOS.length > 0) {
        params.status = 2;
      }
      this.$api
        .getAppr(params)
        .then(res => {
          this.$toast.success(res);
          this.goBack();
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    getApprovalByType() {
      let self = this;
      this.$api.getApprovalByType({ id: this.$route.query.sysGtaskId }).then(res => {
        //审批信息
        self.formItem.approval = res.approval;

        self.formItem.rectificationVO.projectId = res.acceptanceItems.projectId;
        self.formItem.rectificationVO.rectificationCompleteTime = res.rectification.rectificationCompleteTime;
        self.formItem.rectificationVO.rectificationExplain = res.rectification.rectificationExplain;
        self.formItem.rectificationVO.attachments = res.rectification.attachments;

        self.formItem.acceptanceItems = res.acceptanceItems;
        self.formItem.sysGtasks = res.sysGtasks;
        self.formItem.projectApproval = res.projectApproval;

        self.formItem.approvalList = res.approvalList;
        self.formItem.assignRecordList = res.assignRecordList;

        self.completionAcceptanceManagement = res.rectification.completionAcceptanceManagement; //返回null导致报错
      });
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
  }
}
.inspect {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
}
.rectify {
  width: 100%;
  height: 224px;
  background-color: #fff;
  margin-bottom: 10px;
}
.txt {
  display: flex;
  padding: 11px 10px;
  .txt_l {
    width: 150px;
    color: #4e5969;
    font-weight: 300;
  }
}

.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.suggestion {
  width: 100%;
  background-color: #fff;
  margin-bottom: 60px;

  > div {
    padding: 5px 10px;
  }
}
.public {
  display: inline-block;
  width: 4px;
  height: 14px;
  margin-right: 5px;
  background: #3562db;
}
</style>
