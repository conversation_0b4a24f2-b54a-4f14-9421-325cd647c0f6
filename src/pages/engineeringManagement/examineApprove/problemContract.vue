<template>
  <div class="inner">
    <Header title="问题合同" @backFun="goBack"></Header>
    <div class="essential">
      <div class="txt"><i class="public"></i>基础信息</div>
      <div class="txt">
        <div class="txt_l">待办编号</div>
        <div>{{ formItem.sysGtasks.number }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">待办标题</div>
        <div>{{ formItem.sysGtasks.title }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起人</div>
        <div>{{ formItem.sysGtasks.createName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起时间</div>
        <div>{{ formItem.sysGtasks.createTime }}</div>
      </div>
    </div>

    <div class="rectify">
      <div class="txt"><i class="public"></i>项目信息</div>
      <div class="txt">
        <div class="txt_l">项目名称</div>
        <div>{{ contract.projectName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">合同名称</div>
        <div>{{ contract.contractName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">合同编号</div>
        <div>{{ contract.contractNumber }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">签订日期</div>
        <div>{{ contract.signTime }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">合同类型</div>
        <div>{{ getContractTypeDict(contract.contractType) }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">合同金额(万元)</div>
        <div>{{ contract.contractPrice }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">甲方单位</div>
        <div>{{ contract.partyAUnit }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">乙方单位</div>
        <div>{{ contract.partyBUnit }}</div>
      </div>
    </div>
    <div class="essential">
      <div class="txt"><i class="public"></i>问题说明</div>
      <div class="txt">
        <div class="txt_l">问题类型</div>
        <div>{{ getContractIssueDict(contractProblem.type) }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">问题描述</div>
        <div>{{ contractProblem.problemComment }}</div>
      </div>
    </div>
    <div class="essential" v-if="$route.query.status == 1">
      <div class="txt"><i class="public"></i>处理意见</div>
      <div class="txt">
        <div class="txt_l">处理意见</div>
        <div>{{ formItem.contractProblemVO.legalComment }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">问题描述</div>
        <div>{{ contractProblem.problemComment }}</div>
      </div>
    </div>
    <div class="suggestion">
      <van-field
        required
        v-if="$route.query.status == 0"
        v-model="formItem.contractProblemVO.legalComment"
        rows="2"
        :readonly="formItem.sysGtasks.status == 1"
        autosize
        label="法务部处理意见"
        type="textarea"
        maxlength="500"
        placeholder="请输入法务部处理意见,字数限制500字以内"
        show-word-limit
      />
      <van-field
        required
        :readonly="formItem.sysGtasks.status == 1"
        v-if="$route.query.status == 1"
        v-model="formItem.contractProblemVO.disposeComment"
        rows="2"
        autosize
        label="项目组处理意见"
        type="textarea"
        maxlength="500"
        placeholder="请输入项目组处理意见,字数限制500字以内"
        show-word-limit
      />
      <div class="uplod">
        <div>附件</div>
        <van-uploader
          :after-read="afterRead"
          v-model="fileListCurrent"
          :max-size="maxSize * 1024 * 1024"
          @oversize="onOversize"
          :before-delete="beforeDel"
          accept="image/*,video/*"
          :disabled="fileMessage == '上传中' || formItem.sysGtasks.status == 1"
          max-count="5"
        >
          <template v-if="fileMessage == '上传成功'" #preview-cover="{ file }">
            <div class="preview-cover van-ellipsis">上传成功</div>
          </template>
        </van-uploader>
        <div class="upload-tip">注：上传格式图片、视频，单张大小≤50M</div>
      </div>
    </div>
    <div class="table_sty" style="height:130px;">
      <van-field v-if="formItem.sysGtasks.status == 0" v-model="dealingName" label="指定协办人" readonly right-icon="arrow" input-align="left" @click="showDealing = true" />
      <van-field v-else v-model="dealingName" label="指定协办人" readonly right-icon="arrow" input-align="left" />
      <van-popup v-model="showDealing" position="bottom">
        <van-picker value-key="userName" show-toolbar :columns="dealingList" @confirm="onDealing" @cancel="showDealing = false" />
      </van-popup>
      <div class="txt"><i class="public"></i>指定记录</div>
      <el-table :data="formItem.assignRecordList" border stripe>
        <el-table-column prop="createName" label="指定人" align="center"></el-table-column>
        <el-table-column prop="disposeName" label="协办人" align="center"></el-table-column>
        <el-table-column prop="createTime" label="指定时间" align="center"></el-table-column>
      </el-table>
    </div>

    <div v-if="formItem.sysGtasks.status == 0" class="btn"><van-button style="width:90%" color="#3562db" @click="handleSubmit">提交</van-button></div>
  </div>
</template>

<script>
import axios from "axios";
import YBS from "@/assets/utils/utils.js";

export default {
  components: {},
  data() {
    return {
      fileListCurrent: [],
      maxSize: 50, //
      fileMessage: "",
      dealingList: [], //指定协办人
      showDealing: false,
      dealingName: "",
      formItem: {
        gtasksId: "",
        status: 1,
        sysGtasks: {}, //待办对象
        assignRecordList: [], //指定待办记录
        contractProblemVO: {}, //项目前期
        userVOS: []
      },
      legalAttachmentList: [],

      contract: {},
      contractProblem: {},
      contractTypeDict: "",
      contractIssueDict: ""
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    // 获取储存、相机权限
    if (!YBS.hasPermission("storage")) {
      YBS.reqPermission(["storage"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          if (!YBS.hasPermission("camera")) {
            YBS.reqPermission(["camera"], function(ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              }
            });
            return;
          } else {
          }
        }
      });
      return;
    }
    if (!YBS.hasPermission("camera")) {
      YBS.reqPermission(["camera"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
        }
      });
      return;
    }
  },
  mounted() {
    console.log(this.$route.query.status, "wwwwww");
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.loadDict();
    this.getApprovalByType();
  },
  methods: {
    handleSubmit() {
      let self = this;
      if (this.$route.query.status == 0 && this.formItem.contractProblemVO.legalComment == "") return this.$toast.fail("请填写法务部处理意见");
      if (this.$route.query.status == 1 && this.formItem.contractProblemVO.disposeComment == "") return this.$toast.fail("请填写项目组处理意见");
      let params = this.formItem;

      //如果有指定协办人
      if (params.userVOS.length > 0) {
        params.status = 2;
      }
      //修改问题合同状态 为法务已处理
      if (this.$route.query.status == 0) {
        params.contractProblemVO.status = 1;
      } else {
        params.contractProblemVO.status = 2;
      }
      params.type = self.$route.query.approvalType;

      params.contractProblemVO.legalAttachmentList = self.legalAttachmentList;
      self.$api
        .getDisposeContract(params)
        .then(res => {
          this.$toast.success(res);
          this.goBack();
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    afterRead(file2) {
      this.fileMessage = "上传中";
      var fileSize = parseFloat(parseInt(file2.file["size"]) / 1024 / 1024).toFixed(2);
      const { file } = file2;

      file2.status = "uploading";
      file2.message = "上传中...";
      this.handleUploadImg(file, file2);
    },

    handleUploadImg(file, file2) {
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.IMEM_API + "/api/file/uploadFile",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          userId: JSON.parse(localStorage.getItem("loginInfo")).staffId
        }
      }).then(res => {
        if (res.data.code == 0) {
          file2.status = "success";
          file2.message = "上传成功";
          this.fileMessage = "上传成功";
          this.formItem.attachments.push(res.data.data);
        } else {
          file2.status = "failed";
          file2.message = "上传失败";
          this.fileMessage = "上传失败";
        }
      });
    },
    // 删除图片 之前
    beforeDel(file, index) {
      console.log(index, "index");
      // 上传到服务器失败
      this.formItem.attachments.splice(index, 1);
      this.fileListCurrent.splice(index, 1);
    },
    onOversize() {
      this.$toast.fail(`大小不能超过${this.maxSize}M`);
    },
    getByProjectId(id) {
      this.$api.getByProjectId({ id: id }).then(res => {
        this.dealingList = res;
      });
    },
    onDealing(value) {
      this.dealingName = value.userName;
      this.formItem.userVOS.push(value);
      this.showDealing = false;
    },
    loadDict() {
      let self = this;
      //合同类型数据字典
      this.$api.getDict({ type: "system_contract_type" }).then(res => {
        console.log(res, "ssss");
        this.contractTypeDict = res;
      });

      // 问题合同类型
      this.$api.getDict({ type: "sys_contract_issue_type" }).then(res => {
        this.contractIssueDict = res;
      });
    },
    getContractTypeDict(value) {
      return this.selectDictLabel(value, this.contractTypeDict);
    },
    getContractIssueDict(value) {
      return this.selectDictLabel(value, this.contractIssueDict);
    },
    selectDictLabel(value, datas) {
      var actions = [];
      Object.keys(datas).some(key => {
        if (datas[key].dataValue == "" + value) {
          actions.push(datas[key].dataLabel);
          return true;
        }
      });
      return actions.join("");
    },

    getApprovalByType() {
      let self = this;
      this.formItem.id = self.$route.query.sysGtaskId;
      this.formItem.gtasksId = self.$route.query.sysGtaskId;
      this.$api.getApprovalByType({ id: this.$route.query.sysGtaskId }).then(res => {
        self.formItem.sysGtasks = res.sysGtasks;
        self.formItem.contractProblemVO = res.contractProblem;
        self.formItem.assignRecordList = res.assignRecordList;
        self.disposeAttachmentList = res.contractProblem.disposeAttachmentList;
        self.problemAttachments = res.contractProblem.problemAttachments;
        self.contract = res.contract;
        self.getByProjectId(self.formItem.sysGtasks.projectId);
        self.contractProblem = res.contractProblem;
        if (res.contractProblem.problemAttachmentList) {
          self.problemAttachmentList = res.contractProblem.problemAttachmentList;
        }
      });
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    // height: 200px;
    background-color: #fff;
    margin-bottom: 10px;
  }
}
.inspect {
  width: 100%;
  height: 270px;
  background-color: #fff;
  margin-bottom: 10px;
}
.rectify {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
}
.txt {
  display: flex;
  padding: 11px 10px;
  .txt_l {
    width: 150px;
    color: #4e5969;
    font-weight: 300;
  }
}

.btn {
  z-index: 999;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.suggestion {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
  .uplod {
    background-color: #fff;
    line-height: 50px;
    width: 95%;
    margin-bottom: 10px;
    padding: 0 10px;
  }
  > div {
    padding: 5px 15px;
  }
}

.table_sty {
  width: 100%;
  margin-bottom: 120px;

  background-color: #fff;
  .txt {
    display: flex;
    padding: 11px 10px;
  }
}
/deep/ .van-collapse-item__content {
  padding: 12px 0 !important;
}
/deep/ .el-table__header th {
  background-color: #e6effc; /* 设置表头背景颜色 */
  color: #1d2129; /* 设置表头文字颜色 */

  font-weight: 400;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
.upload-tip {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #696969;
  line-height: 20px;
}
.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  height: 20px;
  line-height: 15px;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
