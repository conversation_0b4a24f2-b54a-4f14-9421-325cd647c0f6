<template>
  <div class="inner">
    <Header title="进度批量审批" @backFun="goBack"></Header>
    <div class="essential">
      <div class="txt"><i class="public"></i> 基础信息</div>
      <div class="txt">
        <div class="txt_l">项目名称</div>
        <div>{{ plantasks.length ? plantasks[0].projectName : "" }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">待办编号</div>
        <div>{{ sysGtasks.number }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">待办标题</div>
        <div>{{ sysGtasks.title }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起人</div>
        <div>{{ sysGtasks.createName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起时间</div>
        <div>{{ sysGtasks.createTime }}</div>
      </div>
    </div>

    <div class="essential" v-if="plantasks[0].childs && plantasks[0].childs.length">
      <div v-for="(item, index) in plantasks[0].childs" :key="index">
        <van-collapse v-model="activeNames" accordion>
          <van-collapse-item :title="'任务名称：' + item.name" :name="index + 1">
            <div class="txt">
              <div class="txt_l">当前完成进度</div>
              <div class="txt_r">{{ item.currentPercen + "%" || "" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">计划开始时间</div>
              <div class="txt_l">{{ item.planStartTime || "" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">计划完成时间</div>
              <div class="txt_l">{{ item.planEndTime || "" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">实际开始时间</div>
              <div class="txt_l">{{ item.taskReport.realityStartTime || "" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">本次填报时间</div>
              <div class="txt_l">{{ item.taskReport.endTime || "" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">实际完成时间</div>
              <div class="txt_l">{{ item.taskReport.realityEndTim || "" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">上报任务进度</div>
              <div class="txt_l">{{ item.taskReport.submitPercen + "%" || "" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">累计完成进度</div>
              <div class="txt_l">{{ item.taskReport.currentPercen + "%" || "" }}</div>
            </div>
            <div class="table_sty">
              <el-table :data="item.taskReport.taskStepReportList" border stripe>
                <el-table-column prop="stepName" label="步骤" width="110" align="center"></el-table-column>
                <el-table-column prop="submitPercen" label="本期进度" width="80" align="center"></el-table-column>
                <el-table-column prop="endTime" label="上报时间" width="110" align="center"></el-table-column>
                <el-table-column prop="currentPercen" label="当前完成进度" align="center"></el-table-column>
              </el-table>
              <br />
              <el-table :data="item.taskReport.approvalList" border stripe>
                <el-table-column prop="fileApprovalPersonName" label="审批人" width="110" align="center"></el-table-column>
                <el-table-column prop="comment" label="审批意见" width="80" align="center"></el-table-column>
                <el-table-column prop="approveTime" label="审批时间" width="110" align="center"></el-table-column>
                <el-table-column prop="status" label="审批结果" align="center">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.status == -1 ? "驳回" : scope.row.status == 0 ? "待审批" : scope.row.status == 1 ? "通过" : scope.row.status == 2 ? "指定代办人" : ""
                    }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>
    </div>
    <div
      class="table_sty"
      style="height:130px;
  margin-bottom: 70px;
    "
    >
      <van-field v-if="sysGtasks.status == 0" readonly v-model="dealingName" label="指定协办人" right-icon="arrow" input-align="left" @click="showDealing = true" />
      <van-field v-else readonly v-model="dealingName" label="指定协办人" right-icon="arrow" input-align="left" />
      <van-popup v-model="showDealing" position="bottom">
        <van-picker value-key="userName" show-toolbar :columns="dealingList" @confirm="onDealing" @cancel="showDealing = false" />
      </van-popup>
      <div class="txt"><i class="public"></i>指定记录</div>
      <el-table :data="assignRecordList" border stripe>
        <el-table-column prop="createName" label="指定人" align="center"></el-table-column>
        <el-table-column prop="disposeName" label="协办人" align="center"></el-table-column>
        <el-table-column prop="createTime" label="指定时间" align="center"></el-table-column>
      </el-table>
    </div>
    <div class="suggestion">
      <van-field
        required
        v-model="approval.comment"
        rows="3"
        autosize
        :readonly="sysGtasks.status == 1"
        label="审批意见"
        type="textarea"
        maxlength="500"
        placeholder="请输入审批意见,字数限制500字以内"
        show-word-limit
      />
    </div>

    <div class="btn" v-if="sysGtasks.status == 0">
      <van-button style="width:40%" color="#3562db" @click="handleSubmit(-1)">驳回</van-button
      ><van-button style="width:40%" color="#3562db" @click="handleSubmit(1)">通过</van-button>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      dealingName: "",
      showDealing: false,
      dealingList: [], //指定协办人
      activeNames: [],

      sysGtasks: {}, //待办对象
      plantasks: [],
      approval: {
        userVOS: []
      }, //审批记录
      assignRecordList: [],

      tableData: [],
      tel: "",
      active: "0"
    };
  },
  created() {
    this.getApprovalByType();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    handleSubmit(status) {
      let self = this;

      var params = this.approval;
      if (params.userVOS && params.userVOS.length > 0) {
        params.status = 2;
      } else {
        params.status = status;
      }
      if (params.comment == "") return this.$toast.fail("请填写审批意见");
      this.$api
        .getAppr(params)
        .then(res => {
          this.$toast.success(res);
          this.goBack();
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    getApprovalByType() {
      let self = this;
      this.$api.getApprovalByType({ id: this.$route.query.sysGtaskId }).then(res => {
        console.log(res, "ssssssssssss");
        this.plantasks = res.plantasks;
        self.sysGtasks = res.sysGtasks;
        self.approval = res.approval || { comment: "", userVOS: [] };
        this.getByProjectId(self.approval.projectId);
        //   self.taskReport = res.data.taskReport;
        //   self.planTask = res.data.taskReport.planTask;
        self.assignRecordList = res.assignRecordList || [];
      });
    },
    getByProjectId(id) {
      this.$api.getByProjectId({ id: id }).then(res => {
        this.dealingList = res;
      });
    },
    onDealing(value) {
      this.dealingName = value.userName;
      this.approval.userVOS = [];
      this.approval.userVOS.push(value);
      this.showDealing = false;
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    // height: 200px;
    background-color: #fff;
    margin-bottom: 10px;
  }
}
.appear {
  width: 100%;
  height: 128px;
  background-color: #fff;
  margin-bottom: 10px;
}
.txt {
  display: flex;
  padding: 11px 10px;
  .txt_l {
    width: 150px;
    color: #4e5969;
    font-weight: 300;
  }
  .txt_r {
    color: #1d2129;
    font-weight: 400;
  }
}
.table_sty {
  width: 100%;

  background-color: #fff;
  .txt {
    display: flex;
    padding: 11px 10px;
  }
}
.btn {
  z-index: 999;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.suggestion {
  width: 100%;
  background-color: #fff;
  margin-bottom: 60px;

  > div {
    padding: 5px 15px;
  }
}
/deep/ .van-collapse-item__content {
  padding: 12px 0 !important;
}
/deep/ .el-table__header th {
  background-color: #e6effc; /* 设置表头背景颜色 */
  color: #1d2129; /* 设置表头文字颜色 */
  font-weight: 400;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
</style>
