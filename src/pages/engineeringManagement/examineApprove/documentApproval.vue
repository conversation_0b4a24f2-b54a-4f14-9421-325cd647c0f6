<template>
  <div class="inner">
    <Header title="文件审批" @backFun="goBack"></Header>
    <div class="essential">
      <div class="txt"><i class="public"></i>基础信息</div>
      <div class="txt">
        <div class="txt_l">待办编号</div>
        <div>{{ formItem.sysGtasks.number }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">待办标题</div>
        <div>{{ formItem.sysGtasks.title }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起人</div>
        <div>{{ formItem.sysGtasks.createName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">发起时间</div>
        <div>{{ formItem.sysGtasks.createTime }}</div>
      </div>
    </div>
    <div class="hTphotos">
      <van-tabs v-model="active">
        <van-tab title="最新文件" name="0">
          <div class="download" v-if="formItem.projectGuidel.attachments && formItem.projectGuidel.attachments.length">
            <div
              style=" display: flex;
    justify-content: space-between;"
              v-for="(i, index) in formItem.projectGuidel.attachments"
              :key="index"
            >
              <div>{{ i.fileName }}</div>
              <div style="color:#3562db" @click="dload(i)">下载</div>
            </div>
          </div>
        </van-tab>
        <van-tab title="历史文件" name="1">
          <div class="download" v-if="formItem.projectGuidel.historyAttachmentsList &&formItem.projectGuidel.historyAttachmentsList.length">
            <div
              style=" display: flex;
    justify-content: space-between;"
              v-for="(h_item, index) in formItem.projectGuidel.historyAttachmentsList"
              :key="index"
            ></div>
            <div>{{ h_item.fileName }}</div>
            <div @click="dload(i)">下载</div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    <div class="suggestion">
      <van-field
        :readonly="formItem.sysGtasks.status == 1"
        required
        v-model="formItem.comment"
        rows="2"
        autosize
        label="审批意见"
        type="textarea"
        maxlength="500"
        placeholder="请输入评审审批意见,字数限制500字以内"
        show-word-limit
      />
      <div class="uplod">
        <div>附件</div>
        <van-uploader
          :after-read="afterRead"
          v-model="fileListCurrent"
          :max-size="maxSize * 1024 * 1024"
          @oversize="onOversize"
          :before-delete="beforeDel"
          accept="image/*,video/*"
          :disabled="fileMessage == '上传中' || formItem.sysGtasks.status == 1"
          max-count="5"
        >
          <template v-if="fileMessage == '上传成功'" #preview-cover="{ file }">
            <div class="preview-cover van-ellipsis">上传成功</div>
          </template>
        </van-uploader>
        <div class="upload-tip">注：上传格式图片、视频，单张大小≤50M</div>
      </div>
    </div>
    <div class="table_sty" style="height:130px">
      <van-field v-if="formItem.sysGtasks.status == 0" readonly v-model="dealingName" right-icon="arrow" input-align="left" @click="showDealing = true" label="指定协办人" />
      <van-field v-else readonly v-model="dealingName" right-icon="arrow" input-align="left" label="指定协办人" />
      <van-popup v-model="showDealing" position="bottom">
        <van-picker value-key="userName" show-toolbar :columns="dealingList" @confirm="onDealing" @cancel="showDealing = false" />
      </van-popup>
      <div class="txt"><i class="public"></i>指定记录</div>
      <el-table :data="formItem.assignRecordList" border stripe>
        <el-table-column prop="createName" label="指定人" align="center"></el-table-column>
        <el-table-column prop="disposeName" label="协办人" align="center"></el-table-column>
        <el-table-column prop="createTime" label="指定时间" align="center"></el-table-column>
      </el-table>
    </div>

    <div class="btn" v-if="formItem.sysGtasks.status == 0">
      <van-button style="width:40%" color="#3562db" @click="handleSubmit(-1)">驳回</van-button
      ><van-button style="width:40%" color="#3562db" @click="handleSubmit(1)">通过</van-button>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Vue from "vue";
import { Dialog } from "vant";
import YBS from "@/assets/utils/utils.js";
import { ActionSheet } from "vant";
Vue.use(Dialog);

Vue.use(ActionSheet);
export default {
  components: {},
  data() {
    return {
      fileListCurrent: [],
      maxSize: 50, //
      fileMessage: "",
      dealingName: "",
      dealingList: [], //指定协办人
      showDealing: false,
      files: [],
      accept: "",
      attachmentUrl: [], // 图片地址

      uploadShow: false,

      formItem: {
        id: "", //主键
        sysGtasks: {}, //待办对象
        approval: {}, //审批记录
        assignRecordVOS: [], //指定待办记录
        projectGuidel: {}, //项目前期
        userVOS: [],
        comment: "" //审批意见
      },
      tableData: [],
      tel: "",
      active: "0",
      uploadInstance: ""
    };
  },
  created() {
    // 获取储存、相机权限
    if (!YBS.hasPermission("storage")) {
      YBS.reqPermission(["storage"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          if (!YBS.hasPermission("camera")) {
            YBS.reqPermission(["camera"], function(ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              }
            });
            return;
          } else {
          }
        }
      });
      return;
    }
    if (!YBS.hasPermission("camera")) {
      YBS.reqPermission(["camera"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
        }
      });
      return;
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.uploadInstance = this.$refs.upload;
    this.getApprovalByType();
  },
  methods: {
    dload(item) {
      api.download(
        {
          url: this.$YBS.imgUrlTranslation("imem/" + item.fileUrl),
          savePath: "fs://" + item.fileName,
          report: true,
          cache: true,
          allowResume: true
        },
        function(ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", ret);
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {});

            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },

    handleSubmit(status) {
      let self = this;
      if (this.formItem.comment == "") return this.$toast.fail("请填写审批意见");
      if (this.formItem.approval.userVOS && this.formItem.approval.userVOS.length > 0) {
        this.ruleValidate = {};
      }
      self.formItem.approval.comment = self.formItem.comment;
      var params = this.formItem.approval;
      params.status = status;
      if (params.userVOS && params.userVOS.length > 0) {
        params.status = 2;
      }
      this.$api
        .getAppr(params)
        .then(res => {
          console.log(res, "ssssssss");
          this.$toast.success(res);
          this.goBack();
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    afterRead(file2) {
      this.fileMessage = "上传中";
      var fileSize = parseFloat(parseInt(file2.file["size"]) / 1024 / 1024).toFixed(2);
      const { file } = file2;

      file2.status = "uploading";
      file2.message = "上传中...";
      this.handleUploadImg(file, file2);
    },

    handleUploadImg(file, file2) {
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.IMEM_API + "/api/file/uploadFile",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          userId: JSON.parse(localStorage.getItem("loginInfo")).staffId
        }
      }).then(res => {
        if (res.data.code == 0) {
          file2.status = "success";
          file2.message = "上传成功";
          this.fileMessage = "上传成功";
          this.legalAttachmentList.push(res.data.data);
        } else {
          file2.status = "failed";
          file2.message = "上传失败";
          this.fileMessage = "上传失败";
        }
      });
    },
    // 删除图片 之前
    beforeDel(file, index) {
      console.log(index, "index");
      // 上传到服务器失败
      this.legalAttachmentList.splice(index, 1);
      this.fileListCurrent.splice(index, 1);
    },
    onOversize() {
      this.$toast.fail(`大小不能超过${this.maxSize}M`);
    },
    getApprovalByType() {
      let self = this;
      this.formItem.id = this.$route.query.sysGtaskId;
      this.$api
        .getApprovalByType({ id: this.$route.query.sysGtaskId })
        .then(res => {
          self.approvalList = res.approvalList;
          self.formItem.approval = res.approval;
          this.getByProjectId(self.formItem.approval.projectId);
          if (self.formItem.approval) {
            self.formItem.comment = self.formItem.approval.comment;
          }
          //设置旧的状态，以控制保存按钮是否可用
          self.formItem.approval.oldStatus = this.formItem.approval.status;
          self.formItem.assignRecordList = res.assignRecordList;
          self.formItem.projectGuidel = res.projectGuidel;
          self.formItem.sysGtasks = res.sysGtasks;
          if (res.fileFinalSubmission) {
            self.fileFinalSubmission = res.fileFinalSubmission;
          }
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    onDealing(value) {
      this.dealingName = value.userName;
      this.formItem.userVOS.push(value);
      this.showDealing = false;
    },
    getByProjectId(id) {
      this.$api.getByProjectId({ id: id }).then(res => {
        this.dealingList = res;
      });
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.hTphotos {
  width: 100%;
  padding-bottom: 5px;
  background-color: #fff;
  margin-bottom: 10px;
  .download {
    width: 95%;
    padding: 10px;
    margin: 0 auto 10px;
  }
}
.table_sty {
  width: 100%;
  margin-bottom: 119px;
  background-color: #fff;
  .txt {
    display: flex;
    padding: 11px 10px;
  }
}
.btn {
  z-index: 999;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.suggestion {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
  > div {
    padding: 5px 15px;
    font-size: 15px;
  }
  .uplod {
    background-color: #fff;
    line-height: 50px;
    width: 95%;
    margin-bottom: 10px;
    padding: 0 10px;
  }
}
.box {
  width: 100%;
  text-align: center;
  line-height: 40px;
}
/deep/ .el-table__header th {
  background-color: #e6effc; /* 设置表头背景颜色 */
  color: #1d2129; /* 设置表头文字颜色 */

  font-weight: 400;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
.upload-tip {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #696969;
  line-height: 20px;
}
.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  height: 20px;
  line-height: 15px;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
