<template>
  <div class="inner">
    <Header title="审批" @backFun="goBack"></Header>
    <div v-if="list.length > 0" class="list">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in list" :key="item.id" class="listStyle" @click="skipList(item)">
              <div class="name">
                <div class="left"><span style="color:#3562DB">工程项目</span><span style="padding:0 5px">|</span>{{ item.approvalName }}</div>
                <div><van-icon name="arrow" /></div>
              </div>
              <div class="txt">
                <div class="txt_l" style="width:100%">{{ item.createTime }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">发起人</div>
                <div>{{ item.initiateUserName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">编号</div>
                <div>{{ item.number }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      page: 1,
      limit: 15,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getExamineApproveList();
  },
  methods: {
    getExamineApproveList() {
      let data = {
        page: this.page,
        limit: this.limit,
        status: 0
      };
      this.$api
        .examineApproveList(data)
        .then(res => {
          this.loading = false;
          res.dataList.forEach(item => {
            this.list.push(item);
          });
          if (this.list.length >= res.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        })
        .catch(res => {
          console.log(res, "res");
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getExamineApproveList();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getExamineApproveList();
    },
    skipList(item) {
      if (item.approvalType == 1) {
        // 文件审批
        this.$router.push({
          path: "/documentApproval",
          query: {
            sysGtaskId: item.sysGtaskId
          }
        });
      } else if (item.approvalType == 3) {
        // 问题合同
        this.$router.push({
          path: "/problemContract",
          query: {
            approvalType: item.approvalType,
            sysGtaskId: item.sysGtaskId,
            status: item.businessObject.status
          }
        });
      } else if (item.approvalType == 4) {
        // 收付款填写
        this.$router.push({
          path: "/receiptPayment",
          query: {
            sysGtaskId: item.sysGtaskId
          }
        });
      } else if (item.approvalType == 5) {
        // 质量整改
        this.$router.push({
          path: "/qualityControl",
          query: {
            sysGtaskId: item.sysGtaskId
          }
        });
      } else if (item.approvalType == 6) {
        // 质量整改审批
        this.$router.push({
          path: "/qualityRectification",
          query: {
            sysGtaskId: item.sysGtaskId
          }
        });
      } else if (item.approvalType == 10) {
        //预收付款
        this.$router.push({
          path: "/advancePayment",
          query: {
            sysGtaskId: item.sysGtaskId
          }
        });
      } else if (item.approvalType == 12) {
        //竣工验收
        this.$router.push({
          path: "/finalAcceptance",
          query: {
            sysGtaskId: item.sysGtaskId
          }
        });
      } else if (item.approvalType == 13) {
        // 竣工验收审批
        this.$router.push({
          path: "/finalAcceptanceApproval",
          query: {
            sysGtaskId: item.sysGtaskId
          }
        });
      } else if (item.approvalType == 21) {
        //  进度批量审批
        this.$router.push({
          path: "/batchApprovalProgress",
          query: {
            sysGtaskId: item.sysGtaskId
          }
        });
      }
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  height: 100vh;
  overflow: hidden;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 100px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.list {
  height: calc(100% - 80px);
  overflow: auto;
}
.notList {
  position: relative;
  height: calc(100% - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
