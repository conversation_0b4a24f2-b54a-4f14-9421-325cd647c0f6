<template>
  <div class="inner">
    <Header title="项目报表" @backFun="goBack"></Header>

    <div style="width:100%">
      <div class="listStyle">
        <div class="txt"><i class="public"></i>工程进度</div>
        <div class="circle">
          <div>
            <van-circle v-model="currentRate" :rate="form.planProgress || 0" :speed="100" :text="text" layer-color="#F7F8FA" color="#3562DB" stroke-width="90" />
            <div>
              预计完成比例
            </div>
          </div>
          <div>
            <van-circle v-model="currentRate" :rate="form.actualProgress || 0" :speed="100" :text="text" layer-color="#F7F8FA" color="#FF7D00" stroke-width="90" />
            <div>
              实际完成比例
            </div>
          </div>
        </div>
        <div class="txt">
          <div class="txt_l">计划开始时间</div>
          <div>{{ form.startDate }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">计划结束时间</div>
          <div>{{ form.planEndTime }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">实际开始时间</div>
          <div>{{ form.actualBeginTime }}</div>
        </div>

        <div class="txt">
          <div class="txt_l">上报截至时间</div>
          <div>{{ form.reportTime }}</div>
        </div>

        <div class="statistics">
          <div>
            <div class="value">{{ form.constructionPeriod || 0 }}</div>
            <div class="value">{{ form.beforeDay || 0 }}</div>
            <div class="value">{{ form.afterDay || 0 }}</div>
          </div>
          <div>
            <div class="lable">计划工期</div>
            <div class="lable">提前天数</div>
            <div class="lable">滞后天数</div>
          </div>
        </div>
        <div class="unit">单位：天</div>
      </div>

      <div class="skSty">
        <div class="title">应收账款</div>

        <div class="statistics">
          <div>
            <div class="value" style="color:#3562DB">{{ form.planIncomeMoney || 0 }}</div>
            <div class="value" style="color:#FF7D00">{{ form.actualIncomeMoney || 0 }}</div>
            <div class="value" style="color:#F53F3F">{{form.uncollectedMoney||0}}</div>
          </div>
          <div>
            <div class="lable">应收总额</div>
            <div class="lable">实收总额</div>
            <div class="lable">未收总额</div>
          </div>
        </div>
        <div class="unit">单位：万元</div>
        <div id="chargeStatistics" class="echarts"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      currentRate: 0,
      echartsObj: {
        x: [200, 150, 50],
        y: ["应收总额", "实收总额", "未收总额"]
      }
    };
  },
  computed: {
    text() {
      return this.currentRate.toFixed(0) + "%";
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getStatements();

  },
  methods: {
    getStatements() {
      this.$api.getStatements({ projectId: this.$route.query.id }).then(res => {
        this.form = res;
        this.echartsObj.x=res.xmoneyName
        this.echartsObj.y=res.ymoneyValue
  this.$nextTick(() => {
      this.initChargeStatistics(this.form);
    });
      });
    },

    initChargeStatistics(data) {
      console.log(data,'aaaaaaaaaa');
      const getchart = this.$echarts.init(document.getElementById("chargeStatistics"));
      let colors = ["#3562DB", "#FF7D00", "#F53F3F"];
      getchart.setOption({
        color: colors,
        tooltip: {
          trigger: "axis",
          // axisPointer: {
          //   type: "cross"
          // },
          textStyle: {
            // 文字提示样式
            color: "#000000",
            fontSize: "13"
          },
          backgroundColor: "#fff"
        },
        grid: {
          right: "15%",
          left: "12%"
        },

        xAxis: [
          {
            type: "category",
            axisLine: {
              lineStyle: {
                color: "#86909C"
              }
            },

            axisTick: {
              show: false,

              alignWithLabel: true
            },
            data: data.xmoneyName
          }
        ],
        yAxis: [
          {
            axisLine: {
              show: false
            }
          },
          {
            type: "value",
            name: "",
            min: 0,
            max: Math.max.apply(null, data.ymoneyValue),
            position: "left",
            axisLine: {
              show: false,
              lineStyle: {
                color: "#414653"
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: "bar",
            yAxisIndex: 1,
            barWidth: 12,
            data: data.ymoneyValue,
            itemStyle: {
              borderRadius: 2
            }
          }
        ]
      });
    },
    skipList() {
      this.$router.push({
        path: "/hiddenTroubleDetails"
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  .listStyle {
    width: 100%;

    height: 410px;
    background-color: #fff;
    margin-bottom: 10px;

    .name {
      padding: 10px 10px 5px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.statistics {
  width: 95%;
  height: 77px;
  background-color: #f7f8fa;
  margin: 0 auto;
  border-radius: 5px;
  > div {
    width: 100%;
    height: 50%;
    display: flex;
    > div {
      flex: 1;
      text-align: center;
      line-height: 39px;
    }
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #1d2129;
    }
    .lable {
      color: #86909c;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.skSty {
  height: 450px;
  background-color: #fff;
  width: 100%;
  .title {
    padding: 10px;
  }
  .detailsStyle {
    width: 100%;
    height: 80px;
    > div {
      width: 100%;
      height: 50%;
      display: flex;
      > div {
        flex: 1;
        text-align: center;
        line-height: 39px;
      }
      .value {
        font-size: 18px;
        font-weight: bold;
        color: #1d2129;
      }
      .lable {
        color: #86909c;
        font-weight: 400;
        font-size: 14px;
      }
    }
  }
}
.circle {
  display: flex;
  > div {
    flex: 1;
    text-align: center;
  }
}
.unit {
  font-size: 14px;
  width: 95%;
  margin-top: 5px;
  line-height: 20px;
  font-weight: 400;
  color: #c9cdd4;
  text-align: right;
}
.echarts {
  width: 100%;
  height: 300px;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
</style>
