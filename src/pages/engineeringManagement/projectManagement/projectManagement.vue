<template>
  <div class="inner">
    <Header title="项目管理" @backFun="goBack"></Header>
    <div v-if="list.length > 0" class="list">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in list" :key="item.id" class="listStyle" @click="skipList(item)">
              <div class="name">
                <div class="left"><img :src="img" alt="" />{{ item.name }}</div>
                <div>
                  {{
                    item.status == "approved"
                      ? "已立项"
                      : item.status == "inprogress"
                      ? "进行中"
                      : item.status == "completed"
                      ? "已完成"
                      : item.status == "termination"
                      ? "终止"
                      : ""
                  }}
                </div>
                <div><van-icon name="arrow" /></div>
              </div>
              <div class="txt">
                <div class="txt_l">项目经理</div>
                <div>{{ item.managerName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">管理模式</div>
                <div>{{ item.managementModel == 0 ? "外部项目" : "内部项目" }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">计划开始时间</div>
                <div>{{ item.startDate }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">计划结束时间</div>
                <div>{{ item.endDate }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">项目进度</div>
                <div style="width: calc(100% - 150px);">
                  <van-progress color="#3562DB" :percentage="item.actualProgress || 0" />
                </div>
              </div>
              <div class="statistics">
                <div>
                  <div class="value">{{ item.proxBudget || 0 }}</div>
                  <div class="value">{{ item.expectedProfit || 0 }}</div>
                  <div class="value">{{ item.planIncomeMoney || 0 }}</div>
                  <div class="value">{{ item.uncollectedMoney || 0 }}</div>
                </div>
                <div>
                  <div class="lable">预算</div>
                  <div class="lable">预期利润</div>
                  <div class="lable">应收总额</div>
                  <div class="lable">未收总额</div>
                </div>
              </div>
              <div class="unit">单位：万元</div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import img from "@/assets/images/Frame 427318804.png";

export default {
  data() {
    return {
      list: [],
      page: 1,
      limit: 10,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      img
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getProjectList();
  },
  methods: {
    getProjectList() {
      let data = {
        page: this.page,
        limit: this.limit
      };
      this.$api
        .getProjectList(data)
        .then(res => {
          this.loading = false;
          res.list.forEach(item => {
            this.list.push(item);
          });
          if (this.list.length >= res.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getProjectList();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getProjectList();
    },
    skipList(row) {
      this.$router.push({
        path: "/projectDetails",
        query: {
          id: row.id
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  height: 100vh;
  overflow: hidden;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    .name {
      padding: 10px 10px 5px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.list {
  height: calc(100% - 80px);
  overflow: auto;
}
.statistics {
  width: 95%;
  height: 77px;
  background-color: #f7f8fa;
  margin: 0 auto;
  border-radius: 5px;
  > div {
    width: 100%;
    height: 50%;
    display: flex;
    > div {
      flex: 1;
      text-align: center;
      line-height: 39px;
    }
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #1d2129;
    }
    .lable {
      color: #86909c;
      font-weight: 400;
      font-size: 14px;
    }
  }
}
.unit {
  font-size: 14px;
  width: 95%;
  line-height: 20px;
  font-weight: 400;
  color: #c9cdd4;
  text-align: right;
}
img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}
.notList {
  position: relative;
  height: calc(100vh - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
