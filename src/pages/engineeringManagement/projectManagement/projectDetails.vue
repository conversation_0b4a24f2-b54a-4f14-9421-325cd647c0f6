<template>
  <div class="inner">
    <Header title="项目详情" @backFun="goBack"></Header>

    <div style="width:100%">
      <div class="listStyle">
        <div class="txt"><i class="public"></i>基础信息</div>

        <div class="txt">
          <div class="txt_l">项目名称</div>
          <div>{{ form.name }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">所属分公司</div>
          <div>{{ form.branchCompanyName }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">所属项目组</div>
          <div>{{ form.projectTeamName }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">项目进度</div>
          <div style="width: calc(100% - 150px);">
            <van-progress color="#3562DB" :percentage="form.actualProgress || 0" />
          </div>
        </div>
        <div class="txt">
          <div class="txt_l">计划开始时间</div>
          <div>{{ form.startDate }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">计划结束时间</div>
          <div>{{ form.endDate }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">项目地址</div>
          <div>{{ form.address }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">管理模式</div>
          <div>{{ form.managementModel == 0 ? "外部项目" : "内部项目" }}</div>
        </div>
        <div class="statistics">
          <div>
            <div class="value">{{ form.proxBudget || 0 }} <span class="lable">万元</span></div>
            <div class="value">{{ form.expectedProfit || 0 }}<span class="lable">万元</span></div>
            <div class="value">{{ form.beforeDay || 0 }}<span class="lable">天</span></div>
            <div class="value">{{ form.afterDay || 0 }}<span class="lable">天</span></div>
          </div>
          <div>
            <div class="lable">预算</div>
            <div class="lable">预期利润</div>
            <div class="lable">提前天数</div>
            <div class="lable">滞后天数</div>
          </div>
        </div>
      </div>

      <div class="skSty">
        <div class="title">收账款信息</div>
        <div class="detailsStyle">
          <div>
            <div class="value">{{ form.planIncomeMoney || 0 }} <span class="lable">万元</span></div>
            <div class="value">{{ form.actualMoney || 0 }} <span class="lable">万元</span></div>
            <div class="value">{{ form.uncollectedMoney || 0 }} <span class="lable">万元</span></div>
          </div>
          <div>
            <div class="lable">应收总额</div>
            <div class="lable">实收总额</div>
            <div class="lable">未收总额</div>
          </div>
        </div>
      </div>
    </div>
    <div class="btn"><van-button style="width:90%" color="#3562db" @click="skipList">查看报表</van-button></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {}
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getProjecDetails();
  },
  methods: {
    getProjecDetails() {
      this.$api.getProjecDetails({ id: this.$route.query.id }).then(res => {
        this.form = res;
      });
    },
    skipList() {
      this.$router.push({
        path: "/projectReport",
        query: {
          id: this.$route.query.id
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  .listStyle {
    width: 100%;

    height: 410px;
    background-color: #fff;
    margin-bottom: 10px;

    .name {
      padding: 10px 10px 5px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .txt {
      display: flex;
      padding: 10px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.statistics {
  width: 95%;
  height: 77px;
  background-color: #f7f8fa;
  margin: 0 auto;
  border-radius: 5px;
  > div {
    width: 100%;
    height: 50%;
    display: flex;
    > div {
      flex: 1;
      text-align: center;
      line-height: 39px;
    }
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #1d2129;
    }
    .lable {
      color: #86909c;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.skSty {
  height: 120px;
  background-color: #fff;
  width: 100%;
  .title {
    padding: 10px;
  }
  .detailsStyle {
    width: 100%;
    height: 80px;
    > div {
      width: 100%;
      height: 50%;
      display: flex;
      > div {
        flex: 1;
        text-align: center;
        line-height: 39px;
      }
      .value {
        font-size: 18px;
        font-weight: bold;
        color: #1d2129;
      }
      .lable {
        color: #86909c;
        font-weight: 400;
        font-size: 14px;
      }
    }
  }
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}

.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
</style>
