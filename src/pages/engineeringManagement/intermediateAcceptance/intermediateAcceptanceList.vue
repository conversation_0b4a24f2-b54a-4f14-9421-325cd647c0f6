<template>
  <div class="inner">
    <Header title="中间验收" @backFun="goBack">
      <div class="topSelest" @click="add">
        <van-icon name="plus" />
        <p>添加</p>
      </div>
    </Header>
    <div v-if="list.length > 0" class="list">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in list" :key="item.id" class="listStyle">
              <div class="name" @click="details(item)">
                <div class="left">
                  <img :src="img" alt="" />
                  <div>
                    <div>{{ item.projectName }}</div>
                    <div style="font-size: 13px;">
                      {{ item.acceptanceNumber }}
                    </div>
                  </div>
                </div>
                <div><van-icon name="arrow" /></div>
              </div>
              <div class="time">
                {{ item.createTime }}
              </div>
              <div class="txt">
                <div class="txt_l">交出单位</div>
                <div>{{ item.handOverUnit }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">接收单位</div>
                <div>{{ item.receivingUnit }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">交出日期</div>
                <div>{{ item.dateHandover }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">接收日期</div>
                <div>{{ item.dateReceivingUnit }}</div>
              </div>
              <div class="btn">
                <div @click="edit(item)">编辑</div>
                <div @click="strikeOut(item)">删除</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import { Dialog } from "vant";
import img from "@/assets/images/zjys.png";

export default {
  data() {
    return {
      img,
      list: [],
      page: 1,
      limit: 10,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getIntermedList();
  },
  methods: {
    getIntermedList() {
      this.$api.getIntermedList({ projectId: this.$route.query.id }).then(res => {
        this.loading = false;
        res.dataList.forEach(item => {
          this.list.push(item);
        });
        if (this.list.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getIntermedList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getIntermedList();
    },
    add() {
      this.$router.push({
        path: "/intermediateAcceptanceAddandAdd"
      });
    },
    edit(row) {
      this.$router.push({
        path: "/intermediateAcceptanceAddandEdit",
        query: {
          id: row.id
        }
      });
    },

    details(row) {
      this.$router.push({
        path: "/intermediateAcceptanceAddandDetails",
        query: {
          id: row.id
        }
      });
    },
    strikeOut(row) {
      Dialog.confirm({
        title: "",
        message: "是否删除该信息"
      })
        .then(() => {
          // on confirm
          this.$api
            .getAcceptanceDelete({ id: row.id })
            .then(res => {
              this.$toast.success(res);
              this.page = 1;
              this.list = [];
              this.getIntermedList();
            })
            .catch(res => {
              if (res.data.code == "500") {
                this.$toast.fail(res.data.msg);
              }
            });
        })
        .catch(() => {
          // on cancel
        });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  height: 100vh;
  overflow: hidden;

  .topSelest {
    display: flex;
    align-items: center;
    color: #3562db;
  }
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    .name {
      height: 40px;
      line-height: 20px;
      padding: 10px 10px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        display: flex;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .btn {
      margin-top: 10px;
      display: flex;
      border-top: 1px solid #e5e6eb;
      > div {
        flex: 1;
        text-align: center;
        line-height: 40px;

        font-size: 14px;
        color: #86909c;
        font-weight: 400;
      }
    }
    .time {
      padding-left: 10px;
      line-height: 17px;
      color: #86909c;
      font-weight: 400;
      font-size: 12px;
    }
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
img {
  width: 40px;
  height: 40px;
  margin-right: 5px;
}
.list {
  height: calc(100% - 80px);
  overflow: auto;
}
.notList {
  position: relative;
  height: calc(100vh - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
