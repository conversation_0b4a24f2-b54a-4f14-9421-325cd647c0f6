<template>
  <div class="inner">
    <Header title="选择组织单位/部门" @backFun="goBack">
      <div class="topSelest" v-if="JSON.stringify(rowInfo) != '{}'">
        <p @click="confirm">确定</p>
      </div></Header
    >
    <div style="width:100%;overflow-y:scroll;">
      <el-table
        height="calc(100vh - 80px)"
        :data="options"
        style="width: 100%;margin-bottom: 20px;"
        row-key="id"
        border
        :tree-props="{ children: 'child', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="机构名">
          <template slot-scope="scope">
            <el-radio :label="scope.row.name" v-model="radio" @change.native="handleSelectionChange(scope.$index, scope.row)"></el-radio>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      radio: "",
      rowInfo: {},

      options: [],
      defaultProps: {
        children: "child",
        label: "name"
      }
    };
  },
  created() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getDepartmentList();
  },
  methods: {
    confirm() {
      this.$router.push({
        path: "/intermediateAcceptanceAddandEdit",
        query: {
          rowInfo: this.rowInfo
        }
      });
    },
    handleSelectionChange(index, row) {
      console.log(row, "www");
      this.rowInfo = row;
    },

    handleNodeClick() {},
    getDepartmentList() {
      this.$api.getDepartmentList({}).then(res => {
        this.options = res;
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #fff;
  min-height: 100vh;
  .topSelest {
    display: flex;
    align-items: center;
    color: #3562db;
  }
}
</style>
