<template>
  <div class="inner">
    <Header title="中间验收编辑" @backFun="goBack"></Header>
<van-form @submit="onSubmit">
    <div style="width:100%;overflow-y:scroll;">
      <div class="listStyle">
        <van-field required :rules="[{ required: true }]"  v-model="formItem.projectName" placeholder="请输入项目名称" label="项目名称" readonly right-icon="arrow" input-align="left" @click="showDealing = true" />
        <van-popup v-model="showDealing" position="bottom">
          <van-picker value-key="name" show-toolbar :columns="projectList" @confirm="onDealing" @cancel="showDealing = false" />
        </van-popup>
        <!-- <van-field v-model="formItem.branchCompanyName" placeholder="请选择组织单位/部门" label="组织单位/部门" readonly right-icon="arrow" input-align="left" @click="showdept" /> -->
        <van-field
          v-model="formItem.branchCompanyName"
          placeholder="请选择组织单位/部门"
          label="组织单位/部门"
          readonly
          @click="show = true"
          right-icon="arrow"
          input-align="left"
        />
        <van-popup v-model="show" position="bottom" @close="deptClose">
          <el-table
            height="calc(50vh - 80px)"
            :data="options"
            style="width: 100%;margin-bottom: 20px;"
            row-key="id"
            border
            :tree-props="{ children: 'child', hasChildren: 'hasChildren' }"
          >
            <el-table-column prop="name" label="机构名">
              <template slot-scope="scope">
                <el-radio :label="scope.row.name" v-model="radio2" @change.native="handleSelectionChange(scope.$index, scope.row)"></el-radio>
              </template>
            </el-table-column>
          </el-table>
        </van-popup>
        <van-field v-model="formItem.unitProjectName" placeholder="请输入单位工程名称" label="单位工程名称" />
        <van-field v-model="formItem.engineeringtName" placeholder="请输入工程名称" label="工程名称" />
        <van-field required :rules="[{ required: true }]"  v-model="formItem.receivingUnit" placeholder="请输入接收单位" label="接收单位" />
        <van-field required :rules="[{ required: true }]"  v-model="formItem.handOverUnit" placeholder="请输入交出单位" label="交出单位" />
        <van-field required :rules="[{ required: true }]"  v-model="formItem.contentsInstructions" rows="1" autosize label="交出内容及说明" type="textarea" placeholder="请输入交出内容及说明" />
        <van-field required :rules="[{ required: true }]"  v-model="formItem.opinionsReceivingUnit" placeholder="请输入接收单位意见" label="接收单位意见" />
      </div>

      <div class="skSty">
        <div class="title">接收单位</div>

        <div class="detailsStyle">
          <van-field v-model="formItem.professionalEngineerReceivingUnit" placeholder="请输入工专业工程师" label="专业工程师" />
          <van-field v-model="formItem.personChargeReceivingUnit" placeholder="请输入负责人" label="负责人" />
          <van-field v-model="formItem.dateReceivingUnit" placeholder="请选择日期" label="日期" @click="showBeginDate" right-icon="arrow" input-align="left" readonly />
          <van-popup v-model="showPickerDate" position="bottom">
            <van-datetime-picker v-model="currentDate" type="date" @cancel="onCancelDate" @confirm="onConfirmDate" :min-date="minDate" :max-date="maxDate" :formatter="formatter" />
          </van-popup>
        </div>
      </div>
      <div class="skSty">
        <div class="title">组织交接单位/部门</div>

        <div class="detailsStyle">
          <van-field v-model="formItem.professionalEngineerOrganizationHandoverUni" placeholder="请输入工专业工程师" label="专业工程师" />
          <van-field v-model="formItem.responsiblePersonOrganizationHandoverUnit" placeholder="请输入负责人" label="负责人" />
          <van-field v-model="formItem.organizationHandoverUnitDate" placeholder="请选择日期" label="日期" @click="showBeginDate2" right-icon="arrow" input-align="left" readonly />
          <van-popup v-model="showPickerDate2" position="bottom">
            <van-datetime-picker
              v-model="currentDate"
              type="date"
              @cancel="onCancelDate"
              @confirm="onConfirmDate2"
              :min-date="minDate"
              :max-date="maxDate"
              :formatter="formatter"
            />
          </van-popup>
        </div>
      </div>
      <div class="skSty">
        <div class="title">交出单位</div>

        <div class="detailsStyle">
          <van-field v-model="formItem.handOverUnitProfessionalEngineer" placeholder="请输入工专业工程师" label="专业工程师" />
          <van-field v-model="formItem.handOverPersonChargeUnit" placeholder="请输入负责人" label="负责人" />
          <van-field v-model="formItem.dateHandover" placeholder="请选择日期" label="日期" @click="showBeginDate3" right-icon="arrow" input-align="left" readonly />
          <van-popup v-model="showPickerDate3" position="bottom">
            <van-datetime-picker
              v-model="currentDate"
              type="date"
              @cancel="onCancelDate"
              @confirm="onConfirmDate3"
              :min-date="minDate"
              :max-date="maxDate"
              :formatter="formatter"
            />
          </van-popup>
        </div>
      </div>
      <div class="accessory">
        <div>附件</div>
        <div class="download" v-if="formItem.attachments.length">
          <div
            style=" display: flex;
    justify-content: space-between;"
            v-for="(i, index) in formItem.attachments"
            :key="index"
          >
            <div>{{ i.fileName }}</div>
            <div style="color:#3562db"  @click="dload(i)">下载</div>
          </div>
        </div>
      </div>
      <div class="upload">
        <div>上传附件</div>
        <van-uploader
          :after-read="afterRead"
          v-model="fileListCurrent"
          :max-size="maxSize * 1024 * 1024"
          @oversize="onOversize"
          :before-delete="beforeDel"
          accept="image/*,video/*"
          :disabled="fileMessage == '上传中'"
          max-count="5"
        >
          <template v-if="fileMessage == '上传成功'" #preview-cover="{ file }">
            <div class="preview-cover van-ellipsis">上传成功</div>
          </template>
        </van-uploader>
        <div class="upload-tip">注：上传格式图片、视频，单张大小≤50M</div>
      </div>
    </div>
    <div class="btn"><van-button style="width:90%" color="#3562db" >提交</van-button></div>
</van-form>
  </div>
</template>

<script>
import axios from "axios";
import Vue from "vue";
import { Dialog } from "vant";
Vue.use(Dialog);

export default {
  data() {
    return {
      deptRow: {},
      show: false,
      options: [],
      fileListCurrent: [],
      maxSize: 50, //
      fileMessage: "",
      showDealing: false,
      projectList: [],
      showPickerDate: false,
      showPickerDate2: false,
      showPickerDate3: false,
      radio2: "",
      currentDate: new Date(),
      value: "",
      value2: "",
      value3: "",
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      formItem: {
        id: "", //主键
        createBy: "", //创建用户
        createTime: "", //创建时间
        updateBy: "", //更新用户
        updateTime: "", //更新时间
        acceptanceNumber: "", //验收编号
        projectId: "", //项目id
        projectName: "", //项目名称
        branchCompanyId: "", //组织单位/部门id
        branchCompanyName: "", //组织单位/部门名称
        unitProjectName: "", //单位工程名称
        engineeringtName: "", //工程名称
        receivingUnit: "", //接收单位
        handOverUnit: "", //交出单位
        contentsInstructions: "", //交出内容及说明
        opinionsReceivingUnit: "", //接收单位意见
        professionalEngineerReceivingUnit: "", //接收单位专业工程师
        personChargeReceivingUnit: "", //接收单位负责人
        dateReceivingUnit: "", //接收单位日期
        professionalEngineerOrganizationHandoverUnit: "", //组织交接单位/部门专业工程师
        responsiblePersonOrganizationHandoverUnit: "", //组织交接单位/部门负责人
        organizationHandoverUnitDate: "", //组织交接单位/部门日期
        handOverUnitProfessionalEngineer: "", //交出单位专业工程师
        handOverPersonChargeUnit: "", //交出单位负责人
        dateHandover: "", //交出单位日期
        attachmentIds: "", //附件ids
        delState: "", //删除状态 0未删除 1已删除
        attachments: [] //附件
      }
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getAcceptance();
    this.getNoList();
    this.getDepartmentList();
    // this.formItem.branchCompanyId = this.$route.query.rowInfo.id; //组织单位/部门id
    // this.formItem.branchCompanyName = this.$route.query.rowInfo.name; //组织单位/部门名称
  },
  methods: {
        dload(item) {
      api.download(
        {
          url: this.$YBS.imgUrlTranslation("imem/" + item.fileUrl),
          savePath: "fs://" + item.fileName,
          report: true,
          cache: true,
          allowResume: true
        },
        function(ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", ret);
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {
            });

            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },
    getAcceptance() {
      let self = this;

      this.$api.getAcceptance({ id: this.$route.query.id }).then(res => {
        self.formItem.id = res.id; //主键
        self.formItem.createBy = res.createBy; //创建用户
        self.formItem.createTime = res.createTime; //创建时间
        self.formItem.updateBy = res.updateBy; //更新用户
        self.formItem.updateTime = res.updateTime; //更新时间
        self.formItem.acceptanceNumber = res.acceptanceNumber; //验收编号
        self.formItem.projectId = res.projectId; //项目id
        self.formItem.projectName = res.projectName; //项目名称
        self.formItem.branchCompanyId = res.branchCompanyId; //组织单位/部门id
        self.formItem.branchCompanyName = res.branchCompanyName; //组织单位/部门名称
        self.formItem.unitProjectName = res.unitProjectName; //单位工程名称
        self.formItem.engineeringtName = res.engineeringtName; //工程名称
        self.formItem.receivingUnit = res.receivingUnit; //接收单位
        self.formItem.handOverUnit = res.handOverUnit; //交出单位
        self.formItem.contentsInstructions = res.contentsInstructions; //交出内容及说明
        self.formItem.opinionsReceivingUnit = res.opinionsReceivingUnit; //接收单位意见
        self.formItem.professionalEngineerReceivingUnit = res.professionalEngineerReceivingUnit; //接收单位专业工程师
        self.formItem.personChargeReceivingUnit = res.personChargeReceivingUnit; //接收单位负责人
        self.formItem.dateReceivingUnit = res.dateReceivingUnit; //接收单位日期
        self.formItem.professionalEngineerOrganizationHandoverUnit = res.professionalEngineerOrganizationHandoverUnit; //组织交接单位/部门专业工程师
        self.formItem.responsiblePersonOrganizationHandoverUnit = res.responsiblePersonOrganizationHandoverUnit; //组织交接单位/部门负责人
        self.formItem.organizationHandoverUnitDate = res.organizationHandoverUnitDate; //组织交接单位/部门日期
        self.formItem.handOverUnitProfessionalEngineer = res.handOverUnitProfessionalEngineer; //交出单位专业工程师
        self.formItem.handOverPersonChargeUnit = res.handOverPersonChargeUnit; //交出单位负责人
        self.formItem.dateHandover = res.dateHandover; //交出单位日期
        self.formItem.attachmentIds = res.attachmentIds; //附件ids
        self.formItem.delState = res.delState; //删除状态 0未删除 1已删除
        self.formItem.attachments = res.attachments;
      }).catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    deptClose() {
      this.formItem.branchCompanyId = this.deptRow.id; //组织单位/部门id
      this.formItem.branchCompanyName = this.deptRow.name; //组织单位/部门名称
    },
    handleSelectionChange(index, row) {
      this.deptRow = row;
    },
    afterRead(file2) {
      this.fileMessage = "上传中";
      var fileSize = parseFloat(parseInt(file2.file["size"]) / 1024 / 1024).toFixed(2);
      const { file } = file2;

      file2.status = "uploading";
      file2.message = "上传中...";
      this.handleUploadImg(file, file2);
    },

    handleUploadImg(file, file2) {
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.IMEM_API + "/api/file/uploadFile",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          userId: JSON.parse(localStorage.getItem("loginInfo")).staffId
        }
      }).then(res => {
        if (res.data.code == 0) {
          file2.status = "success";
          file2.message = "上传成功";
          this.fileMessage = "上传成功";
          this.formItem.attachments.push(res.data.data);
        } else {
          file2.status = "failed";
          file2.message = "上传失败";
          this.fileMessage = "上传失败";
        }
      });
    },
    // 删除图片 之前
    beforeDel(file, index) {
      console.log(index, "index");
      // 上传到服务器失败
      this.formItem.attachments.splice(index, 1);
      this.fileListCurrent.splice(index, 1);
    },
    onOversize() {
      this.$toast.fail(`大小不能超过${this.maxSize}M`);
    },

    getDepartmentList() {
      this.$api.getDepartmentList({}).then(res => {
        this.options = res;
      });
    },
    onDealing(value) {
      this.formItem.projectName = value.name;
      this.formItem.projectId = value.id;
      this.showDealing = false;
    },
    getNoList() {
      let data = {
        page: 1,
        limit: 999
      };
      this.$api.getNoList(data).then(res => {
        this.projectList = res.list;
      });
    },
    onSubmit() {
        var params = this.formItem;
      this.$api
        .getAcceptanceEditSave(params)
        .then(res => {
          this.$router.go(-1);
          this.$toast.success(res);
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    onConfirmDate() {
      this.formItem.dateReceivingUnit = `${this.value1}-${this.value2}-${this.value3}`; // 字符串拼接 结果入2020-07-03
      this.onCancelDate();
    },
    onConfirmDate2() {
      this.formItem.organizationHandoverUnitDate = `${this.value1}-${this.value2}-${this.value3}`; // 字符串拼接 结果入2020-07-03
      this.onCancelDate();
    },
    onConfirmDate3() {
      this.formItem.dateHandover = `${this.value1}-${this.value2}-${this.value3}`; // 字符串拼接 结果入2020-07-03
      this.onCancelDate();
    },
    // 日期组件自定义格式
    formatter(type, value) {
      if (type === "year") {
        this.value1 = value; // 可以拿到当前点击的数值
        return `${value}年`;
      } else if (type === "month") {
        this.value2 = value;
        return `${value}月`;
      }
      this.value3 = value;
      return `${value}日`;
    },
    showBeginDate() {
      this.showPickerDate = true;
    },
    showBeginDate2() {
      this.showPickerDate2 = true;
    },
    showBeginDate3() {
      this.showPickerDate3 = true;
    },
    onCancelDate() {
      this.showPickerDate = false;
      this.showPickerDate2 = false;
      this.showPickerDate3 = false;
    },

    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  .listStyle {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
  }
}
.statistics {
  width: 95%;
  height: 77px;
  background-color: #f7f8fa;
  margin: 0 auto;
  border-radius: 5px;
  > div {
    width: 100%;
    height: 50%;
    display: flex;
    > div {
      flex: 1;
      text-align: center;
      line-height: 39px;
    }
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #1d2129;
    }
    .lable {
      color: #86909c;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.skSty {
  background-color: #fff;
  width: 100%;
  margin-bottom: 10px;
  .title {
    padding: 10px 15px;
  }
  .detailsStyle {
    width: 100%;
  }
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}

.upload {
  background-color: #fff;
  line-height: 50px;
  width: 95%;
  margin-bottom: 100px;
  padding: 0 10px;
}
.accessory {
  background-color: #fff;
  min-height: 50px;
  width: 95%;
  margin-bottom: 10px;
  padding: 10px 10px;
  .download {
    width: 95%;
    margin: 10px;
  }
}

/deep/ .van-field__label {
  width: 7.5em;
}
.upload-tip {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #696969;
  line-height: 20px;
}
.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  height: 20px;
  line-height: 15px;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
