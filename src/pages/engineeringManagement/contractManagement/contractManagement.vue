<template>
  <div class="inner">
    <Header title="合同管理" @backFun="goBack"></Header>
    <div v-if="list.length > 0" class="list">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in list" :key="item.id" class="listStyle" @click="skipList(item)">
              <div class="name">
                <div class="left"><img :src="img" alt="" />{{ item.projectName }}</div>
                <div><van-icon name="arrow" /></div>
              </div>
              <div class="txt">
                <div>
                  <div class="txt_l">付款合同金额</div>
                  <div class="txt_r">{{ item.rpType == "payment" ? item.advances : 0 }} <span>万元</span></div>
                </div>
                <div>
                  <div class="txt_l">收款合同金额</div>
                  <div class="txt_r">{{ item.rpType == "collection" ? item.advances : 0 }} <span>万元</span></div>
                </div>
                <div>
                  <div class="txt_l">项目计划投资金额</div>
                  <div class="txt_r">{{ item.proxBudget || 0 }} <span>万元</span></div>
                </div>
              </div>
              <div class="txt">
                <div>
                  <div class="txt_l">合同个数</div>
                  <div class="txt_r">{{ item.contractNum || 0 }} <span>个</span></div>
                </div>
                <div>
                  <div class="txt_l">问题合同个数</div>
                  <div class="txt_r" style="color: #F53F3F;">{{ item.problemNum || 0 }} <span>个</span></div>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import img from "@/assets/images/Frame 427318804.png";

export default {
  data() {
    return {
      list: [],
      page: 1,
      limit: 10,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      img
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getByProjectList();
  },
  methods: {
    getByProjectList() {
      let data = {
        page: this.page,
        limit: this.limit
      };
      this.$api.getByProjectList(data).then(res => {
        this.loading = false;
        res.list.forEach(item => {
          this.list.push(item);
        });
        if (this.list.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getByProjectList();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getByProjectList();
    },
    skipList(row) {
      this.$router.push({
        path: "/contractManagementList",
        query: {
          id: row.projectId
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  height: 100vh;
  overflow: hidden;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 0 0 10px 0;
    .name {
      padding: 10px 10px 5px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .txt {
      width: 90%;
      margin: 0 auto 5px;
      border-radius: 5px;
      background-color: #f7f8fa;
      padding: 0 10px;
      > div {
        display: flex;
        justify-content: space-between;
        line-height: 44px;
        .txt_l {
          width: 150px;
          color: #1d2129;

          font-weight: 400;
        }
        .txt_r {
          color: #1d2129;

          color: #1d2129;
          font-size: 18px;

          font-weight: bold;
          > span {
            color: #86909c;
            font-weight: 400;
            font-size: 14px;
          }
        }
      }
    }
  }
}
.list {
  height: calc(100% - 80px);
  overflow: auto;
}
.statistics {
  width: 95%;
  height: 77px;
  background-color: #f7f8fa;
  margin: 0 auto;
  border-radius: 5px;
  > div {
    width: 100%;
    height: 50%;
    display: flex;
    > div {
      flex: 1;
      text-align: center;
      line-height: 39px;
    }
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #1d2129;
    }
    .lable {
      color: #86909c;
      font-weight: 400;
      font-size: 14px;
    }
  }
}
.unit {
  font-size: 14px;
  width: 95%;
  line-height: 20px;
  font-weight: 400;
  color: #c9cdd4;
  text-align: right;
}
img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}
.notList {
  position: relative;
  height: calc(100vh - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
