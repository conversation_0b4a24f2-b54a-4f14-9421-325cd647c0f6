<template>
  <div class="inner">
    <Header title="合同管理" @backFun="goBack"></Header>

    <div v-if="list.length > 0" class="list">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in list" :key="item.id" class="listStyle">
              <div class="name">
                <div class="left"><img :src="img" alt="" />{{ item.contractName }}</div>
                <div>{{ item.status == 0 ? "正常" : item.status == 1 ? "异常" : item.status == 2 ? "已完结" : "" }}</div>
                <div><van-icon name="arrow" /></div>
              </div>
              <div class="txt">
                <div class="txt_l">合同名称</div>
                <div>{{ item.contractName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">合同类型</div>
                <div>{{ item.contractType }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">合同状态</div>
                <div>{{ item.status == 0 ? "正常" : item.status == 1 ? "异常" : item.status == 2 ? "已完结" : "" }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">收付类型</div>
                <div>{{ item.rpType == "payment" ? "付款" : item.rpType == "collection" ? "收款" : "" }}</div>
              </div>

              <div class="txt">
                <div class="txt_l">合同签订日期</div>
                <div>{{ item.signTime }}个月</div>
              </div>
              <div class="txt">
                <div class="txt_l">合同金额</div>
                <div>{{ item.contractPrice }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">签约日期</div>
                <div>{{ item.signTime }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">付款节点</div>
                <div>{{ item.paymentNodeNumber }}</div>
              </div>
              <div class="btn">
                <div @click="markProblem(item)">标记问题合同</div>
                <div @click="skipList(item)">编辑</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import img from "@/assets/images/Frame 427318804.png";
export default {
  data() {
    return {
      img,
      list: [],
      page: 1,
      limit: 10,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getlistByProjectId();
  },
  methods: {
    getlistByProjectId() {
      let data = {
        page: this.page,
        limit: this.limit,
        projectId: this.$route.query.id
      };
      this.$api.getlistByProjectId(data).then(res => {
        this.loading = false;
        res.list.forEach(item => {
          this.list.push(item);
        });
        if (this.list.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getlistByProjectId();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getlistByProjectId();
    },
    markProblem(row) {
      this.$router.push({
        path: "/problemContractMark",
        query: {
          row: row
        }
      });
    },
    skipList(row) {
      this.$router.push({
        path: "/contractManagementEdit",
        query: {
          id: row.contractId
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  height: 100vh;
    overflow :hidden;

  .listStyle {
    width: 95%;
    // height: 332px;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    .name {
      padding: 10px 10px 5px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.list{
    height:calc(100% - 80px);
    overflow:auto;
    }
.btn {
  margin-top: 10px;
  display: flex;
  border-top: 1px solid #e5e6eb;
  > div {
    flex: 1;
    text-align: center;
    line-height: 40px;

    font-size: 14px;
    color: #86909c;
    font-weight: 400;
  }
}
img {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.notList {
  position: relative;
  height: calc(100vh - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
