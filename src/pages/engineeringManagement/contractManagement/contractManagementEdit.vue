<template>
  <div class="inner">
    <Header title="合同编辑" @backFun="goBack"></Header>
    <van-form @submit="onSubmit">
      <div style="width:100%;overflow-y:scroll;">
        <div class="listStyle">
          <van-field :rules="[{ required: true }]" required readonly v-model="formItem.projectName" placeholder="请输入项目名称" label="项目名称" />
          <van-field :rules="[{ required: true }]" required v-model="formItem.contractName" placeholder="请输入合同名称" label="合同名称" />
          <van-field
            :rules="[{ required: true }]"
            required
            v-model="contractName"
            placeholder="请选择合同类型"
            label="合同类型"
            @click="showDealing = true"
            readonly
            right-icon="arrow"
            input-align="left"
          />
          <van-popup v-model="showDealing" position="bottom">
            <van-picker value-key="dataLabel" show-toolbar :columns="contractTypeDict" @confirm="onDealing" @cancel="showDealing = false" />
          </van-popup>
          <van-field :rules="[{ required: true }]" required v-model="formItem.contractPrice" placeholder="请输入合同金额" label="合同金额(万元)" />
          <van-field v-model="formItem.partyBUnit" placeholder="请输入乙方单位" label="乙方单位" />
          <div style="display:flex">
            <van-field style="width:80%" v-model="formItem.warrantyTime" placeholder="请输入质保期" label="质保期" />
            <el-select style="width:20%;" v-model="formItem.warrantyUnit">
              <el-option v-for="item in contractWarrantyTimeUniteDict" :key="item.id" :label="item.dataLabel" :value="item.dataValue"></el-option>
            </el-select>
          </div>
          <van-field v-model="formItem.advancesTime" placeholder="请选择预收/付时间" label="接预收/付时间" @click="showBeginDate" right-icon="arrow" input-align="left" readonly />
          <van-popup v-model="showPickerDate" position="bottom">
            <van-datetime-picker v-model="currentDate" type="date" @cancel="onCancelDate" @confirm="onConfirmDate" :min-date="minDate" :max-date="maxDate" :formatter="formatter" />
          </van-popup>
          <van-field
            :rules="[{ required: true }]"
            required
            v-model="formItem.signTime"
            placeholder="请选择签约日期"
            label="签约日期"
            @click="showBeginDate2"
            right-icon="arrow"
            input-align="left"
            readonly
          />
          <van-popup v-model="showPickerDate2" position="bottom">
            <van-datetime-picker v-model="currentDate" type="date" @cancel="onCancelDate" @confirm="onConfirmDate" :min-date="minDate" :max-date="maxDate" :formatter="formatter" />
          </van-popup>

          <van-field
            v-model="formItem.parentName"
            placeholder="请输入或选择主合同名称"
            label="主合同名称"
            @click="showContract = true"
            readonly
            right-icon="arrow"
            input-align="left"
          />
          <van-popup v-model="showContract" position="bottom">
            <van-picker value-key="contractName" show-toolbar :columns="masterContract" @confirm="onContract" @cancel="showContract = false" />
          </van-popup>
          <div style="display:flex">
            <van-field :rules="[{ required: true }]" required style="width:80%" v-model="formItem.contractSignProjectTime" placeholder="请输入合同签订工期" label="合同签订工期">
            </van-field>
            <el-select
              style="width:20%;

            "
              v-model="formItem.contractSignProjectTimeUnit"
            >
              <el-option v-for="item in contractSignProjectTimeUnitDict" :key="item.id" :label="item.dataLabel" :value="item.dataValue"></el-option>
            </el-select>
          </div>
          <van-field v-model="formItem.partyAUnit" placeholder="请输入甲方单位" label="甲方单位" /><van-field
            v-model="rpName"
            required
            :rules="[{ required: true }]"
            placeholder="请选择收付类型"
            label="收付类型"
            @click="rpShow = true"
            readonly
            right-icon="arrow"
            input-align="left"
          />
          <van-popup v-model="rpShow" position="bottom">
            <van-picker value-key="dataLabel" show-toolbar :columns="contractRpTypeDict" @confirm="rpDealing" @cancel="rpShow = false" />
          </van-popup>

          <van-field v-model="formItem.advances" placeholder="请输入预收/付款" label="预收/付款(万元)" />
        </div>

        <div class="skSty">
          <div class="title">
            <div>
              付款节点
            </div>
            <div class="topSelest" @click="add">
              <van-icon name="plus" />
              <p>添加</p>
            </div>
          </div>

          <div class="detailsStyle">
            <div class="box" v-for="(item, index) in formItem.contractPaymentNodeList" :key="index">
              <van-swipe-cell>
                <div class="txt">
                  <van-field v-model="item.terms" placeholder="请输入条款内容" label="条款内容" />
                  <van-field v-model="item.amountProportion" placeholder="请输入金额比例" label="金额比例" />
                  <van-field v-model="item.amountUnitName" placeholder="请选择单位" label="单位" @click="payment(index)" readonly right-icon="arrow" input-align="left" />

                  <van-field v-model="item.planTaskNames" placeholder="请选择任务节点" label="任务节点" @click="taskNode(index)" readonly right-icon="arrow" input-align="left" />
                </div>
                <template #right>
                <van-button square @click="DelRow(index)" text="删除" type="danger" class="delete-button" />
              </template>
              </van-swipe-cell>
            </div>
            <van-popup v-model="showPayment" position="bottom">
              <van-picker value-key="dataLabel" show-toolbar :columns="amountUnitDict" @confirm="onPayment" @cancel="showPayment = false" />
            </van-popup>
            <van-popup v-model="showTaskNode" position="bottom" @close="taskNodeClose(index)">
              <!-- <van-picker value-key="dataLabel" show-toolbar :columns="amountUnitDict" @confirm="onPayment" @cancel="showTaskNode = false" /> -->
              <el-table
                ref="table"
                height="calc(50vh - 80px)"
                :data="options"
                style="width: 100%;margin-bottom: 20px;"
                row-key="id"
                :tree-props="{ children: 'childs', hasChildren: 'hasChildren' }"
              >
                <el-table-column width="75">
                  <template v-slot="{ row }">
                    <!--                    <Checkbox v-model="row.checkbox" :disabled="row.taskMode==0"></Checkbox>-->
                    <el-checkbox v-model="row.checkbox" :disabled="taskTreeCheckboxDisabled(row)"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="fakeId" label="编码"> </el-table-column>
                <el-table-column prop="name" label="任务名称"> </el-table-column>
              </el-table>
            </van-popup>
          </div>
        </div>
      </div>
      <div class="btn"><van-button style="width:90%" color="#3562db" type="info" native-type="submit">保存</van-button></div>
    </van-form>
  </div>
</template>

<script>
import Vue from "vue";
import { SwipeCell } from "vant";

Vue.use(SwipeCell);
export default {
  data() {
    return {
      paymentNodeTaskList: [],
      paymentNodeColumnsCurrentIndex: 0, //当前行
      showTaskNode: false,
      showContract: false,
      showPickerDate: false,
      showPickerDate2: false,
      currentDate: new Date(),
      value: "",
      value2: "",
      value3: "",
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      showPayment: false,
      rpShow: false,
      showDealing: false,
      formItem: {
        projectId: "", //项目id
        projectName: "", //项目名称
        signTime: "", //签约日期
        contractNumber: "", //合同编号
        contractName: "", //合同名称
        parentId: "", //主合同id
        parentName: "", //主合同名称
        contractType: "", //合同类型
        contractSignProjectTime: "", //合同签订工期
        contractSignProjectTimeUnit: "", //合同签订工期单位
        contractPrice: "", //合同金额
        partyAUnit: "", //甲方单位
        partyBUnit: "", //乙方单位
        warrantyTime: "", //质保期
        warrantyUnit: "", //质保单位
        advances: "", //预收款
        status: "", //合同状态
        partyAUnitId: "", //甲方单位id
        partyBUnitId: "", //乙方单位id
        rpType: "", //收付款类型
        advancesTime: "", //预收付款时间
        contractPaymentNode: [], //合同付款节点数据
        attachments: [], //合同附件,
        contractPaymentNodeList: [], //付款节点
        contractProblemList: [] //问题合同
      },
      contractName: "", //合同类型name
      rpName: "", //收付款name
      contractWarrantyTimeUniteDict: [],
      contractIssueDict: [],
      contractTypeDict: [],
      contractRpTypeDict: [],
      contractSignProjectTimeUnitDict: [],
      amountUnitDict: [],
      masterContract: [],
      options: [],
      arr: [],
      index: "",
      index2: "",
      text: ""
    };
  },
  created() {
    this.loadDict();
    this.getDetailsContract();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getcontractlist();
  },
  methods: {
    taskNodeClose(index) {
      this.arr = [];
      this.setName(this.options); //递归去掉未选中的
      let records = this.arr;
      let names = "";
      records.forEach(function(item, index) {
        if (index != records.length - 1) {
          names = names + item.name + ",";
        } else {
          names = names + item.name;
        }
      });
      if (this.formItem.contractPaymentNodeList[this.index].planTaskNames != "") {
        this.formItem.contractPaymentNodeList[this.index].planTaskNames = this.formItem.contractPaymentNodeList[this.index].planTaskNames + "," + names;
      } else {
        this.formItem.contractPaymentNodeList[this.index].planTaskNames = names;
      }

      if (this.formItem.contractPaymentNodeList[this.index].planTaskList) {
        records.forEach(i => {
          this.formItem.contractPaymentNodeList[this.index].planTaskList.push(i);
        });
      } else {
        this.formItem.contractPaymentNodeList[this.index].planTaskList = records;
      }

      this.getSearch();
    },
    taskTreeCheckboxDisabled(row) {
      let self = this;
      let disabledStatus = false;
      if (row.taskMode == 0) {
        return true;
      }
      this.paymentNodeTaskList.forEach(i => {
        if (i.fakeId == row.fakeId) {
          disabledStatus = true;
        }
      });
      //遍历已选任务
      this.formItem.contractPaymentNodeList.forEach((pnodeItem, pindex) => {
        if (pnodeItem.planTaskList) {
          pnodeItem.planTaskList.forEach(titem => {
            if (titem.id == row.id) {
              disabledStatus = true;
            }
          });
        }

        //当前任务节点
        if (self.paymentNodeColumnsCurrentIndex == pindex) {
          if (pnodeItem.planTaskList) {
            pnodeItem.planTaskList.forEach(titem => {
              if (titem.id == row.id) {
                disabledStatus = false;
              }
            });
          }
        }
      });
      return disabledStatus;
    },
    setName(datas) {
      //遍历树
      for (let index = 0; index < datas.length; index++) {
        if (datas[index].checkbox) {
          this.arr.push(datas[index]);
        }
        if (datas[index].childs) {
          this.setName(datas[index].childs);
        }
      }
    },
    getPaymentNodeTaskListByProjectId() {
      this.$api.getPaymentNodeTaskListByProjectId({ projectId: this.formItem.projectId }).then(res => {
        this.paymentNodeTaskList = res;
      });
    },
    getSearch() {
      this.$api.getSearch({ projectId: this.formItem.projectId }).then(res => {
        this.options = res;
      });
    },
    payment(index) {
      this.index2 = index;
      this.showPayment = true;
    },
    taskNode(index) {
      this.index = index;
      console.log(index, "sss");
      this.showTaskNode = true;
    },
    // 日期组件自定义格式
    formatter(type, value) {
      if (type === "year") {
        this.value1 = value; // 可以拿到当前点击的数值
        return `${value}年`;
      } else if (type === "month") {
        this.value2 = value;
        return `${value}月`;
      }
      this.value3 = value;
      return `${value}日`;
    },
    showBeginDate() {
      this.showPickerDate = true;
    },
    onCancelDate() {
      this.showPickerDate = false;
      this.showPickerDate2 = false;
    },
    showBeginDate2() {
      this.showPickerDate2 = true;
    },
    onConfirmDate() {
      this.formItem.advancesTime = `${this.value1}-${this.value2}-${this.value3}`; // 字符串拼接 结果入2020-07-03
      this.onCancelDate();
    },
    onConfirmDate2() {
      this.formItem.signTime = `${this.value1}-${this.value2}-${this.value3}`; // 字符串拼接 结果入2020-07-03
      this.onCancelDate();
    },
    loadDict() {
      let self = this;

      //质保期单位
      this.$api.getDict({ type: "sys_contract_warranty_time_unit" }).then(res => {
        this.contractWarrantyTimeUniteDict = res;
      });
      // 问题合同类型
      this.$api.getDict({ type: "sys_contract_issue_type" }).then(res => {
        this.contractIssueDict = res;
      });
      // 合同类型数据字典
      this.$api.getDict({ type: "system_contract_type" }).then(res => {
        this.contractTypeDict = res;
      });
      // 收付类型
      this.$api.getDict({ type: "sys_contract_rp_type" }).then(res => {
        this.contractRpTypeDict = res;
      });
      // 合同签订工期单位
      this.$api.getDict({ type: "sys_contract_sign_project_time_unit" }).then(res => {
        this.contractSignProjectTimeUnitDict = res;
      });
      // 付款节点单位
      this.$api.getDict({ type: "sys_contract_amount_unit" }).then(res => {
        this.amountUnitDict = res;
      });
    },
    getcontractlist() {
      let data = {
        limit: 9999,
        page: 1,
        projectId: this.formItem.projectId
      };
      this.$api.getcontractlist(data).then(res => {
        this.masterContract = res.list;
      });
    },
    getDetailsContract() {
      let self = this;

      this.$api.getDetailsContract({ id: this.$route.query.id }).then(res => {
        self.formItem.id = res.id; //合同id
        self.formItem.projectId = res.projectId; //项目id
        this.getSearch();
        this.getPaymentNodeTaskListByProjectId();
        self.formItem.projectName = res.projectName; //项目名称
        self.formItem.signTime = res.signTime; //签约日期
        self.formItem.contractNumber = res.contractNumber; //合同编号
        self.formItem.contractName = res.contractName; //合同名称
        self.formItem.parentId = res.parentId; //主合同id
        (this.contractName = res.contractTypeName), //合同类型name
          (this.rpName = res.rpTypeName), //收付款name
          (self.formItem.contractType = res.contractType); //合同类型
        self.formItem.contractSignProjectTime = res.contractSignProjectTime; //合同签订工期
        self.formItem.contractSignProjectTimeUnit = res.contractSignProjectTimeUnit; //合同签订工期单位
        self.formItem.contractPrice = res.contractPrice ? res.contractPrice * 1 : ""; //合同金额
        self.formItem.partyAUnit = res.partyAUnit; //甲方单位
        self.formItem.partyBUnit = res.partyBUnit; //乙方单位
        self.formItem.warrantyTime = res.warrantyTime; //质保期
        self.formItem.warrantyUnit = res.warrantyUnit; //质保期
        self.formItem.advances = res.advances ? res.advances * 1 : ""; //预收款
        self.formItem.status = res.status; //合同状态
        self.formItem.partyAUnitId = res.partyAUnitId; //甲方单位id
        self.formItem.partyBUnitId = res.partyBUnitId; //乙方单位id
        self.formItem.rpType = res.rpType; //收付款类型
        self.formItem.advancesTime = res.advancesTime; //收付款时间
        self.formItem.attachments = res.attachments; //合同附件
        self.formItem.contractPaymentNodeList = res.contractPaymentNodeList; //付款节点
        self.formItem.contractProblemList = res.contractProblemList; //问题
        self.contractPaymentReceivedList = res.contractPaymentReceivedList; //收付记录

        if (self.formItem.parentId && self.formItem.parentId != 0) {
          this.$api.getDetailsContract({ id: self.formItem.parentId }).then(res2 => {
            self.formItem.parentName = res2.contractName; //主合同名称
          });
        }
      });
    },
    onContract(value) {
      this.formItem.parentName = value.contractName;
      this.formItem.parentId = value.id;
      this.showContract = false;
    },
    onDealing(value) {
      this.contractName = value.dataLabel;
      this.formItem.amountProportion = value.dataValue;
      this.showDealing = false;
    },
    onPayment(value) {
      this.formItem.contractPaymentNodeList[this.index2].amountUnitName = value.dataLabel;

      this.formItem.contractPaymentNodeList[this.index2].amountUnit = value.dataValue;
      this.showPayment = false;
    },
    rpDealing(value) {
      this.rpName = value.dataLabel;
      this.formItem.rpType = value.dataValue;
      this.rpShow = false;
    },
    add() {
      this.formItem.contractPaymentNodeList.push({
        terms: "", //条款内容
        amountProportion: "", //金额比例
        amountUnit: "", //单位
        planTaskNames: "" //任务节点
      });
    },
    DelRow(index) {
      this.formItem.contractPaymentNodeList.splice(index, 1);
    },
    onSubmit(value) {
      console.log(value,'ssssssssssssss');
      let self = this;
      if (self.formItem.advances > self.formItem.contractPrice) return  this.$toast.fail("预付款不能大于合同金额");
       var params = {
            id: self.formItem.id,//合同id
            projectId: self.formItem.projectId,//项目id
            projectName: self.formItem.projectName,//项目名称
            signTime: self.formItem.signTime,//签约日期
            contractNumber: self.formItem.contractNumber,//合同编号
            contractName: self.formItem.contractName,//合同名称
            parentId: self.formItem.parentId,//主合同id
            parentName: self.formItem.parentName,//主合同名称
            contractType: self.formItem.contractType,//合同类型
            contractSignProjectTime: self.formItem.contractSignProjectTime,//合同签订工期
            contractSignProjectTimeUnit: self.formItem.contractSignProjectTimeUnit,//合同签订工期
            contractPrice: self.formItem.contractPrice,//合同金额
            partyAUnit: self.formItem.partyAUnit,//甲方单位
            partyBUnit: self.formItem.partyBUnit,//乙方单位
            warrantyTime: self.formItem.warrantyTime,//质保期
            warrantyUnit: self.formItem.warrantyUnit,//质保单位
            advances: self.formItem.advances,//预收款
            status: self.formItem.status,//合同状态
            partyAUnitId: self.formItem.partyAUnitId,//甲方单位id
            partyBUnitId: self.formItem.partyBUnitId,//乙方单位id
            rpType: self.formItem.rpType,//收付款类型
            advancesTime: self.formItem.advancesTime,//预收付时间
            contractPaymentNodeList: self.formItem.contractPaymentNodeList,
            attachments: this.formItem.attachments,
          }
           var terms_ok = false;
          var amountProportion_ok = false;
          var planTaskList_ok = false;
          var danwei = false;//单位
          var bili = false;//比例
          params.contractPaymentNodeList.forEach(function (v, i) {
            if (v.terms == '' || v.terms == null) {
              terms_ok = true;
            }
            if (v.amountProportion == '' || v.amountProportion == null) {
              amountProportion_ok = true;
            }
            if (v.planTaskList == null || v.planTaskList.length == 0) {
              planTaskList_ok = true;
            }
            if (v.amountUnit=='') {
              danwei = true;
            }
            if (v.amountUnit=='percent'&&v.amountProportion*1>100) {
              bili = true;
            }
          })
          if (terms_ok) {
            this.$toast.fail('付款节点条款内容请填写完整');
            return
          }
          if (amountProportion_ok) {
            this.$toast.fail('付款节点金额请填写完整');
            return
          }
          if (planTaskList_ok) {
            this.$toast.fail('付款节点任务节点请选择完整');
            return
          }
          if (danwei) {
            this.$toast.fail('付款节点付款单位请选择完整');
            return
          }
          if (bili) {
            this.$toast.fail('付款节点选择单位百分比数值不能大于100');
            return
          }
      this.$api
        .getEditSave(params)
        .then(res => {
          this.$router.go(-1);
          this.$toast.success(res);
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  .listStyle {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
  }
}
.statistics {
  width: 95%;
  height: 77px;
  background-color: #f7f8fa;
  margin: 0 auto;
  border-radius: 5px;
  > div {
    width: 100%;
    height: 50%;
    display: flex;
    > div {
      flex: 1;
      text-align: center;
      line-height: 39px;
    }
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #1d2129;
    }
    .lable {
      color: #86909c;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.skSty {
  background-color: #fff;
  width: 100%;
  margin-bottom: 10px;
  .title {
    display: flex;
    justify-content: space-between;
    padding: 10px;
  }
}
.detailsStyle {
  width: 100%;
  background-color: #fff;
  margin-bottom: 60px;
  .box {
    // padding-left: 10px;
    border-radius: 5px;
    padding-bottom: 5px;
    .txt {
      width: 95%;
      margin: 0 auto;
      border-radius: 5px;
      background-color: #f7f8fa;
      // padding: 0 10px;
      .van-cell {
        background-color: #f7f8fa;
      }
    }
  }
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}

.upload {
  background-color: #fff;
  height: 50px;
  line-height: 50px;
  width: 95%;
  margin-bottom: 100px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
}
.topSelest {
  display: flex;
  align-items: center;
  color: #3562db;
}
.delete-button {
  height: 100%;
}

/deep/ .el-input__inner {
  border: none;

  box-shadow: none;
}
/deep/ .van-field__label {
  width: 7em;
}
</style>
