<template>
  <div class="inner">
    <Header title="问题合同" @backFun="goBack"></Header>
    <van-form @submit="onSubmit">
      <div class="essential">
        <div class="txt">
          <div class="txt_l">合同名称</div>
          <div>{{ contractProblemForm.contractName }}</div>
        </div>
        <van-field
          :rules="[{ required: true }]"
          required
          v-model="dealingName"
          label="问题类型"
          placeholder="请选择问题类型"
          readonly
          right-icon="arrow"
          input-align="left"
          @click="showDealing = true"
        />
        <van-popup v-model="showDealing" position="bottom">
          <van-picker value-key="dataLabel" show-toolbar :columns="contractIssueDict" @confirm="onDealing" @cancel="showDealing = false" />
        </van-popup>
      </div>
      <div class="suggestion">
        <div>标识说明</div>
        <van-field
          v-model="contractProblemForm.problemComment"
          rows="3"
          autosize
          label=""
          type="textarea"
          maxlength="500"
          placeholder="请输入标识说明,字数限制500字以内"
          show-word-limit
        />
        <div class="upload">
          <div>附件</div>
          <van-uploader
            :after-read="afterRead"
            v-model="fileListCurrent"
            :max-size="maxSize * 1024 * 1024"
            @oversize="onOversize"
            :before-delete="beforeDel"
            accept="image/*,video/*"
            :disabled="fileMessage == '上传中'"
            max-count="5"
          >
            <template v-if="fileMessage == '上传成功'" #preview-cover="{ file }">
              <div class="preview-cover van-ellipsis">上传成功</div>
            </template>
          </van-uploader>
          <div class="upload-tip">注：上传格式图片、视频，单张大小≤50M</div>
        </div>
      </div>
      <div class="btn">
        <van-button style="width:40%" color="#3562db" @click="goback">取消</van-button
        ><van-button style="width:40%" color="#3562db" type="info" native-type="submit">保存提交</van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import axios from "axios";

export default {
  components: {},
  data() {
    return {
      fileListCurrent: [],
      maxSize: 50, //
      fileMessage: "",
      //问题合同
      contractProblemForm: {
        createId: "", //标记用户
        contractId: "", //合同id
        contractNumber: "", //合同编号
        contractName: "", //合同名称
        type: "", //问题类型
        problemComment: "", //问题说明
        disposeComment: "", //处理说明
        disposeAttachments: "", //处理附件
        legalComment: "", //法务处理说明
        legalAttachments: "", //法务处理附件
        problemAttachmentList: []
      },
      dealingName: "",
      contractIssueDict: [], //指定协办人
      showDealing: false
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.contractProblemForm.contractName = this.$route.query.row.contractName;
    this.contractProblemForm.contractId = this.$route.query.row.contractId;
    this.loadDict();
  },
  methods: {
    onSubmit() {
      this.$api
        .addContractMark(this.contractProblemForm)
        .then(res => {
          this.$toast.success(res);
          this.$router.go(-1);
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    afterRead(file2) {
      this.fileMessage = "上传中";
      var fileSize = parseFloat(parseInt(file2.file["size"]) / 1024 / 1024).toFixed(2);
      const { file } = file2;

      file2.status = "uploading";
      file2.message = "上传中...";
      this.handleUploadImg(file, file2);
    },

    handleUploadImg(file, file2) {
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.IMEM_API + "/api/file/uploadFile",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          userId: JSON.parse(localStorage.getItem("loginInfo")).staffId
        }
      }).then(res => {
        if (res.data.code == 0) {
          file2.status = "success";
          file2.message = "上传成功";
          this.fileMessage = "上传成功";
          this.contractProblemForm.problemAttachmentList.push(res.data.data);
        } else {
          file2.status = "failed";
          file2.message = "上传失败";
          this.fileMessage = "上传失败";
        }
      });
    },
    // 删除图片 之前
    beforeDel(file, index) {
      this.contractProblemForm.problemAttachmentList.splice(index, 1);
      this.fileListCurrent.splice(index, 1);
    },
    onOversize() {
      this.$toast.fail(`大小不能超过${this.maxSize}M`);
    },
    loadDict() {
      let self = this;

      // 问题合同类型
      this.$api.getDict({ type: "sys_contract_issue_type" }).then(res => {
        this.contractIssueDict = res;
      });
    },
    onDealing(value) {
      this.dealingName = value.dataLabel;
      this.contractProblemForm.type = value.dataValue;
      this.showDealing = false;
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  font-size: 15px;
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
  }
}

.txt {
  display: flex;
  font-size: 15px;
  padding: 11px 15px;
  .txt_l {
    width: 150px;
    color: #4e5969;
    font-weight: 300;
  }
}

.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.suggestion {
  width: 100%;
  background-color: #fff;
  margin-bottom: 60px;

  > div {
    padding: 10px;
  }
}
.upload {
  background-color: #fff;
  line-height: 50px;
  width: 95%;
  margin-bottom: 10px;
  padding: 0 10px;
}
.upload-tip {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #696969;
  line-height: 20px;
}
.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  height: 20px;
  line-height: 15px;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/ .van-cell {
  font-size: 15px;
  color: #4e5969 !important;
  font-weight: 300;
}
</style>
