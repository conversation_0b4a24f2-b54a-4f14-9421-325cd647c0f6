<template>
  <div class="inner">
    <Header title="整改单" @backFun="goBack"></Header>
    <div class="essential">
      <div class="txt"><i class="public"></i>基础信息</div>
      <div class="txt">
        <div class="txt_l">受检单位</div>
        <div>{{ qualityInspectionForm.inspectedUnit }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改部门</div>
        <div>{{ inspectionPartForm.rectificationDepartmentName }}</div>
      </div>

      <div class="txt">
        <div class="txt_l">整改责任人</div>
        <div>{{ inspectionPartForm.rectificationPeopleName }}</div>
      </div>
    </div>

    <div class="suggestion">
      <div class="txt">
        <div class="txt_l">隐患类别</div>
        <div>{{ getDangerTypeDict(inspectionPartForm.dangerType) }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改日期</div>
        <div>{{ inspectionPartForm.rectificationTime }}</div>
      </div>

      <div>隐患描述</div>
      <van-field readonly v-model="inspectionPartForm.dangerDesc" rows="2" autosize label="" type="textarea" placeholder="" show-word-limit />
      <div>整改建议</div>
      <van-field readonly v-model="inspectionPartForm.dangerAdvise" rows="2" autosize label="" type="textarea" placeholder="" show-word-limit />
      <div class="uplod">
        <div>附件</div>

        <div class="download" v-if="inspectionPartForm.attachments.length">
          <div
            style=" display: flex;
    justify-content: space-between;"
            v-for="(i, index) in inspectionPartForm.attachments"
            :key="index"
          >
            <div>{{ i.fileName }}</div>
            <div style="color:#3562db"  @click="dload(i)">下载</div>
          </div>
        </div>
      </div>
    </div>

    <div class="btn"><van-button style="width:90%" color="#3562db" @click="$router.go(-1)">返回</van-button></div>
  </div>
</template>

<script>
import axios from "axios";

import Vue from "vue";
import { ActionSheet } from "vant";
import { Dialog } from "vant";
Vue.use(Dialog);

Vue.use(ActionSheet);
export default {
  components: {},
  data() {
    return {
      inspectionPartForm: {
        qualityInspectionId: "", //质量检查id
        projectId: "", //项目id
        rectificationTime: "", //整改期限
        examinationTime: "", //检查日期
        rectificationDepartmentName: "", //整改部门name
        rectificationDepartmentId: "", //整改部门_id
        rectificationPeopleId: "", //整改人id
        rectificationPeopleName: "", //整改人名称
        inspectedItem: "", //受检项
        inspectedPart: "", //受检部位
        status: "", //状态 0  正常 1 异常
        partCode: "", //部位编码
        rectificationPeoplePhone: "", //整改人手机号
        sequence: "", //序号
        dangerType: "", //隐患类别
        dangerGrade: "", //隐患等级
        dangerDesc: "", //隐患描述
        dangerAdvise: "", //整改建议
        attachmentIds: "", //隐患附件
        attachments: []
      },
      rectificationForm: {
        id: "", //主键
        inspectionPartId: "", //检查部位id
        qualityInspectionId: "", //质量检查id
        projectId: "", //项目id
        createId: "", //创建用户
        createTime: "", //创建时间
        updateId: "", //更新用户
        updateTime: "", //更新时间
        delState: "", // 0未删除 1已删除
        rectificationCompleteTime: "", //整改完成时间
        rectificationExplain: "", //整改说明
        attachmentIds: "" //整改附件
      },
      qualityInspectionForm: {
        inspectedUnit: "" //受检单位
      },
      dangerTypeDict: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.loadDict();
    this.getRectification();
  },
  methods: {
      dload(item) {
      api.download(
        {
          url: this.$YBS.imgUrlTranslation("imem/" + item.fileUrl),
          savePath: "fs://" + item.fileName,
          report: true,
          cache: true,
          allowResume: true
        },
        function(ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", ret);
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {
            });

            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },
    getRectification() {
      let self = this;
      this.$api.getRectification({ id: this.$route.query.id }).then(res => {
        self.rectificationForm = res; //整改单
        self.qualityInspectionForm = res.qualityInspection || {
          inspectedUnit: "" //受检单位
        };
        self.inspectionPartForm = res.inspectionPart || {
          qualityInspectionId: "", //质量检查id
          projectId: "", //项目id
          rectificationTime: "", //整改期限
          examinationTime: "", //检查日期
          rectificationDepartmentName: "", //整改部门name
          rectificationDepartmentId: "", //整改部门_id
          rectificationPeopleId: "", //整改人id
          rectificationPeopleName: "", //整改人名称
          inspectedItem: "", //受检项
          inspectedPart: "", //受检部位
          status: "", //状态 0  正常 1 异常
          partCode: "", //部位编码
          rectificationPeoplePhone: "", //整改人手机号
          sequence: "", //序号
          dangerType: "", //隐患类别
          dangerGrade: "", //隐患等级
          dangerDesc: "", //隐患描述
          dangerAdvise: "", //整改建议
          attachmentIds: "", //隐患附件
          attachments: []
        };
      });
    },
    loadDict() {
      this.$api.getDict({ type: "sys_danger_type" }).then(res => {
        this.dangerTypeDict = res;
      });
    },
    getDangerTypeDict(value) {
      return this.selectDictLabel(value, this.dangerTypeDict);
    },
    selectDictLabel(value, datas) {
      var actions = [];
      Object.keys(datas).some(key => {
        if (datas[key].dataValue == "" + value) {
          actions.push(datas[key].dataLabel);
          return true;
        }
      });

      return actions.join("");
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.hTphotos {
  width: 100%;
  padding-bottom: 5px;
  background-color: #fff;
  margin-bottom: 10px;
}
.table_sty {
  width: 100%;
  margin-bottom: 119px;
  background-color: #fff;
  .txt {
    display: flex;
    padding: 11px 10px;
  }
}
.btn {
  z-index: 999;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.suggestion {
  width: 100%;
  background-color: #fff;
  margin-bottom: 10px;
  .txt {
    display: flex;
    padding: 11px 10px;
    .txt_l {
      width: 150px;
      color: #4e5969;
      font-weight: 300;
    }
  }
  > div {
    padding: 5px 10px;
  }
  .uplod {
    width: 95%;
    min-height: 50px;
    .download {
      width: 95%;

      margin: 0 auto 10px;
    }
    // height: 20px;
    // display: flex;
    // justify-content: space-between;
  }
}
.box {
  width: 100%;
  text-align: center;
  line-height: 40px;
}
.van-cell {
  font-size: 15px;

  padding: 10px !important;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
</style>
