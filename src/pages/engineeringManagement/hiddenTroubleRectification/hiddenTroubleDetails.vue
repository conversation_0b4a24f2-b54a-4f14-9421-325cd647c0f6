<template>
  <div class="inner">
    <Header title="详情" @backFun="goBack"></Header>
    <div class="essential">
      <div class="txt"><i class="public"></i>基本信息</div>
      <div class="txt">
        <div class="txt_l">整改部门</div>
        <div>{{ inspectionPartForm.rectificationDepartmentName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改责任人</div>
        <div>{{ inspectionPartForm.rectificationPeopleName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">联系电话</div>
        <div>{{ inspectionPartForm.rectificationPeoplePhone }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">隐患等级</div>
        <div>{{ getDangerGradeListString(rectificationForm) }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">隐患描述</div>
        <div>{{ acceptanceItemsForm.dangerDesc }}</div>
      </div>
    </div>
    <div class="hTphotos">
      <div class="photos">
        <div class="title">
          <div>隐患照片</div>
        </div>
        <div  v-for="(rowitem, index) in inspectionPartForm.attachments" :key="index">
          <van-image width="100" height="100" @click="dkImg($YBS.imgUrlTranslation('imem/' + rowitem.fileUrl))" :src="$YBS.imgUrlTranslation('imem/' + rowitem.fileUrl)" />
        </div>
      </div>
    </div>
    <div class="essential">
      <div class="txt"><i class="public"></i>整改信息</div>
      <div class="txt">
        <div class="txt_l">整改建议</div>
        <div>{{ inspectionPartForm.dangerAdvise }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改期限</div>
        <div>{{ inspectionPartForm.rectificationTime }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">整改措施落实说明</div>
        <div>{{ rectificationForm.rectificationExplain }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">完成整改时间</div>
        <div>{{ rectificationForm.rectificationCompleteTime }}</div>
      </div>
    </div>
    <div class="hTphotos">
      <div class="photos">
        <div class="title">
          <div>附件</div>

          <div class="download" v-if="rectificationForm.attachments.length">
            <div
              style=" display: flex;
    justify-content: space-between;"
              v-for="(i, index) in rectificationForm.attachments"
              :key="index"
            >
              <div>{{ i.fileName }}</div>
              <div style="color:#3562db" @click="dload(i)">下载</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table_sty" style="height:130px">
      <div class="txt"><i class="public"></i>评审记录</div>
      <el-table :data="approvalList" border stripe>
        <el-table-column prop="fileApprovalPersonName" label="评审人" align="center"></el-table-column>
        <el-table-column prop="comment" label="评审意见" align="center"></el-table-column>
        <el-table-column prop="status" label="审批结果" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.status == 0 ? "待审批" : scope.row.status == 1 ? "通过" : scope.row.status == 2 ? "指定代办人" : scope.row.status == -1 ? "驳回" : "" }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="btn">
      <van-button style="width:40%" color="#3562db" @click="goRectification">整改单</van-button><van-button style="width:40%" color="#3562db" @click="goReply">回复单</van-button>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { Image as VanImage } from "vant";
import { Dialog } from "vant";

import { ImagePreview } from "vant";
Vue.use(Dialog);

Vue.use(VanImage);
export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  data() {
    return {
      dangerGradeList: [
        { dataLabel: "一级", dataValue: 1 },
        { dataLabel: "二级", dataValue: 2 },
        { dataLabel: "三级", dataValue: 3 }
      ],
      rectificationForm: {
        id: "", //主键
        inspectionPartId: "", //检查部位id
        qualityInspectionId: "", //质量检查id
        projectId: "", //项目id
        createId: "", //创建用户
        createTime: "", //创建时间
        updateId: "", //更新用户
        updateTime: "", //更新时间
        delState: "", // 0未删除 1已删除
        rectificationCompleteTime: "", //整改完成时间
        rectificationExplain: "", //整改说明
        attachmentIds: "" //整改附件
      },
      inspectionPartForm: {
        qualityInspectionId: "", //质量检查id
        projectId: "", //项目id
        rectificationTime: "", //整改期限
        examinationTime: "", //检查日期
        rectificationDepartmentName: "", //整改部门name
        rectificationDepartmentId: "", //整改部门_id
        rectificationPeopleId: "", //整改人id
        rectificationPeopleName: "", //整改人名称
        inspectedItem: "", //受检项
        inspectedPart: "", //受检部位
        status: "", //状态 0  正常 1 异常
        partCode: "", //部位编码
        rectificationPeoplePhone: "", //整改人手机号
        sequence: "", //序号
        dangerType: "", //隐患类别
        dangerGrade: "", //隐患等级
        dangerDesc: "", //隐患描述
        dangerAdvise: "", //整改建议
        attachmentIds: "", //隐患附件
        attachments: []
      },
      approvalList: [], //审批记录
      acceptanceItemsForm: {},
      completionAcceptanceManagementForm: {}
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getRectification();
  },
  methods: {
    dload(item) {
      api.download(
        {
          url: this.$YBS.imgUrlTranslation("imem/" + item.fileUrl),
          savePath: "fs://" + item.fileName,
          report: true,
          cache: true,
          allowResume: true
        },
        function(ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", ret);
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {});

            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },
    getRectification() {
      let self = this;
      this.$api.getRectification({ id: this.$route.query.id }).then(res => {
        self.rectificationForm = res; //整改单
        //检查单
        self.inspectionPartForm = res.inspectionPart || {
          qualityInspectionId: "", //质量检查id
          projectId: "", //项目id
          rectificationTime: "", //整改期限
          examinationTime: "", //检查日期
          rectificationDepartmentName: "", //整改部门name
          rectificationDepartmentId: "", //整改部门_id
          rectificationPeopleId: "", //整改人id
          rectificationPeopleName: "", //整改人名称
          inspectedItem: "", //受检项
          inspectedPart: "", //受检部位
          status: "", //状态 0  正常 1 异常
          partCode: "", //部位编码
          rectificationPeoplePhone: "", //整改人手机号
          sequence: "", //序号
          dangerType: "", //隐患类别
          dangerGrade: "", //隐患等级
          dangerDesc: "", //隐患描述
          dangerAdvise: "", //整改建议
          attachmentIds: "", //隐患附件
          attachments: []
        }; //整改部位
        self.approvalList = res.approvalList || []; //审批记录
        self.acceptanceItemsForm = res.acceptanceItems || {};
        self.completionAcceptanceManagementForm = res.completionAcceptanceManagement || {};
      });
    },
    getDangerGradeListString(row) {
      let value = "";
      if (row.type == 1) {
        value = row.inspectionPart.dangerGrade;
      }
      if (row.type == 2) {
        value = row.acceptanceItems.dangerGrade;
      }
      return this.selectDictLabel(value, this.dangerGradeList);
    },
    selectDictLabel(value, datas) {
      var actions = [];
      Object.keys(datas).some(key => {
        if (datas[key].dataValue == "" + value) {
          actions.push(datas[key].dataLabel);
          return true;
        }
      });

      return actions.join("");
    },
    // 整改单
    goRectification() {
      this.$router.push({
        path: "/rectificationSheet",
        query: {
          id: this.$route.query.id
        }
      });
    },
    // 回复单
    goReply() {
      this.$router.push({
        path: "/replySheet",
        query: {
          id: this.$route.query.id
        }
      });
    },
    dkImg(img) {
      ImagePreview([img]);
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  overflow: auto;
  .essential {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.hTphotos {
  width: 100%;
  min-height: 50px;
  background-color: #fff;
  margin-bottom: 10px;
  .photos {
    padding: 10px;
    .title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      .download {
        width: 95%;

        margin: 0 auto 10px;
      }
    }
  }
}
.table_sty {
  width: 100%;
  margin-bottom: 90px;
  background-color: #fff;
  .txt {
    display: flex;
    padding: 11px 10px;
  }
}
.btn {
  z-index: 999;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
/deep/ .el-table__header th {
  background-color: #e6effc; /* 设置表头背景颜色 */
  color: #1d2129; /* 设置表头文字颜色 */

  font-weight: 400;
}
</style>
