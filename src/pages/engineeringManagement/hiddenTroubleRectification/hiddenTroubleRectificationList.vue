<template>
  <div class="inner">
    <Header title="隐患整改" @backFun="goBack"></Header>
    <div v-if="list.length > 0" class="list">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in list" :key="item.id" class="listStyle" @click="skipList(item)">
              <div class="name">
                <div class="left">{{ item.projectName }}</div>

                <div>{{ item.status == "0" ? "待整改" : item.status == "1" ? "整改中" : item.status == "2" ? "已整改" : "" }}</div>
                <div><van-icon name="arrow" /></div>
              </div>
              <div class="txt">
                <div class="txt_l">整改责任人</div>
                <div>{{ item.peopleName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">整改期限</div>
                <div>{{ item.rectificationTime }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">整改等级</div>
                <div>{{ item.dangerGrade }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">整改人手机号</div>
                <div>{{ item.peoplePhone }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      page: 1,
      limit: 10,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      arr: [
        {
          name: "A项目",
          id: "1"
        },
        {
          name: "B项目",
          id: "2"
        }
      ]
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getProjectListById();
  },
  methods: {
    getProjectListById() {
      let data = {
        page: this.page,
        limit: this.limit,
        projectId: this.$route.query.id
      };
      this.$api.getProjectListById(data).then(res => {
        this.loading = false;
        res.list.forEach(item => {
          this.list.push(item);
        });
        if (this.list.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getByProjectIdList();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getByProjectIdList();
    },
    skipList(row) {
      this.$router.push({
        path: "/hiddenTroubleDetails",
        query: {
          id: row.rectificationId
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
   background-color: #f2f4f8;
  height: 100vh;
  overflow: hidden;
  height: 100%;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    .name {
      padding: 10px 15px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.list {
  height: calc(100% - 80px);
  overflow: auto;
}
.notList {
  position: relative;
  height: calc(100vh - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
