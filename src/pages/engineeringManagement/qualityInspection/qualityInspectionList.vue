<template>
  <div class="inner">
    <Header title="质量检查" @backFun="goBack"></Header>
    <div v-if="list.length > 0" class="list">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in list" :key="item.id" class="listStyle">
              <div @click="skipList(item)">
                <div class="name">
                  <div class="left"><img :src="img" alt="" />{{ item.projectName }}</div>
                  <div><van-icon name="arrow" /></div>
                </div>
                <div class="txt">
                  <div class="txt_l">受检单位</div>
                  <div>{{ item.inspectedUnit }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">巡检人</div>
                  <div>{{ item.checkingPeopleName }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">检查日期</div>
                  <div>{{ item.examinationTime }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">检查类型</div>
                  <div>{{ item.status == 0 ? "外部" : "内部" }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">异常数</div>
                  <div>{{ item.inspectionPartCount }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">整改数</div>
                  <div>{{ item.rectificationCount }}</div>
                </div>
              </div>

              <div class="btn" @click="Edit(item)" v-if="item.status != 1">
                <div>编辑</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import img from "@/assets/images/Frame 427318804.png";
export default {
  data() {
    return {
      list: [],
      page: 1,
      limit: 10,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      img
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getByProjectIdList();
  },
  methods: {
    getByProjectIdList() {
      let data = {
        page: this.page,
        limit: this.limit,
        projectId: this.$route.query.id
      };
      this.$api.getListByProjectId(data).then(res => {
        this.loading = false;
        res.list.forEach(item => {
          this.list.push(item);
        });
        if (this.list.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getByProjectIdList();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getByProjectIdList();
    },
    Edit(row) {
      this.$router.push({
        path: "/qualityInspectionEdit",
        query: {
          id: row.id
        }
      });
    },
    skipList(row) {
      this.$router.push({
        path: "/qualityInspectionDetails",
        query: {
          id: row.id
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  height: 100vh;
  overflow: hidden;
  .listStyle {
    width: 95%;
    // height: 332px;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    .name {
      padding: 10px 10px 5px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 70%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .txt {
      display: flex;
      padding: 11px 10px;
      .txt_l {
        width: 150px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}
.list {
  height: calc(100% - 80px);
  overflow: auto;
}
.btn {
  margin-top: 10px;
  display: flex;
  border-top: 1px solid #e5e6eb;
  > div {
    flex: 1;
    text-align: center;
    line-height: 40px;

    font-size: 14px;
    color: #86909c;
    font-weight: 400;
  }
}
img {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.notList {
  position: relative;
  height: calc(100vh - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
