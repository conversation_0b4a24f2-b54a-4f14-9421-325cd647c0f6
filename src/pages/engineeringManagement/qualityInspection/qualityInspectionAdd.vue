<template>
  <div class="inner">
    <Header title="添加质量检查" @backFun="goBack"></Header>

    <div style="width:100%;overflow-y:scroll;">
      <div class="listStyle">
        <div class="title"><i class="public"></i>基础信息</div>
        <van-field required v-model="formItem.projectName" placeholder="请选择项目" label="项目" @click="showProject = true" readonly right-icon="arrow" input-align="left" />
        <van-popup v-model="showProject" position="bottom">
          <van-picker value-key="name" show-toolbar :columns="projectList" @confirm="onDealing" @cancel="showProject = false" />
        </van-popup>
        <van-field required v-model="formItem.inspectedUnit" placeholder="请输入受检单位" label="受检单位" />
        <van-field
          required
          v-model="formItem.examinationTime"
          placeholder="请选择检查日期"
          label="检查日期"
          @click="showBeginDate"
          right-icon="arrow"
          input-align="left"
          readonly
        />
        <van-popup v-model="showPickerDate" position="bottom">
          <van-datetime-picker v-model="currentDate" type="date" @cancel="onCancelDate" @confirm="onConfirmDate" :min-date="minDate" :max-date="maxDate" :formatter="formatter" />
        </van-popup>
        <van-field required v-model="formItem.checkingPeopleName" placeholder="请输入巡检人" label="巡检人" />
        <van-field required name="radio" label="巡检班次">
          <template #input>
            <van-radio-group v-model="formItem.shift" direction="horizontal">
              <van-radio name="0">白班</van-radio>
              <van-radio name="1">夜班</van-radio>
            </van-radio-group>
          </template>
        </van-field>

        <van-field v-model="formItem.contractName" placeholder="请选择合同" label="合同" @click="openContract" readonly right-icon="arrow" input-align="left" />
        <van-popup v-model="showContract" position="bottom">
          <van-picker value-key="contractName" show-toolbar :columns="masterContract" @confirm="onContract" @cancel="showContract = false" />
        </van-popup>
      </div>

      <div class="skSty">
        <div class="title">
          <div><i class="public"></i>受检信息</div>
          <div class="topSelest" @click="add">
            <van-icon name="plus" />
            <p>添加</p>
          </div>
        </div>

        <div class="detailsStyle">
          <div class="box" v-for="(item, index) in formItem.inspectionPartList" :key="index">
            <van-swipe-cell>
              <div class="txt">
                <van-field v-model="item.inspectedPart" placeholder="请输入受检部位" label="受检部位" />
                <van-field v-model="item.inspectedItem" placeholder="请输入受检项" label="受检项" />
                <van-field name="radio" label="受检状态">
                  <template #input>
                    <van-radio-group v-model="item.status" direction="horizontal">
                      <van-radio :name="0">正常</van-radio>
                      <van-radio :name="1">异常</van-radio>
                    </van-radio-group>
                  </template>
                </van-field>
                <van-collapse v-model="activeName" v-if="item.status == 1 && item.status != ''">
                  <van-collapse-item title="异常" :name="index">
                    <div>
                      <div class="title">
                        <div>
                          检查部位
                        </div>
                        <div style="color:#3562db" @click="confirm(index)">
                          保存
                        </div>
                      </div>
                      <van-field
                        required
                        v-model="inspectionPartForm[index].rectificationDepartmentName"
                        placeholder="请选择整改部门"
                        label="整改部门"
                        @click="selectDepartment(index)"
                        readonly
                        right-icon="arrow"
                        input-align="left"
                      />

                      <van-field
                        required
                        v-model="inspectionPartForm[index].rectificationPeopleName"
                        placeholder="请选择整改责任人"
                        label="整改责任人"
                        @click="personLiable(index)"
                        readonly
                        right-icon="arrow"
                        input-align="left"
                      />
                      <van-field type="tel" v-model="inspectionPartForm[index].rectificationPeoplePhone" placeholder="请输入联系电话" label="联系电话" />
                      <van-field name="radio" label="隐患等级" required>
                        <template #input>
                          <van-radio-group v-model="inspectionPartForm[index].dangerGrade" direction="horizontal">
                            <van-radio name="1">一级</van-radio>
                            <van-radio name="2">二级</van-radio>
                            <van-radio name="3">三级</van-radio>
                          </van-radio-group>
                        </template>
                      </van-field>
                      <van-field name="radio" label="隐患类别" required>
                        <template #input>
                          <van-radio-group v-model="inspectionPartForm[index].dangerType" direction="horizontal">
                            <van-radio :name="item.dataValue" v-for="item in dangerTypeDict" :key="item.dataValue">{{ item.dataLabel }}</van-radio>
                          </van-radio-group>
                        </template>
                      </van-field>

                      <van-field
                        v-model="inspectionPartForm[index].rectificationTime"
                        placeholder="请选择整改日期"
                        label="整改日期"
                        @click="showBeginDate2(index)"
                        right-icon="arrow"
                        input-align="left"
                        readonly
                        required
                      />
                      <van-field
                        required
                        v-model="inspectionPartForm[index].dangerDesc"
                        rows="1"
                        autosize
                        label="隐患描述"
                        type="textarea"
                        placeholder="请输入隐患描述"
                        maxlength="200"
                        show-word-limit
                      />
                      <van-field
                        v-model="inspectionPartForm[index].dangerAdvise"
                        rows="1"
                        autosize
                        label="整改建议"
                        type="textarea"
                        placeholder="整改建议"
                        maxlength="200"
                        show-word-limit
                      />
                    </div>
                  </van-collapse-item>
                </van-collapse>
              </div>
              <template #right>
                <van-button square @click="DelRow(index)" text="删除" type="danger" class="delete-button" />
              </template>
            </van-swipe-cell>
          </div>
          <van-popup v-model="showPersonLiable" position="bottom">
            <van-picker value-key="userName" show-toolbar :columns="dealingList" @confirm="onPersonLiable" @cancel="showPersonLiable = false" />
          </van-popup>
          <van-popup v-model="showDept" position="bottom" @close="deptClose">
            <el-table
              height="calc(50vh - 80px)"
              :data="options"
              style="width: 100%;margin-bottom: 20px;"
              row-key="id"
              border
              :tree-props="{ children: 'child', hasChildren: 'hasChildren' }"
            >
              <el-table-column prop="name" label="机构名">
                <template slot-scope="scope">
                  <el-radio :label="scope.row.name" v-model="radio2" @change.native="handleSelectionChange(scope.$index, scope.row)"></el-radio>
                </template>
              </el-table-column>
            </el-table>
          </van-popup>
          <van-popup v-model="showPickerDate2" position="bottom">
            <van-datetime-picker
              v-model="currentDate"
              type="date"
              @cancel="onCancelDate"
              @confirm="onConfirmDate2"
              :min-date="minDate"
              :max-date="maxDate"
              :formatter="formatter"
            />
          </van-popup>
        </div>
      </div>
    </div>
    <div class="btn">
      <van-button style="width:45%" color="#3562db" @click="handleSubmit(0)">保存</van-button
      ><van-button style="width:45%" color="#3562db" @click="handleSubmit(1)">提交</van-button>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { SwipeCell } from "vant";

Vue.use(SwipeCell);
export default {
  data() {
    return {
      showPersonLiable: false,
      showDept: false,
      masterContract: [],
      showContract: false,
      currentDate: new Date(),
      value: "",
      value2: "",
      value3: "",
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      showPickerDate: false,
      showPickerDate2: false,

      showProject: false,
      projectList: [],
      activeName: [],
      formItem: {
        id: "", //主键
        contractId: "", //合同id
        projectId: "", //项目id
        examinationTime: "", //检查日期
        checkingPeopleId: "", //巡检人id
        checkingPeopleName: "", //巡检人名称
        inspectedUnit: "", //受检单位
        status: "", //状态 0  未提交 1 已巡检
        inspectionCode: "", //检查编码
        shift: "", //班次0白班1夜班
        contractName: "",
        projectName: "",
        inspectionPartList: [] //检查部位
      },
      dealingList: [],
      inspectionPartForm: [],
      dangerTypeDict: [],
      index: "",
      index2: "",
      index3: "",
      radio: "",
      radio2: "",
      options: [],
      deptRow: ""
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.loadDict();
    this.getDepartmentList();
    this.getProjectListBy();
  },
  methods: {
    getByProjectId(id) {
      this.$api.getByProjectId({ id: this.formItem.projectId }).then(res => {
        this.dealingList = res;
      });
    },
    selectDepartment(index) {
      this.index2 = index;
      this.showDept = true;
    },
    handleSelectionChange(index, row) {
      this.deptRow = row;
    },
    deptClose() {
      this.inspectionPartForm[this.index2].rectificationDepartmentName = this.deptRow.name;
      this.inspectionPartForm[this.index2].rectificationDepartmentId = this.deptRow.id;
    },
    confirm(index) {
      if (
        this.inspectionPartForm[index].rectificationDepartmentName == "" ||
        this.inspectionPartForm[index].rectificationPeopleName == "" ||
        this.inspectionPartForm[index].dangerGrade == "" ||
        this.inspectionPartForm[index].dangerType == "" ||
        this.inspectionPartForm[index].rectificationTime == "" ||
        this.inspectionPartForm[index].dangerDesc == ""
      )
        return $.toast("请确定填写完全", "text");
      this.inspectionPartForm[index].inspectedPart = this.formItem.inspectionPartList[index].inspectedPart;
      this.inspectionPartForm[index].inspectedItem = this.formItem.inspectionPartList[index].inspectedItem;
      this.inspectionPartForm[index].status = this.formItem.inspectionPartList[index].status;
      this.formItem.inspectionPartList[index] = this.inspectionPartForm[index];
      $.toast("保存成功", "text");
    },
    getDepartmentList() {
      this.$api.getDepartmentList({}).then(res => {
        this.options = res;
      });
    },
    onPersonLiable(value) {
      this.inspectionPartForm[this.index3].rectificationPeopleName = value.userName;
      this.inspectionPartForm[this.index3].rectificationPeopleId = value.userId;
      this.showPersonLiable = false;
    },
    onContract(value) {
      this.formItem.contractName = value.contractName;
      this.formItem.contractId = value.id;
      this.showContract = false;
    },
    personLiable(index) {
      this.index3 = index;
      if (this.formItem.projectName == "") return $.toast("请先选择项目", "text");
      this.getByProjectId();
      this.showPersonLiable = true;
    },
    openContract() {
      if (this.formItem.projectName == "") return $.toast("请先选择项目", "text");
      this.getcontractlist();
      this.showContract = true;
    },
    getcontractlist() {
      this.$api.getcontractlist({ projectId: this.formItem.projectId }).then(res => {
        this.masterContract = res.list;
      });
    },
    onConfirmDate() {
      this.formItem.examinationTime = `${this.value1}-${this.value2}-${this.value3}`; // 字符串拼接 结果入2020-07-03
      this.onCancelDate();
    },
    onConfirmDate2() {
      this.inspectionPartForm[this.index].rectificationTime = `${this.value1}-${this.value2}-${this.value3}`; // 字符串拼接 结果入2020-07-03
      this.onCancelDate();
    },

    // 日期组件自定义格式
    formatter(type, value) {
      if (type === "year") {
        this.value1 = value; // 可以拿到当前点击的数值
        return `${value}年`;
      } else if (type === "month") {
        this.value2 = value;
        return `${value}月`;
      }
      this.value3 = value;
      return `${value}日`;
    },
    showBeginDate() {
      this.showPickerDate = true;
    },
    showBeginDate2(index) {
      this.index = index;
      this.showPickerDate2 = true;
    },

    onCancelDate() {
      this.showPickerDate = false;
      this.showPickerDate2 = false;
    },
    onDealing(value) {
      this.formItem.projectName = value.name;
      this.formItem.projectId = value.id;
      this.showProject = false;
    },
    getProjectListBy() {
      let data = {
        page: 1,
        limit: 9999
      };
      this.$api.getNoList(data).then(res => {
        this.projectList = res.list;
      });
    },
    loadDict() {
      let self = this;

      //质保期单位
      this.$api.getDict({ type: "sys_danger_type" }).then(res => {
        this.dangerTypeDict = res;
      });
    },
    add() {
      let newRowData = {
        qualityInspectionId: this.formItem.id,
        projectId: "", //项目id
        rectificationTime: "", //整改期限
        examinationTime: "", //检查日期
        rectificationDepartmentName: "", //整改部门name
        rectificationDepartmentId: "", //整改部门_id
        rectificationPeopleId: "", //整改人id
        rectificationPeopleName: "", //整改人名称
        inspectedItem: "", //受检项
        inspectedPart: "", //受检部位
        status: 0, //状态 0  正常 1 异常
        partCode: "", //部位编码
        rectificationPeoplePhone: "", //整改人手机号
        sequence: "", //序号
        dangerType: "", //隐患类别
        dangerGrade: "", //隐患等级
        dangerDesc: "", //隐患描述
        dangerAdvise: "", //整改建议
        attachmentIds: "", //隐患附件
        attachments: []
      };
      this.formItem.inspectionPartList.push(newRowData);
      let inspectionPartForm = {
        qualityInspectionId: "", //质量检查id
        projectId: "", //项目id
        rectificationTime: "", //整改期限
        examinationTime: "", //检查日期
        rectificationDepartmentName: "", //整改部门name
        rectificationDepartmentId: "", //整改部门_id
        rectificationPeopleId: "", //整改人id
        rectificationPeopleName: "", //整改人名称
        inspectedItem: "", //受检项
        inspectedPart: "", //受检部位
        status: "", //状态 0  正常 1 异常
        partCode: "", //部位编码
        rectificationPeoplePhone: "", //整改人手机号
        sequence: "", //序号
        dangerType: "", //隐患类别
        dangerGrade: "", //隐患等级
        dangerDesc: "", //隐患描述
        dangerAdvise: "", //整改建议
        attachmentIds: "", //隐患附件
        attachments: []
      };
      this.inspectionPartForm.push(inspectionPartForm);
    },
    DelRow(index) {
      this.formItem.inspectionPartList.splice(index, 1);
      this.inspectionPartForm.splice(index, 1);
    },
    handleSubmit(status) {
      let self = this;
      if (
        this.formItem.projectName == "" ||
        this.formItem.inspectedUnit == "" ||
        this.formItem.examinationTime == "" ||
        this.formItem.checkingPeopleName == "" ||
        this.formItem.shift == ""
      )
        return $.toast("请确定填写完全", "text");

      if (!self.formItem.inspectionPartList || self.formItem.inspectionPartList.length < 1) {
        $.toast("未添加检查项,请确认后再提交", "text");
        return;
      }
      var params = this.formItem;
      params.status = status;
      console.log(params, "params");
      var inspectedPart = false; //受检部位
      var inspectedItem = false; //受检项
      //异常表单中有内容是空
      var inspectionPartEmpty = false;
      params.inspectionPartList.forEach((v, i) => {
        if (v.inspectedPart == null || v.inspectedPart == "") {
          inspectedPart = true;
        }
        if (v.inspectedItem == null || v.inspectedItem == "") {
          inspectedItem = true;
        }

        if (v.status == 1 && (v.rectificationTime == null || v.rectificationTime == "")) {
          inspectionPartEmpty = true;
        }
      });
      if (inspectedPart) {
        $.toast("受检部位不能为空", "text");

        this.$Message.error("受检部位不能为空");
        return;
      }
      if (inspectedItem) {
        $.toast("受检项不能为空", "text");

        this.$Message.error("受检项不能为空");
        return;
      }

      if (inspectionPartEmpty) {
        $.toast("未请填写并保存异常信息", "text");

        return;
      }
      this.$api
        .getQualityInspectionAdd(params)
        .then(res => {
          this.$router.go(-1);
          $.toast(res);
        })
        .catch(res => {
          if (res.data.code == "500") {
            $.toast(res.data.msg);
          }
        });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  .listStyle {
    width: 100%;
    background-color: #fff;
    margin-bottom: 10px;
    .title {
      padding: 10px;
    }
  }
}
.statistics {
  width: 95%;
  height: 77px;
  background-color: #f7f8fa;
  margin: 0 auto;
  border-radius: 5px;
  > div {
    width: 100%;
    height: 50%;
    display: flex;
    > div {
      flex: 1;
      text-align: center;
      line-height: 39px;
    }
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #1d2129;
    }
    .lable {
      color: #86909c;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.skSty {
  background-color: #fff;
  width: 100%;
  margin-bottom: 10px;
  .title {
    display: flex;
    justify-content: space-between;
    padding: 10px;
  }
}
.detailsStyle {
  width: 100%;
  background-color: #fff;
  margin-bottom: 60px;
  .box {
    // padding-left: 10px;
    border-radius: 5px;
    padding-bottom: 5px;
    .txt {
      width: 95%;
      margin: 0 auto;
      border-radius: 5px;
      background-color: #f7f8fa;
      // padding: 0 10px;
      .van-cell {
        background-color: #f7f8fa;
      }
    }
  }
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}

.topSelest {
  display: flex;
  align-items: center;
  color: #3562db;
}
.delete-button {
  height: 100%;
}
.bom {
  margin-bottom: 60px !important;
}
/deep/ .van-collapse-item__content {
  padding: 0 !important;
}
.public {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 5px;
  background: #3562db;
}
</style>
