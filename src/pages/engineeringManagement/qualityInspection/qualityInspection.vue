<template>
  <div class="inner">
    <Header title="质量检查" @backFun="goBack">
      <div class="topSelest" @click="add">
        <van-icon name="plus" />
        <p>添加</p>
      </div>
    </Header>

    <div v-if="list.length > 0" class="list">
      <div class="title">
        质量检查数量
      </div>
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in list" :key="item.id" class="listStyle" @click="skipList(item)">
              <div>
                <div class="left"><img :src="img" alt="" /> {{ item.projectName }}</div>
                <div>{{item.qualityCount}}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import img from "@/assets/images/Frame 427318804.png";
export default {
  data() {
    return {
      list: [],
      page: 1,
      limit: 10,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      img,
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getByProjectList();
  },
  methods: {
    getByProjectList() {
      let data = {
        page: this.page,
        limit: this.limit
      };
      this.$api.getListByProject(data).then(res => {
        this.loading = false;
        res.list.forEach(item => {
          this.list.push(item);
        });
        if (this.list.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getByProjectList();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getByProjectList();
    },
    add() {
      this.$router.push({
        path: "/qualityInspectionAdd"
      });
    },
    skipList(row) {
      this.$router.push({
        path: "/qualityInspectionList",
        query: {
          id: row.projectId
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  height: 100vh;
  overflow: hidden;
  .topSelest {
    display: flex;
    align-items: center;
    color: #3562db;
  }
  .title {
    margin: 2.5% 0 0 2.5%;
    color: #1d2129;
    font-weight: bold;
    font-size: 16px;
  }
  .listStyle {
    width: 95%;
    height: 80px;
    background-color: #fff;
    border-radius: 5px;
    margin: 10px auto;
    > div {
      padding: 0 15px;
      line-height: 80px;
      display: flex;
      justify-content: space-between;
      .left {
        width: 85%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
    }
  }
}
.list {
  height: calc(100% - 80px);
  overflow: auto;
}
.notList {
  position: relative;
  height: calc(100vh - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
