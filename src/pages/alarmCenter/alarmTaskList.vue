<template>
  <div class="inner">
    <Header title="任务列表" @backFun="goBack"></Header>
    <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <div v-for="(item, index) in taskList" :key="index" class="listStyle">
          <div class="name">
            <div>{{ item.taskName }}</div>
            <div class="state">
              <span class="taskStatus" :style="item.taskStatus == 1 ? 'background: #ffece8;color:#f53f3f' : 'background: #e8ffea;color: #00b42a;'">
                {{ item.taskStatus == 1 ? "未执行" : "已执行" }}</span
              >
            </div>
          </div>
          <div class="txt">
            <div class="txt_l">任务类型</div>
            <div class="txt_r">{{ item.taskType }}</div>
          </div>
          <div class="txt">
            <div class="txt_l">执行人员</div>
            <div class="txt_r">{{ item.executionPersonName }}</div>
          </div>
          <div class="txt">
            <div class="txt_l">执行时间</div>
            <div class="txt_r">{{ item.executionTime }}</div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      taskList: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getInspectionTaskVoPage();
  },
  methods: {
    onRefresh() {
      this.pageNo = 1;
      this.finished = false;
      this.loading = true;
      this.taskList = [];
      this.getInspectionTaskVoPage();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.pageNo++;
      this.getInspectionTaskVoPage();
    },
    getInspectionTaskVoPage() {
      let alarmDetails = this.$route.query.alarmDetails;
      let data = {
        alarmDeviceId: alarmDetails.alarmDeviceId,
        alarmStartTime: alarmDetails.alarmStartTime,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      };
      this.$api.getInspectionTaskVoPage(data).then(res => {
        this.loading = false;
        res.records.forEach(item => {
          this.taskList.push(item);
        });
        if (this.taskList.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  width: 100vw;
  // height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
      > div:nth-child(1) {
        width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .state {
        .taskStatus {
          padding: 2px 8px;
          border-radius: 4px;
        }
      }
    }

    .txt {
      display: flex;
      padding: 7px 10px;

      .txt_l {
        margin: auto 0;
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
}
</style>
