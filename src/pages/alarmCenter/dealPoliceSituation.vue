<template>
  <div class="inner">
    <Header title="处理警情" @backFun="goBack"></Header>
    <van-field readonly required right-icon="arrow" v-model="policeTypeName" label="警情类型" placeholder="请选择" @click="openPolice" />
    <van-popup v-model="showPolice" position="bottom">
      <van-picker value-key="name" show-toolbar :columns="policeTypeList" @confirm="onPolice" @cancel="showPolice = false" />
    </van-popup>
    <van-field type="textarea" v-model="message" label="警情说明" placeholder="请输入说明,限制100字" rows="3" autosize maxlength="100" show-word-limit />
    <div class="accessory">
      <div style="display: flex; justify-content: space-between">
        <div>附件 ({{ recordList.length }})</div>
        <!-- <div style="font-size: 12px; color: #646566">最大不超过20MB</div> -->
      </div>
      <div v-for="(item, index) in recordList" :key="index" class="listStyle">
        <div class="name">
          <div class="state">
            <span> {{ item.name }}</span>
          </div>
          <div @click="deleteFile(item.url)">
            <van-icon name="delete" size="20px" />
          </div>
        </div>
      </div>
      <div class="uploader-container">
        <van-uploader accept=".doc,.docx,.xls,.xlsx,.pdf/*" :max-size="maxSize * 1024 * 1024" :after-read="afterRead">
          <span style="color: #3563dc"><van-icon name="plus" />上传文件</span>
        </van-uploader>
      </div>
    </div>
    <upload class="bottom" style="position: relative" ref="imgs" @getImg="getImg" @delImg="delImg"></upload>
    <div style="height: 50px"></div>
    <div class="btn">
      <van-button style="width: 45%" color="#E6EFFC" class="custom-button-text-color" @click="dischargeAlarm(1)">解除并存为经典</van-button
      ><van-button style="width: 45%" color="#3562db" @click="dischargeAlarm(0)">解除报警</van-button>
    </div>
  </div>
</template>
<script>
import upload from "./components/upload.vue";
export default {
  components: {
    upload
  },
  data() {
    return {
      maxSize: 20,
      fileList: [],
      recordList: [],
      showPolice: false,
      policeTypeName: "",
      policeTypeId: "",
      message: "",
      imagesList: [],
      imgFileList: [],
      policeTypeList: [
        {
          name: "真实报警",
          id: 1
        },
        {
          name: "误报",
          id: 2
        },
        {
          name: "演练",
          id: 3
        },
        {
          name: "调试",
          id: 4
        }
      ]
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    //解除报警
    dischargeAlarm(classic) {
      if (this.policeTypeName == "") return this.$toast.fail("请选择警情类型");
      let data = {
        alarmAffirm: this.policeTypeId,
        classic: classic,
        dispatch: 0,
        alarmId: this.$route.query.alarmId,
        operationUrl: JSON.stringify([...this.recordList, ...this.imgFileList])
      };
      console.log(data, "datadatadatadatadata");
      this.$api.handleAlarmAffirmById(data).then(res => {
        this.$toast.success("操作成功");
        this.goBack();
      });
    },
    deleteFile(value) {
      let fileArr = this.recordList.filter(i => i.url !== value);
      this.recordList = fileArr;
    },
    afterRead(files) {
      console.log("files", files);
      this.fileList.forEach(i => {
        return (i.status = "uploading");
      }); // 使用formdata上传
      const params = new FormData();
      params.append("file", files.file);
      this.subImg(params);
    },
    subImg(params) {
      this.axios
        .post(__PATH.ONESTOP + "/minio/upload", params, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          console.log("uploadres", res);
          if ((res.data.code = "200")) {
            const item = res.data.data.picUrl;
            this.recordList.push({
              name: res.data.data.name,
              url: item
            });

            this.fileList.forEach(i => {
              return (i.status = "done");
            });
          }
        });
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    getImg(img, imgFileList) {
      this.imgFileList = imgFileList;

      this.imagesList.push(img);
    },
    delImg(url) {
      this.imgFileList = this.imgFileList.filter(item => {
        return item.url != url;
      });
      this.imagesList = this.imagesList.filter(item => {
        return item != url;
      });
    },
    openPolice() {
      this.showPolice = true;
    },
    onPolice(value) {
      console.log(value, "---------");
      this.policeTypeName = value.name;
      this.policeTypeId = value.id;
      this.showPolice = false;
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100vw;
  height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .accessory {
    overflow: auto;
    padding: 15px;
    background-color: #fff;
    .uploader-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
    }
  }
  .listStyle {
    width: 100%;
    background-color: #f2f4f9;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      line-height: 25px;
      align-items: center;
      justify-content: space-between;
    }
    .state {
      .alarmStatus {
        padding: 2px 8px;
        border-radius: 4px;
      }
      .alarmStatus0 {
        background: #ffece8;
        color: #f53f3f;
      }
      .alarmStatus1 {
        background: #fff7e8;
        color: #ff7d00;
      }
      .alarmStatus2 {
        background: #e8ffea;
        color: #00b42a;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .alarmLevel0 {
        color: #00b42a;
      }
      .alarmLevel1 {
        color: #3562db;
      }
      .alarmLevel2 {
        color: #ff7d00;
      }
      .alarmLevel3 {
        color: #f53f3f;
      }
      .txt_l {
        margin: auto 0;
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.custom-button-text-color {
  color: #3562db !important;
}
</style>
