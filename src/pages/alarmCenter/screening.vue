<template>
  <div class="inner">
    <Header title="报警筛选" @backFun="goBack"></Header>
    <van-search
      v-model="form.appUnion"
      background="#fff"
      clearable
      :show-action="true"
      placeholder="搜索报警ID,报警对象,工单ID"
      @search="searchAlarmList"
      @clear="searchCancel"
      @input="appUnionInput"
    >
      <div slot="action" @click="sreening" style="display: flex; flex-direction: column">
        <van-icon name="search" size="22px" />
        <span style="font-size: 12px">筛选</span>
      </div>
    </van-search>
    <div v-if="alarmList.length > 0" class="alarmList">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width: 100%">
            <div v-for="item in alarmList" :key="item.id" class="listStyle" @click="goDetails(item)">
              <div class="name">
                <div>{{ item.alarmId }}</div>
                <div class="state">
                  <span :class="['alarmStatus', `alarmStatus${item.alarmStatus}`]"> {{ alarmType(item.alarmStatus) }}</span>
                  <span><van-icon name="arrow" /></span>
                </div>
              </div>
              <div class="txt">
                <div class="txt_l">报警时间</div>
                <div class="txt_r">{{ item.alarmStartTime }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警级别</div>
                <div :class="[`alarmLevel${item.alarmLevel}`]">{{ alarmLevel(item.alarmLevel) }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警系统</div>
                <div class="txt_r">{{ item.projectName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警类型</div>
                <div class="txt_r">{{ item.alarmType }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警位置</div>
                <div class="txt_r">{{ item.completeRegionName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警对象</div>
                <div class="txt_r">{{ item.alarmObjectName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警描述</div>
                <div class="txt_r">{{ item.alarmDetails }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
    <van-action-sheet v-model="actionShow" title="报警筛选">
      <div class="screening">
        <div class="grade">
          <div class="title">报警等级</div>
          <div class="choose">
            <van-radio-group v-model="form.alarmLevel">
              <van-button :color="form.alarmLevel == '0' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="0" icon-size="14px" checked-color="#3562db"><span :style="form.alarmLevel == '0' ? 'color:#3562db' : ''">通知</span></van-radio></van-button
              >
              <van-button :color="form.alarmLevel == '1' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="1" icon-size="14px" checked-color="#3562db"><span :style="form.alarmLevel == '1' ? 'color:#3562db' : ''">一般</span></van-radio></van-button
              >
              <van-button :color="form.alarmLevel == '2' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="2" icon-size="14px" checked-color="#3562db"><span :style="form.alarmLevel == '2' ? 'color:#3562db' : ''">重要</span></van-radio></van-button
              >
              <van-button :color="form.alarmLevel == '3' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="3" icon-size="14px" checked-color="#3562db"><span :style="form.alarmLevel == '3' ? 'color:#3562db' : ''">紧急</span></van-radio></van-button
              >
            </van-radio-group>
          </div>
        </div>
        <div class="alarmTime">
          <div>报警时间</div>
          <div class="time">
            <van-field placeholder="选择开始时间" readonly @click="openStart" v-model="form.startTime" />
            <van-field placeholder="选择结束时间" readonly @click="openEnd" v-model="form.endTime" />
            <van-popup position="bottom" v-model="startShow"
              ><van-datetime-picker v-model="currentDate" type="date" :min-date="minDate" @confirm="startConfirm" @cancel="onCancelStartDate" :formatter="formatter"
            /></van-popup>
            <van-popup position="bottom" v-model="endShow"
              ><van-datetime-picker v-model="currentDate" type="date" :min-date="minDate" :formatter="formatter" @confirm="endConfirm" @cancel="onCancelEndDate"
            /></van-popup>
          </div>
        </div>
        <van-field readonly right-icon="arrow" v-model="positionName" label="报警位置" placeholder="请选择" @click="openPosition" />
        <van-popup v-model="showPosition" position="bottom">
          <div style="height: 400px" class="personnel">
            <div class="breadcrumb">
              <el-breadcrumb separator-class="el-icon-arrow-right">
                <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index"
                  ><i @click="breadcrumbClick(index)" :style="index == breadcrumbList.length - 1 ? 'color:#3562DB' : ''">{{ item }}</i></el-breadcrumb-item
                >
              </el-breadcrumb>
              <div v-for="item in hospitalList" :key="item.id" @click="Down(item)">
                <div class="list">
                  <span>{{ item.ssmName }}</span>
                  <span><van-icon name="arrow" /></span>
                </div>
              </div>
            </div>
          </div>
        </van-popup>
        <van-field readonly right-icon="arrow" v-model="projectName" label="报警系统" placeholder="请选择" @click="openSystem" />
        <van-popup v-model="showSystem" position="bottom">
          <van-picker value-key="thirdSystemName" show-toolbar :columns="alarmSystemList" @confirm="onSystem" @cancel="showSystem = false" />
        </van-popup>
        <van-field readonly right-icon="arrow" v-model="alarmTypeName" label="报警类型" placeholder="请选择" @click="openAlarmType" />
        <van-popup v-model="showAlarmType" position="bottom">
          <van-picker value-key="thirdName" show-toolbar :columns="alarmTypeList" @confirm="onAlarmType" @cancel="showAlarmType = false" />
        </van-popup>
        <div class="grade">
          <div class="title">处理状态</div>
          <div class="choose">
            <van-radio-group v-model="form.alarmStatusAll">
              <van-button :color="form.alarmStatusAll == '0' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="0" icon-size="14px" checked-color="#3562db"><span :style="form.alarmStatusAll == '0' ? 'color:#3562db' : ''">未处理</span></van-radio></van-button
              >
              <van-button :color="form.alarmStatusAll == '1' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="1" icon-size="14px" checked-color="#3562db"><span :style="form.alarmStatusAll == '1' ? 'color:#3562db' : ''">处理中</span></van-radio></van-button
              >
              <van-button :color="form.alarmStatusAll == '2' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="2" icon-size="14px" checked-color="#3562db"><span :style="form.alarmStatusAll == '2' ? 'color:#3562db' : ''">已处理</span></van-radio></van-button
              >
            </van-radio-group>
          </div>
        </div>
        <div class="grade">
          <div class="title">警情类型</div>
          <div class="choose">
            <van-radio-group v-model="form.alarmAffirm">
              <van-button :color="form.alarmAffirm == '0' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="0" icon-size="14px" checked-color="#3562db"
                  ><span :style="form.alarmAffirm == '0' ? 'color:#3562db' : ''">未确认报警</span></van-radio
                ></van-button
              >
              <van-button :color="form.alarmAffirm == '1' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="1" icon-size="14px" checked-color="#3562db"><span :style="form.alarmAffirm == '1' ? 'color:#3562db' : ''">真实报警</span></van-radio></van-button
              >
              <van-button :color="form.alarmAffirm == '2' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="2" icon-size="14px" checked-color="#3562db"><span :style="form.alarmAffirm == '2' ? 'color:#3562db' : ''">误报</span></van-radio></van-button
              >
              <van-button :color="form.alarmAffirm == '3' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="3" icon-size="14px" checked-color="#3562db"><span :style="form.alarmAffirm == '3' ? 'color:#3562db' : ''">演练</span></van-radio></van-button
              >
              <van-button :color="form.alarmAffirm == '4' ? '#E6EFFC' : '#F8F9FA'" size="small"
                ><van-radio name="4" icon-size="14px" checked-color="#3562db"><span :style="form.alarmAffirm == '4' ? 'color:#3562db' : ''">调试</span></van-radio></van-button
              >
            </van-radio-group>
          </div>
        </div>
      </div>
      <div class="btn">
        <van-button style="width: 45%" color="#E6EFFC" class="custom-button-text-color" @click="reset">重置</van-button
        ><van-button style="width: 45%" color="#3562db" @click="confirm">确认</van-button>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import YBS from "@/assets/utils/utils.js";
import Vue from "vue";
import { Breadcrumb, BreadcrumbItem } from "element-ui";
Vue.use(Breadcrumb);
Vue.use(BreadcrumbItem);
export default {
  data() {
    return {
      breadcrumbList: [],
      breadcrumLocationList: [], //存储面包屑节点的内容，方便回显

      form: {
        appUnion: "",
        alarmLevel: "", //报警级别
        startTime: "",
        endTime: "",
        alarmSpaceId: "", //报警位置
        projectCode: "", //系统类型
        alarmType: "", //报警类型
        alarmStatusAll: "", //处理状态
        alarmAffirm: "" //警情类型
      },
      showAlarmType: false,
      showSystem: false,
      startShow: false,
      endShow: false,
      minHour: 10,
      maxHour: 20,
      minDate: new Date(2023, 10, 1),
      currentDate: new Date(),
      actionShow: false,
      alarmList: [],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      pageSize: 10,
      total: 0,
      showPosition: false,
      treeList: [],
      hospitalList: [],
      positionName: "",
      alarmSystemList: [],
      projectName: "",
      alarmTypeName: "",
      alarmTypeList: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getAlarmList();
    this.getTree();
    this.getAlarmThirdSystemList();
  },
  methods: {
    searchAlarmList() {
      this.current = 1;
      this.alarmList = [];
      this.getAlarmList();
    },
    openAlarmType() {
      if (!this.projectName) return this.$toast.fail("请先选择报警系统");
      this.showAlarmType = true;
    },
    onAlarmType(value) {
      this.alarmTypeName = value.thirdName;
      this.form.alarmType = value.id;
      this.showAlarmType = false;
    },
    onSystem(value) {
      this.projectName = value.thirdSystemName;
      this.form.projectCode = value.thirdSystemCode;
      this.showSystem = false;
      this.$api.queryFieldsConfigList({ thirdSystemCode: value.thirdSystemCode }).then(res => {
        this.alarmTypeList = res;
      });
    },
    openSystem() {
      this.showSystem = true;
    },
    goDetails(row) {
      this.$router.push({
        path: "/alarmDetails",
        query: {
          alarmId: row.alarmId
        }
      });
    },
    //获取报警类型
    getAlarmThirdSystemList() {
      this.$api.getAlarmThirdSystemList().then(res => {
        this.alarmSystemList = res;
      });
    },
    Down(item) {
      this.hospitalList = item.children;
      this.breadcrumLocationList.push(item.children);
      this.breadcrumbList.push(item.ssmName);
      if (item.ssmId) {
        this.alarmSpaceId = item.ssmId;
        this.positionName = item.ssmName;
        this.breadcrumbList = [];
        this.breadcrumLocationList = [];
        this.getTree();
        this.showPosition = false;
      } else if (!item.children) {
        let treeIdArr = this.ListTree(this.treeList, item.pid);
        treeIdArr.push(item.id);
        let data = {
          size: 999,
          current: 1,
          simCode: treeIdArr.toString(),
          functionDictId: ""
        };
        this.$api.spaceList(data).then(res => {
          this.hospitalList = res.records;
        });
      }
    },
    breadcrumbClick(index) {
      if (index == 0) {
        this.breadcrumbList = [];
        this.sliceArr(index);
        this.getTree();
      } else {
        this.sliceArr(index);
        this.hospitalList = this.breadcrumLocationList[index];
      }
    },
    sliceArr(index) {
      let arr = this.breadcrumbList;
      this.breadcrumbList = arr.slice(0, index + 1);
    },
    openPosition() {
      this.showPosition = true;
    },
    getTree() {
      this.$api.structureTree().then(res => {
        this.treeList = res;
        this.hospitalList = YBS.transData(res, "id", "pid", "children");
      });
    },
    // 日期组件自定义格式
    formatter(type, value) {
      if (type === "year") {
        this.value1 = value;
        return `${value}年`;
      } else if (type === "month") {
        this.value2 = value;
        return `${value}月`;
      }
      this.value3 = value;
      return `${value}日`;
      // if (type === "year") {
      //   this.value1 = value;
      //   return `${value}年`;
      // } else if (type === "month") {
      //   this.value2 = value;
      //   return `${value}月`;
      // } else if (type === "day") {
      //   this.value3 = value;
      //   return `${value}日`;
      // } else if (type === "hour") {
      //   this.value4 = value;
      //   return `${value}时`;
      // } else {
      //   this.value5 = value;
      //   return `${value}分`;
      // }
    },
    startConfirm() {
      this.form.startTime = `${this.value1}-${this.value2}-${this.value3}`;
      this.onCancelStartDate();
    },
    endConfirm() {
      this.form.endTime = `${this.value1}-${this.value2}-${this.value3}`;
      this.onCancelEndDate();
    },
    onCancelStartDate() {
      this.startShow = false;
    },
    onCancelEndDate() {
      this.endShow = false;
    },
    openStart() {
      this.startShow = true;
    },
    openEnd() {
      this.endShow = true;
    },
    alarmType(alarmState) {
      //alarmState 0.未处理 1.处理中 2.已处理
      const alarmStatusMap = {
        0: "未处理",
        1: "处理中",
        2: "已处理"
      };
      if (alarmState < 0 || alarmState > 2) {
        return "";
      }

      return alarmStatusMap[alarmState];
    },
    alarmLevel(alarmLevel) {
      const alarmLevelMap = {
        0: "提示",
        1: "一般",
        2: "较重",
        3: "严重"
      };
      if (alarmLevel < 0 || alarmLevel > 3) {
        return "";
      }

      return alarmLevelMap[alarmLevel];
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.alarmList = [];
      this.getAlarmList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.current++;
      this.getAlarmList();
    },
    confirm() {
      this.current = 1;
      this.alarmList = [];
      this.getAlarmList();
      this.actionShow = false;
    },
    reset() {
      this.current = 1;
      this.alarmList = [];
      this.form.alarmLevel = "";
      this.form.startTime = "";
      this.form.endTime = "";
      this.form.projectCode = "";
      this.form.alarmType = "";
      this.form.alarmStatusAll = "";
      this.form.alarmAffirm = "";
      this.form.alarmSpaceId = "";
      this.positionName = "";
      this.projectName = "";
      this.alarmTypeName = "";
      this.getAlarmList();
      this.actionShow = false;
    },
    getAlarmList() {
      let data = {
        timeOrType: 3,
        pageSize: this.pageSize,
        pageNo: this.current,
        alarmStatus: "",
        ...this.form
      };
      this.$api
        .selectAlarmRecordAll(data)
        .then(res => {
          this.loading = false;
          res.records.forEach(item => {
            this.alarmList.push(item);
          });
          if (this.alarmList.length >= res.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        })
    },
    appUnionInput(value) {
      if (!value) {
        this.form.appUnion = "";
        this.current = 1;
        this.alarmList = [];
        this.getAlarmList();
      }
    },
    // 取消搜索
    searchCancel() {
      this.form.appUnion = "";
      this.current = 1;
      this.alarmList = [];
      this.getAlarmList();
    },
    sreening() {
      console.log("点击了搜索--------");
      this.actionShow = true;
    },
    ListTree(list, rootid) {
      var arr = [];
      list.forEach(item => {
        if (item.id === rootid) {
          arr.push(item.id);
          const newArr = this.ListTree(list, item.pid);
          if (newArr) {
            arr.unshift(...newArr);
          }
        }
      });
      return arr;
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
  .van-search {
    /deep/ .van-field__control {
      text-align: left !important;
    }
  }
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
    }
    .state {
      .alarmStatus {
        padding: 2px 8px;
        border-radius: 4px;
      }
      .alarmStatus0 {
        background: #ffece8;
        color: #f53f3f;
      }
      .alarmStatus1 {
        background: #fff7e8;
        color: #ff7d00;
      }
      .alarmStatus2 {
        background: #e8ffea;
        color: #00b42a;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .alarmLevel0 {
        color: #00b42a;
      }
      .alarmLevel1 {
        color: #3562db;
      }
      .alarmLevel2 {
        color: #ff7d00;
      }
      .alarmLevel3 {
        color: #f53f3f;
      }
      .txt_l {
        margin: auto 0;
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
  .alarmList {
    height: calc(100% - 120px);
    overflow: auto;
  }
  .notList {
    position: relative;
    height: calc(100% - 1.44rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
.van-search__action {
  line-height: 18px;
}
.screening {
  padding: 0 15px;
  .grade {
    .title {
      line-height: 30px;
    }
    .choose {
      .van-radio-group {
        display: flex;
        // justify-content: space-around;
        flex-wrap: wrap;
        .van-button {
          // padding: 10px 25px;
          margin: 0 5px 10px;
        }
      }
    }
  }
  .alarmTime {
    .time {
      display: flex;
      justify-content: space-between;
      /deep/ .van-field__control {
        text-align: center !important;
      }
      .van-field {
        // flex: 1;
        width: 45%;
      }
    }
  }
}
/deep/ .van-field__control {
  text-align: right !important;
}
.breadcrumb {
  padding: 10px;
  .el-breadcrumb__item {
    // max-width: 50px;
    // white-space: nowrap;
    //   overflow: hidden;
    //   text-overflow: ellipsis;
  }
}
.personnel {
  padding: 5px 10px;
  > div {
    .list {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #e5e6eb;
      line-height: 40px;
    }
  }
}
.el-breadcrumb__item {
  max-width: 70px; /* 或你需要的宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.van-cell {
  padding: 10px 0 !important;
}
.btn {
  margin-top: 10px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  padding-bottom: 15px;
}
.custom-button-text-color {
  color: #3562db !important;
}
</style>
