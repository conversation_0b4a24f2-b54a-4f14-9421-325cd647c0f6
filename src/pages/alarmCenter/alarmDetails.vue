<template>
  <div class="inner">
    <Header title="报警详情" @backFun="goBack"></Header>
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left"><span class="lineTitle"></span><span>详细信息</span></div>
            <div class="state">
              <span :class="['alarmStatus', `alarmStatus${alarmRecord.alarmStatus}`]"> {{ alarmType(alarmRecord.alarmStatus) }}</span>
            </div>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警级别：</span>
            <span :class="[`alarmLevel${alarmRecord.alarmLevel}`]">{{ alarmLevel(alarmRecord.alarmLevel) }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警时间：</span>
            <span class="list-itemContent">{{ alarmRecord.alarmStartTime }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警ID：</span>
            <span class="list-itemContent">{{ alarmRecord.alarmId }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警系统：</span>
            <span class="list-itemContent">{{ alarmRecord.projectName }}</span>
          </div>

          <div class="list-item">
            <span class="list-itemTitle">报警类型：</span>
            <span class="list-itemContent">{{ alarmRecord.alarmType }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警位置：</span>
            <span class="list-itemContent">{{ alarmRecord.completeRegionName }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警对象：</span>
            <span class="list-itemContent">{{ alarmRecord.alarmObjectName }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警设备：</span>
            <span class="list-itemContent">{{ alarmRecord.alarmDeviceName }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警数值：</span>
            <span class="list-itemContent">{{ alarmRecord.alarmValue }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">报警描述：</span>
            <span class="list-itemContent">{{ alarmRecord.alarmDetails }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">警情类型：</span>
            <span class="list-itemContent">{{ alarmAffirm(alarmRecord.alarmAffirm) }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">影像信息：</span>
            <span v-for="(item, index) in alarmRecord.imageFileList" :key="index" class="list-itemContent">
              <van-image width="100" height="100" @click="dkImg($YBS.imgUrlTranslation(item.fileUrl))" :src="$YBS.imgUrlTranslation(item.fileUrl)" />
            </span>
          </div>
        </div>
      </div>
      <div style="margin-top: 5px">
        <van-tabs :ellipsis="false">
          <van-tab title="处理过程"><processingProcedure :handleProcessInfo="handleProcessInfo" :taskList="taskList" :alarmDetails="alarmRecord"></processingProcedure></van-tab>
          <van-tab title="警情处理记录"><processingRecords :list="timeLineList"></processingRecords></van-tab>
          <van-tab :title="'工单信息' + '(' + `${workInfo.length}` + ')'"><workOrderInformation :workInfo="workInfo"></workOrderInformation></van-tab>
          <van-tab title="总结分析" v-if="summaryInfo"><summaryInfo :summaryInfo="summaryInfo"></summaryInfo></van-tab>
        </van-tabs>
      </div>
    </div>
    <div class="buttom">
      <!-- 底部 -->
      <el-dropdown trigger="click" @command="handleCommand">
        <span class="el-dropdown-link" style="color: #3562db">更多</span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1" :disabled="!!alarmRecord.workNum">派单</el-dropdown-item>
          <el-dropdown-item command="2" :disabled="alarmRecord.alarmStatus !== 0">{{ alarmRecord.shield ? "取消屏蔽" : "屏蔽" }}</el-dropdown-item>
          <el-dropdown-item command="3" >添加说明</el-dropdown-item>
          <el-dropdown-item command="4">{{ alarmRecord.classic == 1 ? "取消收藏" : "收藏案例" }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <van-button color="#3562db" v-if="alarmRecord.alarmStatus == 0 && alarmRecord.disposalTerminal && alarmRecord.disposalTerminal.includes('1')" @click="dealWith"
        >处理</van-button
      >
      <van-button color="#3562db" v-if="alarmRecord.alarmStatus == 2 && !summaryInfo && alarmRecord.shield == 0" @click="summary">总结</van-button>
    </div>
    <van-action-sheet v-model="shieldShow" title="屏蔽报警" @close="shieldClose">
      <van-field required type="number" v-model="shieldTime" label="屏蔽时长" placeholder="请输入分钟" >
        <span slot="button">分钟</span>
      </van-field>
      <van-field
        :style="shieldReasonId != '' ? 'margin-bottom: 66px' : ''"
        readonly
        required
        right-icon="arrow"
        v-model="shieldReason"
        label="屏蔽原因"
        placeholder="请选择"
        @click="openShield"
      />
      <van-popup v-model="showShield" position="bottom">
        <van-picker value-key="name" show-toolbar :columns="shieldTypeList" @confirm="onShield" @cancel="showShield = false" />
      </van-popup>
      <van-field
        v-if="shieldReasonId == ''"
        type="textarea"
        v-model="remarks"
        label="备注"
        placeholder="请输入备注,限制100字"
        rows="5"
        autosize
        maxlength="100"
        show-word-limit
        style="margin-bottom: 66px"
      />
      <div class="submit-btn">
        <van-button class="weui-btn weui-btn_primary" @click="confirmShield(true)">屏蔽</van-button>
      </div>
    </van-action-sheet>
    <van-action-sheet v-model="illustrationShow" title="添加说明" @close="illustrationClose">
      <van-field
        type="textarea"
        v-model="illustration"
        label="说明"
        placeholder="请输入说明,限制100字"
        rows="5"
        autosize
        maxlength="100"
        show-word-limit
        style="margin-bottom: 66px"
      />
      <div class="submit-btn">
        <van-button class="weui-btn weui-btn_primary" @click="addInstructions">提交</van-button>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import Vue from "vue";
import { Dialog, ImagePreview, Image as VanImage } from "vant";
import { Dropdown, DropdownMenu, DropdownItem } from "element-ui";
Vue.use(Dropdown);
Vue.use(DropdownMenu);
Vue.use(DropdownItem);
Vue.use(VanImage);

import processingRecords from "./components/processingRecords.vue";
import processingProcedure from "./components/processingProcedure.vue";
import workOrderInformation from "./components/workOrderInformation.vue";
import summaryInfo from "./components/summaryInfo.vue";
export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    processingRecords,
    processingProcedure,
    workOrderInformation,
    summaryInfo
  },
  data() {
    return {
      taskList: [],
      imgList: [],
      shieldReason: "误报", //屏蔽原因
      shieldReasonId: "2", //屏蔽原因Id
      shieldTime: "", //屏蔽时长
      showShield: false,
      shieldTypeList: [
        {
          name: "误报",
          id: "2"
        },
        {
          name: "调试",
          id: "4"
        },
        {
          name: "其他",
          id: ""
        }
      ],
      remarks: "", //备注
      shieldShow: false,
      illustrationShow: false,
      illustration: "", //说明
      alarmRecord: "", //详情
      timeLineList: [], //警情处理记录/报警处置流程
      handleProcessInfo: {}, //通话记录
      workInfo: [], //工单
      summaryInfo: {} //总结分析
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getAlaemDetails();
  },
  methods: {
    getInspectionTaskVoPage() {
      let data = {
        alarmDeviceId: this.alarmRecord.alarmDeviceId,
        alarmStartTime: this.alarmRecord.alarmStartTime,
        pageNo: 1,
        pageSize: 1
      };
      this.$api.getInspectionTaskVoPage(data).then(res => {
        this.taskList = res.records;
      });
    },
    //说明
    addInstructions() {
      let params = {
        alarmId: this.alarmRecord.alarmId,
        operationSource: 2,
        remark: this.illustration
      };
      this.$api.insertRemarkById(params).then(res => {
        this.$toast.success("操作成功");
        this.illustrationClose();
        this.illustrationShow = false;
      });
    },
    //屏蔽
    confirmShield(type) {
      if (this.shieldTime == "") return this.$toast.fail("请输入屏蔽时长");
      if (this.shieldReason == "") return this.$toast.fail("请选择屏蔽原因");
      let params = {
        alarmAffirm: this.shieldReasonId,
        shield: type,
        alarmId: this.alarmRecord.alarmId,
        alarmObjectId: this.alarmRecord.alarmObjectId,
        operationSource: 2,
        alarmType: this.alarmRecord.alarmFleldsConfigId,
        shieldTime: this.shieldTime
      };
      if (params.alarmAffirm == 2 || params.alarmAffirm == 4) {
        params.remarks = this.shieldReason;
      } else {
        params.remarks = this.remarks;
      }
      this.shieldShow = false;
      this.$api.getShield(params).then(res => {
        this.$toast.success("操作成功");
        this.shieldClose();
        this.getAlaemDetails();
        this.shieldShow = false;
      });
    },
    openShield() {
      this.showShield = true;
    },
    onShield(value) {
      this.shieldReason = value.name;
      this.shieldReasonId = value.id;
      this.showShield = false;
    },
    shieldClose() {
      this.shieldReason = "误报";
      this.shieldReasonId = 2;
      this.shieldTime = "";
      this.remarks = "";
    },
    dkImg(img) {
      ImagePreview([img]);
    },
    illustrationClose() {
      this.illustration = "";
    },
    handleCommand(value) {
      if (value == 1) {
        this.getOneKeyDispatch();
      } else if (value == 2) {
        if (this.alarmRecord.shield == 1) {
          Dialog.confirm({
            message: "是否取消屏蔽当前报警?"
          }).then(() => {
            this.confirmShield(false);
            this.$nextTick(() => {
              this.getAlaemDetails();
            });
          });
        } else {
          this.shieldShow = true;
        }
      } else if (value == 3) {
        this.illustrationShow = true;
      } else {
        this.setAlarmRecordToClassics();
      }
    },
    setAlarmRecordToClassics() {
      let param = {
        alarmId: this.alarmRecord.alarmId,
        classic: this.alarmRecord.classic == 1 ? "0" : "1",
        operationSource: 2
      };
      this.$api.setAlarmRecordToClassics(param).then(res => {
        if (param.classic == "1") {
          Dialog.alert({
            message: "已存为经典案例，警情处理完毕后会在经典案例中显示"
          }).then(() => {
            this.getAlaemDetails();
          });
        } else {
          this.$nextTick(() => {
            this.getAlaemDetails();
          });
        }
      });
    },
    getOneKeyDispatch() {
      let param = {
        alarmId: this.alarmRecord.alarmId,
        alarmLevel: this.alarmRecord.alarmLevel,
        alarmSourceName: this.alarmRecord.alarmSource,
        incidentName: this.alarmRecord.alarmType,
        spaceId: this.alarmRecord.alarmSpaceId,
        projectCode: this.alarmRecord.projectCode,
        operationSource: 2
      };
      this.$api.OneKeyDispatch(param).then(res => {
        this.$toast.success("已派单");
        this.getAlaemDetails();
      });
    },
    getLimAlarmFile() {
      let data = {
        alarmId: this.$route.query.alarmId,
        fileType: 1
      };
      this.$api.queryLimAlarmFile(data).then(res => {
        this.imgList = res.data;
      });
    },
    getAlaemDetails() {
      let data = {
        alarmId: this.$route.query.alarmId
      };
      this.$api.selectAlarmRecordById(data).then(res => {
        this.alarmRecord = res.record; //详情
        this.timeLineList = res.detail; //警情处理记录/报警处置流程
        this.handleProcessInfo = res.handleProcessInfo; //通话记录
        this.workInfo = res.workInfo; //工单
        this.summaryInfo = res.summaryInfo; //总结分析
        this.getInspectionTaskVoPage();
      });
    },
    alarmType(alarmState) {
      //alarmState 0.未处理 1.处理中 2.已处理
      const alarmStatusMap = {
        0: "未处理",
        1: "处理中",
        2: "已处理"
      };
      if (alarmState < 0 || alarmState > 2) {
        return "";
      }

      return alarmStatusMap[alarmState];
    },
    alarmLevel(alarmLevel) {
      const alarmLevelMap = {
        0: "提示",
        1: "一般",
        2: "较重",
        3: "严重"
      };
      if (alarmLevel < 0 || alarmLevel > 3) {
        return "";
      }
      return alarmLevelMap[alarmLevel];
    },
    alarmAffirm(alarmAffirm) {
      const alarmAffirmMap = {
        0: "未确认",
        1: "真实报警",
        2: "误报",
        3: "演练",
        4: "调试"
      };
      if (alarmAffirm < 0 || alarmAffirm > 4) {
        return "";
      }
      return alarmAffirmMap[alarmAffirm];
    },
    //处理
    dealWith() {
      this.$router.push({
        path: "/dealPoliceSituation",
        query: {
          alarmId: this.alarmRecord.alarmId
        }
      });
    },
    //总结
    summary() {
      this.$router.push({
        path: "/summaryAnalysis",
        query: {
          alarmId: this.alarmRecord.alarmId
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  width: 100vw;
  // height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 140px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-taskInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-documentInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      font-size: 14px;
      color: #353535;
      .list-wrap {
        margin-bottom: 10px;
        .list-item {
          font-size: 16px;
          padding: 7px 0;
          display: flex;
          .list-itemTitle {
            min-width: 102px;
            color: #353535;
          }
          .list-itemContent {
            color: #888;
            overflow: scroll;
            white-space: nowrap;
          }
        }
      }
      .list-flex {
        display: flex;
        justify-content: space-between;
        .list-itemContent {
          color: #86909c;
          font-size: 16px;
        }
      }
      .copywriting {
        margin-top: 10px;
        width: 100%;
        word-break: break-all;
        text-overflow: ellipsis;
      }
    }
    .content-effectInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .radioListItem {
        padding: 0px 0px 12px 0;
        border-bottom: 1px solid #e5e6eb;
        .radioListItem-title {
          margin: 10px 0;
          color: #1d2129;
        }
      }
    }
    .content-evaluateInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      .list-wrap {
        display: flex;
        justify-content: flex-start;
      }
      .evaluateTitle {
        font-size: 16px;
      }
    }
    .attachmentsLists {
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      .upload {
        color: #3562db;
      }
    }
    .list-wrap {
      .list-title {
        font-size: 16px;
        color: #1d2129;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        height: 0.58rem;
        .state {
          .alarmStatus {
            padding: 2px 8px;
            border-radius: 4px;
          }
          .alarmStatus0 {
            background: #ffece8;
            color: #f53f3f;
          }
          .alarmStatus1 {
            background: #fff7e8;
            color: #ff7d00;
          }
          .alarmStatus2 {
            background: #e8ffea;
            color: #00b42a;
          }
        }
        .title-left {
          margin: auto 0;
          .lineTitle {
            height: 0.35rem;
            width: 0.1rem;
            background-color: #3562db;
            display: inline-block;
            margin-right: 6px;
            vertical-align: bottom;
          }
        }
      }
      .state {
        .taskState {
          padding: 6px 8px;
          border-radius: 4px;
          font-size: 14px !important;
          display: inline-block;
        }
        .taskState0 {
          background: #ffece8;
          color: #f53f3f;
        }
        .taskState1 {
          background: #e8ffea;
          color: #00b42a;
        }
      }
      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 92px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
}
.alarmLevel0 {
  color: #00b42a;
}
.alarmLevel1 {
  color: #3562db;
}
.alarmLevel2 {
  color: #ff7d00;
}
.alarmLevel3 {
  color: #f53f3f;
}
.buttom {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 10px 0px;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
}
/deep/ .van-tabs__line {
  background-color: #3562db;
}
.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
  position: fixed;
  bottom: 0;
  z-index: 999;
}
</style>
