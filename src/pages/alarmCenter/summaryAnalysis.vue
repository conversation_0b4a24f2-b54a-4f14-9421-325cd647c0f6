<template>
  <div class="inner">
    <div v-if="!personnelShow">
      <Header title="总结分析" @backFun="goBack"></Header>
      <van-field type="textarea" v-model="summaryAnalysis" label="总结分析" placeholder="请输入分析内容,限制100字" rows="3" autosize maxlength="100" show-word-limit />
      <div class="accessory">
        <div style="display: flex; justify-content: space-between">
          <div>附件 ({{ recordList.length }})</div>
          <!-- <div style="font-size: 12px; color: #646566">最大不超过20MB</div> -->
        </div>
        <div v-for="(item, index) in recordList" :key="index" class="listStyle">
          <div class="name">
            <div class="state">
              <span> {{ item.name }}</span>
            </div>
            <div @click="deleteFile(item.url)">
              <van-icon name="delete" size="20px" />
            </div>
          </div>
        </div>
        <div class="uploader-container">
          <van-uploader accept=".doc,.docx,.xls,.xlsx,.pdf/*" :max-size="maxSize * 1024 * 1024" :after-read="afterRead">
            <span style="color: #3563dc"><van-icon name="plus" />上传文件</span>
          </van-uploader>
        </div>
      </div>

      <upload class="bottom" style="position: relative" ref="imgs" @getImg="getImg" @delImg="delImg"></upload>
      <div style="border-bottom: 1px solid #f2f3f5; border-top: 1px solid #f2f3f5" class="accessory">
        <div class="people">
          <div>院内参与人员 ({{ personnelData.length }})</div>
          <div @click="choosePeople" style="color: #3562db">去选择<van-icon name="arrow" /></div>
        </div>
        <div v-for="i in personnelData" :key="i.id" class="pelname">
          <span style="float: left"> {{ i.staffName }}</span> <span style="float: right" @click="deletePersonnel(i.id)"><van-icon name="cross" /></span>
        </div>
      </div>
      <van-field type="textarea" v-model="otherPerson" label="其他参与人员" placeholder="请输入其他参与人员" rows="3" autosize maxlength="100" />
      <div class="btn">
        <van-button style="width: 90%" color="#3562db" @click="submit">提交</van-button>
      </div>
    </div>
    <personnel v-else :affiliatedPerson="personnelData" @getPersonnel="getPersonnel"></personnel>
  </div>
</template>
<script>
import { mapState } from "vuex";
import upload from "./components/upload.vue";
import personnel from "./components/personnel.vue";
export default {
  components: {
    upload,
    personnel
  },
  data() {
    return {
      personnelData: [],
      personnelShow: false,
      maxSize: 20,
      fileList: [],
      recordList: [],
      summaryAnalysis: "",
      otherPerson: "",
      imagesList: [],
      imgFileList: []
    };
  },
  computed: {
    ...mapState(["staffInfo"])
  },
  mounted() {
     this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    submit() {
      let params = {
        hospitalPerson: this.personnelData.map(person => person.staffName).join(","),
        fileUrl: JSON.stringify([...this.recordList, ...this.imgFileList] || ""),
        createName: this.staffInfo.staffName,
        summaryAnalysis: this.summaryAnalysis,
        otherPerson: this.otherPerson,
        limAlarmId: this.$route.query.alarmId,
        operationSource: 2
      };
      this.$api
        .summarySave(params)
        .then(res => {
          this.$toast.success("操作成功");
          this.goBack();
        })
    },
    deletePersonnel(obj) {
      this.personnelData = this.personnelData.filter(item => item.id !== obj);
    },
    getPersonnel(data) {
      this.personnelData = [];
      this.personnelData = data;
      this.personnelShow = false;
    },
    choosePeople() {
      this.personnelShow = true;
    },
    deleteFile(value) {
      let fileArr = this.recordList.filter(i => i.url !== value);
      this.recordList = fileArr;
    },
    afterRead(files) {
      console.log("files", files);
      this.fileList.forEach(i => {
        return (i.status = "uploading");
      });
      const params = new FormData();
      params.append("file", files.file);
      this.subImg(params);
    },
    subImg(params) {
      this.axios
        .post(__PATH.ONESTOP + "/minio/upload", params, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          console.log("uploadres", res);
          if ((res.data.code = "200")) {
            const item = res.data.data.picUrl;
            this.recordList.push({
              name: res.data.data.name,
              url: item
            });

            this.fileList.forEach(i => {
              return (i.status = "done");
            });
          }
        });
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    getImg(img, imgFileList) {
      this.imgFileList = imgFileList;

      this.imagesList.push(img);
    },
    delImg(url) {
      this.imgFileList = this.imgFileList.filter(item => {
        return item.url != url;
      });
      this.imagesList = this.imagesList.filter(item => {
        return item != url;
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100vw;
  height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .accessory {
    padding: 15px;
    background-color: #fff;
    .uploader-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
      border-bottom: 1px solid #f2f3f5;
      margin: 15px 0;
    }
    .people {
      padding-bottom: 15px;
      display: flex;
      justify-content: space-between;
    }
    .pelname {
      display: inline-block;
      justify-content: space-between;
      width: 20%;
      padding: 5px 10px;
      background-color: #f2f3f5;
      margin: 5px;
      border-radius: 15px;
    }
  }
  .listStyle {
    width: 100%;
    background-color: #f2f4f9;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      line-height: 25px;
      align-items: center;
      justify-content: space-between;
    }
    .state {
      .alarmStatus {
        padding: 2px 8px;
        border-radius: 4px;
      }
      .alarmStatus0 {
        background: #ffece8;
        color: #f53f3f;
      }
      .alarmStatus1 {
        background: #fff7e8;
        color: #ff7d00;
      }
      .alarmStatus2 {
        background: #e8ffea;
        color: #00b42a;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .alarmLevel0 {
        color: #00b42a;
      }
      .alarmLevel1 {
        color: #3562db;
      }
      .alarmLevel2 {
        color: #ff7d00;
      }
      .alarmLevel3 {
        color: #f53f3f;
      }
      .txt_l {
        margin: auto 0;
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
</style>
