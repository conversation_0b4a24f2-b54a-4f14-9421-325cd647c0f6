<template>
  <div class="inner" :style="list.length > 0 ? 'height: 100%' : 'height: 50px'">
    <div v-if="list.length > 0">
      <van-steps direction="vertical" :active="list.length" active-color="#3562db">
        <van-step v-for="item in list" :key="item.id">
          <p style="font-weight: bold;" class="heightRow">{{ item.createdTime }}</p>
          <p class="heightRow">{{ item.operationTypeName }}</p>
          <p class="heightRow">
            添加人：{{ item.operationPersonName }} <span v-if="item.operationPersonId">{{ "(" + item.operationPersonId + ")" }}</span>
          </p>
          <p class="heightRow">操作端：{{ item.operationSourceName }}</p>
          <p v-if="item.remark" class="heightRow">说明：{{ item.remark }}</p>
        </van-step>
      </van-steps>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["list"],
  data() {
    return {
    };
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow: auto;
  color: #353535;
   margin: 5px 0 0 0;
  .heightRow{
    line-height: 25px;
  }
}
/deep/ .van-step__line {
  background-color: #e5e6eb !important;
}
.notList {
  position: relative;
  height: 50px;
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
