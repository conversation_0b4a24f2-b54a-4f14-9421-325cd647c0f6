<template>
  <div class="inner" :style="workInfo.length > 0 ? 'height: 100%' : 'height: 50px'">
    <div v-if="workInfo.length > 0">
      <div v-for="item in workInfo" :key="item.id" class="listStyle" @click="enterDetailInfo(item)">
        <div class="name">
          <div><span style="width: 70px; display: inline-block">工单号：</span> {{ item.workNum }}</div>
          <div class="state">
            <span class="alarmStatus alarmStatus0"> {{ item.flowtype }}</span>
            <span><van-icon name="arrow" /></span>
          </div>
        </div>
        <div class="name">
          <div><span style="width: 70px; display: inline-block">工单类型：</span>{{ item.workTypeName }}</div>
        </div>
      </div>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["workInfo"],
  data() {
    return {};
  },
  methods: {
    enterDetailInfo(item) {
      if (item.flowcode == 1) {
        this.$router.push({
          path: "/vieform",
          query: {
            source: "myWorkbench",
            interfaceNum: 0,
            id: item.taskId,
            flowcode: item.flowcode,
            from: this.$route.query.from || ""
          }
        });
      }
      //已派工
      if (item.flowcode == 2) {
        this.$router.push({
          path: "/completed",
          query: {
            source: "myWorkbench",
            interfaceNum: 0,
            id: item.taskId,
            flowcode: item.flowcode,
            isMy: this.$route.query.isMy,
            from: this.$route.query.from || ""
          }
        });
      }
      //已挂单
      if (item.flowcode == 3) {
        this.$router.push({
          path: "/completed",
          query: {
            source: "myWorkbench",
            interfaceNum: 0,
            id: item.taskId,
            hanged: true,
            flowcode: item.flowcode,
            from: this.$route.query.from || ""
          }
        });
      }
      //已结束
      if (item.flowcode == 4 || item.flowcode == 15) {
        this.$router.push({
          path: "/deskDetails",
          query: {
            id: item.taskId,
            flowcode: item.flowcode,
            from: this.$route.query.from || ""
          }
        });
      }
      // 待验收(已完工)
      if (item.flowcode == 5) {
        this.$router.push({
          path: "/workOrderDetails",
          query: {
            id: item.taskId,
            source: "",
            flowcode: item.flowcode,
          }
        });
      }
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow: auto;
  color: #353535;
  .listStyle {
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
    margin: 7px 0;
    // padding: 5px 0;
    .name {
      padding: 12px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
    }
    .state {
      .alarmStatus {
        padding: 2px 8px;
        border-radius: 4px;
      }
      .alarmStatus0 {
        background: #ffece8;
        color: #f53f3f;
      }
      .alarmStatus1 {
        background: #fff7e8;
        color: #ff7d00;
      }
      .alarmStatus2 {
        background: #e8ffea;
        color: #00b42a;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .alarmLevel0 {
        color: #00b42a;
      }
      .alarmLevel1 {
        color: #3562db;
      }
      .alarmLevel2 {
        color: #ff7d00;
      }
      .alarmLevel3 {
        color: #f53f3f;
      }
      .txt_l {
        margin: auto 0;
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
}
.notList {
  position: relative;
  height: 50px;
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
