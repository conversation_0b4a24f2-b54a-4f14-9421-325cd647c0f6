<template>
  <div class="inner">
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div style="font-weight: 600; font-size: 16px; line-height: 30px">总结分析</div>
          <div class="list-item">
            <span class="list-itemTitle">总结分析：</span>
            <span class="list-itemContent">{{ summaryInfo.summaryAnalysis || "-" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">分析文件：</span>
          </div>
          <div v-for="(item, index) in JSON.parse(summaryInfo.fileUrl)" :key="index" class="fileStyle" @click="lookDown(item)">
            <div>
              <p>{{ item.name }}</p>
            </div>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">院内参与人员：</span>
            <span class="list-itemContent">{{ summaryInfo.hospitalPerson || "-" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">其他参与人员：</span>
            <span class="list-itemContent">{{ summaryInfo.otherPerson || "-" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">处理人：</span>
            <span class="list-itemContent">{{ summaryInfo.createName || "-" }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {  ImagePreview } from "vant";
export default {
   components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
  },
  props: ["summaryInfo"],
  data() {
    return {};
  },
  methods: {
    lookDown(item) {
      let file = {
        name: item.name,
        url: this.$YBS.imgUrlTranslation(item.url)
      };
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;
        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function (ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function (ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function (ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      } else {
        ImagePreview({
          images: [file.url],
          showIndex: true,
          loop: false,
          swipeDuration: 50
        });
      }
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow: auto;
  color: #353535;
  .content {
    margin: 5px 0 0 0;
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-taskInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-documentInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      font-size: 14px;
      color: #353535;
      .list-wrap {
        margin-bottom: 10px;
        .list-item {
          font-size: 16px;
          padding: 7px 0;
          display: flex;
          .list-itemTitle {
            min-width: 110px;
            color: #353535;
          }
          .list-itemContent {
            color: #888;
            overflow: scroll;
            white-space: nowrap;
          }
        }
      }
      .list-flex {
        display: flex;
        justify-content: space-between;
        .list-itemContent {
          color: #86909c;
          font-size: 16px;
        }
      }
      .copywriting {
        margin-top: 10px;
        width: 100%;
        word-break: break-all;
        text-overflow: ellipsis;
      }
    }
    .content-effectInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .radioListItem {
        padding: 0px 0px 12px 0;
        border-bottom: 1px solid #e5e6eb;
        .radioListItem-title {
          margin: 10px 0;
          color: #1d2129;
        }
      }
    }
    .content-evaluateInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      .list-wrap {
        display: flex;
        justify-content: flex-start;
      }
      .evaluateTitle {
        font-size: 16px;
      }
    }
    .attachmentsLists {
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      .upload {
        color: #3562db;
      }
    }
    .list-wrap {
      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 110px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
}
.fileStyle {
  padding: 5px 10px;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f7f8fa;
  margin-bottom: 10px;
  border-radius: 5px;
  > div {
    line-height: 25px;
  }
}
</style>
