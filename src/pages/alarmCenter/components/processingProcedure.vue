<template>
  <div class="inner">
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div style="font-weight: 600; font-size: 16px; line-height: 30px">关联人员信息</div>
          <div class="list-item">
            <span class="list-itemTitle">区域责任部门：</span>
            <span class="list-itemContent">{{ handleProcessInfo.liabilityDeptName || "-" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">区域负责人：</span>
            <span class="list-itemContent">{{ handleProcessInfo.liabilityPersonName || "-" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">受理警情队伍：</span>
            <span class="list-itemContent">{{ handleProcessInfo.acceptAlarmTeamName || "-" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">值班人员：</span>
            <span class="list-itemContent">{{ handleProcessInfo.operatorOnDuty || "-" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">应急人员：</span>
            <span class="list-itemContent">{{ handleProcessInfo.emergencyTeamMembers || "-" }}</span>
          </div>
          <div v-for="(item, index) in taskList" :key="index" class="listStyle">
            <div class="name">
              <div>{{ item.taskName }}</div>
              <div class="state">
                <span class="taskStatus" :style="item.taskStatus == 1 ? 'background: #ffece8;color:#f53f3f' : 'background: #e8ffea;color: #00b42a;'">
                  {{ item.taskStatus == 1 ? "未执行" : "已执行" }}</span
                >
              </div>
            </div>
            <div class="txt">
              <div class="txt_l">任务类型：</div>
              <div class="txt_r">{{ item.taskType }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">执行人员：</div>
              <div class="txt_r">{{ item.executionPersonName }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">执行时间：</div>
              <div class="txt_r">{{ item.executionTime }}</div>
            </div>
          </div>
          <div style="float: right; color: #3562db" @click="goTaskList" v-if="taskList.length > 0">
            查看更多任务 <span><van-icon name="arrow" /></span>
          </div>
        </div>
        <div style="margin-top: 25px">
          <div style="font-weight: 600; font-size: 16px; line-height: 30px">现场文件</div>
          <div v-for="(item, index) in handleProcessInfo.fileVoList" :key="index" class="fileStyle" @click="lookDown(item)">
            <div>
              <p style="font-weight: 600">{{ item.name }}</p>
              <p>{{ item.createdTime }}</p>
            </div>
            <div>{{ item.createdName }}</div>
          </div>
        </div>
        <div style="margin-top: 15px">
          <div style="font-weight: 600; font-size: 16px; line-height: 30px">通话记录</div>
          <div v-for="(item, index) in handleProcessInfo.clientOperationLogVoList" :key="index" class="fileStyle" @click="openCallLog(item)">
            <div>
              <p style="font-weight: 600">{{ item.communicateTypeName }}</p>
              <p>{{ item.operationStartTime }}</p>
            </div>
            <div>{{ item.callDuration }}</div>
          </div>
        </div>
      </div>
    </div>
    <van-action-sheet v-model="callLogShow" title="通话记录" @close="callLogShow = false">
      <div class="txt">
        <div class="txt_l">呼叫类型：</div>
        <div class="txt_r">{{ callLog.communicateTypeName }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">通话时长：</div>
        <div class="txt_r">{{ callLog.callDuration }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">通话开始时间：</div>
        <div class="txt_r">{{ callLog.operationStartTime }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">通话结束时间：</div>
        <div class="txt_r">{{ callLog.operationEndTime }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">被呼叫人：</div>
        <div class="txt_r">
          <span v-for="(item, index) in callLog.callInfoList" :key="index">
            <span>{{ item.callPhone }}</span>
            <span :style="`color: ${item.callStatus == '接通' ? '#67c23a' : '#FA403C'}`">({{ item.callStatus }})</span>
            <span v-if="index !== callLog.callInfoList.length - 1">,</span>
          </span>
        </div>
      </div>
      <div class="txt">
        <div class="txt_l">通话人数：</div>
        <div class="txt_r">{{ callLog.callPersonNum }}</div>
      </div>
      <div class="txt">
        <div class="txt_l">通话录音：</div>
        <div class="txt_r"><audio id="" :src="callLog.operationUrl" controls /></div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { ImagePreview } from "vant";
export default {
  props: ["handleProcessInfo", "taskList", "alarmDetails"],
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  data() {
    return {
      callLog: "",
      callLogShow: false
    };
  },
  mounted() {},
  methods: {
    openCallLog(row) {
      this.callLog = row;
      this.callLogShow = true;
    },
    lookDown(item) {
      let file = {
        name: item.name,
        url: this.$YBS.imgUrlTranslation(item.url)
      };
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;
        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function (ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function (ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function (ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      } else {
        ImagePreview({
          images: [file.url],
          showIndex: true,
          loop: false,
          swipeDuration: 50
        });
      }
    },
    goTaskList() {
      this.$router.push({
        path: "/alarmTaskList",
        query: {
          alarmDetails: this.alarmDetails
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow: auto;
  color: #353535;
  .content {
    margin: 5px 0 0 0;
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-taskInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-documentInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      font-size: 14px;
      color: #353535;
      .list-wrap {
        margin-bottom: 10px;
        .list-item {
          font-size: 16px;
          padding: 7px 0;
          display: flex;
          .list-itemTitle {
            min-width: 110px;
            color: #353535;
          }
          .list-itemContent {
            color: #888;
            overflow: scroll;
            white-space: nowrap;
          }
        }
      }
      .list-flex {
        display: flex;
        justify-content: space-between;
        .list-itemContent {
          color: #86909c;
          font-size: 16px;
        }
      }
      .copywriting {
        margin-top: 10px;
        width: 100%;
        word-break: break-all;
        text-overflow: ellipsis;
      }
    }
    .content-effectInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .radioListItem {
        padding: 0px 0px 12px 0;
        border-bottom: 1px solid #e5e6eb;
        .radioListItem-title {
          margin: 10px 0;
          color: #1d2129;
        }
      }
    }
    .content-evaluateInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      .list-wrap {
        display: flex;
        justify-content: flex-start;
      }
      .evaluateTitle {
        font-size: 16px;
      }
    }
    .attachmentsLists {
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      .upload {
        color: #3562db;
      }
    }
    .list-wrap {
      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 110px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
}
.fileStyle {
  padding: 5px 10px;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f7f8fa;
  margin-bottom: 10px;
  border-radius: 5px;
  > div {
    line-height: 25px;
  }
  > div:nth-child(1) {
    width: 75%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.listStyle {
  width: 100%;
  background-color: #f2f4f9;
  border-radius: 10px;
  margin: 10px auto;
  padding: 5px 0;
  .name {
    padding: 10px 10px;
    color: #1d2129;
    display: flex;
    justify-content: space-between;
    > div:nth-child(1) {
      width: 80%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .state {
      .taskStatus {
        padding: 2px 8px;
        border-radius: 4px;
      }
    }
  }

  .txt {
    display: flex;
    padding: 7px 10px;

    .txt_l {
      margin: auto 0;
      width: 90px;
      color: #4e5969;
      font-weight: 300;
    }
    .txt_r {
      color: #1d2129;
      flex: 1;
    }
  }
  .txtOpertaion {
    display: flex;
    height: 35px;
    line-height: 35px;
    padding: 0px 10px;
    .txt_l {
      width: 90px;
      color: #4e5969;
      font-weight: 300;
    }
    .btn {
      flex: 1;
      text-align: right;
      span {
        display: inline-block;
        height: 30px;
        line-height: 30px;
        padding: 0px 15px;
        background-color: #3562db;
        color: #fff;
      }
    }
  }
}
.txt {
  display: flex;
  padding: 10px;

  .txt_l {
    margin: auto 0;
    width: 90px;
    color: #4e5969;
    font-weight: 300;
  }
  .txt_r {
    color: #1d2129;
    flex: 1;
  }
}
</style>
