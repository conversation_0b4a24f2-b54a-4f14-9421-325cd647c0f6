<template>
  <div class="inner">
    <Header title="选择院参与人" @backFun="confirm"></Header>
    <van-search v-model="staffName" background="#fff" clearable placeholder="搜索成员姓名" @search="getPerList" @clear="searchCancel" @input="nameInput"> </van-search>
    <div class="content" v-if="contentShow">
      <div class="breadcrumb">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index"
            ><i @click="breadcrumbClick(index)" :style="index == breadcrumbList.length - 1 ? 'color:#3562DB' : ''">{{ item }}</i></el-breadcrumb-item
          >
        </el-breadcrumb>
      </div>
      <div v-if="!officeId" style="height: 500px">
        <div class="personnel" v-if="breadcrumbList.length == 1">
          <div v-for="item in hospitalList" :key="item.umId" @click="hospitalDown(item.umId, item.unitComName)">
            <div class="list">
              <span>{{ item.unitComName }}</span>
              <span><van-icon name="arrow" /></span>
            </div>
          </div>
        </div>
        <div class="personnel" v-if="breadcrumbList.length > 1">
          <div v-if="deptList.length">
            <div v-for="item in deptList" :key="item.id" @click="deptDown(item)">
              <div class="list">
                <span>{{ item.deptName }}</span>
                <span><van-icon name="arrow" /></span>
              </div>
            </div>
          </div>
          <div v-else class="notList">
            <div class="emptyImg">
              <span class="emptyText">暂无数据</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="personnelList">
        <div v-if="personnelList.length > 0">
          <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
              <div class="listStyle">
                <van-checkbox-group v-model="result" @change="personnelCheck">
                  <van-checkbox v-for="item in personnelList" :key="item.id" :name="item.id" shape="square" checked-color="#3562DB">
                    <div class="box">
                      <div class="icon">
                        <van-icon name="user-o" size="20px" color="#fff" />
                      </div>
                      &nbsp; &nbsp; &nbsp;

                      <div>
                        <div>{{ item.staffName }}</div>
                        <div>{{ item.mobile }}</div>
                      </div>
                    </div>
                  </van-checkbox>
                </van-checkbox-group>
              </div>
            </van-list>
          </van-pull-refresh>
        </div>
        <div v-else class="notList">
          <div class="emptyImg">
            <span class="emptyText">暂无数据</span>
          </div>
        </div>
      </div>
    </div>
    <div class="content" v-else>
      <div v-if="personnelList.length > 0">
        <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <div class="listStyle">
              <van-checkbox-group v-model="result" @change="personnelCheck">
                <van-checkbox v-for="item in personnelList" :key="item.id" :name="item.id" shape="square" checked-color="#3562DB">
                  <div class="box">
                    <div class="icon">
                      <van-icon name="user-o" size="20px" color="#fff" />
                    </div>
                    &nbsp; &nbsp; &nbsp;

                    <div>
                      <div>{{ item.staffName }}</div>
                      <div>{{ item.mobile }}</div>
                    </div>
                  </div>
                </van-checkbox>
              </van-checkbox-group>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div>
        已选择:
        <div class="personnel" @click="personalManagement">{{ personnelData.map(obj => obj.staffName).join(",") || "" }}</div>
      </div>
      <van-button color="#3562db" @click="confirm">确定({{ personnelData.length }})</van-button>
    </div>
    <van-action-sheet v-model="personnelShow" :title="title" @close="personnelnClose">
      <div class="perlistStyle">
        <div v-for="item in personnelData" :key="item.id" :name="item.id" shape="square" checked-color="#3562DB" class="perList">
          <div class="box2">
            <div class="icon">
              <van-icon name="user-o" size="20px" color="#fff" />
            </div>
            &nbsp; &nbsp; &nbsp;

            <div>
              <div>{{ item.staffName }}</div>
              <div>{{ item.mobile }}</div>
            </div>
          </div>
          <div @click="deletePersonnel(item)">
            <van-icon name="delete" size="20px" />
          </div>
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import YBS from "@/assets/utils/utils.js";
import Vue from "vue";
import { Breadcrumb, BreadcrumbItem } from "element-ui";
Vue.use(Breadcrumb);
Vue.use(BreadcrumbItem);
export default {
  props: ["affiliatedPerson"],
  data() {
    return {
      contentShow: true,
      title: "",
      personnelShow: false,
      result: [],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      pageSize: 10,
      staffName: "",
      hospitalList: [],
      breadcrumbList: ["院区"],
      deptList: [],
      personnelList: [],
      personnelData: [],
      umId: "",
      officeId: "",
      breadcrumLocationList: [] //存储面包屑节点的内容，方便回显
    };
  },
  mounted() {
    this.personnelData = this.affiliatedPerson || [];
    if (this.personnelData.length > 0) {
      this.result = this.personnelData.map(obj => obj.id);
    }

    this.getSelected();
  },
  methods: {
    confirm() {
      let newData = [...this.personnelData];
      this.$emit("getPersonnel", newData);
    },
    deletePersonnel(row) {
      let resultArr = this.result;
      let personnelArr = this.personnelData;
      this.result = resultArr.filter(item => item !== row.id);
      this.personnelData = personnelArr.filter(item => item.id !== row.id);
      this.title = "已选择" + "(" + this.personnelData.length + ")";
      if (this.personnelData.length == 0) {
        this.personnelShow = false;
      }
    },
    personalManagement() {
      if (this.personnelData.length > 0) {
        this.title = "已选择" + "(" + this.personnelData.length + ")";
        this.personnelShow = true;
      }
    },
    personnelnClose() {
      this.personnelShow = false;
    },
    personnelCheck(data) {
      this.personnelList.forEach(i => {
        data.forEach(j => {
          if (i.id == j) {
            this.personnelData.push(i);
          }
        });
      });
      let personnelArr = this.personnelData.filter((item, index, self) => index === self.findIndex(t => t.id === item.id));
      this.personnelData = personnelArr.filter(obj => data.includes(obj.id));
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.personnelList = [];
      this.getLersonnelList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.current++;
      this.getLersonnelList();
    },
    nameInput(value) {
      if (!value) {
        this.breadcrumbClick(0);
        this.contentShow = true;
      }
    },
    getPerList() {
      this.current = 1;
      this.personnelList = [];
      this.contentShow = false;
      this.officeId = "";
      this.getLersonnelList();
    },
    //查人
    getLersonnelList() {
      let params = {
        current: this.current,
        size: this.pageSize,
        sex: "",
        pmId: "",
        officeId: this.officeId,
        postId: "",
        stationStatus: 0,
        staffName: this.staffName
      };
      this.$api.staffList(params).then(res => {
        this.loading = false;
        res.records.forEach(item => {
          this.personnelList.push(item);
        });
        if (this.personnelList.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    deptDown(item) {
      if (item.children) {
        this.deptList = item.children;
        this.breadcrumLocationList.push(item.children);
        this.breadcrumbList.push(item.deptName);
      } else {
        this.officeId = item.id;
        this.personnelList = [];
        this.getLersonnelList();
      }
    },
    hospitalDown(id, name) {
      this.breadcrumbList.push(name);
      this.umId = id;
      this.getSelectedDept();
    },

    // 根据所属单位回去部门列表
    getSelectedDept() {
      this.$api.getSelectedDept({ unitId: this.umId }).then(res => {
        this.deptList = YBS.transData(res, "id", "pid", "children");
      });
    },
    //查询单位
    getSelected() {
      this.$api.getSelected({}).then(res => {
        this.hospitalList = res;
      });
    },
    breadcrumbClick(index) {
      if (index == 0) {
        this.sliceArr(index);
      } else if (index == 1) {
        this.getSelectedDept();
        this.breadcrumLocationList = [];
        this.sliceArr(index);
      } else {
        this.sliceArr(index);
        this.deptList = this.breadcrumLocationList[index - 2];
      }
      this.officeId = "";
    },
    sliceArr(index) {
      let arr = this.breadcrumbList;
      this.breadcrumbList = arr.slice(0, index + 1);
    },

    searchCancel() {
      this.breadcrumbClick(0);
      this.staffName = "";
      this.contentShow = true;
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999;
  width: 100vw;
  height: calc(100vh - 70px);
  background-color: #fff;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    height: calc(100vh - 200px);
    background-color: #fff;

    .breadcrumb {
      padding: 10px;
      .el-breadcrumb__item {
        // max-width: 50px;
        // white-space: nowrap;
        //   overflow: hidden;
        //   text-overflow: ellipsis;
      }
    }
    .personnel {
      padding: 5px 10px;
      > div {
        .list {
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid #e5e6eb;
          line-height: 40px;
        }
      }
    }
  }
}

.personnelList {
}

.bottom {
  position: fixed;
  background-color: #fff;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 60px;
  align-items: center;
  padding: 0 10px;
  z-index: 999;
  display: flex;
  > div {
    width: 70%;
    display: flex;
    .personnel {
      width: 50%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #3562db;
    }
  }
}
.listStyle {
  width: 95%;
  background-color: #fff;
  border-radius: 10px;
  margin: 10px auto;
  padding: 5px 0;
  .box {
    width: 100%;
    margin: 5px 0;
    height: 50px;
    display: flex;
    align-items: center;
    > div:nth-child(2) {
      line-height: 20px;
    }
    .icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: #3562db;
      padding: 10px;
    }
  }
}
.perlistStyle {
  width: 95%;
  height: 300px;

  background-color: #fff;
  border-radius: 10px;
  margin: 10px auto;
  padding: 5px 0;
  .perList {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .box2 {
      width: 100%;
      margin: 5px 0;
      height: 50px;
      display: flex;
      align-items: center;
      > div:nth-child(2) {
        line-height: 20px;
      }
      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #3562db;
        padding: 10px;
      }
    }
  }
}
.van-checkbox {
  border-bottom: 1px solid #f2f3f5;
}
.notList {
  .emptyImg {
    position: absolute;
    height: 70%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
.el-breadcrumb__item {
  max-width: 80px; /* 或你需要的宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
