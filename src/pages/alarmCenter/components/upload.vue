<template>
  <div class="instructions" style="border: none">
    <div class="explain" style="justify-content: space-between">
      <span style="color: #333">上传图片</span>
      <p class="upload-img" style="color: #646566">注：上传本地图片或视频，最大不超过20MB</p>
    </div>
    <div style="width: 95%; margin: 0 auto">
      <van-uploader
        :max-size="maxSize * 1024 * 1024"
        @oversize="onOversize"
        ref="uplodImg"
        accept="image/*,video/*"
        v-model="fileList"
        :after-read="afterRead"
        @click-upload="clickUpload"
        @delete="deleteImg"
      />
    </div>
  </div>
</template>

<script>
import detector from "detector";
import { mapState } from "vuex";
import YBS from "@/assets/utils/utils.js";
export default {
  name: "UploadImg",

  data() {
    return {
      fileList: [],
      maxSize: 20,
      attachmentUrl: "",
      recordList: []
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  methods: {
    emptyImg() {
      console.log("清空缓存");
      this.fileList = [];
    },
    lookDown(file) {
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;

        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function (ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function (ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function (ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      }
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    clickUpload() {
      // this.power()
    },
    afterRead(files) {
      console.log("files", files);
      this.fileList.forEach(i => {
        return (i.status = "uploading");
      });
      // 使用formdata上传
      const params = new FormData();
      params.append("file", files.file);
      this.subImg(params);
    },
    //删除图片
    deleteImg(e) {
      this.recordList.forEach((item, index) => {
        if (item.name == e.file.name) {
          this.$emit("delImg", item.url);
        }
      });
    },
    subImg(params) {
      this.axios
        .post(__PATH.ONESTOP + "/minio/upload", params, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          console.log("uploadres", res);
          if ((res.data.code = "200")) {
            const item = res.data.data.picUrl;
            // this.form.fileList.push(item);
            this.recordList.push({
              name: res.data.data.name,
              url: item
            });
            this.$emit("getImg", item, this.recordList);
            this.fileList.forEach(i => {
              return (i.status = "done");
            });
          }
        });
    }
  },
  created() {
    // 获取储存、相机权限
    if (!YBS.hasPermission("storage")) {
      YBS.reqPermission(["storage"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          if (!YBS.hasPermission("camera")) {
            YBS.reqPermission(["camera"], function(ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              }
            });
            return;
          } else {
          }
        }
      });
      return;
    }
    if (!YBS.hasPermission("camera")) {
      YBS.reqPermission(["camera"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
        }
      });
      return;
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
.instructions {
  border-bottom: 4px solid #f2f3f5;
  background-color: #fff;
  // margin-bottom: 12px;
}
.instructions .explain {
  position relative;
  background-color: #fff;
  min-height: 48px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}
.instructions .explain span {
  font-size: 14px;
}
/deep/ .van-uploader__upload-icon {
  color: #86909C;
  transform: translateY(-20%);
}
/deep/ .van-uploader__upload::after {
  content:'添加照片';
  position absolute;
  top:68%;
  font-size: 12px;
  color: #86909C;
  width: 100%;
  text-align: center;
}
/deep/ .van-uploader__preview-delete {
  top:3px;
  right:3px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  background-color:rgba(0,0,0,.5);
}
/deep/ .van-uploader__preview-delete-icon {
  transform: scale(.8);
  top: 2px;
  left: 2px;
  font-weight: bold;
}
.upload-img {
  font-size: 12px;
}
.star::before {
  position: absolute;
  left: 7px;
  top: 40%;
  content:'*';
  color:red;
}
</style>
