<template>
  <div class="inner">
    <Header title="全部报警" showRightBtn rightIcon="search" @taskScanCode="scanCode" @backFun="goBack"></Header>
    <van-tabs @click="tabClick" style="height: 40px" :ellipsis="false">
      <van-tab v-for="item in tabList" :name="item.id" :title="item.name + '(' + item.num + ')'" :key="item.id" />
    </van-tabs>
    <div v-if="alarmList.length > 0" class="alarmList">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width: 100%">
            <div v-for="item in alarmList" :key="item.id" class="listStyle" @click="goDetails(item)">
              <div class="name">
                <div>{{ item.alarmId }}</div>
                <div class="state">
                  <span :class="['alarmStatus', `alarmStatus${item.alarmStatus}`]"> {{ alarmType(item.alarmStatus) }}</span>
                  <span><van-icon name="arrow" /></span>
                </div>
              </div>
              <div class="txt">
                <div class="txt_l">报警时间：</div>
                <div class="txt_r">{{ item.alarmStartTime }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警级别：</div>
                <div :class="[`alarmLevel${item.alarmLevel}`]">{{ alarmLevel(item.alarmLevel) }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警系统：</div>
                <div class="txt_r">{{ item.projectName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警类型：</div>
                <div class="txt_r">{{ item.alarmType }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警位置：</div>
                <div class="txt_r">{{ item.completeRegionName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警对象：</div>
                <div class="txt_r">{{ item.alarmObjectName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">报警描述：</div>
                <div class="txt_r">{{ item.alarmDetails }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      alarmStatus: 0,
      alarmList: [],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      pageSize: 10,
      total: 0,
      num: 0,
      cardType: "",
      tabList: [
        {
          id: 0,
          num: 0,
          name: "未处理"
        },
        {
          id: 1,
          num: 0,

          name: "处理中"
        },
        {
          id: "",
          num: 0,

          name: "全部"
        },
        {
          id: 2,
          num: 0,

          name: "已屏蔽"
        },
        {
          id: 3,
          num: 0,

          name: "经典案例"
        }
      ]
    };
  },
  computed: {},
  mounted() {
    this.getAlarmStatisticsCount();
    this.getAlarmList();
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    //查询全部报警分类数量统计
    getAlarmStatisticsCount() {
      this.$api
        .queryAllAlarmStatisticsCount()
        .then(res => {
          this.tabList[0].num = res.untreatedCount; //未处理
          this.tabList[1].num = res.processingCount; //处理种
          this.tabList[2].num = res.allCount; //全部
          this.tabList[3].num = res.shieldCount; //屏蔽
          this.tabList[4].num = res.classCount; //经典案例
        })
        .catch(res => {
          if (res.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    alarmType(alarmState) {
      //alarmState 0.未处理 1.处理中 2.已处理
      const alarmStatusMap = {
        0: "未处理",
        1: "处理中",
        2: "已处理"
      };
      if (alarmState < 0 || alarmState > 2) {
        return "";
      }

      return alarmStatusMap[alarmState];
    },
    alarmLevel(alarmLevel) {
      const alarmLevelMap = {
        0: "通知",
        1: "一般",
        2: "较重",
        3: "严重"
      };
      if (alarmLevel < 0 || alarmLevel > 3) {
        return "";
      }
      return alarmLevelMap[alarmLevel];
    },
    tabClick(val) {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.alarmStatus = val;
      this.alarmList = [];
      this.getAlarmStatisticsCount()
      if(val == 3) {
        this.getClassicsAlarm()
      } else {

        this.getAlarmList();
      }
    },
    // 获取经典案例接口
    getClassicsAlarm() {
      let data = {
        timeOrType: '',
        pageSize: this.pageSize,
        pageNo: this.current
      };
      this.$api.selectClassicsAlarm(data).then(res => {
          this.loading = false;
          res.records.forEach(item => {
            this.alarmList.push(item);
          });
          if (this.alarmList.length >= res.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
      })
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.alarmList = [];
      if(this.alarmStatus == 3) {
        this.getClassicsAlarm()
      } else {

        this.getAlarmList();
      }
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.current++;
      if(this.alarmStatus == 3) {
        this.getClassicsAlarm()
      } else {

        this.getAlarmList();
      }
    },
    getAlarmList() {
      let data = {
        timeOrType: '',
        pageSize: this.pageSize,
        pageNo: this.current
      };
      if (this.alarmStatus == 0 || this.alarmStatus == 1) {
        data.alarmStatus = this.alarmStatus;
      } else if (this.alarmStatus == 2) {
        data.shield = 1;
        data.alarmStatus = "";
      } else if (this.alarmStatus == 3) {
        data.classic = 1;
        data.alarmStatus = "";
      } else {
        data.alarmStatus = "";
      }
      this.$api
        .selectAlarmRecordAll(data)
        .then(res => {
          this.loading = false;
          res.records.forEach(item => {
            this.alarmList.push(item);
          });
          if (this.alarmList.length >= res.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        })
    },
    goDetails(row) {
      this.$router.push({
        path: "/alarmDetails",
        query: {
          alarmId: row.alarmId
        }
      });
    },
    scanCode() {
      console.log("点击了搜索--------");
      this.$router.push({
        path: "/screening"
      });
    },
    goBack() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
    }
    .state {
      .alarmStatus {
        padding: 2px 8px;
        border-radius: 4px;
      }
      .alarmStatus0 {
        background: #ffece8;
        color: #f53f3f;
      }
      .alarmStatus1 {
        background: #fff7e8;
        color: #ff7d00;
      }
      .alarmStatus2 {
        background: #e8ffea;
        color: #00b42a;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .alarmLevel0 {
        color: #00b42a;
      }
      .alarmLevel1 {
        color: #3562db;
      }
      .alarmLevel2 {
        color: #ff7d00;
      }
      .alarmLevel3 {
        color: #f53f3f;
      }
      .txt_l {
        margin: auto 0;
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }

  .alarmList {
    height: calc(100% - 120px);
    overflow: auto;
  }
  .notList {
    position: relative;
    height: calc(100% - 1.44rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
/deep/ .van-tabs__line {
  background-color: #3562db;
}
</style>
