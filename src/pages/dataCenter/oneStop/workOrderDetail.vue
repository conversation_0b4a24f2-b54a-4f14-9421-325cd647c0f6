<template>
  <div class="inner">
    <Header title="工单详情" @backFun="goBack"></Header>
    <div class="content">
      <van-collapse v-model="activeNames">
        <van-collapse-item
          v-for="(item, index) in detailData"
          :key="index"
          :name="index"
          :id="item.operationCode == '6' ? 'completed' : ''"
        >
          <template #title>
            <div class="left-icon">
              <div class="iconfont">&#xe681;</div>
            </div>
            <div class="title-textTime">
              <div>
                <span class="point"></span>
                <span class="item-title">{{ item.operationType }}</span>
              </div>
              <span class="date">{{
                moment(item.createDate).format("YYYY.MM.DD HH:mm:ss")
              }}</span>
            </div>
          </template>
          <template #right-icon>
            <van-icon
              id="right-arrow"
              name="arrow-down"
              class="van-icon van-icon-arrow van-cell__right-icon"
              color="#C9CDD4"
            />
          </template>
          <!-- 创建工单 -->
          <div class="list-wrap" v-if="item.operationCode == '1'">
            <div class="list-item">
              <span class="list-itemTitle">工单号</span>
              <span class="list-itemContent">{{ item.workNum }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">工单类型</span>
              <span class="list-itemContent">{{ item.workTypeName }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">紧急程度</span>
              <span :class="{ urgent: ['1','0'].includes(item.urgencyDegreeCode) }">{{
                item.urgencyDegree
              }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">联系人</span>
              <span class="list-itemContent" style="color:#1aaff1" >{{ item.callerName }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">电话</span>
              <span class="list-itemContent" @click="makePhoneCall(item.sourcesPhone)">{{ item.sourcesPhone }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">职工工号</span>
              <span class="list-itemContent">{{ item.callerJobNum }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">问题描述</span>
              <span class="list-itemContent">{{
                item.questionDescription
              }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">录音</span>
              <span class="list-itemContent">
                <div class="voice-desc">
                  <div v-if="item.callerTapeUrl != ''" class="voice-content">
                    <div class="voice-btn">
                      <button
                        class="weui-btn weui-btn_primary"
                        style="padding-left: 26px"
                        v-if="play"
                        @click="handlePlayAudioClick(index)"
                      >
                        <div class="play-icon">
                          <img
                            src="@/assets/images/icon_wify2.png"
                            class="play-img"
                          />
                          <span>{{ audioDuration }}</span>
                        </div>
                        点击播放
                      </button>
                      <button
                        class="weui-btn weui-btn_primary"
                        style="padding-left: 26px"
                        v-else
                        @click="handlePauseAudioClick"
                      >
                        <div class="play-icon">
                          <img
                            src="@/assets/images/icon_wify1.gif"
                            class="play-img"
                          />
                          <span>{{ audioDuration }}</span>
                        </div>
                        正在播放
                      </button>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">图片</span>
              <div class="img-content">
                <div
                  class="img-wrapper"
                  v-for="(imgs, imgInd) in item.attachment"
                  :key="imgInd"
                >
                  <div class="img-box">
                    <img :src="$YBS.imgUrlTranslation(imgs)" class="img" @click="toView(item.attachment)" />
                  </div>
                </div>
              </div>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">来电号码</span>
              <span class="list-itemContent">{{ item.needPhone }}</span>
            </div>
            <div class="list-item" v-if="!(item.appointmentType!='预约'&& item.urgencyDegree=='一般')">
              <span class="list-itemTitle">服务时间</span>
              <span class="list-itemContent" v-if="item.appointmentDate == '0'"
                >立刻</span
              >
              <span class="list-itemContent" v-else style="color:red">{{ item.time }}</span>
            </div>
            <!-- <div class="list-item">
              <span class="list-itemTitle">申报来源</span>
              <span class="list-itemContent">{{item.repairWork == 1 ? "否" : "是"}}</span>
            </div> -->
            <div class="list-item">
              <span class="list-itemTitle">申报属性</span>
              <span class="list-itemContent">{{ item.typeSources }}</span>
            </div>
          </div>
          <!-- 已受理 -->
          <div class="list-wrap" v-if="item.operationCode == '2'">
            <div class="list-item">
              <span class="list-itemTitle">调度员</span>
              <span class="list-itemContent">{{ item.createByName }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">职工工号</span>
              <span class="list-itemContent">{{ item.createByNo }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">服务地点</span>
              <div class="user-list">
                <span
                  class="list-itemContent"
                  v-for="(childs, ind) in item.itemType"
                  :key="ind"
                >
                  {{ childs.localtion }}
                </span>
              </div>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">所属科室</span>
              <span class="list-itemContent">{{
                item.transportStartLocalOffice
              }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">服务事项</span>
              <div class="user-list">
                <span
                  class="list-itemContent"
                  v-for="(childs, ind) in item.itemType"
                  :key="ind"
                >
                  {{ childs.itemTypeName
                  }}{{
                    childs.itemDetailName
                      ? "-" + childs.itemDetailName
                      : childs.itemDetailName
                  }}-{{ childs.itemServiceName }}
                </span>
              </div>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">服务部门</span>
              <span class="list-itemContent">{{ item.designateDeptName }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">申报描述</span>
              <span class="list-itemContent">{{
                item.questionDescription
              }}</span>
            </div>
          </div>
          <!-- 已派工 -->
          <div class="list-wrap" v-if="item.operationCode == '3'">
            <div class="list-item">
              <span class="list-itemTitle">服务人员</span>
              <span class="list-itemContent">{{
                item.designatePersonName
              }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">人员电话</span>
              <span class="list-itemContent">{{
                item.designatePersonPhone
              }}</span>
            </div>
          </div>
          <!-- 已挂单 -->
          <div class="list-wrap" v-if="item.operationCode == '4'">
            <div class="list-item last-list">
              <span class="list-itemTitle">挂单说明</span>
              <span class="last-text">{{ item.disEntryOrdersReason }}</span>
            </div>
            <div class="list-item last-list">
              <span class="list-itemTitle">解决方案</span>
              <span class="last-text">{{ item.disEntryOrdersSolution }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">预计解决</span>
              <span class="list-itemContent">{{
                moment(item.disPlanSolutionTime).format("YYYY-MM-DD")
              }}</span>
            </div>
          </div>
          <!-- 已回访 -->
          <div class="list-wrap" v-if="item.operationCode == '5'">
            <div class="list-item last-list">
              <span class="list-itemTitle">回访说明</span>
              <span class="last-text">{{ item.feedbackExplain }}</span>
            </div>
          </div>
          <!-- 已完工 -->
          <div class="list-wrap" v-if="item.operationCode == '6'">
            <div class="list-item last-list">
              <span class="list-itemTitle">完工说明</span>
              <span class="last-text">{{ item.disFinishRemark }}</span>
            </div>
            <div class="list-item last-list">
              <span class="consumables">耗材实际使用</span>
              <div
                class="optionWarp"
                v-for="(i, ind) in item.actual"
                :key="ind"
              >
                <span>{{ i.depThreeTypeName }}</span
                ><span>{{ i.num }}</span>
              </div>
            </div>
            <div v-for="(i, ind) in item.taskMalfunctionList" :key="ind">
              <div class="list-item">
                <span class="list-itemTitle">故障原因</span>
                <span class="list-itemContent">{{ i.reasonName }}</span>
              </div>
              <div class="list-item">
                <span class="list-itemTitle"
                  >处理方法</span
                >
                <div v-for="(child, indexs) in i.methodList" :key="indexs">
                  <span class="list-itemContent">{{ child.methodName }}</span>
                </div>
              </div>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">总服务费</span>
              <span class="list-itemContent">{{ item.completePrice }}</span>
            </div>
            <div class="list-item last-list">
              <span class="list-itemTitle">工单附件</span>
              <div class="img-content">
                <div
                  class="img-wrapper"
                  v-for="(imgs, imgInd) in item.disAttachmentUrl"
                  :key="imgInd"
                >
                  <div class="img-box">
                    <img :src="$YBS.imgUrlTranslation(imgs)" class="img" @click="toView(item.disAttachmentUrl)" />
                  </div>
                </div>
              </div>
            </div>
            <div class="list-item satisfy">
              <span class="list-itemTitle">满意度评价</span>
              <span class="list-itemContent star">
                <span>
                  <img
                    src="@/assets/images/star.png"
                    height="17px"
                    alt=""
                    v-for="(i, ind) in number('show', item.disDegree)"
                    :key="ind"
                  /><img
                    src="@/assets/images/star_hidden.png"
                    height="17px"
                    alt=""
                    v-for="i in number('hidden', item.disDegree)"
                    :key="i + 6"
                  />
                </span>
                <span>{{ fomentEvaluation(item.disDegree) }}</span>
              </span>
            </div>
          </div>
          <!-- 取消 -->
          <div class="list-wrap" v-if="item.operationCode == '7'">
            <div class="list-item last-list">
              <span class="list-itemTitle">取消理由</span>
              <span class="last-text">{{ item.cancelReasonId }}</span>
            </div>
            <div class="list-item last-list">
              <span class="list-itemTitle">取消说明</span>
              <span class="last-text">{{ item.cancelExplain }}</span>
            </div>
          </div>
          <!-- 已督办 -->
          <div class="list-wrap" v-if="item.operationCode == '8'">
            <div class="list-item last-list">
              <span class="list-itemTitle">督办说明</span>
              <span class="last-text">{{ item.feedbackExplain }}</span>
            </div>
          </div>
          <!-- 转派 -->
          <div class="list-wrap" v-if="item.operationCode == '9'">
            <div class="list-item">
              <span class="list-itemTitle">服务部门</span>
              <span class="list-itemContent">{{ item.designateDeptName }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">服务人员</span>
              <span class="list-itemContent">{{
                item.designatePersonName
              }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">人员电话</span>
              <span class="list-itemContent">{{
                item.designatePersonPhone
              }}</span>
            </div>
            <div class="list-item last-list">
              <span class="list-itemTitle">转派说明</span>
              <span class="last-text">{{ item.feedbackExplain }}</span>
            </div>
          </div>
          <!-- 已变更 -->
          <div class="list-wrap" v-if="item.operationCode == '10'">
            <!-- <div class="list-item">
              <span class="list-itemTitle">原服务部门：</span>
              <span class="list-itemContent">{{ item.designateDeptName }}</span>
            </div> -->
            <div class="header">
              <div class="iconfont">&#xe681;</div>
              <div class="title-body">
                <span class="dot"></span>
                <span class="title">{{ item.operationType }}</span>
                <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
              </div>
              <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
            </div>
            <changed
              :changedData="item"
              :workTypeCode="workTypeCode"
              :template="template"
            ></changed>
          </div>
          <!-- 转派 -->
          <div class="list-wrap" v-if="item.operationCode == '11'">
            <div class="list-item">
              <span class="list-itemTitle">服务部门</span>
              <span class="list-itemContent">{{ item.designateDeptName }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">服务人员</span>
              <span class="list-itemContent">{{
                item.designatePersonName
              }}</span>
            </div>
            <div class="list-item">
              <span class="list-itemTitle">人员电话</span>
              <span class="list-itemContent">{{
                item.designatePersonPhone
              }}</span>
            </div>
            <div class="list-item last-list">
              <span class="list-itemTitle">转派说明</span>
              <span class="last-text">{{ item.feedbackExplain }}</span>
            </div>
          </div>
          <audio
            class="audio"
            controls
            ref="audio"
            @canplay="handleCanPlay"
            @ended="handleAudioEnded"
            :src="$YBS.imgUrlTranslation(item.callerTapeUrl)"
          ></audio>
        </van-collapse-item>
      </van-collapse>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { ImagePreview } from "vant";
import Changed from "@/pages/mine/components/Changed";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  mixins: [fontSizeMixin],
  components: {
    Changed
  },
  data() {
    return {
      moment,
      activeNames: [],
      detailData: [],
      play: true,
      audioDuration: "",
      workTypeCode: '',
      template: ''
      // imgList: [] //图片列表
    };
  },
  computed: {},
  watch: {},
  methods: {
     //点击电话号码拨打电话
     makePhoneCall(phoneNumber) {
      // 创建一个 a 元素
      const link = document.createElement('a');
      // 设置 href 属性为 tel 协议链接
      link.href = `tel:${phoneNumber}`;
      // 触发点击事件
      link.click();
    },
    number(type, val) {
      if (type == "show") {
        return Number(val);
      } else {
        return 5 - Number(val);
      }
    },
    getDetail(detail) {
      const parmate = {
        workNum: detail.workNum,
        taskId: detail.taskId
      };
      this.$api.getTaskDetail(parmate).then(res => {
        // if (res.code == '200') {
        this.$nextTick(() => {
          let newRes = res.map(item => {
            item.fold = false;
            return item;
          }); // 时间轴内容折叠/展开
          let baseInfo = newRes[0];
          newRes.forEach(item => {
            if (item.operationCode == 10) {
              item.itemType = baseInfo.itemType;
              item.transportName = baseInfo.transportName;
              item.sourcesDeptName = baseInfo.sourcesDeptName;
              item.transportEndLocalOfficeName =
                baseInfo.transportEndLocalOfficeCode;
              item.transportEndLocalOffice = baseInfo.transportEndLocalOffice;
              item.transportEndLocal = baseInfo.transportEndLocal;
              item.transportStartLocal = baseInfo.transportStartLocal;
              item.transportStartLocalOffice = baseInfo.transportStartLocalOffice;
              item.transportName = baseInfo.transportName;
              item.customtransportNum = baseInfo.customtransportNum;
              item.questionDescription = baseInfo.questionDescription;
            }
          });
          this.workTypeCode = newRes[0].workTypeCode;
          this.template = newRes[0].template;
          res.map((i, index) => {
            if (i.operationCode == "3") {
              i.personnel = [];
              const nameArr = i.designatePersonName.split(",");
              const phoneArr = i.designatePersonPhone.split(",");
              for (let f = 0; f < nameArr.length; f++) {
                const item = {
                  name: nameArr[f],
                  phone: phoneArr[f]
                };
                res[index].personnel.push(item);
              }
            }
          });
          this.detailData = res;
          this.detailData.forEach((item, index) => {
            this.activeNames.push(index)
            if (item.attachment) {
              item.attachment = this.$YBS.imgUrlTranslation(item.attachment)
            }
          });
        });
        // }
      });
    },
    timeFunc(val) {  },
    handleCanPlay() {
      // this.audioDuration = Math.round(this.$refs.audio.duration) + "″";
    },
    // 播放结束
    handleAudioEnded() {
      this.play = true;
    },
    // 播放
    handlePlayAudioClick(index) {
      this.play = false;
      const audio = this.$refs.audio[index];
      audio.play();
    },
    // 暂停
    handlePauseAudioClick() {
      this.play = true;
    },
    // 格式化评价分级
    fomentEvaluation(val) {
      switch (val) {
        case "1":
          return "非常差";
        case "2":
          return "差";
        case "3":
          return "一般";
        case "4":
          return "满意";
        case "5":
          return "非常满意";
      }
    },
    // 查看图片
    toView(urls) {
      ImagePreview({
        images: urls.map(url => this.$YBS.imgUrlTranslation(url)),
        showIndex: false, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    },
    goBack() {
      // this.$router.push({
      //   path: '/workOrderList',
      //   query: {
      //     type: this.$route.query.type,
      //     option: this.$route.query.option,
      //     from: this.$route.query.from || ''
      //   }
      // })
      this.$router.go(-1);
    }
  },
  created() {
    this.getDetail(this.$route.query.detail);
  },
  mounted() {
    try {
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    } catch (e) {
      console.log(e);
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  height: 100vh;
  overflow-y: auto;
  background-color: #F2F4F9;
  font-size: 14px;
  .content {
    padding: 10px 10px;
    margin-bottom: 20px;
    /deep/ .van-cell {
      display: flex;
      align-items: center;
      // overflow: visible;
      // padding: 10px 20px !important;
      padding-left: 24px !important;
    }
    .left-icon {
      // width: 24px;
      // height: 24px;
      position: absolute;
      left: -18px;
      top: 0;
      color: #3562DB;
    }
    .van-collapse {
      border-radius: 8px;
      // overflow: hidden;
    }
    /deep/ .van-cell__title {
      position: relative;
      // width: 240px !important;
      min-height: 30px !important;
      background-color: #eceef8;
      border-radius: 20px;
      // color: #252525 !important;
      // padding-left:0;
      font-weight: 600;
      line-height: 30px;
      // display: flex;
      // align-items: center;
      // justify-content: flex-start;
      .title-textTime {
        display: flex;
        .point {
          margin: 0 2px 2px 10px;
          display: inline-block;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: #3562DB;
        }
        .date {
          color: #4f87fb;
          font-weight: normal;
        }
      }
    }
    /deep/ .van-cell__title::before {
      content: none;
    }
    /deep/ .van-collapse-item__content {
      padding: 0;
      .list-wrap {
        padding-left: 20px;
        background-color: #fff;
        .list-item {
          padding-left: 4px;
          line-height: 48px;
          // border-bottom: 1px solid #eceef8;
          font-size: calc(16px * var(--font-scale))!important;
          display: flex;
          .list-itemTitle {
            min-width: 75px;
            color: #353535;
          }
          .urgent {
            color: #ff0000;
          }
          .list-itemContent {
            color: #888;
            overflow: scroll;
            // white-space: nowrap;
            .voice-desc {
              display: flex;
              align-items: center;
              padding: 8px;
              background-color: #fff;
              .text-title {
                width: 2.4rem;
                color: #888;
              }
              .weui-btn {
                height: 28px;
                font-size: 14px;
              }
              button {
                display: flex;
                align-items: center;
                justify-content: center;
                .play-icon {
                  display: flex;
                  position: absolute;
                  align-items: center;
                  left: 0;
                  justify-content: center;
                  padding-left: 5px;
                  .play-img {
                    width: 12px;
                  }
                  span {
                    font-size: 14px;
                    padding-left: 5px;
                  }
                }
              }
            }
            .voice-content {
              width: 100%;
              .voice-btn {
                flex: 1;
              }
            }
          }
          .user-list {
            display: flex;
            flex-direction: column;
          }
        }
        .last-list {
          display: flex;
          flex-direction: column;
          .last-text {
            padding: 0 24px 24px 0;
            color: #888;
            line-height: 24px;
          }
        }
        .satisfy {
          padding-right: 20px;
          border: none;
          display: flex;
          justify-content: space-between;
          .star {
            display: flex;
            align-items: center;
            > span:last-child {
              padding-left: 10px;
            }
          }
        }
      }
    }
    #completed {
      .last-list {
        display: flex;
        flex-direction: column;
        .consumables {
          color: #353535;
        }
        .optionWarp {
          display: flex;
          font-size: 15px;
          color: #888;
          span {
            width: 50%;
            text-align: center;
          }
        }
        .optionWarp > span:last-child {
          text-align: left;
        }
      }
    }
  }
  .audio {
    display: none;
  }
  .img-content {
    background: #fff;
    padding: 0 0.3rem;
    overflow: auto;
    display: flex;
    .img-wrapper {
      flex-shrink: 0;
      width: 30%;
      // height: 1.6rem;
      margin: 0.2rem;
      position: relative;
      .img-box {
        height: 100%;
        overflow: hidden;

        .img {
          width: 100%;
        }
      }
      .icon-img {
        position: absolute;
        top: -0.1rem;
        right: -0.1rem;
        width: 0.5rem;
      }
    }
  }
}
/deep/ .title-textTime {
  font-size: calc(16px * var(--font-scale))!important;
  .date {
    max-width: 100px;
  }
  >div:nth-child(1) {
    margin-right: 10px;
    display: flex;
    align-items: center;
  }
}
</style>
