<template>
  <div class="inner">
    <Header title="班组维修量分析" @backFun="goback()"></Header>
    <div class="tabs">
      <div class="changeBtn">
        <div v-for="(item, index) in tabArr" :key="index" class="headerBtn" :class="['btn', dateType == item.id ? 'active-btn' : '']" @click="changeType(item.id)">
          <span>{{ item.title }}</span>
        </div>
      </div>
      <div v-if="tableData.length > 0" id="chart1" style="width: 100%; height: 240px"></div>
      <div v-else class="notCharts">
        <img height="100%" src="../../../assets/images/equipmentManagement/缺省-图表@2x.png" alt="" />
        <span class="nullText">数据为空，请刷新再试</span>
      </div>
      <el-table :data="tableData" style="width: 100%" :max-height="maxHeight" header-row-class-name="header-style">
        <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
        <el-table-column prop="designateDeptName" label="班组名称"> </el-table-column>
        <el-table-column prop="maintenancecount" label="服务数量" align="center" width="50">
          <template slot-scope="scope">
            <el-link type="primary" @click="goWorkOrderList(scope.row.maintenancecount || 0, scope)">{{ scope.row.maintenancecount || 0 }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="responseTime" label="平均响应时间" width="80"> </el-table-column>
        <el-table-column prop="averageFinishTime" label="平均完成时间" width="80"> </el-table-column>
      </el-table>
      <van-calendar v-model="show" type="range" @confirm="onConfirm" color="#29BEBC" :min-date="minDate" :max-date="maxDate" />
    </div>
  </div>
</template>
<script>
import moment from "moment";
export default {
  components: {},
  data() {
    return {
      moment,
      maxHeight: "",
      show: false,
      active: 0,
      tabArr: [
        {
          id: "day",
          title: "今日",
          detail: [],
        },
        {
          id: "week",
          title: "本周",
          detail: [],
        },
        {
          id: "month",
          title: "本月",
          detail: [],
        },
        {
          id: "year",
          title: "本年",
          detail: [],
        },
        {
          id: "",
          title: "自定义",
          detail: [],
        },
      ],
      tableData: [],
      dateType: "day",
      paramte: {
        dateType: "day",
      },
      minDate: new Date(new Date().setTime(new Date() - 31536000000)),
      maxDate: new Date(),
    };
  },
  computed: {},
  watch: {},
  methods: {
    changeType(val) {
      this.dateType = val;
      this.paramte.dateType = val;
      if (val == "") {
        this.show = true;
      } else {
        this.show = false;
        this.paramte = {
          dateType: this.dateType,
        };
        this.getFixData();
      }
    },
    getFixData() {
      this.$toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0,
      });
      this.$api.getDepartmentMaintenance(this.paramte).then((res) => {
        this.$toast.clear();
        this.tableData = res.list;
        this.$nextTick(() => {
          this.initChart();
        });
      });
    },
    initChart() {
      if (this.tableData.length == 0) {
        const notCharts = document.getElementsByClassName("notCharts")[0];
        const childCharts = notCharts.getElementsByTagName("div");
        if (childCharts.length > 0) {
          childCharts[0].style.display = "none";
        }
      } else {
        const Charts = document.getElementById("chart1");
        if (Charts.getElementsByTagName("div")[0]) {
          Charts.getElementsByTagName("div")[0].style.display = "block";
        }
        let myChart1 = this.$echarts.init(document.getElementById("chart1"));
        const chartsData = [];
        this.tableData.forEach((i) => {
          const item = {
            name: i.designateDeptName,
            value: i.maintenancecount,
          };
          chartsData.push(item);
        });
        myChart1.setOption({
          backgroundColor: "#fff",
          color: [
            "#FFBD93",
            "#FFCC61",
            "#6A9BFF",
            "#2E86DE",
            "#00D2D3",
            "#10AC84",
            "#34B253",
            "#01A3A4",
            "#9B59B6",
            "#D6A2E8",
            "#F18080",
            "#C66868",
            "#80D6FF",
            "#7C88C8",
            "#994DE2",
            "#B24A6F",
            "#527D55",
            "#5140A2",
            "#868895",
          ],
          legend: {
            icon: "circle",
            type: "scroll",
            pageIconSize: 14,
            orient: "vertical",
            itemGap: 15,
            itemWidth: 10,
            itemHeight: 10,
            right: "5%",
            top: "15%",
            formatter: (name) => {
              const count = chartsData.find((i) => i.name == name);
              if (name.length > 5) {
                return name.slice(0, 5) + "..." + "(" + count.value + "次)";
              } else {
                return name + "(" + count.value + "次)";
              }
            },
          },
          series: [
            {
              itemStyle: {
                borderColor: "#fff",
                borderWidth: 2,
              },
              type: "pie",
              radius: "65%",
              center: ["25%", "50%"],
              data: chartsData,
              hoverAnimation: false,
              label: {
                show: false,
              },
              stillShowZeroSum: false, //值为0不显示
            },
          ],
        });
      }
    },
    onConfirm(date) {
      this.paramte.startTime = this.moment(date[0]).format("YYYY-MM-DD");
      this.paramte.endTime = this.moment(date[1]).format("YYYY-MM-DD");
      this.getFixData();
      this.show = false;
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    goWorkOrderList(count, row) {
      if (count == 0) {
        $.toast("暂无更多", "text");
      } else {
        const option = {
          designateDeptCodes: row.row.designateDeptCode, //code
          flowcode: row.column.property == "completionCount" ? 5 : "", //是否完工
          startTime: "",
          endTime: "",
        };
        if (this.dateType == "week") {
          option.startTime = this.moment().weekday(1).format("YYYY-MM-DD");
          option.endTime = this.moment().weekday(7).format("YYYY-MM-DD");
        } else if (this.dateType == "month") {
          option.startTime = this.moment().format("YYYY-MM") + "-01";
          option.endTime = this.moment().format("YYYY-MM") + "-31";
        } else if (this.dateType == "year") {
          option.startTime = this.moment().format("YYYY") + "-01-01";
          option.endTime = this.moment().format("YYYY") + "-12-31";
        } else {
          option.startTime = this.paramte.startTime;
          option.endTime = this.paramte.endTime;
        }
        console.log(option);
        this.$router.push({
          path: "/workOrderList",
          query: {
            type: this.dateType,
            option: JSON.stringify(option),
            from: "teamFixCount",
          },
        });
      }
    },
  },
  created() {
    const windowHeight = parseInt(window.innerHeight);
    this.maxHeight = windowHeight - 352;
    this.getFixData();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
};
</script>
<style lang="scss" scoped>
.changeBtn {
  width: 100%;
  height: 42px;
  background-color: #fff;
  // border-bottom: 10px solid #EBEEF5;
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  .headerBtn {
    width: 68px;
    text-align: center;
    line-height: 42px;
  }
  .active-btn {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    // border-bottom: 2px solid #29BEBC;
    background-color: #e6effc;
    color: #3562db;
  }
}
.notCharts {
  padding: 0 0.32rem;
  height: 230px;
  text-align: center;
  position: relative;
  .nullText {
    position: absolute;
    bottom: 1rem;
    left: calc(50% - 1.4rem);
  }
}
/deep/ .el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border: none;
}
/deep/ .header-style > th {
  color: #2d4a74;
  font-weight: normal;
  border-bottom: 5px solid #ebeef5 !important;
}
/deep/ .el-table__header {
  th {
    background-color: #e6effc;
    color: #1d2129;
  }
}
</style>
