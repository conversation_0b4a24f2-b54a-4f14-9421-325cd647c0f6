<template>
  <div class='inner'>
    <Header :title="titleText" @backFun="goBack"></Header>
    <div class="content">
      <div class="header">
        <div class="option" @click="filterList(1)" :style="{color: active == 1 ? '#3562DB' : ''}">
          <span class="count" :style="{color: active == 1 ? '#3562DB' : ''}">{{headerData.count || 0}}</span>
          <span class="countText" :style="{color: active == 1 ? '#3562DB' : ''}">全部</span>
        </div>
        <div class="option" @click="filterList(2)" :style="{color: active == 2 ? '#3562DB' : ''}">
          <span class="count" :style="{color: active == 2 ? '#3562DB' : ''}">{{headerData.notWorkNum || 0}}</span>
          <span class="countText" :style="{color: active == 2 ? '#3562DB' : ''}">未派工</span>
        </div>
        <div class="option" @click="filterList(4)" :style="{color: active == 4 ? '#3562DB' : ''}">
          <span class="count" :style="{color: active == 4 ? '#3562DB' : ''}">{{headerData.entryOrdersNum || 0}}</span>
          <span class="countText" :style="{color: active == 4 ? '#3562DB' : ''}">已挂单</span>
        </div>
        <div class="option" @click="filterList(5)" :style="{color: active == 5 ? '#3562DB' : ''}">
          <span class="count" :style="{color: active == 5 ? '#3562DB' : ''}">{{headerData.completeNum || 0}}</span>
          <span class="countText" :style="{color: active == 5 ? '#3562DB' : ''}">已结束</span>
        </div>
      </div>
      <div v-if="listData.length == 0" class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
      <van-pull-refresh
        v-model="refreshing"
        @refresh="onRefresh"
        success-text="刷新成功"
        v-else
      >
        <div ref="listWarp" class="listWarp" @scroll="allScoroll($event)">
          <div class="list" v-for="(item,index) in listData" :key="index" @click="goDetail(item)">
            <div class="title">
              <span class="titleText">{{item.workTypeName}}</span>
              <div class="contentRight">
                <span :class="colorType(item.flowcode)" style="font-size: 12px">
                  {{item.flowtype}}
                </span>
                <span class="rightImgWarp">
                  <img height="16px" src="@/assets/images/ic_right.png" alt="">
                </span>
              </div>
            </div>
            <div class="contentWarp">
              <div style="margin-bottom: 5px;">
                <span class="watchWarp">
                  <img width="14px" src="@/assets/images/ic-watch@2x (1).png" alt="">
                </span>
                <span style="color: #86909C">{{moment(item.createDate).format('YYYY-MM-DD')}}</span>
              </div>
            </div>
            <div class="contentWarp">
              <div>
                <span class="itemTitle">服务事项：</span>
                <span class="itemContent">{{item.itemServiceName}}</span>
              </div>
            </div>
            <div class="contentWarp" style="margin-top: 10px;">
              <div>
                <span class="itemTitle">服务地点：</span>
                <span class="itemContent">{{item.localtionName}}</span>
              </div>
            </div>
          </div>
        </div>
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
  export default {
    components: {},
    data() {
      return {
        moment,
        loginInfo: '',
        titleText: '',
        listData: [],
        headerData: {},
        params: {
          interfaceNum: 2, // 1：我的工单 2：全院工单
          curPage: 1,
          pageSize: 10,
          startTime: '', // 不传查今日
          endTime: '', // 不传查今日
          sourcesDept: '',//部门code查询
          depId: '',//耗材code查询
          itemServiceCode: '',//事项code查询
          localtion: '',//区域code查询
          designateDeptCodes: '',//班组code查询
          flowcod: '',//条件
          type: '',
          unitCode: '',
          hospitalCode: ''
        },
        finished: true,
        refreshing: false,
        active: 1
      }
    },
    computed: {},
    watch: {},
    methods: {
      onRefresh() {
        this.params.curPage = 1
        this.params.pageSize = 10
        this.finished = true
        this.getData()
      },
      filterList (type) {
        this.active = type
        this.params.type = type == 1 ? '' : type
        this.params.curPage = 1
        this.params.pageSize = 10
        this.finished = true
        this.getData()
      },
      getData (type) {
        console.log(this.params)
        this.axios
        .get(__PATH.ONESTOP + "/appOlgTaskManagement.do?getMineTaskForApp", {
          params: this.params,
          headers: {
            Authorization:
              "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          if (res.data.code == '200') {
            this.$nextTick(() => {
              if (type == 'pull') {
                if (res.data.data.length == 0) {
                  this.finished = false
                } else if (res.data.data.length > 0) {
                  res.data.data.forEach( i => this.listData.push(i))
                }
              } else {
                this.listData = res.data.data
              }
              if (res.data.data.length == 0) {
                this.finished = false
              }
              // this.headerData = res.data.getWorkOrderNumber
              this.refreshing = false
              console.log(res.data)
            })
          }
        });
      },
      goDetail (item) {
        this.$router.push({
          path: '/workOrderDetail',
          query: {
            detail: item,
            type: this.$route.query.type,
            option: this.$route.query.option,
            from: this.$route.query.from || ''
          }
        })
      },
      colorType (status) {
        if (status == 1) {
          return 'pink'
        } else if (status == 2) {
          return 'purple'
        } else if (status == 3 || status == 10) {
          return 'blue'
        } else if (status == 4) {
          return 'green'
        } else if (status == 5 || status == 8) {
          return 'yellow'
        } else if (status == 6) {
          return 'gray'
        }
      },
      isscroll () {
        let scrollY = document.documentElement.scrollTop || document.body.scrollTop
        let vh = document.documentElement.clientHeight;
        const scrollHeight = document.documentElement.scrollHeight
        if (scrollY + vh + 10 >= scrollHeight && this.finished) {
          ++this.params.curPage
          this.thorttle(this.getData('pull'))
        }
      },
      // 节流
      thorttle (fn) {
        let timer = null
        return () => {
          if (!timer) {
            timer = setTimeout(() => {
              fn.call(this)
              timer = null
            }, 1000)
          }
        }
      },
      goBack () {
        this.$router.go(-1)
      },
      allScoroll(e) {
        console.log(e)
      }
    },
    created() {
      console.log('this.$route.query', this.$route.query)
      this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
      this.params.unitCode = this.loginInfo.unitCode
      this.params.hospitalCode = this.loginInfo.hospitalCode
      if (this.$route.query.type == 'day') {
        this.titleText = '本日工单'
      } else if (this.$route.query.type == 'monts') {
        this.titleText = '本月工单'
        this.params.startTime = this.moment().format('YYYY-MM') + '-01'
        this.params.endTime = this.moment().format('YYYY-MM') + '-31'
      } else {
        this.titleText = '工单列表'
      }
      if (this.$route.query.option) {
        this.params.sourcesDept = JSON.parse(this.$route.query.option).sourcesDept
        this.params.depId = JSON.parse(this.$route.query.option).depId
        this.params.flowcode = JSON.parse(this.$route.query.option).flowcode
        this.params.itemServiceCode = JSON.parse(this.$route.query.option).itemServiceCode
        this.params.localtion = JSON.parse(this.$route.query.option).id
        this.params.designateDeptCodes = JSON.parse(this.$route.query.option).designateDeptCodes
        this.params.startTime = JSON.parse(this.$route.query.option).startTime
        this.params.endTime = JSON.parse(this.$route.query.option).endTime
      }
      // this.getData()
      this.axios
        .get(__PATH.ONESTOP + "/appOlgTaskManagement.do?getMineTaskForApp", {
          params: this.params,
          headers: {
            Authorization:
              "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          if (res.data.code == '200') {
            this.$nextTick(() => {
              this.listData = res.data.data
              if (res.data.data.length == 0) {
                this.finished = false
              }
              this.headerData = res.data.getWorkOrderNumber
              this.refreshing = false
            })
          }
        });
    },
    mounted() {
      window.onscroll = this.isscroll
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    },
    beforeDestroy() {
      window.onscroll = null
    }
  }
</script>
<style lang="scss" scoped>
.inner {
  min-height: 100vh;
  background-color: #F2F4F9 !important;
  .content {
    height: 90vh;
    .header {
      height: 86px;
      background-color: #fff;
      display: flex;
      .option {
        width: 25%;
        font-size: 14px;
        font-family: 'HarmonyOS Sans SC';
        color: #353535;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .count {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        .countText {
          color: #86909C;
        }
      }
    }
    .listWarp {
      overflow: auto;
      font-size: 14px;
      padding: 0 10px;
      .list {
        margin-top: 10px;
        border-radius: 5px;
        padding: 20px 16px;
        height: 94px;
        background-color: #fff;
        border-bottom: 1px solid #FFEDEEF2;
        .title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .fixWarp {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #E4EFFF;
            border-radius: 50%;
            text-align: center;
            width: 22px;
            height: 22px;
          }
          .titleText {
            color: #353535;
            font-size: 16px;
            font-weight: 600;
          }
          .watchWarp {
            margin: 0 6px 0 20px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .contentWarp {
          margin-top: 8px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .itemTitle {
            color: #B3B3B3;
          }
          .itemContent {
            color: #353535;
          }
          .contentRight {
            display: flex;
            align-items: center;
            .rightImgWarp {
              display: flex;
              align-items: center;
            }
          }
          .contentRight>span:nth-child(1) {
            font-size: 15px;
            // color: #2EC0BE;
            margin-right: 8px;
          }
        }
      }
    }
    /deep/ .van-pull-refresh {
      // height: calc(100% - 96px);
      overflow: auto;
      background-color: #F2F4F9 !important;
    }
  }
  .notList {
    position: relative;
    height: calc(100% - 86px);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url('../../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
.purple {
  color:#F53F3F;
  padding: 4px 6px;
  background-color: #FFECE8;
  margin-right: 8px;
}
.pink {
  color: #FF7D9C;
  padding: 4px 6px;
  background-color: #fceff2;
  margin-right: 8px;
}
.yellow {
  color: #FFB034;
  padding: 4px 6px;
  background-color: #fff8ed;
  margin-right: 8px;
}
.green {
  // color: #87D0C0;
  // padding: 4px 6px;
  // background-color: #e1fff9;
  // margin-right: 8px;
  color:#F53F3F;
  padding: 4px 6px;
  background-color: #FFECE8;
  margin-right: 8px;
}
.blue {
  color: #00B42A;
  padding: 4px 6px;
  background-color: #E8FFEA;
  margin-right: 8px;
}
.gray {
  color: #4E5969;
  padding: 4px 6px;
  background-color: #F2F3F5;
  margin-right: 8px;
}
</style>
