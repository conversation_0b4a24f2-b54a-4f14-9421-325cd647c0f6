<template>
  <div class="inner">
    <Header title="报修区域分析" @backFun="goback"></Header>
    <div class="tabs">
      <div class="changeBtn">
        <div v-for="(item, index) in tabArr" :key="index" class="headerBtn" :class="['btn', dateType == item.id ? 'active-btn' : '']" @click="changeType(item.id)">
          <span>{{ item.title }}</span>
        </div>
      </div>
      <div v-if="tableData.length > 0" id="chart1" style="width: 100%; height: 230px"></div>
      <div v-else class="notCharts">
        <img height="100%" src="../../../assets/images/equipmentManagement/缺省-图表@2x.png" alt="" />
        <span class="nullText">数据为空，请刷新再试</span>
      </div>
      <el-table :data="tableData" style="width: 100%" :max-height="maxHeight" header-row-class-name="header-style">
        <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
        <el-table-column label="维修区域" align="left">
          <template slot-scope="scope">
            <span>
              {{ formatLocaltion(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="allCount" label="数量" align="center" width="50">
          <template slot-scope="scope">
            <el-link type="primary" style="color: #3f63d3" @click="goWorkOrderList(scope.row.allCount || 0, scope)">{{ scope.row.allCount || 0 }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="serviceHB" label="环比" align="center" width="80">
          <template slot-scope="scope">
            <span :class="formatColor(scope.row.serviceHB)">{{ scope.row.serviceHB }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="proportion" label="百分比" align="center" width="80"> </el-table-column>
      </el-table>
      <van-calendar v-model="show" type="range" @confirm="onConfirm" color="#3562DB" :min-date="minDate" :max-date="maxDate" />
    </div>
  </div>
</template>
<script>
import YBS from "@/assets/utils/utils.js";
import moment from "moment";
export default {
  components: {},
  data() {
    return {
      moment,
      maxHeight: "",
      show: false,
      active: 0,
      tabArr: [
        {
          id: 1,
          title: "今日",
          detail: []
        },
        {
          id: 2,
          title: "本周",
          detail: []
        },
        {
          id: 3,
          title: "本月",
          detail: []
        },
        {
          id: 4,
          title: "本年",
          detail: []
        },
        {
          id: 5,
          title: "自定义",
          detail: []
        }
      ],
      tableData: [],
      dateType: 1,
      paramte: {
        btnType: 1,
        startTime: "",
        endTime: ""
      },
      minDate: new Date(new Date().setTime(new Date() - 31536000000)),
      maxDate: new Date()
    };
  },
  computed: {},
  watch: {},
  methods: {
    changeType(val) {
      this.dateType = val;
      this.paramte.btnType = val;
      if (val == 5) {
        this.show = true;
      } else {
        this.show = false;
        this.getFixData();
      }
    },
    getFixData() {
      this.$toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.getRepairArea(this.paramte).then(res => {
        this.$toast.clear();
        // this.tableData = res.filter(i => i.allCount > 0)
        this.tableData = res;
        this.$nextTick(() => {
          this.initChart();
        });
      });
    },
    initChart() {
      if (this.tableData.length == 0) {
        const notCharts = document.getElementsByClassName("notCharts")[0];
        const childCharts = notCharts.getElementsByTagName("div");
        if (childCharts.length > 0) {
          childCharts[0].style.display = "none";
        }
      } else {
        const Charts = document.getElementById("chart1");
        if (Charts.getElementsByTagName("div")[0]) {
          Charts.getElementsByTagName("div")[0].style.display = "block";
        }
        let myChart1 = this.$echarts.init(document.getElementById("chart1"));
        const xData = [];
        const yData = [];
        this.tableData.forEach(i => {
          xData.push(this.formatLocaltion(i));
          yData.push(i.allCount);
        });
        myChart1.setOption({
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            formatter: function (params) {
              var tooltipStr = "";
              params.forEach(function (item) {
                // 单独处理 axisValue，添加换行符 \n
                var axisValue = item.axisValue.length > 8 ? item.axisValue.substring(0, 8) + "..." : item.axisValue;
                tooltipStr += axisValue + "<br>" + item.marker + item.seriesName + ": " + item.value;
              });
              return tooltipStr;
            }
          },
          grid: {
            top: "5%",
            bottom: "20%",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: xData,
              axisTick: {
                show: false
              },
              axisLabel: {
                interval: 0,
                rotate: -20,
                margin: 20,
                align: "center",
                formatter: function (value, index) {
                  if (value.length > 5) {
                    return value.substring(0, 5) + "...";
                  } else {
                    return value;
                  }
                }
              },
              nameTextStyle: {
                fontSize: 13,
                color: "#86909C"
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              axisLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: "区域维修数",
              type: "bar",
              barWidth: "60%",
              data: yData,
              color: "#A0B8F6",
              barWidth: 15
            }
          ],
          dataZoom: [
            {
              type: "slider",
              realtime: true,
              startValue: 0,
              endValue: 6,
              height: 4,
              fillerColor: "rgba(17, 100, 210, 0.42)", // 滚动条颜色
              borderColor: "rgba(17, 100, 210, 0.12)",
              handleSize: 0, // 两边手柄尺寸
              showDetail: false, // 拖拽时是否展示滚动条两侧的文字
              top: "96%",
              zoomLock: true // 是否只平移不缩放
              // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
              // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
            },
            {
              type: "inside", // 支持内部鼠标滚动平移
              start: 0,
              end: 80,
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ]
        });
      }
    },
    onConfirm(date) {
      this.paramte.startTime = this.moment(date[0]).format("YYYY-MM-DD 00:00:00");
      this.paramte.endTime = this.moment(date[1]).format("YYYY-MM-DD 23:59:59");
      this.getFixData();
      this.show = false;
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    goWorkOrderList(count, row) {
      if (count == 0) {
        $.toast("暂无更多", "text");
      } else {
        let type = "";
        const option = {
          id: row.row.id, //code
          flowcode: row.column.property == "completionCount" ? 5 : "", //是否完工
          startTime: "",
          endTime: ""
        };
        if (this.dateType == 1) {
          type = "day";
        } else if (this.dateType == 2) {
          type = "monts";
          option.startTime = this.moment().weekday(1).format("YYYY-MM-DD");
          option.endTime = this.moment().weekday(7).format("YYYY-MM-DD");
        } else if (this.dateType == 3) {
          option.startTime = this.moment().format("YYYY-MM") + "-01";
          option.endTime = this.moment().format("YYYY-MM") + "-31";
        } else if (this.dateType == 4) {
          option.startTime = this.moment().format("YYYY") + "-01-01";
          option.endTime = this.moment().format("YYYY") + "-12-31";
        } else {
          option.startTime = this.paramte.startTime;
          option.endTime = this.paramte.endTime;
        }
        this.$router.push({
          path: "/workOrderList",
          query: {
            type,
            option: JSON.stringify(option),
            from: "repairArea"
          }
        });
      }
    },
    formatColor(val) {
      if (val == "0%" || val == "0.0%") {
        return false;
      } else if (val.indexOf("-") != 0) {
        return "redColor";
      } else if (val.indexOf("-") == 0) {
        return "greenColor";
      }
    },
    formatLocaltion(val) {
      if (val.parentName) {
        if (val.ssmName) {
          return val.parentName + ">" + val.ssmName;
        } else {
          return val.parentName;
        }
      } else {
        if (val.ssmName) {
          return val.ssmName;
        } else {
          return "";
        }
      }
    }
  },
  created() {
    const windowHeight = parseInt(window.innerHeight);
    this.maxHeight = windowHeight - 352;
    this.getFixData();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  }
};
</script>
<style lang="scss" scoped>
.changeBtn {
  width: 100%;
  height: 42px;
  background-color: #fff;
  border-bottom: 0.2rem solid #fff;
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  .headerBtn {
    width: 68px;
    text-align: center;
    line-height: 42px;
  }
  .active-btn {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    // border-bottom: 2px solid #29BEBC;
    background-color: #e6effc;
    color: #3562db;
  }
}
.notCharts {
  padding: 0 0.32rem;
  height: 230px;
  text-align: center;
  position: relative;
  .nullText {
    position: absolute;
    bottom: 1rem;
    left: calc(50% - 1.4rem);
  }
}
/deep/ .el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border: none;
}
/deep/ .header-style > th {
  color: #2d4a74;
  font-weight: normal;
  border-bottom: 5px solid #ebeef5 !important;
}
.redColor {
  color: #ff4848;
}
.greenColor {
  color: #42a441;
}
/deep/ .el-table__header {
  th {
    background-color: #e6effc;
    color: #1d2129;
  }
}
</style>
