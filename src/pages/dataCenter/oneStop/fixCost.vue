<template>
  <div class='inner'>
    <Header title="科室维修成本分析" @backFun="goback()"></Header>
    <div class="tabs">
      <div class="changeBtn">
        <div
          v-for="(item, index) in tabArr"
          :key="index"
          class="headerBtn"
          :class="['btn', dateType == item.id ? 'active-btn' : '']"
          @click="changeType(item.id)"
        >
          <span>{{item.title}}</span>
        </div>
      </div>
      <!-- <div v-if="!dateType">
        123
      </div> -->
      <el-table
        :data="tableData"
        style="width: 100%;border-top: 0.2rem solid #F2F4F9;"
        :max-height="maxHeight"
      >
        <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50"
        >
        </el-table-column>
        <el-table-column
          prop="sourcesDeptName"
          label="科室名称"
        >
        </el-table-column>
        <el-table-column
          prop="count"
          label="报修总量"
          align="center"
          width="80"
        >
          <template slot-scope="scope">
            <el-link type="primary" @click="goWorkOrderList(scope.row.count || 0, scope)">{{ scope.row.count || 0 }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="completionCount"
          label="已完工"
          align="center"
          width="80"
        >
          <template slot-scope="scope">
            <el-link type="primary" @click="goWorkOrderList(scope.row.completionCount || 0, scope)">{{ scope.row.completionCount || 0 }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="totalPrice"
          label="费用"
          align="center"
          width="80"
        >
        </el-table-column>
      </el-table>
      <van-calendar
        v-model="show"
        type="range"
        @confirm="onConfirm"
        color="#3562DB"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </div>
  </div>
</template>
<script>
import YBS from "@/assets/utils/utils.js";
import moment from "moment"
  export default {
    components: {},
    data() {
      return {
        moment,
        maxHeight: '',
        show: false,
        active: 0,
        tabArr: [
          {
            id: 'day',
            title: '今日',
            detail: []
          },
          {
            id: 'week',
            title: '本周',
            detail: []
          },
          {
            id: 'month',
            title: '本月',
            detail: []
          },
          {
            id: 'year',
            title: '本年',
            detail: []
          },
          {
            id: '',
            title: '自定义',
            detail: []
          }
        ],
        tableData: [],
        dateType: 'day',
        paramte: {
          dateType: 'day',
        },
        minDate: new Date(new Date().setTime(new Date() - 31536000000)),
        maxDate: new Date()
      }
    },
    computed: {},
    watch: {},
    methods: {
      changeType (val) {
        this.dateType = val
        this.paramte.dateType = val
        if (val == '') {
          this.show = true
        } else {
          this.show = false
          this.paramte = {
            dateType: this.dateType,
          }
          this.getFixData()
        }
      },
      getFixData () {
        this.$api.getMaintenanceCosts(this.paramte).then(res => {
          this.tableData = res.list
        })
      },
      onConfirm (date) {
        this.paramte.startTime = this.moment(date[0]).format('YYYY-MM-DD')
        this.paramte.endTime = this.moment(date[1]).format('YYYY-MM-DD')
        this.getFixData()
        this.show = false
      },
      goback () {
        this.$YBS.apiCloudCloseFrame()
      },
      goWorkOrderList (count, row) {
        if (count == 0) {
          $.toast("暂无更多",'text');
        } else {
          const option = {
            sourcesDept: row.row.sourcesDept,//code
            flowcode: row.column.property == 'completionCount' ? 5 : '',//是否完工
            startTime: '',
            endTime: ''
          }
          if (this.dateType == 'week') {
            option.startTime = this.moment().weekday(1).format('YYYY-MM-DD')
            option.endTime = this.moment().weekday(7).format('YYYY-MM-DD')
          } else if (this.dateType == 'month') {
            option.startTime = this.moment().format('YYYY-MM') + '-01'
            option.endTime = this.moment().format('YYYY-MM') + '-31'
          } else if (this.dateType == 'year') {
            option.startTime = this.moment().format('YYYY') + '-01-01'
            option.endTime = this.moment().format('YYYY') + '-12-31'
          } else {
            option.startTime = this.paramte.startTime
            option.endTime = this.paramte.endTime
          }
          this.$router.push({
            path: '/workOrderList',
            query: {
              type: this.dateType,
              option: JSON.stringify(option),
              from: 'fixCost'
            }
          })
        }
      },
    },
    created() {
      const windowHeight = parseInt(window.innerHeight)
      this.maxHeight = (windowHeight - 112)
      this.getFixData()
    },
    mounted() {
      this.$YBS.apiCloudEventKeyBack(this.goback);
    }
  }
</script>
<style lang="scss" scoped>
.changeBtn {
  width: 100%;
  height: 42px;
  background-color: #fff;
  border-bottom: 0.2rem solid #fff;
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  .headerBtn {
    width: 68px;
    text-align: center;
    line-height: 42px;
  }
  .active-btn {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    // border-bottom: 2px solid #29BEBC;
    background-color: #E6EFFC;
    color: #3562DB;
  }
}
/deep/ .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border: none;
}
/deep/ .el-link.el-link--primary {
  color: #3562DB;
}
/deep/ .el-table__header {
  th {
    background-color: #E6EFFC;
    color: #1D2129;
  }
}
</style>
