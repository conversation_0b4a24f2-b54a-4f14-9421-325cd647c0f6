<template>
  <div class="content">
    <Header title="费用分析" @backFun="goback()"></Header>
    <div class="typeTop">
      <div class="line_type">本年能耗费用占比</div>
      <div
        v-if="pieData.length > 0"
        id="chart1"
        style="width: 100%;height: 400px"
      ></div>
      <div v-else class="executeCharts">
        <img
          height="100%"
          src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
    <div>
      <div class="line_type">每月能耗费用查看</div>
      <div
        v-if="barData.length > 0"
        id="chart2"
        style="width: 100%;height: 300px"
      ></div>
      <div v-else class="executeCharts">
        <img
          height="100%"
          src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%"
      :max-height="maxHeight"
      header-row-class-name="header-style"
      :summary-method="getSummaries"
      show-summary
    >
      <el-table-column prop="dataTime" label="类型" align="center" width="50">
      </el-table-column>
      <el-table-column
        :label="item.label + '(元)'"
        :prop="item.label"
        v-for="(item, index) in columns"
        :key="index"
        :align="item.align"
        :min-width="item.prop.length > 2 ? '91' : '71'"
      >
        <template slot-scope="scope" v-if="scope.row[item.label]">
          <span>{{ Number(scope.row[item.label]).toFixed(2)||0 }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import moment from "moment";
moment.locale("zh-cn");
export default {
  components: {},
  data() {
    return {
      energyId: "", // 能耗大类id
      kgceToken: "", // 能耗token
      upImg: "",
      maxHeight: "",
      dateType: "day",
      pieData: [],
      barData: [],
      unitItems: {
        电力: "kWh"
      }, // 能量单位
      tableData: [],
      columns: [
        // {
        //   label: "电力",
        //   prop: "kWh"
        // },
        // {
        //   label: "用水",
        //   prop: "t"
        // },
        // {
        //   label: "热能",
        //   prop: "GJ"
        // },
        // {
        //   label: "蒸汽",
        //   prop: "kg/m3"
        // }
      ]
    };
  },
  computed: {},
  watch: {},
  methods: {
    kgceLogin() {
      // const param = {
      //   username: "gy_energy",
      //   password: "Gy12345678!1"
      // };
      this.$api.KgceLogin(intAccount).then((res) => {
        if (res.code == 200) {
          this.kgceToken = res.result.token
          sessionStorage.setItem("kgceToken", this.kgceToken)
          this.getEnergyAndClassifyTree()
        }
      })
    },
    // 能耗所有大类和小类
    getEnergyAndClassifyTree() {
      this.$api.GetEnergyAndClassifyTree().then(res => {
          let newData = res.filter(
            item => item.name !== "综合能耗"
          )
          this.energyId=newData.map(item=>item.id).join(',')
          this.getYearData(); // 获取本年费用接口
          this.getMonthData(); // 获取月费用接口
      });
    },
    getModelEnergyDataList(params) {
      return new Promise((resolve, reject) => {
          this.$api.GetModelEnergyDataList(params)
            .then((res) => {
              // if (res.data.code === 200) {
                if (res.length) {
                  res.forEach((item) => {
                    this.unitItems[item.energyName] = item.energyUnitEn
                    let obj={}
                    obj.label=item.energyName
                    obj.prop=item.energyUnitEn
                    this.columns.push(obj)
                  })
                  var newObj={}
                  this.columns = this.columns.reduce(function(item, next) {
                    newObj[next.label] ? "" : (newObj[next.label] = true && item.push(next));
                    return item;
                  }, []);
                }
                resolve(res)
              // } else {
              //   reject(res)
              // }
            })
            .catch((err) => {
              reject(err)
            })
      });
    },
    // 获取本年大类费用
    getYearData() {
      let params = {
        emodelId: emodelId,
        energyId: this.energyId,
        // aggregation: "sum",
        valueName: "valueCost",
        timeCategory: "year",
        startDate:
          moment()
            .startOf("year")
            .format("YYYY-MM-DD") + " 00:00:00",
        endDate:
          moment()
            .endOf("year")
            .format("YYYY-MM-DD") + " 23:59:59",
        groupBy: "energyId"
      };
      this.getModelEnergyDataList({ ...params, groupBy: 'energyId' })
        .then((res) => {
          const arr = res
          if (arr.length) {
            this.pieData = arr
            this.$nextTick(()=>{
               this.getPieData(this.pieData,'chart1')
            })
          }
        })
        .catch()
    },
    getPieData(arr, id) {
      const getchart = this.$echarts.init(document.getElementById(id));
      const nameList = Array.from(arr, item => item.energyName);
      const valueList = Array.from(arr, item =>
        item.valueCost ? item.valueCost.toFixed(2) : 0
      );
      const data = [];
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i]
        });
      }
      getchart.setOption({
        backgroundColor: "#fff",
        color: ["#FF7D00", "#3562DB", "#FFCF8B", "#A0B8F6", "#FF8C66"],
        legend: {
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemWidth: 10,
          itemHeight: 10,
          bottom: "10%",
          left: "center",
          formatter: name => {
            let sum = 0;
            data.forEach(item => {
              sum = sum + Number(item.value);
            });
            const count = data.find(i => i.name == name);
            return (
              name +
              "  " +
              +count.value +
              "元" +
              " " +
              "占比：" +
              (count.value / sum).toFixed(2) * 100 +
              "%"
            );
          }
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2
          },
          type: "pie",
          radius: "50%",
          center: ["50%", "35%"],
          data: data,
          hoverAnimation: false,
          labelLine: {
            //指示线样式设置
            normal: {
              lineStyle: {
                color: "#4E5969" // 设置标示线的颜色
              }
            }
          },
          label: {
            normal: {
              textStyle: {
                color: "#4E5969"
              }
            }
          }
        }
      });
    },
    // 获取月费用接口
    getMonthData() {
      let params = {
        emodelId: emodelId,
        energyId: this.energyId,
        // aggregation: "sum",
        valueName: "valueCost",
        timeCategory: "month",
        startDate:
          moment()
            .startOf("year")
            .format("YYYY-MM-DD") + " 00:00:00",
        endDate:
          moment()
            .endOf("year")
            .format("YYYY-MM-DD") + " 23:59:59",
        groupBy: "energyId"
      };
      this.getModelEnergyDataList({ ...params, groupBy: "energyId" })
        .then(res => {
          const arr = res;
          if (arr.length) {
            arr.forEach(
              item => (item.dataTime = item.dataTime.slice(5, 7) + "月")
            );
            this.barData = arr;
            this.getTableData(this.barData);
            this.barData.sort((a, b) => {
              return a.dataTime.slice(0, 2) - b.dataTime.slice(0, 2);
            });
            this.$nextTick(() => {
              this.getBarData(this.barData, "chart2");
            });
          }
        })
        .catch();
    },
    getBarData(arr, id) {
      const getchart = this.$echarts.init(document.getElementById(id));
      let xtime = []; //x轴日期
      let lenged = []; //series的个数
      arr.map(item => {
        xtime.push(item["dataTime"]);
        lenged.push(item["energyName"]);
      });
      xtime = [...new Set(xtime)]; //去重
      lenged = [...new Set(lenged)];
      let series = [];
      lenged.map(item => {
        //生成  series
        let obj = {
          name: item,
          type: "bar",
          stack: "As",
          emphasis: {
            focus: "series"
          },
          barWidth: "30%",
          data: []
        };
        series.push(obj);
      });
      arr.map(item => {
        //对series 的data进行处理
        series.map(item1 => {
          if (
            item1.name == item["energyName"] &&
            xtime.indexOf(item["dataTime"]) > -1
          ) {
            item1.data[xtime.indexOf(item["dataTime"])] = item["valueCost"];
          }
        });
      });
      getchart.setOption({
        color: ["#FF7D00", "#3562DB", "#FFCF8B", "#A0B8F6"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          }
        },
        legend: {
          type: "scroll",
          pageIconSize: 14,
          itemWidth: 10,
          itemHeight: 10
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: xtime,
            axisLine: {
              show: true, //是否显示轴线
              lineStyle: {
                color: "#86909C" //刻度线的颜色
              }
            },
            axisTick: {
              alignWithLabel: true // 刻度与文案居中对齐
            },
            axisLabel: {
              color: "#86909C", //刻度线标签颜色
              interval: 0, //X轴内容过多造成内容缺失时加上这个内容就会显示出来
              rotate:-36
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            name: "单位：万元",
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            axisLabel: {
              color: "#86909C" //刻度线标签颜色
            }
          }
        ],
        series: series
      });
    },
    getTableData(newArray) {
      newArray.forEach(item => {
        this.columns.forEach(item1 => {
          if (item1.label == item.energyName) {
            item[item1.label] = item.valueCost;
          }
        });
      });
      let newArray1 = newArray.reduce((total, cur, index) => {
        let hasValue = total.findIndex(current => {
          return current.dataTime === cur.dataTime;
        });
        hasValue === -1 && total.push(cur);
        hasValue !== -1 && (total[hasValue] = { ...total[hasValue], ...cur });
        return total;
      }, []);
      newArray1.sort((a, b) => {
        return a.dataTime.slice(0, 2) - b.dataTime.slice(0, 2);
      });
      this.tableData = newArray1;
      console.log(this.tableData,'table');
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "年耗";
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = sums[index].toFixed(2)
        } else {
          sums[index] = " ";
        }
      });
      return sums;
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    }
  },
  created() {
    const windowHeight = parseInt(window.innerHeight);
    this.maxHeight = windowHeight - 352;
    this.kgceLogin()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback)
  }
};
</script>
<style lang="stylus" scoped>
/deep/ .el-table {
  display: flex;
  flex-direction: column;
}
/deep/ .el-table__body-wrapper {
  order: 1;
}
/deep/ .el-table__body{
  width:100% !important;
}
/deep/ .el-table__fixed-body-wrapper {
  top: 97px !important;
}
/deep/ .el-table__empty-block{
  width:100% !important;
}
/deep/ .el-table__fixed-footer-wrapper {
  z-index: 0;
  top: 50px;
}
.content{
  background-color: #fff;
}
/deep/ .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border: none;
}
/deep/ .header-style > th {
  color: #2D4A74;
  font-weight: normal;
  border-bottom: 5px solid #EBEEF5 !important;
}
.executeCharts {
  height: 32vh;
  text-align: center;
  position: relative;
  .nullText {
    position: absolute;
    bottom: 1rem;
    left: 43%
  }
}
.typeTop{
  padding:.1875rem
  line-height :normal
}
.line_type{
  height:0.8rem;
  line-height :0.8rem;
  font-size:16px;
  padding:.1875rem;
}

/deep/ .el-table .cell{
  font-size:12px;
}
</style>
