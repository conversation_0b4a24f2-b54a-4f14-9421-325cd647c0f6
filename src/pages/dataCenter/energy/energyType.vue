<template>
  <div class="content">
    <Header :title="title" @backFun="goback()"></Header>
    <div class="typeTop">
      <div class="item">
        <div class="timeTop">
          <span class="timeType">今日{{ showContentType }}</span>
          <span
            :class="getColor(electricityData.dayElectricity.momValueRatio)"
            >{{ electricityData.dayElectricity.momValueRatio || 0 }}%</span
          >
          <img
            v-if="electricityData.dayElectricity.momValueRatio"
            :src="
              electricityData.dayElectricity.momValueRatio
                .toString()
                .indexOf('-') != -1
                ? downImg
                : upImg
            "
            alt=""
          />
        </div>
        <div class="timeBottom">
          <span class="timeNum">{{
            electricityData.dayElectricity.value || 0
          }}</span>
          <span class="unit">{{ unitItems[showContentType] }}</span>
        </div>
      </div>
      <div class="item">
        <div class="timeTop">
          <span class="timeType">本月{{ showContentType }}</span>
          <span
            :class="getColor(electricityData.monthElectricity.momValueRatio)"
            >{{ electricityData.monthElectricity.momValueRatio || 0 }}%</span
          >
          <img
            v-if="electricityData.monthElectricity.momValueRatio"
            :src="
              electricityData.monthElectricity.momValueRatio
                .toString()
                .indexOf('-') != -1
                ? downImg
                : upImg
            "
            alt=""
          />
        </div>
        <div class="timeBottom">
          <span class="timeNum">{{
            electricityData.monthElectricity.value || 0
          }}</span>
          <span class="unit">{{ unitItems[showContentType] }}</span>
        </div>
      </div>
      <div class="item sum">
        <div class="timeTop">
          <span class="timeType">本年{{ showContentType }}</span>
        </div>
        <div>
          <span class="timeNum">{{
            electricityData.yearElectricity.value || 0
          }}</span>
          <span class="unit">{{ unitItems[showContentType] }}</span>
        </div>
      </div>
    </div>
    <div class="btn_tags">
      <div
        v-for="(item, index) in tabArr"
        :key="index"
        class="btn_item"
        :class="dateType == item.id ? 'btn_item_active' : ''"
        @click="changeType(item.id)"
      >
        {{ item.title }}
      </div>
    </div>
    <div
      v-show="chartsData.length > 0"
      id="contentTypePieEcharts"
      style="width: 100%;height: 400px"
    ></div>
    <div v-show="!chartsData.length" class="executeCharts">
      <img
        height="100%"
        src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
        alt=""
      />
      <span class="nullText">暂无数据</span>
    </div>
    <el-table
      :data="regionalList"
      style="width: 100%"
      :max-height="maxHeight"
      header-row-class-name="header-style"
    >
      <el-table-column type="index" label="序号" align="center" width="50">
      </el-table-column>
      <el-table-column prop="emodelName" label="建筑" align="center">
      </el-table-column>
      <el-table-column
        v-if="showContentType"
        prop="value"
        :label="showContentType + this.unitItems[this.showContentType]"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <span>{{ Number(scope.row.value).toFixed(2) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="lineContent">
      <div class="line_type">本月{{ showContentType }}趋势</div>
      <div
        v-show="lineMonthData.length > 0"
        id="analysisLineEcharts1"
        style="width: 100%;height: 260px"
      ></div>
      <div v-show="!lineMonthData.length" class="executeCharts">
        <img
          height="100%"
          src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
    <div class="lineContent">
      <div class="line_type">本年{{ showContentType }}趋势</div>
      <div
        v-show="lineYearData.length > 0"
        id="analysisLineEcharts2"
        style="width: 100%;height: 260px"
      ></div>
      <div v-show="!lineYearData.length" class="executeCharts">
        <img
          height="100%"
          src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
moment.locale("zh-cn");
import downImg from "@/assets/images/down-circle-fill.png";
import upImg from "@/assets/images/up-circle-fill.png";
export default {
  components: {},
  data() {
    return {
      title: "电耗分析",
      kgceToken: "",
      upImg,
      downImg,
      maxHeight: "",
      active: 0,
      tabArr: [
        {
          id: "day",
          title: "今日",
          detail: []
        },
        {
          id: "month",
          title: "本月",
          detail: []
        },
        {
          id: "year",
          title: "本年",
          detail: []
        }
      ],
      regionalList: [],
      dateType: "day",
      chartsData: [],
      lineData: [],
      lineMonthData: [],
      lineYearData: [],
      itemData: "",
      electricityData: {
        dayElectricity: {},
        weekElectricity: {},
        monthElectricity: {},
        yearElectricity: ""
      }, // 实时监测数据
      showContentType: "",
      unitItems: {
        电力: "kWh",
        用水: "t",
        热能: "GJ",
        蒸汽: "t"
      }, // 能量单位
      regionalList: [], // 排行列表
      energyId: "" // 能源大类id
    };
  },
  computed: {},
  watch: {},
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title =
        to.query.id == "SU035"
          ? "电耗分析"
          : to.query.id == "CM020"
          ? "水耗分析"
          : to.query.id == "4c78a70e0b7deb51b0a227fb2cd9196f"
          ? "热耗分析"
          : "汽耗分析";
    }
    next();
  },
  created() {
    const windowHeight = parseInt(window.innerHeight);
    this.maxHeight = windowHeight - 352;
    this.energyId = this.$route.query.id || "";
    this.title =
      this.energyId == "SU035"
        ? "电耗分析"
        : this.energyId == "CM020"
        ? "水耗分析"
        : this.energyId == "4c78a70e0b7deb51b0a227fb2cd9196f"
        ? "热耗分析"
        : "汽耗分析";
    this.kgceLogin()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback)
  },
  methods: {
    // 获取能耗token
    kgceLogin() {
      // const param = {
      //   username: "gy_energy",
      //   password: "Gy12345678!1"
      // };
      this.$api.KgceLogin(intAccount).then((res) => {
        if (res.code == 200) {
          this.kgceToken = res.result.token
          sessionStorage.setItem("kgceToken", this.kgceToken)
          this.getEnergyAndClassifyTree()
        }
      })
    },
    // 能耗所有大类和小类
    getEnergyAndClassifyTree() {
      this.$api.GetEnergyAndClassifyTree().then(res => {
          let newData = res.filter(
            item => item.name !== "综合能耗"
          )
          this.itemData=newData.find(item=>item.id==this.energyId)
          this.itemData.children.unshift({
            id: this.itemData.id,
            allId: this.itemData.children.map(v => v.id).join(","),
            name: "总览"
          });
          this.showContentType = this.itemData.name;
          this.init()
      });
    },
    init(){
      this.realTimeMonitor();
      this.typeAnalysis(); //
      this.regionalRanking(); // 区域排行
      this.energyTrend(this.lineMonthData, "analysisLineEcharts1", "month"); // 本月能耗趋势
      this.energyTrend(this.lineYearData, "analysisLineEcharts2", "year"); // 本年能耗趋势
    },
    changeType(val) {
      this.dateType = val;
      this.typeAnalysis();
      this.regionalRanking();
    },
    getParams() {
      const param = {
        aggregation: "sum",
        valueName: "value"
      };
      if (this.dateType === "day") {
        return {
          ...param,
          timeCategory: "day",
          startDate: moment().format("YYYY-MM-DD") + " 00:00:00",
          endDate: moment().format("YYYY-MM-DD") + " 23:59:59"
        };
      } else if (this.dateType === "month") {
        return {
          ...param,
          timeCategory: "month",
          startDate:
            moment()
              .startOf("month")
              .format("YYYY-MM-DD") + " 00:00:00",
          endDate:
            moment()
              .endOf("month")
              .format("YYYY-MM-DD") + " 23:59:59"
        };
      } else if (this.dateType === "year") {
        return {
          ...param,
          timeCategory: "year",
          startDate:
            moment()
              .startOf("year")
              .format("YYYY-MM-DD") + " 00:00:00",
          endDate:
            moment()
              .endOf("year")
              .format("YYYY-MM-DD") + " 23:59:59"
        };
      }
    },
    // 实时监测
    realTimeMonitor() {
      const params = {
        emodelId: emodelId, // 模型id
        // energyId: 'SU035' // 能源id
        energyId: this.itemData.children
          ? this.itemData.children[0].id
          : this.itemData.id
        // timeCategory: '' // 数据类型 （hour小时数据 day日数据 week周数据  month月数据 year年数据）
      };
      const date = {
        day: {
          startDate: moment().format("YYYY-MM-DD") + " 00:00:00",
          endDate: moment().format("YYYY-MM-DD") + " 23:59:59"
        },
        week: {
          startDate:
            moment()
              .startOf("week")
              .format("YYYY-MM-DD") + " 00:00:00",
          endDate:
            moment()
              .endOf("week")
              .format("YYYY-MM-DD") + " 23:59:59"
        },
        month: {
          startDate:
            moment()
              .startOf("month")
              .format("YYYY-MM-DD") + " 00:00:00",
          endDate:
            moment()
              .endOf("month")
              .format("YYYY-MM-DD") + " 23:59:59"
        },
        year: {
          startDate:
            moment()
              .startOf("year")
              .format("YYYY-MM-DD") + " 00:00:00",
          endDate:
            moment()
              .endOf("year")
              .format("YYYY-MM-DD") + " 23:59:59"
        }
      };
      const requestList = [
        {
          key: "dayElectricity",
          type: "day",
          data: { value: 0, momValueRatio: 0 }
        },
        {
          key: "monthElectricity",
          type: "month",
          data: { value: 0, momValueRatio: 0 }
        },
        {
          key: "yearElectricity",
          type: "year",
          data: { value: 0, momValueRatio: 0 }
        }
      ];
      for (let i = 0; i < requestList.length; i++) {
        this.electricityData[requestList[i].key] = requestList[i].data;
        this.getModelEnergyDataList({
          timeCategory: requestList[i].type,
          ...params,
          ...date[requestList[i].type]
        })
          .then(res => {
            this.electricityData[requestList[i].key] = {
              value: res[0].value
                ? res[0].value.toFixed(2)
                : 0,
              momValueRatio: res[0].momValueRatio
                ? res[0].momValueRatio.toFixed(2)
                : 0
            };
          })
          .catch();
      }
    },
    // 获取模型能源数据列表
    getModelEnergyDataList(params) {
      return new Promise((resolve, reject) => {
          this.$api.GetModelEnergyDataList(params, this.kgceToken)
            .then(res => {
              // if (res.data.code === 200) {
                if (res.length) {
                  res.forEach(item => {
                    this.unitItems[item.energyName] = item.energyUnitEn;
                  });
                }
                resolve(res);
              // } else {
              //   reject(res);
              // }
            })
            .catch(err => {
              reject(err);
            });
      });
    },
    // 类型分析
    typeAnalysis() {
      const param = {
        emodelId: emodelId, // 模型id
        // energyId: 'SU035' // 能源id
        energyId: this.itemData.children && this.itemData.children[0].allId
          ? this.itemData.children[0].allId
          : this.itemData.id
      };
      this.getModelEnergyDataList({
        ...param,
        ...this.getParams(),
        groupBy: "energyId"
      })
        .then(res => {
          if (res.length) {
            this.chartsData = res;
            this.$nextTick(() => {
              this.electricityPieEchart(this.chartsData, "contentTypePieEcharts");
            });
          }
        })
        .catch();
    },
    electricityPieEchart(arr, id) {
      const getchart = this.$echarts.init(document.getElementById(id));
      const nameList = Array.from(arr, item => item.energyName);
      const valueList = Array.from(arr, item =>
        item.value ? item.value.toFixed(2) : 0
      );
      const data = [];
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i]
        });
      }
      getchart.setOption({
        backgroundColor: "#fff",
        color: ["#FBAF1B", "#A0B8F6", "#7BE188", "#86909C", "#FBACA3 "],
        legend: {
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemWidth: 10,
          itemHeight: 10,
          bottom: "10%",
          left: "center",
          formatter: name => {
            let sum = 0;
            data.forEach(item => {
              sum = sum + Number(item.value);
            });
            const count = data.find(i => i.name == name);
            return (
              name +
              "  " +
              +count.value +
              this.unitItems[this.showContentType] +
              " " +
              "占比：" +
              (((count.value / sum) || 0) * 100).toFixed(2) +
              "%"
            );
          }
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2
          },
          type: "pie",
          radius: "50%",
          center: ["50%", "35%"],
          data: data,
          hoverAnimation: false,
          labelLine: {
            //指示线样式设置
            normal: {
              lineStyle: {
                color: "#4E5969" // 设置标示线的颜色
              }
            }
          },
          label: {
            normal: {
              textStyle: {
                color: "#4E5969"
              }
            }
          }
        }
      });
    },
    // 区域排行
    regionalRanking() {
      const param = {
        emodelId: emodelId,
        energyId: this.itemData.children
          ? this.itemData.children[0].id
          : this.itemData.id
      };
      this.getModelEnergyDataList({
        ...param,
        ...this.getParams(),
        groupBy: "emodelId"
      })
        .then(res => {
          const arr = res;
          if (arr.length) {
            arr.sort((a, b) => {
              return b.value - a.value;
            });
            this.regionalList = arr;
          }
        })
        .catch();
    },
    // 能耗趋势
    energyTrend(arr, id, type) {
      const basicParams = {
        emodelId: emodelId, // 模型id
        // energyId: 'SU035'
        energyId: this.itemData.children
          ? this.itemData.children[0].id
          : this.itemData.id
      };
      let params = {};
      if (type === "month") {
        params = {
          timeCategory: "day",
          startDate: moment().startOf("month").format("YYYY-MM-DD") + " 00:00:00",
          endDate: moment().endOf("month").format("YYYY-MM-DD") + " 23:59:59"
        };
      } else if (type === "year") {
        params = {
          timeCategory: "month",
          startDate:
            moment()
              .startOf("year")
              .format("YYYY-MM-DD") + " 00:00:00",
          endDate:
            moment()
              .endOf("year")
              .format("YYYY-MM-DD") + " 23:59:59"
        };
      }
      this.getModelEnergyDataList({ ...basicParams, ...params })
        .then(res => {
          if (res) {
            if (res.length) {
              if(type=='month'){
                this.lineMonthData=res
                this.$nextTick(() => {
                  this.electricityLineEchart(this.lineMonthData,id,type);
                });
              }else if(type=='year'){
                this.lineYearData=res
                this.$nextTick(() => {
                  this.electricityLineEchart(this.lineYearData,id,type);
                });
              }
            }
          }
        })
        .catch();
    },
    electricityLineEchart(arr, id,type) {
      const getchart = this.$echarts.init(document.getElementById(id));
      const nameList = arr.map(item =>
        type=='year'? item.dataTime.slice(5, 7)+'月':item.dataTime.slice(5, 10)
      );
      const valueList = arr.map(item => Number(item.value).toFixed(2)|| 0);
      getchart.setOption({
        tooltip: {
          trigger: "axis",
          show: true,
          extraCssText: "width:80px;height:80px",
          backgroundColor: "#fff",
          borderColor: "rgba(32, 33, 36,0.20)",
          borderWidth: 1,
          textStyle: {
            // 文字提示样式
            color: "#000000",
            fontSize: "13"
          },
          axisPointer: {
            // 坐标轴虚线
            type: "cross",
            label: {
              backgroundColor: "#6a7985"
            }
          },
          formatter: params => {
            var result = params[0].axisValue;
            params.forEach(item => {
              result += "<br/>";
              result +=
                '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
                item.color +
                '"></span>';
              result +=
                this.showContentType +
                "量" +
                "<br/>" +
                item.value +
                this.unitItems[this.showContentType];
            });
            return result;
          }
        },
        grid: {
          left: '1%',
          right: '4%',
          top:'12%',
          bottom: '4%',
          containLabel: true
        },
        xAxis: {
          type: "category",
          nameLocation: "start",
          data: nameList,
          axisLabel: {
            color: "#86909C", //刻度线标签颜色
            interval: 0, //X轴内容过多造成内容缺失时加上这个内容就会显示出来
            formatter(val) {
              if (val.length > 5) {
                return val.slice(0, 4) + "...";
              } else {
                return val;
              }
            }
          },
          axisLine: {
            show: true, //是否显示轴线
            lineStyle: {
              color: "#86909C" //刻度线的颜色
            }
          },
          axisTick: {
            alignWithLabel: true // 刻度与文案居中对齐
          }
        },
        yAxis: {
          type: "value",
          name: "单位:" + this.unitItems[this.showContentType],
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: false // 不显示坐标轴线
          },
          axisLabel: {
            color: "#86909C", //刻度线标签颜色
            margin: 2,
            formatter: function(value, index) {
              if (value >= 10000 && value < 10000000) {
                value = value / 10000 + "万";
              } else if (value >= 10000000) {
                value = value / 10000000 + "千万";
              }
              return value;
            }
          },
          grid: {
            left: 35
          },
        },
        dataZoom: [
        {
            type: "slider",
            show:arr.length > 6, // 是否显示滑动条
            realtime: true,
            startValue: 0,
            endValue: 4,
            height: 6,
            top:'96%',
            borderRadius: 0,
            borderColor: '#D7DEE8',
            fillerColor: '#C4C4C4', // 滑动块的颜色
            // backgroundColor: "#33384b", //两边未选中的滑动条区域的颜色
            // 是否显示detail，即拖拽时候显示详细数值信息
            showDetail: false,
            zoomLock: false,
            brushSelect: false,
            // 控制手柄的尺寸
            handleSize: 8,
            // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
            showDataShadow: false,
            // filterMode: 'filter',
            handleIcon: 'M0,0 v11h3 v-11h-3 Z',
            handleStyle: {
              color: '#FFF',
              shadowOffsetX: 0, // 阴影偏移x轴多少
              shadowOffsetY: 0 // 阴影偏移y轴多少
              // borderCap: 'square',
              // borderColor: '#D8DFE9',
              // borderType: [15, 20],
            }
          },
          {
            type: "inside", // 支持内部鼠标滚动平移
            start: 0,
            end: 40,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        series: [
          {
            data: valueList,
            type: "line",
            symbol: "circle", //将小圆点改成实心 不写symbol默认空心
            symbolSize: 6, //小圆点的大小
            color: "#FF9F42"
          }
        ]
      });
    },
    getColor(str){
      if(str){
       return str.toString().indexOf('-') != -1? 'downNum': 'upNum'
      }else{
        return 'downNum'
      }
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>
<style lang="stylus" scoped>
.content{
  height:100%;
  background-color: #F2F4F9;
}
.changeBtn {
  width: 100%;
  height: 42px;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  padding:.1875rem
  .headerBtn {
    width: 60px;
    text-align: center;
    line-height: 42px;
  }
  .typeActive {
    background-color: #E6EFFC;
    color: #3562DB;
  }
  .unActive{
    background-color: #F7F8FA;
  }
}
/deep/ .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border: none;
}
/deep/ .header-style > th {
  color: #2D4A74;
  font-weight: normal;
  border-bottom: 5px solid #EBEEF5 !important;
}
.executeCharts {
  height: 32vh;
  text-align: center;
  position: relative;
  .nullText {
    position: absolute;
    bottom: 1rem;
    left: 43%
  }
}
.typeTop{
  padding:.25rem;
  display: flex;
  flex-wrap: wrap;
  justify-content space-between
  line-height :normal;
  .item{
    width:2.875rem;
    min-height:1.3rem;
    padding:.5rem .25rem;
    margin-bottom:.25rem;
    background-color #fff;
    border-radius: .1875rem
    .timeTop{
      margin-bottom:.5rem
      display:flex;
      align-items center
    }
    .timeType{
      font-size:14px;
      color:#4E5969;
      margin-right:.16rem
    }
    .timeNum{
      word-break:break-all;
      font-size:18px;
      color:#1D2129
    }
    .unit{
      font-size:13px;
      color:#86909C
    }
  }
  .sum{
    margin-bottom:0
  }
  .chain{
    font-size:14px;
    color:#4E5969 ;
    margin-right:.1875rem
  }
  .downNum{
    font-size:16px;
    color:#00B42A
  }
  .upNum{
    font-size:16px;
    color:#F53F3F
  }
}

.lineContent{
  background-color #fff;
}
.line_type{
  height:0.8rem;
  line-height :0.8rem;
  font-size:16px;
  padding:.1875rem;
}
  .btn_tags{
    display: flex;
    justify-content: space-between;
    padding:.3125rem;
    background-color #fff
    .btn_item{
      width: 1.6rem;
      height: .7rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #F7F8FA;
      p{
        font-size: 14px;
        color: #4E5969;
      }
    }
    .btn_item_active{
      background: #E6EFFC;
      color: #3562DB;
    }
}
</style>
