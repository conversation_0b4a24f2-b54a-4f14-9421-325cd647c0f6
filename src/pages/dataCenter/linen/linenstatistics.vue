<template>
  <div style="height:100%">
    <Header title="布草统计" @backFun="goback()"></Header>
    <div class="content">
      <div class="conent_top">
      <div class="sumValue">
        <img
          style="width:.625rem;height:.625rem"
          src="../../../assets/images/linen/linen.png"
          alt=""
        />
        <span class="sumLable">布草总数</span>
        <span class="sumNumber">{{ linenTotal|| 0}}</span>
      </div>
      <div
        v-show="pieData.length > 0"
        id="chart1"
        style="width: 100%;height: 400px"
      ></div>
      <div v-if="pieData.length <= 0" class="executeCharts">
        <img
          height="100%"
          src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
    <!-- 布草状态 -->
    <div class="linenStatus">
      <div class="status_top">
        <div class="item">
           <div class="num inUseNum">{{ statusData.inUseStatusQuantity||0 }}</div> 
           <div class="value">使用中</div> 
        </div>
        <div class="item">
           <div class="num unusedNum">{{ statusData.leaveUnused||0 }}</div> 
           <div class="value">闲置</div> 
        </div>
        <!-- <div class="item">
           <div class="num announceNum">{{ statusData.scrap||0 }}</div> 
           <div class="value">报废</div>  
        </div> -->
      </div>
      <div class="status_center">
        <div class="statusLinen">使用中布草状态</div>
        <div v-show="barData.length > 0" style="height:260px" id="chart2"></div>
        <div v-if="barData.length <= 0" class="executeCharts">
        <img
          height="100%"
          src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
      </div>
      <div class="status_bottom">
        <div class="item">
           <div class="num unusedNum">{{ statusData.sewingQuantity||0 }}<span class="linenUnit">次</span></div> 
           <div class="value">缝补量</div> 
        </div>
        <div class="item">
           <div class="num unusedNum">{{ statusData.labelScrapQuantity||0 }} <span class="linenUnit">个</span></div> 
           <div class="value">标签报废量</div> 
        </div>
        <div class="item">
           <div class="num unusedNum">{{ statusData.userQuantity||0 }} <span class="linenUnit">人</span></div> 
           <div class="value">使用人员</div>  
        </div>
      </div>
    </div>
    <!-- 排行 -->
    <div class="linenRanking">
      <div class="btn_tags">
        <div class="btn_item" :class="dateType==0 ? 'btn_item_active' : ''" @click="dateBtn('day')">
          <p>今日</p>
        </div>
        <div class="btn_item" :class="dateType==1 ? 'btn_item_active' : ''" style="margin:0 10px;" @click="dateBtn('week')">
          <p>本周</p>
        </div>
        <div class="btn_item" :class="dateType==2 ? 'btn_item_active' : ''" style="margin-right:10px;" @click="dateBtn('month')">
          <p>本月</p>
        </div>
        <div class="btn_item" :class="dateType==3 ? 'btn_item_active' : ''" @click="dateBtn('year')">
          <p>本年</p>
        </div>
      </div>
      <div class="statusLinen">各科室收集数量排行</div>
      <div v-show="rankingData.length>0"  style="height:300px" id="chart3"></div>
      <div v-if="rankingData.length <= 0" class="executeCharts">
        <img
          height="100%"
          src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
    <!-- 趋势 -->
    <div class="linenTrend">
      <div class="statusLinen">收发趋势</div>
      <div class="linenAgeing">
        <span class="value">平均收发时效</span>
        <span class="num">{{averageCollectionTime||0}}小时</span>
      </div>
      <div v-show="ageingData.length>0" style="height:300px" id="chart4"></div>
      <div v-if="ageingData.length <= 0" class="executeCharts">
        <img
          height="100%"
          src="../../../assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
moment.locale("zh-cn");
export default {
  components: {},
  data() {
    return {
      dateType:'0',
      linenTotal:'', //布草总数
      statusData:{
        inUseStatusQuantity:'',
        leaveUnused:'',
        sewingQuantity:'',
        labelScrapQuantity:'',
        userQuantity:''
      },
      startTime:'',
      endTime:'',
      averageCollectionTime:'',
      pieData:[],
      barData:[],
      rankingData:[],
      ageingData:[]
    };
  },
  computed: {},
  watch: {},
  created(){
    this.getSumType()
    this.getLinenStatus()
    this.startTime=moment().format('YYYY-MM-DD ');
    this.endTime=moment().format('YYYY-MM-DD ');
    this.getRanking()
    this.getAgeing()
  },
  mounted(){},
  methods: {
    dateBtn(type){
      if(type=='day'){
        this.dateType=0
        this.startTime=moment().format('YYYY-MM-DD '); 
        this.endTime=moment().format('YYYY-MM-DD ');
      }else if(type=='week'){
        this.dateType=1
        this.startTime=moment().startOf('isoWeek').format('YYYY-MM-DD ')
        this.endTime=moment().endOf('isoWeek').format('YYYY-MM-DD ');
      }else if(type=='month'){
        this.dateType=2
        this.startTime=moment().startOf('month').format('YYYY-MM-DD ')
        this.endTime=moment().endOf('month').format('YYYY-MM-DD ');
      }else if(type=='year'){
        this.dateType=3
        this.startTime=moment().startOf('year').format('YYYY-MM-DD ')
        this.endTime=moment().endOf('year').format('YYYY-MM-DD ');
      }
      this.getRanking()
      this.getAgeing()
    },
    // 布草总数和类型
    getSumType(){
      this.$api.linenSumType().then(res => {
        this.linenTotal=res.linenTotal
        this.pieData=res.pieChart
        if(this.pieData.length){
          this.$nextTick(()=>{
            this.getPieData(this.pieData,'chart1')
          })
        }
      });
    },
    getPieData(arr, id) {
      const getchart = this.$echarts.init(document.getElementById(id));
      const nameList = Array.from(arr, item => item.linenTypeName);
      const valueList = Array.from(arr, item =>item.linenTypeTotal);
      const data = [];
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i]
        });
      }
      getchart.setOption({
        backgroundColor: "#fff",
        color: ["#FF7D00", "#3562DB", "#FFCF8B", "#A0B8F6", "#FF8C66"],
        legend: {
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemWidth: 10,
          itemHeight: 10,
          bottom: "10%",
          left: "center",
          formatter: name => {
            let sum = 0;
            data.forEach(item => {
              sum = sum + Number(item.value);
            });
            const count = data.find(i => i.name == name);
            return (
              name +
              "  " +
              +count.value +
              "件" +
              " " +
              "占比：" +
              ((count.value / sum)* 100).toFixed(2)+
              "%"
            );
          }
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2
          },
          type: "pie",
          radius: "50%",
          center: ["50%", "30%"],
          data: data,
          hoverAnimation: false,
          labelLine: {
            //指示线样式设置
            normal: {
              lineStyle: {
                color: "#4E5969" // 设置标示线的颜色
              }
            }
          },
          label: {
            normal: {
              textStyle: {
                color: "#4E5969"
              }
            }
          }
        }
      });
    },
    // 布草状态
    getLinenStatus(){
      this.$api.linenStatus().then(res => {
        this.statusData={
          inUseStatusQuantity:res.inUseStatusQuantity,
          leaveUnused:res.leaveUnused,
          sewingQuantity:res.sewingQuantity,
          labelScrapQuantity:res.labelScrapQuantity,
          userQuantity:res.userQuantity
        }
        this.barData=res.histogram
        if(this.barData.length){
          this.$nextTick(()=>{ 
          this.getBarData({
          names: this.barData.map((item) => {
              return item.name
          }),
          array: this.barData.map((item) => {
              return item.value
          })
        },'chart2')
      })
        }
      });
    },
    getBarData(obj, chartId) {
      // 基于准备好的dom，初始化echarts实例
      let myChart = this.$echarts.init(document.getElementById(chartId))
      // 绘制图表
      myChart.setOption({
        tooltip: {
          trigger: 'axis',
        },
        grid: {
          left: '1%',
          right: '4%',
          top:'3%',
          bottom: '4%',
          containLabel: true,
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          // 不显示轴线刻度
          axisTick: {
            show: false,
          },
          // 设置y轴刻度标签的颜色
          axisLabel: {
            color: '#86909C',
          },
        },
        legend: {
          show: false,
        },
        xAxis: [
          {
            type: 'category',
            data: obj.names,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#86909C'
            },
          },
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: obj.array,
            // label: {
            //   show: true,
            //   position: 'top',
            //   color: '#414653',
            // },
            itemStyle: {
              color: '#3F63D3',
            },
          },
        ],
      })
    },
    // 科室排行
    getRanking(){
      let params={
        startTime:this.startTime,
        endTime:this.endTime
      }
      this.$api.linenRanking(params).then(res => {
        this.rankingData=res
        if(this.rankingData.length){
          this.$nextTick(()=>{
          this.getRankingData({
          names: this.rankingData.map((item) => {
                return item.deptName
              }),
          array: this.rankingData.map((item) => {
                return item.linenNum
              })
        },'chart3')
        })
        }
      });

    },
    getRankingData(obj, chartId){
      let myChart = this.$echarts.init(document.getElementById(chartId))
      myChart.setOption({
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          show: false,
        },
        grid: {
          left: '1%',
          right: '4%',
          top:'3%',
          bottom: '4%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          // 不显示轴线刻度
          axisTick: {
            show: false,
          },
          // 设置y轴刻度标签的颜色
          axisLabel: {
            color: '#86909C',
          },
        },
        yAxis: {
          type: 'category',
          axisLine: {
            show: false,
          },
          // 不显示轴线刻度
         axisTick: {
           show: false,
         },
         // 设置y轴刻度标签的颜色
         axisLabel: {
           color: '#86909C',
         },
          data: obj.names
        },
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: obj.array,
            itemStyle: {
              color: '#FBAF1B',
            },
          },
        ]
      })
    },
    // 收取趋势
    getAgeing(){
      let params={
        startTime:this.startTime,
        endTime:this.endTime
      }
      if(this.dateType=='3'){
        params.flag=2
      }
      this.$api.linenAgeing(params).then(res => {
        this.averageCollectionTime=res.averageCollectionTime
        this.ageingData=res.collectionTrendDtoList
        if(this.ageingData.length){
          this.$nextTick(()=>{
            this.getAgeingData(this.ageingData,'chart4')
          })
        }
      });
    },
    getAgeingData(arr,chartId){
      const getchart = this.$echarts.init(document.getElementById(chartId));
      const nameList = Array.from(arr, item => 
        this.dateType=='3'? item.operationDate.slice(5, 10)+'月':item.operationDate.slice(5, 10)
      );
      const value1List = Array.from(arr, item =>
        item.receivedQuantity ? Number(item.receivedQuantity).toFixed(2) : 0
      );
      const value2List = Array.from(arr, item =>
        item.distributionQuantity ? Number(item.distributionQuantity).toFixed(2) : 0
      );
      getchart.setOption({
        legend: {
          data: ['收取', '派发'],
          itemWidth: 15,
          itemHeight: 8,
        },
        tooltip: {
          trigger: "axis",
          show: true,
          extraCssText: "width:80px;height:80px",
          backgroundColor: "#fff",
          borderColor: "rgba(32, 33, 36,0.20)",
          borderWidth: 1,
          textStyle: {
            // 文字提示样式
            color: "#000000",
            fontSize: "13"
          },
          axisPointer: {
            // 坐标轴虚线
            type: "cross",
            label: {
              backgroundColor: "#6a7985"
            }
          }
        },
        xAxis: {
          type: "category",
          nameLocation: "start",
          data: nameList,
          axisLabel: {
            color: "#86909C", //刻度线标签颜色
            interval: 0, //X轴内容过多造成内容缺失时加上这个内容就会显示出来
          },
          axisLine: {
            show: true, //是否显示轴线
            lineStyle: {
              color: "#86909C" //刻度线的颜色
            }
          },
          axisTick: {
            alignWithLabel: true // 刻度与文案居中对齐
          }
        },
        yAxis: {
          type: "value",
          name: "单位: 件",
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: false // 不显示坐标轴线
          },
          axisLabel: {
            color: "#86909C" //刻度线标签颜色
          }
        },
        dataZoom: [
          {
            type: "slider",
            show:arr.length > 6, // 是否显示滑动条
            realtime: true,
            startValue: 0,
            endValue: 4,
            height: 6,
            top:'96%',
            borderRadius: 0,
            borderColor: '#D7DEE8',
            fillerColor: '#C4C4C4', // 滑动块的颜色
            // backgroundColor: "#33384b", //两边未选中的滑动条区域的颜色
            // 是否显示detail，即拖拽时候显示详细数值信息
            showDetail: false,
            zoomLock: false,
            brushSelect: false,
            // 控制手柄的尺寸
            handleSize: 8,
            // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
            showDataShadow: false,
            // filterMode: 'filter',
            handleIcon: 'M0,0 v11h3 v-11h-3 Z',
            handleStyle: {
              color: '#FFF',
              shadowOffsetX: 0, // 阴影偏移x轴多少
              shadowOffsetY: 0 // 阴影偏移y轴多少
              // borderCap: 'square',
              // borderColor: '#D8DFE9',
              // borderType: [15, 20],
            }
          },
          {
            type: "inside", // 支持内部鼠标滚动平移
            start: 0,
            end: 40,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        series: [
          {
            name:'收取',
            data: value1List,
            type: "line",
            symbol: "circle", //将小圆点改成实心 不写symbol默认空心
            symbolSize: 6, //小圆点的大小
            color: "#30B75E"
          },
          { 
            name:'派发',
            data: value2List,
            type: "line",
            symbol: "circle", //将小圆点改成实心 不写symbol默认空心
            symbolSize: 6, //小圆点的大小
            color: "#FF9F42"
          }
        ]
      });
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>
<style lang="stylus" scoped>
*{
  box-sizing: border-box;
  margin:0;
  padding:0;
}
.content{
  background-color #F7F8FA;
}
.conent_top{
  padding:.3125rem;
  background-color #fff
  .sumValue{
    height: 1.25rem;
    display: flex;
    justify-content center;
    align-items center
    border-radius: .1875rem
    background-color #FFF7E8
  }
}
.linenStatus{
   padding:.3125rem;
   background-color #fff
   margin-top:.3125rem
   .status_top,
   .status_bottom{
     display: flex;
     height: 1.5625rem
     justify-content space-between
     .item{
        flex: 1;
        padding: .3125rem 0
        display: flex;
        flex-direction: column;
        align-items center ;
        justify-content space-between
        .num{
           font-size: 24px;
           font-weight:700
        }
        .value{
           font-size: 14px 
           color: #86909c
        }
        .inUseNum{
           color: #00B42A
        }
        .unusedNum{
           color: #1D2129  
        }
        .announceNum{
           color: #86909C
        }
        .linenUnit{
           color: #86909C
           font-size: 13px
        }
     }
   }
}
.sumLable{
    font-size: 14px 
    color: #86909c 
    margin: 0 .3125rem
}
.sumNumber{
    font-size: 18px 
    color: #1D2129 
}
.statusLinen{
   font-size: 16px 
   color: #1D2129;
   margin: .625rem 0
}
.linenRanking{
  padding:.3125rem;
  background-color #fff
  margin-top:.3125rem
  .btn_tags{
    display: flex;
    justify-content: center;
    .btn_item{
      flex: 1
      // width: 2rem;
      height: .7rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #F7F8FA;
      p{
        font-size: 14px;
        color: #4E5969;
      }
    }
    .btn_item_active{
      background: #E6EFFC;
      color: #3562DB;
    }
  }
}
.linenTrend{
  border-top:1px solid #fff
  padding:0 .3125rem;
  background-color #fff
  margin-top:.3125rem
  .linenAgeing{
    text-align center;
    margin-bottom:.625rem
    .value{
      font-size:14px;
      color:#86909c
    }
    .num{
      font-size:18px;
      color:#1D2129;
      font-weight: 700
    }
  }
}
.executeCharts {
  height: 28vh;
  text-align: center;
  position: relative;
  .nullText {
    position: absolute;
    bottom: 1rem;
    left: 43%
  }
}
</style>
