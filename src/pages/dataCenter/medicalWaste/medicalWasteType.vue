<template>
  <div class="inner">
    <Header title="医废类型分析" @backFun="goback()"></Header>
    <div class="tabs">
      <div class="changeBtn">
        <div v-for="(item, index) in tabArr" :key="index" class="headerBtn" :class="dateType == item.id ? 'typeActive' : 'unActive'" @click="changeType(item.id)">
          {{ item.title }}
        </div>
      </div>
      <div v-show="chartsData.length > 0" id="chart1" style="width: 100%; height: 240px"></div>
      <div v-if="chartsData.length <= 0" class="executeCharts">
        <img height="100%" src="../../../assets/images/equipmentManagement/缺省-图表@2x.png" alt="" />
        <span class="nullText">暂无数据</span>
      </div>
      <el-table :data="tableData" style="width: 100%" :max-height="maxHeight" header-row-class-name="header-style" highlight-current-row stripe>
        <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
        <el-table-column prop="wasteType" label="医废类型" align="center"> </el-table-column>
        <el-table-column prop="gatherWeigh" label="总重量" align="center" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.gatherWeigh + "kg" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="rate" label="环比" align="center" width="80">
          <template slot-scope="scope">
            <span :class="scope.row.rate.indexOf('-') != -1 ? 'positive' : 'negative'">{{ scope.row.rate }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="percentage" label="百分比" align="center" width="80"> </el-table-column>
      </el-table>
      <van-calendar v-model="show" type="range" @confirm="onConfirm" color="#3562DB" :min-date="minDate" :max-date="maxDate" />
    </div>
  </div>
</template>
<script>
import moment from "moment";
export default {
  components: {},
  data() {
    return {
      maxHeight: "",
      show: false,
      active: 0,
      tabArr: [
        {
          id: "day",
          title: "今日",
          detail: [],
        },
        {
          id: "week",
          title: "本周",
          detail: [],
        },
        {
          id: "month",
          title: "本月",
          detail: [],
        },
        {
          id: "year",
          title: "本年",
          detail: [],
        },
        {
          id: "",
          title: "自定义",
          detail: [],
        },
      ],
      tableData: [],
      dateType: "day",
      paramte: {
        dateType: "day",
      },
      minDate: new Date(new Date().setTime(new Date() - 31536000000)),
      maxDate: new Date(),
      chartsData: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    changeType(val) {
      this.dateType = val;
      this.paramte.dateType = val;
      if (val == "") {
        this.show = true;
      } else {
        this.chartsData = [];

        this.show = false;
        this.paramte = {
          dateType: this.dateType,
        };
        this.getFixData();
      }
    },
    getFixData() {
      this.$toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0,
      });
      this.$api.getMedicalWasteType(this.paramte).then((res) => {
        this.$toast.clear();
        this.tableData = res.list;
        this.tableData.forEach((i) => {
          const item = {
            name: i.wasteType,
            value: i.gatherWeigh,
            percentage: i.percentage,
          };
          this.chartsData.push(item);
        });
        if (this.chartsData.length == 0) {
          return;
        }
        this.$nextTick(() => {
          this.initChart();
        });
      });
    },
    initChart() {
      let myChart1 = this.$echarts.init(document.getElementById("chart1"));
      myChart1.setOption({
        backgroundColor: "#fff",
        color: ["#FF616C", "#4089FF", "#08C673", "#B97FFF", "#FF8C66"],
        legend: {
          icon: "circle",
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemGap: 15,
          itemWidth: 10,
          itemHeight: 10,
          right: "5%",
          top: "12%",
          formatter: (name) => {
            const count = this.chartsData.find((i) => i.name == name);
            console.log(count.name + " " + count.percentage, "count");
            return count.name + count.percentage;
          },
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2,
          },
          type: "pie",
          radius: "50%",
          center: ["40%", "50%"],
          data: this.chartsData,
          hoverAnimation: false,
          label: {
            normal: {
              // formatter: (params) => {
              //    return params.data.name + params.data.percentage;
              // },
              rich: {
                name: {
                  fontSize: 14,
                  padding: [0, 10, 0, 0],
                  color: "#121f3e",
                },
                value: {
                  fontSize: 18,
                  fontWeight: "bold",
                  color: "#121f3e",
                },
              },
              textStyle: {
                color: "#4E5969",
              },
            },
            // show: true,
            // position: "inside",
            // fontSize: 10,
            // formatter: `{b}\n{d}%`
          },
        },
      });
    },
    onConfirm(date) {
      this.chartsData = [];
      this.paramte.startTime = moment(date[0]).format("YYYY-MM-DD 00:00:00");
      this.paramte.endTime = moment(date[1]).format("YYYY-MM-DD 23:59:59");
      this.getFixData();
      this.show = false;
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
  },
  created() {
    const windowHeight = parseInt(window.innerHeight);
    this.maxHeight = windowHeight - 352;
    this.getFixData();
  },
  mounted() {},
};
</script>
<style lang="stylus" scoped>

.changeBtn {
  width: 100%;
  height: 42px;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  .headerBtn {
    width: 60px;
    text-align: center;
    line-height: 42px;
  }
  .typeActive {
    background-color: #E6EFFC;
    color: #3562DB;
  }
  .unActive{
    background-color: #F7F8FA;
  }
}
/deep/ .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border: none;
}
/deep/ .header-style > th {
  color: #2D4A74;
  font-weight: normal;
  background-color #E6EFFC
}
.executeCharts {
  height: 28vh;
  text-align: center;
  position: relative;
  .nullText {
    position: absolute;
    bottom: 1rem;
    left: 43%
  }
}
.positive{
  color:#00B42A
}
.negative{
  color:#F53F3F
}
</style>
