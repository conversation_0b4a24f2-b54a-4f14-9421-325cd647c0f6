<template>
  <div class="inner">
    <Header title="科室产废分析" @backFun="goback()"></Header>
    <div class="tabs">
      <div class="changeBtn">
        <div
          v-for="(item, index) in tabArr"
          :key="index"
          class="headerBtn"
          :class="dateType == item.id ? 'typeActive' : 'unActive'"
          @click="changeType(item.id)"
        >
          {{ item.title }}
        </div>
      </div>
      <div class="dataCenter">
        <div class="dayWorkOrder">
          <div class="option">
            <span class="workOrderCount"
              >{{ topData.totalBagsNum || 0
              }}<span class="workOrderUnit">袋</span></span
            >
            <span class="workOrderText">数量</span>
          </div>
          <div class="option">
            <span class="workOrderCount"
              >{{ topData.gatherWeigh || 0
              }}<span class="workOrderUnit">kg</span></span
            >
            <span class="workOrderText">重量 </span>
          </div>
        </div>
        <div class="dayWorkOrder">
          <div class="option">
            <span class="workOrderCount"
              >{{ topData.stockBagsNum || 0
              }}<span class="workOrderUnit">袋</span></span
            >
            <span class="workOrderText">库存数量</span>
          </div>
          <div class="option">
            <span class="percentageCount"
              >{{ topData.timeoutBagsNum || 0
              }}<span class="workOrderUnit">袋</span></span
            >
            <span class="workOrderText">超时处理</span>
          </div>
        </div>
      </div>
      <div class="topText">科室数据Top10</div>
      <el-table
        :data="tableData"
        style="width: 100%"
        :max-height="maxHeight"
        header-row-class-name="header-style"
        highlight-current-row
        stripe
      >
        <el-table-column type="index" label="序号" align="center" width="50">
        </el-table-column>
        <el-table-column prop="officeName" label="科室名称" align="center">
        </el-table-column>
        <el-table-column
          prop="totalweigh"
          label="总重量"
          align="center"
          width="80"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.totalweigh + "kg" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="rate" label="环比" align="center" width="80">
          <template slot-scope="scope">
            <span :class="scope.row.rate.indexOf('-') != -1?'positive':'negative'">{{ scope.row.rate }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="percentage"
          label="百分比"
          align="center"
          width="80"
        >
        </el-table-column>
      </el-table>
      <van-calendar
        v-model="show"
        type="range"
        @confirm="onConfirm"
        color="#3562DB"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </div>
  </div>
</template>
<script>
import moment from "moment"
export default {
  components: {},
  data() {
    return {
      maxHeight: "",
      show: false,
      active: 0,
      tabArr: [
        {
          id: "day",
          title: "今日",
          detail: []
        },
        {
          id: "week",
          title: "本周",
          detail: []
        },
        {
          id: "month",
          title: "本月",
          detail: []
        },
        {
          id: "year",
          title: "本年",
          detail: []
        },
        {
          id: "",
          title: "自定义",
          detail: []
        }
      ],
      tableData: [],
      topData: {},
      dateType: "day",
      paramte: {
        dateType: "day"
      },
      minDate: new Date(new Date().setTime(new Date() - 31536000000)),
      maxDate: new Date()
    };
  },
  computed: {},
  watch: {},
  methods: {
    changeType(val) {
      this.dateType = val;
      this.paramte.dateType = val;
      if (val == "") {
        this.show = true;
      } else {
        this.show = false;
        this.paramte = {
          dateType: this.dateType
        };
        this.getData();
        this.getTopData();
      }
    },
    getData() {
      this.$api.getMedicalTopten(this.paramte).then(res => {
        this.tableData = res.list;
      });
    },
    getTopData() {
      this.$api.getMedicalWasteOption(this.paramte).then(res => {
        this.topData = res.map;
      });
    },
    onConfirm(date) {
      this.paramte.startTime = moment(date[0]).format(
        "YYYY-MM-DD 00:00:00"
      );
      this.paramte.endTime = moment(date[1]).format(
        "YYYY-MM-DD 23:59:59"
      );
      this.getData();
      this.getTopData();
      this.show = false;
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    }
  },
  created() {
    const windowHeight = parseInt(window.innerHeight);
    this.maxHeight = windowHeight - 430;
    this.getData();
    this.getTopData();
  },
  mounted() {
  this.$YBS.apiCloudEventKeyBack(this.goback)}
};
</script>
<style lang="stylus" scoped>
.changeBtn {
  width: 100%;
  height: 42px;
  background-color: #fff;
  margin-bottom: .125rem
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  .headerBtn {
    width: 60px;
    text-align: center;
    line-height: 42px;
  }

.typeActive {
  background-color: #E6EFFC;
  color: #3562DB;
}
.unActive{
  background-color: #F7F8FA;
}
}
/deep/ .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border: none;
}
/deep/ .header-style > th {
  color: #2D4A74;
  font-weight: normal;
  background-color #E6EFFC
}
.dayWorkOrder {
      width: 100%;
      height: 120px;
      background-color: #fff;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .option {
        height: 55px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .workOrderCount {
          margin-bottom: 8px;
          font-size: 24px;
          color: #353535;
          font-weight: 600;
          .workOrderUnit {
            margin-left: 4px;
            font-size: 14PX;
            color: #8C8C8C;
            font-weight: normal;
          }
        }
        .workOrderText {
          font-size: 14px;
          color: #353535;
        }
        .percentageCount {
          margin-bottom: 8px;
          font-size: 24px;
          color: #FF4848;
          font-weight: 600;
          .percentageUnit {
            margin-left: 4px;
            font-size: 14PX;
            color: #FF4848;
            font-weight: normal;
          }
          .workOrderUnit {
            margin-left: 4px;
            font-size: 14PX;
            color: #8C8C8C;
            font-weight: normal;
          }
        }
        .completeCount {
          margin-bottom: 8px;
          font-size: 24px;
          color: #29BEBC;
          font-weight: 600;
          .workOrderUnit {
            margin-left: 4px;
            font-size: 14PX;
            color: #8C8C8C;
            font-weight: normal;
          }
        }
      }
      .workOrderRes {
        height: 60%;
        display: flex;
        padding-left: 11.5%;
        justify-content: start;
        align-items: center;
        .resTime {
          margin-left: 15PX;
          .time {
            font-size: 24px;
            font-weight: 600;
            color: #353535;
          }
          .unit {
            font-size: 14px;
            color: #8C8C8C;
          }
        }
      }
      .averageBottom {
        height: 34px;
        padding: 0 10px;
        >div {
          height: 100%;
          width: calc(100% - 11.5%);
          display: flex;
          padding-left: calc(11.5% - 10px);
          justify-content: start;
          align-items: center;
          color: #8C8C8C;
          .percentage {
            margin-left: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
          }
          .percentage > span:nth-child(1) {
            font-size: 16px;
          }
          .percentage > span:nth-child(2) {
            font-size: 14px;
          }
          .percentage > img {
            margin-left: 10px;
          }
        }
      }
}
.dayWorkOrder:nth-child(1) {
  border-radius: 8px 8px 0 0;
}
.dayWorkOrder:nth-child(2) {
  border-radius:0 0 8px 8px ;
}
.topText {
  font-size: 16px;
  color: #353535;
  font-weight: 600;
  margin: .3125rem
}
.dataCenter{
  background-color #EBEEF5
  padding: .1875rem
}
.positive{
  color:#00B42A
}
.negative{
  color:#F53F3F
}
</style>
