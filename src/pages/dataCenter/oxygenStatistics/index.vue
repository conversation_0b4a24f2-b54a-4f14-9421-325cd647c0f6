<template>
  <div class="oxygenStatistics">
    <Header title="用氧统计" @backFun="goback"></Header>
    <div class="oxygenStatistics-head">
      <div class="head-type">
        <div class="type-item" :class="{'type-active': item.type == requestInfo.timeType}" v-for="item in dateTypeList" :key="item.name" @click="dateTypeSelect(item.type)">{{item.name}}</div>
      </div>
      <div v-show="requestInfo.timeType == 4" class="head-select">
        <p @click="() => { show = true }">{{requestInfo.dataRange[0] ? requestInfo.dataRange[0] : '请选择起使日期'}}</p>
        <span></span>
        <p @click="() => { show = true }">{{requestInfo.dataRange[1] ? requestInfo.dataRange[1] : '请选择结束日期'}}</p>
      </div>
    </div>
    <div class="oxygenStatistics-main">
      <div class="main-count">
        <p class="count-title">用氧统计</p>
        <div class="count-num">
          <img src="../../../assets/images/icon/oxygenCount.png">
          <p>{{airData.count}}</p>
          <span>m³</span>
        </div>
      </div>
      <el-table :data="airData.list" style="width: 100%">
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="surveyName" label="监测项名称"></el-table-column>
        <el-table-column prop="number" label="用氧量(m³)" width="90"></el-table-column>
        <el-table-column prop="ratio" label="百分比" width="70"></el-table-column>
      </el-table>
    </div>
    <van-calendar
      v-model="show"
      type="range"
      @confirm="onConfirm"
      color="#29BEBC"
      :min-date="minDate"
      :max-date="maxDate"
    />
  </div>
</template>

<script>
import moment from "moment";
moment.locale("zh-cn");
import { monitorTypeList } from "../../monitor/components/dict";
export default {
  name: '',
  data() {
    return {
      show: false,
      minDate: new Date(new Date().setTime(new Date() - 31536000000)),
      maxDate: new Date(),
      dateTypeList: [
        {name: '今日', type: 0},
        {name: '本周', type: 1},
        {name: '本月', type: 2},
        {name: '本年', type: 3},
        {name: '自定义', type: 4},
      ],
      requestInfo: {
        projectCode: monitorTypeList.find(item => item.projectName === '医用气体').projectCode,
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 时间范围
        timeType: 0
      },
      airData: {}
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {
    this.dateTypeSelect(0)
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    onConfirm (date) {
      this.requestInfo.dataRange = [moment(date[0]).format('YYYY-MM-DD'), moment(date[1]).format('YYYY-MM-DD')]
      this.getAirStatistics()
      this.show = false
    },
    // 时间类型选择
    dateTypeSelect(type) {
      let newObj = {
        0: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        1: [moment().startOf('week').format('YYYY-MM-DD'), moment().endOf('week').format('YYYY-MM-DD')],
        2: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        3: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
        4: []
      }
      this.requestInfo.dataRange = newObj[type]
      this.requestInfo.timeType = type
      if(type != 4) {
        this.getAirStatistics()
      }
      // else {
      //   this.show = true
      // }
    },
    getAirStatistics() {
      let parasm = {
        projectCode: this.requestInfo.projectCode,
        timeType: this.requestInfo.timeType,
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1]
      }
      this.airData.list = []
      this.$api.GetAirStatistics(parasm).then(res => {
        this.airData = res
      })
    },
  }
}

</script>

<style lang="stylus" scoped>
.oxygenStatistics{
  height 100%
  background: #F2F4F9;
  display: flex;
  flex-direction: column;
  .oxygenStatistics-head{
    padding: 10px 16px 8px 16px;
    background: #fff;
    .head-type{
      display: flex;
      justify-content: space-between;
      .type-item{
        min-width: 43px;
        padding 8px;
        font-size: 14px;
        color: #4E5969;
        line-height: 20px;
        background: #F7F8FA;
        text-align: center;
      }
      .type-active{
        color: #3562DB;
        background: #E6EFFC;
      }
    }
    .head-select{
      display: flex;
      align-items: center;
      padding-top: 10px;
      span{
        display: inline-block;
        width: 10px;
        height: 1px;
        background: #C9CDD4;
        margin: 0px 12px;
      }
      p{
        flex: 1;
        padding: 8px 0px 8px 10px;
        background: #F2F3F5;
        font-size: 15px;
        color: #86909C;
        line-height: 21px;
      }
    }
  }
  .oxygenStatistics-main{
    flex: 1;
    width: 100%;
    overflow-y: auto;
    .main-count{
      margin: 10px;
      background: #FFFFFF;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      align-items: center
      .count-title{
        font-size: 14px;
        font-weight: 400;
        color: #1D2129;
        line-height: 20px;
      }
      .count-num{
        display: flex;
        align-items: center;
        margin-top: 12px;
        img{
          width: 40px;
          height: 40px;
        }
        p{
          font-size: 18px;
          font-weight: bold;
          color: #1D2129;
          line-height: 25px;
          padding: 0px 4px 0px 16px;
        }
        span{
          font-size: 13px;
          font-weight: 400;
          color: #86909C;
          line-height: 18px;
        }
      }
    }
    /deep/ .el-table {
      .el-table__header-wrapper{
        th{
          border: none;
          background: #E6EFFC;
          .cell{
            padding-left: 8px;
            padding-right: 8px;
            color: #1D2129;
          }
        }
      }
      .el-table__body-wrapper{
        td{
          .cell{
            padding-left: 8px;
            padding-right: 8px;
            color: #4E5969
          }
        }
      }
    }
  }
}
</style>
