<!--
 * @Author: hedd
 * @Date: 2023-05-17 10:06:34
 * @LastEditTime: 2023-07-25 16:46:09
 * @FilePath: \ybs_h5\src\pages\dataCenter\giveAnAlarm\giveAnAlarmList.vue
 * @Description:
-->

<!-- 废弃页面  使用公共页面 -->
<template>
  <div class="inner">
    <Header title="告警统计" @backFun="goback"></Header>
    <van-tabs v-model="active" sticky offset-top="10vh">
      <van-tab v-for="item in alarmStatusArr" :title="item.name" :key="item.alarmStatus">
        <ListPolice
          :requestApi="$api.selectAlarmRecordAll"
          :queryParams="{
            'alarmStatus':item.alarmStatus,
            'projectCode':item.projectCode
          }"
        >
          <template #default="slotItem">
            <ListItem :item="slotItem.item"></ListItem>
          </template>
        </ListPolice>
      </van-tab>
    </van-tabs>

  </div>
</template>

<script>
import ListPolice from "@/pages/monitor/components/listPolice/index.vue";
import ListItem from "@/pages/monitor/components/listItem/index.vue";
export default {
  components: {
    ListPolice,
    ListItem
  },
  data(){
    return{
      alarmStatusArr:[
        {
          name:'全部',
          alarmStatus:'',
          projectCode:''
        },
        {
          name:'未处理',
          alarmStatus:0,
          projectCode:''
        },
        {
          name:'处理中',
          alarmStatus:1,
          projectCode:''
        }
      ],
      active:'',
    }
  },
  mounted(){
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame( )
    },
  }
}
</script>

<style scoped lang="stylus">
.inner{
  // height: 100%;
  background-color: #F2F4F9;
}
</style>
