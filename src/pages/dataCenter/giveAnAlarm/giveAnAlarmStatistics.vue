<template>
  <div class="inner">
    <Header title="告警统计" @backFun="goback"></Header>
    <van-row gutter="10" class="give_handle">
      <van-col span="12" @click="jumpVariousTypesPage(0)">
        <div class="give_handle_item">
          <img src="@/assets/images/icon-wrapper-B.png" alt="">
          <p style="color: #F53F3F;">未处理</p>
          <span style="color: #F53F3F;">{{policeInfo.unDisposeCount}}</span>
        </div>
      </van-col>
      <van-col span="12" @click="jumpVariousTypesPage(1)">
        <div class="give_handle_item">
          <img src="@/assets/images/icon-wrapper-A.png" alt="">
          <p style="color: #FF7D00;">处理中</p>
          <span style="color: #FF7D00;">{{policeInfo.disposeCount}}</span>
        </div>
      </van-col>
    </van-row>
    <van-row gutter="10" class="give_add">
      <van-col span="12" v-for="item in policeInfo.countInfo" :key="item.timeType + 'countInfo'">
        <div class="give_add_item">
          <div class="add_item_top">
            <p>{{ timeList.find(ele=>ele.type == item.timeType).name }}新增</p>
            <span>{{ item.count }}</span>
          </div>
          <div
            class="add_item_bom"
            :class="item.ringRatioType==1?'add_item_bom_down':item.ringRatioType==0?'add_item_bom_up':''"
            v-if="item.ringRatio"
          >
            <p>环比</p>
            <span>{{ item.ringRatio }}</span>
            <img v-if="item.ringRatioType==1" src="@/assets/images/direction-down-circle-fill.png" alt="">
            <img v-if="item.ringRatioType==0" src="@/assets/images/direction-up-circle-fill.png" alt="">
          </div>
        </div>
      </van-col>
    </van-row>
    <van-row gutter="10" class="time_tabs">
      <van-col span="6" v-for="item in timeList" :key="item.type" @click="onClickTabs(item)">
        <div class="tabs_btn" :class="timeType == item.type ? 'tabs_btn_active' : ''">
          {{ item.name }}
        </div>
      </van-col>
    </van-row>
    <div id="givePieEchats"></div>
    <div class="give_echart_title">
      <p>报警来源</p>
    </div>
    <div id="giveVerticalBarEchats"></div>
    <div class="give_echart_title">
      <p>报警事件类型Top10</p>
    </div>
    <div id="giveTransverseBarEchats"></div>
    <div class="give_echart_title">
      <p>报警新增趋势</p>
    </div>
    <div id="giveLineEchats"></div>
  </div>
</template>

<script>
import mixins from './mixins/index.js'
import moment from 'moment'
export default {
  data() {
    return {
      policeInfo:'',
      // YYYY-MM-DD HH:mm:ss
      timeList: [
        { name: '今日', type: '0', xAxisType:'h', xAxisUnit:'时' },
        { name: '本周', type: '1', xAxisType:'dddd', xAxisUnit:'' },
        { name: '本月', type: '2', xAxisType:'D', xAxisUnit:'日' },
        { name: '本年', type: '3', xAxisType:'M', xAxisUnit:'月' },
      ],
      timeType: '',
    }
  },
  mixins: [mixins],
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback)
    this.getPoliceInfo()
    this.onClickTabs(this.timeList[1])
  },
  methods: {
    // 跳转报警清单 故障离线高低液位页
    jumpVariousTypesPage(active) {
      this.$router.push({
        path:'/callThePolice',
        query: {
          title:'告警统计',
          active,
        }
      });
    },
    getPoliceInfo(){
      this.$api.getPoliceInfoByApp().then(res=>{
        this.policeInfo = res
      })
    },
    onClickTabs(item) {
      this.timeType = item.type
      let parm = {timeType : item.type}
      // 饼图
      this.$api.getPolicePieByApp(parm).then(res=>{
        // 	报警级别(0:通知 1:一般 2:紧急 3:重要)
        let polLevelList = {
          3:{name:'重要'},
          2:{name:'紧急'},
          1:{name:'一般'},
          0:{name:'通知'},
        }
        let sortData = res.sort((a,b) => b.alarmLevel-a.alarmLevel)
        let data = sortData.filter(ele => {
          if(ele.alarmCount){
            ele['name'] = polLevelList[ele.alarmLevel].name
            ele['value'] = ele.alarmCount
            return true
          }
          return false
        })

        this.getPieEchats(data)
      })
      // top10报警类型
      this.$api.getAlarmTopCount(parm).then(res=>{
        let topData = res.sort((a,b) => a.count-b.count)
        this.getGiveTransverseBarEchats(topData)
      })
      // 报警来源
      this.$api.getAlarmSourceCount(parm).then(res=>{
        this.getGiveVerticalBarEchats(res)
      })
      // 报警趋势
      this.$api.getAlarmTrend(parm).then(res=>{
        res.forEach(ele=>{
          ele.time = moment(ele.time).locale('zh-cn').format(item.xAxisType) + item.xAxisUnit
        })
        this.getGiveLineEchats(res, item.xAxisUnit)
      })
    },
    goback() {
      this.$YBS.apiCloudCloseFrame()
    },
  }
}
</script>

<style scoped lang="stylus">
// 折线图
#giveLineEchats{
  width: 100%
  height: 4.4rem;
}
// 柱状图（横）
#giveTransverseBarEchats{
  width: 100%
  height: 6.2rem;
}
// 柱状图（竖）
#giveVerticalBarEchats{
  width: 100%
  height: 4.4rem;
}
.give_echart_title{
  width: 100%;
  background: white
  margin-top: 10px;
  p{
    padding: 16px;
    font-size: 16px;
    font-weight: 400;
    color: #1D2129;
  }
}
// 饼图
#givePieEchats{
  width: 100%
  height: 7rem;
  background: white
}
.time_tabs{
  padding: 10px;
  background: white
  .tabs_btn{
    width: 100%;
    height: .7rem;
    display: flex
    justify-content: center
    align-items: center
    font-size: 14px;
    font-weight: 400;
    color: #4E5969;
  }
  .tabs_btn_active{
    color: #3562DB;
    background: #E6EFFC;
  }
}
.give_add{
  padding: 10px 10px 0px 10px
  .give_add_item{
    width: 100%
    height: 2rem;
    background: white;
    border-radius: 8px;
    display: flex
    justify-content: center
    align-items: center;
    flex-direction: column
    margin-bottom: 10px;
    .add_item_top{
      display: flex
      align-items: center
      margin-bottom: 15px;
      p{
        font-size: 14px;
        font-weight: 400;
        color: #4E5969;
        margin-right: 10px;
      }
      span{
        font-size: 18px;
        font-weight: bold;
        color: #1D2129;
      }
    }
    .add_item_bom_down{
      background: #E8FFEA !important;
      span{
        color: #00B42A !important;
      }
    }
    .add_item_bom_up{
      background: #FFECE8 !important;
      span{
        color: #F53F3F !important;
      }
    }
    .add_item_bom{
      width: 80%;
      display: flex
      justify-content: center
      align-items: flex-start
      background: #E5E6EB
      padding: 7px 0;
      p{
        font-size: 14px;
        font-weight: 400;
        color: #4E5969;
        line-height: 14px;
      }
      span{
        font-size: 16px;
        font-weight: 400;
        margin: 0 4px 0 10px;
        line-height: 16px;
      }
      img{
        margin-top: -1px;
      }
    }
  }
}
.give_handle{
  padding:10px;
  background: white;
  .give_handle_item{
    background: #FFECE8;
    border-radius: 8px;
    padding: 19px 0;
    display: flex
    justify-content: center
    align-items: center
    p{
      font-size: 14px;
      font-weight: 400;
      margin: 0 16px 0 5px;
    }
    span{
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.inner{
  background-color: #F2F4F9;
}
</style>
