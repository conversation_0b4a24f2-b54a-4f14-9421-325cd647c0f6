import global from "@/utils/Global";
export default {
  data() {
    return {};
  },
  methods: {
    getGiveLineEchats(data,unit){
      let myChart4 = this.$echarts.init(
        document.getElementById("giveLineEchats")
      );
      // let data = []
      // for (let i = 1; i < 31; i++) {
      //   data.push({
      //     time: i,
      //     value:Math.round(Math.random() * 500)
      //   })
      // }
      let textColor = "#86909C";
      let option = {
        backgroundColor: "#ffffff",
        color: ["#FBAF1B"],
        tooltip: {// 鼠标悬浮弹出提示框
          trigger: 'axis', // 提示框弹出的触发时间，折线图和柱状图为axis
          backgroundColor: 'rgba(255, 255, 255)',
          formatter: (params)=>{
            return `<p style="color:#86909C;font-size: 12px;">${params[0].name}${unit}</p>
                    <p style="color:#000000;font-size: 12px;margin: 10px 0 8px 0;">报警数量</p>
                    <p style="color:#000000;font-size: 12px;font-weight: 500;">${params[0].value}单</p>`
          },
          extraCssText: 'width: 100px;box-shadow: 0px 5px 14px 4px rgba(0,0,0,0.05), 0px 3px 8px 0px rgba(0,0,0,0.08), 0px 2px 3px -4px rgba(0,0,0,0.12);',
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show:false,
          },
          axisTick: {
            show:false,
          },
          axisLabel: {
            color: textColor,
            // interval:4,
            fontSize: 12
          },
          data: data.map(x => x.time),
        },
        yAxis: {
          type: 'value',
          name: '单位：单',
          minInterval: 1,
          gridIndex: 0,
          nameTextStyle:{
            color: textColor,
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 13
          },
        },
        series: [
          {
            data: data.map(x => x.count),
            type: 'line',
          }
        ],
        grid: {
          // 让图表占满容器
          top: "30px",
          left: "16px",
          right: "16px",
          bottom: "20px",
          containLabel: true
        }
      }
      myChart4.setOption(option);
    },
    getGiveTransverseBarEchats(data) {
      let myChart3 = this.$echarts.init(
        document.getElementById("giveTransverseBarEchats")
      );
      global.extension(myChart3,'yAxis',5)
      let textColor = "#86909C";
      let option = {
        backgroundColor: "#ffffff",
        color: ["#FBAF1B"],
        textStyle: {
          color: "#333"
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          }
        },
        grid: {
          // 让图表占满容器
          top: "30px",
          left: "16px",
          right: "16px",
          bottom: "20px",
          containLabel: true
        },
        xAxis: {
          type: "value",
          minInterval: 1,
          gridIndex: 0,
          axisLine: {
            show:false,
          },
          axisTick: {
            show:false,
          },
          axisLabel: {
            color: textColor,
            fontSize: 12
          }
        },
        yAxis: {
          data: data.map(x => x.incidentName),
          triggerEvent: true,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 13,
            margin: 6,
            formatter:function(params){
              var val="";
              if(params.length >4){
                  val = params.substr(0,5)+'...';
                  return val;
              }else{
                  return params;  
              }
            }
          },
        },
        series: [
          {
            type: "bar",
            barCategoryGap: "30%",
            data: data.map(x => x.count)
          }
        ]
      };
      myChart3.setOption(option);
    },
    getGiveVerticalBarEchats(data) {
      let myChart2 = this.$echarts.init(
        document.getElementById("giveVerticalBarEchats")
      );
      global.extension(myChart2,'xAxis',4)
      let textColor = "#86909C";
      let option = {
        backgroundColor: "#ffffff",
        color: ["#3562DB"],
        xAxis: {
          type: "category",
          data: data.map(x => x.projectName),
          triggerEvent: true,
          axisLine: {
            lineStyle: {
              color: "#E5E6EB"
            }
          },
          axisLabel: {
            interval:0,
            color: textColor,
            fontSize: 12,
            formatter:function(params){
              var val="";
              if(params.length > 4){
                  val = params.substr(0,4)+'...';
                  return val;
              }else{
                  return params;  
              }
            }
          }
        },
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            start: 0,
            end: 20,  // 数据窗口范围的结束百分比。范围是：0 ~ 100。
            height: 5, // 组件高度
            left: 5, // 左边的距离
            right: 5, // 右边的距离
            bottom: 10, // 下边的距离
            show: data.length > 5,  // 是否展示
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            zoomLock: true,         // 是否只平移不缩放
            moveOnMouseMove: false, // 鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel: false, // 鼠标移动能触发数据窗口缩放
            // 下面是自己发现的一个问题，当点击滚动条横向拖拽拉长滚动条时，会出现文字重叠，导致效果很不好，以此用下面四个属性进行设置，当拖拽时，始终保持显示六个柱状图，可结合自己情况进行设置。添加这个属性前后的对比见**图二**
            startValue: 0, // 从头开始。
            endValue: 4,  // 最多六个
            minValueSpan: 4,  // 放大到最少几个
            maxValueSpan: 4  //  缩小到最多几个
          },
          {
            type: 'inside',  // 支持内部鼠标滚动平移
            start: 0,
            end: 20,
            zoomOnMouseWheel: false,  // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移 
          }
        ],
        yAxis: {
          type: "value",
          name: '单位：单',
          minInterval: 1,
          gridIndex: 0,
          nameTextStyle:{
            color: textColor,
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 13
          },
          // y轴轴线颜色
          splitLine: {
            show: true,
            lineStyle: {
              color: "#E5E6EB"
            }
          }
        },

        series: [
          {
            data: data.map(x => x.count),
            type: "bar",
            barWidth: 10
          }
        ],
        grid: {
          // 让图表占满容器
          top: "30px",
          left: "16px",
          right: "16px",
          bottom: "20px",
          containLabel: true
        }
      };
      myChart2.setOption(option);
    },
    getPieEchats(data) {
      let myChart1 = this.$echarts.init(
        document.getElementById("givePieEchats")
      );
      myChart1.clear()
      if (!data.length) {
        return myChart1.setOption({
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        })
      }
      let total = data.reduce((a, b) => a + b.value, 0);
      myChart1.setOption({
        backgroundColor: "#fff",
        color: ["#F53F3F", "#FF7D00", "#3562DB", '#2186DB'],
        title: [],
        legend: {
          bottom: 20,
          left: "center",
          orient: "vertical",
          itemHeight: 8,
          itemWidth: 8,
          formatter: name => {
            let value;
            for (let i = 0; i < data.length; i++) {
              if (data[i].name == name) {
                value = data[i].value;
              }
            }
            // console.log('(value / total)*100===========',(value / total)*100);
            let proportion = total ? Math.round((value / total) * 100) : 0;
            let arr = `{name|${name} : ${value}个}{proportion|占比 : ${proportion}%}`;
            return arr;
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                color: "#4E5969",
                padding: [0, 20, 0, 0]
              },
              proportion: {
                fontSize: 14,
                color: "#4E5969"
              }
            }
          }
        },
        series: [
          {
            type: "pie",
            radius: "45%",
            center: ["50%", "35%"],
            data: data,
            labelLine: {
              show: true,
              lineStyle: {
                color: "#4E5969"
              }
            },
            label: {
              show: true,
              color: "#4E5969",
              fontSize: 14
            }
          }
        ]
      });
    }
  }
};
