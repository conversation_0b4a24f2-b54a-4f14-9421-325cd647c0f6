<template>
  <div class="view_box">
    <Header title="风险详情" @backFun="goBack"> </Header>
    <!-- 风险点基本信息 -->
    <div class="card_box">
      <div class="card_top border-bottom">
        <span></span>
        风险点基本信息
      </div>
      <div class="card_content">
        <ul>
          <li>
            <div class="row_title">风险点名称</div>
            <div class="row_content">{{ riskDetails.riskName }}12121</div>
          </li>
          <li v-if="riskDetails.attachmentUrl.length">
            <div class="row_title">风险点图片</div>
          </li>
          <li class="accident_photo">
            <img
              v-for="srcPhoto in riskDetails.attachmentUrl"
              :key="srcPhoto"
              :src="srcPhoto"
              v-preview="srcPhoto"
              preview-title-enable="true"
              preview-nav-enable="false"
              preview-top-title-tnable="true"
              preview-title-extend="false"
              alt=""
            />
          </li>
          <li>
            <div class="row_title">风险位置</div>
            <div class="row_content">{{ riskDetails.riskPlace }}</div>
          </li>
          <li>
            <div class="row_title">责任部门</div>
            <!-- <div
              @click="changeStatusPro('taskTeamNameShowMore1')"
              :class="[
                'row_content',
                taskTeamNameShowMore1 ? '' : 'height_one'
              ]"
            >
              {{ riskDetails.taskTeamName }}
            </div> -->
            <!-- <img
              class="arrow-img"
              src="../../assets/images/riskManagement/arrow-up.png"
              v-if="taskTeamNameShowMore1"
              name="arrow-up"
            />
            <img
              class="arrow-img"
              src="../../assets/images/riskManagement/arrow-down.png"
              v-else
              name="arrow-down"
            /> -->
            <div class="height_one">
              {{ riskDetails.taskTeamName }}
            </div>
          </li>
          <li>
            <div class="row_title">责任人</div>
            <!-- <div
              @click="changeStatusPro('responsiblePersonNameShowMore1')"
              :class="[
                'row_content',
                responsiblePersonNameShowMore1 ? '' : 'height_one'
              ]"
            >
              {{ riskDetails.responsiblePersonName }}
            </div>
            <img
              class="arrow-img"
              src="../../assets/images/riskManagement/arrow-up.png"
              v-if="responsiblePersonNameShowMore1"
              name="arrow-up"
            />
            <img
              class="arrow-img"
              src="../../assets/images/riskManagement/arrow-down.png"
              v-else
              name="arrow-down"
            /> -->
            <div class="height_one">
              {{ riskDetails.responsiblePersonName }}
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- 风险研判信息 -->
    <div class="card_box">
      <div class="card_top border-bottom">
        <span></span>
        风险研判信息
      </div>
      <div class="card_content">
        <ul>
          <li>
            <div class="row_title">风险等级</div>
            <div class="row_content">
              <span
                class="risk_grade"
                :class="{
                  set_bgc_r: riskDetails.riskLevel == 1,
                  set_bgc_o: riskDetails.riskLevel == 2,
                  set_bgc_y: riskDetails.riskLevel == 3,
                  set_bgc_b: riskDetails.riskLevel == 4
                }"
                >{{ riskDetails.riskLevel | levelRisk }}</span
              >
            </div>
          </li>
          <li v-if="riskDetails.riskLevel">
            <div class="row_title">风险评价方法</div>
            <div class="row_content">
              {{ riskDetails.judgeType | evaluationMethodology }}
            </div>
          </li>
          <template v-if="riskDetails.judgeType == 2">
            <li>
              <div class="row_title">L:</div>
              <div class="row_content">{{ riskDetails.LECDLExplain }}</div>
            </li>
            <li>
              <div class="row_title">E:</div>
              <div class="row_content">{{ riskDetails.LECDEExplain }}</div>
            </li>
            <li>
              <div class="row_title">C:</div>
              <div class="row_content">{{ riskDetails.LECDCExplain }}</div>
            </li>
          </template>
          <template v-if="riskDetails.judgeType == 3">
            <li>
              <div class="row_title">L:</div>
              <div class="row_content">{{ riskDetails.LSRLExplain }}</div>
            </li>
            <li>
              <div class="row_title">S:</div>
              <div class="row_content">{{ riskDetails.LSRSExplain }}</div>
            </li>
          </template>
        </ul>
      </div>
    </div>
    <!-- 事故后果 -->
    <div class="card_box" v-if="riskDetails.accidentTypeList.length">
      <div class="card_top border-bottom">
        <span></span>
        事故后果
      </div>
      <div class="card_content">
        <ul>
          <li>
            <div class="row_title">事故后果</div>
            <div class="row_content">
              {{ riskDetails.accidentTypeList | consequence }}
            </div>
          </li>
          <li class="accident_photo">
            <img
              v-for="itemSrc in riskDetails.accidentTypeList"
              :key="itemSrc.id"
              :src="itemSrc.attachmentUrl"
              v-preview="itemSrc.attachmentUrl"
              preview-title-enable="true"
              preview-nav-enable="false"
              preview-top-title-tnable="true"
              preview-title-extend="false"
              alt=""
            />
          </li>
        </ul>
      </div>
    </div>
    <!-- 风险因素及管控措施 -->
    <div class="card_box" style="display: none">
      <div class="card_top border-bottom">
        <span></span>
        风险因素及管控措施
      </div>
      <div class="card_content">
        <ul>
          <li>
            <div class="row_title">主要风险因素</div>
            <div class="row_content">
              <!-- <pre>{{ riskDetails.riskElement }}</pre> -->
              <van-field readonly v-model="riskDetails.riskElement" autosize type="textarea" />
            </div>
          </li>
          <li>
            <div class="row_title">主要风险管控措施</div>
            <div class="row_content">
              <!-- {{ riskDetails.controlElement }} -->
              <van-field readonly v-model="riskDetails.controlElement" autosize type="textarea" />
            </div>
          </li>
          <li>
            <div class="row_title">安全操作要点</div>
            <div class="row_content">
              <!-- {{ riskDetails.operationElement }} -->
              <van-field readonly v-model="riskDetails.operationElement" autosize type="textarea" />
            </div>
          </li>
          <li>
            <div class="row_title">应急处置措施</div>
            <div class="row_content">
              <!-- {{ riskDetails.handleElement }} -->
              <van-field readonly v-model="riskDetails.handleElement" autosize type="textarea" />
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="card_box box_pro">
      <div class="card_top border-bottom">
        <span></span>
        风险因素及管控措施
      </div>
      <div class="card_content">
        <template v-if="riskDetails.riskType == '3' && riskDetails.templateType == '1'">
          <ul v-for="(item, index) in riskDetails.analysisList" :key="item.id">
            <li>
              <div class="row_title">作业步骤{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore1')"
                :class="['row_content', item.showMore1 ? '' : 'height_one']"
              >
                {{ item.checkItem }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore1"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.checkItem }}
              </div>
            </li>
            <li>
              <div class="row_title">危险源或潜事件{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore2')"
                :class="['row_content', item.showMore2 ? '' : 'height_one']"
              >
                {{ item.standard }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore2"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.standard }}
              </div>
            </li>
            <li>
              <div class="row_title">可能发生的事故类型及后果{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore3')"
                :class="['row_content', item.showMore3 ? '' : 'height_one']"
              >
                {{ item.result }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore3"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.result }}
              </div>
            </li>
            <li>
              <div class="row_title">工程技术措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore4')"
                :class="['row_content', item.showMore4 ? '' : 'height_one']"
              >
                {{ item.technicalMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore4"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.technicalMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">管理措施{{ index + 1 }}</div>
              <div class="row_content">
                {{ item.manageMeasure }}
              </div>
              <!-- <img src="../../assets/images/arrow-up.png" v-if="item.showMore5" name="arrow-up" /> -->
              <!-- <img src="../../assets/images/arrow-down.png" v-else name="arrow-down" /> -->
            </li>
            <li>
              <div class="row_title">培训教育措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore6')"
                :class="['row_content', item.showMore6 ? '' : 'height_one']"
              >
                {{ item.educationMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore6"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.educationMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">个体防护措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore7')"
                :class="['row_content', item.showMore7 ? '' : 'height_one']"
              >
                {{ item.protectiveMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore7"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.protectiveMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">应急措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore8')"
                :class="['row_content', item.showMore8 ? '' : 'height_one']"
              >
                {{ item.emergencyMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore8"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.emergencyMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">建议改进措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore9')"
                :class="['row_content', item.showMore9 ? '' : 'height_one']"
              >
                {{ item.adviceMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore9"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.adviceMeasure }}
              </div>
            </li>
          </ul>
        </template>
        <template v-if="riskDetails.riskType != '3' && riskDetails.templateType == '1'">
          <ul v-for="(item, index) in riskDetails.analysisList" :key="item.id">
            <li>
              <div class="row_title">检查项目{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore1')"
                :class="['row_content', item.showMore1 ? '' : 'height_one']"
              >
                {{ item.checkItem }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore1"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.checkItem }}
              </div>
            </li>
            <li>
              <div class="row_title">标准{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore2')"
                :class="['row_content', item.showMore2 ? '' : 'height_one']"
              >
                {{ item.standard }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore2"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.standard }}
              </div>
            </li>
            <li>
              <div class="row_title">不符合标准情况及后果{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore3')"
                :class="['row_content', item.showMore3 ? '' : 'height_one']"
              >
                {{ item.result }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore3"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.result }}
              </div>
            </li>
            <li>
              <div class="row_title">工程技术措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore4')"
                :class="['row_content', item.showMore4 ? '' : 'height_one']"
              >
                {{ item.technicalMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore4"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.technicalMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">管理措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore5')"
                :class="['row_content', item.showMore5 ? '' : 'height_one']"
              >
                {{ item.manageMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore5"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.manageMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">培训教育措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore6')"
                :class="['row_content', item.showMore6 ? '' : 'height_one']"
              >
                {{ item.educationMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore6"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.educationMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">个体防护措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore7')"
                :class="['row_content', item.showMore7 ? '' : 'height_one']"
              >
                {{ item.protectiveMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore7"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.protectiveMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">应急措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore8')"
                :class="['row_content', item.showMore8 ? '' : 'height_one']"
              >
                {{ item.emergencyMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore8"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.emergencyMeasure }}
              </div>
            </li>
            <li>
              <div class="row_title">建议改进措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore9')"
                :class="['row_content', item.showMore9 ? '' : 'height_one']"
              >
                {{ item.adviceMeasure }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore9"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.adviceMeasure }}
              </div>
            </li>
          </ul>
        </template>
        <template v-if="riskDetails.templateType == '2'">
          <ul v-for="(item, index) in riskDetails.analysisList" :key="item.id">
            <li>
              <div class="row_title">主要风险因素{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore1')"
                :class="['row_content', item.showMore1 ? '' : 'height_one']"
              >
                {{ item.riskFactor }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore1"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.riskFactor }}
              </div>
            </li>
            <li>
              <div class="row_title">安全操作要点{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore2')"
                :class="['row_content', item.showMore2 ? '' : 'height_one']"
              >
                {{ item.operationElement }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore2"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.operationElement }}
              </div>
            </li>
            <li>
              <div class="row_title">主要风险管控措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore3')"
                :class="['row_content', item.showMore3 ? '' : 'height_one']"
              >
                {{ item.controlElement }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore3"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.controlElement }}
              </div>
            </li>
            <li>
              <div class="row_title">应急处置措施{{ index + 1 }}</div>
              <!-- <div
                @click="changeStatus(item, 'showMore4')"
                :class="['row_content', item.showMore4 ? '' : 'height_one']"
              >
                {{ item.handleElement }}
              </div>
              <img
                src="../../assets/images/riskManagement/arrow-up.png"
                v-if="item.showMore4"
                name="arrow-up"
              />
              <img
                src="../../assets/images/riskManagement/arrow-down.png"
                v-else
                name="arrow-down"
              /> -->
              <div class="row_content">
                {{ item.handleElement }}
              </div>
            </li>
          </ul>
        </template>
      </div>
    </div>
    <!-- 安全责任信息 -->
    <div class="card_box">
      <div class="card_top border-bottom">
        <span></span>
        安全责任信息
      </div>
      <div class="card_content">
        <ul>
          <li>
            <div class="row_title">责任部门</div>
            <!-- <div
              @click="changeStatusPro('taskTeamNameShowMore2')"
              :class="[
                'row_content',
                taskTeamNameShowMore2 ? '' : 'height_one'
              ]"
            >
              {{ riskDetails.taskTeamName }}
            </div>
            <img
              class="arrow-img"
              src="../../assets/images/riskManagement/arrow-up.png"
              v-if="taskTeamNameShowMore2"
              name="arrow-up"
            />
            <img
              class="arrow-img"
              src="../../assets/images/riskManagement/arrow-down.png"
              v-else
              name="arrow-down"
            /> -->
            <div class="row_content">
              {{ riskDetails.taskTeamName }}
            </div>
          </li>
          <li>
            <div class="row_title">责任人</div>
            <!-- <div
              @click="changeStatusPro('responsiblePersonNameShowMore2')"
              :class="[
                'row_content',
                responsiblePersonNameShowMore2 ? '' : 'height_one'
              ]"
            >
              {{ riskDetails.responsiblePersonName }}
            </div>
            <img
              class="arrow-img"
              src="../../assets/images/riskManagement/arrow-up.png"
              v-if="responsiblePersonNameShowMore2"
              name="arrow-up"
            />
            <img
              class="arrow-img"
              src="../../assets/images/riskManagement/arrow-down.png"
              v-else
              name="arrow-down"
            /> -->
            <div class="row_content">
              {{ riskDetails.responsiblePersonName }}
            </div>
          </li>
          <li>
            <div class="row_title">应急电话</div>
            <div class="row_content">{{ riskDetails.urgentPhone }}</div>
          </li>
          <li>
            <div class="row_title">应急联系电话</div>
            <div class="row_content">{{ riskDetails.urgentContactPhone }}</div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import axios from "axios";
import { Toast } from "vant";
const qs = require("qs");
export default {
  data() {
    return {
      riskDetails: {
        accidentTypeList: [],
        attachmentUrl: []
      },
      params: {
        unitCode: "",
        hospitalCode: "",
        id: ""
      },
      taskTeamNameShowMore1: false,
      taskTeamNameShowMore2: false,
      responsiblePersonNameShowMore1: false,
      responsiblePersonNameShowMore2: false,
      timer: ""
    };
  },
  filters: {
    consequence(val) {
      let consequenceArray = val
        .map(item => {
          return item.dictLabel;
        })
        .join(",");
      return consequenceArray;
    },
    evaluationMethodology(val) {
      if (!val) return "未研判";
      const wayObject = {
        1: "直接研判",
        2: "LEC研判法",
        3: "LS研判法"
      };
      return wayObject[val];
    },
    levelRisk(val) {
      if (!val) return "未研判";
      const riskObject = {
        1: "重大风险",
        2: "较大风险",
        3: "一般风险",
        4: "低风险"
      };
      return riskObject[val];
    }
  },
  created() {
    this.timer = setInterval(() => {
      if (localStorage.getItem("loginInfo")) {
        this.delay = true;
        this.params.unitCode = JSON.parse(localStorage.getItem("loginInfo")).unitCode;
        this.params.hospitalCode = JSON.parse(localStorage.getItem("loginInfo")).hospitalCode;
        this.params.id = api.pageParam.id;
        this.getRiskDetail();
        clearInterval(this.timer);
      }
    }, 100);
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    //   setTimeout(() => {
    //     this.sysClickBack();
    //   }, 500);
  },
  methods: {
    // 图片预览
    previewImage(item, photoArray) {
      // wx.previewImage({
      //   current: item, // 当前显示图片的http链接
      //   urls: photoArray // 需要预览的图片http链接列表
      // });
    },
    getRiskDetail() {
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        duration: 0
      });
      axios
        .post(__PATH.IPSM_URL2 + "/riskManageController/getRiskDetail", qs.stringify(this.params), {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          // 清除loading
          Toast.clear();
          if (res.data.code == "200") {
            this.riskDetails = res.data.data;
            let arr = [];
            this.riskDetails.attachmentUrl.forEach(item => {
              arr.push(this.$YBS.imgUrlTranslation(item));
            });
            let arr2 = [];
            this.riskDetails.accidentTypeList.forEach(item => {
              arr2.push(this.$YBS.imgUrlTranslation(item));
            });
            this.riskDetails.accidentTypeList = arr2;
            this.riskDetails.attachmentUrl = arr;
            this.riskDetails.analysisList.forEach(item => {
              item.showMore1 = false;
              item.showMore2 = false;
              item.showMore3 = false;
              item.showMore4 = false;
              item.showMore5 = false;
              item.showMore6 = false;
              item.showMore7 = false;
              item.showMore8 = false;
              item.showMore9 = false;
            });
          }
        })
        .catch(error => {
          throw new Error(error);
        });
    },
    sysClickBack() {
      // api.addEventListener(
      //   {
      //     name: "keyback666"
      //   },
      //   (ret, err) => {
      //     this.goBack();
      //   }
      // );
    },
    goBack() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
    changeStatus(item, label) {
      item[label] = !item[label];
      this.$forceUpdate();
    },
    changeStatusPro(label) {
      this[label] = !this[label];
      this.$forceUpdate();
    }
  }
};
</script>
<style lang="scss" scoped>
.view_box {
  background-color: #f0f0f4;
}

.card_box {
  background-color: #fff;
  padding: 0 0.32rem;
  padding-bottom: 15px;
  margin-top: 5px;
  .card_top {
    padding-left: 7px;
    span {
      display: inline-block;
      width: 4px;
      height: 15px;
      background: #3562db;
    }
    height: 49px;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #353535;
    line-height: 49px;
  }
}
.card_content {
  li {
    display: flex;
    margin-top: 15px;
    padding: 0.2rem 0.3rem align-items center;
    .row_title {
      width: 110px;
      margin-right: 0.3rem;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #4e5969;
    }
    .row_content {
      flex: 1;
      flex-wrap: wrap;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #1d2129;
      word-break: break-all;
      .risk_grade {
        // padding: 5px 10px;
        padding: 4px 6px;
        margin-right: 8px;
        font-size: 12px;
      }
    }
    .row_img {
      img {
        width: 80px;
        height: 80px;
        margin: 5px 5px 0 0;
      }
    }
  }
}
.accident_photo {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0.3rem;
  img {
    width: 80px;
    height: 80px;
    margin: 5px 5px 0 0;
  }
}
.card_box:nth-child(1) {
  margin-top: 0;
}
.set_bgc_r {
  color: #f53f3f;
  background-color: #ffece8;
}
.set_bgc_o {
  color: #ff7d00;
  background-color: #fff7e8;
}
.set_bgc_y {
  color: #d9d000;
  background-color: #fffde8;
}
.set_bgc_b {
  color: #3562db;
  background-color: #e6effc;
}
.box_pro img,
.arrow-img {
  width: 20px;
  height: 20px;
}
.height_one {
  height: 20px;
  overflow-y: hidden;
}
ul {
  padding-right: 16px;
}
</style>
