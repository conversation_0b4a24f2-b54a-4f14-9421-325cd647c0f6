<template>
  <div class="container">
    <Header title="重点台账" @backFun="goBack"> </Header>
    <van-tabs
      v-model="active"
      sticky
      offset-top="10vh"
      color="#3562DB"
      line-width="60"
    >
      <van-tab title="设备安全">
        <van-pull-refresh
          v-model="refreshing"
          @refresh="onRefresh"
          success-text="刷新成功"
        >
          <div v-for="(item, index) in listData" :key="index" class="item">
            <van-empty description="暂无数据" v-if="!item" />
            <div v-else @click="goTaskDetail(item)">
              <div class="accoutName">
                <div>{{ item.eqName || "-" }}</div>
                <div>
                  <span v-if="item.facilityStateName" :class="item.facilityStateName=='正常'?'normal':item.facilityStateName=='异常'?'unnormal':'otherStatus'">{{item.facilityStateName}}</span>
                  <van-icon name="arrow" color="#C9CDD4"/>
                </div>
              </div>
              <div class="child">
                  <span class="child-text">首次投入使用日期</span>
                  <span class="child-value">{{ item.firstTime || "-" }}</span>
                </div>
                <div class="child">
                  <span class="child-text">使用部门</span>
                  <span class="child-value">{{ item.useDept || "-" }}</span>
                </div>
                <div class="child">
                  <span class="child-text">安全责任人</span>
                  <span class="child-value">{{ item.safeDuty || "-" }}</span>
                </div>
            </div>
          </div>
          <div class="bottom-tips">
            <div v-if="finished">
              加载中<img
                style="vertical-align:middle;"
                src="../../assets/images/riskManagement/加载.png"
              />
            </div>
            <div v-else>暂无更多</div>
          </div>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      active: 0,
      loading: false,
      finished: true,
      refreshing: false,
      listData: [],
      params: {
        pageNo: 1,
        pageSize: 10
      },
      loginInfo: "",
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      timer: "",
      delay: false,
      loginInfo: ""
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.params.pageNo = 1;
    this.timer = setInterval(() => {
      this.delay = true;
      this.getDataList();
      this.sysClickBack();
      clearInterval(this.timer);
    }, 100);
    window.onscroll = this.isscroll;
  },
  mounted() {},
  methods: {
    sysClickBack() {
      // api.addEventListener({
      //   name:'keyback666'
      // },(ret,err) => {
      //   this.goBack()
      // })
    },
    onRefresh() {
      this.params.pageNo = 1;
      this.params.pageSize = 10;
      this.finished = true;
      this.getDataList();
    },
    goTaskDetail(item) {
      this.$router.push({
        path: "accountDetail",
        query: {
          detail: item
        }
      });
    },
    getDataList(type) {
      let data = {
        ...this.params,
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode,
        staffId: this.loginInfo.staffId,
        sysForShort: "ipsm",
        platformFlag: "2",
        userName: this.loginInfo.userName
      };
      this.$api.getAccountList(data).then(res => {
        if (type == "pull") {
          if (res.count == this.listData.length) return;
          res.list.forEach(item => {
            this.listData.push(item);
          });
        } else {
          this.listData = res.list;
        }
        if (res.list == 0 || this.listData.length == res.count) {
          console.log("加载结束");
          this.finished = false;
        }
        this.refreshing = false;
      });
    },
    isscroll() {
      let scrollY =
        document.documentElement.scrollTop || document.body.scrollTop;
      let vh = document.documentElement.clientHeight;
      const scrollHeight = document.documentElement.scrollHeight;
      if (
        scrollY + vh >= scrollHeight - 10 &&
        this.finished &&
        this.$route.name == "account"
      ) {
        console.log("满足加载条件");
        this.params.pageNo++;
        this.thorttle(this.getDataList("pull"));
      }
    },
    // 节流
    thorttle(fn) {
      let timer = null;
      return () => {
        if (!timer) {
          timer = setTimeout(() => {
            fn.call(this);
            timer = null;
          }, 1000);
        }
      };
    },
    goBack() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="stylus" scoped>
.container {
  height: 100%;
  background-color: #F2F4F9;
  .item{
    background-color: #fff
    margin:0 .1875rem .1875rem .1875rem
    padding:.3125rem
    border-radius: .125rem
    font-size: 16px;
    color:#1D2129 ;
    font-family: PingFang SC-Medium
    line-height :20px
    .accoutName{
      display: flex;
      justify-content space-between
      >div:nth-child(1){
        flex: 1
      }
    }
    .child{
      margin-top:.1875rem;
      display:flex
    }
  }
}
/deep/ .van-tab__text {
  font-size: 15px;
}
/deep/ .van-tabs__content {
  margin-top: 10px;
  background-color: #F2F4F9;
}
.child-text {
  width:43%;
  color: #4E5969 ;
  margin-right:.3125rem
}
.child-value{
  flex: 1
}
.bottom-tips {
  font-size: 12px;
  text-align: center;
  color: #969799;
}
.normal{
  display: inline-block
  width 36px
  height: 24px
  line-height: 24px
  background-color #E8FFEA
  color:#00B42A
  font-size: 12px
  text-align center
  vertical-align: middle
}
.unnormal{
  display: inline-block
  width 36px
  height: 24px
  line-height: 24px
  background-color #FFECE8
  color:#F53F3F
  font-size: 12px
  text-align center
  vertical-align: middle
}
.otherStatus{
  display: inline-block;
  width :36px;
  height: 24px;
  line-height: 24px
  background-color: #FFF7E8;
  color:#FF7D00 ;
  font-size: 12px
  text-align center
  vertical-align: middle
}
</style>
