<template>
  <div class="home-container">
    <div class="home-content">
      <div class="content-list">
        <serve-list
          :serveList="serveListTest"
          :checkMenuList="checkMenu"
          :hospitalCode="hospitalCode"
        ></serve-list>
      </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
import serveList from "@/common/customize/serveList/serveList";
import noticesList from "@/common/customize/serveList/noticesList";
import serveSlide from "@/common/customize/serveList/serveSlide";
import meeting from "@/assets/images/icon/meeting.png";
import Dining from "@/assets/images/icon/Dining.png";
import elevator from "@/assets/images/icon/elevator.png";
import bus from "@/assets/images/icon/bus.png";
import water from "@/assets/images/icon/water.png";
import repair from "@/assets/images/icon/repair.png";
import cleaning from "@/assets/images/icon/cleaning.png";
import consult from "@/assets/images/icon/consult.png";
import complaint from "@/assets/images/icon/complaint.png";
import Assets from "@/assets/images/icon/Assets.png";
import Linen from "@/assets/images/icon/Linen.png";
import carry from "@/assets/images/icon/carry.png";
import Venue from "@/assets/images/icon/Venue.png";
import feedback from "@/assets/images/icon/feedback.png";
import custom from "@/assets/images/icon/custom.png";
import PubliceCar from "@/assets/images/icon/PubliceCar.jpg";

import { mapState } from "vuex";
export default {
  computed: {
    ...mapState(["loginInfo"])
  },
  data() {
    return {
      checkMenu: [],
      // swiperTestData: [
      //   { image: require("../../assets/images/carousel/carousel1.png") },
      //   { image: require("../../assets/images/carousel/carousel2.png") },
      //   { image: require("../../assets/images/carousel/carousel3.png") }
      // ],
      serveListTest: [
        {
          title: "设备设施维护",
          list: [
            {
              text: "总务报修",
              icon: repair,
              path: "/repair",
              workTypeCode: "1"
            },
            {
              text: "资产报修",
              icon: Assets,
              path: "/Assets",
              workTypeCode: "999"
            }
          ]
        },
        {
          title: "综合保障",
          list: [
            {
              text: "应急保洁",
              icon: cleaning,
              path: "/cleaning",
              workTypeCode: "2"
            },
            {
              text: "运送服务",
              icon: carry,
              path: "/transport",
              workTypeCode: "3"
            },
            {
              text: "综合服务",
              path: "/service",
              icon: elevator,
              workTypeCode: "6"
            },
            {
              text: "布草服务",
              path: "/Linen",
              icon: Linen,
              workTypeCode: "777"
            }
          ]
        },
        {
          title: "业务预定",
          list: [
            // {
            //   text: "会务服务",
            //   icon: meeting,
            //   path: process.env.WX + "/icrs/dist/#/"
            // },
            // {
            //   text: "食堂餐饮",
            //   icon: Dining,
            //   path: process.env.WX + "/order/#/home"
            // },
            // {
            //   text: "场馆预订",
            //   icon: Venue,
            //   path: process.env.WX + "/isrs/index/field.html"
            // },
            {
              text: "公车预订",
              icon: PubliceCar,
              path: "/carApply"
            }
          ]
        },
        {
          title: "投诉与建议",
          list: [
            {
              text: "后勤投诉",
              icon: complaint,
              path: process.env.WX + "/complain?source=0"
            },
            {
              text: "意见反馈",
              icon: feedback,
              // path: process.env.WX + "/feedback"
              path: "/feedback"
            }
          ]
        }
      ],
      hospitalCode: "" //如果有值则为天坛后勤公众号
    };
  },

  components: {
    serveList,
    noticesList
  },
  created() {
    // console.log(process.env.WX);
    
    if (this.$route.query.hospitalCode) {
      this.hospitalCode = this.$route.query.hospitalCode;
    }
    let checkArr = [];
    if (this.loginInfo.userOffice) {
      this.$api
        .getWorkTypeList({
          flag: 1,
          type: 1
        })
        .then(res => {
          res.forEach(item => {
            // if (item.workTypeCode == "ba04baba697d42a09aca1cb08f1332d7") {
            //   //桶装水                
            //   item.text = item.workTypeName;
            //   item.path = "/Water";
            //   item.source = "custom";
            //   item.icon = custom;
            // } else {
            //   item.text = item.workTypeName;
            //   item.path = "/task";
            //   item.source = "custom";
            //   item.icon = custom;              
            // }
              item.text = item.workTypeName;
              item.path = "/task";
              item.source = "custom";
              item.icon = custom;              
              item.template = item.template;              
            if (item.workTypeCode.length > 2) {
              this.serveListTest[1].list.push(item);
            } else {
              checkArr.push(item);
            }
          });
          this.checkMenu = checkArr.map(result => {
            return result.workTypeCode;
          });
          console.log(this.checkMenu);
          this.serveListTest.forEach(item => {
            item.list.forEach(list => {
              if (list.workTypeCode && list.workTypeCode.length == 1) {
                if (this.checkMenu.indexOf(list.workTypeCode) == -1) {
                  list.hidden = true
                }
              }
            });
          });
        });
    }
  }
};
</script>
<style rel="stylesheet/stylus" lang="stylus" scoped type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.home-container {
  height: 100%;
  background: $bgColor;

  .home-content {
    /* position: relative;
    top: -1.6rem; */
    .content-list {
      // bg-image('~images/bg');
      background: #FFFFFF;
      // padding: 0.38rem 0.2rem;
    }

    .home-swiper {
      height: 2.8rem;
      margin-top: 15px;

      img {
        width: 100%;
        height: 100%;
      }

      .cube-slide-dots {
        text-align: right;

        & > span {
          height: 3px;
        }

        & > .active {
          background: $color;
        }
      }
    }
  }
}
</style>
