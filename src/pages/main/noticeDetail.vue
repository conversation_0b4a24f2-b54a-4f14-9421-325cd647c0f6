<template>
  <div  class="notice-content">
    <div class="notice-content-header">{{notice.title}}</div>
    <div class="notice-content-time" v-text=" notice.createTime"></div>
    <div class="notice-content-content" v-html="notice.content"></div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment";

export default {
  name: "noticeDetail",
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  data() {
    return {
      notice: {}
    };
  },
  created() {
    let params = {
      id: this.$route.query.id,
      userId: this.loginInfo.id,
      readFlag: true
    };
    if (this.staffInfo.type == "1") {
      this.$api.getNoticeDetail(params).then(res => {
        if(res.createTime!==0){
          res.createTime=moment(res.createTime).format('YYYY-MM-DD  HH:mm:ss')
        }
        this.notice = res;
      });
    } else {
      this.$api.getDisNoticeDetail(params).then(res => {
        if(res.createTime!==0){
          res.createTime=moment(res.createTime).format('YYYY-MM-DD  HH:mm:ss')
        }
        this.notice = res;
      }).catch(err=>{
        this.notice = {
          title:"404",
          content:"<div><img style='width:100%' src="+ require('../../assets/images/default/block.png') +"></div>"
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus"  type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';
.notice-content{
  // 覆盖通用样式设置 仅作用到此页面
  strong{
    font-weight: bold !important; 
  }
  ul,li{
    list-style-type: inherit ;
  }
  ol{
    list-style-type: decimal  ;
  }
  sub{
    vertical-align:sub;
  }
  sup {
    vertical-align: super 
  }
  u{
    text-decoration: underline  ;
  }
  s{
    text-decoration: line-through  ;
  }
  i, cite, em{
    font-style: italic;
  }
  table,tbody{
    border:1px solid #000;
    border-collapse: collapse;
  }
  tr{
     vertical-align: inherit ;
    border:1px solid #000;
    border-collapse :separate;
  }
  td{
    border:1px solid #000;
     vertical-align: inherit ;
  }
  th{
    border:1px solid #000;
    display:table-cell;
    vertical-align: inherit;
  }
}


.notice-content {
  height: 100vh;
  overflow-x :hidden;
  padding:16px 16px 0 16px;
}

.notice-content-header {
  min-height : 44px;
  line-height: 0.42rem;
  font-size: 17px;
  font-weight: 600;
  color: rgba(0, 0, 0, 1);
  word-wrap: break-word;
  word-break:break-all;
}

.notice-content-time {
  padding: 5px 0;
  color :#A6A6B1;
  font-size:13px;
}
.notice-content-content {
  padding-top:15px;
  padding-bottom:5px;
  word-wrap: break-word;
  word-break:break-all;
  line-height :0.42rem
}
</style>