<template>
<div>

  <div v-if="noticesTestList.length>0" class="noticeDetail-list content">
    <div  v-for="(item,index) of noticesTestList" :key="index">
      <div  class="list-content" @click="openToNoticeDetail(item)">
        <h3 class="list-title">{{item.title}}</h3>
        <div class="list-time" v-text=" moment(item.createTime)"></div>
        <div class="list-text" v-html="formatTime(item.content)"></div>
      </div>
    </div>
     <div class="footer weui-loadmore" >
        <span v-if="loadMode" class="weui-loadmore__tips">
          <i class="weui-loading"></i>正在加载
        </span>
        <span v-else>{{hintMessage}}</span>
      </div>
  </div>
  <!-- 没有数据的时候 -->
  <div v-else class="no">
    <img src="@/assets/images/noDataDefault/search-empty.png" />
    <div class="no-text">暂无咨讯~</div>
  </div>
</div>
</template>

<script>
import { mapState } from "vuex";

let moment = require("moment");


export default {
  name: "noticeDetailList",
  computed: {
    ...mapState(["loginInfo", "staffInfo"]),
  },
  data() {
    return {
      noticesTestList:[],
      hintMessage:"没有更多数据了",
      loading: true,
      flag: false,
      loadMode:false
    };
  },
  methods:{
    openToNoticeDetail(option) {
      this.$router.push({
        path: "/noticeDetail",
        query: {
          id: option.id
        }
      });
    },
    formatTime(value){
      value.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi);
      return value.replace(/<[^>]+>|&[^>]+;/g,"");
    },
    moment(value){
        value=moment(value).format('YYYY-MM-DD  HH:mm:ss')
      return value
    },
    getNotice(pageInfo,flag){
      pageInfo = pageInfo || {};
        let params = {
        curPage: pageInfo.curPage ||1,
        pageSize: pageInfo.pageSize || 20,
        userId: this.staffInfo.staffId,
        deptCode: this.staffInfo.officeId //this.staffInfo.type == 1? this.staffInfo.officeId:this.staffInfo.deptCode
      };
      if (this.staffInfo.type == "1") {
        //院内
        this.$api.getNotice(params).then(res => {
          this.loadMode = false
          pageInfo.curPage = pageInfo.curPage ? pageInfo.curPage : 1;
          if (pageInfo.curPage <= 1) {
          this.noticesTestList = res;
        } else {
          if (res.length == 0) {
            this.hintMessage = "没有更多数据了";
            this.loading = false;
          } else {
            this.noticesTestList = this.noticesTestList.concat(res);
          }
        }
        if (flag) {
          this.flag = false;
          $(".content").pullToRefreshDone();
        }
        });
      } else {
        this.$api.getNoticeList(params).then(res => {
        this.loadMode = false
        pageInfo.curPage = pageInfo.curPage ? pageInfo.curPage : 1;
        if (pageInfo.curPage <= 1) {
          this.noticesTestList = res;
        } else {
          if (res.length == 0) {
            this.hintMessage = "没有更多数据了";
            this.loading = false;
          } else {
            this.noticesTestList = this.noticesTestList.concat(res);
          }
        }

        if (flag) {
          this.flag = false;
          $(".content").pullToRefreshDone();
        }
        });
      }
    }
  },
  created() {
    let i = 1;
    $(document.body)
      .infinite()
      .on("infinite", () => {
        if (this.loading) {
          this.loadMode = true
          this.getNotice({
            pageSize: 20,
            curPage: i++
          });
        }
      });
      this.getNotice()
  },
  mounted() {

  },
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';
.noticeDetail-list{
  width :100vw;
  height :100vh;
  overflow-x :hidden;
  background :#f0eff2;
}
.list-content{
  margin-bottom:5px;
  padding:15px;
  background:#fff;
  .list-title{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size:16px;
    font-family:PingFang SC;
    font-weight:500;
    color:rgba(53,53,53,1);
    line-height:19px;

  }
  .list-time{
    padding-top:7px;
    padding-bottom:9px;
    font-size :12px;
    color:#A6A6B1;
  }
  .list-text{
    font-size :14px;
    color:$contentColor;
    line-height :20px;
    overflow: hidden;
    position : relative;
    text-overflow: ellipsis;
    max-height : 56px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    word-break:break-all;
  }
  .list-text::after{
    content:"...";
    position:absolute;
    bottom:0;
    right:0;
    padding:0 5px 0px 5px;
    background-color: #fff;
  }
}
.footer{
  color:$textColor;
  font-size:13px;
  text-align :center;
}
.no {
  width: 100%;
  margin-top: 2.24rem;
  text-align: center;
  font-size: 0.3rem;
  color: $contentColor;

  img {
    width: 2.68rem;
  }

  .no-text {
    margin-top: 0.59rem;
  }
}

</style>
