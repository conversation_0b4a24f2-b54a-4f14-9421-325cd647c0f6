<template>
  <page-layout @back="backFun">
    <div class="info-box">
      <!-- 作业名称 -->
      <div class="title-text">
        <span>{{ detailData.projectName }}</span>
        <div :class="['status-tag', getStatusClass(detailData.projectStatusCode)]">
          {{ getStatusText(detailData.projectStatusCode) }}
        </div>
      </div>
      <div class="info-item">
        <span class="info-label" style="line-height: 20px">施工地点：</span>
        <div class="info-value-wrapper">
          <span class="info-value" style="line-height: 20px">{{ detailData.locationType == "0" ? "室内" : "室外" }}</span>
          <span class="info-value" style="line-height: 20px">{{ detailData.locationType == "0" ? detailData.projectLocationName : "医院>院区>室外" }}</span>
        </div>
      </div>
      <div class="info-item">
        <span class="info-label">类型：</span>
        <span class="info-value">{{ detailData.businessFormName }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">作业证：</span>
        <span class="info-value">{{ detailData.projectName }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">适用流程：</span>
        <span class="info-value">{{ detailData.processDefinitionName }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">描述：</span>
        <span class="info-value">{{ detailData.description }}</span>
      </div>
    </div>
    <van-tabs v-model="activeTab" color="#3562DB">
      <van-tab title="申请信息">
        <div class="parting-line"></div>
        <yx-form ref="yxForm" :option="formOption" v-model="formData" :useFormSubmit="false" @formSkipView="skipView" />
        <div class="reply-opinion">
          <van-field v-model="replyOpinion" label="备注" :readonly="queryType != 1" type="textarea" placeholder="请输入备注" rows="3" class="reply-textarea" />
        </div>
        <!-- <div class="topItem notBoder" v-if="queryType == 1 && isSignature">
          <div class="itemTitle">签名</div>
          <template v-if="!signatureData">
            <div class="itemConten" @click="toPatroSignature">
              去签名
              <van-icon name="arrow" />
            </div>
          </template>
          <div v-else class="signatured">
            <img height="44px" width="60px" :src="signatureImage" @click="previewImg([signatureImage])" />
            <div class="itemConten" @click="toPatroSignature">
              重签
              <van-icon name="arrow" />
            </div>
          </div>
        </div> -->
        <div class="btn-box" v-if="queryType == 1 && isCurrentProcessor">
          <van-button color="#3562DB" v-if="getButton('wf_add_instance')" @click="handleAddInstance" block> {{ getButton("wf_add_instance").name }}</van-button>
          <!-- <van-button color="#3562DB" @click="handleAddInstance" block>{{ getButton("wf_add_instance").name }}</van-button> -->
          <van-button color="#3562DB" v-if="getButton('wf_pass1')" @click="onFormSubmit('pass')" block> {{ getButton("wf_pass1").name }}</van-button>
          <van-button color="#3562DB" v-if="getButton('wf_pass')" @click="onFormSubmit('pass')" block>{{
            detailData.status == "reject" ? "重新提交" : getButton("wf_pass").name
          }}</van-button>
          <van-button color="#3562DB" v-if="getButton('wf_reject')" @click="onFormSubmit('reject')" block> {{ getButton("wf_reject").name }}</van-button>
        </div>
      </van-tab>
      <van-tab title="流转信息">
        <div class="parting-line"></div>
        <van-steps direction="vertical" :active="flows.length" active-color="#3562DB">
          <template v-for="(item, index) in flows">
            <van-step v-if="!['candidate', 'sequenceFlow'].includes(item.historyActivityType)" :key="index">
              <div class="flow-card">
                <p>
                  {{ item.assigneeName }} 在 [{{ item.createTime }}] 开始处理 [{{ item.historyActivityType == "endEvent" ? "结束" : item.historyActivityName || "未命名" }}] 环节
                </p>
                <p v-if="item.historyActivityDurationTime">任务历时 [{{ item.historyActivityDurationTime }}]</p>
                <p v-if="item.attachments && item.attachments.length" class="sign-img">
                  <span>签&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名: </span>
                  <span>
                    <img
                      height="44px"
                      width="60px"
                      :style="{ display: ig.url ? '' : 'none' }"
                      :src="$YBS.imgUrlTranslation(ig.url)"
                      @click="previewImg([$YBS.imgUrlTranslation(ig.url)])"
                      v-for="ig in item.attachments"
                      :key="ig.id"
                      alt=""
                    />
                  </span>
                </p>
                <template v-if="item.comments && item.comments.length > 0">
                  <p v-for="(comment, idx) in item.comments.filter(c => c.action === 'AddComment')" :key="idx">
                    <template v-if="idx < 1 || isExpanded">
                      <p><span v-if="comment.type === 'comment'">审批结果: [通过]</span> <span v-if="comment.type === 'rollbackComment'">审批结果: [驳回]</span></p>
                      <span v-if="commentMap[comment.type]">{{ commentMap[comment.type] }}: [{{ comment.fullMessage }}]</span>
                      <span v-if="comment.time">[{{ comment.time }}]</span>
                    </template>
                  </p>

                  <van-button v-if="item.comments.filter(c => c.action === 'AddComment').length > 1" size="small" type="primary" plain @click="isExpanded = !isExpanded">
                    {{ isExpanded ? "收起" : "展开" }}
                  </van-button>
                </template>

                <p v-if="item.endTime">结束时间: [{{ item.endTime }}]</p>
              </div>
            </van-step>
          </template>
        </van-steps>
      </van-tab>
      <van-tab title="过程管理">
        <div class="parting-line"></div>
        <yx-form
          v-if="
            processFieldsOption && ((processFieldsOption.column && processFieldsOption.column.length > 0) || (processFieldsOption.group && processFieldsOption.group.length > 0))
          "
          v-model="formData"
          :option="processFieldsOption"
        ></yx-form>
        <div v-else style="text-align: center; padding: 10px 16px; background-color: #fff">暂无更多</div>
      </van-tab>
      <van-tab title="作业证">
        <div class="parting-line"></div>
        <div v-if="workCertificate.length" class="fileList">
          <div v-for="(i, index) in workCertificate" :key="index" class="fileItem" @click="clickFile(i)">
            <img src="../../assets/images/construction/file-icon.png" height="40" width="40" alt="" />
            <div class="file-name">{{ i.workName }}</div>
          </div>
        </div>
        <div v-else style="text-align: center; padding: 10px 16px; background-color: #fff">暂无更多</div>
      </van-tab>
      <van-tab title="施工验收">
        <div class="parting-line"></div>
        <yx-form
          v-if="
            acceptanceFheckFieldsOption &&
            ((acceptanceFheckFieldsOption.column && acceptanceFheckFieldsOption.column.length > 0) ||
              (acceptanceFheckFieldsOption.group && acceptanceFheckFieldsOption.group.length > 0))
          "
          v-model="formData"
          :option="acceptanceFheckFieldsOption"
        ></yx-form>
        <div v-else style="text-align: center; padding: 10px 16px; background-color: #fff">暂无更多</div>
      </van-tab>
    </van-tabs>
    <signature v-if="showSignature" @saveImg="saveImg" @close="showSignature = false"></signature>
    <personnel v-if="showPersonnel" :affiliatedPerson="personnelData" @getPersonnel="getPersonnel"></personnel>
    <constructionRequirements
      v-if="['Activity_0i9u4e8', 'Activity_1lnj8tq'].includes(currentNodeId) && queryType == 1 && isCurrentProcessor && detailData.processInstanceId"
      :instanceId="detailData.processInstanceId"
    ></constructionRequirements>
  </page-layout>
</template>

<script>
import PageLayout from "@/components/PageLayout.vue";
import YxForm from "@/common/workFlowForm/form.vue";
import signature from "@/common/signature.vue";
import axios from "axios";
import { filterAvueColumn, validateNull, deepClone } from "@/common/workFlowForm/formFormat.js";
import { mapState } from "vuex";
import { Toast, ImagePreview } from "vant";
import store from "@/store";
import constructionRequirements from "./constructionRequirements.vue";
import personnel from "@/pages/alarmCenter/components/personnel.vue";
export default {
  components: {
    PageLayout,
    YxForm,
    signature,
    constructionRequirements,
    personnel
  },
  data() {
    return {
      replyOpinion: "",
      queryType: 1,
      detailData: {},
      activeTab: 0,
      formOption: {},
      formData: {},
      buttonList: [],
      process: {},
      option: {},
      vars: {},
      form: {},
      flows: [],
      isExpanded: false,
      showSignature: false,
      commentMap: {
        assigneeComment: "变更审核人",
        dispatchComment: "调度",
        transferComment: "转办",
        delegateComment: "委托",
        rollbackComment: "驳回意见",
        terminateComment: "终止意见",
        addMultiInstanceComment: "加签",
        deleteMultiInstanceComment: "减签",
        withdrawComment: "撤销",
        recallComment: "撤回",
        deleteProcessComment: "删除流程",
        comment: "审批意见"
      },
      signatureData: "",
      signatureImage: "",
      isSignature: false,
      processFields: [],
      acceptanceFheckFields: [],
      processFieldsOption: [],
      acceptanceFheckFieldsOption: [],
      workCertificate: [],
      currentNodeId: "",
      personnelInfo: {},
      personnelData: [],
      showPersonnel: false,
      queryProjectCode: "",
      yxForm: null
    };
  },
  computed: {
    ...mapState(["loginInfo"]),
    isCurrentProcessor() {
      if (!this.detailData.currentProcessorIds || !this.loginInfo.staffId) {
        return false;
      }
      const processorIds = this.detailData.currentProcessorIds.split(",");
      return processorIds.includes(this.loginInfo.staffId);
    }
  },

  created() {
    this.queryType = this.$route.query.queryType || 1;
    this.currentNodeId = this.$route.query.currentNodeId || "";
  },
  mounted() {
    this.queryProjectCode = this.$route.query.projectCode;
    this.getDetailData();
    this.$YBS.apiCloudEventKeyBack(this.backFun);
    this.getPersonnelInfo();
  },
  methods: {
    getPersonnel(data) {
      this.personnelData = [];
      this.personnelData = data;
      this.showPersonnel = false;
      let assigneeId = this.personnelData.map(item => item.id).join(",");
      this.$api
        .projectAddSignature({
          projectCode: this.detailData.projectCode,
          comment: this.replyOpinion || "",
          assigneeId: assigneeId,
          copyUserId: assigneeId
        })
        .then(res => {
          console.log("加签", res);
          if (res) {
            this.$toast.success("加签成功");
            if (this.$route.query.fromMsg) {
              api.closeWin();
            } else {
              setTimeout(() => {
                this.$router.go(-1);
              }, 1500);
            }
          } else {
            this.$toast.fail("操作失败");
          }
        });
    },
    handleAddInstance() {
      this.showPersonnel = true;
    },
    getPersonnelInfo() {
      this.$api
        .getPersonnelInfo({
          id: this.loginInfo.staffId
        })
        .then(res => {
          console.log("人员信息", res);
          this.personnelInfo = res;
        });
    },
    backFun() {
      if (this.$route.query.fromMsg) {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    previewImg(images) {
      ImagePreview({
        images: images,
        showIndex: true, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    },
    toPatroSignature() {
      this.showSignature = true;
    },
    saveImg(data) {
      this.signatureImage = data;
      if (data) {
        // base64转流
        let arr = data.split(",");
        let mime = arr[0].match(/:(.*?);/)[1];
        let bstr = atob(arr[1]);
        let n = bstr.length;
        let u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        const file = new File([u8arr], "signature.png", {
          type: mime
        });
        let formData = new FormData();
        formData.append("file", file);
        formData.append("hospitalCode", this.loginInfo.hospitalCode);
        formData.append("unitCode", this.loginInfo.unitCode);
        axios({
          method: "post",
          url: __PATH.SPACE_API + "/lease/upload",
          data: formData,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: localStorage.getItem("token"),
            hospitalSth: localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
          }
        })
          .then(res => {
            if (res.data.code == 200) {
              this.signatureData = res.data.data;
            }
            this.showSignature = false;
          })
          .catch(() => {
            this.$toast.fail("签名保存失败");
            this.showSignature = false;
          });
      }
      this.showSignature = false;
    },
    // 跳转页面
    skipView(val) {
      if (val && val.prop === "construction_unit") {
        this.$router.push({
          path: "/addUnit",
          query: {
            id: val.id,
            type: "edit",
            showIcon: true
          }
        });
      }
      if (val && val.prop === "site_responsible_person") {
        this.$router.push({
          path: "/addPersonnel",
          query: {
            id: val.id,
            type: "edit",
            showIcon: true
          }
        });
      }
    },
    getDetailData() {
      this.$api
        .getApprovalDetail({
          projectCode: this.queryProjectCode
        })
        .then(res => {
          console.log("getDetailData_res", res);
          if (res) {
            this.detailData = res;
            this.detailsTabs("1");
            this.detailsTabs("2");
            this.getStartWorkFlowForm();
            this.getWorkCardDetail();
          }
        });
    },
    getStartWorkFlowForm() {
      this.$api
        .getDetailByWorkFlow({
          processInstanceId: this.detailData.processInstanceId
        })
        .then(res => {
          res && this.getFormOptions(res);
          // this.flows = this.handleResolveFlows(JSON.parse(res).flow);
          this.flows = JSON.parse(res).flow;
          console.log("getDetailByWorkFlow_res", JSON.parse(res));
          console.log("flows", this.flows);
        });
    },
    handleResolveFlows(flow) {
      const flows = [];

      flow.forEach(f => {
        let { assigneeName, createTime, endTime, comments } = f;

        if (/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)) {
          // safari
          createTime = createTime.replace(/-/g, "/");
          endTime = endTime.replace(/-/g, "/");
        }

        const ff = {
          id: f.historyActivityId,
          class: !endTime && f.historyActivityType != "candidate" ? "nodePrimary" : ""
        };
        let tooltip = "";
        if (assigneeName) {
          tooltip = `<span title='${assigneeName}'>${assigneeName}</span><br>`;
          if (createTime) tooltip += `<span title='${createTime}'>${createTime}</span><br>`;

          if (comments && comments.length > 0) {
            let comment;
            const { type, fullMessage } = comments.find(c => c.action == "AddComment") || {};

            if (type == "assigneeComment") {
              comment = "变更审核人：" + fullMessage;
              ff.class = "nodeWarn";
            }
            if (type == "dispatchComment") {
              comment = "调度：" + fullMessage;
              ff.class = "nodeWarn";
            }
            if (type == "transferComment") {
              comment = "转办：" + fullMessage;
              ff.class = "nodeWarn";
            }
            if (type == "delegateComment") {
              comment = "委托：" + fullMessage;
              ff.class = "nodeWarn";
            }
            if (type == "rollbackComment") {
              comment = "驳回：" + fullMessage;
              ff.class = "nodeError";
            }
            if (type == "terminateComment") {
              comment = "终止：" + fullMessage;
              ff.class = "nodeError";
            }
            if (type == "addMultiInstanceComment") {
              comment = "加签：" + fullMessage;
              ff.class = "nodeWarn";
            }
            if (type == "deleteMultiInstanceComment") {
              comment = "减签：" + fullMessage;
              ff.class = "nodeError";
            }
            if (type == "withdrawComment") {
              comment = "撤销：" + fullMessage;
              ff.class = "nodeWarn";
            }
            if (type == "deleteProcessComment") {
              comment = "删除流程：" + fullMessage;
              ff.class = "nodeError";
            }
            if (type == "comment") {
              comment = "审批：" + fullMessage;
              ff.class = "nodeSuccess";
            }
            if (comment) tooltip += `<span title='${comment}'>${comment}</span>`;
          }
          ff.tooltip = tooltip;
        }

        if (f.historyActivityType == "sequenceFlow") ff.class = "lineWarn";
        else if (!ff.class && f.historyActivityType != "candidate") ff.class = "nodeSuccess";

        const index = flows.findIndex(fl => fl.id == f.historyActivityId);
        if (index != -1) flows.splice(index, 1, ff);
        else flows.push(ff);
      });
      return flows;
    },
    getButton(key) {
      return this.buttonList.find(b => b.buttonKey == key);
    },
    getFormOptions(formFormat) {
      const { process, form, button } = JSON.parse(formFormat);
      this.buttonList = button;
      this.process = process;
      const { variables, status } = process;
      const { allForm, taskForm, formList } = form;
      if (!allForm && !taskForm && !formList) {
        this.$toast.fail("获取到表单信息或流程已不存在!");
        return;
      }
      if (allForm) {
        const { option, vars } = this.handleResolveOption(eval("(" + allForm + ")"), taskForm, status);
        option.menuBtn = false;
        for (const key in variables) {
          if (validateNull(variables[key])) delete variables[key];
        }
        option.column.forEach(i => {
          if (i.value && i.value.includes("${") && i.value.includes("}")) {
            const funStr = i.value.replace(/\$\{(.*?)\}/g, "$1");
            i.value = eval("`" + funStr + "`");
            const regex = /flowGenericUtils\(\s*"([^"]+)"\s*,\s*"([^"]+)"\s*\)/;
            const match = funStr.match(regex);
            i.value = this.flowGenericUtils(match[1], match[2]);
          }
        });
        this.option = option;
        this.formOption = option;
        if (this.buttonList.length) {
          if (this.buttonList.some(item => item.buttonKey == "wf_sign")) {
            this.isSignature = true;
          }
        }
        let fieldsOption = deepClone(option);
        if (this.processFields.length) {
          let fieldsColumn = [];
          fieldsOption.column.forEach((item, index) => {
            if (this.processFields.includes(item.prop)) {
              item.display = true;
              item.disabled = true;
              item.readonly = true;
              fieldsColumn.push(item);
            }
          });
          this.processFieldsOption = {
            ...fieldsOption
          };
          this.processFieldsOption.column = fieldsColumn;
        }
        if (this.acceptanceFheckFields.length) {
          let fieldsColumn = [];
          fieldsOption.column.forEach((item, index) => {
            if (this.acceptanceFheckFields.includes(item.prop)) {
              item.display = true;
              item.disabled = true;
              item.readonly = true;
              fieldsColumn.push(item);
            }
          });
          this.acceptanceFheckFieldsOption = {
            ...fieldsOption
          };
          this.acceptanceFheckFieldsOption.column = fieldsColumn;
        }
        this.vars = vars;
      }
      this.form = {
        ...variables
      };
      this.formData = {
        ...variables
      };
    },
    handleResolveOption(option, taskForm, status) {
      let { column, group } = option;
      let vars = [];
      if (taskForm && taskForm.length > 0) {
        const columnFilter = filterAvueColumn(column, taskForm, false, undefined, this);
        column = columnFilter.column;
        vars = columnFilter.vars || [];

        const groupArr = [];
        if (group && group.length > 0) {
          // 处理group
          group.forEach(gro => {
            const groupFilter = filterAvueColumn(gro.column, taskForm, false, undefined, this);
            gro.column = groupFilter.column;
            vars = vars.concat(groupFilter.vars);
            if (gro.column.length > 0) groupArr.push(gro);
          });
        }
        group = groupArr;
      }

      if (status != "todo") {
        // 已办，删除字段默认值
        option.detail = true;
      }
      column.forEach(item => {
        if (item.prop === "construction_unit" || item.prop === "site_responsible_person") {
          item.showIconClick = true;
        } else {
          item.showIconClick = false;
        }
      });
      option.column = column;
      option.group = group;
      return {
        option,
        vars
      };
    },
    onFormSubmit(type) {
      // 处理基础值和显示值
      const formValues = this.$refs.yxForm.form;
      const baseValues = {
        ...formValues
      };
      const displayValues = {};

      // 处理select和picker类型的字段
      this.formOption.column.forEach(column => {
        if (column.type === "select" || column.type === "picker") {
          const fieldValue = baseValues[column.prop];
          const dicData = column.dicData || [];
          const selectedOption = dicData.find(item => item.label === fieldValue);
          if (selectedOption) {
            baseValues[column.prop] = selectedOption.value;
            displayValues[`$${column.prop}`] = selectedOption.name || selectedOption.label;
          }
        }
      });

      // 合并值并转换为需要的格式
      const mergedValues = {
        ...baseValues,
        ...displayValues
      };
      const submitData = Object.entries(mergedValues).map(([key, value]) => ({
        key,
        value
      }));
      // if (!this.signatureData && this.isSignature) {
      //   return this.$toast.fail("请签字");
      // }
      let signatureData = "";
      // if (this.signatureData) {
      //   signatureData = this.signatureData.split("*/*").map(item => {
      //     return {
      //       url: item,
      //       type: "shouqian",
      //       name: item.split("/")[item.split("/").length - 1],
      //       userId: this.loginInfo.staffId
      //     };
      //   });
      // }
      if (type == "pass") {
        signatureData = [
          {
            url: this.personnelInfo.signUrl,
            type: "shouqian",
            name: "canvas.png",
            userId: this.loginInfo.staffId
          }
        ];
      }
      console.log("提交的表单数据:", submitData);
      // 这里可以调用相应的API进行提交
      let projectOperationDto = {
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode,
        isPass: type == "pass" ? 1 : 0,
        projectCode: this.detailData.projectCode,
        comment: this.replyOpinion || "-",
        annexUrl: signatureData ? JSON.stringify(signatureData) : ""
      };
      let params = {
        flowFormList: submitData,
        projectOperationDto
      };
      console.log("params", params);
      this.$api
        .projectHandle(params)
        .then(res => {
          if (res) {
            this.$toast.success("操作成功");
            if (this.$route.query.fromMsg) {
              api.closeWin();
            } else {
              // setTimeout(() => {
              this.$router.go(-1);
              // }, 1500);
            }
          } else {
            this.$toast.fail(res.msg || "操作失败");
          }
        })
        .catch(err => {
          console.log(err);
          this.$toast.fail(err.data.msg || "操作失败");
        });
    },
    getStatusText(status) {
      const statusMap = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
        3: "已超时"
      };
      return statusMap[status] || "未知状态";
    },
    getStatusClass(status) {
      const statusClassMap = {
        0: "not-started",
        1: "in-progress",
        2: "finished",
        3: "overtime"
      };
      return statusClassMap[status] || "unknown";
    },
    detailsTabs(type) {
      let flowTypeValue = "";
      if (this.$route.query.flowType) {
        flowTypeValue = this.$route.query.flowType == "admission_qj" ? "1" : this.$route.query.flowType == "admission_gcwx" ? "2" : "-1";
      } else if (this.detailData && this.detailData.code) {
        flowTypeValue = this.detailData.code == "admission_qj" ? "1" : this.detailData.code == "admission_gcwx" ? "2" : "-1";
      }
      this.$api
        .detailsTabs({
          tabType: type,
          flowType: flowTypeValue
        })
        .then(res => {
          if (type == "1") this.processFields = res;
          if (type == "2") this.acceptanceFheckFields = res;
        });
    },
    getWorkCardDetail() {
      const params = {
        page: 1,
        pageSize: 999,
        instanceId: this.detailData.processInstanceId,
        projectCode: this.detailData.projectCode
      };
      this.$api.workCardDetail(params).then(res => {
        this.workCertificate = res;
      });
    },
    clickFile(i) {
      const fileType = i.workName.slice(i.workName.lastIndexOf(".") + 1, i.workName.length);
      if (fileType.toLowerCase() === "jpg" || fileType.toLowerCase() === "jpeg" || fileType.toLowerCase() === "png") {
        ImagePreview({
          images: [this.$YBS.imgUrlTranslation(i.fileUrl)],
          showIndex: false // 是否显示页码
        });
      } else {
        navigator.clipboard.writeText(i.fileUrl);
        this.$toast("暂不支持预览，已复制链接请粘贴至浏览器下载查看");
      }
    },
    flowGenericUtils(type, format) {
      if (type === "dateFormat") {
        const date = new Date();
        const map = {
          YYYY: date.getFullYear(),
          MM: String(date.getMonth() + 1).padStart(2, "0"),
          DD: String(date.getDate()).padStart(2, "0"),
          HH: String(date.getHours()).padStart(2, "0"),
          mm: String(date.getMinutes()).padStart(2, "0"),
          ss: String(date.getSeconds()).padStart(2, "0")
        };
        return Object.keys(map).reduce((result, key) => result.replace(key, map[key]), format);
      } else if (type === "userInfo") {
        return store.state.loginInfo[format];
      } else if (type === "getTime") {
        return new Date().getTime();
      }
      return "";
    }
  }
};
</script>

<style lang="scss" scoped>
.info-box {
  margin-top: 10px;
  padding: 16px;
  background-color: #fff;
}

.info-item {
  margin-bottom: 16px;
  display: flex;

  .info-label {
    flex-shrink: 0;
    color: #4e5969;
    width: 120px;
  }

  .info-value-wrapper {
    display: flex;
    flex-wrap: wrap;

    .info-value {
      color: #1d2129;
      margin-right: 8px;
    }
  }
}

.parting-line {
  height: 10px;
  background-color: #f2f4f9;
}

.btn-box {
  display: flex;
  flex-wrap: wrap;
  padding: 0 16px;
  padding-bottom: 16px;
  gap: 8px;

  > button {
    flex: 1 0 calc(33.333% - 6px);
    min-width: 100px;

    &:only-child {
      width: 100%;
    }
  }
}

.title-text {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  > span:nth-child(1) {
    font-size: 16px;
    font-weight: bold;
  }

  > div {
    transform: translateX(-8px);
  }
}

.status-tag {
  padding: 0 6px;
  height: 24px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.not-started {
    color: #faad14;
    background: #fff7e6;
  }

  &.in-progress {
    color: #1677ff;
    background: #e6f4ff;
  }

  &.finished {
    color: #52c41a;
    background: #f6ffed;
  }

  &.overtime {
    color: #ff4d4f;
    background: #fff1f0;
  }

  &.unknown {
    color: #8c8c8c;
    background: #f5f5f5;
  }
}

.reply-opinion {
  background-color: #fff;

  .reply-textarea {
    font-size: 16px; // 添加文字大小设置

    :deep(.van-field__label) {
      color: #4e5969;
      width: 120px;
      font-size: 16px; // 标签文字也设置为16px
    }
  }
}

.topItem {
  height: 1.08rem;
  padding: 0px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #1d2129;
  font-size: 0.32rem;
  margin-bottom: 20px;

  .signatured {
    display: flex;
    align-items: center;
  }

  .itemConten {
    margin-left: 10px;
  }

  .notBoder {
    border: none;
  }

  .itemConten {
    font-size: 0.28rem;
    color: #9ca2a9;
  }
}

.sign-img {
  display: flex;

  span {
    flex-shrink: 0;
  }
}

.fileList {
  background: #fff;
  padding: 16px;

  .fileItem {
    position: relative;
    padding: 8px 16px;
    background: #f7f8fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .file-name {
      width: calc(100% - 50px);
      margin-left: 10px;
      color: #1d2129;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .fileItem:last-child {
    margin-bottom: 0;
  }
}

.van-tab__pane {
  overflow: auto;
}
</style>
