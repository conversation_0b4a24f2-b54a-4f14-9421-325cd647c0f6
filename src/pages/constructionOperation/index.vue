<template>
  <page-layout @back="goback">
    <div class="content">
      <div class="card">
        <div class="card-title">作业管理</div>
        <div class="card-item" v-for="item in menuList" :key="item.label" @click="goPage(item.url)">
          <van-badge :content="item.count" max="99">
            <img :src="item.icon" />
          </van-badge>
          <span>{{ item.label }}</span>
        </div>
      </div>
      <div class="card">
        <div class="card-title">作业方管理</div>
        <div class="card-item" v-for="item in menuList2" :key="item.label" @click="goPage(item.url)">
          <van-badge :content="item.count" max="99">
            <img :src="item.icon" />
          </van-badge>
          <span>{{ item.label }}</span>
        </div>
      </div>
      <div class="card">
        <div class="card-title">作业台账</div>
        <div class="card-item" v-for="item in menuList3" :key="item.label" @click="goPage(item.url)">
          <van-badge :content="item.count" max="99">
            <img :src="item.icon" />
          </van-badge>
          <span>{{ item.label }}</span>
        </div>
      </div>
    </div>
  </page-layout>
</template>

<script>
import PageLayout from '@/components/PageLayout.vue'
import { mapState } from "vuex";
import YBS from "@/assets/utils/utils.js";
export default {
  name: "constructionOperation",
  components: {
    PageLayout
  },
  data() {
    return {
      YBS,
      timer: "",
      menuList: [
        {
          icon: require("@/assets/images/construction/icon-5.png"),
          label: "作业申请",
          url: "workOrderApply?assignmentType=0",
          count: "",
          countForKey: "allOrder",
          pathUrl: "/ihcrsYBSApp/orderIndexMyorder"
        },
        {
          icon: require("@/assets/images/construction/icon-1.png"),
          label: "作业证申请",
          url: "workOrderApply?assignmentType=1",
          count: "",
          countForKey: "allTeamPersonOrder",
          pathUrl: "/ihcrsYBSApp/orderIndexWorkbench"
        },
        {
          icon: require("@/assets/images/construction/icon-7.png"),
          label: "审批",
          url: "approvalList",
          count: "",
          countForKey: "acceptanceNum",
          pathUrl: "/ihcrsYBSApp/orderIndexOrderAcceptance"
        },
        {
          icon: require("@/assets/images/construction/icon-6.png"),
          label: "施工巡检",
          url: "safetyPatrol",
          count: "",
          countForKey: "acceptanceNum",
          pathUrl: "/ihcrsYBSApp/orderIndexOrderAcceptance"
        }
      ],
      menuList2: [
        {
          icon: require("@/assets/images/construction/icon-2.png"),
          label: "单位管理",
          url: "unitManagement",
          count: "",
          countForKey: "allOrder",
          pathUrl: "/ihcrsYBSApp/orderIndexMyorder"
        },
        {
          icon: require("@/assets/images/construction/icon-3.png"),
          label: "人员管理",
          url: "personnelManagement",
          count: "",
          countForKey: "allTeamPersonOrder",
          pathUrl: "/ihcrsYBSApp/orderIndexWorkbench"
        }
      ],
      menuList3: [
        {
          icon: require("@/assets/images/construction/icon-4.png"),
          label: "全部作业",
          url: "workAccount",
          count: "",
          countForKey: "allOrder",
          pathUrl: "/ihcrsYBSApp/orderIndexMyorder"
        }
      ]
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  methods: {
    getAppTaskListCount() {
      this.$api.getAppTaskListCount({
        planPersonCode: this.loginInfo.staffId,
        deptId: this.loginInfo.deptId,
        planStatus: 0
      }).then(res => {
        this.menuList[3].count = res || 0
      })
    },
    getList() {
      this.$api
        .getApprovalList({
          page: 1,
          pageSize: 1,
          queryType: 1
        })
        .then(res => {
          this.menuList[2].count = res.total || 0
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handlePopState() {
      window.history.pushState(null, null, window.location.hash);
      this.goback();
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    goPage(url) {
      if (!url) return;
      this.$router.push({ path: url });
    },
    // 权限
    getPermission() {
      if (!this.YBS.hasPermission("storage")) {
        this.YBS.reqPermission(["storage"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            if (!this.YBS.hasPermission("camera")) {
              this.YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                  const btn = document.getElementById('btn')
                  btn.click()
                } else {
                  this.$toast('请打开相机权限否则将无法拍照')
                }
              });
              return;
            }
          }
        });
        return;
      } else {
        if (!this.YBS.hasPermission("camera")) {
          this.YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            } else {
              this.$toast('请打开相机权限否则将无法拍照')
            }
          });
          return;
        }
      }
    }
  },
  mounted() {
    window.history.pushState(null, null, window.location.hash);
    window.addEventListener("popstate", this.handlePopState);
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getList()
    this.getAppTaskListCount()
    this.getPermission()
  },
  beforeDestroy() {
    window.removeEventListener("popstate", this.handlePopState);
  }
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  background-color: #f2f4f9;
  padding: 12px;
  box-sizing: border-box;
}
.card {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  position: relative;
  padding-top: 20px;
}
.card-title {
  position: absolute;
  top: 20px;
  left: 12px;
  width: 100%;
  color: #1d2129;
  font-size: 16px;
  font-weight: bold;
}
.card-item {
  width: 22vw;
  height: 32vw;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: calc((100% - 29vw * 3) / 3 / 2);
  // margin-bottom: 16px;
}
.card-item img {
  width: 40px;
}
.card-item span {
  transform: translateY(12px);
}
</style>
