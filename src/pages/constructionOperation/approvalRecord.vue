<template>
  <page-layout @back="goback" title="审批记录">
    <div class="content">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100" :immediate-check="false">
        <div class="card" v-for="item in unitList" :key="item.id" @click="goDetails(item.projectCode)">
          <div style="position: relative">
            <span class="name-style">{{ item.projectName }}</span>
            <div class="right-content">
              <div :class="['status-tag', getStatusClass(item.projectStatusCode)]">{{ getStatusText(item.projectStatusCode) }}</div>
              <van-icon name="arrow" />
            </div>
          </div>
          <div>
            <span>类型：</span>
            <span>{{ item.businessFormName }}</span>
          </div>
          <div>
            <span>发起人：</span>
            <span>{{ item.currentProcessorNames }}</span>
          </div>
          <div>
            <span>施工单位：</span>
            <span>{{ item.constructionUnitName }}</span>
          </div>
        </div>
      </van-list>
    </div>
  </page-layout>
</template>
  
<script>
import PageLayout from "@/components/PageLayout.vue";
export default {
  name: "approvalList",
  components: {
    PageLayout
  },
  data() {
    return {
      loading: false,
      finished: false,
      unitList: [],
      pageNo: 1,
      pageSize: 10,
      showStartTimePicker: false,
      showEndTimePicker: false,
      startTime: "",
      endTime: "",
      constructionUnitName: "",
      loginInfo: {},
      headerImg: require("@/assets/images/construction/record-icon.png")
    };
  },
  methods: {
    getList() {
      this.$api
        .getApprovalList({
          page: this.pageNo,
          pageSize: this.pageSize,
          queryType: 2
        })
        .then(res => {
          console.log(res);
          this.unitList = this.pageNo === 1 ? res.records : [...this.unitList, ...res.records];
          this.loading = false;
          this.finished = this.unitList.length >= res.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    onLoad() {
      this.pageNo++;
      this.getList();
    },
    getStatusText(status) {
      const statusMap = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
        3: "已超时"
      };
      return statusMap[status] || "未知状态";
    },
    getStatusClass(status) {
      const statusClassMap = {
        0: "not-started",
        1: "in-progress",
        2: "finished",
        3: "overtime"
      };
      return statusClassMap[status] || "unknown";
    },
    goDetails(projectCode) {
      this.$router.push({ path: "/conApprovalDetails", query: { projectCode, queryType: 2 } });
    },
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getList();
  },
  computed: {}
};
</script>
      
  <style lang="scss" scoped>
.content {
  background-color: #f2f4f9;
  height: 100%;
}
.van-list {
  padding: 0 10px;
  padding-top: 12px;
  padding-bottom: 60px;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}
.btn-box {
  display: flex;
  align-items: center;
  position: fixed;
  bottom: 0;
  height: 60px;
  width: 100%;
  display: flex;
  justify-content: center;
  background-color: #fff;
  .van-button--block {
    width: 80%;
  }
}
.card {
  background-color: #fff;
  border-radius: 8px;
  font-size: 16px;
  padding: 12px;
  margin-bottom: 12px;
  padding-top: 16px;
  div > span:nth-child(1) {
    color: #4e5969;
  }
  div > span:nth-child(2) {
    color: #1d2129;
  }
}
.card > div {
  margin-bottom: 12px;
}
.tool-box {
  width: 100%;
  height: 100px;
  background-color: #fff;
  position: sticky;
  top: 0;
  border-bottom: 1px solid #e5e6eb;
  padding: 0 16px;
  padding-top: 12px;
  box-sizing: border-box;
  z-index: 100;
}
.time-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.time-box > div {
  width: 45%;
  height: 35px;
  background-color: #f2f3f5;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1d2129;
  border-radius: 4px;
}
.line {
  position: absolute;
  width: 10px;
  height: 2px;
  top: 50%;
  left: 48%;
  background-color: #c9cdd4;
}
.name-style {
  color: #1d2129 !important;
  font-size: 16px !important;
  font-weight: bold !important;
}
.right-content {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
}
.status-tag {
  padding: 0 6px;
  height: 24px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  &.not-started {
    color: #faad14;
    background: #fff7e6;
  }

  &.in-progress {
    color: #1677ff;
    background: #e6f4ff;
  }

  &.finished {
    color: #52c41a;
    background: #f6ffed;
  }

  &.overtime {
    color: #ff4d4f;
    background: #fff1f0;
  }

  &.unknown {
    color: #8c8c8c;
    background: #f5f5f5;
  }
}
</style>
      