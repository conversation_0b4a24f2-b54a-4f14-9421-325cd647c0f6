<template>
  <page-layout :title="computedTitle" @back="goback">
    <!-- 主要内容区域 -->
    <div class="unit-content" :class="{ 'content-edit': isEdit }">
      <div class="unit-title" v-if="isEdit">
        <div>单位信息</div>
        <van-icon
          name="edit"
          color="#3562DB"
          size="20"
          v-if="!$route.query.showIcon"
          @click="editing = true"
        />
      </div>
      <van-form v-show="currentStep == 1" :class="{ 'unit-form-edit': isEdit }">
        <van-field
          v-model="constructionUnitName"
          label="施工单位名称"
          required
          :readonly="isEdit && !editing"
          :rules="[{ required: true, message: '请输入施工单位名称' }]"
          placeholder="请输入施工单位名称"
        />
        <van-field
          v-model="creditCode"
          label="信用代码"

          :readonly="isEdit && !editing"
          placeholder="请输入信用代码"
          maxlength="18"
          />
        <van-field readonly label="关联部门" :value="deptName" placeholder="请选择部门" @click="showDeptPicker = true" />
        <van-popup v-model="showDeptPicker" position="bottom">
          <search-picker
            v-model="selectedId"
            mode="multiple"
            :columns="deptList"
            :field-props="{
              label: 'deptName',
              value: 'id'
            }"
            @confirm="onDeptConfirm"
            @cancel="showDeptPicker = false"
          />
        </van-popup>
          <!-- :rules="[{ required: true, message: '请输入信用代码' }]" -->
        <van-field name="uploader1" label="营业执照">
          <template #input>
            <van-uploader
              v-model="uploader1"
              :deletable="!(isEdit && !editing)"
              :disabled="isEdit && !editing"
              :max-count="1"
              accept="image/*"
              :after-read="file => afterRead(file, 'businessImage')"
            >
              <img class="normal-img" :src="normalImg" />
            </van-uploader>
          </template>
        </van-field>
        <van-field
          v-model="legalPersonName"
          label="法人姓名"
          :readonly="isEdit && !editing"
          placeholder="请输入法人姓名"
          />
          <!-- :rules="[{ required: true, message: '请输入法人姓名' }]" -->
        <van-field name="uploader2" label="法人身份证" class="idcard-uploader">
          <template #input>
            <van-uploader
              class="idcard-uploader-1"
              v-model="uploader2"
              :deletable="!(isEdit && !editing)"
              :disabled="isEdit && !editing"
              :max-count="1"
              accept="image/*"
              :after-read="file => afterRead(file, 'idCardImageR')"
            >
              <img class="idcard-img" :src="idcard1" />
            </van-uploader>
            <van-uploader
              v-model="uploader3"
              :deletable="!(isEdit && !editing)"
              :disabled="isEdit && !editing"
              :max-count="1"
              accept="image/*"
              :after-read="file => afterRead(file, 'idCardImageS')"
            >
              <img class="idcard-img" :src="idcard2" />
            </van-uploader>
          </template>
        </van-field>
        <van-field name="uploader4" label="施工安全协议">
          <template #input>
            <van-uploader
              v-model="uploader4"
              :deletable="!(isEdit && !editing)"
              :disabled="isEdit && !editing"
              :max-count="9"
              accept="image/*"
              :after-read="file => afterRead(file, 'attachmentInfo')"
            >
              <img class="normal-img" :src="normalImg" />
            </van-uploader>
          </template>
        </van-field>
        <van-field name="uploader5" label="消防安全承诺">
          <template #input>
            <van-uploader
              v-model="uploader5"
              :deletable="!(isEdit && !editing)"
              :disabled="isEdit && !editing"
              :max-count="9"
              accept="image/*"
              :after-read="file => afterRead(file, 'fireControlInfo')"
            >
              <img class="normal-img" :src="normalImg" />
            </van-uploader>
          </template>
        </van-field>
      </van-form>
      <div class="unit-title" v-if="isEdit">
        <div>人员信息</div>
      </div>
      <div class="add-person-box" :class="{ 'add-person-box-edit': isEdit }" v-show="currentStep == 2 || isEdit">
        <div class="add-person-item" :class="{ 'add-person-item-edit': isEdit }" v-for="(item, index) in personList" :key="index">
          <van-field v-model="item.personName" label="姓名" :readonly="isEdit && !editing" placeholder="请输入姓名" />
          <van-field name="radio" label="性别">
            <template #input>
              <van-radio-group v-model="item.sex" direction="horizontal" :disabled="isEdit && !editing">
                <van-radio name="1">男</van-radio>
                <van-radio name="2">女</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <!-- 手机号 -->
          <van-field
            v-model="item.phone"
            label="手机号"
            @input="value => handlePhoneInput(value, index)"
            placeholder="请输入手机号"
            maxlength="11"
            :readonly="isEdit && !editing"
          />
          <!-- 身份证 -->
          <van-field v-model="item.idCard" label="身份证号" placeholder="请输入身份证号" maxlength="18" :readonly="isEdit && !editing" />
          <!-- 岗位 -->
          <van-field v-model="item.position" label="岗位" placeholder="请输入岗位" :readonly="isEdit && !editing" />
          <!-- 资质证书 -->
          <van-field name="uploader6" label="资质证书">
            <template #input>
              <van-uploader
                v-model="item.uploader"
                :deletable="!(isEdit && !editing)"
                :disabled="isEdit && !editing"
                :max-count="9"
                accept="image/*"
                :after-read="file => afterRead(file, 'personnelCertificate', index)"
              >
                <img class="normal-img" :src="normalImg" />
              </van-uploader>
            </template>
          </van-field>
          <div class="delete-icon" v-if="!isEdit">
            <van-icon name="delete-o" color="#F54545" size="20" @click="deletePerson(index)" />
          </div>
        </div>
        <div class="add-btn-box" @click="addPerson" v-if="!isEdit">
          <van-icon name="plus" />
          <span style="margin-left: 8px">新增一个员工信息</span>
        </div>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <template #footer v-if="!isEdit || editing">
      <div class="footer-box" v-if="!isEdit">
        <template v-if="currentStep == 1">
          <van-button type="default" color="#E6EFFC" @click="goToAddPerson">添加人员</van-button>
          <van-button type="primary" @click="handleSubmit" color="#3562DB">完成</van-button>
        </template>
        <template v-if="currentStep == 2">
          <van-button type="default" color="#E6EFFC" @click="currentStep = 1">上一步</van-button>
          <van-button type="primary" @click="handleSubmit2" color="#3562DB">提交</van-button>
        </template>
      </div>
      <div class="footer-box" :class="{ 'footer-box-edit': isEdit }" v-if="editing">
        <van-button type="primary" @click="handleEdit" color="#3562DB">修改</van-button>
      </div>
    </template>
  </page-layout>
</template>

<script>
import PageLayout from '@/components/PageLayout.vue'
import axios from "axios";
import SearchPicker from '@/components/SearchPicker.vue'

export default {
  components: {
    PageLayout,
    SearchPicker
  },
  computed: {
    computedTitle() {
      if (!this.isEdit) return '新增单位'
      if (this.editing) return '编辑单位'
      return '单位详情'
    }
  },
  data() {
    return {
      editing: false,
      isEdit: false,
      constructionUnitName: "",
      creditCode: "",
      legalPersonName: "",
      uploader1: [],
      uploader2: [],
      uploader3: [],
      uploader4: [],
      uploader5: [],
      idcard1: require("@/assets/images/construction/id-card1.png"),
      idcard2: require("@/assets/images/construction/id-card2.png"),
      normalImg: require("@/assets/images/construction/normal.png"),
      businessImage: "",
      idCardImageR: "",
      idCardImageS: "",
      attachmentInfo: [],
      fireControlInfo: [],
      currentStep: 1,
      personList: [
        {
          personName: "",
          sex: "",
          phone: "",
          idCard: "",
          position: "",
          uploader: [],
          imgUrl: ""
        }
      ],
      deptName: '',
      deptId: '',
      showDeptPicker: false,
      deptList: [],
      selectedId: []
    };
  },
  created() {
    if (this.$route.query.type == "edit") {
      this.isEdit = true;
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if (this.isEdit) {
      this.$api.getConstructionUnitDetail({ id: this.$route.query.id }).then(res => {
        console.log(res);
        this.constructionUnitName = res.companyName;
        this.creditCode = res.creditCode;
        this.legalPersonName = res.legalPersonName;
        this.businessImage = res.businessImage;
        this.idCardImageR = res.idCardImageR;
        this.idCardImageS = res.idCardImageS;
        this.attachmentInfo = JSON.parse(res.attachmentInfo);
        this.fireControlInfo = JSON.parse(res.fireControlInfo);
        // 添加图片回显
        if (res.businessImage) {
          this.uploader1 = [{ url: this.$YBS.imgUrlTranslation(res.businessImage) }];
        }
        if (res.idCardImageR) {
          this.uploader2 = [{ url: this.$YBS.imgUrlTranslation(res.idCardImageR) }];
        }
        if (res.idCardImageS) {
          this.uploader3 = [{ url: this.$YBS.imgUrlTranslation(res.idCardImageS) }];
        }
        // 施工安全协议
        if (this.attachmentInfo.length) {
          this.uploader4 = this.attachmentInfo.map(item => ({ url: this.$YBS.imgUrlTranslation(item.url) }));
        }
        // 消防安全承诺
        if (this.fireControlInfo.length) {
          this.uploader5 = this.fireControlInfo.map(item => ({ url: this.$YBS.imgUrlTranslation(item.url) }));
        }
        // 人员信息回显
        if (res.staffList && res.staffList.length) {
          this.personList = res.staffList.map(staff => ({
            personName: staff.name,
            sex: staff.sex,
            phone: staff.phone,
            idCard: staff.idCard,
            position: staff.positionName,
            uploader: staff.certificationImage ? [{ url: this.$YBS.imgUrlTranslation(staff.certificationImage) }] : [],
            imgUrl: staff.certificationImage
          }));
        }
      });
    } else {
      this.$api.getApplyDept().then(res => {
        this.deptList = res;
      });
    }
  },
  methods: {
    goToAddPerson() {
      // 添加表单验证
      if (!this.constructionUnitName) {
        this.$toast("请输入施工单位名称");
        return;
      }
      // if (!this.creditCode) {
      //   this.$toast("请输入信用代码");
      //   return;
      // }
      // if (!this.legalPersonName) {
      //   this.$toast("请输入法人姓名");
      //   return;
      // }
      // if (!this.businessImage) {
      //   this.$toast("请上传营业执照");
      //   return;
      // }
      // if (!this.idCardImageR) {
      //   this.$toast("请上传法人身份证正面");
      //   return;
      // }
      // if (!this.idCardImageS) {
      //   this.$toast("请上传法人身份证反面");
      //   return;
      // }
      // if (!this.attachmentInfo.length) {
      //   this.$toast("请上传施工安全协议");
      //   return;
      // }
      // if (!this.fireControlInfo.length) {
      //   this.$toast("请上传消防安全承诺");
      //   return;
      // }
      this.currentStep = 2;
    },
    handleEdit() {
      // 构建人员列表数据
      let staffList = this.personList.map(person => {
        const { personName: name, position: positionName, imgUrl: certificationImage, uploader, ...rest } = person;
        return { name, positionName, certificationImage, ...rest };
      });

      // 构建请求参数
      let params = {
        id: this.$route.query.id, // 添加施工单位ID
        companyName: this.constructionUnitName,
        creditCode: this.creditCode,
        legalPersonName: this.legalPersonName,
        businessImage: this.businessImage,
        idCardImageR: this.idCardImageR,
        idCardImageS: this.idCardImageS,
        attachmentInfo: JSON.stringify(this.attachmentInfo),
        fireControlInfo: JSON.stringify(this.fireControlInfo),
        staffList: staffList
      };

      // 调用编辑接口
      this.$api
        .updateConstructionUnit(params)
        .then(res => {
          this.editing = false; // 关闭编辑状态
          this.$toast({
            message: "修改成功！",
            onClose: () => {
              this.$router.go(0); // toast结束后刷新页面
            }
          });
        })
        .catch(() => {
          this.$toast("修改失败！");
        });
    },
    handlePhoneInput(value, index) {
      // 只允许输入数字
      this.personList[index].phone = value.replace(/\D/g, "");
    },
    deletePerson(index) {
      if (this.personList.length == 1) {
        this.$toast("至少需要添加一个员工信息");
        return;
      }
      this.personList.splice(index, 1);
    },
    addPerson() {
      this.personList.push({
        personName: "",
        sex: "",
        phone: "",
        idCard: "",
        position: "",
        uploader: [],
        imgUrl: ""
      });
    },
    handleSubmit2() {
      //添加人员信息校验
      for (let i = 0; i < this.personList.length; i++) {
        const person = this.personList[i];
        if (!person.personName) {
          this.$toast(`请输入第${i + 1}个员工的姓名`);
          return;
        }
        if (!person.sex) {
          this.$toast(`请选择第${i + 1}个员工的性别`);
          return;
        }
        if (!person.phone) {
          this.$toast(`请输入第${i + 1}个员工的手机号`);
          return;
        }
        if (!/^1[3-9]\d{9}$/.test(person.phone)) {
          this.$toast(`第${i + 1}个员工的手机号格式不正确`);
          return;
        }
        if (!person.idCard) {
          this.$toast(`请输入第${i + 1}个员工的身份证号`);
          return;
        }
        if (!person.position) {
          this.$toast(`请输入第${i + 1}个员工的岗位`);
          return;
        }
        if (!person.imgUrl) {
          this.$toast(`请上传第${i + 1}个员工的资质证书`);
          return;
        }
      }
      let staffList = this.personList.map(person => {
        const { personName: name, position: positionName, imgUrl: certificationImage, uploader, ...rest } = person;
        return { name, positionName, certificationImage, ...rest };
      });
      let params = {
        companyName: this.constructionUnitName,
        creditCode: this.creditCode,
        legalPersonName: this.legalPersonName,
        businessImage: this.businessImage,
        idCardImageR: this.idCardImageR,
        idCardImageS: this.idCardImageS,
        attachmentInfo: JSON.stringify(this.attachmentInfo),
        fireControlInfo: JSON.stringify(this.fireControlInfo),
        staffList: staffList
      };
      this.$api
        .addConstructionUnit(params)
        .then(res => {
          this.$toast("提交成功！");
          this.$router.go(-1);
        })
        .catch(() => {
          this.$toast("提交失败！");
        });
    },
    handleSubmit() {
      // 校验
      if (!this.constructionUnitName) {
        this.$toast("请输入施工单位名称");
        return;
      }
      // if (!this.creditCode) {
      //   this.$toast("请输入信用代码");
      //   return;
      // }
      // if (!this.legalPersonName) {
      //   this.$toast("请输入法人姓名");
      //   return;
      // }
      // if (!this.businessImage) {
      //   this.$toast("请上传营业执照");
      //   return;
      // }
      // if (!this.idCardImageR) {
      //   this.$toast("请上传法人身份证正面");
      //   return;
      // }
      // if (!this.idCardImageS) {
      //   this.$toast("请上传法人身份证反面");
      //   return;
      // }
      // if (!this.attachmentInfo.length) {
      //   this.$toast("请上传施工安全协议");
      //   return;
      // }
      // if (!this.fireControlInfo.length) {
      //   this.$toast("请上传消防安全承诺");
      //   return;
      // }
      let params = {
        companyName: this.constructionUnitName,
        creditCode: this.creditCode,
        legalPersonName: this.legalPersonName,
        businessImage: this.businessImage,
        idCardImageR: this.idCardImageR,
        idCardImageS: this.idCardImageS,
        deptId: this.deptId,
        deptName: this.deptName,
        attachmentInfo: JSON.stringify(this.attachmentInfo),
        fireControlInfo: JSON.stringify(this.fireControlInfo)
      };
      this.$api
        .addConstructionUnit(params)
        .then(res => {
          this.$toast("提交成功！");
          this.$router.go(-1);
        })
        .catch(() => {
          this.$toast("提交失败！");
        });
    },
    afterRead(file, type, index) {
      console.log(file.file);
      const fromData = new FormData();
      fromData.append("file", file.file);
      axios
        .post(__PATH.SPACE_API + "/SecurityLedger/upload", fromData, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
            hospitalSth: localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
          }
        })
        .then(res => {
          console.log(res);
          if (res.data.code == 200) {
            const attachmentObj = {
              url: res.data.data.picUrl,
              attachment: res.data.data.name,
              createBy: JSON.parse(localStorage.getItem("loginInfo")).staffName,
              createDate: this.formatDate(new Date())
            };
            switch (type) {
              case "businessImage":
                this.businessImage = res.data.data.picUrl;
                break;
              case "idCardImageR":
                this.idCardImageR = res.data.data.picUrl;
                break;
              case "idCardImageS":
                this.idCardImageS = res.data.data.picUrl;
                break;
              case "attachmentInfo":
                this.attachmentInfo.push(attachmentObj);
                break;
              case "fireControlInfo":
                this.fireControlInfo.push(attachmentObj);
                break;
              case "personnelCertificate":
                this.personList[index].imgUrl = res.data.data.picUrl;
                break;
            }
          }
        });
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    onDeptConfirm({value, item}) {
      const checkItem = item
      if (checkItem.length) {
        this.deptName = Array.from(checkItem, (item) => item.deptName).join(',');
        this.selectedId = Array.from(checkItem, (item) => item.id);
        this.deptId = this.selectedId.join(',');
      }
      this.showDeptPicker = false;
    },
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.unit-content {
  // 移除之前的固定高度和padding-bottom
  background-color: #fff;

  &.content-edit {
    background-color: #f2f4f9;
  }
}

.footer-box {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  box-sizing: border-box;

  > button {
    width: 45%;

    &:nth-child(1) {
      color: #3562db !important;
    }
  }
}

.footer-box-edit {
  > button:nth-child(1) {
    width: 100%;
    color: #fff !important;
  }
}

.add-person-box {
  background-color: #f2f4f9;
  padding: 10px;
  // min-height: calc(100vh - 60px);
}
.add-person-box-edit {
  padding: 0;
}
.add-person-item {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}
.idcard-img {
  width: 50vw;
  height: 32vw;
}
/deep/ .idcard-uploader .van-uploader__preview-image {
  width: 50vw !important;
  height: 32vw !important;
}
/deep/ .idcard-uploader .van-field__control--custom {
  flex-wrap: wrap;
}
.idcard-uploader-1 {
  margin-bottom: 10px;
}
.normal-img {
  width: 80px;
}
.delete-icon {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10px;
}
.add-btn-box {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 10px;
  color: #3562db;
}
.unit-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 45px;
  padding: 0 16px;
  background-color: #fff;
  > div {
    color: #1d2129;
    font-size: 18px;
    position: relative;
    padding-left: 10px;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 18px;
      background-color: #3562db;
    }
  }
}
.unit-form-edit {
  margin-bottom: 10px;
}
.add-person-item-edit {
  border-radius: 0;
}
</style>
