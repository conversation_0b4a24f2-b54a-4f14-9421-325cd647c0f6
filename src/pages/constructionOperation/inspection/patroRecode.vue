<template>
  <div class="inner-container">
    <Header title="巡查记录" @backFun="goback"></Header>
    <div class="inner">
      <div class="recode">
        <van-dropdown-menu active-color="#3562DB" :close-on-click-outside="false">
          <van-dropdown-item title="筛选" ref="item">
            <van-cell center title="作业名称">
              <van-field v-model="filterData.workName" slot="default" placeholder="请输入" />
            </van-cell>
            <van-cell center title="类型">
              <van-field v-model="filterData.workType" slot="default" readonly placeholder="请选择" right-icon="arrow" @click="workTypeShow = true" />
            </van-cell>
            <!-- <van-cell center title="申请施工科室">
              <van-field v-model="filterData.dept" slot="default" readonly placeholder="请选择（支持查询）" right-icon="arrow" @click="deptShow = true" />
            </van-cell> -->
            <div class="filterBtn">
              <van-button color="#E6EFFC" @click="resetFilter">重置</van-button>
              <van-button color="#3562DB" @click="onConfirm">确认</van-button>
            </div>
          </van-dropdown-item>
        </van-dropdown-menu>
      </div>
      <div class="innerContent">
        <van-pull-refresh v-if="listData.length > 0" v-model="isLoading" @refresh="onRefresh">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <div v-for="(i, ind) in listData" :key="ind" class="list" :class="ind == listData.length - 1 ? 'lastList' : ''" @click="goTaskList(i)">
              <div class="titleWrap">
                <span class="task-name">{{ i.workName || "" }}</span>
                <span class="typeWarp">
                  <span class="taskType" :class="'taskType' + i.projectStatusCode">{{ i.projectStatus }}</span>
                  <van-icon name="arrow" />
                </span>
              </div>
              <div class="itemWrap">
                <span class="itemTitle">作业类型</span>
                <span class="itemContent">{{ i.workTypeName || "" }}</span>
              </div>
              <div class="itemWrap">
                <span class="itemTitle">施工地点</span>
                <span class="location">
                  <span v-if="i.workLocalType" class="locationType">{{ i.workLocalType == "0" ? "室内" : "室外" }}</span>
                  <span class="locationDetail">{{ i.workLocationName || "" }}</span>
                </span>
              </div>
              <div class="itemWrap">
                <div class="optionItem">
                  <span>周期类型</span>
                  <span>{{ cycleTypeFormat(i.cycleType) }}</span>
                </div>
                <div class="optionItem">
                  <span style="text-align: center">频次</span>
                  <span>{{ i.frequency || "-" }}次</span>
                </div>
              </div>
              <!-- <div class="itemWrap">
                <span class="itemTitle">申请施工科室</span>
                <span class="itemContent">{{ i.workDeptName || "" }}</span>
              </div> -->
              <div class="itemWrap">
                <span class="itemTitle">施工单位</span>
                <span class="itemContent">{{ i.workCompanyName || "" }}</span>
              </div>
              <div class="statisticsOperation">
                <div class="statistics">
                  <span class="molecule">已巡 /</span>
                  <span class="denominator">应巡</span>
                  <span class="molecule">{{ i.alreadyMaintainNum }} /</span>
                  <span class="denominator">{{ i.shouldMaintainNum }}</span>
                </div>
                <div class="operation" :class="'operation' + i.planStatus">
                  <span>{{ i.planStatus == "1" ? "已完成" : moment().unix() > i.workEndTime  ? "已超期" :  "未完成" }}</span>
                  <!-- <van-button v-if="i.planStatus != '1'" type="primary" color="#3562DB">去巡查</van-button> -->
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
        <div v-else class="notList">
          <div class="emptyImg">
            <span class="emptyText">暂无数据</span>
          </div>
        </div>
      </div>
      <!-- 作业类型 -->
      <van-popup v-model="workTypeShow" position="bottom">
        <van-picker
          v-model="filterData.workType"
          show-toolbar
          title="选择作业类型"
          :columns="workTypes.map(i => i.name)"
          @confirm="confirmWorkType"
          @cancel="workTypeShow = false"
        />
      </van-popup>
      <!-- 申请科室 -->
      <van-popup v-model="deptShow" position="bottom">
        <van-picker v-model="filterData.dept" show-toolbar title="选择申请科室" :columns="deptArr.map(i => i.deptName)" @confirm="confirmDept" @cancel="deptShow = false" />
      </van-popup>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { Toast } from "vant";
export default {
  components: {},
  data() {
    return {
      moment,
      loginInfo: {},
      listData: [],
      loading: false,
      finished: false,
      isLoading: false,
      value: 0,
      switch1: false,
      switch2: false,
      option: [
        { text: "全部商品", value: 0 },
        { text: "新款商品", value: 1 },
        { text: "活动商品", value: 2 }
      ],
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      filterData: {
        workName: "",
        workType: "",
        dept: ""
      },
      listData: [],
      typeOptions: [
        {
          cycleType: 8,
          label: "单次"
        },
        {
          cycleType: 6,
          label: "每日"
        },
        {
          cycleType: 0,
          label: "每周"
        },
        {
          cycleType: 2,
          label: "每月"
        },
        {
          cycleType: 3,
          label: "季度"
        },
        {
          cycleType: 5,
          label: "全年"
        }
      ],
      workTypes: [],
      workTypeShow: false,
      currentWorkType: "",
      deptArr: [],
      deptShow: false,
      currentDept: ""
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.getListData();
    this.getWorkTypes();
    this.getApplyDeptData();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    onLoad() {
      this.pageParmes.pageNo++;
      this.loading = true;
      this.getListData();
    },
    onRefresh() {
      this.pageParmes = {
        pageNo: 1,
        pageSize: 10
      };
      this.listData = [];
      this.getListData();
    },
    getListData() {
      const params = {
        currentPage: this.pageParmes.pageNo,
        size: this.pageParmes.pageSize,
        workName: this.filterData.workName,
        planStatus: 1 // 已完成
      };
      if (this.filterData.workType) {
        console.log("11");
        params.workType = this.workTypes.find(i => i.name == this.filterData.workType).id;
      }
      if (this.filterData.dept) {
        console.log("22");
        params.deptId = this.deptArr.find(i => i.deptName == this.filterData.dept).deptCode;
      }
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.getPatroList(params).then(res => {
        this.listData = this.listData.concat(res.list);
        Toast.clear();
        this.isLoading = false;
        if (!res.list || res.list.length === 0 || this.listData.length >= res.count) {
          this.finished = true;
        } else {
          this.finished = false;
        }
        this.loading = false;
      });
    },
    goTaskList(item) {
      this.$router.push({
        path: "/safetyPatrol",
        query: {
          id: item.id,
          status: item.planStatus,
          type: "recode"
        }
      });
    },
    formatType(type) {
      // 作业状态 0:未开始 1:已完成 2:进行中 3:已结束 4:已超时
      if (type == "0") return "未开始";
      if (type == "1") return "已完成";
      if (type == "2") return "进行中";
      if (type == "3") return "已结束";
      if (type == "4") return "已超时";
    },
    cycleTypeFormat(type) {
      let name = "-";
      this.typeOptions.find(i => {
        if (i.cycleType == type) {
          name = i.label;
        }
      });
      return name;
    },
    // 获取作业类型
    getWorkTypes() {
      this.$api.getWorkTypes({ assignmentType: "0" }).then(res => {
        this.workTypes = res;
      });
    },
    confirmWorkType(val) {
      this.filterData.workType = val;
      this.workTypeShow = false;
    },
    // 获取申请科室
    getApplyDeptData() {
      const params = {
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getApplyDept(params).then(res => {
        this.deptArr = res;
      });
    },
    confirmDept(val) {
      this.filterData.dept = val;
      this.deptShow = false;
    },
    onConfirm() {
      this.onRefresh();
    },
    resetFilter() {
      this.filterData = {
        workName: "",
        workType: "",
        dept: ""
      };
      this.onRefresh();
    },
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.inner-container {
  height: 100%;
}
.inner {
  position: relative;
  padding-top: 40px;
  background-color: #f2f4f9;
  height: calc(100% - 87.5px);
  box-sizing: border-box;
  font-size: 16px;
  .recode {
    position: absolute;
    top: 10px;
    right: 16px;
    font-size: 14px;
    color: #3562db;
    /deep/ .van-dropdown-menu {
      .van-dropdown-menu__bar {
        height: 20px;
        background-color: unset;
        box-shadow: none;
      }
      .van-dropdown-item {
        .van-cell--center {
          .van-cell__title {
            width: 100px;
            flex: none;
            font-weight: 600;
          }
          > .van-cell__value {
            width: calc(100% - 100px);
          }
        }
        .filterBtn {
          display: flex;
          padding: 5px 16px;
          .van-button {
            flex: 1;
          }
          .van-button:first-child {
            margin-right: 5px;
            color: #3562db !important;
          }
          .van-button:last-child {
            margin-left: 5px;
          }
        }
      }
    }
  }
  .innerContent {
    padding: 0 10px;
    height: 100%;
    overflow: auto;
    .list {
      padding: 18px 16px;
      border-radius: 8px;
      background-color: #fff;
      margin-bottom: 10px;
      .titleWrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .task-name {
          color: #1d2129;
          font-weight: 600;
        }
        .typeWarp {
          .taskType {
            padding: 2px 6px;
            font-size: 14px;
          }
          .taskType0 {
            background-color: #ffece8;
            color: #f53f3f;
          }
          .taskType2 {
            background-color: rgba($color: #61e29d, $alpha: 0.2);
            color: #61e29d;
          }
          .taskType1 {
            background-color: #e6effc;
            color: #3562db;
          }
          .taskType3 {
            background-color: #fff7e8;
            color: #ff7d00;
          }
          .taskType99 {
            background-color: rgba($color: #9be2ff, $alpha: 0.2);
            color: #909399;
          }
        }
      }
      .itemWrap {
        display: flex;
        align-items: baseline;
        margin-top: 16px;
        .itemTitle {
          width: 110px;
          color: #4e5969;
        }
        .itemContent {
          width: calc(100% - 110px);
          color: #1d2129;
        }
        .location {
          display: flex;
          align-items: baseline;
          width: calc(100% - 110px);
          .locationType {
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 14px;
            background-color: #f2f3f5;
            color: #4e5969;
            font-weight: 600;
          }
          .locationDetail {
            width: calc(100% - 40px);
          }
        }
        .optionItem {
          display: flex;
          span: {
            width: 50%;
          }
          span:first-child {
            width: 110px;
            color: #4e5969;
          }
          span:last-child {
            width: calc(100% - 110px);
            color: #1d2129;
          }
        }
      }
      .statisticsOperation {
        font-size: 14px;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #e5e6eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .statistics {
          .molecule {
            color: #4e5969;
          }
          .denominator {
            color: #9ca2a9;
            margin-right: 20px;
          }
        }
        .operation {
          .van-button {
            height: 28px;
            margin-left: 8px;
          }
        }
        .operation0,
        .operation2 {
          color: #f53f3f;
        }
        .operation1 {
          color: #61e29d;
        }
      }
    }
    .lastList {
      margin: 0 !important;
    }
    .notList {
      width: 100%;
      position: relative;
      height: 70vh;
      .emptyImg {
        position: absolute;
        height: 100%;
        width: 50%;
        left: 25%;
        background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
        background-size: 100% auto;
        .emptyText {
          position: absolute;
          width: 100%;
          text-align: center;
          bottom: 40%;
        }
      }
    }
  }
}
</style>