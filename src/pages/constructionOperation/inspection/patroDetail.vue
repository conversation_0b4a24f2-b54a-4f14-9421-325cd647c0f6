<template>
  <div id="taskConten-inner" class='inner'>
    <Header title="巡检详情" @backFun="goBack"></Header>
    <!-- 查看详情 -->
    <template v-if="status == '1' || $route.query.type == 'recode'">
      <div class="baseInfo">
        <div class="titleItem">
          <div class="lineBar"></div>
          <span>基本信息</span>
        </div>
        <!-- <div class="contentItem">
          <span>申请科室</span>
          <span>{{ pointRelease.departmentName || '' }}</span>
        </div> -->
        <div class="contentItem">
          <span>周期类型</span>
          <span>{{ cycleTypeFormat(pointRelease.cycleType) }}</span>
        </div>
        <div class="contentItem" v-if="pointRelease.cycleType != 8">
          <span>频次</span>
          <span>{{ pointRelease.frequency || '' }}次</span>
        </div>
        <div class="contentItem">
          <span>检查时间</span>
          <span>{{ moment(pointRelease.actualExecutionTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </div>
        <div class="contentItem">
          <span>检查部门</span>
          <span>{{ pointRelease.actualExecutionDeptName || '' }}</span>
        </div>
        <div class="contentItem">
          <span>开启定位</span>
          <span>否</span>
        </div>
      </div>
      <div class="baseInfo">
        <div class="titleItem">
          <div class="lineBar"></div>
          <span>巡检内容</span>
        </div>
        <!-- 日常 -->
        <van-collapse v-if="type == '0'" v-model="activeNames" :border="false">
          <van-collapse-item
            :is-link="false"
            title=""
            :name="index"
            v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
            :key="item.id"
            disabled
          >
            <template #title>
              <div class="inspectionConten">
                <span class="project">第{{ index + 1 }}项</span>
                <span class="line"></span>
                <span class="project">{{ item.detailName }}</span>
                <span v-if="item.inspectionBasis!=''&&item.inspectionBasis!=null" class="basisSpanClass" @click.stop="basisClick(item.inspectionBasis)">依据</span>
              </div>
            </template>
            <div class="daily-items">
              <div class="item-conten">{{item.standardRequirements}}</div>
            </div>
            <!-- <div class="daily-items basisSpanClassCopy">
              <div class="item-conten">{{item.inspectionBasis}}</div>
                 <span v-if="item.inspectionBasis!=''||item.inspectionBasis!=null" class="basisSpanClass"  @click.stop="basisClick(item.inspectionBasis)">依据</span>
            </div> -->
            <div class="quality-check">
              <van-radio-group v-model="item.maintainProjectdetailsTermReleaseList[0].normal" direction="horizontal" disabled>
                <van-radio name="2" checked-color="#3562DB" icon-size="16px">合格</van-radio>
                <van-radio name="3" checked-color="#3562DB" icon-size="16px">不合格</van-radio>
              </van-radio-group>
            </div>
            <div class="photo-proof" v-if="item.maintainProjectdetailsTermReleaseList[0].pictureUrl">
              <div class="photo-proof-title">上传照片证明</div>
              <div class="sumUpContent">
                <img
                  v-for="(imgUrl, idx) in item.maintainProjectdetailsTermReleaseList[0].pictureUrl.split(',')"
                  :key="idx"
                  height="80px"
                  :src="$YBS.imgUrlTranslation(imgUrl)"
                  style="margin-right: 8px; margin-bottom: 8px;"
                  v-preview="$YBS.imgUrlTranslation(imgUrl)"
                />
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
        <!-- 专业 -->
        <van-collapse v-if="type == '1'" v-model="activeNames" :border="false">
          <van-collapse-item
            :is-link="false"
            title=""
            v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
            :key="item.id"
            :name="index"
            disabled
          >
            <template #title>
              <div class="inspectionConten">
                <span class="project">第{{ index + 1 }}项</span>
                <span class="line"></span>
                <span class="project">{{ item.detailName }}</span>
                <span v-if="item.inspectionBasis!=''&&item.inspectionBasis!=null" class="basisSpanClass" @click.stop="basisClick(item.inspectionBasis)">依据</span>
              </div>
            </template>
            <div
              v-for="(item2, inde) in item.maintainProjectdetailsTermReleaseList"
              :key="item2.id"
            >
              <div class="gist type1" v-if="item2.isNum == '1'">
                <span
                  >{{ inde + 1 }}. 检查要点：{{
                    item2.content ? item2.content : ""
                  }}</span
                > 
              </div>
              <div class="gist type2" v-if="item2.isNum == '2'">
                <span
                  >{{ inde + 1 }}. 检查要点：{{
                    item2.content ? item2.content : ""
                  }}</span
                > 
                <van-field
                  v-model="item2.contentStandard"
                  readonly
                  label=""
                  rows="1"
                  type="textarea"
                  autosize
                />
              </div>
              <div class="gist type3" v-if="item2.isNum == '0'">
                <span
                  >{{ inde + 1 }}. 检查要点：{{
                    item2.content
                      ? item2.content +
                        "(" +
                        item2.rangeStart +
                        "-" +
                        item2.rangeEnd +
                        (item2.einheitName ? item2.einheitName : '') +
                        ")"
                      : ""
                  }}</span
                > 
                <van-field
                  v-model="item2.contentStandard"
                  label=""
                  readonly
                  type="number"
                  autosize
                >
                  <div slot="extra" v-if="item2.outRange" style="color:red">输入范围异常</div>
                </van-field>
              </div>
              <div class="gist type4" v-if="item2.isNum == '3'">
                <span
                  >{{ inde + 1 }}. 检查要点：{{
                    item2.content ? item2.content : ""
                  }}</span
                > 
                <van-radio-group v-model="item2.contentStandard">
                  <van-radio
                    v-for="item3 in item2.termJsonArray"
                    :key="item3.conText"
                    :name="item3.contText"
                    checked-color="#3562DB"
                    icon-size="16px"
                    disabled
                    >{{ item3.contText }}</van-radio
                  >
                </van-radio-group>
              </div>
              
              <div class="photo-proof" v-if="item2.pictureUrl">
                <div class="photo-proof-title">上传照片证明</div>
                <div class="sumUpContent">
                  <img
                    v-for="(imgUrl, idx) in item2.pictureUrl.split(',')"
                    :key="idx"
                    v-preview="$YBS.imgUrlTranslation(imgUrl)"
                    height="80px"
                    :src="$YBS.imgUrlTranslation(imgUrl)"
                    style="margin-right: 8px; margin-bottom: 8px;"
                  />
                </div>
              </div>
            </div>
            <div class="step-line"></div>
          </van-collapse-item>
        </van-collapse>
        <div class="sumUp">
          <div class="sumUpTitle">检查结果总结</div>
          <div class="sumUpContent">{{ result.desc }}</div>
          <div class="sumUpTitle">现场照片和视频</div>
          <div class="sumUpContent">
            <template v-if="result.imageUrl">
              <img
                v-for="(imgUrl, idx) in result.imageUrl.split(',')"
                :key="idx"
                height="80px"
                :src="$YBS.imgUrlTranslation(imgUrl)"
                style="margin-right: 8px; margin-bottom: 8px;"
                v-preview="$YBS.imgUrlTranslation(imgUrl)"
              />
            </template>
          </div>
        </div>
        <div class="execute">
          <div class="executeItem">
            <span>检查部门</span>
            <span>{{ pointRelease.actualExecutionDeptName || '' }}</span>
          </div>
          <div class="executeItem">
            <span>检查人</span>
            <span>{{ pointRelease.actualExecutionUserName || '' }}</span>
          </div>
          <div class="executeItem">
            <span>签名</span>
            <img
              v-if="$YBS.imgUrlTranslation(result.signatureUrl)"
              height="54px"
              :src="$YBS.imgUrlTranslation(result.signatureUrl)"
              v-preview="$YBS.imgUrlTranslation(result.signatureUrl)"
            />
          </div>
        </div>
      </div>
    </template>
    <!-- 执行任务 -->
    <template v-else>
      <div class="pointTitle">
        {{ pointRelease.taskName }}
      </div>
      <!-- 模板内容 -->
      <div class="taskBookConten">
        <!-- 日常 -->
        <van-collapse v-if="type == '0'" v-model="activeNames" :border="false">
          <van-collapse-item :is-link="false" title="" :name="index"
            v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
            :key="item.id"
            disabled>
            <template #title>
              <div class="inspectionConten">
                <span class="project">第{{ index + 1 }}项</span>
                <span class="line"></span>
                <span class="project">{{ item.detailName }}</span>
                <span v-if="item.inspectionBasis!=''&&item.inspectionBasis!=null" class="basisSpanClass" @click.stop="basisClick(item.inspectionBasis)">依据</span>
              </div>
            </template>
            <div class="daily-items">
              <div class="item-conten">{{item.standardRequirements}}</div>
            </div>
            <!-- <div class="daily-items basisSpanClassCopy">
              <div class="item-conten">{{item.inspectionBasis}}</div>
            </div> -->
            <div class="quality-check">
              <van-radio-group v-model="item.normal" direction="horizontal">
                <van-radio name="2" checked-color="#3562DB" icon-size="16px">合格</van-radio>
                <van-radio name="3" checked-color="#3562DB" icon-size="16px">不合格</van-radio>
              </van-radio-group>
            </div>
            <div class="photo-proof">
              <div style="display: flex;vertical-align: middle;height: 100%;justify-content: space-between;align-items: center;">
                <div class="photo-proof-title">上传照片证明</div>
                <div style="display: flex;vertical-align: middle;height: 100%">
                  <div class="tolta">{{ item.proofPhotos ? item.proofPhotos.length : 0 }}/5</div>
                  <ApiGetPicture ref="apiProofPicture" @submitFile="(file) => getApiProofFile(file, index)" :limited="5" />
                </div>
              </div>
              <div class="picture-select" style="width: 100%; margin: 0 auto">
                <van-uploader ref="uploadProofImg" :disabled="h5Mode == 'apicloud'" v-model="item.proofFiles" :max-count="5" accept="image/*"
                  :after-read="(file) => afterReadProof(file, index)" @delete="(file) => deleteProofImg(file, index)" :preview-full-image="false" 
                  @click-preview="(e) => previewImg((item.proofFiles || []).map(i => i.content))">
                  <span v-if="h5Mode == 'apicloud'"></span>
                </van-uploader>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
        <!-- 专业 -->
        <van-collapse v-if="type == '1'" v-model="activeNames" :border="false">
          <van-collapse-item :is-link="false" title=""
            v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList" :key="item.id"
            :name="index"
            disabled>
            <template #title>
              <div class="inspectionConten">
                <span class="project">第{{ index + 1 }}项</span>  
                <span class="line"></span>
                <span class="project">{{ item.detailName }}</span>
                <span v-if="item.inspectionBasis!=''&&item.inspectionBasis!=null" class="basisSpanClass" @click.stop="basisClick(item.inspectionBasis)">依据</span>
              </div>
            </template>
            
            <div v-for="(item2, inde) in item.maintainProjectdetailsTermReleaseList" :key="item2.id">
              <div class="gist type1" v-if="item2.isNum == '1'">
                <span>{{ inde + 1 }}. 检查要点{{
                    item2.content ? item2.content : ""
                  }}</span>
              </div>
              <div class="gist type2" v-if="item2.isNum == '2'">
                <span>{{ inde + 1 }}. 检查要点{{
                    item2.content ? item2.content : ""
                  }}</span>
                <van-field v-model="item2.value" placeholder="请输入" @focus="clickField" @blur="checkField(item2)"
                  :error-message="item2.error" maxlength="50" label="" rows="1" type="textarea" autosize />
              </div>
              <div class="gist type3" v-if="item2.isNum == '0'">
                <span>{{ inde + 1 }}. 检查要点{{
                    item2.content
                      ? item2.content +
                        "(" +
                        item2.rangeStart +
                        "-" +
                        item2.rangeEnd +
                        (item2.einheitName ? item2.einheitName : '') +
                        ")"
                      : ""
                  }}</span>
                <van-field v-model="item2.value" label="" placeholder="请输入" @focus="clickField" @blur="checkField(item2)"
                  :error-message="item2.error" type="number" autosize maxlength="20">
                  <div slot="extra" v-if="item2.outRange" style="color:red">输入范围异常</div>
                </van-field>
              </div>
              <div class="gist type4" v-if="item2.isNum == '3'">
                <span>{{ inde + 1 }}. 检查要点{{
                    item2.content ? item2.content : ""
                  }}</span>
                <van-radio-group v-model="activeChecked[index][inde]" direction="horizontal">
                  <van-radio v-for="(item3, i) in item2.termJsonArray" :key="item3.conText" :name="i" checked-color="#3562DB"
                    icon-size="16px" @click="tapRadio(item2)">{{ item3.contText }}</van-radio>
                </van-radio-group>
              </div>
              
              <div class="photo-proof">
                <div style="display: flex;vertical-align: middle;height: 100%;justify-content: space-between;align-items: center;">
                  <div class="photo-proof-title">上传照片证明</div>
                  <div style="display: flex;vertical-align: middle;height: 100%">
                    <div class="tolta">{{ item2.proofPhotos ? item2.proofPhotos.length : 0 }}/5</div>
                    <ApiGetPicture ref="apiProofPicture" @submitFile="(file) => getApiProofFile(file, index, inde)" :limited="5" />
                  </div>
                </div>
                <div class="picture-select" style="width: 100%; margin: 0 auto">
                  <van-uploader ref="uploadProofImg" :disabled="h5Mode == 'apicloud'" v-model="item2.proofFiles" :max-count="5" accept="image/*"
                    :after-read="(file) => afterReadProof(file, index, inde)" @delete="(file) => deleteProofImg(file, index, inde)" :preview-full-image="false" 
                    @click-preview="(e) => previewImg((item2.proofFiles || []).map(i => i.content))">
                    <span v-if="h5Mode == 'apicloud'"></span>
                  </van-uploader>
                </div>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>
      <!-- 检查内容 -->
      <div class="bottom-conten">
        <div class="situation">检查结果总结</div>
        <van-field v-model="repairExplain" rows="2" autosize type="textarea" maxlength="200"
          placeholder="请输入检查结果，最多200字" :rules="[{ required: true }]"
          show-word-limit />
        <div class="situationUplod">
          <div class="imgTitle">现场照片和视频</div>
          <div style="display: flex;vertical-align: middle;height: 100%">
            <div class="tolta">{{ attachmentUrl.length }}/5</div>
            <ApiGetPicture ref="apiPicture" @submitFile="getApiFile" :limited="5" />
          </div>
        </div>
        <div class="picture-select" style="width: 100%; margin: 0 auto">
          <van-uploader ref="uplodImg" :disabled="h5Mode == 'apicloud'" v-model="files" :max-count="5" accept="image/*"
            :after-read="afterRead" @delete="deleteImg" :preview-full-image="false" @click-preview="previewImg(files.map(i => i.content))">
            <span v-if="h5Mode == 'apicloud'"></span>
          </van-uploader>
        </div>
        <div class="topItem">
          <div class="itemTitle">检查部门</div>
          <div class="itemConten">{{ loginInfo.deptName }}</div>
        </div>
        <div class="topItem">
          <div class="itemTitle">检查人员</div>
          <div class="itemConten">{{ loginInfo.staffName }}</div>
        </div>
        <div class="topItem notBoder">
          <div class="itemTitle">签名</div>
          <template v-if="!signatureData">
            <div class="itemConten" @click="toPatroSignature">
              去签名
              <van-icon name="arrow"/>
            </div>
          </template>
          <div v-else class="signatured">
            <img
              height="54px"
              :src="signatureImage"
              @click="previewImg([signatureImage])"
            />
            <div class="itemConten" @click="toPatroSignature">
              重签
              <van-icon name="arrow"/>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-bar">
        <div class="qualified" @click="confirm">确定</div>
      </div>
    </template>
    <van-popup v-model="signatureShow">
      <signature-page @saveImg="onSaveImg" class="signaturePage"></signature-page>
    </van-popup>
      <van-dialog   v-model="showDialog" :show-cancel-button="false" title="规范依据" confirmButtonColor="#3562DB">
          <div class="dialogBasis">{{inspectionBasisDialog}}</div>
          </van-dialog>
  </div>
</template>
<script>
import moment from "moment";
import axios from "axios";
import ImageCompressor from "image-compressor.js";
import SignaturePage from "@/pages/form/formDetails/components/Signature";
import YBS from "@/assets/utils/utils.js";
import { Toast, ImagePreview } from "vant";
import { mapState } from "vuex";
import vue from 'vue';
export default {
  components: {
    SignaturePage
  },
  data() {
    return {
      inspectionBasisDialog:"",
      moment,
      activeNames: [],
      loginInfo: {},
      taskBookType: '',
      id: '',
      type: '',
      excute: {},
      result: {},
      pointRelease: {
        maintainProjectRelease: {
          maintainProjectdetailsReleaseList: []
        }
      },
      startExecutionTime: '',
      typeOptions: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      selectAllOption: false,//全选
      activeChecked: [], // 单选默认选中
      repairOption: [], // 报修项
      repairExplain: '', // 检查结果总结
      attachmentUrl: [],
      files: [],
      signatureShow: false,
      signatureData: '', // 签名
      signatureImage: '', // 展示签名
      status: '0', // 0未完成 1已完成
      showDialog:false,
      planId:""
    }
  },
  computed: {
    ...mapState(["h5Mode"])
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.id = this.$route.query.id
    this.planId = this.$route.query.planId
    this.status = this.$route.query.status
    this.startExecutionTime = moment().format('YYYY-MM-DD HH:mm:ss')
    this.getBookData()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    window.onresize = function () {
      console.log('change')
    }
  },
  methods: {
    //专业巡检依据
      basisClick(item){
      this.inspectionBasisDialog = item
       this.showDialog = true
       },
    // 判断手机 - ios/andriod
    isIOS() {
      const u = navigator.userAgent;
      return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    },
    clickField(e) {
      if (this.isIOS()) {
        window.addEventListener('focusin', function () {
          if (
            document.activeElement.tagName === 'INPUT' ||
            document.activeElement.tagName === 'TEXTAREA'
          ) {
            setTimeout(function () {
              document.documentElement.scrollTop = document.body.scrollHeight;
            }, 0);
          }
        });
      } else {
        document.activeElement.scrollIntoView()
      }
    },
    getBookData() {
      let params = {
        recordId: this.id,
        taskStatus: this.status == '1'  ? this.status : '0' // 1：整改计划执行完毕 0和2：未执行完毕
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      let url = ''
      // 详情
      if (this.status == '1' || this.$route.query.type == 'recode') {
        url = 'getPatroDetail'
      } else {
        url = 'getPatroRecodeDetail'
      }
      this.$api[url](params).then(res => {
        if (!res.excute || !res.result || !res.pointRelease) {
          const that = this
          Toast.clear()
          Toast({
            message: '当前任务执行时间未到',
            forbidClick: true,
            overlay: false,
            duration: 3000,
            onClose() {
              that.$router.go(-1)
            }
          });
        } else {
          const { excute, result, pointRelease } = res
          this.excute = excute
          this.result = result
          this.pointRelease = pointRelease
          if (pointRelease.maintainProjectRelease) {
            this.type = pointRelease.maintainProjectRelease.templateType
          }
          pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => {
       
            this.activeNames.push(index)
            this.repairOption.push(false)
            // 为每个巡检项初始化照片数组
            this.$set(i, 'proofPhotos', [])
            this.$set(i, 'proofFiles', [])
            // 只在执行任务时初始化pictureUrl，查看详情时不重置
            if (this.status != '1' && this.$route.query.type != 'recode') {
              this.$set(i, 'pictureUrl', '')
            }
            // 仅在执行任务时设置默认值，查看详情时不需要
            if (this.status != '1' && this.$route.query.type != 'recode') {
              i.normal = '' // 日常巡检项不设置默认值
            }
            if (i.maintainProjectdetailsTermReleaseList) {
              const iii = []
              i.maintainProjectdetailsTermReleaseList.forEach(j => {
                // 为每个专业巡检要点初始化照片数组
                this.$set(j, 'proofPhotos', [])
                this.$set(j, 'proofFiles', [])
                // 只在执行任务时初始化pictureUrl，查看详情时不重置
                if (this.status != '1' && this.$route.query.type != 'recode') {
                  this.$set(j, 'pictureUrl', '')
                }
                // 仅在执行任务时设置默认值，查看详情时不需要
                if (this.status != '1' && this.$route.query.type != 'recode') {
                  j.normal = '2' // 专业巡检默认为合格
                }
                if (j.isNum == '3') {
                  j.termJsonArray.forEach((k, ind) => {
                    if (k.isDefault == '0') {
                      iii.push(ind)
                    }
                  })
                } else {
                  iii.push('')
                }
              })
              this.activeChecked.push(iii)
            }
          })
          Toast.clear()
        }
      })
    },
    // 输入校验
    checkField(item2) {
      if (!item2.value || item2.value.length == 0) {
        item2.error = "内容不能为空";
      } else {
        item2.error = "";
      }
      console.log('item2.value', item2.value)
      console.log('rangestart', parseInt(item2.rangeStart))
      console.log('rangeEnd',parseInt(item2.rangeEnd))
      if (item2.value &&
        (item2.value < parseInt(item2.rangeStart) ||
          item2.value > parseInt(item2.rangeEnd))
      ) {
        item2.outRange = true;
        item2.normal = '3'; // 如果输入范围异常，默认选择不合格
      } else {
        item2.outRange = false;
      }
      this.$forceUpdate();
    },
    tapRadio(item) {
      this.$forceUpdate();
    },
    // 提交校验
    confirm() {
      // 添加合格/不合格必填校验
      if (this.type == 0) {
        // 检查日常巡检项是否已选择合格/不合格
        const hasUnselected = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.some(item => {
          return item.normal !== '2' && item.normal !== '3';
        });
        
        if (hasUnselected) {
          this.$toast.fail('请先选择所有巡检项的合格/不合格');
          return;
        }
      }
      
      const arr = []
      // 输入范围校验
      let outRange = false
      this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, ind) => {
        if (this.type == 0) { // 日常
          const item = {}
          item.id = i.maintainProjectdetailsTermReleaseList[0].id
          item.normal = i.normal // 使用选择的合格/不合格，不设置默认值
          // 添加照片URL
          item.pictureUrl = i.pictureUrl || ''
          arr.push(item)
        } else {
          i.maintainProjectdetailsTermReleaseList.forEach((item2, index) => {
            if (item2.isNum == "3") {
              let obj = {
                id: item2.id,
                value: item2.termJsonArray[this.activeChecked[ind][index]].contText,
                normal: item2.termJsonArray[this.activeChecked[ind][index]].qualified == 'true' ? '2' : '3', // 根据qualified判断合格/不合格
                // 添加照片URL
                pictureUrl: item2.pictureUrl || ''
              };
              arr.push(obj);
            } else if (item2.isNum != "1") {
              if (!item2.value || item2.value.length == 0) {
                item2.error = "内容不能为空";
              } else {
                item2.error = "";
              }
              this.$forceUpdate();
              if (item2.outRange) {
                outRange = true
              }
              let obj = {
                id: item2.id,
                value: item2.value,
                normal: item2.isNum == "0" && item2.outRange ? '3' : '2', // 数值类型根据是否超出范围判断
                // 添加照片URL
                pictureUrl: item2.pictureUrl || ''
              };
              arr.push(obj);
            } else {
              // isNum 为 1 的情况
              let obj = {
                id: item2.id,
                value: '',
                normal: '2', // 其他类型都算合格
                // 添加照片URL
                pictureUrl: item2.pictureUrl || '',
                isNum: item2.isNum
              };
              arr.push(obj);
            }
          })
        }
      })
      if (this.type == 0) {
        this.subParams(arr)
      } else {
          // 校验所有填写项是否填写
          if (arr.some(i => !i.value && i.isNum !== '1')) {
          this.$toast.fail('请先完成任务书！')
        } else {
          // 移除输入范围异常的校验，允许即使有异常也可以提交成功
          this.subParams(arr)
        }
      }
    },
    subParams(arr) {
      // 判断是否有不合格项
      let hasUnqualified = false;
      
      if (this.type == 0) { // 日常
        // 检查是否有不合格项
        hasUnqualified = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.some(item => item.normal === '3');
      } else { // templateType为1
        // 检查是否有不合格项
        hasUnqualified = arr.some(item => item.normal === '3');
      }
      
      const params = {
        answerMapList: JSON.stringify(arr),
        attachmentUrl: this.attachmentUrl.length ? this.attachmentUrl.map(i => i.fileKey).join(',') : '',
        details: this.repairExplain,
        officeId: this.loginInfo.deptId,
        officeName: this.loginInfo.deptName,
        staffId: this.loginInfo.staffId,
        staffName: this.loginInfo.staffName,
        taskId: this.id,
        planId:this.planId,
        taskPointReleaseId: this.pointRelease.id,
        signature: this.signatureData,
        state: hasUnqualified ? '3' : '2', // 如有一项不合格，整个巡检结果为不合格(3)；全部合格，整个结果为合格(2)
        submitLocation: '',
        bookEmpty: '',
        callerTapeUrl: '',
        executeDate: '',
        guaranteeCode: '',
        spyScan: 1,
        taskPointReleaseId: this.pointRelease.id,
        startExecutionTime: this.startExecutionTime
      }
      console.log('参数', params)
      this.$api.getPatroSubmit(params).then(res => {
        if (res) {
          this.$toast.success('提交成功！')
          this.$router.go(-1)
        }
      })
    },
    goBack() {
      sessionStorage.removeItem('isPointDetail')
      this.$router.go(-1)
    },
    // 全选
    selectAllChange(e) {
      this.repairOption = []
      if (!e) {
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => {
          this.repairOption.push(true)
        })
      } else {
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => {
          this.repairOption.push(false)
        })
      }
    },
    // 单选
    radioChange(e, index) {
      const hasEmptyString = this.repairOption.every((i) => i === true);
      if (!hasEmptyString) {
        this.selectAllOption = false
      } else {
        this.selectAllOption = true
      }
    },
    // 图片选择
    async afterRead(files, detail) {
      this.setFile(files)
    },
    //删除图片
    deleteImg(e) {
      this.attachmentUrl = this.attachmentUrl.filter(i => i.name != e.file.name);
    },
    getApiFile(file) {
      console.log(file,'__PATH.IMGURL__PATH.IMGURL__PATH.IMGURL__PATH.IMGURL');
      
      if (this.files.length == 5) {
        return this.$toast('最多上传五个附件')
      }
      this.files.push(
        {
          content: file.base64Data,
          status: "uploading",
          file: file.file,
          message: ''
        }
      )
      this.setFile(file)
    },
    setFile(files) {
      let formData = new FormData();
      const allComressImage = [];
      // 每次提交所有的fileList导致服务器同等照片会存储多遍，目前multiple的兼容性并不友好，未找到合适的测试机型，如后续有可以考虑遍历files，增加当前选中的图片文件
      this.files[this.files.length - 1].status = "uploading";
      allComressImage.push(this.compressImage(this.files[this.files.length - 1]));
      formData.append("locationName", localStorage.getItem("location") || "");
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      Promise.all(allComressImage)
        .then(results => {
          results.forEach(file => {
            formData.append("file", file);
          });
          // 处理API调用的结果
          axios({
            method: "post",
            url: __PATH.SPACE_API + "/lease/upload",
            data: formData,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              Authorization: localStorage.getItem("token"),
              'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
            }
          })
            .then(res => {
              if (res.data.code == 200) {
                this.files.forEach(i => {
                  return (i.status = "done");
                });
                // 单个上传
                this.attachmentUrl.push({
                  fileKey: this.$YBS.imgUrlTranslation(res.data.data),
                  name: files.file.name
                });
                this.files.forEach(i => {
                  return (i.status = "done");
                });
              }
            })
            .catch(() => {
              this.files.forEach(i => {
                return (i.status = "failed");
              });
              this.$toast.fail("上传失败");
            });
        })
        .catch(error => {
          // 处理API调用过程中发生的错误
          this.$toast.fail("上传失败");
        });
    },
    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        if (this.h5Mode == "apicloud") {
          resolve(file.file);
        } else {
          new ImageCompressor(file.file, {
            quality: 0.6,
            checkOrientation: false,
            success(res) {
              let file = new window.File([res], res.name, { type: res.type });
              resolve(file);
            },
            error(e) {
              reject();
            }
          });
        }
      });
    },
    previewImg(images) {
      ImagePreview({
        images: images,
        showIndex: true, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    },
    toPatroSignature() {
      vue.prototype.$signatureCustom = true
      this.signatureShow = true
    },
    // 保存签名
    onSaveImg(val) {
      this.signatureImage = val
      // base64转流
      let arr = val.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      const file = new File([u8arr], 'signature.png', { type: mime });
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.SPACE_API + "/lease/upload",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: localStorage.getItem("token"),
          'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
        }
      })
        .then(res => {
          if (res.data.code == 200) {
            this.signatureData = this.$YBS.imgUrlTranslation(res.data.data)
          }
          this.signatureShow = false
        })
        .catch(() => {
          this.$toast.fail("签名保存失败");
          this.signatureShow = false
        });
    },
    cycleTypeFormat(type) {
      let name = "";
      this.typeOptions.find(i => {
        if (i.cycleType == type) {
          name = i.label;
        }
      });
      return name;
    },
    // 照片证明相关方法
    afterReadProof(files, index, inde) {
      if (inde !== undefined) {
        // 专业巡检项
        this.setProofFileForProfessional(files, index, inde)
      } else {
        // 日常巡检项
        this.setProofFile(files, index)
      }
    },
    // 删除证明照片
    deleteProofImg(e, index, inde) {
      if (inde !== undefined) {
        // 专业巡检项
        const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
        const item2 = item.maintainProjectdetailsTermReleaseList[inde]
        item2.proofPhotos = item2.proofPhotos.filter(i => i.name != e.file.name);
        
        // 更新pictureUrl
        if (item2.pictureUrl) {
          const urls = item2.pictureUrl.split(',');
          const filteredUrls = urls.filter(url => {
            // 通过比较文件名来确定要删除的URL
            const fileName = url.split('/').pop();
            return fileName !== e.file.name.split('/').pop();
          });
          item2.pictureUrl = filteredUrls.join(',');
        }
      } else {
        // 日常巡检项
        const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
        item.proofPhotos = item.proofPhotos.filter(i => i.name != e.file.name);
        
        // 更新pictureUrl
        if (item.pictureUrl) {
          const urls = item.pictureUrl.split(',');
          const filteredUrls = urls.filter(url => {
            // 通过比较文件名来确定要删除的URL
            const fileName = url.split('/').pop();
            return fileName !== e.file.name.split('/').pop();
          });
          item.pictureUrl = filteredUrls.join(',');
        }
      }
    },
    getApiProofFile(files, index, inde) {
      console.log(files,'filesfilesfilesfiles');
      
      if (inde !== undefined) {
        // 专业巡检项
        const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
        const item2 = item.maintainProjectdetailsTermReleaseList[inde]
        if (item2.proofFiles.length === 5) {
          return this.$toast('最多上传五个附件')
        }
        item2.proofFiles.push(
          {
            content: files.base64Data,
            status: "uploading",
            file: files.file,
            message: ''
          }
        )
        this.setProofFileForProfessional(files, index, inde)
      } else {
        // 日常巡检项
        const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
        if (item.proofFiles.length === 5) {
          return this.$toast('最多上传五个附件')
        }
        item.proofFiles.push(
          {
            content: files.base64Data,
            status: "uploading",
            file: files.file,
            message: ''
          }
        )
        this.setProofFile(files, index)
      }
    },
    // 专业巡检项的照片处理
    setProofFileForProfessional(files, index, inde) {
      const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
      const item2 = item.maintainProjectdetailsTermReleaseList[inde]
      let formData = new FormData();
      const allComressImage = [];
      // 每次提交所有的fileList导致服务器同等照片会存储多遍，目前multiple的兼容性并不友好，未找到合适的测试机型，如后续有可以考虑遍历files，增加当前选中的图片文件
      item2.proofFiles[item2.proofFiles.length - 1].status = "uploading";
      allComressImage.push(this.compressImage(item2.proofFiles[item2.proofFiles.length - 1]));
      formData.append("locationName", localStorage.getItem("location") || "");
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      Promise.all(allComressImage)
        .then(results => {
          results.forEach(file => {
            formData.append("file", file);
          });
          // 处理API调用的结果
          axios({
            method: "post",
            url: __PATH.SPACE_API + "/lease/upload",
            data: formData,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              Authorization: localStorage.getItem("token"),
              'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
            }
          })
            .then(res => {
              if (res.data.code == 200) {
                item2.proofFiles.forEach(i => {
                  return (i.status = "done");
                });
                // 单个上传
                item2.proofPhotos.push({
                  fileKey: this.$YBS.imgUrlTranslation(res.data.data),
                  name: files.file.name
                });
                // 将照片URL存入item2.pictureUrl
                if (!item2.pictureUrl) {
                  item2.pictureUrl = this.$YBS.imgUrlTranslation(res.data.data);
                } else {
                  item2.pictureUrl += ',' + this.$YBS.imgUrlTranslation(res.data.data);
                }
                item2.proofFiles.forEach(i => {
                  return (i.status = "done");
                });
              }
            })
            .catch(() => {
              item2.proofFiles.forEach(i => {
                return (i.status = "failed");
              });
              this.$toast.fail("上传失败");
            });
        })
        .catch(error => {
          // 处理API调用过程中发生的错误
          this.$toast.fail("上传失败");
        });
    },
    // 日常巡检项的照片处理
    setProofFile(files, index) {
      const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
      let formData = new FormData();
      const allComressImage = [];
      // 每次提交所有的fileList导致服务器同等照片会存储多遍，目前multiple的兼容性并不友好，未找到合适的测试机型，如后续有可以考虑遍历files，增加当前选中的图片文件
      item.proofFiles[item.proofFiles.length - 1].status = "uploading";
      allComressImage.push(this.compressImage(item.proofFiles[item.proofFiles.length - 1]));
      formData.append("locationName", localStorage.getItem("location") || "");
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      Promise.all(allComressImage)
        .then(results => {
          results.forEach(file => {
            formData.append("file", file);
          });
          // 处理API调用的结果
          axios({
            method: "post",
            url: __PATH.SPACE_API + "/lease/upload",
            data: formData,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              Authorization: localStorage.getItem("token"),
              'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
            }
          })
            .then(res => {
              if (res.data.code == 200) {
                item.proofFiles.forEach(i => {
                  return (i.status = "done");
                });
                // 单个上传
                item.proofPhotos.push({
                  fileKey: this.$YBS.imgUrlTranslation(res.data.data),
                  name: files.file.name
                });
                // 将照片URL存入item.pictureUrl
                if (!item.pictureUrl) {
                  item.pictureUrl = this.$YBS.imgUrlTranslation(res.data.data);
                } else {
                  item.pictureUrl += ',' + this.$YBS.imgUrlTranslation(res.data.data);
                }
                item.proofFiles.forEach(i => {
                  return (i.status = "done");
                });
              }
            })
            .catch(() => {
              item.proofFiles.forEach(i => {
                return (i.status = "failed");
              });
              this.$toast.fail("上传失败");
            });
        })
        .catch(error => {
          // 处理API调用过程中发生的错误
          this.$toast.fail("上传失败");
        });
    },
  }
}
</script>
<style lang="stylus" scoped>
.selectAll
  width 100%
  display flex
  margin 0.1rem 0
  justify-content flex-end
  span
    margin 0.1rem 0.2rem 0 0
  div
    margin-right 0.16rem
.inner
  height 100%
  background-color #F2F4F9
  .pointTitle
    font-size 0.32rem
    font-weight: 600
    color #1D2129
    padding 0 0.32rem
    height 1.04rem
    line-height 1.5
    background-color #fff
    display flex
    justify-content: center
    align-items: center
    border-bottom: 1px solid #E5E6EB
    .point
      display inline-block
      margin 0 0.24rem
      color #1D2129
      overflow-y auto
      white-space nowrap
  .taskBookConten
    background-color #fff
    padding 0.2rem 0
  .bottom-conten
    border-top 0.2rem solid #F2F4F9
    padding 0 0.32rem 1.48rem 0.32rem
    background-color #fff
    .topItem
      height 1.08rem
      display flex
      justify-content space-between
      align-items center
      border-bottom 1px solid #E5E6EB
      color #1D2129
      font-size 0.32rem
      .itemTitle
        font-weight 600
      .signatured
        display flex
        align-items: center
        .itemConten
          margin-left 10px
    .notBoder
      border none
      .itemConten
        font-size 0.28rem
        color #9CA2A9
    .situation
      height 1.08rem
      line-height 1.08rem
      color #1D2129
      font-size 0.32rem
      font-weight 600
    .situationUplod
      height 1.08rem
      line-height 1.08rem
      display flex
      align-items center
      justify-content space-between
      .imgTitle
        color #1D2129
        font-size 0.32rem
        font-weight 600
      .tolta
        color #86909C
    .picture-select
      display flex
  .bottom-bar
    border-top 10px solid #f2f4f9
    width calc(100% - 0.64rem)
    height 1.08rem
    background-color #fff
    position fixed
    bottom 0
    padding 0.2rem 0.32rem 0 0.32rem
    display flex
    justify-content space-between
    .qualified
      width 100%
      height 0.88rem
      line-height 0.88rem
      text-align center
      background-color #3562DB
      color #fff
      font-size 0.32rem
      border-radius 0.04rem
  .baseInfo 
    padding 0.32rem
    font-size 0.32rem
    background-color #fff
    margin-bottom 0.2rem
    .titleItem
      display flex
      align-items center
      color #1D2129
      font-weight 600
      padding-bottom 0.2rem
      .lineBar
        margin-right 0.16rem
        width 0.06rem
        height 0.32rem
        background-color #3562DB
      span
        padding-top 0.04rem
    .contentItem
      margin-top 0.2rem
      display flex
      align-items center
      padding: 0.04rem 0
      span:first-child
        width 1.76rem
        color #4E5969
      span:last-child
        color #1D2129
    .sumUp
      .sumUpTitle
        padding 0.2rem 0
        color #1D2129
        font-weight: 600
      .sumUpContent
        padding 0.2rem
    .execute
      .executeItem
        display flex
        padding 0.2rem 0
        span:first-child
          width 2.2rem
          color #1D2129
          font-weight: 600
        span:last-child
          color #1D2129
/deep/ .van-cell::after
  border none
/deep/ .van-collapse-item--border::after
  border none
/deep/.van-collapse
  background-color #fff !important
  .van-collapse-item
    background-color #fff !important
  .van-cell
    padding 0.12rem 0.32rem
  .van-cell__title
    display flex
    justify-content space-between
    align-items center
    .inspectionConten
      width 100%
      display flex
      align-items center
      .project
        color #1d2129
        font-size 0.32rem
        font-weight 600
      .line {
        display inline-block
        height 0.28rem
        width 0.04rem
        margin 0 0.12rem
        background-color #1D2129
      }
  .van-collapse-item__wrapper
    background-color #fff !important
  .van-collapse-item__content
    background-color #fff !important
    padding 0 0.32rem 0 1rem
    .daily-items
      background-color #fff !important
      color #1d2129
      font-size 0.3rem
    .item-conten
      background-color #fff !important
      color #4E5969
      font-size 0.3rem
      padding 0.12rem 0
    .van-radio
      padding 0.16rem 0
/deep/ .van-checkbox__icon--checked .van-icon
  background-color #3562DB
  border-color #3562DB
/deep/ .signaturePage
  .btns
    position none !important
    width calc(100% - 10px) !important
    padding-right 10px !important
/deep/ .van-radio--disabled
  .van-radio__icon--checked
    .van-icon
      color: #fff
      background-color: #3562DB
      border-color: #3562DB
  .van-radio__label
    color: #323233
.gist
  margin-bottom 0.2rem
  .quality-check
    margin-top 0.1rem
    display flex
.photo-proof
  margin-top 0.2rem
  .photo-proof-title
    color #1D2129
    font-size 0.32rem
    font-weight 600
    margin-bottom 0.1rem
  .tolta
    color #86909C
    margin-right 0.16rem
    display flex
    align-items center
    justify-content center
.dialogBasis 
    height:3.3rem
    margin: 0.3rem
    overflow: auto  
.basisSpanClass {
  color #3562db
  font-size 16px
  margin-left: auto
  margin-right: 10px
}
.basisSpanClassCopy {
  display flex
  justify-content: space-around
}    
</style>
