<template>
  <page-layout @back="goback" class="inner-continer">
    <div class="inner" :class="type ? 'recodeList' : ''">
      <div v-if="!type" class="recode" @click="toRecode">
        <van-icon name="orders-o" />
        巡检记录
      </div>
      <!-- <div v-else class="dateFilter">
        <div class="dateWrap">
          <van-field v-model="dateInterval[0]" readonly placeholder="开始日期" @click="selecteDate(0)" />
          <van-field v-model="dateInterval[1]" readonly placeholder="结束日期" @click="selecteDate(1)" />
        </div>
        <div class="btnWrap">
          <van-button type="primary" color="#3562DB" @click="search">查询</van-button>
          <van-button @click="resetFilter">清空</van-button>
        </div>
      </div> -->
      <div class="innerContent">
        <van-pull-refresh v-if="listData.length > 0" v-model="isLoading" @refresh="onRefresh">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <div v-for="(i, ind) in listData" :key="ind" class="list" :class="ind == listData.length - 1 ? 'lastList' : ''" @click="goTaskDetail(i)">
              <div class="titleWrap">
                <span class="task-name">{{ i.taskName || "" }}</span>
                <span class="typeWarp">
                  <!-- <span v-if="type" class="taskType taskType1">已完成</span>
                  <span v-else class="taskType" :class="'taskType' + i.projectStatusCode">{{ i.projectStatus }}</span> -->
                  <van-icon name="arrow" />
                </span>
              </div>
              <div class="itemWrap">
                <span class="itemTitle">作业名称</span>
                <span class="itemContent">{{ i.workName || "" }}</span>
              </div>
              <div class="itemWrap">
                <span class="itemTitle">作业类型</span>
                <span class="itemContent">{{ i.workTypeName || "" }}</span>
              </div>
              <div class="itemWrap">
                <span class="itemTitle">施工地点</span>
                <span class="location">
                  <span v-if="i.workLocalType" class="locationType">{{ i.workLocalType == "0" ? "室内" : "室外" }}</span>
                  <span class="locationDetail">{{ i.workLocationName || "" }}</span>
                </span>
              </div>
              <div class="itemWrap">
                <span class="itemTitle">巡检开始时间</span>
                <span class="itemContent">{{ i.workStartTime || "" }}</span>
              </div>
              <div class="itemWrap">
                <span class="itemTitle">巡检结束时间</span>
                <span class="itemContent">{{ i.workEndTime || "" }}</span>
              </div>
              <!-- <div class="itemWrap">
                <span class="itemTitle">施工期限</span>
                <span class="itemContent">
                  {{ (i.workStartTime && i.workEndTime) ? (moment(i.workStartTime).format("YYYY-MM-DD") + " 至 " + moment(i.workEndTime).format("YYYY-MM-DD")) : '' }}
                </span>
              </div> -->
              <!-- <div class="itemWrap">
                <span class="itemTitle">申请施工科室</span>
                <span class="itemContent">{{ i.workDeptName || "" }}</span>
              </div> -->
              <!-- <div class="itemWrap">
                <span class="itemTitle">施工单位</span>
                <span class="itemContent">{{ i.workCompanyName || "" }}</span>
              </div> -->
              <!-- <div v-if="type != 'recode'" class="statisticsOperation">
                <div class="statistics">
                  <span class="molecule">已巡 /</span>
                  <span class="denominator">应巡</span>
                  <span class="molecule">{{ i.alreadyMaintainNum }} /</span>
                  <span class="denominator">{{ i.shouldMaintainNum }}</span>
                </div>
                <div class="operation" :class="'operation' + i.planStatus">
                  <span>{{ i.planStatus == "1" ? "已完成" : "未完成" }}</span>
                  <van-button v-if="i.planStatus != '1'" type="primary" color="#3562DB">去巡查</van-button>
                </div>
              </div> -->
            </div>
          </van-list>
        </van-pull-refresh>
        <div v-else class="notList">
          <div class="emptyImg">
            <span class="emptyText">暂无数据</span>
          </div>
        </div>
      </div>
      <van-popup v-model="dateShow" position="bottom">
        <van-datetime-picker v-model="currentDate" type="date" title="选择年月日" :min-date="minDate" :max-date="maxDate" @confirm="confirmDate" @cancel="dateShow = false" />
      </van-popup>
    </div>
    <div
      v-if="$route.query.type != 'recode'"
      class="bottom-bar"
      @touchstart="dragStart"
      @touchmove="dragMove"
      @touchend="dragEnd"
      @click="handleBarClick"
      :style="{ left: dragHelper.position.x + 'px', top: dragHelper.position.y + 'px' }"
    >
      随手查
    </div>
  </page-layout>
</template>
<script>
import PageLayout from "@/components/PageLayout.vue";
import moment from "moment";
import { Toast } from "vant";
import DragHelper from "@/utils/dragHelper";

export default {
  components: {
    PageLayout
  },
  data() {
    return {
      moment,
      loginInfo: {},
      listData: [],
      loading: false,
      finished: true,
      isLoading: false,
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      dateInterval: ["", ""],
      dateShow: false,
      targetIndex: 0,
      minDate: new Date(2022, 0, 1),
      maxDate: new Date(2027, 10, 1),
      currentDate: "",
      type: "", // 0未完成 1已完成
      dragHelper: null
    };
  },
  watch: {
    $route(to) {
      if (to.query.type !== this.type) {
        this.type = to.query.type || "";
        this.pageParmes = {
          pageNo: 1,
          pageSize: 10
        };
        this.listData = [];
        this.getListData();
      }
    }
  },
  created() {
    this.dragHelper = new DragHelper({
      initX: window.innerWidth - 100,
      initY: window.innerHeight - 100
    });
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    if (this.$route.query.type) {
      this.type = this.$route.query.type;
    }
    this.getListData();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);

    // 初始化拖拽位置
    const bottomBar = document.querySelector(".bottom-bar");
    this.dragHelper.initPosition(bottomBar);
  },
  methods: {
    onLoad() {
      this.pageParmes.pageNo++;
      this.finished = false;
      this.loading = true;
      this.getListData();
    },
    onRefresh() {
      this.pageParmes = {
        pageNo: 1,
        pageSize: 10
      };
      this.listData = [];
      this.getListData();
    },
    getListData() {
      const params = {
        currentPage: this.pageParmes.pageNo,
        size: this.pageParmes.pageSize
        // startDate: this.dateInterval[0] ? moment(this.dateInterval[0]).format("YYYY-MM-DD") : "",
        // endDate: this.dateInterval[1] ? moment(this.dateInterval[1]).format("YYYY-MM-DD") : ""
      };
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      let url = "";
      // 详情
      // if (this.type == "recode") {
      //   url = "getPatroRecodeList";
      //   params.planId = this.$route.query.id;
      // } else {
      //   url = "getPatroListNew";
      //   params.deptId = this.loginInfo.deptId;
      //   params.planPersonCode = this.loginInfo.staffId;
      //   params.planStatus = 0
      // }
      params.deptId = this.loginInfo.deptId;
      params.planPersonCode = this.loginInfo.staffId;
      if (this.type == "recode") {
        params.planStatus = 1;
      } else {
        params.planStatus = 0;
      }
      url = "getAppTaskList";
      this.$api[url](params).then(res => {
        this.listData = this.listData.concat(res.list);
        Toast.clear();
        this.isLoading = false;
        if (this.listData.length == res.count || res.list.length == 0) {
          this.finished = true;
        } else {
          this.finished = false;
        }
        this.loading = false;
      });
    },
    goTaskDetail(item) {
      let url = "/safetyPatroDetail";
      let id = item.id;
      let planId = item.planId;
      if (item.ssc) {
        url = "/freePatroDetail";
        id = item.recordId;
      }
      this.$router.push({
        path: url,
        query: {
          id,
          status: item.planStatus,
          type: this.type,
          planId
        }
      });
    },
    toRecode() {
      this.$router.push({
        path: this.$route.path,
        query: {
          type: "recode"
        }
      });
    },
    formatType(type) {
      // 作业状态 0:未开始 1:已完成 2:进行中 3:已结束 4:已超时
      if (type == "0") return "未开始";
      if (type == "1") return "已完成";
      if (type == "2") return "进行中";
      if (type == "3") return "已结束";
      if (type == "4") return "已超时";
    },
    selecteDate(type) {
      this.targetIndex = type;
      if (type == 0) {
        this.minDate = new Date(2022, 0, 1);
      }
      this.dateShow = true;
    },
    confirmDate(val) {
      this.dateInterval[this.targetIndex] = moment(val).format("YYYY-MM-DD");
      this.minDate = val;
      this.dateShow = false;
    },
    search() {
      this.pageParmes = {
        pageNo: 1,
        pageSize: 10
      };
      this.listData = [];
      this.getListData();
    },
    resetFilter() {
      this.dateInterval = ["", ""];
      this.minDate = new Date(2022, 0, 1);
      this.maxDate = new Date(2027, 10, 1);
      this.pageParmes = {
        pageNo: 1,
        pageSize: 10
      };
      this.listData = [];
      this.getListData();
    },
    goback() {
      if (this.$route.query.from == "todoItem") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
      // this.$router.go(-1);
    },
    // 拖拽相關方法
    dragStart(e) {
      this.dragHelper.dragStart(e);
    },
    dragMove(e) {
      this.dragHelper.dragMove(e);
    },
    dragEnd() {
      this.dragHelper.dragEnd();
    },
    handleBarClick() {
      if (this.dragHelper.isClick()) {
        // 這裡可以添加你的點擊處理邏輯
        this.$router.push({
          path: "/freePatroDetail",
          query: {
            type: this.type
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.inner-continer {
  position: relative;
  .bottom-bar {
    position: fixed; // 改為 fixed 定位
    z-index: 9999; // 確保在最上層
    font-family: PingFang SC, PingFang SC;
    font-weight: bold;
    font-size: 15px;
    color: #ffffff;
    padding: 10px 15px;
    background: linear-gradient(180deg, #6488e9 0%, #3562db 100%);
    box-shadow: 0px 4px 4px 0px rgba(0, 33, 120, 0.2);
    border-radius: 100px;
    border: 1px solid #ffffff;
    touch-action: none; // 防止觸摸事件被系統處理
    user-select: none; // 防止文字被選中
    -webkit-user-select: none;
    cursor: move;
  }
}
.inner {
  position: relative;
  padding-top: 40px;
  background-color: #f2f4f9;
  height: 100%;
  box-sizing: border-box;
  font-size: 16px;
  .innerContent {
    padding: 0 10px;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    .list {
      padding: 18px 16px;
      border-radius: 8px;
      background-color: #fff;
      margin-bottom: 10px;
      .titleWrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .task-name {
          display: inline-block;
          width: 80%;
          color: #1d2129;
          font-weight: 600;
        }
        .typeWarp {
          .taskType {
            padding: 2px 6px;
            font-size: 14px;
          }
          .taskType0 {
            background-color: #ffece8;
            color: #f53f3f;
          }
          .taskType2 {
            background-color: rgba($color: #61e29d, $alpha: 0.2);
            color: #61e29d;
          }
          .taskType1 {
            background-color: #e6effc;
            color: #3562db;
          }
          .taskType3 {
            background-color: #fff7e8;
            color: #ff7d00;
          }
          .taskType99 {
            background-color: rgba($color: #9be2ff, $alpha: 0.2);
            color: #909399;
          }
        }
      }
      .itemWrap {
        display: flex;
        align-items: baseline;
        margin-top: 16px;
        .itemTitle {
          width: 110px;
          color: #4e5969;
        }
        .itemContent {
          width: calc(100% - 110px);
          color: #1d2129;
        }
        .location {
          display: flex;
          align-items: baseline;
          width: calc(100% - 110px);
          .locationType {
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 14px;
            background-color: #f2f3f5;
            color: #4e5969;
            font-weight: 600;
          }
          .locationDetail {
            width: calc(100% - 40px);
          }
        }
      }
      .statisticsOperation {
        font-size: 14px;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #e5e6eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .statistics {
          .molecule {
            color: #4e5969;
          }
          .denominator {
            color: #9ca2a9;
            margin-right: 20px;
          }
        }
        .operation {
          .van-button {
            height: 28px;
            margin-left: 8px;
          }
        }
        .operation0,
        .operation2 {
          color: #f53f3f;
        }
        .operation1 {
          color: #61e29d;
        }
      }
    }
    .lastList {
      margin: 0 !important;
    }
    .notList {
      width: 100%;
      position: relative;
      height: 70vh;
      .emptyImg {
        position: absolute;
        height: 100%;
        width: 50%;
        left: 25%;
        background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
        background-size: 100% auto;
        .emptyText {
          position: absolute;
          width: 100%;
          text-align: center;
          bottom: 40%;
        }
      }
    }
  }
  .recode {
    position: absolute;
    top: 10px;
    right: 16px;
    font-size: 14px;
    color: #3562db;
    cursor: pointer;
  }
}
.recodeList {
  padding-top: 20px;
  height: calc(100vh - 80px);
  box-sizing: border-box;
  .dateFilter {
    background-color: #fff;
    position: absolute;
    padding: 10px;
    width: calc(100% - 20px);
    height: 40px;
    top: 0;
    left: 0;
    display: flex;
    .dateWrap {
      display: flex;
      width: calc(100% - 100px);
      /deep/ .van-cell {
        padding: 8px 10px;
        .van-field__control {
          text-align: center;
        }
      }
      .van-cell::after {
        display: none;
      }
    }
    .btnWrap {
      width: 100px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .van-button {
        padding: 4px 8px;
        height: auto;
      }
    }
  }
}
</style>
