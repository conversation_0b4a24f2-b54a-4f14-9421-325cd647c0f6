<template>
  <div id="taskConten-inner" class='inner'>
    <Header title="随手查详情" @backFun="goBack"></Header>
    <!-- 查看详情 -->
    <template v-if="status == '1' || $route.query.type == 'recode'">
      <div class="baseInfo">
        <div class="titleItem">
          <div class="lineBar"></div>
          <span>基本信息</span>
        </div>
        <div class="contentItem">
          <span>任务名称</span>
          <span>{{ pointRelease.taskName || '' }}</span>
        </div>
        <div class="contentItem">
          <span>检查时间</span>
          <span>{{ moment(pointRelease.excuteTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </div>
      </div>
      <div class="baseInfo">
        <div class="titleItem">
          <div class="lineBar"></div>
          <span>巡检内容</span>
        </div>
        <div class="sumUp">
          <div class="sumUpTitle">检查结果总结</div>
          <div class="sumUpContent">{{ result.desc }}</div>
          <div class="sumUpTitle">现场照片和视频</div>
          <div class="sumUpContent">
            <img
              v-if="pointRelease.imageUrl"
              height="80px"
              :src="$YBS.imgUrlTranslation(pointRelease.imageUrl)"
               v-preview="$YBS.imgUrlTranslation(pointRelease.imageUrl)"
            />
          </div>
        </div>
        <div class="execute">
          <div class="executeItem">
            <span>检查部门</span>
            <span>{{ pointRelease.actualExecutionDeptName || '' }}</span>
          </div>
          <div class="executeItem">
            <span>检查人</span>
            <span>{{ pointRelease.actualExecutionUserName || '' }}</span>
          </div>
          <div class="executeItem">
            <span>签名</span>
            <img
              v-if="pointRelease.signatureUrl"
              height="54px"
              :src="$YBS.imgUrlTranslation(pointRelease.signatureUrl)"
               v-preview="$YBS.imgUrlTranslation(pointRelease.signatureUrl)"
            />
          </div>
        </div>
      </div>
    </template>
    <!-- 执行任务 -->
    <template v-else>
      <div class="pointTitle">
        <van-field
          v-model="taskName"
          label="选择作业"
          required
          readonly
          right-icon="arrow"
          placeholder="请选择作业"
          @click="showTaskPicker" />
      </div>
      <!-- 检查内容 -->
      <div class="bottom-conten">
        <div class="situation">检查结果总结</div>
        <van-field v-model="repairExplain" rows="2" autosize type="textarea" maxlength="200"
          placeholder="请输入检查结果，最多200字" :rules="[{ required: true }]"
          show-word-limit />
        <div class="situationUplod">
          <div class="imgTitle">现场照片和视频</div>
          <div style="display: flex;vertical-align: middle;height: 100%">
            <div class="tolta">{{ attachmentUrl.length }}/5</div>
            <ApiGetPicture ref="apiPicture" @submitFile="getApiFile" :limited="5" />
          </div>
        </div>
        <div class="picture-select" style="width: 100%; margin: 0 auto">
          <van-uploader ref="uplodImg" :disabled="h5Mode == 'apicloud'" v-model="files" :max-count="5" accept="image/*"
            :after-read="afterRead" @delete="deleteImg" :preview-full-image="false" @click-preview="previewImg(files.map(i => i.content))">
            <span v-if="h5Mode == 'apicloud'"></span>
          </van-uploader>
        </div>
        <div class="topItem">
          <div class="itemTitle">检查部门</div>
          <div class="itemConten">{{ loginInfo.deptName }}</div>
        </div>
        <div class="topItem">
          <div class="itemTitle">检查人员</div>
          <div class="itemConten">{{ loginInfo.staffName }}</div>
        </div>
        <div class="topItem notBoder">
          <div class="itemTitle">签名</div>
          <template v-if="!signatureData">
            <div class="itemConten" @click="toPatroSignature">
              去签名
              <van-icon name="arrow"/>
            </div>
          </template>
          <div v-else class="signatured">
            <img
              height="54px"
              :src="signatureImage"
              @click="previewImg([signatureImage])"
            />
            <div class="itemConten" @click="toPatroSignature">
              重签
              <van-icon name="arrow"/>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-bar">
        <div class="qualified" @click="subParams">确定</div>
      </div>
    </template>
    <van-popup v-model="signatureShow">
      <signature-page @saveImg="onSaveImg" class="signaturePage"></signature-page>
    </van-popup>
    <van-popup v-model="taskPicker" round position="bottom">
      <van-picker
        show-toolbar
        :columns="taskListData.map(i => i.projectName)"
        @cancel="taskPicker = false"
        @confirm="onConfirm"
      />
    </van-popup>
  </div>
</template>
<script>
import moment from "moment";
import axios from "axios";
import ImageCompressor from "image-compressor.js";
import SignaturePage from "@/pages/form/formDetails/components/Signature";
import YBS from "@/assets/utils/utils.js";
import { Toast, ImagePreview } from "vant";
import { mapState } from "vuex";
import vue from 'vue';
export default {
  components: {
    SignaturePage
  },
  data() {
    return {
      moment,
      loginInfo: {},
      id: '',
      excute: {},
      result: {},
      pointRelease: {},
      repairExplain: '', // 检查结果总结
      attachmentUrl: [],
      files: [],
      signatureShow: false,
      signatureData: '', // 签名
      signatureImage: '', // 展示签名
      status: '0', // 0未完成 1已完成
      taskName: '',
      taskPicker: false,
      taskListData: [],
      activeTask: {}
    }
  },
  computed: {
    ...mapState(["h5Mode"])
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.id = this.$route.query.id
    this.status = this.$route.query.status
    if (this.$route.query.type != 'recode') {
      this.getWorkListData()
    } else {
      this.getBookData()
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    getBookData() {
      let params = {
        recordId: this.id
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.sscTaskDetail(params).then(res => {
        const { excute, result, taskPoint } = res
        this.excute = excute
        this.result = result
        this.pointRelease = taskPoint
        Toast.clear()
      })
    },
    // 提交校验
    subParams() {
      if (!this.activeTask.id || !this.taskName) {
        return this.$toast('请先选择作业！')
      }
      const params = {
        attachmentUrl: this.attachmentUrl.length ? this.attachmentUrl.map(i => i.fileKey).join(',') : '',
        details: this.repairExplain,
        officeId: this.loginInfo.deptId,
        officeName: this.loginInfo.deptName,
        staffId: this.loginInfo.staffId,
        staffName: this.loginInfo.staffName,
        taskId: this.id,
        taskPointReleaseId: this.pointRelease.id,
        signature: this.signatureData,
        state: '2',
        spyScan: 1,
        ssc: 'ssc',
        workId: this.activeTask.projectCode,
        workName: this.activeTask.projectName
      }
      console.log('参数', params)
      this.$api.sscPatroSubmit(params).then(res => {
        if (res) {
          this.$toast.success('提交成功！')
          this.$router.go(-1)
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    // 图片选择
    async afterRead(files, detail) {
      this.setFile(files)
    },
    //删除图片
    deleteImg(e) {
      this.attachmentUrl = this.attachmentUrl.filter(i => i.name != e.file.name);
    },
    getApiFile(file) {
      if (this.files.length == 5) {
        return this.$toast('最多上传五个附件')
      }
      this.files.push(
        {
          content: file.base64Data,
          status: "uploading",
          file: file.file,
          message: ''
        }
      )
      this.setFile(file)
    },
    setFile(files) {
      let formData = new FormData();
      const allComressImage = [];
      // 每次提交所有的fileList导致服务器同等照片会存储多遍，目前multiple的兼容性并不友好，未找到合适的测试机型，如后续有可以考虑遍历files，增加当前选中的图片文件
      this.files[this.files.length - 1].status = "uploading";
      allComressImage.push(this.compressImage(this.files[this.files.length - 1]));
      formData.append("locationName", localStorage.getItem("location") || "");
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      Promise.all(allComressImage)
        .then(results => {
          results.forEach(file => {
            formData.append("file", file);
          });
          // 处理API调用的结果
          axios({
            method: "post",
            url: __PATH.SPACE_API + "/lease/upload",
            data: formData,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              Authorization: localStorage.getItem("token"),
              'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
            }
          })
            .then(res => {
              if (res.data.code == 200) {
                this.files.forEach(i => {
                  return (i.status = "done");
                });
                // 单个上传
                this.attachmentUrl.push({
                  fileKey: this.$YBS.imgUrlTranslation(res.data.data),
                  name: files.file.name
                });
                this.files.forEach(i => {
                  return (i.status = "done");
                });
              }
            })
            .catch(() => {
              this.files.forEach(i => {
                return (i.status = "failed");
              });
              this.$toast.fail("上传失败");
            });
        })
        .catch(error => {
          // 处理API调用过程中发生的错误
          this.$toast.fail("上传失败");
        });
    },
    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        if (this.h5Mode == "apicloud") {
          resolve(file.file);
        } else {
          new ImageCompressor(file.file, {
            quality: 0.6,
            checkOrientation: false,
            success(res) {
              let file = new window.File([res], res.name, { type: res.type });
              resolve(file);
            },
            error(e) {
              reject();
            }
          });
        }
      });
    },
    previewImg(images) {
      ImagePreview({
        images: images,
        showIndex: true, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    },
    toPatroSignature() {
      vue.prototype.$signatureCustom = true
      this.signatureShow = true
    },
    // 保存签名
    onSaveImg(val) {
      this.signatureImage = val
      // base64转流
      let arr = val.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      const file = new File([u8arr], 'signature.png', { type: mime });
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.SPACE_API + "/lease/upload",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: localStorage.getItem("token"),
          'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
        }
      })
        .then(res => {
          if (res.data.code == 200) {
            this.signatureData = this.$YBS.imgUrlTranslation(res.data.data)
          }
          this.signatureShow = false
        })
        .catch(() => {
          this.$toast.fail("签名保存失败");
          this.signatureShow = false
        });
    },
    getWorkListData() {
      this.$api
        .getAssignmentList({
          assignmentType: 0,
          page: 1,
          pageSize: 999
        })
        .then(res => {
          this.taskListData = res.records
        })
        .catch(() => {
          this.loading = false;
        });
    },
    showTaskPicker() {
      this.taskPicker = true
    },
    onConfirm(val, index) {
      this.taskName = val
      this.activeTask = this.taskListData[index]
      this.taskPicker = false
    }
  }
}
</script>
<style lang="stylus" scoped>
.selectAll
  width 100%
  display flex
  margin 0.1rem 0
  justify-content flex-end
  span
    margin 0.1rem 0.2rem 0 0
  div
    margin-right 0.16rem
.inner
  height 100%
  background-color #F2F4F9
  .pointTitle
    font-size 0.32rem
    font-weight: 600
    color #1D2129
    // padding 0 0.32rem
    height 1.04rem
    line-height 1.04rem
    background-color #fff
    display flex
    justify-content: center
    align-items: center
    border-bottom: 1px solid #E5E6EB
    .point
      display inline-block
      margin 0 0.24rem
      color #1D2129
      overflow-y auto
      white-space nowrap
  .taskBookConten
    background-color #fff
    padding 0.2rem 0
  .bottom-conten
    border-top 0.2rem solid #F2F4F9
    padding 0 0.32rem 1.48rem 0.32rem
    background-color #fff
    .topItem
      height 1.08rem
      display flex
      justify-content space-between
      align-items center
      border-bottom 1px solid #E5E6EB
      color #1D2129
      font-size 0.32rem
      .itemTitle
        font-weight 600
      .signatured
        display flex
        align-items: center
        .itemConten
          margin-left 10px
    .notBoder
      border none
      .itemConten
        font-size 0.28rem
        color #9CA2A9
    .situation
      height 1.08rem
      line-height 1.08rem
      color #1D2129
      font-size 0.32rem
      font-weight 600
    .situationUplod
      height 1.08rem
      line-height 1.08rem
      display flex
      align-items center
      justify-content space-between
      .imgTitle
        color #1D2129
        font-size 0.32rem
        font-weight 600
      .tolta
        color #86909C
    .picture-select
      display flex
  .bottom-bar
    border-top 10px solid #f2f4f9
    width calc(100% - 0.64rem)
    height 1.08rem
    background-color #fff
    position fixed
    bottom 0
    padding 0.2rem 0.32rem 0 0.32rem
    display flex
    justify-content space-between
    .qualified
      width 100%
      height 0.88rem
      line-height 0.88rem
      text-align center
      background-color #3562DB
      color #fff
      font-size 0.32rem
      border-radius 0.04rem
  .baseInfo
    padding 0.32rem
    font-size 0.32rem
    background-color #fff
    margin-bottom 0.2rem
    .titleItem
      display flex
      align-items center
      color #1D2129
      font-weight 600
      padding-bottom 0.2rem
      .lineBar
        margin-right 0.16rem
        width 0.06rem
        height 0.32rem
        background-color #3562DB
      span
        padding-top 0.04rem
    .contentItem
      margin-top 0.2rem
      display flex
      align-items center
      padding: 0.04rem 0
      span:first-child
        width 1.76rem
        color #4E5969
      span:last-child
        color #1D2129
    .sumUp
      .sumUpTitle
        padding 0.2rem 0
        color #1D2129
        font-weight: 600
      .sumUpContent
        padding 0.2rem
    .execute
      .executeItem
        display flex
        padding 0.2rem 0
        span:first-child
          width 2.2rem
          color #1D2129
          font-weight: 600
        span:last-child
          color #1D2129
/deep/ .van-cell::after
  border none
/deep/ .van-collapse-item--border::after
  border none
/deep/.van-collapse
  background-color #fff !important
  .van-collapse-item
    background-color #fff !important
  .van-cell
    padding 0.12rem 0.32rem
  .van-cell__title
    display flex
    justify-content space-between
    align-items center
    .inspectionConten
      display flex
      align-items center
      .project
        color #1d2129
        font-size 0.32rem
        font-weight 600
      .line {
        display inline-block
        height 0.28rem
        width 0.04rem
        margin 0 0.12rem
        background-color #1D2129
      }
  .van-collapse-item__wrapper
    background-color #fff !important
  .van-collapse-item__content
    background-color #fff !important
    padding 0 0.32rem 0 1rem
    .daily-items
      background-color #fff !important
      color #1d2129
      font-size 0.3rem
    .item-conten
      background-color #fff !important
      color #4E5969
      font-size 0.3rem
      padding 0.12rem 0
    .van-radio
      padding 0.16rem 0
/deep/ .van-checkbox__icon--checked .van-icon
  background-color #3562DB
  border-color #3562DB
/deep/ .signaturePage
  .btns
    position none !important
    width calc(100% - 10px) !important
    padding-right 10px !important
/deep/ .van-radio--disabled
  .van-radio__icon--checked
    .van-icon
      color: #fff
      background-color: #1989fa
      border-color: #1989fa
  .van-radio__label
    color: #323233
/deep/ .van-field__label > span
  color #1d2129
  font-size 0.32rem
  font-weight 600
</style>
