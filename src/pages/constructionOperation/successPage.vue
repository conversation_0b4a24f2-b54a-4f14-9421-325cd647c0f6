<template>
  <div class="success-page">
    <img :src="imgUrl" alt="" />
    <div class="success-title">申请成功</div>
    <div class="success-desc">您的作业申请已提交给各部门审核请耐心等待</div>
    <div class="footer-box">
      <van-button type="primary" @click="handleFinish" color="#3562DB">完成</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "successPage",
  data() {
    return {
      imgUrl: require("@/assets/images/construction/success-icon.png")
    };
  },
  methods: {
    handleFinish() {
      this.$router.push("/constructionOperation");
    }
  }
};
</script>

<style lang="scss" scoped>
.success-page {
  padding-top: 30px;
  text-align: center;
}
img {
  width: 40px;
  display: block;
  margin: 0 auto;
}
.success-title {
  font-size: 20px;
  font-weight: bold;
  margin-top: 10px;
  margin-bottom: 24px;
}
.success-desc {
  width: 60%;
  font-size: 14px;
  color: #9ca2a9;
  margin: 0 auto;
  text-align: center;
  line-height: 20px;
}
.footer-box {
  margin-top: 24px;
  .van-button {
    width: 90%;
  }
}
</style>
