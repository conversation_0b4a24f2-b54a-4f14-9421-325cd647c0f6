<template>
  <page-layout :title="assignmentType == 0 ? (currentStep == 2 ? '办理施工单' : '作业申请') : '作业证申请'" @back="goback">
    <div class="content">
      <div class="form-content" :style="{'padding-bottom': currentStep == 2 ? '80px' : '0'}">
        <van-form v-show="currentStep == 1">
          <van-field v-if="assignmentType == 0" readonly label="关联项目" :value="workName" placeholder="请选择关联项目" @click="showAssociatedProPicker = true" />
          <van-field :label="assignmentType == 0 ? '作业名称' : '作业证名称'" v-model="projectName" required placeholder="请输入作业名称" />
          <van-field readonly :label="assignmentType == 0 ? '作业类型' : '作业证类型'" required :value="selectedType" placeholder="请选择作业类型" @click="showTypePicker = true" />
          <van-popup v-model="showTypePicker" position="bottom">
            <van-picker :columns="formattedWorkTypes" @confirm="onTypeConfirm" @cancel="showTypePicker = false" show-toolbar />
          </van-popup>
          <van-field v-if="assignmentType == 0" readonly label="专业类型" required :value="professionaName" placeholder="请选择专业类型" @click="openProfessionaPicker" />
          <van-popup v-model="showProfessionaPicker" position="bottom">
            <van-picker :columns="formattedProfessiona" @confirm="onProfessionaConfirm" @cancel="showProfessionaPicker = false" show-toolbar />
          </van-popup>
          <van-field v-if="assignmentType == 1" readonly label="关联进行中的工程" :value="relatedEngineeringName" placeholder="请选择工程" @click="showAssProjectPicker = true" />
          <van-popup v-model="showAssProjectPicker" position="bottom">
            <search-picker
              :columns="relatedEngineeringList"
              :field-props="{
                label: 'projectName',
                value: 'id'
              }"
              @confirm="onAssProjectConfirm"
              @cancel="showAssProjectPicker = false"
            />
          </van-popup>
          <van-field :value="selectedTypeData.flowModelName" label="适用流程" readonly />
          <van-field :value="selectedTypeData.remark" label="描述" readonly />

          <template v-if="assignmentType == 0">
            <van-field label="施工地点" required>
              <template #input>
                <van-radio-group v-model="constructionLocation" direction="horizontal">
                  <van-radio :name="0">室内</van-radio>
                  <van-radio :name="1">室外</van-radio>
                </van-radio-group>
              </template>
            </van-field>

            <van-field v-if="constructionLocation == 0" readonly label="室内空间" required :value="selectedSpace" placeholder="请选择室内空间" @click="showSpacePicker = true" />
            <van-popup v-model="showSpacePicker" position="bottom">
              <div class="hiddenTrouble">
                <van-search v-model="questName" placeholder="请输入隐患类型" @input="onInput"> </van-search>
              </div>
              <van-cascader
                v-model="selectedSpaceId"
                :options="spaceList"
                :field-names="{
                  text: 'ssmName',
                  value: 'id',
                  children: 'children'
                }"
                @close="showSpacePicker = false"
                @change="onSpaceChange"
                :closeable="true"
                :check-strictly="true"
                active-color="#3562DB"
              />
            </van-popup>
            <!-- 室内空间过滤选择 -->
            <van-popup v-model="showFilterSpacePicker" duration="0" round position="bottom">
              <div class="hiddenTrouble">
                <van-search v-model="questName" placeholder="请输入室内空间" @input="onInput"> </van-search>
              </div>
              <van-picker show-toolbar :columns="filterSpaceList" title="请选择室内空间" @confirm="onFilterPickFinish" @cancel="onFinish"> </van-picker>
            </van-popup>
            <van-field v-if="constructionLocation == 1" readonly label="室外空间" required :value="selectedSpace" placeholder="请选择室外空间" @click="showOutsideSpacePicker = true" />
            <van-popup v-model="showOutsideSpacePicker" position="bottom">
              <van-cascader
                v-model="selectedSpaceId"
                :options="buildingStewardPage"
                :field-names="{
                  text: 'name',
                  value: 'id',
                  children: 'children'
                }"
                @close="showOutsideSpacePicker = false"
                @finish="outsideSpaceChange"
                :closeable="true"
                active-color="#3562DB"
              />
            </van-popup>
          </template>

          <van-popup v-model="showAssociatedProPicker" position="bottom">
            <search-picker
              :columns="associatedProList"
              :field-props="{
                label: 'workName',
                value: 'sporadicInstanceId'
              }"
              @confirm="associatedProConfirm"
              @cancel="showAssociatedProPicker = false"
            />
          </van-popup>

        </van-form>
        <yx-form v-show="currentStep == 2" :option="option" v-model="formData" :useFormSubmit="true" @submit="onFormSubmit" />
      </div>
      <div class="footer-box" v-show="currentStep == 1">
        <van-button type="primary" @click="handleSure" color="#3562DB">下一步</van-button>
      </div>
      <van-popup v-model="signatureShow">
        <signature-page @saveImg="val => onSaveImg(val)" class="signaturePage"></signature-page>
      </van-popup>
    </div>
  </page-layout>
</template>

<script>
import PageLayout from '@/components/PageLayout.vue'
import YxForm from "@/common/workFlowForm/form.vue";
import { filterAvueColumn } from "@/common/workFlowForm/formFormat.js";
import { mapState } from "vuex";
import SearchPicker from '@/components/SearchPicker.vue'
import moment from 'moment'
import SignaturePage from "@/pages/form/formDetails/components/Signature";
import axios from "axios";
export default {
  components: {
    PageLayout,
    YxForm,
    SearchPicker,
    SignaturePage
  },
  name: "workOrderApply",
  data() {
    return {
      moment,
      showTypePicker: false,
      selectedType: "",
      workTypes: [],
      selectedTypeData: {
        flowModelName: ""
      },
      constructionLocation: 0,
      spaceList: [],
      showSpacePicker: false,
      selectedSpaceId: "",
      selectedSpace: "",
      selectedSpaceCode: "",
      showOutsideSpacePicker: false,
      currentStep: 1,
      option: {},
      formData: {},
      projectName: "",
      outdoorSpace: "医院>院区>室外",
      mapImg: require("@/assets/images/construction/map.png"),
      clickPosition: { x: 0, y: 0 },
      originalImageSize: {
        width: 1920,
        height: 1080
      },
      assignmentType: "",
      buildingStewardPage: [],
      showProfessionaPicker: false,
      professionalTypeList: [],
      professionaName: "",
      professionaId: "",
      professionaPersonId: "",
      professionaPersonName: "",
      showAssProjectPicker: false,
      relatedEngineeringList: [],
      relatedEngineeringName: "",
      relatedEngineeringId: "",
      showAssociatedProPicker: false,
      associatedProList: [],
      workName: "",
      sporadicInstanceId: "",
      activeOutdoor: {},
      defaultField: {},
      signatureShow: false,
      values: null,
      showFilterSpacePicker: false,
      questName: "",
      filterSpaceList: [{ text: "加载中...", id: "loading" }],
      fieldEnumerate: ['drawing','else_file','oa'],
      hotWorkPerformer: '',
      personnelInfo: {}
    };
  },
  watch: {
    constructionLocation: {
      handler(newVal) {
        this.selectedSpace = "";
        this.selectedSpaceId = "";
        this.selectedSpaceCode = "";
      }
    },
    formData: {
      handler(newVal) {
        if(newVal['start_work_time'] && newVal['end_work_time']) {
          this.formData.total_time = this.dateDiff(newVal['end_work_time'], newVal['start_work_time'])
        }
      },
      deep: true
    }
  },
  created() {
    this.assignmentType = this.$route.query.assignmentType;
  },
  computed: {
    workType() {
      return this.workTypes.find(item => item.value === this.selectedType);
    },
    formattedWorkTypes() {
      return this.workTypes.map(type => type.name);
    },
    formattedProfessiona() {
      return this.professionalTypeList.map(item => item.name);
    },
    ...mapState(["loginInfo"])
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getWorkTypes();
    this.getSpaceList();
    this.getPersonnelInfo()
    if (this.assignmentType == 0) {
      this.getBuildingStewardPage();
      this.getAssociatedProList()
    } else {
      this.getRelatedEngineeringList();
    }
  },
  methods: {
    handleDownLoadFiles() {
       api.download({
          url: __PATH.SPACE_API + '/assignmentInfo/PersonnelLedgerTemplate',
          savePath: 'fs://download/' + '人员台账模板.xlsx', // 保存路径
          report: true,
          cache: true,
          allowResume: true,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem("token")
          }
        }, function(ret, err) {
          console.log("下载结果", ret);
          if (ret && ret.state == 1) {
            // 下载成功
            console.log("下载成功", ret);
            api.toast({
              msg: '下载成功，文件存储路径：' + ret.savePath,
              duration: 2000,
              location: 'middle'
            });
          } else {
            // 下载失败
            console.log("下载失败", err);
            api.toast({
              msg: '下载失败',
              duration: 2000,
              location: 'middle'
            });
          }
        });
    },
    getPersonnelInfo() {
      this.$api.getPersonnelInfo({
        id: this.loginInfo.staffId
      }).then(res => {
        console.log("人员信息", res);
        this.personnelInfo = res
      })
    },
    // 室内空间 搜索
    onInput(val) {
      const searchKey = val.trim();
      if (!searchKey) {
        this.showFilterSpacePicker = false;
        this.showSpacePicker = true;
      } else {
        this.showSpacePicker = false;
        this.showFilterSpacePicker = true;
        const filterTree = nodes => {
          return nodes
            .filter(node => {
              const hasMatch = node.ssmName ? node.ssmName.includes(searchKey) : "";
              const children = node.children ? filterTree(node.children) : [];
              return hasMatch || children.length > 0;
            })
            .map(node => ({
              ...node,
              children: node.children ? filterTree(node.children) : []
            }));
        };
        this.filterSpaceList = this.flattenLeaves(filterTree(this.spaceList));
      }
    },
    // 数组扁平化并过滤非叶子节点
    flattenLeaves(tree) {
      const result = [];
      const traverse = (node, parentNames = [], parentIds = []) => {
        const currentNames = [...parentNames, node.ssmName];
        const currentIds = [...parentIds, node.id];

        // 判断是否为叶子节点（无子节点）
        if (!node.children || node.children.length === 0) {
          result.push({
            text: currentNames.join("/"),
            id: currentIds.join(",")
          });
        } else {
          // 非叶子节点继续递归子节点
          node.children.forEach(child => traverse(child, currentNames, currentIds));
        }
      };
      tree.forEach(root => traverse(root));
      return result;
    },
    // 室内空间过滤选择确认
    onFilterPickFinish(_, index) {
      const item = this.filterSpaceList[index];
      // 如果没有pick到数据则关闭列表
      if (!item || item.id === "loading") {
        this.resetFilter();
        return;
      }
      this.selectedSpace = item.text.replaceAll(" / ", ",");
      this.selectedSpaceCode = item.id;
      // 设置级联选择器的选中值
      const targetIds = item.id.split(",");
      this.selectedSpaceId = targetIds.pop();
      this.resetFilter();
    },
    // 室内空间过滤选择取消
    onFinish() {
      this.showSpacePicker = false;
      this.resetFilter();
    },
    // 重置室内空间选择过滤器
    resetFilter() {
      this.questName = "";
      this.showFilterSpacePicker = false;
      this.filterSpaceList = [{ text: "加载中...", id: "loading" }];
    },
    handleSure() {
      // 校验
      if (!this.projectName) {
        this.$toast(this.assignmentType == 0 ? "请输入作业名称" : "请输入作业证名称");
        return;
      }
      if (!this.selectedType) {
        this.$toast(this.assignmentType == 0 ? "请选择作业类型" : "请选择作业证类型");
        return;
      }
      if(!this.professionaId && this.assignmentType == 0){
        this.$toast("请选择专业类型");
        return;
      }

      // 只有作业类型(assignmentType == 0)才需要校验施工地点
      if (this.assignmentType == 0) {
        if (this.constructionLocation !== 0 && this.constructionLocation !== 1) {
          this.$toast("请选择施工地点");
          return;
        }
        if (this.constructionLocation == 0 && !this.selectedSpace) {
          this.$toast("请选择室内空间");
          return;
        }
      }

      this.currentStep = 2;
      this.getStartWorkFlowForm();
    },
    dateDiff(d1, d2) {
      const date1 = new Date(d1)
      if (isNaN(date1.getTime())) throw new Error('Invalid date d1')
      const date2 = d2 ? new Date(d2) : new Date()
      if (d2 && isNaN(date2.getTime())) throw new Error('Invalid date d2')
      const msDiff = Math.abs(date1 - date2)
      const days = Math.round((msDiff / (1000 * 60 * 60 * 24)) * 10) / 10 // 保留 1 位小数
      return days
    },
    getStartWorkFlowForm() {
      this.$api
        .getStartWorkFlowForm({
          flowDefinitionId: this.selectedTypeData.flowDefinitionId
        })
        .then(res => {
          res && this.getWorkerFormOptions(res);
        });
    },
    getWorkerFormOptions(formFormat) {
      const { startForm, form } = JSON.parse(formFormat);
      const option = eval("(" + form + ")");
      const { column, group } = option;
      const groupArr = [];
      const columnArr = filterAvueColumn(column, startForm, false, undefined, this).column;
      if (group && group.length > 0) {
        // 处理group
        group.forEach(gro => {
          gro.column = filterAvueColumn(gro.column, startForm, false, undefined, this).column;
          if (gro.column.length > 0) groupArr.push(gro);
        });
      }
      option.column = columnArr;
      if (JSON.stringify(this.defaultField) != "{}") {
        for (const key in this.defaultField) {
          option.column.forEach(item => {
            if (item.prop == key) {
              if(this.fieldEnumerate.includes(item.prop)){
                item.value = this.defaultField[key] && this.defaultField[key].length ? this.defaultField[key].map(item=>{
                  return {
                    name: item.name,
                    url: this.$YBS.imgUrlTranslation(item.url)
                  }
                }) : [];
              } else {
                item.value = this.defaultField[key] || "";
              }
            }
          });
        }
      }
      option.group = groupArr;
      option.menuBtn = false;
      this.option = option;
      this.option.column.forEach(item => {
        // if (item.type === 'radio') {
        //   this.$set(this.formData, item.prop, item.value || '');
        // }
        this.$set(this.formData, item.prop, item.value || "");
      });
      console.log("option", option, this.formData);
    },
    getWorkTypes() {
      this.$api.getWorkTypes({ assignmentType: this.assignmentType }).then(res => {
        this.workTypes = res;
      });
    },
    // 获取专业类型列表
    getProfessionalTypeList() {
      const params = {
        businessFormId: this.selectedTypeData.id,
        status: 1
      }
      this.$api.professionalTypeList(params).then(res => {
        console.log("专业类型列表", res);
        this.professionalTypeList = res
      });
    },
    // 打开专业类型选择
    openProfessionaPicker() {
      if (!this.selectedType) {
        this.$toast("请选择作业类型");
        return;
      }
      this.showProfessionaPicker = true;
    },
    // 获取楼宇列表
    getBuildingStewardPage() {
      const params = {
        page: 1,
        pageSize: 999
      }
      this.$api.buildingStewardPage(params).then(res => {
        const { records } = res;
        if (records.length > 0) {
          this.buildingStewardPage = records.map(item => {
            const regionList = ['东区', '南区', '西区', '北区'];
            return {
              id: item.buildingId,
              name: item.buildingName,
              leader: item.leader,
              leaderId: item.leaderId,
              children: Array.from({ length: 4 }, (_, index) => ({
                id: index,
                name: regionList[index],
                allSpaceName: item.buildingName + '>' + regionList[index],
                allSpaceId: item.buildingId + ',' + index
              }))
            }
          })
          console.log("楼宇列表", this.buildingStewardPage);
        } else {
          this.buildingStewardPage = [];
        }
      });
    },
    // 专业类型选择
    onProfessionaConfirm(value, index) {
      this.professionaName = value;
      this.professionaId = this.professionalTypeList[index].id;
      this.professionaPersonId = this.professionalTypeList[index].leaderId;
      this.professionaPersonName = this.professionalTypeList[index].leader;
      this.showProfessionaPicker = false;
    },
    // 关联项目选择
    onAssProjectConfirm({value, item}) {
      this.relatedEngineeringName = item.projectName;
      this.relatedEngineeringId = item.id;
      this.showAssProjectPicker = false;
    },
    // 获取关联项目列表
    getRelatedEngineeringList() {
      const params = {
        page: 1,
        pageSize: 999,
        assignmentType: 0
      }
      this.$api.getApprovalList(params).then(res => {
        const { records } = res;
        if (records.length > 0) {
          this.relatedEngineeringList = records;
        } else {
          this.relatedEngineeringList = [];
        }
      });
    },
    // 获取关联项目列表
    getAssociatedProList() {
      const params = {
        deptId: this.loginInfo.deptId
      }
      this.$api.getAssociatedProList(params).then(res => {
        if (res.length > 0) {
          this.associatedProList = res;
        } else {
          this.associatedProList = [];
        }
      });
    },
    // 关联项目选择
    associatedProConfirm({value, item}) {
      this.workName = item.workName;
      this.projectName = item.workName;
      this.sporadicInstanceId = item.sporadicInstanceId;
      this.showAssociatedProPicker = false;
      this.getProjectDefaultField()
    },
    // 关联项目 对应默认字段
    getProjectDefaultField() {
      let params = {
        instanceId: this.sporadicInstanceId
      }
      this.$api.getZysqProjectDefaultField(params).then((res) => {
        this.defaultField = res
      })
    },
    // 获取室内空间列表
    getSpaceList() {
      this.$api.getspaceAll().then(res => {
        this.spaceList = this.arrayToTree(res);
      });
    },
    arrayToTree(items) {
      const rootItems = items.filter(item => item.pid === "#");
      const buildTree = parent => {
        const children = items.filter(item => item.pid === parent.id);
        if (children.length > 0) {
          parent.children = children.map(child => buildTree(child));
        }
        return parent;
      };
      return rootItems.map(root => buildTree(root));
    },
    onTypeConfirm(value, index) {
      this.selectedType = value;
      this.selectedTypeData = this.workTypes[index];
      this.showTypePicker = false;
      // 切换作业类型时获取对应专业类型列表
      this.getProfessionalTypeList();
    },
    onSpaceChange({ value, selectedOptions }) {
      const lastOption = selectedOptions[selectedOptions.length - 1];
      if (lastOption.ssmType >= 3) {
        this.selectedSpace = lastOption.allSpaceName;
        this.selectedSpaceId = lastOption.id;
        this.selectedSpaceCode = lastOption.parentGridIds ? `${lastOption.parentGridIds},${lastOption.id}` : lastOption.id;
        this.selectedSpaceCode =  this.selectedSpaceCode.replace('#,', "");
      }
    },
    outsideSpaceChange({ selectedOptions }) {
      const lastOption = selectedOptions[selectedOptions.length - 1];
      this.selectedSpace = lastOption.allSpaceName;
      this.selectedSpaceId = lastOption.id;
      this.selectedSpaceCode = lastOption.allSpaceId;
      this.activeOutdoor = selectedOptions[0]
    },
    onFormSubmit(values) {
      this.values = values
      // this.signatureShow = true
      this.subParams()
    },
    subParams(valuse) {
      let flowFormList = [];
      const baseAddress = localStorage.getItem("picPrefix");
      Object.keys(this.formData).forEach(key => flowFormList.push({ key: key, value: this.formData[key] }));
      if(flowFormList && flowFormList.length > 0){
        flowFormList.forEach(item => {
          if(this.fieldEnumerate.some(input => input.trim() === item.key)){
            if(item.value && item.value.length > 0){
              item.value.forEach(i=>{
                i.url = i.url ? i.url.replace(`${baseAddress}/`,'') : ''
              });
            }
          }
        });
      }
      let hotWorkPerformer = [{
        url: this.personnelInfo.signUrl,
        type: 'shouqian',
        userId: this.loginInfo.staffId,
        name: 'canvas.png'
      }]
      console.log("提交的表单数据:", flowFormList);
      let projectInfoAddDto = {
        assignmentType: this.assignmentType,
        businessFormId: this.selectedTypeData.id,
        businessFormName: this.selectedTypeData.name,
        description: this.formData.description,
        hospitalCode: this.loginInfo.hospitalCode,
        locationType: this.constructionLocation,
        projectName: this.projectName,
        sporadicInstanceId: this.sporadicInstanceId, // 作业关联项目实例id
        workName: this.workName, // 作业关联项目名称
        relatedEngineeringId: this.relatedEngineeringId, // 作业证关联项目id
        relatedEngineeringName: this.relatedEngineeringName, // 作业证关联项目名称
        professionaId: this.professionaId, // 专业类型id
        professionaName: this.professionaName, // 专业类型名称
        professionaPersonId: this.professionaPersonId, // 专业类型负责人id
        professionaPersonName: this.professionaPersonName, // 专业类型负责人名称
        projectLocationId: this.selectedSpaceCode, // 施工地点id
        projectLocationName: this.selectedSpace, // 施工地点名称
        unitCode: this.loginInfo.unitCode,
        hotWorkPerformer: JSON.stringify(hotWorkPerformer),
      };
      // 室外时增加绑定人员
      if (this.constructionLocation == 1) {
        projectInfoAddDto.projectLocationPersonId = this.activeOutdoor.leaderId // 地点绑定的人员id
        projectInfoAddDto.projectLocationPersonName = this.activeOutdoor.leader // 地点绑定的人员名称
      }
      let params = {
        flowFormList,
        projectInfoAddDto
      };
      console.log("params", params);
      this.$api.createAssignment(params).then(res => {
        console.log("res", res);
        if (res) {
          this.$router.push("/successPage");
        }
      }).catch(err => {
        console.log("err", err);
        this.$toast(err.data.msg);
      })
    },
    handleMapClick(event) {
      const rect = event.target.getBoundingClientRect();
      const displayWidth = rect.width;
      const displayHeight = rect.height;

      // 获取点击的相对位置
      const clickX = event.clientX - rect.left;
      const clickY = event.clientY - rect.top;

      // 计算缩放比例
      const scaleX = this.originalImageSize.width / displayWidth;
      const scaleY = this.originalImageSize.height / displayHeight;

      // 计算在原图上的坐标位置
      const originalX = Math.round(clickX * scaleX);
      const originalY = Math.round(clickY * scaleY);

      this.clickPosition = {
        x: originalX,
        y: originalY,
        displayX: clickX, // 保存显示位置
        displayY: clickY
      };

      console.log("原图对应坐标位置：", this.clickPosition);
    },
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    // 保存签名
    onSaveImg(val) {
      this.signatureImage = val
      // base64转流
      let arr = val.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      const file = new File([u8arr], 'signature.png', { type: mime });
      let formData = new FormData();
      formData.append("file", file);
      axios({
        method: "post",
        url: __PATH.AUTH_WEB + "/SysMenu/upload",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: localStorage.getItem("token"),
          'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
        }
      })
        .then(res => {
          if (res.data.code == 200) {
            const hotWorkPerformer = {
              url: res.data.data,
              type: 'shouqian',
              userId: this.loginInfo.staffId,
              name: ''
            }
            this.hotWorkPerformer = JSON.stringify([hotWorkPerformer])
            this.subParams()
          }
          this.signatureShow = false
        })
        .catch(() => {
          this.$toast.fail("签名保存失败");
          this.hotWorkPerformer = ''
          this.signatureShow = false
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f2f4f9;

  .form-content {
    flex: 1;
    overflow-y: auto;
  }
}

.footer-box {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;

  > button {
    width: 100%;
  }
}

.map-img {
  width: 95%;
  margin: 0 auto;
  display: block;
  margin-top: 10px;
  border-radius: 10px;
}
.map-container {
  position: relative;
  width: 95%;
  margin: 10px auto;
}

.marker {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: red;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}
</style>

