<template>
  <page-layout @back="goback">
    <div class="work-account-details">
      <div :class="['status-box', getWorkStatusClass(itemData.status)]">
        <van-icon name="clock" />
        <div class="status-tag">{{ getWorkStatusText(itemData.status) }}</div>
      </div>
      <van-tabs v-model="activeTab" color="#3562DB" class="tabs-container">
        <van-tab title="申请信息">
          <div class="tab-content">
            <div class="parting-line"></div>
            <div class="form-wrapper">
              <yx-form ref="yxForm" :option="formOption" v-model="formData" :useFormSubmit="false" :disabled="true" @formSkipView="skipView" />
            </div>
          </div>
        </van-tab>
        <van-tab title="流转信息">
          <div class="tab-content">
            <div class="parting-line"></div>
            <flow-info :flows="flows" />
          </div>
        </van-tab>
        <van-tab title="过程管理">
          <div class="parting-line"></div>
          <yx-form
            v-if="
              processFieldsOption && ((processFieldsOption.column && processFieldsOption.column.length > 0) || (processFieldsOption.group && processFieldsOption.group.length > 0))
            "
            v-model="formData"
            :option="processFieldsOption"
          ></yx-form>
          <div v-else style="text-align: center; padding: 10px 16px; background-color: #fff">暂无更多</div>
        </van-tab>
        <van-tab title="作业证">
          <div class="parting-line"></div>
          <div v-if="workCertificate.length" class="fileList">
            <div v-for="(i, index) in workCertificate" :key="index" class="fileItem" @click="clickFile(i)">
              <img src="../../assets/images/construction/file-icon.png" height="40" width="40" alt="" />
              <div class="file-name">{{ i.workName }}</div>
            </div>
          </div>
          <div v-else style="text-align: center; padding: 10px 16px; background-color: #fff">暂无更多</div>
        </van-tab>
        <van-tab title="施工验收">
          <div class="parting-line"></div>
          <yx-form
            v-if="
              acceptanceFheckFieldsOption &&
              ((acceptanceFheckFieldsOption.column && acceptanceFheckFieldsOption.column.length > 0) ||
                (acceptanceFheckFieldsOption.group && acceptanceFheckFieldsOption.group.length > 0))
            "
            v-model="formData"
            :option="acceptanceFheckFieldsOption"
          ></yx-form>
          <div v-else style="text-align: center; padding: 10px 16px; background-color: #fff">暂无更多</div>
        </van-tab>
      </van-tabs>
    </div>
  </page-layout>
</template>

<script>
import PageLayout from "@/components/PageLayout.vue";
import YxForm from "@/common/workFlowForm/form.vue";
import FlowInfo from "@/common/FlowInfo.vue";
import { filterAvueColumn, validateNull, deepClone, getDefaultValues } from "@/common/workFlowForm/formFormat.js";
import { mapState } from "vuex";
import { ImagePreview } from "vant";
import store from "@/store";
export default {
  name: "workAccountDetails",
  components: {
    PageLayout,
    YxForm,
    FlowInfo
  },
  data() {
    return {
      itemData: {},
      formOption: {},
      formData: {},
      buttonList: [],
      process: {},
      option: {},
      vars: {},
      form: {},
      flows: [],
      activeTab: 0,
      processFields: [],
      acceptanceFheckFields: [],
      hideFields: [],
      processFieldsOption: [],
      acceptanceFheckFieldsOption: [],
      workCertificate: []
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.itemData = JSON.parse(this.$route.query.item);
    this.getStartWorkFlowForm();
    if (this.itemData.assignmentType == 0) {
      this.detailsTabs("1");
      this.detailsTabs("2");
    }
    this.detailsTabs("3");
    this.getWorkCardDetail();
  },
  methods: {
    // 跳转页面
    skipView(val) {
      if (val && val.prop === "construction_unit") {
        this.$router.push({
          path: "/addUnit",
          query: {
            id: val.id,
            type: "edit",
            showIcon: true
          }
        });
      }
      if (val && val.prop === "site_responsible_person") {
        this.$router.push({
          path: "/addPersonnel",
          query: {
            id: val.id,
            type: "edit",
            showIcon: true
          }
        });
      }
    },
    getStartWorkFlowForm() {
      this.$api.getDetailByWorkFlow({ processInstanceId: this.itemData.processInstanceId }).then(res => {
        res && this.getFormOptions(res);
        this.flows = JSON.parse(res).flow;
      });
    },
    handleResolveOption(option, taskForm, status) {
      let { column, group } = option;
      let vars = [];
      if (taskForm && taskForm.length > 0) {
        const columnFilter = filterAvueColumn(column, taskForm, false, undefined, this);
        column = columnFilter.column;
        vars = columnFilter.vars || [];
        const groupArr = [];
        if (group && group.length > 0) {
          // 处理group
          group.forEach(gro => {
            const groupFilter = filterAvueColumn(gro.column, taskForm, false, undefined, this);
            gro.column = groupFilter.column;
            vars = vars.concat(groupFilter.vars);
            if (gro.column.length > 0) groupArr.push(gro);
          });
        }
        group = groupArr;
      }

      if (status != "todo") {
        // 已办，删除字段默认值
        option.detail = true;
      }
      column.forEach(item => {
        if (item.prop === "construction_unit" || item.prop === "site_responsible_person") {
          item.showIconClick = true;
        } else {
          item.showIconClick = false;
        }
      });
      option.column = column;
      option.group = group;
      return { option, vars };
    },
    getFormOptions(formFormat) {
      const { process, form, button } = JSON.parse(formFormat);
      this.buttonList = button;
      this.process = process;
      const { variables, status } = process;
      const { allForm, taskForm, formList } = form;
      if (!allForm && !taskForm && !formList) {
        this.$toast.fail("获取到表单信息或流程已不存在!");
        return;
      }
      if (allForm) {
        const { option, vars } = this.handleResolveOption(eval("(" + allForm + ")"), taskForm, status);
        option.menuBtn = false;
        for (const key in variables) {
          if (validateNull(variables[key])) delete variables[key];
          if (typeof variables[key] === 'string' && variables[key].includes('${') && variables[key].includes('}')) {
            variables[key] = getDefaultValues(variables[key]);
          }
        }
        option.column.forEach(i => {
          if (i.value && i.value.includes("${") && i.value.includes("}")) {
            const funStr = i.value.replace(/\$\{(.*?)\}/g, "$1");
            i.value = eval("`" + funStr + "`");
            const regex = /flowGenericUtils\(\s*"([^"]+)"\s*,\s*"([^"]+)"\s*\)/;
            const match = funStr.match(regex);
            i.value = this.flowGenericUtils(match[1], match[2]);
          }
          const isHideField = this.hideFields.includes(i.prop);
          i.display = !isHideField;
          i.disabled = true;
          i.readonly = true;
        });

        // 处理表单组
        if (option.group) {
          option.group.forEach(group => {
            if (group.column) {
              group.column.forEach(item => {
                const isHideField = this.hideFields.includes(item.prop);
                item.display = !isHideField;
                item.disabled = true;
                item.readonly = true;
              });
            }
          });
        }

        let fieldsOption = deepClone(option);
        if (this.processFields.length) {
          let fieldsColumn = [];
          fieldsOption.column.forEach((item, index) => {
            if (this.processFields.includes(item.prop)) {
              item.display = true;
              item.disabled = true;
              item.readonly = true;
              fieldsColumn.push(item);
            }
          });
          this.processFieldsOption = { ...fieldsOption };
          this.processFieldsOption.column = fieldsColumn;
        }
        if (this.acceptanceFheckFields.length) {
          let fieldsColumn = [];
          fieldsOption.column.forEach((item, index) => {
            if (this.acceptanceFheckFields.includes(item.prop)) {
              item.display = true;
              item.disabled = true;
              item.readonly = true;
              fieldsColumn.push(item);
            }
          });
          this.acceptanceFheckFieldsOption = { ...fieldsOption };
          this.acceptanceFheckFieldsOption.column = fieldsColumn;
        }
        this.option = option;
        this.formOption = option;
        this.vars = vars;
      }
      this.formData = { ...variables };
    },
    getWorkStatusText(status) {
      const statusMap = {
        unfinished: "进行中",
        draft: "暂存",
        terminate: "已终结",
        finished: "已完成",
        reject: "驳回"
      };
      return statusMap[status] || "未知状态";
    },
    getWorkStatusClass(status) {
      const statusClassMap = {
        unfinished: "work-in-progress",
        draft: "work-not-started",
        terminate: "work-overtime",
        finished: "work-finished",
        reject: "work-overtime"
      };
      return statusClassMap[status] || "unknown";
    },
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    detailsTabs(type) {
      this.$api
        .detailsTabs({
          tabType: type,
          flowType: this.$route.query.flowType == "admission_qj" ? "1" : this.$route.query.flowType == "admission_gcwx" ? "2" : "-1"
        })
        .then(res => {
          if (type == "1") this.processFields = res;
          if (type == "2") this.acceptanceFheckFields = res;
          if (type == "3") this.hideFields = res;
        });
    },
    getWorkCardDetail() {
      const params = {
        page: 1,
        pageSize: 999,
        instanceId: this.itemData.processInstanceId,
        projectCode: this.itemData.projectCode
      };
      this.$api.workCardDetail(params).then(res => {
        this.workCertificate = res;
      });
    },
    clickFile(i) {
      const fileType = i.workName.slice(i.workName.lastIndexOf(".") + 1, i.workName.length);
      if (fileType.toLowerCase() === "jpg" || fileType.toLowerCase() === "jpeg" || fileType.toLowerCase() === "png") {
        ImagePreview({
          images: [this.$YBS.imgUrlTranslation(i.fileUrl)],
          showIndex: false // 是否显示页码
        });
      } else {
        navigator.clipboard.writeText(i.fileUrl);
        this.$toast("暂不支持预览，已复制链接请粘贴至浏览器下载查看");
      }
    },
    flowGenericUtils(type, format) {
      if (type === "dateFormat") {
        const date = new Date();
        const map = {
          YYYY: date.getFullYear(),
          MM: String(date.getMonth() + 1).padStart(2, "0"),
          DD: String(date.getDate()).padStart(2, "0"),
          HH: String(date.getHours()).padStart(2, "0"),
          mm: String(date.getMinutes()).padStart(2, "0"),
          ss: String(date.getSeconds()).padStart(2, "0")
        };
        return Object.keys(map).reduce((result, key) => result.replace(key, map[key]), format);
      } else if (type === "userInfo") {
        return store.state.loginInfo[format];
      } else if (type === "getTime") {
        return new Date().getTime();
      }
      return "";
    }
  }
};
</script>

<style lang="scss" scoped>
.work-account-details {
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.status-box {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 12px;
  border-radius: 4px;

  .status-tag {
    margin-left: 10px;
  }

  // 进行中状态
  &.work-in-progress {
    background-color: rgba(64, 158, 255, 0.1);
    .van-icon,
    .status-tag {
      color: #409eff;
    }
  }

  // 暂存状态
  &.work-not-started {
    background-color: rgba(144, 147, 153, 0.1);
    .van-icon,
    .status-tag {
      color: #909399;
    }
  }

  // 已终结/驳回状态
  &.work-overtime {
    background-color: rgba(245, 108, 108, 0.1);
    .van-icon,
    .status-tag {
      color: #f56c6c;
    }
  }

  // 已完成状态
  &.work-finished {
    background-color: rgba(103, 194, 58, 0.1);
    .van-icon,
    .status-tag {
      color: #67c23a;
    }
  }

  // 未知状态
  &.unknown {
    background-color: rgba(144, 147, 153, 0.1);
    .van-icon,
    .status-tag {
      color: #909399;
    }
  }
}
.footer-box {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 10px;
  padding: 12px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);

  .van-button {
    flex: 1;
  }
}
.sign-img {
  display: flex;
  span {
    flex-shrink: 0;
  }
}

.tabs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/deep/ .van-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;

  .van-tabs__wrap {
    flex-shrink: 0;
  }

  .van-tabs__content {
    flex: 1;
    overflow: hidden;
  }

  .van-tab__pane {
    height: 100%;
  }
}

.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.parting-line {
  flex-shrink: 0;
}

.form-wrapper,
.flow-wrapper {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.fileList {
  background: #fff;
  padding: 16px;
  .fileItem {
    position: relative;
    padding: 8px 16px;
    background: #f7f8fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .file-name {
      width: calc(100% - 50px);
      margin-left: 10px;
      color: #1d2129;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .fileItem:last-child {
    margin-bottom: 0;
  }
}
.van-tab__pane {
  overflow: auto;
}
</style>
