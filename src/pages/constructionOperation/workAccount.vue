<template>
  <page-layout @back="goback">
    <van-tabs v-model="activeTab" color="#3562DB" class="tabs-container">
      <van-tab title="全部作业申请">
        <div class="tab-content">
          <div class="count-box-wrapper">
            <div class="count-box">
              <div class="count-item" v-for="(item, index) in countItems" :key="index" :class="{ active: selectedIndex === index }" @click="changeCount(index)">
                <div class="count-item-num">{{ item.num }}</div>
                <div class="count-item-title">{{ item.title }}</div>
              </div>
            </div>
          </div>
          <div class="list-wrapper">
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100" :immediate-check="false">
              <div class="card" v-for="item in accountList" :key="item.id" @click="goDetails(item)">
                <div style="position: relative">
                  <span class="name-style">{{ item.projectName }}</span>
                  <div class="right-content">
                    <div :class="['status-tag', getStatusClass(item.projectStatusCode)]">{{ getStatusText(item.projectStatusCode) }}</div>
                    <van-icon name="arrow" />
                  </div>
                </div>
                <div>
                  <span>类型：</span>
                  <span>{{ item.businessFormName }}</span>
                </div>
                <div>
                  <span>发起人：</span>
                  <span>{{ item.createName }}</span>
                </div>
                <div>
                  <span>施工单位：</span>
                  <span>{{ item.constructionUnitName }}</span>
                </div>
                <div class="line"></div>
                <div style="display: flex; gap: 8px; align-items: center">
                  <div :class="['status-tag', getWorkStatusClass(item.status)]">{{ getWorkStatusText(item.status) }}</div>
                  <div>
                    <img class="node-img" :src="nodeImg" />
                    <span style="font-size: 14px; color: #ff7d00">当前节点：{{ item.currentNodeName }}</span>
                  </div>
                </div>
              </div>
            </van-list>
          </div>
        </div>
      </van-tab>
      <van-tab title="全部作业证申请">
        <div class="tab-content">
          <div class="count-box-wrapper">
            <div class="count-box">
              <div class="count-item" v-for="(item, index) in countItems2" :key="index" :class="{ active: selectedIndex2 === index }" @click="changeCount2(index)">
                <div class="count-item-num">{{ item.num }}</div>
                <div class="count-item-title">{{ item.title }}</div>
              </div>
            </div>
          </div>
          <div class="list-wrapper">
            <van-list v-model="loading2" :finished="finished2" finished-text="没有更多了" @load="onLoad2" offset="100" :immediate-check="false">
              <div class="card" v-for="item in accountList2" :key="item.id" @click="goDetails(item)">
                <div style="position: relative">
                  <span class="name-style">{{ item.projectName }}</span>
                  <div class="right-content">
                    <div :class="['status-tag', getStatusClass(item.projectStatusCode)]">{{ getStatusText(item.projectStatusCode) }}</div>
                    <van-icon name="arrow" />
                  </div>
                </div>
                <div>
                  <span>类型：</span>
                  <span>{{ item.businessFormName }}</span>
                </div>
                <div>
                  <span>发起人：</span>
                  <span>{{ item.createName }}</span>
                </div>
                <div>
                  <span>施工单位：</span>
                  <span>{{ item.constructionUnitName }}</span>
                </div>
                <div class="line"></div>
                <div style="display: flex; gap: 8px; align-items: center">
                  <div :class="['status-tag', getWorkStatusClass(item.status)]">{{ getWorkStatusText(item.status) }}</div>
                  <div>
                    <img class="node-img" :src="nodeImg" />
                    <span style="font-size: 14px; color: #ff7d00">当前节点：{{ item.currentNodeName }}</span>
                  </div>
                </div>
              </div>
            </van-list>
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </page-layout>
</template>

<script>
import PageLayout from '@/components/PageLayout.vue'

export default {
  components: {
    PageLayout
  },
  data() {
    return {
      accountList: [],
      page: 1,
      pageSize: 10,
      loading: false,
      finished: false,
      activeTab: 0,
      selectedIndex: 0,
      countItems: [
        { num: 0, title: "全部" },
        { num: 0, title: "未开始" },
        { num: 0, title: "进行中" },
        { num: 0, title: "已结束" },
        { num: 0, title: "超时" }
      ],
      countItems2: [
        { num: 0, title: "全部" },
        { num: 0, title: "未开始" },
        { num: 0, title: "进行中" },
        { num: 0, title: "已结束" },
        { num: 0, title: "超时" }
      ],
      nodeImg: require("@/assets/images/construction/node-icon.png"),
      selectedIndex2: 0,
      accountList2: [],
      loading2: false,
      finished2: false,
      page2: 1
    };
  },
  watch: {
    activeTab: {
      handler(newVal) {
        if (newVal === 0) {
          this.page = 1;
          this.accountList = [];
          this.finished = false;
          this.getList();
        } else {
          this.page2 = 1;
          this.accountList2 = [];
          this.finished2 = false;
          this.getList2();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getCount();
    this.getCount2();
  },
  methods: {
    changeCount(index) {
      this.selectedIndex = index;
      this.page = 1;
      this.getList();
    },
    getStatusText(status) {
      const statusMap = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
        3: "已超时"
      };
      return statusMap[status] || "未知状态";
    },
    getStatusClass(status) {
      const statusClassMap = {
        0: "not-started",
        1: "in-progress",
        2: "finished",
        3: "overtime"
      };
      return statusClassMap[status] || "unknown";
    },
    getWorkStatusText(status) {
      const statusMap = {
        unfinished: "进行中",
        draft: "暂存",
        terminate: "已终结",
        finished: "已完成",
        reject: "驳回"
      };
      return statusMap[status] || "未知状态";
    },
    getWorkStatusClass(status) {
      const statusClassMap = {
        unfinished: "work-in-progress",
        draft: "work-not-started",
        terminate: "work-overtime",
        finished: "work-finished",
        reject: "work-overtime"
      };
      return statusClassMap[status] || "unknown";
    },
    getCount() {
      this.$api
        .getAssignmentTypeStatistics({
          assignmentType: 0
        })
        .then(res => {
          this.countItems[0].num = res.sum || 0; // 全部
          this.countItems[1].num = res.haveNotStarted || 0; // 未开始
          this.countItems[2].num = res.underwayCount || 0; // 进行中
          this.countItems[3].num = res.finishCount || 0; // 已结束
          this.countItems[4].num = res.overtimeCount || 0; // 超时
        });
    },
    getCount2() {
      this.$api
        .getAssignmentTypeStatistics({
          assignmentType: 1
        })
        .then(res => {
          this.countItems2[0].num = res.sum || 0; // 全部
          this.countItems2[1].num = res.haveNotStarted || 0; // 未开始
          this.countItems2[2].num = res.underwayCount || 0; // 进行中
          this.countItems2[3].num = res.finishCount || 0; // 已结束
          this.countItems2[4].num = res.overtimeCount || 0; // 超时
        });
    },
    getList() {
      this.$api
        .getAssignmentList({
          assignmentType: this.activeTab,
          ...(this.selectedIndex !== 0 && { assignmentStatus: this.selectedIndex - 1 }),
          page: this.page,
          pageSize: this.pageSize
        })
        .then(res => {
          this.accountList = this.page === 1 ? res.records : [...this.accountList, ...res.records];
          this.loading = false;
          this.finished = this.accountList.length >= res.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    onLoad() {
      this.page++;
      this.getList();
    },
    goDetails(item) {
      this.$router.push({ path: "/workAccountDetails", query: { item: JSON.stringify(item), flowType:item.code } });
    },
    changeCount2(index) {
      this.selectedIndex2 = index;
      this.page2 = 1;
      this.getList2();
    },
    getList2() {
      this.$api
        .getAssignmentList({
          assignmentType: this.activeTab,
          ...(this.selectedIndex2 !== 0 && { assignmentStatus: this.selectedIndex2 - 1 }),
          page: this.page2,
          pageSize: this.pageSize
        })
        .then(res => {
          this.accountList2 = this.page2 === 1 ? res.records : [...this.accountList2, ...res.records];
          this.loading2 = false;
          this.finished2 = this.accountList2.length >= res.total;
        })
        .catch(() => {
          this.loading2 = false;
        });
    },
    onLoad2() {
      this.page2++;
      this.getList2();
    },
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style scoped lang="scss">
.tabs-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/deep/ .van-tabs__content {
  flex: 1;
  overflow: hidden;
}

.tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.count-box-wrapper {
  flex-shrink: 0;
  background: #fff;
}

.count-box {
  display: flex;
  padding: 15px;
  white-space: nowrap;
  min-width: min-content;
}

.count-item {
  flex-shrink: 0;
  margin-right: 12px;

  &:last-child {
    margin-right: 0;
  }

  text-align: center;
  padding: 8px 12px;
  border-radius: 4px;

  &.active {
    background: linear-gradient(180deg, #ffffff 27%, #f0f5ff 100%);

    .count-item-num,
    .count-item-title {
      color: #3562db;
    }
  }

  &-num {
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin-bottom: 8px;
  }

  &-title {
    font-size: 14px;
    color: #666;
  }
}
.card {
  background-color: #fff;
  border-radius: 8px;
  font-size: 16px;
  padding: 12px;
  margin-bottom: 12px;
  padding-top: 16px;
  div > span:nth-child(1) {
    color: #4e5969;
  }
  div > span:nth-child(2) {
    color: #1d2129;
  }
}
.card > div {
  margin-bottom: 12px;
}
.name-style {
  color: hsl(220, 17%, 14%) !important;
  font-size: 16px !important;
  font-weight: bold !important;
}
.right-content {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
}
.status-tag {
  padding: 0 6px;
  height: 24px;
  border-radius: 4px;
  font-size: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &.not-started {
    color: #faad14;
    background: #fff7e6;
  }

  &.in-progress {
    color: #1677ff;
    background: #e6f4ff;
  }

  &.finished {
    color: #52c41a;
    background: #f6ffed;
  }

  &.overtime {
    color: #ff4d4f;
    background: #fff1f0;
  }

  &.unknown {
    color: #8c8c8c;
    background: #f5f5f5;
  }

  &.work-not-started {
    color: #faad14;
    background: #fff;
    border: 1px solid #faad14;
  }

  &.work-in-progress {
    color: #1677ff;
    background: #fff;
    border: 1px solid #1677ff;
  }

  &.work-finished {
    color: #52c41a;
    background: #fff;
    border: 1px solid #52c41a;
  }

  &.work-overtime {
    color: #ff4d4f;
    background: #fff;
    border: 1px solid #ff4d4f;
  }
}
.list-wrapper {
  flex: 1;
  overflow: hidden;
  padding: 0 16px;
  background-color: #f2f4f9;
}
.van-list {
  height: 100%;
  overflow-y: auto;
  margin-top: 10px;
}
.van-tab__pane {
  height: 100%;
  background-color: #f2f4f9;
}
.line {
  height: 1px;
  background-color: #e5e6eb;
  margin: 12px 0;
}
.node-img {
  width: 16px;
  height: 16px;
}
</style>

