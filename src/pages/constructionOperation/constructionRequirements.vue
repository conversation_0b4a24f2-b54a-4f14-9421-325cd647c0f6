<template>
  <div class="popup-container">
    <van-popup v-model="showSgyqPopup" :close-on-click-overlay="false">
      <div class="container-content">
        <div class="content-title">相关要求:施工安全注意事项等相关内容查看并确认</div>
        <div class="content-box">
          <van-field v-model="info.const_require" readonly label="施工具体要求" placeholder="施工具体要求" label-align="top" />
          <van-field v-model="info.technical_disclosure" label-width="100px" rows="3" readonly type="textarea" label="技术交底和其他相关要求说明" placeholder="技术交底和其他相关要求说明" label-align="top" />
          <div class="file-container">
            <div class="file-title">相关要求文件</div>
            <div class="file-box" v-for="(item, index) in fileList" :key="index">
              <div class="file-name" @click="preview(item, index)">{{ item.name }}</div>
              <div class="file-download-btn" @click="download(item, index)">下载</div>
              <div class="file-status" :style="{ color: item.status === '已读' ? '#169bd5' : '#333' }">{{ item.status }}</div>
            </div>
          </div>
        </div>
        <div class="container-btn" @click="confirm">已阅读并确认</div>
      </div>
    </van-popup>
    <van-popup v-model="showPreview" position="bottom" closeable style="border-radius: 0px" :close-on-click-overlay="false" @close="deptClose">
      <div class="preview-content">
        <iframe
          :src="iframeUrl"
          style="width: 100%; height: 100%"
          id="iframe1"
          frameborder="no"
          border="0"
          marginwidth="0"
          marginheight="0"
          scrolling="yes"
          allowtransparency="yes"
        ></iframe>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { ImagePreview } from "vant";
import { Base64 } from "js-base64";
export default {
  props: {
    instanceId: {
      type: String,
      default: ""
    }
  },
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  data() {
    return {
      showSgyqPopup: true,
      showPreview: false,
      fileList: [],
      info: {},
      iframeUrl: ""
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    download(row, idx) {
      this.fileList[idx].status = "已读";
      let file = {
        name: row.name,
        url: this.$YBS.imgUrlTranslation(row.url)
      };
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;
        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function (ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function (ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function (ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      } else {
        ImagePreview({
          images: [file.url],
          showIndex: true,
          loop: false,
          swipeDuration: 50
        });
      }
    },
    preview(row, idx) {
      this.fileList[idx].status = "已读";
      this.iframeUrl = `${__PATH.PREVIEW_URL}${encodeURIComponent(Base64.encode(this.$YBS.imgUrlTranslation(decodeURIComponent(row.url))))}`;
      this.showSgyqPopup = false;
      this.showPreview = true;
    },
    deptClose() {
      this.showSgyqPopup = true;
      this.showPreview = false;
    },
    confirm() {
      if (this.fileList.length) {
        let readingStatus = this.fileList.every(item => item.status === "已读");
        if (readingStatus) {
          this.showSgyqPopup = false;
        } else {
          this.$toast.fail("请先查看所有要求文件!");
        }
      } else {
        this.showSgyqPopup = false;
      }
    },
    getData() {
      let params = {
        instanceId: this.instanceId
      };
      this.$api.getSgyqDefaultField(params).then(res => {
        this.info = res;
        if (res.relate_file && res.relate_file.length) {
          let arr = [];
          res.relate_file.forEach(e => {
            arr.push({
              ...e,
              status: "未读"
            });
          });
          this.fileList = arr;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.van-popup {
  border-radius: 10px;
}
.van-popup--center {
  top: 55% !important;
}
.van-cell {
  padding-left: 0px !important;
  flex-direction: column;
}
.container-content {
  width: 340px;
  height: 80vh;
  border-radius: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .content-title {
    padding: 15px;
    padding-bottom: 0px;
  }
  .content-box {
    flex: 1;
    overflow-y: auto;
    flex-shrink: 0;
    padding: 15px;
    .file-container {
      padding-top: 15px;
      .file-box {
        margin-top: 15px;
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 1.5;
        .file-name {
          width: calc(100% - 85px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #0000ff;
        }
        .file-download-btn,
        .file-status {
          width: 40px;
          text-align: center;
        }
        .file-download-btn {
          background: #169bd5;
          color: #fff;
          border-radius: 3px;
        }
        .file-status {
          margin-left: 5px;
        }
      }
    }
  }
  .container-btn {
    height: 40px;
    background: #169bd5;
    color: #fff;
    text-align: center;
    line-height: 40px;
    font-size: 16px;
  }
}
.preview-content {
  width: 100vw;
  height: 90vh;
  box-sizing: border-box;
}
.van-popup__close-icon--top-right {
  top: 0px !important;
}
</style>