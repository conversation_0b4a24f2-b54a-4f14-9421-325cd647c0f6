<template>
  <page-layout :title="computedTitle" @back="goback">
    <!-- 主要内容区域 -->
    <div class="unit-content" :class="{ 'content-edit': isEdit }">
      <div class="unit-title" v-if="isEdit">
        <div>人员信息</div>
        <van-icon name="edit" color="#3562DB" size="20" v-if="!$route.query.showIcon" @click="editing = true" />
      </div>
      <div class="add-person-box" :class="{ 'add-person-box-edit': isEdit }">
        <div class="add-person-item" :class="{ 'add-person-item-edit': isEdit }" v-for="(item, index) in personList" :key="index">
          <van-field v-model="item.personName" label="姓名" required :readonly="isEdit && !editing" :rules="[{ required: true, message: '请输入姓名' }]" placeholder="请输入姓名" />
          <van-field name="radio" label="性别" required>
            <template #input>
              <van-radio-group v-model="item.sex" direction="horizontal" :disabled="isEdit && !editing">
                <van-radio name="1">男</van-radio>
                <van-radio name="2">女</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <!-- 手机号 -->
          <van-field
            v-model="item.phone"
            label="手机号"
            required
            @input="value => handlePhoneInput(value, index)"
            placeholder="请输入手机号"
            maxlength="11"
            :readonly="isEdit && !editing"
          />
          <!-- 身份证 -->
          <van-field v-model="item.idCard" label="身份证号" required placeholder="请输入身份证号" maxlength="18" :readonly="isEdit && !editing" />
          <!-- 岗位 -->
          <van-field v-model="item.position" label="岗位" required placeholder="请输入岗位" :readonly="isEdit && !editing" />
          <!-- 资质证书 -->
          <van-field name="uploader6" label="资质证书" required>
            <template #input>
              <van-uploader
                v-model="item.uploader"
                :deletable="!(isEdit && !editing)"
                :disabled="isEdit && !editing"
                :max-count="9"
                accept="image/*"
                :after-read="file => afterRead(file, 'personnelCertificate', index)"
              >
                <img class="normal-img" :src="normalImg" />
              </van-uploader>
            </template>
          </van-field>
          <div class="delete-icon" v-if="!isEdit">
            <van-icon name="delete-o" color="#F54545" size="20" @click="deletePerson(index)" />
          </div>
        </div>
        <div class="add-btn-box" @click="addPerson" v-if="!isEdit">
          <van-icon name="plus" />
          <span style="margin-left: 8px">新增一个员工信息</span>
        </div>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <template #footer v-if="!isEdit || editing">
      <div class="footer-box" v-if="!isEdit">
        <van-button type="primary" @click="handleSubmit2" color="#3562DB">提交</van-button>
      </div>
      <div class="footer-box" :class="{ 'footer-box-edit': isEdit }" v-if="editing">
        <van-button type="primary" @click="handleEdit" color="#3562DB">修改</van-button>
      </div>
    </template>
  </page-layout>
</template>

<script>
import PageLayout from '@/components/PageLayout.vue'
import axios from "axios";

export default {
  components: {
    PageLayout
  },
  data() {
    return {
      editing: false,
      isEdit: false,
      constructionUnitName: "",
      creditCode: "",
      legalPersonName: "",
      uploader1: [],
      uploader2: [],
      uploader3: [],
      uploader4: [],
      uploader5: [],
      idcard1: require("@/assets/images/construction/id-card1.png"),
      idcard2: require("@/assets/images/construction/id-card2.png"),
      normalImg: require("@/assets/images/construction/normal.png"),
      businessImage: "",
      idCardImageR: "",
      idCardImageS: "",
      attachmentInfo: [],
      fireControlInfo: [],
      currentStep: 2,
      personList: [
        {
          personName: "",
          sex: "",
          phone: "",
          idCard: "",
          position: "",
          uploader: [],
          imgUrl: ""
        }
      ]
    };
  },
  computed: {
    computedTitle() {
      if (!this.isEdit) return '新增人员'
      if (this.editing) return '编辑人员'
      return '人员详情'
    }
  },
  created() {
    if (this.$route.query.type == "edit") {
      this.isEdit = true;
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if (this.isEdit) {
      this.$api.getPersonnelDetail({ id: this.$route.query.id }).then(res => {
        console.log(res);
        // 人员信息回显
        if (res) {
          this.personList = [
            {
              personName: res.name,
              sex: res.sex,
              phone: res.phone,
              idCard: res.idCard,
              position: res.positionName,
              uploader: res.certificationImage ? [{ url: this.$YBS.imgUrlTranslation(res.certificationImage) }] : [],
              imgUrl: this.$YBS.imgUrlTranslation(res.certificationImage)
            }
          ];
        }
      });
    }
  },
  methods: {
    handleEdit() {
      // 构建人员列表数据
      let staffList = this.personList.map(person => {
        const { personName: name, position: positionName, imgUrl: certificationImage, uploader, ...rest } = person;
        return { name, positionName, certificationImage: this.$YBS.imgUrlTranslation(certificationImage), ...rest };
      });
      let personnel = staffList[0];
      personnel.id = this.$route.query.id;
      // 调用编辑接口
      this.$api
        .updatePersonnel(personnel)
        .then(res => {
          this.editing = false; // 关闭编辑状态
          this.$toast({
            message: "修改成功！",
            onClose: () => {
              this.$router.go(0); // toast结束后刷新页面
            }
          });
        })
        .catch(() => {
          this.$toast("修改失败！");
        });
    },
    handlePhoneInput(value, index) {
      // 只允许输入数字
      this.personList[index].phone = value.replace(/\D/g, "");
    },
    deletePerson(index) {
      if (this.personList.length == 1) {
        this.$toast("至少需要添加一个员工信息");
        return;
      }
      this.personList.splice(index, 1);
    },
    addPerson() {
      this.personList.push({
        personName: "",
        sex: "",
        phone: "",
        idCard: "",
        position: "",
        uploader: [],
        imgUrl: ""
      });
    },
    handleSubmit2() {
      //添加人员信息校验
      for (let i = 0; i < this.personList.length; i++) {
        const person = this.personList[i];
        if (!person.personName) {
          this.$toast(`请输入第${i + 1}个员工的姓名`);
          return;
        }
        if (!person.sex) {
          this.$toast(`请选择第${i + 1}个员工的性别`);
          return;
        }
        if (!person.phone) {
          this.$toast(`请输入第${i + 1}个员工的手机号`);
          return;
        }
        if (!/^1[3-9]\d{9}$/.test(person.phone)) {
          this.$toast(`第${i + 1}个员工的手机号格式不正确`);
          return;
        }
        if (!person.idCard) {
          this.$toast(`请输入第${i + 1}个员工的身份证号`);
          return;
        }
        if (!person.position) {
          this.$toast(`请输入第${i + 1}个员工的岗位`);
          return;
        }
        if (!person.imgUrl) {
          this.$toast(`请上传第${i + 1}个员工的资质证书`);
          return;
        }
      }
      let staffList = this.personList.map(person => {
        const { personName: name, position: positionName, imgUrl: certificationImage, uploader, ...rest } = person;
        return { name, positionName, certificationImage: this.$YBS.imgUrlTranslation(certificationImage), ...rest };
      });
      let params = {
        staffList: staffList
      };
      this.$api
        .addPersonnel(params)
        .then(res => {
          this.$toast("提交成功！");
          this.$router.go(-1);
        })
        .catch(() => {
          this.$toast("提交失败！");
        });
    },
    afterRead(file, type, index) {
      console.log(file.file);
      const fromData = new FormData();
      fromData.append("file", file.file);
      axios
        .post(__PATH.SPACE_API + "/SecurityLedger/upload", fromData, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
            hospitalSth: localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
          }
        })
        .then(res => {
          console.log(res);
          if (res.data.code == 200) {
            const attachmentObj = {
              url: res.data.data.picUrl,
              attachment: res.data.data.name,
              createBy: JSON.parse(localStorage.getItem("loginInfo")).staffName,
              createDate: this.formatDate(new Date())
            };
            switch (type) {
              case "businessImage":
                this.businessImage = res.data.data.picUrl;
                break;
              case "idCardImageR":
                this.idCardImageR = res.data.data.picUrl;
                break;
              case "idCardImageS":
                this.idCardImageS = res.data.data.picUrl;
                break;
              case "attachmentInfo":
                this.attachmentInfo.push(attachmentObj);
                break;
              case "fireControlInfo":
                this.fireControlInfo.push(attachmentObj);
                break;
              case "personnelCertificate":
                this.personList[index].imgUrl = res.data.data.picUrl;
                break;
            }
          }
        });
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.unit-content {
  background-color: #fff;

  &.content-edit {
    background-color: #f2f4f9;
  }
}

.footer-box {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  box-sizing: border-box;

  > button {
    width: 100%;
  }
}

.add-person-box {
  background-color: #f2f4f9;
  padding: 10px;
  box-sizing: border-box;
}

.add-person-box-edit {
  padding: 0;
}

.add-person-item {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.idcard-img {
  width: 50vw;
  height: 32vw;
}

/deep/ .idcard-uploader .van-uploader__preview-image {
  width: 50vw !important;
  height: 32vw !important;
}

/deep/ .idcard-uploader .van-field__control--custom {
  flex-wrap: wrap;
}

.idcard-uploader-1 {
  margin-bottom: 10px;
}

.normal-img {
  width: 80px;
}

.delete-icon {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10px;
}

.add-btn-box {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 10px;
  color: #3562db;
}

.unit-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 45px;
  padding: 0 16px;
  background-color: #fff;
  > div {
    color: #1d2129;
    font-size: 18px;
    position: relative;
    padding-left: 10px;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 18px;
      background-color: #3562db;
    }
  }
}

.unit-form-edit {
  margin-bottom: 10px;
}

.add-person-item-edit {
  border-radius: 0;
}
</style>
