<template>
  <page-layout @back="backFn" title="施工单位申报">
    <div class="content">
      <van-popup v-model="showStartTimePicker" position="bottom">
        <van-datetime-picker v-model="startDateTime" type="datetime" @confirm="onStartTimeConfirm" @cancel="showStartTimePicker = false" />
      </van-popup>
      <van-popup v-model="showEndTimePicker" position="bottom">
        <van-datetime-picker v-model="endDateTime" type="datetime" :min-date="startDateTime" @confirm="onEndTimeConfirm" @cancel="showEndTimePicker = false" />
      </van-popup>
      <div class="tool-box">
        <div class="time-box">
          <i class="line"></i>
          <div class="start-time" @click="showStartTimePicker = true">
            <span v-if="startTime">{{ startTime }}</span>
            <span v-else style="color: #86909c">请选择开始时间</span>
          </div>
          <div class="end-time" @click="showEndTimePicker = true">
            <span v-if="endTime">{{ endTime }}</span>
            <span v-else style="color: #86909c">请选择结束时间</span>
          </div>
        </div>
        <div class="search-box">
          <van-field v-model="constructionUnitName" placeholder="施工单位名称查询" @input="getList()" />
          <div class="reset" @click="handleReset">重置</div>
        </div>
      </div>
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100" :immediate-check="false">
        <div class="card" v-for="item in unitList" :key="item.id" @click="goDetails(item.id)">
          <div style="position: relative">
            <span style="color: #1d2129">施工单位：</span>
            <span style="color: #1d2129">{{ item.companyName }}</span>
            <van-icon name="arrow" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%)" />
          </div>
          <div>
            <span>信用代码：</span>
            <span>{{ item.creditCode }}</span>
          </div>
          <div>
            <span>法人名称：</span>
            <span>{{ item.legalPersonName }}</span>
          </div>
          <div>
            <span>创建时间：</span>
            <span>{{ item.createDate }}</span>
          </div>
        </div>
      </van-list>
      <div class="btn-box">
        <van-button color="#3562DB" @click="addUnit" block>新建单位</van-button>
      </div>
    </div>
  </page-layout>
</template>
  
<script>
import PageLayout from "@/components/PageLayout.vue";

export default {
  name: "constructionApplication",
  components: {
    PageLayout
  },
  data() {
    return {
      loading: false,
      finished: false,
      unitList: [],
      pageNo: 1,
      pageSize: 10,
      showStartTimePicker: false,
      showEndTimePicker: false,
      startTime: "",
      endTime: "",
      constructionUnitName: "",
      loginInfo: {},
      addbtnDisabled: false,
      startDateTime: new Date(), // 开始时间默认为当前时间
      endDateTime: new Date() // 结束时间默认为当前时间
    };
  },
  created() {
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.backFn();
        }
      );
    }, 100);
  },
  methods: {
    getList() {
      this.$api
        .getConstructionUnitList({
          currentPage: this.pageNo,
          pageSize: this.pageSize,
          startDate: this.startTime,
          endDate: this.endTime,
          companyName: this.constructionUnitName
        })
        .then(res => {
          if (this.pageNo === 1) {
            this.unitList = res.records;
          } else {
            this.unitList = [...this.unitList, ...res.records];
          }
          this.loading = false;
          this.finished = this.unitList.length >= res.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    backFn() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    onLoad() {
      this.pageNo++;
      this.getList();
    },
    goDetails(id) {
      console.log(id);
      this.$router.push({
        path: "/addUnit",
        query: {
          id,
          type: "edit"
        }
      });
    },
    addUnit() {
      if (this.addbtnDisabled) {
        this.$toast("建设单位已经被申报");
        return;
      }
      this.$router.push({
        path: "/addUnit"
      });
    },
    onStartTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.startTime = year + "-" + month + "-" + day + " " + hour + ":" + minute;
      this.showStartTimePicker = false;
      this.getList();
    },
    onEndTimeConfirm(val) {
      console.log(val);
      // 将时间格式��成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.endTime = year + "-" + month + "-" + day + " " + hour + ":" + minute;
      this.showEndTimePicker = false;
      this.getList();
    },
    handleReset() {
      this.startTime = "";
      this.endTime = "";
      this.constructionUnitName = "";
      this.pageNo = 1;
      this.finished = false;
      this.getList();
    }
  },
  mounted() {
    this.getList();
  },
  computed: {
    currentDate() {
      return new Date();
    },
    currentDate2() {
      return new Date();
    }
  }
};
</script>
  
  <style lang="scss" scoped>
.content {
  background-color: #f2f4f9;
  height: 100%;
}
.van-list {
  padding: 0 10px;
  padding-top: 12px;
  padding-bottom: 60px;
  height: calc(100% - 100px);
  box-sizing: border-box;
  overflow-y: auto;
}
.btn-box {
  display: flex;
  align-items: center;
  position: fixed;
  bottom: 0;
  height: 60px;
  width: 100%;
  display: flex;
  justify-content: center;
  background-color: #fff;
  .van-button--block {
    width: 80%;
  }
}
.card {
  background-color: #fff;
  border-radius: 8px;
  font-size: 16px;
  padding: 12px;
  margin-bottom: 12px;
  padding-top: 16px;
  div > span:nth-child(1) {
    color: #4e5969;
  }
  div > span:nth-child(2) {
    color: #1d2129;
  }
}
.card > div {
  margin-bottom: 12px;
}
.tool-box {
  width: 100%;
  height: 100px;
  background-color: #fff;
  position: sticky;
  top: 0;
  border-bottom: 1px solid #e5e6eb;
  padding: 0 16px;
  padding-top: 12px;
  box-sizing: border-box;
  z-index: 100;
}
.time-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.time-box > div {
  width: 45%;
  height: 35px;
  background-color: #f2f3f5;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1d2129;
  border-radius: 4px;
}
.line {
  position: absolute;
  width: 10px;
  height: 2px;
  top: 50%;
  left: 48%;
  background-color: #c9cdd4;
}
.search-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .van-cell::after {
    border: none;
  }
}
.search-box .van-field {
  width: 60%;
  margin-top: 5px;
}
.reset {
  font-size: 14px;
  color: #1d2129;
}
</style>
  