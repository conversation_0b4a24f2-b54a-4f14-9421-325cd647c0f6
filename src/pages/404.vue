<!--
 * @Author: hedd
 * @Date: 2023-04-27 15:50:50
 * @LastEditTime: 2023-05-22 15:02:37
 * @FilePath: \ybs_h5\src\pages\404.vue
 * @Description:
-->
<template>
  <div class="lack-content">
    <Header title="404" @backFun="goback"></Header>
    <ybsEmpty imageType="contentError" height="calc(90vh - 0.15rem)" />
  </div>
</template>
<script>
import YBS from "@/assets/utils/utils.js";
export default {
  name: "404",
  data() {
    return {};
  },
  methods: {
    goback() {
      YBS.apiCloudBack(this)
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
}
</script>
<style rel="stylesheet/stylus" lang="stylus" scoped>

</style>
<style lang="scss" scoped>
.lack-content {
  display: block;
  position: absolute;
  background: #efeff4;
  width: 100%;
  height: 100%;
  overflow: auto;
  .img-content {
    height: calc(90vh - 0.15rem);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fff;
  }
  img {
    width: 3rem;
  }
  p {
    color: #4e5969;
    font-size: 0.3rem;
    margin: 10px;
  }
}
</style>
