<!--
 * @Author: hedd
 * @Date: 2024-04-09 16:52:08
 * @LastEditTime: 2024-04-10 15:38:59
 * @FilePath: \ybs_h5\src\pages\todoItemsOrder\components\WarningBox.vue
 * @Description:
-->
<template>
  <pull-to :bottom-load-method="refresh" :is-top-bounce="false">
    <div v-if="isHaveData" class="WarningBox">
      <ul class="region">
        <li class="border-bottom" v-for="item of dataArr" :key="item.alarmId" @click="goDetail(item.alarmId)">
          <ListItem :item="item"></ListItem>
        </li>
      </ul>
    </div>
    <ybsEmpty v-if="!isHaveData" imageType="searchEmpty" height="calc(90vh - 0.15rem)" />
  </pull-to>
</template>

<script>
import PullTo from "vue-pull-to";
import { mapState } from "vuex";
import ListItem from "@/pages/monitor/components/listItem/index.vue";
export default {
  name: "WarningBox",
  components: {
    PullTo,
    ListItem,
  },
  data() {
    return {
      isHaveData: true,
      dataArr: [],
      curPage: 0,
      isDiff: false,
      flag: false,
    };
  },
  computed: {
    ...mapState(["loginInfo"]),
  },
  mounted() {
    this.curPage = 0;
    this.isDiff = true;
    this.dataArr = [];
    this.getAlarmList();
  },
  methods: {
    // 获取报警列表
    getAlarmList() {
      this.curPage = this.curPage + 1;
      let data = {
        staffId: this.loginInfo.staffId,
        pageSize: 20,
        pageNo: this.curPage,
      };
      $.showLoading();
      this.$api.selectAlarmRecordAll(data).then((res) => {
        $.hideLoading();
        if (res.records && res.records.length) {
          if (this.isDiff || this.curPage == 1) {
            this.dataArr = res.records;
          } else {
            this.dataArr = this.dataArr.concat(res.records);
          }
        } else {
          //第一次请求或者由时间查询结构
          if (this.dataArr.length == 0 || this.curPage == 1) {
            this.dataArr = [];
            this.isHaveData = false;
          }
          this.$YBS.apiToast('没有更多的数据啦')
        }
        this.flag = true;
      }).catch((err) => {
        $.hideLoading();
      });
    },
    goDetail(id) {
      this.$router.push({
        path: '/alarmAck',
        query:{
          alarmId: id
        }
      })
    },
    /**
     * 上拉加载更多
     */
    refresh(loaded) {
      this.isDiff = false;
      this.getAlarmList();
      if (this.flag) {
        loaded("done");
        this.flag = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
