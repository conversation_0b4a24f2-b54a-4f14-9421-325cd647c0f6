<template>
  <pull-to :bottom-load-method="refresh" :is-top-bounce="false">
    <div v-if="isHaveData" class="WorkOrderBox">
      <ul class="region">
        <li class="border-bottom" v-for="item of dataArr" :key="item.taskId" @click="enterDetailInfo(item.taskId)">
          <div class="middle">
            <div class="middel-title">
              <div class="middel-text">
                <!-- <div :class="{ 'red-round': item.readStatus == 0 }"></div> -->
                <div class="middel-text-name">{{ item.workTypeName }}</div>
              </div>
              <div class="right">
                <span class="flowtype red" v-if="item.flowCode == '1'">{{ item.flowType }}</span>
                <span class="flowtype blue" v-if="item.flowCode == '2'">{{ item.flowType }}</span>
                <span class="flowtype green" v-if="item.flowCode == '3'">{{ item.flowType }}</span>
                <span class="flowtype yellow" v-if="item.flowCode == '4'">{{ item.flowType }}</span>
                <span class="flowtype red" v-if="item.flowCode == '5'">待验收</span>
                <span class="flowtype gray" v-if="item.flowCode == '6'">{{ item.flowType }}</span>
                <span class="flowtype yellow bg-green" v-if="item.flowCode == '15'">{{ item.flowType }}</span>
                <span class="iconfont">&#xe646;</span>
              </div>
            </div>
            <div class="time-text-content">
              <img src="@/assets/images/ic-time.png" class="ic-time-img" />
              <span class="time-text">{{ item.createDate }}</span>
            </div>
            <div class="service-text">
              <div class="label"><span>工&nbsp;&nbsp;单&nbsp;&nbsp;号&nbsp;&nbsp;</span></div>
              <div class="content"><span>{{ item.workNum }}</span></div>
            </div>
            <div class="service-text">
              <div class="label"><span>所属科室&nbsp;&nbsp;</span></div>
              <div class="content"><span>{{ item.sourcesDeptName || '' }}</span></div>
            </div>
            <div class="service-text">
              <div class="label"><span>服务部门&nbsp;&nbsp;</span></div>
              <div class="content"><span>{{ item.designateDeptName }}</span></div>
            </div>
            <div class="service-text"><span>服务人员</span>&nbsp;&nbsp; <span>{{ item.designatePersonName }}</span></div>
            <div class="service-text"><span>服务事项</span>&nbsp;&nbsp; <span>{{ item.serviceItemShow }}</span></div>
            <div class="service-text"><span>服务地点</span>&nbsp;&nbsp; <span>{{ item.localtionName }}</span></div>
            <div class="service-text"><span>申报描述</span>&nbsp;&nbsp; <span>{{ item.questionDescription }}</span></div>
          </div>
        </li>
      </ul>
    </div>
    <ybsEmpty v-if="!isHaveData" imageType="searchEmpty" height="calc(90vh - 0.15rem)" />
  </pull-to>
</template>

<script>
import PullTo from "vue-pull-to";
import { mapState } from "vuex";
export default {
  name: "WorkOrderBox",
  components: {
    PullTo
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  data() {
    return {
      isHaveData: true,
      dataArr: [],
      curPage: 0,
      isDiff: false,
      flag: false
    };
  },
  mounted() {
    this.curPage = 0;
    this.isDiff = true;
    this.dataArr = [];
    this.getOrderList();
  },
  methods: {
    /**
     * 发送工单列表请求
     */
    getOrderList() {
      console.log(this.staffInfo.teamPersonId, "this.staffInfo");
      $.showLoading();
      let lastPage = this.curPage;
      this.curPage = ++lastPage;
      this.$api
        .getStayTaskOrderList({
          hospitalCode: this.loginInfo.hospitalCode,
          unitCode: this.loginInfo.unitCode,
          teamPersonId: this.staffInfo.teamPersonId,
          curPage: this.curPage,
          pSize: 20
        })
        .then(this.getOrderListSucc);
    },
    /**
     * 工单列表请求成功函数
     */
    getOrderListSucc(data) {
      if (data.list.length == 0) {
        $.hideLoading();
        //第一次请求或者由时间查询结构
        if (this.dataArr.length == 0 || this.curPage == 1) {
          this.dataArr = [];
          this.isHaveData = false;
        }
        this.$YBS.apiToast("没有更多的数据啦");
      } else {
        this.isHaveData = true;
        $.hideLoading();
        let list = data.list;
        if (this.isDiff || this.curPage == 1) {
          this.dataArr = list;
        } else {
          this.dataArr = this.dataArr.concat(list);
        }
      }
      this.flag = true;
    },
    /**
     * 上拉加载更多
     */
    refresh(loaded) {
      this.isDiff = false;
      this.getOrderList();
      if (this.flag) {
        loaded("done");
        this.flag = false;
      }
    },
    /**
     * 进入工单详情
     */
    enterDetailInfo(id) {
      this.$api
        .getTaskDetail({
          taskId: id
        })
        .then(res => {
          if (res.length && res[0].flowCode) {
            let code = res[0].flowCode;
            //未处理
            if (code == 1) {
              this.$router.push({
                path: "/vieform",
                query: {
                  source: "myWorkbench",
                  interfaceNum: 0,
                  id: id,
                  flowcode: code
                }
              });
            }
            //已派工
            if (code == 2) {
              this.$router.push({
                path: "/completed",
                query: {
                  source: "myWorkbench",
                  interfaceNum: 0,
                  id: id,
                  flowcode: code
                }
              });
            }
            //已挂单
            if (code == 3) {
              this.$router.push({
                path: "/completed",
                query: {
                  source: "myWorkbench",
                  interfaceNum: 0,
                  id: id,
                  hanged: true,
                  flowcode: code
                }
              });
            }
            //已结束
            if (code == 4 || code == 15) {
              this.$router.push({
                path: "/deskDetails",
                query: {
                  id: id,
                  flowcode: code
                }
              });
            }
            // 待验收(已完工)
            if (code == 5) {
              this.$router.push({
                path: "/workOrderDetails",
                query: {
                  id: id,
                  source: "",
                  flowCode: code
                }
              });
            }
          } else {
            this.$YBS.apiToast("没有更多的数据啦");
          }
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.region {
  padding: 0 10px;
  box-sizing: border-box;
  width: 100%;
  li {
    background-color: #fff;
    height: fit-content;
    // display: flex;
    font-size: calc(15.6px * var(--font-scale))!important;
    padding: 0.2rem 0.32rem;
    padding-left: 0.2rem;
    margin-bottom: 10px;
    border-radius: 8px;
    .middle {
      // flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      padding-left: 0.2rem;
      color: #1d2129;
      font-size: calc(15.6px * var(--font-scale))!important;
      .middel-title {
        display: flex;
        justify-content: space-between;
        padding: 0.1rem 0;
        .middel-text {
          position: relative;
          font-size: calc(15.6px * var(--font-scale))!important;
          display: flex;
          align-items: center;
          .middel-text-name {
          }
          .red-round {
            width: 6px;
            height: 6px;
            background: #fa403c;
            border-radius: 50%;
            margin: auto 0;
            margin-right: 5px;
          }
        }
        .right {
          // padding-top: 8px;
          .flowtype {
            display: inline-block;
            padding: 4px 6px;
            border-radius: 2px;
            font-size: calc(13px * var(--font-scale))!important;
          }
          .iconfont {
            color: #e5e5e5;
            font-size: 12px;
          }
          .red {
            color: #f53f3f;
            background-color: #ffece8;
          }
          .blue {
            color: #f53f3f;
            background-color: #ffece8;
          }
          .green {
            color: #00b42a;
            background-color: #e8ffea;
          }
          .yellow {
            color: #f53f3f;
            background-color: #ffece8;
          }
          .gray {
            color: #4e5969;
            background-color: #f2f3f5;
          }
        }
      }
      .time-text-content {
        color: #4e5969;
        padding: 0.1rem 0;
        vertical-align: middle;
        .ic-time-img {
          width: 0.24rem;
          vertical-align: middle;
        }
        .time-text {
          font-size: calc(15.6px * var(--font-scale))!important;
        }
      }
      .service-text {
        font-size: calc(15.6px * var(--font-scale))!important;
        padding: 0.1rem 0;
        width: 100%;
        display: flex; /* 使用 flex 布局 */
        align-items: flex-start; /* 顶部对齐 */
        
        /* 第一个 span - 标签部分 */
        span:first-child {
          white-space: nowrap; /* 强制不换行 */
          flex-shrink: 0; /* 防止缩小 */
          // color: #4e5969;
        }
        
        /* 第二个 span - 内容部分 */
        span:nth-child(2) {
          flex: 1; /* 占用剩余空间 */
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal; /* 允许换行 */
          word-wrap: break-word;
          word-break: break-all;
          display: -webkit-box;
          -webkit-line-clamp: 5; /* 最多显示5行 */
          -webkit-box-orient: vertical;
          // color: #4e5969;
        }
      }
    }
  }
}
</style>
