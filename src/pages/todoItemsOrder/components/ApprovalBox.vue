<template>
  <pull-to :bottom-load-method="refresh" :is-top-bounce="false">
    <div v-if="isHaveData" class="ApprovalBox">
      <div v-for="(item, index) in dataArr" :key="index" class="listStyle" @click="goApprove(item)">
        <div class="name">
          <div class="left">
            <!-- <span style="color: #3562db">{{ item.msgSysTypeName }}</span
            ><span style="padding: 0 5px">|</span>{{ item.approvalName }} -->
            <div class="processClass">{{item.processName}}</div>
            <!-- <div class="processClass">{{'流程名称'}}</div> -->
          </div>
          <!-- <div><van-icon v-if="item.msgSysType != 3" name="arrow" /></div> -->
          <div class="approvalClass">{{item.approvalStatusName}}</div>
          <!-- <div class="approvalClass">{{'待审批'}}</div> -->
        </div>
        <!-- <div class="txt">
          <div class="txt_l" style="width: 100%">{{ item.createTime }}</div>
        </div> -->
        <div class="txt">
          <div class="txt_l">所属模块：</div>
          <div>{{ item.typeName }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">发起人：</div>
          <div>{{ item.initiatorName }}</div>
        </div>
        <div class="txt">
          <div class="txt_l">发起时间：</div>
          <div>{{ item.date }}</div>
        </div>
      </div>
    </div>
    <ybsEmpty v-if="!isHaveData" imageType="searchEmpty" height="calc(90vh - 0.15rem)" />
  </pull-to>
</template>

<script>
  import PullTo from "vue-pull-to";
  import {
    mapState
  } from "vuex";
  export default {
    name: "ApprovalBox",
    components: {
      PullTo
    },
    data() {
      return {
        isHaveData: true,
        dataArr: [],
        curPage: 0,
        isDiff: false,
        flag: false
      };
    },
    computed: {
      ...mapState(["loginInfo"])
    },


    mounted() {
      this.curPage = 0;
      this.isDiff = true;
      this.dataArr = [];
      this.getOrderList();
    },
   
    methods: {
      /**
       * 发送工单列表请求
       */
      getOrderList() {
        $.showLoading();
        // let lastPage = this.curPage;
        // this.curPage = ++lastPage;
        // this.$api
        //   .getMessageListAppByCategory({
        //     hospitalCode: this.loginInfo.hospitalCode,
        //     unitCode: this.loginInfo.unitCode,
        //     category: 2,
        //     staffId: this.loginInfo.staffId,
        //     page: this.curPage,
        //     pageSize: 20
        //   })
        //   .then(this.getOrderListSucc);
        this.$api
          .approvalBacklog({
            hospitalCode: this.loginInfo.hospitalCode,
            unitCode: this.loginInfo.unitCode,
            userId: this.loginInfo.staffId,
          }).then((res) => {
            console.log(res, 'resresres');
            this.dataArr = res.approvalData

          })
      },
      /**
       * 工单列表请求成功函数
       */
      getOrderListSucc(data) {
        if (data.approvalData.length == 0) {
          $.hideLoading();
          //第一次请求或者由时间查询结构
          if (this.dataArr.length == 0 || this.curPage == 1) {
            this.dataArr = [];
            this.isHaveData = false;
          }
          this.$YBS.apiToast("没有更多的数据啦");
        } else {
          this.isHaveData = true;
          $.hideLoading();
          let list = data.approvalData;
          // msgBody空值去除
          list = list
            .map(item => {
              let msgBody = {};
              try {
                msgBody = JSON.parse(item.msgBody);
              } catch (error) {
                msgBody = {};
              }
              return {
                msgSysTypeName: item.msgSysTypeName,
                msgSysType: item.msgSysType,
                approvalName: msgBody.approvalName || item.msgTitle,
                number: msgBody.number,
                createTime: msgBody.createTime || item.createdTime,
                createName: msgBody.createName || item.createName,
                contractProblemStatus: msgBody.contractProblemStatus,
                approvalType: msgBody.approvalType,
                gtasksId: msgBody.gtasksId,
                iomsApprovalType: msgBody.type,
                approvalControlId: msgBody.approvalControlId
              };
            })
            .filter(item => item);
          if (this.isDiff || this.curPage == 1) {
            this.dataArr = list;
          } else {
            this.dataArr = this.dataArr.concat(list);
          }
        }
        this.flag = true;
      },
      goApprove(item) {
        // 一站式挂单审批
        if (item.typeId == "1") {
          this.$router.push({
            path: "/approvalDetails",
            query: {
              id: item.id
            }
          });

          //出入库审批
        } else if (item.typeId == "2") {
          this.$router.push({
            path: "/myAccessRequestDetails",
            query: {
              id: item.id
            }
          });
          //施工
        } else if (item.typeId == "3") {
          this.$router.push({
            path: "/conApprovalDetails",
            query: {
              projectCode: item.id,
              flowType:"admission_gcwx",
              currentNodeId:item.currentNodeId
            }
          });

        }
        // 零星项目工程审批 暂不做处理
        if (item.msgSysType == "3") {
          return;
        }

      },
      /**
       * 上拉加载更多
       */
      refresh(loaded) {
        this.isDiff = false;
        this.getOrderList();
        if (this.flag) {
          loaded("done");
          this.flag = false;
        }
      }
    }
  };

</script>
<style lang="scss" scoped>
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 0 auto 10px;
    padding: 5px 0;

    .name {
      padding: 10px 10px;
      display: flex;
      justify-content: space-between;

      .left {
        line-height: 30px;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .processClass {
          font-size: 16px;
        }
      }

      .approvalClass {
        height: 30px;
        width: 60px;
        background-color: #f9ead2;
        line-height: 30px;
        text-align: center;
        border-radius: 4px;
        color: #eaa856;
      }
    }

    .txt {
      display: flex;
      padding: 10px 10px;

      .txt_l {
        width: 100px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }

</style>
