<template>
  <div class="area-content">
    <Header title="待办事项" @backFun="goback"></Header>
    <div class="tab-content">
      <div class="tab-title">
        <div @click="judgeIsSame('0')" :class="{ textStyle: curArea == 0 }">
          <span class="title">工单</span>
          <span :class="{ tabItemActive: curArea == 0 }"></span>
        </div>
        <div @click="judgeIsSame('1')" :class="{ textStyle: curArea == 1 }">
          <span class="title">报警</span>
          <span :class="{ tabItemActive: curArea == 1 }"></span>
        </div>
        <div @click="judgeIsSame('2')" :class="{ textStyle: curArea == 2 }">
          <span class="title">审批</span>
          <span :class="{ tabItemActive: curArea == 2 }"></span>
        </div>
      </div>
      <div class="line"></div>
    </div>
    <div class="select-area">
      <WorkOrderBox v-if="curArea == 0" />
      <WarningBox v-else-if="curArea == 1" />
      <ApprovalBox v-else-if="curArea == 2" />
    </div>
  </div>
</template>

<script>
import WorkOrderBox from "./components/WorkOrderBox.vue";
import WarningBox from "./components/WarningBox.vue";
import ApprovalBox from "./components/ApprovalBox.vue";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "TodoItemsOrder",
  mixins: [fontSizeMixin],
  components: {
    WorkOrderBox,
    WarningBox,
    ApprovalBox,
  },
  data() {
    return {
      flag: false,
      curArea: null,
    };
  },
  mounted() {
    console.log('进来页面了');
    
    if (systemType == "ios") {
      document.querySelector(".select-area").style.top = "calc(12vh + 1.08rem)";
    }
    const orderParams = this.$route.query;
    this.curArea = orderParams.type || 0;
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    judgeIsSame(type) {
      this.curArea = type;
    }
  },
};
</script>

<style lang="scss" scoped>
$bgColor: #efeff4;
$btnColor: #3562db;
.area-content {
  display: block;
  position: absolute;
  background: #efeff4;
  width: 100%;
  height: 100%;
  overflow: auto;
  &:after {
    display: block;
    content: ".";
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
    .time-text {
      display: inline-block;
      width: 2.5em;
      border-right: 1px solid #888;
      margin-right: 0.5em;
    }
    .time-wrapper {
      flex: 1;
      display: flex;
      justify-content: space-between;
      white-space: nowrap;
    }
  }
  .tab-content {
    position: fixed;
    z-index: 1;
    width: 100%;
    .line {
      height: 0.2rem;
      background-color: $bgColor;
    }
    .textStyle .title {
      color: #1d2129;
      font-family: PingFang SC-Bold, PingFang SC;
      font-weight: 600 !important;
    }
    .tab-title {
      height: 0.88rem;
      display: flex;
      font-size: 0.3rem;
      background-color: #fff;
      color: #86909c;
      font-family: PingFang SC-Regular, PingFang SC;
      div {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        line-height: 100%;
        .title {
          font-size: 0.3rem;
          padding: 0.17rem 0 0.23rem 0;
        }
        .num {
          font-size: 0.4rem;
        }
      }
      .disClick {
        pointer-events: none;
      }
      .tabItemActive {
        position: absolute;
        width: 0.5rem;
        height: 3px;
        border-radius: 2px;
        background: $btnColor;
        bottom: calc(0.2rem + 3px);
      }
    }
  }
  .select-area {
    width: 100%;
    height: calc(90vh - 1.08rem);
    background: transparen;
    display: flex;
    position: absolute;
    top: calc(10vh + 1.08rem);
    /deep/ .vue-pull-to-wrapper {
      width: 100%;
      .action-block {
        display: none;
      }
    }
  }
}

</style>
