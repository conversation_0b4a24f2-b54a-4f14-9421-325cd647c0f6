<template>
  <div class="select-hosp">
    <div class="search-wrapper">
      <input type="text" class="search-content" maxlength="50" placeholder="请输入关键字搜索" v-model="iptHospName">
      <div class="search-text">
        <span class="iconfont" v-if="iptHospName" @click="clearText">&#xe61f;</span>
        <span class="search" @click="getIptText">搜索</span>
      </div>
    </div>
    <div class="select-content">
      <div class="left">
        <div class="item" v-for="(item,idx) of cityLists" @click="checkedItem(idx,item.name)" :class="{active: (idx==isActive || item.name == location)}">
          {{ item.name }}
        </div>
      </div>
      <div class="right">
        <div class="list" v-for="(item,idx) of hospLists" v-if="hospLists.length != 0" @click="returnHospInfo(item)">
          <img :src="item.emblem" alt="">
          <span>{{ item.hospitalName }}</span>
          <span class="opening" v-if="item.state == 1 || (gongce && (item.state == 1 || item.state == 3 || item.state == 4))">{{ item.stateName }}</span>
        </div>
        <div v-if="hospLists.length == 0 && isSearch" class="default-box">
          <!-- <img src="~images/default/search.png" alt=""> -->
          <p>抱歉，没有找到您搜索的结果</p>
        </div>
        <div v-if="hospLists.length == 0 && !isSearch" class="default-box">
          <img src="~images/noDataDefault/search-empty.png" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import { mapMutations,mapState } from 'vuex'
  export default {
    name: "HospListNew",
    data () {
      return {
        isActive:0,//用于激活省份
        hospLists:[],//医院列表
        cityLists:[],//全国所有省份及直辖市
        prePathName:'',//记录上一个页面的path地址
        province:'',//当前定位的省份
        iptHospName:'',//搜索内容
        isSearch: false,//是否为搜索操作
        gongce: false,//医院是否为公测版
        allHosp:[],//所有医院，不做城市区分
        location:"",//搜索到的城市（搜索结果仅为一个的情况下赋值）
        city:"",//所选择的医院位于的城市（省份）
      }
    },
    computed: {
      ...mapState(['staffInfo'])
    },
    methods: {
      /**
       * 点击省份
       * @param index 循环使用的索引值
       */
      checkedItem(index,name){
        this.isActive = -2
        this.location = ""
        if(this.isActive == index)return false
        this.iptHospName = ""
        this.isActive=index;
        $.showLoading()
        this.city = name == "附近" ? '' : name;
        this.getHospInfo(name)
      },
      /**
       * 获取医院信息
       */
      getHospInfo (name) {
        if(name == '附近'){
          name = ''
        }
        this.$api.getHospitalListAndLogoByUnitCode({
          province:name || this.province,
          city:(name || this.province) == this.city ? '' : this.city
        }).then(res => {
          this.hospLists = res
          let allHosp = []
          res.forEach((item, index) => {
            //将state == 2、3、4 的医院不进行显示,只有0,1显示（开通状态（0已上线、1开通中、2暂停使用、3演示版、4公测版）,搜索时显示演示版和公测版）
            if(item.state != 2 && item.state != 4 && item.state != 3 ){
              allHosp.push(item)
            }
          })
          if (Object.keys(allHosp).length > 0) {
            this.hospLists = allHosp
          }
        })
      },
      /**
       * 点击某个医院返回到上一页并带回医院信息
       */
      returnHospInfo (hosp) {
        if(hosp.state == 1){
          $.toast("正在开通中…","text")
         }else if(hosp.appList[0] && hosp.appList[0].appId != process.env.WxAppid){
          $.toast("当前公众号暂不支持此医院","text")
         }else{
          //  this.changeHosp(hosp)
           sessionStorage.setItem("unitCode",hosp.unitCode)
           sessionStorage.setItem("hospitalCode",hosp.hospitalCode)
           this.$router.push({
             path: this.prePathName,
           })
         }
      },
      /**
       * 点击搜索
       */
      getIptText () {
        if (this.iptHospName) {
          let restList = []
          this.allHosp.forEach((item, idx) => {
            if (item.hospitalName.includes(this.iptHospName)) {
              restList.push(item)
            }
          })
          if (restList.length > 0) {
            this.hospLists = restList
            this.gongce = true
            //如果搜索结果>1，左侧显示附件，如果搜索结果为1个，则显示相应的区域位置
            if(restList.length > 1){
              this.isActive = 0
            }else{
              this.isActive = -1
              this.location = restList[0].province
              this.city = this.location
            }
          }else{
            //未搜索到结果
            this.isSearch = true
            this.hospLists = []
          }
        }
      },
      /**
       * 获取所有医院，不做城市区分
       */
      getAllHosp () {
        this.$api.getHospitalListAndLogoByUnitCode().then(res => {
          this.allHosp = res
        })
      },
      /**
       * 清空输入框中的内容
       */
      clearText () {
        this.iptHospName = ""
        //以下代码为左侧区域恢复到附近，右侧同样也为附近的医院
//        this.isActive = 0
        this.gongce = false
        this.getHospInfo()
      },
      // ...mapMutations(['changeHosp']),
    },
    created () {
      let queryInfo = this.$route.query
      this.prePathName = queryInfo.pathName
      this.province = queryInfo.province
      this.city = queryInfo.city
      this.getHospInfo()
      this.getAllHosp()
      this.$api.cascadingQueryAreaList({
        type:'2',
        haveHospital:'1'
      }).then(res => {
        res.unshift({name:'附近'})
        this.cityLists = res
      })
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/mixins.styl"
@import "~styles/varibles.styl"
.select-hosp
  height: 100%
  background-color: #fff
  .search-wrapper
    padding: .18rem .32rem
    margin-bottom: .14rem
    position: relative
    height: .68rem
    .search-content
      background: $bgColor url('~images/newForm/search.png') no-repeat .21rem
      background-size: .26rem
      height: .68rem
      border-radius .34rem
      padding-left: .66rem
      width: 100%
      box-sizing: border-box
      padding-right: 1rem
    .search-text
      position: absolute
      top: .35rem
      right: 0.55rem
      color: #898991
      .search
        border-left: 1px solid #d7d7d7
        padding-left: .16rem
  .select-content
    display flex
    height: calc(100% - 1.18rem)
    .left
      height: 100%
      overflow: auto
      width: 1.76rem
      .item
        height: .9rem
        text-align: center
        color: $darkTextColor
        background-color: #E8E8F1
        padding: 0 .2rem
        box-sizing: border-box
        display: flex
        justify-content: center
        align-items: center
      .active
        background-color: #fff
    .right
      flex 1
      height: 100%
      overflow: auto
      .list
        padding-left: .4rem
        height: .9rem
        display: flex
        align-items center
        position: relative
        .opening
          position: absolute
          top: .25rem
          right: .2rem
          font-size: 12px
          line-height: 1
          background: #e3e3e3
          padding: 4px 10px
          color: #999
          border-radius: 35px
        img
          width: .52rem
          margin-right: .2rem
          display: inline-block
      .default-box
        height: 100%
        display: flex
        justify-content: center
        align-items: center
        flex-direction: column
        img
          width: 50%
          margin-top: -1.4rem
        p
          color: #838383
</style>
