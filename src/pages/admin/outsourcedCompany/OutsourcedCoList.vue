<template>
  <div class="list" ref="wrapper">
    <ul>
      <li
          class="item"
          v-for="item of lists"
          @click="handleOutsourcedCoClick(item)"
      >{{item.companyName}}</li>
    </ul>
  </div>
</template>

<script type=text/ecmascript-6>
  import BScroll from 'better-scroll'
  import { mapMutations } from 'vuex'
  export default {
    name: "OutsourcedCoList",
    data () {
      return {
        lists:[],
        getHospCode:'',
        getUnitCode:'',
        pathName:'',
      }
    },
    methods:{
      /**
       * 点击每一项处理函数
       */
      handleOutsourcedCoClick (item) {
        let outsourcedCoInfo = item
        this.changeOutsourcedCo(outsourcedCoInfo)
        this.$router.push(this.pathName)
      },
      /**
       * 获取服务公司列表请求
       */
      getOutsourcedCoList () {
        $.showLoading()
        this.axios.get(process.env.API_BASE + '/outsourcedController/getOutsourcedCompanyList',{
              params:{
                unitCode:this.$route.params.unitCode,
                hospitalCode:this.$route.params.hospitalCode
              }
            })
            .then(this.handleGetOutsourcedCoListSucc)
      },
      /**
       * 获取服务公司列表成功回调函数
       */
      handleGetOutsourcedCoListSucc (res) {
        $.hideLoading()
        if(res.data.code == 200) {
          res = res.data.data;
          this.lists = res
        }else{
          $.toast(res.data.messages,'text')
        }
      },
      ...mapMutations(['changeOutsourcedCo'])
    },
    mounted () {
      this.scroll = new BScroll(this.$refs.wrapper,{click:true})
      this.getOutsourcedCoList()
      this.getHospCode = this.$route.params.hospitalCode
      this.getUnitCode = this.$route.params.unitCode
      this.pathName = this.$route.params.pathName
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  .border-bottom
    &:before
      border-color: #ccc
  .list
    overflow: hidden
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    background-color: $bgColor
    .item
      line-height: .98rem
      padding-left: .3rem
      font-size: .36rem
      background-color: #fff
      margin-bottom: 1px
      &:active
        background-color: transparent
</style>