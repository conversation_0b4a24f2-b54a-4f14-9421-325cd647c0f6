<template>
  <div>
    <ul class="list">
      <li
          class="item"
          v-for="item of letters"
          :key="item"
          :ref="item"
          @touchstart.prevent="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @click="handleLetterClick"
      >
        {{item}}
      </li>
    </ul>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "HospitalAlphabet",
    props:["allHosp"],
    computed:{
      letters () {
        const letters = []
        for (let i in this.allHosp) {
          letters.push(i)
        }
        return letters
      }
    },
    data () {
      return {
        touchStatus:false,
        startY:0,
        timer:null
      }
    },
    updated () {
      this.startY = this.$refs[this.letters[0]][0].offsetTop;
    },
    methods:{
      /**
       * 点击某个字母
       * @param e
       */
      handleLetterClick (e) {
        this.$emit('change',e.target.innerText)
      },
      /**
       * 开始触摸母
       * @param e
       */
      handleTouchStart (e) {
        this.touchStatus = true
      },
      /**
       * 触摸字母后移动事件
       * @param e
       */
      handleTouchMove (e) {
        if(this.touchStatus){
          if(this.timer){
            clearTimeout(this.timer)
          }
          this.timer = setTimeout(() => {
            const touchY = e.touches[0].clientY
            const index = Math.floor((touchY - this.startY) / 30)
            if (index >= 0 && index <= this.letters.length) {
              this.$emit('change',this.letters[index])
            }
          },16)
        }
      },
      /**
       * 手指离开屏幕
       * @param e
       */
      handleTouchEnd (e) {
        this.touchStatus = false
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  .list
    display: flex
    flex-direction: column
    justify-content: center
    position: absolute
    top: 0
    right: 0
    bottom: 0
    width: .4rem
    .item
      text-align: center
      line-height: .6rem
      color: $textColor
</style>