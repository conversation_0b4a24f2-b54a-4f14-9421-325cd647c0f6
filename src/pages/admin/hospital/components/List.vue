<template>
  <div class="list">
    <div class="search-box">
      <div class="ipt-box">
        <span class="iconfont icon-search">&#xe60a;</span>
        <input type="text" v-model="iptHospName" placeholder="请输入医院名称...">
        <span class="iconfont icon-delete" v-if="iptHospName" @click="clearIptText">&#xe61f;</span>
      </div>
      <span class="search-text" @click="getIptText">
        <span class="line">|</span>
        <span>搜索</span>
      </span>
    </div>
    <div ref="wrapper" class="list-wrapper">
      <div>
        <div
            class="hosp"
            v-for="(item,key) of allHosp"
            :key="key"
            :ref="key"
        >
          <div class="title border-topbottom">{{ key }}</div>
          <div class="item-list">
            <div
                class="item border-bottom"
                v-for="innerItem of item"
                :key="innerItem.id"
                @click="handleHospClick(innerItem)"
            >
              <div class="hosp-logo">
                <img :src="innerItem.emblem" class="img">
              </div>
              <p>
                {{ innerItem.hospitalName }}
                <span class="opening" v-if="innerItem.state == 1 || gongce">{{ innerItem.stateName }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import BScroll from 'better-scroll'
  import { mapMutations } from 'vuex'
  export default {
    name: "HospitalList",
    props: ["allHosp","letter","pathName"],
    data () {
      return {
        iptHospName:'',
        gongce: false
      }
    },
    methods:{
      /**
       * 点击医院
       * @param innerItem
       */
      handleHospClick (innerItem) {
        console.log(innerItem);
        if(innerItem.state == 1){
          $.toast("正在开通中…","text")
        }else if(innerItem.appList[0] && innerItem.appList[0].appId != localStorage.wxAppId){
          $.toast("当前公众号暂不支持此医院","text")
        }else{
          // this.changeHosp(innerItem)
          this.$router.push(this.pathName)
        }
      },
      // ...mapMutations(['changeHosp']),
      /**
       * 搜索
       */
      getIptText () {
        if (this.iptHospName) {
          let hospLists = this.$parent.hospListsStorageStation
          let restList = []
          hospLists.forEach((item, idx) => {
            if (item.hospitalName.includes(this.iptHospName)) {
              restList.push(item)
            }
          })
          if (restList.length > 0) {
            this.$parent.hospLists = restList
            this.gongce = true
          }else{
            $.toast('未查询到结果','text')
          }
        } else {
          this.gongce = false
          this.$parent.getHospitalInfo()
        }
        this.$parent.sortByHosp(this.gongce)
      },
      /**
       * 清除输入框中的内容
       */
      clearIptText () {
        this.iptHospName = ''
      }
    },
    watch: {
      letter () {
        if (this.letter) {
          const element = this.$refs[this.letter][0]
          this.scroll.scrollToElement(element)
        }
      }
    },
    mounted () {
      this.scroll = new BScroll(this.$refs.wrapper,{click:true})
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  .list
    overflow: hidden
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    background-color: $bgColor
    .list-wrapper
      height: calc(100% - 1.3rem)
      overflow: hidden
    .search-box
      color: $textColor
      height: 1.18rem
      width: 100%
      padding: .2rem
      background-color: #fff
      box-sizing: border-box
      display: flex
      margin-bottom: .1rem
      .ipt-box
        position: relative
        display: flex
        flex: 1
        background-color: $bgColor
        border-radius: .1rem 0 0 .1rem
        overflow: hidden
        input
          background-color: $bgColor
          padding-left: .72rem
          width: 100%
          &::-webkit-input-placeholder
            color:$textColor
        .iconfont
          position: absolute
          top: 50%
          margin-top: -8px
        .icon-search
          left: .28rem
        .icon-delete
          right: 7px
      .search-text
        color: #353535
        line-height: .78rem
        width: 1.17rem
        text-align: center
        background-color: $bgColor
        border-radius: 0 .1rem .1rem 0
        .line
          float: left
          color: #D3D3D5
    .title
      line-height: .6rem
      padding-left: .2rem
      color: #666
      font-size: .3rem
    .item-list
      .item
        line-height: 1.4rem
        padding-left: .2rem
        font-size: .36rem
        color: #353535
        background-color: #fff
        display: flex
        align-items: center
        position: relative
        .hosp-logo
          width: .8rem
          height: .8rem
          padding-right: .2rem
          display: flex
          align-items: center
          overflow: hidden
          .img
            width: 100%
            vertical-align:baseline
        .opening
          position: absolute
          top: .5rem
          right: .2rem
          font-size: 12px
          line-height: 1
          background: #e3e3e3
          padding: 4px 10px
          color: #999
          border-radius: 35px
</style>