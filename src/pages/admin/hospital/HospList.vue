<template>
  <div>
    <hospital-list
        :allHosp="allHosp"
        :letter="letter"
        :pathName="pathName"
    ></hospital-list>
    <hospital-alphabet
        :allHosp="allHosp"
        @change="handleLetterChange"
    ></hospital-alphabet>
  </div>
</template>

<script type=text/ecmascript-6>
  import HospitalList from './components/List'
  import HospitalAlphabet from './components/Alphabet'
  export default {
    name: "Hospital",
    components: {
      HospitalList,
      HospitalAlphabet
    },
    data () {
      return {
        allHosp: {},
        letter: '',
        pathName:'',
        hospLists:[],
        hospListsStorageStation:[],
        province: '',//用于医院定位传参，省
        city: '',//用于医院定位传参，市
        district: ''//用于医院定位传参，区
      }
    },
    methods: {
      /**
       * 获取医院信息
       */
      getHospitalInfo () {
        $.showLoading()
        this.$api.getHospitalListAndLogoByUnitCode({
          province: this.province,//this.province
          city: this.province == this.city ? "" : this.city,//this.city
          district: this.district//this.district
        }).then(this.handleGetHospInfoSucc)
      },
      /**
       * 获取医院信息成功函数
       * @param result  结果
       */
      handleGetHospInfoSucc (res) {
        $.hideLoading()
        this.hospLists = res
        this.hospListsStorageStation = res.data
        this.sortByHosp ()
      },
      /**
       * 医院排序（按照展示方式）
       */
      sortByHosp (searchRestState) {
        let hositals = {}
        let letters = []
        this.hospLists.forEach((item, index) => {
          //将state == 2、3、4 的医院不进行显示,只有0,1显示（开通状态（0已上线、1开通中、2暂停使用、3演示版、4公测版））
          if(item.state != 2 && ( searchRestState || (item.state != 4 && item.state != 3)) ){
            let alphabet = item.hospitalCode.substr(0, 1)
            letters.push(alphabet)
            if (!hositals[alphabet]) {
              hositals[alphabet] = []
            }
            hositals[alphabet].push(item)
          }
        })
        if (Object.keys(hositals).length > 0) {
          this.allHosp = hositals
        }
      },
      /**
       * 改变字母选项
       * @param letter  点击的字母
       */
      handleLetterChange (letter) {
        this.letter = letter;
      }
    },
    mounted () {
      this.getHospitalInfo()
    },
    created () {
      this.pathName = this.$route.query.pathName
      this.province = this.$route.query.province
      this.city = this.$route.query.city
      this.district = this.$route.query.district
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>

</style>