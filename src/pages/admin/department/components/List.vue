<template>
  <div class="department-list">

    <div class="department-search">
      <div class="search-ipt" v-show="!query">       
          <span class="iconfont">&#xe60a;</span>
          <input
            v-model="officeName"
            class="search"
            type="text"
            maxlength="50"
            placeholder="请输入关键字"
          />
          <span class="line"></span>
          <span
            class="cancle"
            @click="switchto"
          >取消</span>
      </div>
      <div class="st"  @click="switchto" v-show="query">
          <span class="iconfont">&#xe60a;</span>
          <span>搜索</span>
      </div>
    </div>
    <!-- <div style="overflow:auto " > -->

    <ul ref="wrapper"  class="content-list"  v-if="query">
      <li
       class="item"
       v-for="(item,index) of depts"
       :key="index"
       @click="handleDeptClick(item)"
       >{{item.deptName}}</li>
    </ul>
    <!-- </div> -->

    <ul  ref="wrapper" class="content-list"  v-show="!query">
      <li
       class="item"
       v-for="(item,index) of officeNameList"
       :key="index"
       @click="handleDeptClick(item)"
       >{{item.deptName}}</li>
    </ul>
  </div>
</template>

<script type=text/ecmascript-6>
import BScroll from 'better-scroll'
import axios from 'axios'
import { mapMutations,mapState } from 'vuex'
  export default {
    name: "DeptList",
    props:["depts","pathName"],
    data(){
      return{
        query:true,
        officeName:"",
        officeNameList:[]
      }
    },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
    methods:{
      move(){
        console.log(123);
        
      },
      switchto(){
        this.query=!this.query
        this.officeName=""
      },
      handleDeptClick (item) {
        let deptInfo = {
          deptCode:item.id,
          deptName:item.deptName
        }
        this.changeDept(deptInfo)
        this.$router.push({
          path:this.pathName,
          query:{
            source:this.$route.query.source
          }
        })
      },
      ...mapMutations(['changeDept'])
    },
    mounted () {
      this.scroll = new BScroll(this.$refs.wrapper,{click:true})
    },
    watch:{

      officeName(){
      this.officeName = this.officeName.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,'');
        if(this.officeName.length===0){
          this.officeNameList=[]
          this.query=false
          return
        }
        // this.axios
        // .post(
        //   __PATH.ONESTOP +
        //     "/appOlgTaskManagement/getNameOffice",
        //   this.$qs.stringify({
        //     unitCode: this.loginInfo.userOffice[0].unitCode,
        //     hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
        //     type:3,//4查询全部  
        //     officeName:this.officeName  
        //   })
        axios.post( process.env.API_BASE + "/departmentManager/department-manager/selectByPage",
            {
              current: 1,
              size: 999,
              deptName: this.officeName
            },
            {
              headers: {
                unitCode: this.loginInfo.userOffice[0].unitCode,
                hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
              }
            }
        )
        .then(res => {
          const data = res.data
          if (data.code == 200) {
            this.officeNameList=data.data.records
          }
        })          
        
      }
    },
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"

.border-bottom
  &:before
    border-color: #ccc
.department-list
  height :100vh;
  overflow : hidden;
  background-color: $bgColor
  
.department-search
  width: 100%;
  height: 0.78rem;
  line-height: 0.78rem;
  padding: 0.2rem 0rem;
  background-color: #fff;
  .search-ipt 
    position: relative;
    .iconfont
      position: absolute;
      left: 0.4rem;
      top: 0rem;
      color: $textColor;
.search 
  background-color: $bgColor;
  padding-left: 0.8rem;
  padding-right: 1.1rem;
  box-sizing: border-box;
  height: 0.78rem;
  display: inline-block;
  width: 95%;
  margin-left: 2.5%;
  border-radius: 5px;
.line 
  width: 1px;
  height: 0.3rem;
  background-color: #D3D3D5;
  display: inline-block;
  position: absolute;
  right: 1.1rem;
  top: 0.24rem;
.cancle 
  position: absolute;
  right: 7px;
  top: 0;
  width: 1rem;
  text-align: center;

.content-list
  height :calc(100% - 1.2rem);
  overflow-y:auto;
  .item
    margin-bottom: 1px
    min-height: 0.96rem;
    padding: 0.3rem 0.3rem;
    line-height: 0.42rem;
    box-sizing: border-box;
    word-wrap: break-word;
    word-break: break-all;
    background :#fff;
    font-size: .32rem
    &:active
      background-color: transparent

.st
  text-align :center;
  background:#eee;
  padding-left: 0.8rem;
  padding-right: 1.1rem;
  box-sizing: border-box;
  height: 0.78rem;
  width: 95%;
  margin-left: 2.5%;
  border-radius: 5px;
  color:$textColor;
  font-size:14px;
.item-color{
  background : #fcfcfc ;
}
</style>