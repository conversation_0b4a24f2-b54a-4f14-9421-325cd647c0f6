<template>
  <div class="officeNameList">
        <list  class="aaa"
      :depts="depts"
      :pathName="pathName"
      ></list>
  </div>
</template>

<script type=text/ecmascript-6>
  import List from './components/List'
  import axios from 'axios'
  export default {
    name: "DeptList",
    components:{
      List
    },
    data () {
      return {
        depts:[],
        getHospCode:'',
        getUnitCode:'',
        pathName:''
      }
    },
    methods:{
      getDeptInfo () {
        $.showLoading()
        // this.$api.getHospitalOfficeInfo({
          // unitCode:this.getUnitCode,
          // hospitalCode:this.getHospCode
          axios.post( process.env.API_BASE + "/departmentManager/department-manager/selectByPage",
            {
              current: 1,
              size: 999,
            },
            {
              headers: {
                unitCode:this.getUnitCode,
                hospitalCode:this.getHospCode
              }
            }
        ).then(this.handleGetDeptInfoSucc)
      },
      handleGetDeptInfoSucc (res) {
        $.hideLoading()
        const data = res.data
        if (data.code == 200) {
          this.depts = data.data.records
        }
      }
    },
    mounted () {
      this.getHospCode = this.$route.query.hospitalCode
      this.getUnitCode = this.$route.query.unitCode
      this.getDeptInfo()
      this.pathName = this.$route.query.pathName
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>

</style>
