<template>
  <div class="officeNameList">
    <div class="department-list">

    <div class="department-search">
      <div class="search-ipt" v-show="!query">       
          <span class="iconfont">&#xe60a;</span>
          <input
            v-model="officeName"
            class="search"
            type="text"
            maxlength="50"
            placeholder="请输入关键字"
          />
          <span class="line"></span>
          <span
            class="cancle"
            @click="switchto"
          >取消</span>
      </div>
      <div class="st"  @click="switchto" v-show="query">
          <span class="iconfont">&#xe60a;</span>
          <span>搜索</span>
      </div>
    </div>
    <ul ref="wrapper"  class="content-list" style="display: block; overflow-y:auto" v-if="!query">
      <li
       class="item"
       v-for="(item,index) of depts"
       :key="index"
       @click="handleDeptClick(item)"
       >{{item.deptName}}</li>
    </ul>
    <div class="content-list" v-show="query">
      <ul class="content-list_unit">
        <li
         class="item"
         :class="activeUnit==item.umId?'activeUnit':''"
         v-for="(item,index) of UnitNameList"
         :key="index"
         @click="handleUnitClick(item)"
         >{{item.unitComName}}</li>
      </ul>
      <ul  ref="wrapper" class="content-list_dept" v-show="query">
        <li
         class="item"
         v-for="(item,index) of officeNameList"
         :key="index"
         @click="handleDeptClick(item)"
         >{{item.deptName}}</li>
      </ul>
    </div>
  </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import { mapMutations,mapState } from 'vuex'
  import axios from 'axios'
  export default {
    name: "UnitDeptList",
    data () {
      return {
        depts:[],
        getHospCode:'',
        getUnitCode:'',
        pathName:'',
        query:true,
        officeName:"",
        officeNameList:[],
        UnitNameList: [],
        activeUnit: ''
      }
    },
    computed: {
      ...mapState(["loginInfo", "staffInfo", "hospitalInfo"]),
    },
    watch:{
      officeName(){
        this.officeName = this.officeName.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,'');
        console.log(this.officeName)
        this.depts = this.officeNameList.filter(item => {
          return item.deptName.indexOf(this.officeName) > -1
        })
      }
    },
    methods:{
      // 获取单位列表
      getUnitgetSelected() {
        axios.get( process.env.API_BASE + "/unitManager/unit-manager/getSelected",
            {
              headers: {
                unitCode: this.hospitalInfo.unitCode,
                hospitalCode: this.hospitalInfo.hospitalCode,
              }
            }
        )
        .then(res => {
          const data = res.data
          if (data.code == 200) {
            if (data.data.length) {
              this.UnitNameList=data.data.sort((a,b)=> a.unitComName == '北京世纪坛医院' ? -1 : 1)
            }
            this.getOfficeSelected(data.data[0].umId)
          }
        }) 
      },
      getOfficeSelected(pid, type) {
        this.activeUnit = pid
        axios.get( process.env.API_BASE + "/departmentManager/department-manager/getSelectedDept?unitId=" + pid,
          {
            headers: {
              unitCode: this.hospitalInfo.unitCode,
              hospitalCode: this.hospitalInfo.hospitalCode,
            }
          }
        )
        .then(res => {
          const data = res.data
          if (data.code == 200) {
            this.officeNameList=data.data
            if (type === 'all') {
              this.depts = JSON.parse(JSON.stringify(data.data))
            }
          }
        })
      },
      handleUnitClick(item) {
        this.getOfficeSelected(item.umId)
      },
      handleDeptClick(item) {
        let deptInfo = {
          deptCode:item.id,
          deptName:item.deptName,
          umId: item.umId,
          umName: item.umName
        }
        this.changeDept(deptInfo)
        this.$router.push({
          path:this.pathName,
          query:{
            source:this.$route.query.source
          }
        })
      },
      switchto(){
        this.query=!this.query
        this.officeName=""
        this.getOfficeSelected('', 'all')
      },
      ...mapMutations(['changeDept'])
    },
    mounted () {
      this.getHospCode = this.$route.query.hospitalCode
      this.getUnitCode = this.$route.query.unitCode
      this.getUnitgetSelected()
      this.pathName = this.$route.query.pathName
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"

.border-bottom
  &:before
    border-color: #ccc
.department-list
  height :100vh;
  overflow : hidden;
  background-color: $bgColor
  
.department-search
  width: 100%;
  height: 0.78rem;
  line-height: 0.78rem;
  padding: 0.2rem 0rem;
  background-color: #fff;
  .search-ipt 
    position: relative;
    .iconfont
      position: absolute;
      left: 0.4rem;
      top: 0rem;
      color: $textColor;
.search 
  background-color: $bgColor;
  padding-left: 0.8rem;
  padding-right: 1.1rem;
  box-sizing: border-box;
  height: 0.78rem;
  display: inline-block;
  width: 95%;
  margin-left: 2.5%;
  border-radius: 5px;
.line 
  width: 1px;
  height: 0.3rem;
  background-color: #D3D3D5;
  display: inline-block;
  position: absolute;
  right: 1.1rem;
  top: 0.24rem;
.cancle 
  position: absolute;
  right: 7px;
  top: 0;
  width: 1rem;
  text-align: center;

.content-list
  height :calc(100% - 1.2rem);
  display: flex;
  .activeUnit 
    background-color: #fff!important;
  .item
    margin-bottom: 1px
    min-height: 0.96rem;
    padding: 0.3rem 0.3rem;
    line-height: 0.42rem;
    box-sizing: border-box;
    word-wrap: break-word;
    word-break: break-all;
    background :#fff;
    font-size: .32rem
    &:active
      background-color: transparent
  .content-list_unit
    width: calc(30% - 0px);
    overflow-y:auto;
    .item
      padding: 0.1rem;
      font-size: 0.28rem;
      background-color: $bgColor;
      min-height: 0.75rem;
      &.active
        background-color: #f5f5f5;
  .content-list_dept
    width: 70%;
    overflow-y:auto;
.st
  text-align :center;
  background:#eee;
  padding-left: 0.8rem;
  padding-right: 1.1rem;
  box-sizing: border-box;
  height: 0.78rem;
  width: 95%;
  margin-left: 2.5%;
  border-radius: 5px;
  color:$textColor;
  font-size:14px;
.item-color{
  background : #fcfcfc ;
}
</style>
