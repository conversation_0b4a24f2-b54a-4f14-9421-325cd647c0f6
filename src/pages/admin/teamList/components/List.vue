<template>
  <div class="list" ref="wrapper">
    <ul>
      <li
       class="item"
       v-for="item of teamList"
       @click="handleTeamClick(item)"
       >{{item.name}}</li>
    </ul>
  </div>
</template>

<script type=text/ecmascript-6>
import BScroll from 'better-scroll'
import { mapMutations } from 'vuex'
  export default {
    name: "TeamList",
    props:["teamList","pathName"],
    methods:{
      handleTeamClick (item) {
        let deptInfo = {
          deptCode:item.id,
          deptName:item.name,
        }
        this.changeDept(deptInfo)
        this.$router.push({
          path:this.pathName,
          query:{
            deptInfo:deptInfo
          }
        })
      },
      ...mapMutations(['changeDept'])
    },
    mounted () {
      this.scroll = new BScroll(this.$refs.wrapper,{click:true})
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  .border-bottom
    &:before
      border-color: #ccc
  .list
    overflow: hidden
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    background-color: $bgColor
    .item
      line-height: .98rem
      padding-left: .3rem
      font-size: .36rem
      background-color: #fff
      margin-bottom: 1px
      &:active
        background-color: transparent
</style>
