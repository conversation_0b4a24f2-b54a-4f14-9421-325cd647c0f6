<template>
  <div>
    <list
     :teamList="teamList"
     :pathName="pathName"
    ></list>
  </div>
</template>

<script type=text/ecmascript-6>
  import List from './components/List'
  import { mapState } from 'vuex'
  export default {
    name: "TeamList",
    components:{
      List
    },
    computed: {
      ...mapState(['loginInfo'])
    },
    data () {
      return {
        teamList:[],
        getHospCode:'',
        getUnitCode:'',
        companyCode:'',
        pathName:'',
      }
    },
    methods:{
      getTeamInfo () {
        $.showLoading()
        this.$api.getOfficeAndTeamList({
          pageSize:'20',
          currentPage:1,
          type:2,
          companyCode:this.loginInfo.userOffice[0].companyCode
        }) .then(this.handleGetTeamInfoSucc)
      },
      handleGetTeamInfoSucc (res) {
        this.teamList = res.officeList
      }
    },
    mounted () {
      this.getTeamInfo()
      this.pathName = this.$route.query.pathName
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>

</style>
