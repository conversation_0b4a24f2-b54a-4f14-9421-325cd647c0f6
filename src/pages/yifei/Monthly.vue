<template>
  <div class="monthly">
    <div>
      <div class="monthly-title">
        <span class="date"> {{list.month}}</span>
        <span>{{list.officeName}}</span>
      </div>
      <!-- 上部分统计 -->
      <div class="monthly-total"> 
        <div class="total-weight"> 
          <span class="titleColor">总重量：</span>
          <span class="contentColor">{{list.gatherWeighMonth || 0}}KG</span>
        </div>
        <div class="total-quantity"> 
          <div class="quantity-count">
            <span class="titleColor"   >总数量：</span>
            <span class="contentColor" >{{list.gatherCountMonth || 0 }}袋</span>
          </div>
          <div class="quantity-divst" v-if="waste.length>0">
            <img src="@/assets/images/triangle.png"  class="bor">
              <span class="list" v-for="(item,index) of waste" :key="index">
                <span>{{item.wasteType}}</span>
                <span>:</span>
                <span>{{item.count}}袋</span>
              </span>
          </div>
        </div>
      </div>
      <!-- 每月 -->
      <div class="monthly-content">
        <div class="content-info" @click="goDetails(item.day)" v-for="(item,i) of dayList" :key="i">
            <div class="info-date">
              <p class="day">{{item.day.split("-")[2]}}</p>
              <p>{{item.day.split("-")[1]}}月</p>
            </div>
            <div class="info-number">
              <div>
                <span class="titleColor"  >重量:</span>
                <span class="contentColor ml3">{{ item.gatherWeighDay  || 0}}KG</span>
              </div>
              <div>
                <span class="titleColor"  >数量:</span>
                <span class="contentColor ml3">{{item.gatherCountDay  || 0 }}袋</span>
              </div>
            </div>
            <span  class="iconfont trouble-icon">&#xe646;</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6 >
export default {
  name:"YifeiMonthly",
  data(){
    return{
      list:[],
      waste:[],
      dayList:[],
      dateday:[]
    }
  },
  methods:{
    goDetails(val){
      this.$router.push({
        path: "/yifeiDetails",
        query: {
          day:val,
          id:this.$route.query.officeId
        }
      });
    },
    monthRequest(){
      this.$api
       .wasteRecordMonthlyReport({
         officeId:this.officeId ,
         month:this.month 
       })
       .then(res => {
         this.list = res;
         this.waste = res.monthWasteTypeList;
         this.dayList=res.dayList;
       })
    },
 
  },
  mounted(){
    this.officeId=this.$route.query.officeId
    this.month=this.$route.query.month
    this.monthRequest() 
  },
  watch(){

  },
}
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .monthly
    height : 100%
    // overflow hidden
    background : #eff0f4
  .contentColor
    color:$contentColor
    font-size:.28rem
  .titleColor
    color:#353535
    font-size:.3rem
  .ml3
    margin-left: .3rem
  .monthly-title
    width : 100%
    height : .88rem
    padding: .37rem 0rem .35rem .29rem
    background :#eff0f4
    color: #5B5B74
    font-size:.28rem
    box-sizing :border-box
    // margin:10px 8px 4px
    // margin-bottom:1
    // margin-left:1
    // margin-right:1
    .date
      margin-right:.2rem
  .monthly-total
    width :100%
    background :#fff
    margin-bottom:.2rem
    .total-weight
      padding-left:.32rem
      height :.98rem
      line-height: .98rem
      border-bottom:1px solid #eff0f4
    .total-quantity
      padding-left:.32rem
      padding-top:.35rem
      padding-bottom:.35rem
      display :flex
  .quantity-count
    margin-right: .5rem
  .quantity-divst
    width: 2.5rem
    position :relative
    top: -.15rem
    // left: 2.8rem
    padding : .22rem .13rem .2rem .3rem
    border-radius:4px;
    font-size:.26rem
    color :#5B5B74
    background:#F3F3F8
    margin-bottom:.1rem
    // display:flex  
    .list
      display :block
      width : 2rem 
      line-height : .4rem 
    .bor
      position :absolute
      top: .16rem
      left:  -.18rem
  .monthly-content
    height:calc(100% - 4rem)
    overflow :auto
    background : #fff  
    .content-info
      position :relative
      height :1.17rem
      display :flex
      padding-top:.27rem
      border-bottom:1px solid #eff0f4 
      .info-date
        height : .8rem
        margin-left:.32rem
        padding:.08rem .25rem
        margin-right:.31rem
        border:1px solid #eff0f4
        text-align :center
        .day
          color :$btnColor
          font-size:.4rem
          font-weight:bold;
      .info-number
        margin-top: -4px
        line-height :.6rem
      .trouble-icon
        position :absolute
        top: .5rem
        right: .17rem
        color:#e6e6e6
        



</style>