<template>
  <div class="details">
    <div class="text">单位:袋</div>
    <div class="details-title" >
      <div class="title-list" >
        <p class="number color1">{{dayWasteTypeList[0].count}}</p>
        <p class="name ">{{dayWasteTypeList[0].wasteType}}</p>
      </div>
      <div class="title-list" >
        <p class="number color2">{{dayWasteTypeList[1].count}}</p>
        <p class="name ">{{dayWasteTypeList[1].wasteType}}</p>
      </div>
      <div class="title-list" >
        <p class="number color3">{{dayWasteTypeList[2].count}}</p>
        <p class="name ">{{dayWasteTypeList[2].wasteType}}</p>
      </div>
      <div class="title-list" >
        <p class="number color4">{{dayWasteTypeList[3].count}}</p>
        <p class="name ">{{dayWasteTypeList[3].wasteType}}</p>
      </div>
      <div class="title-list" >
        <p class="number color5">{{dayWasteTypeList[4].count}}</p>
        <p class="name ">{{dayWasteTypeList[4].wasteType}}</p>
      </div>
      <div class="title-list" >
        <p class="number color6">{{dayWasteTypeList[5].count}}</p>
        <p class="name ">{{dayWasteTypeList[5].wasteType}}</p>
      </div>
    </div>
    <div class="details-body">
      <div class="details-content" v-for="(val,i) of gatherList" :key="i">
        <div class="details-info">
          <div class="info-text">
            <span class="titleColor">收集时间:</span>
            <span  class="contentColor">{{val.gatherTime}}</span>
          </div>
          <div class="info-text">
            <span class="titleColor">医废类型:</span>
            <span class="contentColor">{{val.wasteType}}</span>
          </div>
          <div class="info-text">
            <span class="titleColor">收集人:</span>
            <span class="contentColor">{{val.receivedPersonName}}</span>
          </div>
        </div>
    </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6 >
export default {
  name:"yifeiDetails",
  data(){
    return{
      name:[
        '病理类',
        '化学类',
        '药物类',
        '感染类',
        '损伤类',
        '涉疫类'
      ],
      detaillList:"",
      gatherList:[],
      dayWasteTypeList:[],
    
    }
  },
  methods:{

    DetailsRequest(){
      this.$api
       .wasteRecordMonthlyReportDetail({
        //  unitCode:"BJSYGJ",
        //  hospitalCode:"ZXYSHJ",
         officeId:this.$route.query.id,
         day:this.$route.query.day
       })
       .then(res => {
         this.detaillList = res;
         this.gatherList = res.gatherList;
         this.dayWasteTypeList = res.dayWasteTypeList;
       })
    },

  },
  beforeMount(){
    this.DetailsRequest()
    },
  mounted(){
  },
  watch(){

  },
}
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .details
    height : 100%
    background : #eff0f4
    .text
      color:#b2b2b2
      font-size :.2rem
      line-height :.32rem
      padding-left :.31rem

  .details-title
    display :flex
    justify-content :space-around
    padding: .32rem .29rem .35rem  .29rem
    margin-bottom:.2rem
    background : #fff
    .title-list
      border-right:1px solid #eff0f4
      text-align :center
      padding-right: .29rem
      padding-left: .29rem
      .number
        margin-bottom:.32rem
        font-size:.32rem
      .color1
        color:#FF437D
      .color2
        color:#67A1FF
      .color3
        color:#AD98F1
      .color4
        color:#38C7C4
      .color5
        color:#FFA545
      .color6
        color:#E4393C
      .name
        font-size :.26rem
        color:#333
    .title-list:last-child
      border:0
  .details-body
    // margin-top:1.8rem
    height:calc(100% - 2rem)
    overflow :auto
  .details-content
    background: #fff
    .details-info
      border-bottom:1px solid #eff0f4
      padding-left:.34rem
      position: relative
      .info-text
        line-height : .7rem
  .contentColor
    color:$contentColor
    font-size:.28rem
    position :absolute
    left:0
    margin-left: 1.9rem    
  .titleColor
    color:#353535
    font-size:.3rem
</style>