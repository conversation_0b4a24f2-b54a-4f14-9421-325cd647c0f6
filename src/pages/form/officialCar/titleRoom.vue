<template>
  <div class="titleRoom">
    <div @click="goBack()" class="titleBack">
      <span class="iconfont goBack">&#xe646;</span>返回
    </div>
    <div>{{title}}</div>
    <div class="titleHome" @click="goHome()">首页</div>
  </div>
</template>
<script>
export default {
  props: ["title"],
  data() {
    return {};
  },
  watch: {},
  computed: {},
  filters: {},
  created() {},
  mounted() {},
  methods: {
    goBack() {
      window.history.go(-1);
    },
    goHome() {
      this.$router.push("/carApply");
    }
  }
};
</script>
<style rel="stylesheet/stylus" lang="stylus" scoped type="stylesheet/stylus">
.titleRoom {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 1;
  justify-content: space-between;
  padding: 0 0.23rem !important;
  min-height: 0.8rem !important;
  line-height: 0.8rem !important;
  font-size: 0.3rem !important;
  z-index: 999
  background: #F4F5F9
  .goBack {
    transform: rotate(180deg);
    display: inline-block;
    margin-right: 6px;
  }
}
</style>