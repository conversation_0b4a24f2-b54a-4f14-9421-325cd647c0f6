<template>
  <div class="wrapper">
    <!-- 返回title -->
    <Header title="车辆选择" @backFun="goback"></Header>
    <!-- <titleRoom /> -->
    <div class="wrap_content">
      <div class="carSwipe">
        <div class="swiper-container">
          <div class="swiper-wrapper">
            <div
              v-if="swiperShow"
              class="swiper-slide"
              v-for="(item, itemI) in carList"
              :key="itemI"
            >
              <div class="swiperContent">
                <div class="content_title">
                  <span>{{ item.brand + " " }}</span
                  ><span>{{ item.number + " " }}</span
                  ><span>{{ item.seatsNum + "座" }}</span>
                </div>
                <img
                  class="pic1"
                  referrerpolicy="no-referrer"
                  :src="
                    item.free1 ? item.ossFilePrefix + item.free1 : defaultPng
                  "
                  @error="imgError"
                />
              </div>
            </div>
            <img
              v-if="!swiperShow"
              class="pic2"
              referrerpolicy="no-referrer"
              :src="defaultPng"
            />
          </div>
          <!-- 如果需要分页器 -->
          <div class="swiper-pagination"></div>
        </div>
      </div>
      <div v-if="hasUseTime" class="use_record">
        <div class="show_time">
          <div class="time_text">
            <div class="text_left">用车时间</div>
            <div class="text_update" @click="updateUseCarTime">
              <span class="iconfont">&#xe675;</span>
              <span style="margin: 0 0.3rem 0 0.1rem;">更改</span>
            </div>
          </div>
          <div class="time_left">
            <div class="left_start">
              <div class="box_start left_box">
                起
                <div class="box_line"></div>
              </div>
              <div class="box_right">
                <div>{{ dateRange[0] }}</div>
                <div>{{ weekRange[0] }}</div>
                <div style="color: #6083ff">{{ dateChanged[0] }}</div>
              </div>
            </div>
            <div class="left_end">
              <div class="box_end left_box">止</div>
              <div class="box_right">
                <div>{{ dateRange[1] }}</div>
                <div>{{ weekRange[1] }}</div>
                <div style="color: #ed3030">{{ dateChanged[1] }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="record">预约记录</div>
      <div style="display:block!important;background-color:#f4f5f9">
        <div v-for="(time, index) in timeList" :key="index" class="record_list">
          <div>{{ time.dayTime }}</div>
          <div v-for="j in 2" :key="j" class="table_list">
            <table class="record_time">
              <tr>
                <td
                  v-for="i in 48"
                  :key="i"
                  :class="getSelectTime(i, j, time.rangeTime)"
                  class="time_li"
                ></td>
              </tr>
            </table>

            <div v-if="j == 1" class="table_bottom_time">
              <span>00:00</span>
              <span>06:00</span>
              <span>12:00</span>
            </div>
            <div v-else class="table_bottom_time">
              <span>12:00</span>
              <span>18:00</span>
              <span>24:00</span>
            </div>
            <div v-if="j == 2" class="record_bottom_time">
              <div class="selectBox"></div>
              <div class="selectTimeText">已预订时间段:</div>
              <div
                class="selectTimeRange"
                v-for="(rangeSome, key) in time.rangeTime"
                :key="key"
              >
                {{ rangeSome | timeFilter }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div style="height: 1.2rem;background-color:#f4f5f9"></div> -->
    </div>
    <div class="btn">
      <button
        v-if="!hasUseTime"
        @click="setTimeDate"
        class="weui-btn weui-btn_primary"
      >
        我要用车
      </button>
      <div v-else class="btnFlex">
        <button @click="cancelBook" class="weui-btn cancelBTN">取消</button>
        <button @click="determineBook" class="weui-btn weui-btn_primary">
          确定预约
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import defaultPng from "@/assets/images/noCar.png";
import utils from "@/utils/Global";
import moment from "moment";
import titleRoom from "./titleRoom";
import Swiper from "swiper";
import "swiper/dist/css/swiper.css";
export default {
  name: "carMessage",
  components: {
    titleRoom
  },
  computed: {},
  data() {
    return {
      defaultPng,
      swiperShow: true, //轮播图是否初始化显示
      weekRange: [null, null], //周
      dateRange: [null, null], //日期
      dateChanged: [null, null], //时间
      hasUseTime: false, //是否 有用车时间数据
      carList: [], //车辆列表
      timeList: [
        // {
        //   dayTime: '2022-01-15',
        //   rangeTime: [
        //     ['2022-01-15 8:15:00','2022-01-15 12:15:00'],
        //     ['2022-01-15 18:15:00','2022-01-15 23:59:59'],
        //   ]
        // },{
        //   dayTime: '2022-01-16',
        //   rangeTime: [
        //     ['2022-01-16 3:45:00','2022-01-16 10:30:00'],
        //     ['2022-01-16 15:15:00','2022-01-16 22:00:0'],
        //   ]
        // }
      ]
    };
  },
  filters: {
    timeFilter: msg => {
      // msg表示要过滤的数据，a表示传入的参数
      let newMeg =
        moment(msg[0]).format("HH:mm") + "-" + moment(msg[1]).format("HH:mm");
      return newMeg;
    }
  },
  mounted() {
    this.getOfficialCarList();

    // console.log(this.$route.query);
    //路由穿参 用车时间初始化赋值
    let routeQuery = this.$route.query;
    if (routeQuery && routeQuery.time) {
      this.dateRange = [];
      this.dateChanged = [];
      this.weekRange = [];
      routeQuery.time.forEach(item => {
        this.dateRange.push(item.split(" ")[0]);
        this.dateChanged.push(item.split(" ")[1]);
        this.weekRange.push(utils.getWeek(item.split(" ")[0]));
        this.hasUseTime = true;
      });
    }
    // if(this.$router.query)
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    //获取车辆列表
    getOfficialCarList() {
      let that = this;
      this.$api
        .getOfficialCarList({
          currentPage: 1,
          pageSize: 9999,
          status: 0
        })
        .then(res => {
          if (res && res.list.length > 0) {
            that.carList = res.list;
            // console.log(that.carList);
            that.getSwiper();
          } else {
            $.toast("暂无车辆数据！", "text");
            that.swiperShow = false;
          }
        });
    },
    //渲染轮播图
    getSwiper() {
      let that = this;
      let initialSlide = sessionStorage.getItem("swiperIndex")
        ? Number(sessionStorage.getItem("swiperIndex"))
        : 0;
      // let initialSlide = 10
      this.$nextTick(() => {
        new Swiper(".swiper-container", {
          loop: true,
          slidesPerView: 1.4, // 控制显示轮播图个数，（可以是小数：显示上一张末尾、下一张开头）
          spaceBetween: 10, // 图片之间间隙间隔
          centeredSlides: true,
          initialSlide: initialSlide,
          // 如果需要分页器
          pagination: ".swiper-pagination",
          paginationType: "fraction",
          onSlideChangeEnd: function(swiper) {
            // console.log(swiper);
            sessionStorage.setItem("swiperIndex", swiper.realIndex);
            // console.log(swiper.activeIndex); //切换结束时，告诉我现在是第几个slide
            (that.timeList = []), //时间列表
              that.getCarDetailById(swiper.realIndex);
          }
        });
      });
    },
    //根据车辆id 查询具体信息
    getCarDetailById(id) {
      let that = this;
      let carDetail = that.carList[id];
      let selectCar = {
        carId: carDetail.id,
        // text: that.carList[id].brand + ' ' + that.carList[id].number + ' ' + that.carList[id].seatsNum,
        brand: carDetail.brand,
        number: carDetail.number,
        seatsNum: carDetail.seatsNum,
        colour: carDetail.colour
      };
      //保存 首页需要的 选中车辆信息
      sessionStorage.setItem("carDetail", JSON.stringify(selectCar));
      this.$api
        .getOfficialCarDateById({
          id: carDetail.id
        })
        .then(res => {
          // console.log(res);
          if (res.list && res.list.length > 0) {
            let timeList = res.list;
            // res.list.forEach(item => {
            //   let startTime = moment(item.startDate).format(
            //     "YYYY-MM-DD HH:mm:ss"
            //   );
            //   let endTime = moment(item.endDate).format("YYYY-MM-DD HH:mm:ss");
            //   timeList.push([startTime, endTime]);
            // });
            //赋值选中车辆预定时间
            selectCar.timeList = timeList;
            sessionStorage.setItem("carDetail", JSON.stringify(selectCar));
            that.setTimeList(timeList);
          }
        });
    },
    //图片报错使用默认图片
    imgError(event) {
      let img = event.srcElement;
      img.src = require("@/assets/images/noCar.png");
      img.onerror = null; //防止闪图
    },
    /**
     * 我要用车
     */
    setTimeDate() {
      this.$router.push("/dateTime");
    },
    //取消 重新进入约车页面
    cancelBook() {
      this.$router.push({
        path: "/carMessage"
        // query: {
        //   init: true
        // }
      });
      this.hasUseTime = false;
      this.weekRange = [null, null]; //周
      this.dateRange = [null, null]; //日期
      this.dateChanged = [null, null]; //时间
      sessionStorage.setItem("swiperIndex", 0);
      this.getOfficialCarList();
    },
    //确定 进入申请页
    determineBook() {
      let dateTimeRange = this.$route.query.time;
      let carDetail = JSON.parse(sessionStorage.getItem("carDetail"));
      if (carDetail && carDetail.timeList && carDetail.timeList.length > 0) {
        let carDetailTimeList = carDetail.timeList;
        let timeFlag = carDetailTimeList.filter(e => {
          let diff1 = moment(dateTimeRange[0]).diff(moment(e[1]), "minutes");
          let diff2 = moment(dateTimeRange[1]).diff(moment(e[0]), "minutes");
          return diff1 < 0 && diff2 >= 0;
        });
        if (timeFlag.length > 0) {
          $.toast.prototype.defaults.duration = 3500;
          return $.toast(
            "选择时间与已预约时间段" +
              timeFlag.join("-") +
              "相冲突,请重新选择或更换其他车辆！",
            "text"
          );
        }
        $.toast.prototype.defaults.duration = 1500;
      }
      this.$router.push({
        path: "carApply",
        query: {
          dateRange: [this.dateRange[0], this.dateRange[1]],
          dateChanged: [this.dateChanged[0], this.dateChanged[1]],
          weekRange: [this.weekRange[0], this.weekRange[1]]
        }
      });
    },
    //重新选择 用车时间
    updateUseCarTime() {
      this.$router.push({
        path: "dateTime",
        query: {
          dateRange: [this.dateRange[0], this.dateRange[1]],
          dateChanged: [this.dateChanged[0], this.dateChanged[1]],
          weekRange: [this.weekRange[0], this.weekRange[1]]
        }
      });
    },
    setTimeList(timeList) {
      // let data = {
      //   timeArray: [
      //     ['2022-01-16 3:45:00','2022-01-16 10:30:00'],
      //     ['2022-01-16 15:15:00','2022-01-16 22:00:00'],
      //     ['2022-01-16 22:15:00','2022-01-16 22:45:00'],
      //     ['2022-01-17 15:15:00','2022-01-19 22:00:00'],
      //   ]
      // }

      if (timeList && timeList.length > 0) {
        for (let i = 0; i < timeList.length; i++) {
          const element = timeList[i];
          if (!element[0] || !element[1]) {
            continue;
          }
          let dayDiff = moment(moment(element[1]).format("YYYY-MM-DD")).diff(
            moment(moment(element[0]).format("YYYY-MM-DD")),
            "day"
          );
          for (let d = 0; d <= dayDiff; d++) {
            let [dayTime, rangeTime] = ["", []];
            if (d == 0) {
              // 开始时间和结束时间在同一天
              dayTime = moment(element[0]).format("YYYY-MM-DD");
              if (d == dayDiff) {
                rangeTime = [element[0], element[1]];
                //开始时间和结束时间不在同一天 且第一天 结束时间为当天的最后一秒
              } else {
                rangeTime = [
                  element[0],
                  moment(element[1]).format("YYYY-MM-DD 23:59:59")
                ];
              }
            } else {
              dayTime = moment(element[0])
                .add(d, "days")
                .format("YYYY-MM-DD");
              //结束时间的 最后一天 开始时间为 最后一天的 0点
              if (d == dayDiff) {
                //开始时间结束时间 都为 00 则不展示
                if (
                  moment(element[1]).format("YYYY-MM-DD 00:00:00") == element[1]
                ) {
                  continue;
                }
                rangeTime = [
                  moment(element[1]).format("YYYY-MM-DD 00:00:00"),
                  element[1]
                ];
              } else {
                rangeTime = [
                  moment(element[0])
                    .add(d, "days")
                    .format("YYYY-MM-DD 00:00:00"),
                  moment(element[0])
                    .add(d, "days")
                    .format("YYYY-MM-DD 23:59:59")
                ];
              }
            }
            //判断时间是否为同一天 同一天只增加rangeTime
            let timeIndex = this.timeList.findIndex(e => e.dayTime == dayTime);
            if (timeIndex != -1) {
              this.timeList[timeIndex].rangeTime.push(rangeTime);
            } else {
              this.timeList.push({
                dayTime,
                rangeTime: [rangeTime]
              });
            }
          }
        }
      }
    },
    //获取默认选中的样式
    getSelectTime(i, j, timeList) {
      //获取时间段对应的索引值
      let block = j == 1 ? i : 48 + i;
      for (let timeIndex = 0; timeIndex < timeList.length; timeIndex++) {
        const time = timeList[timeIndex];
        //获取开始时间结束时间对应的 分钟
        const starMinutes = moment
          .duration(moment(time[0]).format("HH:mm"))
          .asMinutes();
        let endMinutes = moment
          .duration(moment(time[1]).format("HH:mm"))
          .asMinutes();
        //考虑没有24:00整点 且时间段跨天数 则手动将23:59:59++
        if (endMinutes == 1439) endMinutes++;
        //满足条件的索引 更改为选中的样式
        if (starMinutes < block * 15 && block * 15 <= endMinutes) {
          return "bgChange";
        }
      }
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper {
  background: #F4F5F9;
  height: 100%;
  overflow: hidden
  // padding-top: 5px;
  .wrap_content{
    margin-top: 8px;
    height: calc(100% - 2.5rem)
    overflow: scroll
    > div {
      itemBaseStyle();
      padding: 0 0.2rem;
      display: flex;
    }
  }



  .btn {
    padding: 0.22rem 10px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;

    .btnFlex {
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 100%;

      .cancelBTN {
        margin-top: 15px;
        background: #fff;
        color: #262626;
        border: 2px solid #CACAD2;
      }

      button {
        width: 47%;
      }
    }
  }
}

.carSwipe {
  // margin-top: 0.8rem;
  height: 30%;
  background: #fff;

  .swiperContent {
    width: 100%;
    height: 100%;

    .content_title {
      height: 30px;
      line-height: 30px;
      text-align: center;

      display: flex;
      span {
        flex: 1
        color: black;
        margin-bottom: 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 0.3rem;
      }
    }

    .pic1 {
      width: 100%;
      height: calc(100% - 35px);
      border-radius: 8px;
    }
  }

  .pic2 {
    width: 100%;
    height: 100%;
  }
}

.use_record {
  margin-top: 5px;
  width: 100%;
  padding: 0 0.08rem !important;
  box-sizing: border-box;
  background-color: #F4F5F9 !important;
  display: block;

  .show_time {
    background: #F4F5F9 url('../../../assets/images/useCarbg.png') no-repeat;
    background-size: 100% 100%;
    // border-radius: 16px;
    padding: 0.1rem;
    width: 100%;
    height: 2.1rem;

    // background-color:#FFFFFF
    // display flex
    .time_text {
      width: 100%;
      height: 0.45rem;
      line-height: 0.45rem;
      padding: 0 0.2rem;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;

      .text_left {
        color: #fff;
        font-size: 0.29rem;
        font-weight: 600;
      }

      .text_update {
        margin-top: 0.1rem;
        font-size: 0.31rem;
        color: #3562DB;
        font-family: PingFangSC-Medium, PingFang SC;

        span {
          font-weight: 600;
        }
      }
    }

    .time_left {
      width: 100%;
      height: 1.6rem;
      padding-left: 0.2rem;
      padding-right: 20%;
      box-sizing: border-box;

      >div {
        height: 0.8rem;
        display: flex;
        align-items: center;
      }

      .left_box {
        height: 0.4rem;
        width: 0.4rem;
        line-height: 0.4rem;
        background: red;
        text-align: center;
        color: #fff;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        z-index: 1;
        border-radius: 5px;
        font-size: 0.23rem;
      }

      .box_right {
        display: flex;
        justify-content: space-around;
        font-size: 0.3rem;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #0F1115;
        width: calc(100% - 0.7rem);
      }

      .left_start {
        .box_start {
          background-color: #6083FF;
          position: relative;

          .box_line {
            position: absolute;
            top: 0.4rem;
            left: 50%;
            height: 0.4rem;
            border: 1px dashed #B1B3BA;
          }
        }
      }

      .left_end {
        .box_end {
          background-color: #ED3030;
        }
      }
    }
  }
}

.record {
  height: 21px;
  font-size: 15px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: #0F1115;
  line-height: 21px;
  background-color: #F4F5F9 !important;
}

.bgChange {
  background-color: #D1FFFE;
  border: 1px solid #3562DB !important;
}

.record_list {
  width: 100%;
  box-sizing: border-box;
  padding: 0 10px;
  background: #fff;
  border-radius: 16px;
  margin-bottom: 10px;

  .table_list {
    width: 100%;

    .table_bottom_time {
      width: 100%;
      display: flex;
      justify-content: space-between;
      height: 0.55rem;
      line-height: 0.55rem;
      font-size: 0.24rem;
      color: #ABABAB;

      span {
        // flex 1
      }
    }
  }

  .record_time {
    width: 100%;

    tr {
      border: 1px solid #E2E2E2;
      pading: 0 2%;
      box-sizing: border-box;
      display: flex;
      width: 100%;

      .time_li {
        width: 2%;
        height: calc(2vw - 2px);
        border: 1px solid #E2E2E2;
      }
    }
  }

  .record_bottom_time {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding-bottom: 0.2rem;

    .selectBox {
      width: 2%;
      height: calc(2vw - 2px);
      background-color: #D1FFFE;
      border: 1px solid #3562DB;
      margin-left: 0.1rem;
    }

    .selectTimeText {
      color: #3562DB;
      font-size: 0.25rem;
      margin: 0 0.1rem;
      height: 0.6rem;
      line-height: 0.6rem;
    }

    .selectTimeRange {
      font-size: 0.24rem;
      display: inline-block;
      border-radius: 3px;
      background-color: #F4F5F9;
      line-height: 0.5rem;
      height: 0.5rem;
      padding: 0 0.1rem;
      margin-right: 0.15rem;
    }
  }
}

.swiper-container {
  width: 100%;
  margin: 10px 0;
}

.swiper-pagination {
  width: 55px;
  height: 20px;
  font-size: 14px;
  line-height: 20px;
  background: #747578;
  border-radius: 8px;
  color: #fff;
  left: calc(50% - 30px);
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;
  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  transition: 300ms;
}

.swiper-slide:not(.swiper-slide-active) {
  transform: scale(0.8);
  opacity: 0.5;
}
</style>
