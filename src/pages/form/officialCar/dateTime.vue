<template>
  <div class="wrapper">
    <Header title="选择时间" @backFun="goback"></Header>
    <!-- <titleRoom title="选择时间" /> -->
    <!-- <van-cell title="选择年月日" :value="date" hidden /> -->
    <!-- <div class="wrap_content">

    </div> -->
    <div class="calendar">
      <van-calendar
        ref="calendar"
        v-model="show"
        :poppable="false"
        :show-confirm="false"
        @select="onConfirmDate"
        color="#3562DB"
        type="range"
        :show-title="false"
        :allow-same-day="true"
        :formatter="formatter"
        row-height="30"
        :default-date="dateTimeDefault"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </div>
    <div class="box_shadow"></div>
    <div class="btn">
      <div v-if="timeBoxShow" class="show_time">
        <div class="time_left">
          <div class="left_start">
            <div class="box_start left_box">
              起
              <div class="box_line"></div>
            </div>
            <div class="box_right">
              <div>{{ dateRange | lengthFilter(0) }}</div>
              <div>{{ weekRange | lengthFilter(0) }}</div>
              <div>{{ dateChanged | lengthFilter(0) }}</div>
            </div>
          </div>
          <div class="left_end">
            <div class="box_end left_box">止</div>
            <div class="box_right">
              <div>{{ dateRange | lengthFilter(1) }}</div>
              <div>{{ weekRange | lengthFilter(1) }}</div>
              <div>{{ dateChanged | lengthFilter(1) }}</div>
            </div>
          </div>
        </div>
        <div class="time_right" @click="resetTime">清除</div>
      </div>
      <button @click="saveTime" class="weui-btn weui-btn_primary">确认</button>
    </div>
    <van-popup v-model="Tame" :close-on-click-overlay="false" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="time"
        title="选择时间"
        @confirm="confirmHan"
        :filter="filter"
        cancel-button-text=" "
      />
      <!-- @cancel="cancelHan" -->
    </van-popup>
  </div>
</template>
<script>
import utils from "@/utils/Global";
import moment from "moment";
import titleRoom from "./titleRoom";
export default {
  name: "dateTime",
  components: {
    titleRoom
  },
  data() {
    return {
      show: true, //日历显示
      Tame: false, //时间显示
      weekRange: [], //周
      dateRange: [], //日期
      dateChanged: [], //时间
      currentDate: "",
      dateTimeDefault: null, //日历初始化数据
      minDate: new Date(),
      maxDate: new Date(
        moment()
          .month(moment().month() + 2)
          .format("YYYY/MM/DD")
      ),
      timeBoxShow: false
    };
  },
  filters: {
    lengthFilter(range, length) {
      return range.length > length ? range[length] : null;
    }
  },
  watch: {
    timeBoxShow(flag) {
      if (flag) {
        $(".btn").height("2.58rem");
        $(".calendar").height("calc(95% - 4rem)");
      } else {
        $(".btn").height("0.98rem");
        $(".calendar").height("calc(95% - 2.4rem)");
      }
    }
  },
  mounted() {
    let routeQuery = this.$route.query;
    if (routeQuery && routeQuery.dateRange) {
      this.dateRange = routeQuery.dateRange;
      this.dateChanged = routeQuery.dateChanged;
      this.weekRange = routeQuery.weekRange;
      this.timeBoxShow = true; //回显时间
      //初始化 日历赋值
      this.dateTimeDefault = [
        new Date(moment(routeQuery.dateRange[0]).format("YYYY/MM/DD")),
        new Date(moment(routeQuery.dateRange[1]).format("YYYY/MM/DD"))
      ];
    }
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    //日历时间
    formatter(day) {
      // console.log(day);

      if (day.type === "start") {
        day.bottomInfo = "起";
      } else if (day.type === "end") {
        day.bottomInfo = "止";
      }
      return day;
    },
    //时间过滤器
    filter(type, options) {
      if (type === "minute") {
        return options.filter(option => option % 15 === 0);
      }
      return options;
    },
    onConfirmDate(date) {
      this.dateRange = [];
      this.weekRange = [];
      this.timeBoxShow = true;
      this.Tame = true;
      date.forEach(e => {
        this.dateRange.push(e != null ? moment(e).format("YYYY-MM-DD") : null);
        this.weekRange.push(e != null ? utils.getWeek(e) : null);
      });
      // console.log(this.dateRange);

      //这里使用了 moment组件
      //npm install moment --save    可以直接下载使用
    },
    confirmHan(date) {
      if (this.dateRange.length == 0 || this.dateRange[1] == null) {
        this.dateChanged = [];
      }
      this.dateChanged.push(date);
      this.Tame = false;
    },
    cancelHan() {
      // $.toast('请选择时间！');
    },
    //提交时间
    saveTime() {
      if (
        this.dateRange.includes(null) ||
        this.dateChanged.includes(null) ||
        this.dateRange.length == 0 ||
        this.dateChanged.length == 0
      ) {
        return $.toast("请完善时间选择!", "text");
      }
      let dateTimeRange = [];
      for (let i = 0; i < this.dateRange.length; i++) {
        dateTimeRange.push(this.dateRange[i] + " " + this.dateChanged[i]);
      }
      if (moment(dateTimeRange[0]).diff(moment(), "minutes") <= 0) {
        return $.toast("用车开始时间不能小于当前时间!", "text");
      }
      if (
        moment(dateTimeRange[1]).diff(moment(dateTimeRange[0]), "minutes") <= 0
      ) {
        return $.toast("用车结束时间不能小于用车开始时间!", "text");
      }
      let carDetail = JSON.parse(sessionStorage.getItem("carDetail"));
      if (carDetail && carDetail.timeList && carDetail.timeList.length > 0) {
        let carDetailTimeList = carDetail.timeList;
        let timeFlag = carDetailTimeList.filter(e => {
          let diff1 = moment(dateTimeRange[0]).diff(moment(e[1]), "minutes");
          let diff2 = moment(dateTimeRange[1]).diff(moment(e[0]), "minutes");
          return diff1 < 0 && diff2 >= 0;
        });
        if (timeFlag.length > 0) {
          $.toast.prototype.defaults.duration = 3500;
          return $.toast(
            "选择时间与已预约时间段" +
              timeFlag.join("-") +
              "相冲突,请重新选择或更换其他车辆！",
            "text"
          );
        }
        $.toast.prototype.defaults.duration = 1500;
      }
      this.$router.push({
        path: "/carMessage",
        query: {
          time: dateTimeRange
        }
      });
      // console.log(dateTimeRange);
    },
    //重置时间选择
    resetTime() {
      //时间和 日历清空
      this.weekRange = [];
      this.dateRange = [];
      this.dateChanged = [];
      this.timeBoxShow = false;
      this.dateTimeDefault = null;
      this.$refs.calendar.reset();
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/varibles.styl"
@import "~styles/mixins.styl"
.wrapper>>> .van-calendar__day {
	  align-items: baseline;
    font-size: 0.27rem;
    padding-top: 0.08rem;
}
.wrapper>>> .van-calendar__bottom-info {
	bottom 0
}
.wrapper>>> .van-calendar__body {
  position static;
  z-index 9;
  -webkit-overflow-scrolling auto!important
}

.wrapper {
  width 100%
  height 100%
  overflow hidden
  .calendar {
    height calc(95% - 2.4rem)
    margin-top 10px
  }
  .box_shadow {
    width 100%
    border: 1px solid #F1F1F6;
  }
  .btn{
    width 90%
    height 0.98rem
    margin 0 5%
    padding-top: .22rem
    padding-bottom: .22rem
    position: fixed
    bottom: 0
    left: 0
    right: 0
  }
  .show_time {
    width 100%
    height: 1.6rem
    background-color:#FFFFFF
    display flex
    .time_left {
      width 80%
      height 100%
      padding-left 0.2rem
      >div {
        height 0.8rem
        display flex
        align-items center
      }
      .left_box {
        height 0.4rem
        width 0.4rem
        line-height 0.4rem
        background red
        text-align center
        color #fff
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        z-index 1
        border-radius 5px
        font-size 0.23rem
      }
      .box_right {
        display flex
        justify-content space-around
        font-size: 0.3rem;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #0F1115;
        width calc(100% - 0.7rem)
      }
      .left_start {
        .box_start {
          background-color #6083FF
          position relative
          .box_line {
            position absolute
            top 0.4rem
            left 50%
            height 0.4rem
            border 1px dashed #B1B3BA
          }
        }
      }
      .left_end {
        .box_end {
          background-color #ED3030
        }
      }
    }
    .time_right {
      width 20%
      height 1.6rem
      line-height 1.6rem
      text-align center
      color #29BEBC
      font-weight 600
      font-size 0.32rem
      font-family: PingFangSC-Medium, PingFang SC;
    }
  }
}
</style>
