<template>
  <div class="wrapper">
    <!-- 返回title -->
    <!-- <titleRoom /> -->
    <Header title="公车预定" @backFun="goback"></Header>
    <div class="wrap_content">
      <div class="name border-bottom">
        <div class="title">申请人员</div>
        <div class="content">
          <input type="text" placeholder="请输入2-20位汉字" v-model="contactName" minlength="2" maxlength="20" />
          <span class="iconfont">&#xe675;</span>
        </div>
      </div>
      <div class="phone border-bottom">
        <div class="title">联系电话</div>
        <div class="content">
          <input type="text" placeholder="请输入电话号码" v-model="contactNumber" maxlength="11" />
          <span class="iconfont">&#xe675;</span>
        </div>
      </div>
      <!--院内的都可以修改科室-->
      <!-- <div class="dept border-bottom" v-if="loginInfo.type == 1"> -->
      <div class="dept border-bottom">
        <div class="title">申请部门</div>
        <div class="content" @click="toSelectDept">
          <span class="value">{{ deptInfo.deptName || staffInfo.officeName }}</span>
          <span class="iconfont">&#xe646;</span>
        </div>
      </div>
      <!--院外不可以修改，只是展示-->
      <!-- <div class="dept border-bottom" v-if="loginInfo.type == 2">
        <div class="title">班组</div>
        <div class="content">
          <span class="value value-bz">{{ dept.deptName }}</span>
        </div>
      </div> -->
      <div style="height:10px;min-height:10px;background:#F4F5F9"></div>
      <div class="border-bottom">
        <div class="title imgTitle" style="width:24px">
          <img class="imgWidth" src="../../../assets/images/icStart.png" alt="" />
          <div class="imgLine" style="bottom: 0"></div>
        </div>
        <div class="content">
          <input type="text" placeholder="请输入出发地" v-model="startLocalNames" minlength="2" maxlength="20" class="dest" />
        </div>
      </div>
      <div class="border-bottom">
        <div class="title imgTitle" style="width:24px">
          <img class="imgWidth" src="../../../assets/images/icEnd.png" alt="" />
          <div class="imgLine" style="top: 0"></div>
        </div>
        <div class="content">
          <input type="text" placeholder="请输入目的地" v-model="endLocalNames" minlength="2" maxlength="20" class="dest" />
        </div>
      </div>
      <!--申报描述-->
      <div>
        <div class="weui-cells_form desc-form">
          <div class="er-level border-bottom">
            <div class="er-text title">申报描述</div>
          </div>
          <div class="weui-cell desc">
            <div class="weui-cell__bd">
              <textarea
                class="weui-textarea"
                placeholder="请输入您要申报的内容描述，字数在500字以内"
                maxlength="500"
                rows="5"
                ref="textarea"
                v-model="questionDescription"
                @keydown="keydown($event)"
                @blur="handleTextareaBlurEvent"
              ></textarea>
            </div>
          </div>
        </div>
      </div>
      <div v-if="!selectCarBoxShow" class="border-bottom">
        <div class="title">选择车辆</div>
        <div class="content" @click="selectCar">
          <span>点击选择&emsp;</span>
          <van-icon name="arrow" />
          <!-- <button class="weui-btn car-btn_primary" @click="selectCar">
            <img
              style="width:24px"
              src="../../../assets/images/icCar.png"
              alt=""
            />
            点击选择
          </button> -->
        </div>
      </div>
      <div v-else class="border-bottom car_message">
        <div class="message_left">选择车辆</div>
        <div class="message_right">
          <div class="changed_car">
            <span>已选车辆</span>
            <div class="text_update" @click="updateSelect('car')"><span class="iconfont">&#xe675;</span><span style="margin: 0 0.3rem 0 0.1rem;">更改</span></div>
          </div>
          <div style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
            <span>{{ carDetail.brand + " " }}</span
            ><span>{{ carDetail.number + " " }}</span
            ><span>{{ carDetail.seatsNum + "座" }}</span>
          </div>
          <div class="changed_car">
            <span>用车时间</span>
            <div class="text_update" @click="updateSelect('time')"><span class="iconfont">&#xe675;</span><span style="margin: 0 0.3rem 0 0.1rem;">更改</span></div>
          </div>
          <div class="time_left">
            <div class="left_start">
              <div class="box_start left_box">
                起
                <div class="box_line"></div>
              </div>
              <div class="box_right">
                <div>{{ dateRange[0] }}</div>
                <div>{{ weekRange[0] }}</div>
                <div>{{ dateChanged[0] }}</div>
              </div>
            </div>
            <div class="left_end">
              <div class="box_end left_box">止</div>
              <div class="box_right">
                <div>{{ dateRange[1] }}</div>
                <div>{{ weekRange[1] }}</div>
                <div>{{ dateChanged[1] }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="height: 1rem"></div>
    </div>
    <div class="btn" @click="saveTemporaryUserInfo">
      <button class="weui-btn weui-btn_primary">提交</button>
    </div>
  </div>
</template>

<script>

import utils from "@/utils/Global";
import titleRoom from "./titleRoom";
import { mapState, mapMutations } from "vuex";
import PopBottom from "@/common/PopupBottom";
export default {
  name: "carApply",
  components: {
    PopBottom,
    titleRoom
  },
  computed: {
    ...mapState(["loginInfo", "deptInfo", "staffInfo"])
  },
  data() {
    return {
      questionDescription: "", //申报描述
      contactName: "", //姓名
      contactNumber: "", //电话
      startLocalNames: "", //起点
      endLocalNames: "", //终点
      selectCarBoxShow: false, //回显车辆数据 是否显示
      weekRange: [null, null], //周
      dateRange: [null, null], //日期
      dateChanged: [null, null], //时间
      carDetail: {}, //预约车辆信息
      beforePageUrl: ""
    };
  },
  beforeRouteEnter(to, from, next) {

    let whiteArray = ["/carMessage", "/dateTime", "/department"];
    if (whiteArray.includes(from.path)) {
      let carApplyInfo = JSON.parse(sessionStorage.getItem("carApplyInfo"));
      next(that => {

        // 通过 `vm` 访问组件实例
        //初始化赋值
        that.questionDescription = carApplyInfo.questionDescription; //申报描述
        that.contactName = carApplyInfo.contactName; //姓名
        that.contactNumber = carApplyInfo.contactNumber; //电话
        that.startLocalNames = carApplyInfo.startLocalNames; //起点
        that.endLocalNames = carApplyInfo.endLocalNames; //终点

        let selectCarBoxShow = carApplyInfo.selectCarBoxShow ? carApplyInfo.selectCarBoxShow : false; //无车辆数据
        if (selectCarBoxShow && (from.path == "/department" || JSON.stringify(to.query) == "{}")) {
          that.selectCarBoxShow = selectCarBoxShow;
          that.dateRange = carApplyInfo.dateRange;
          that.dateChanged = carApplyInfo.dateChanged;
          that.weekRange = carApplyInfo.weekRange;
          that.carDetail = carApplyInfo.carDetail;
        }
        //路由穿参赋值 ( 选择车辆后进入 该页面 为页面赋值车辆信息)
        if (JSON.stringify(to.query) != "{}") {
          let routeQuery = to.query;
          that.dateRange = routeQuery.dateRange;
          that.dateChanged = routeQuery.dateChanged;
          that.weekRange = routeQuery.weekRange;
          that.selectCarBoxShow = true; //回显车辆
          that.carDetail = JSON.parse(sessionStorage.getItem("carDetail"));
        }
      });
    }
     next(vm => {
      vm.beforePageUrl = from.path;
    });
    next();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if (this.beforePageUrl == "/") {
      this.getDialogShow();
    }

    if (!this.contactName) {
      this.contactName = this.loginInfo.staffName;
      this.contactNumber = this.loginInfo.phone;
    }
    // this.stopScorll()

    // let routeQuery = this.$route.query
    // if(routeQuery && routeQuery.dateRange) {
    //   this.dateRange = routeQuery.dateRange
    //   this.dateChanged = routeQuery.dateChanged
    //   this.weekRange = routeQuery.weekRange
    //   this.selectCarBoxShow = true //回显车辆
    //   this.carDetail = JSON.parse(sessionStorage.getItem('carDetail'))
    // }
  },
  methods: {
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
    getDialogShow() {
      let params = {
        userId: this.loginInfo.staffId,
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getNotCommentWarn(params).then(res => {
        if (res.commentCount > 0 && res.commentStatus == "1") {
          this.$dialog.confirm({
            message: "您当前有未评价的工单！",
            confirmButtonText: "去评价",
            cancelButtonText: "忽略",
             confirmButtonColor: "#3562db"
          })
            .then(() => {
              this.$router.push({
                path: "/orderAcceptance"
              });
            })
            .catch(() => {});
        }
      });
    },
    touchSatrtFunc(evt) {
      try {
        //evt.preventDefault(); //阻止触摸时浏览器的缩放、滚动条滚动等
        var touch = evt.touches[0]; //获取第一个触点
        var x = Number(touch.pageX); //页面触点X坐标
        var y = Number(touch.pageY); //页面触点Y坐标
        //记录触点初始位置
        startX = x;
        startY = y;
      } catch (e) {
        // alert('touchSatrtFunc：' + e.message);
      }
    },
    stopScorll() {
      // 首先禁止body
      document.body.addEventListener(
        "touchmove",
        function(e) {
          e.preventDefault(); // 阻止默认的处理方式(阻止下拉滑动的效果)
        },
        { passive: false }
      ); // passive 参数不能省略，用来兼容ios和android

      // 然后取得触摸点的坐标
      var startX = 0,
        startY = 0;
      //touchstart事件
      function touchSatrtFunc(evt) {
        try {
          //evt.preventDefault(); //阻止触摸时浏览器的缩放、滚动条滚动等
          var touch = evt.touches[0]; //获取第一个触点
          var x = Number(touch.pageX); //页面触点X坐标
          var y = Number(touch.pageY); //页面触点Y坐标
          //记录触点初始位置
          startX = x;
          startY = y;
        } catch (e) {
          alert("touchSatrtFunc：" + e.message);
        }
      }
      document.body.addEventListener("touchstart", touchSatrtFunc, false);

      // 然后对允许滚动的条件进行判断，这里讲滚动的元素指向body
      var _ss = document.body;
      _ss.ontouchmove = function(ev) {
        var _point = ev.touches[0],
          _top = _ss.scrollTop;
        // 什么时候到底部
        var _bottomFaVal = _ss.scrollHeight - _ss.offsetHeight;
        // 到达顶端
        if (_top === 0) {
          // 阻止向下滑动
          if (_point.clientY > startY) {
            ev.preventDefault();
          } else {
            // 阻止冒泡
            // 正常执行
            ev.stopPropagation();
          }
        } else if (_top === _bottomFaVal) {
          // 到达底部 如果想禁止页面滚动和上拉加载，讲这段注释放开，也就是在滚动到页面底部的制售阻止默认事件
          // 阻止向上滑动
          // if (_point.clientY < startY) {
          //     ev.preventDefault();
          // } else {
          //     // 阻止冒泡
          //     // 正常执行
          //     ev.stopPropagation();
          // }
        } else if (_top > 0 && _top < _bottomFaVal) {
          ev.stopPropagation();
        } else {
          ev.preventDefault();
        }
      };
    },
    //空格校验
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    },
    //更改选择车辆car 时间time
    updateSelect(type) {
      this.saveFormInfo();
      let path = "";
      let query = {};
      if (type == "car") {
        path = "/carMessage";
        query = {
          time: [this.dateRange[0] + " " + this.dateChanged[0], this.dateRange[1] + " " + this.dateChanged[1]]
        };
      } else {
        path = "/dateTime";
        query = {
          dateRange: this.dateRange,
          dateChanged: this.dateChanged,
          weekRange: this.weekRange
        };
      }

      this.$router.push({
        path,
        query
      });
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    //选择车辆
    selectCar() {
      this.saveFormInfo();
      this.$router.push("/carMessage");
    },
    /**
     * 保存
     */
    saveTemporaryUserInfo() {
      // console.log(this.deptInfo);
      let that = this;
      if (!this.carDetail.carId) {
        $.toast("请选择车辆", "text");
        return;
      }
      if (this.contactNumber.length < 11 && this.contactNumber != "") {
        $.toast("请输入正确的电话号", "text");
        return;
      }
      let params = {
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode,
        userId: this.loginInfo.id,
        realName: this.loginInfo.staffName,
        jobNumber: this.staffInfo.staffNumber,
        phoneNumber: this.loginInfo.phone,
        questionDescription: this.questionDescription,
        contactName: this.contactName,
        contactNumber: this.contactNumber,
        startLocalNames: this.startLocalNames,
        endLocalNames: this.endLocalNames,
        startDate: this.dateRange[0] + " " + this.dateChanged[0] + ":00",
        endDate: this.dateRange[1] + " " + this.dateChanged[1] + ":00",
        cars: this.carDetail.carId,
        carsName: this.carDetail.brand,
        carsNum: this.carDetail.number,
        carsSeatNum: this.carDetail.seatsNum,
        carsColour: this.carDetail.colour,
        sourcesDept: this.deptInfo.deptCode || this.loginInfo.deptId, //科室，部门
        sourcesDeptName: this.deptInfo.deptName || this.staffInfo.officeName, //科室，部门
        deptCode: this.loginInfo.deptId, //科室，部门
        deptName: this.loginInfo.deptName, //科室，部门
        workTypeCode: 15,
        workSources: 1,
        type: this.staffInfo.teamId ? 2 : 1,
        staffId: this.staffInfo.staffId
      };
      let param = this.$qs.stringify(params);
      $.showLoading("提交中…");
      this.axios
        .post(__PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat", param, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(this.handleRequestSucc);
      // }
    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      $.hideLoading();
      if (res.data.code == "200") {
        $.toast("工单提交成功", "success", () => {
          sessionStorage.removeItem("hospitalCode");
          sessionStorage.removeItem("unitCode");
          sessionStorage.removeItem("url");
          sessionStorage.removeItem("carDetail");
          sessionStorage.removeItem("carApplyInfo");
          sessionStorage.removeItem("swiperIndex");
        });
        setTimeout(() => {
          // api.closeFrame();
          this.$YBS.apiCloudCloseFrame();
        }, 1000);
      } else {
        $.toast(res.data.message, "text");
      }
    },

    /**
     * 选择科室（院内）
     */
    toSelectDept() {
      this.saveFormInfo();
      this.$router.push({
        path: "/department",
        query: {
          hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
          unitCode: this.loginInfo.userOffice[0].unitCode,
          pathName: "/carApply"
        }
      });
    },
    //页面数据保存缓存
    saveFormInfo() {
      let carApplyInfo = {
        questionDescription: this.questionDescription, //申报描述
        contactName: this.contactName, //姓名
        contactNumber: this.contactNumber, //电话
        startLocalNames: this.startLocalNames, //起点
        endLocalNames: this.endLocalNames, //终点
        selectCarBoxShow: this.selectCarBoxShow //无车辆数据
      };
      if (this.selectCarBoxShow) {
        Object.assign(carApplyInfo, {
          selectCarBoxShow: true,
          dateRange: this.dateRange,
          dateChanged: this.dateChanged,
          weekRange: this.weekRange,
          carDetail: this.carDetail
        });
      }
      //跳转之前保存当前数据
      sessionStorage.setItem("carApplyInfo", JSON.stringify(carApplyInfo));
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"

  .imgWidth {
    width: 12px
  }
  .car_message {
    padding 0.2rem
    width 100%
    box-sizing border-box
    .message_left {
      width 25%
    }
    .message_right {
      width 75%
      padding 0 0.2rem
      box-sizing border-box
      >div {
        height 0.7rem
        line-height 0.7rem
      }
      .changed_car {
        display flex
        justify-content space-between
        margin-top 0.15rem
        >span {
          color #888888
          font-size 0.28rem
        }
        .text_update {
          // margin-top 0.1rem
          font-size 0.31rem
          color #3562DB
          font-family: PingFangSC-Medium, PingFang SC;
          span {
            font-weight 600
          }
        }
      }
      .time_left {
        width 100%
        height 1.6rem!important
        // padding-left 0.2rem
        // padding-right 20%
        // box-sizing border-box
        >div {
          height 0.8rem
          display flex
          align-items center
        }
        .left_box {
          height 0.4rem
          width 0.4rem
          line-height 0.4rem
          background red
          text-align center
          color #fff
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          z-index 1
          border-radius 5px
          font-size 0.23rem
        }
        .box_right {
          display flex
          justify-content space-around
          font-size: 0.3rem;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #0F1115;
          width calc(100% - 0.7rem)
        }
        .left_start {
          .box_start {
            background-color #6083FF
            position relative
            .box_line {
              position absolute
              top 0.4rem
              left 50%
              height 0.4rem
              border 1px dashed #B1B3BA
            }
          }
        }
        .left_end {
          .box_end {
            background-color #ED3030
          }
        }
      }
    }
  }
  .imgTitle {
    position relative
    .imgLine {
      position absolute
      border-left: 1px solid #B1B3BA;
      left: 6px;
      height: 0.3rem;
      z-index: 1;
    }
  }
  .desc-form {
    margin-top: 0;
    background-color: #fff;
    width: 100%
    .desc {
      padding: 0.32rem 0.32rem 0;
      font-size: 0.28rem;
      line-height: 1.5em;
      margin-bottom: 0.2rem;
      color: $contentColor;
      background: #fff;
      textarea {
        display: inline-block;
        // text-indent: 2em;
        font-size: 0.3rem;
        background: #fff;
      }
      textarea::placeholder {
        color: #86909C;
      }
    }
  }
  .car-btn_primary {
    border: 1px solid #3562DB;
    background: #fff;
    color: #3562DB;
    font-weight: 600;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  // $bgColor
.wrapper
  // background: #F4F5F9
  height 100%
  // padding-top 5px
  overflow: hidden
  .wrap_content
    // margin-top: 0.8rem
    height: calc(100% - 2.5rem)
    overflow: scroll
    > div
      itemBaseStyle()
      display flex
      .title
        width: 1.97rem
      .content
        color: $textColor
        display: flex
        justify-content: flex-end
        flex: 1
        align-items: center
        .value
          color: #1D2129
          line-height: 1
          margin-right: 3px
    input
      &::-webkit-input-placeholder
        color: $textColor
      color: #1D2129
      width: 100%
  .btn
    padding: .22rem 10px
    position: fixed
    bottom: 0
    left: 0
    right: 0
    z-index: 2
.value-bz
    display: inline-block;
    max-width: 260px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
</style>

<style scoped>
.dest {
  background-color: #f2f3f5;
  padding: 8px;
  font-size: 14px;
}
.dest::placeholder {
  color: #86909c;
}
</style>
