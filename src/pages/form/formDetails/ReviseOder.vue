<template>
  <div class="wrapper">
    <Header title="修改" @backFun="goback"></Header>
    <div class="content">
      <!--服务科室-->
      <div class="weui-cell matter-style border-rightbottom item-style"
        v-if="$route.query.workTypeCode != 3 && template != 3">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务科室</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt" @click="getTeamDepartment">
          <input class="weui-input ipt ellipsis" type="text" readonly v-model="sourcesDeptName" placeholder="请选择服务科室" />
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <!--服务地点-->
      <div class="other" v-if="$route.query.workTypeCode != 3 && template != 3">
        <div class="weui-cell matter-style border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务区域</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt" @click="toSelectArea">
            <input class="weui-input ipt ellipsis" type="text" readonly v-model="areaVal" placeholder="请选择服务区域" />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>

        <!--服务房间-->
        <div class="weui-cell border-rightbottom item-style site">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务房间</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt" @click="toSelectSite">
            <input class="weui-input ipt ellipsis" type="text" readonly v-model="localtionPlaceName"
              placeholder="请选择服务房间" />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>
      </div>
      <!-- 搬运 逻辑-->
      <!-- 起点 -- 地点+科室 -->
      <div class="tran"
        v-if="$route.query.workTypeCode == 3 || ($route.query.workTypeCode && $route.query.workTypeCode.length > 2 && template == 3)">
        <div class="weui-cell matter-style border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">起点科室</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt" @click="getTeamDepartment">
            <input class="weui-input ipt ellipsis" type="text" readonly v-model="sourcesDeptName"
              placeholder="请选择起点科室" />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>

        <div class="weui-cell matter-style border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务起点</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt" @click="toSelectArea">
            <input class="weui-input ipt ellipsis" type="text" readonly v-model="areaVal" placeholder="请选择服务起点" />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>

        <!--服务房间-->
        <div class="weui-cell border-rightbottom item-style site">
          <div class="weui-cell__hd">
            <label class="weui-label title">起点房间</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt" @click="toSelectSite">
            <input class="weui-input ipt ellipsis" type="text" readonly v-model="localtionPlaceName"
              placeholder="请选择起点房间" />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>

        <!-- 终点 -- 地点+科室 -->
        <div class="weui-cell matter-style border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">终点科室</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt" @click="getTeamDepartmentEnd">
            <input class="weui-input ipt ellipsis" type="text" readonly v-model="recipientPersonDept"
              placeholder="请选择终点科室" />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>
        <div class="weui-cell matter-style border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务终点</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt" @click="toSelectAreaEnd">
            <input class="weui-input ipt ellipsis" type="text" readonly v-model="transportEndLocalName"
              placeholder="请选择服务终点" />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>

        <!--服务房间-->
        <div class="weui-cell border-rightbottom item-style site">
          <div class="weui-cell__hd">
            <label class="weui-label title">终点房间</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt" @click="toSelectSiteEnd">
            <input class="weui-input ipt ellipsis" type="text" readonly v-model="transportEndRoomName"
              placeholder="请选择终点房间" />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>
      </div>

      <!--服务事项-综合维修 -->
      <div class="weui-cell item-style matter-style border-rightbottom" @click="goToSelectMatterlists"
        v-if="$route.query.workTypeCode != 3 && $route.query.workTypeCode != 6 && isItem == 'Y'">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt">
          <div class="ipt-pos">
            <input class="weui-input ipt ellipsis" type="text" v-model="matterlists" readonly placeholder="请选择服务事项" />
            <!-- <span v-text="matterlists"></span> -->
          </div>
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <!--服务事项-综合服务 -->
      <div class="weui-cell item-style matter-style border-rightbottom" @click="goToSelectService"
        v-if="$route.query.workTypeCode == 6">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt">
          <div class="ipt-pos">
            <input class="weui-input ipt ellipsis" type="text" v-model="matterlistsName" readonly
              placeholder="请选择服务事项" />
            <!-- <span v-text="matterlists"></span> -->
          </div>
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <!--服务事项-搬运 -->
      <div class="weui-cell item-style matter-style border-rightbottom" @click="goToSelectService"
        v-if="$route.query.workTypeCode == 3">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt">
          <div class="ipt-pos">
            <input class="weui-input ipt ellipsis" type="text" v-model="matterlistsCarryName" readonly
              placeholder="请选择服务事项" />
            <!-- <span v-text="matterlists"></span> -->
          </div>
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <!--服务部门-->
      <div class="weui-cell matter-style border-rightbottom item-style" v-if="flowCode != 4">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务部门</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt" @click="getTeamInfo">
          <input class="weui-input ipt ellipsis" type="text" readonly v-model="teamInfoName" placeholder="请选择服务部门" />
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <!--数量-->
      <!-- 自定义工单综合类显示数量 -->
      <div class="weui-cell matter-style border-rightbottom item-style" v-if="workType == 'custom' && template != 3">
        <div class="weui-cell__hd">
          <label class="weui-label title">数量</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt">
          <input-number ref="inpNum" @numVariation="variation" @numberChange="countChange"
            :num="reviseNum"></input-number>
        </div>
      </div>
      <!--申报描述-->
      <div class="weui-cells_form desc-form">
        <div class="weui-cell__hd er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc" style="padding: 0 15px">
          <div class="weui-cell__bd">
            <textarea class="weui-textarea" placeholder="请输入您要申报的内容描述，字数在500字以内" maxlength="500" rows="5" ref="textarea"
              v-model="questionDescription" @keydown="keydown($event)" @blur="handleTextareaBlurEvent"></textarea>
          </div>
        </div>
      </div>
      <div class="finished-btn" @click="requiredParameter">
        <button class="weui-btn weui-btn_primary btn">
          <span>确定</span>
        </button>
      </div>
    </div>
    <service-items @back="serviceBack" :type="$route.query.workTypeCode" :list="serviceSyn" v-show="serviceShow"></service-items>
    <PublicServiceDepartment :showPicker="showPicker" :dataObj="dataObj" @cancel="showPicker = false" @confirm="onConfirm"></PublicServiceDepartment>
  </div>
</template>
<script>
import { mapState, mapGetters } from "vuex";
import serviceItems from "./designatePersonnel/components/serviceItems";
import inputNumber from "../newForm/components/inputNumber";
import PublicServiceDepartment from '@/pages/form/newForm/components/publicServiceDepartment.vue'
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  mixins: [fontSizeMixin],
  name: "",
  data() {
    return {
      reviseNum: 0,
      sourcesDeptName: "", //服务科室
      sourcesDeptCode: "",
      workType: "",
      questionDescription: "", //申报描述
      teamInfoName: "", //服务部门
      teamInfoCode: "",
      matterlists: "", //综合维修
      matterCodes: "",
      matterlistsName: "", //综合服务
      matterlistsCode: "",
      matterlistsCarryName: "",
      matterlistsCarryCode: "",
      areaVal: "", //服务地点
      serviceSiteResId: [],
      recipientPersonDept: "", //终点科室
      recipientPersonDeptCode: "",
      localtionPlaceName: "", //服务房间
      localtionPlaceCode:"", // 服务房间code
      allSiteDataInfo: [],
      allSiteDataInfoEnd: [],
      serviceSyn: [],
      transportEndLocalName: "", //服务终点
      transportEndLocalCode: [],
      transportEndRoomName: "", //终点房间
      transportEndRoomCode: "",
      serviceShow: false,
      isItem: "Y",
      kindTypeArr: ["1", "2", "3", "4", "5", "6"], //一般工作类型
      flowCode: 0,
      areaValList: [], //地点列表
      areaValListEnd: [], //终点地点列表
      template: "",
      echoFlag: true,
      lastQuery: "",
      showPicker: false,
      dataObj: {}
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "transLocationEnd", "serviceAreaTreeData"]),
    reviseServiceMatters() {
      return this.$store.state.serviceMatters;
    },
    reviseServiceArea () {
      this.serviceSiteResId=[]
      return this.$store.state.serviceArea;
    },
    reviseServiceAreaEnd() {
      return this.$store.state.serviceAreaEnd;
    },
    changeLocation() {
      return this.$store.state.changeLocation;
    },
    changeLocationEnd() {
      return this.$store.state.changeLocationEnd;
    },
    // 科室
    getDepartments() {
      return this.$store.state.departments;
    },
    // 服务房间
    getLocaltionPlaceName() {
      return this.$store.state.localtionPlaceName;
    },
    // 终点房间
    getTransportEndRoomName() {
      return this.$store.state.transportEndRoomName;
    }
  },
  watch: {
    questionDescription() {
      this.questionDescription = this.questionDescription.replace(
        /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,
        ""
      );
    },
    getDepartments(value, oldvalue) {
      this.sourcesDeptName = value.name; //科室回显
      this.sourcesDeptCode = value.id; //科室id
    },
    getLocaltionPlaceName (value, oldvalue) {      
      this.localtionPlaceName = value.name; //服务房间回显
    },
    getTransportEndRoomName(value, oldvalue) {
      this.transportEndRoomName = value.name; //终点房间回显
    },
    reviseServiceMatters: {
      deep: true,
      handler(value) {
        if (value.itemServiceCode) {
          this.matterlists = value.itemTypeName + "-" + value.itemDetailName + "-" + value.itemServiceName;
          this.matterCodes = value.itemTypeCode + "," + value.itemDetailCode + "," + value.itemServiceCode;
        } else if (value.itemDetailCode) {
          this.matterlists = value.itemTypeName + "-" + value.itemDetailName;
          this.matterCodes = value.itemTypeCode + "," + value.itemDetailCode;
        } else {
          this.matterlists = value.itemTypeName;
          this.matterCodes = value.itemTypeCode;
        }
      }
    },
    reviseServiceArea(value) {
      if (value.name instanceof Array) {
        value.name.forEach((item, index) => {
          if (!item) {
            value.name.splice(index, 1);
          }
        });
        this.areaVal = value.name.join("/");
      } else {
        this.areaVal = value.name;
      }
      this.serviceSiteResId = value.code;
      this.serviceSiteResId.forEach((item, index) => {
        if (!item) {
          this.serviceSiteResId.splice(index, 1);
        }
      });
      //服务区域切换 需要吧科室部分带走
      // this.sourcesDeptName = "";
      // this.sourcesDeptCode = "";
    },
    reviseServiceAreaEnd(value) {
      if (value.name instanceof Array) {
        this.transportEndLocalName = value.name.join("/");
      } else {
        this.transportEndLocalName = value.name;
      }
      this.transportEndLocalCode = value.code;
      //服务区域切换 需要吧科室部分带走
      // this.recipientPersonDept = "";
      // this.recipientPersonDeptCode = "";
    },
    changeLocation (value) {
      this.localtionPlaceName = value.localtionPlaceName;
      this.localtionPlaceCode = value.localtionPlaceCode;
    },
    changeLocationEnd(value) {
      this.transportEndRoomName = value.localtionPlaceName;
      this.transportEndRoomCode = value.localtionPlaceCode;
    },
    localtionPlaceName (value, oldval) {
      if (this.localtionPlaceCode) {
        if (this.serviceSiteResId.length == 4) {
          this.serviceSiteResId[3] = this.localtionPlaceCode;
        } else {
          this.serviceSiteResId.push(this.localtionPlaceCode);
        }
        this.teamInfoName = "";
        this.teamInfoCode = "";
      }
    },
    transportEndRoomName(value, oldvalue) {
      if (this.transportEndRoomCode) {
        if (this.transportEndLocalCode.length == 4) {
          this.transportEndLocalCode[3] = this.transportEndRoomCode;
        } else {
          this.transportEndLocalCode.push(this.transportEndRoomCode);
        }
      }
    },
    matterlists(value, oldVal) {
      if (oldVal) {
        this.teamInfoName = "";
        this.teamInfoCode = "";
      }
    },

    areaVal(value, oldVal) {
      if (oldVal) {
        this.teamInfoName = "";
        this.teamInfoCode = "";
      }
    },
    matterlistsName(value, oldVal) {
      if (oldVal) {
        this.teamInfoName = "";
        this.teamInfoCode = "";
      }
    }
  },
  created() {
    //区分工单
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.lastQuery = this.$route.query.lastQuery || "";
    // this.init()
  },
  activated () {
    // if (this.kindTypeArr.indexOf(this.$route.query.workTypeCode) < 0) {
    // 判断服务事项展示
    this.$api
      .getCustomizeTaskConfiguration({
        workTypeCode: this.$route.query.workTypeCode
      })
      .then(res => {
        this.isItem = res.isItem;
      });
    // }
    if (sessionStorage.pageState !== "false") {
      this.init();
    }
    let workTypeCode = this.$route.query.workTypeCode;
    this.workType = this.kindTypeArr.indexOf(workTypeCode) >= 0 ? "kind" : "custom";
    if (this.$route.query.path == "deptName" && this.echoFlag) { 
      this.echo();
    }
  },
  methods: {
    // 确定
    onConfirm (val) {
      this.teamInfoName = val.team_name; //服务部门
      this.teamInfoCode = val.id;
      this.showPicker = false;
    },
    goback() {
      this.$router.go(-1);
    },
    echo() {
      // 修改页面过来的科室修改操作
      if (this.$route.query.selectedName.length == 0) return;
      if (this.$route.query.level == 1) {
        // level = 1 修改 服务科室/起点科室
        if (this.$route.query.type == 1) {
          this.sourcesDeptName = this.$route.query.selectedName.name; //科室回显
          this.sourcesDeptCode = this.$route.query.selectedName.id; //科室id
          let areaValList = this.$route.query.areaVal;
          if (areaValList.length > 0) {
            for (let i = 0; i < areaValList.length; i++) {
              // 筛选符合条件的地点  gridLevel>2的进行展示
              if (areaValList[i].gridLevel <= 2) {
                areaValList.splice(i, 1);
              }
            }
            this.areaValList = areaValList; //科室回显
            this.serviceSiteResId.push(areaValList[0].id);
            this.areaVal = areaValList[0].parentGridName; //服务区域回显
            this.localtionPlaceName = areaValList[0].ssmName; //服务房间回显
          }
        } else if (this.$route.query.type == 3) {
          this.sourcesDeptName = this.$route.query.selectedName.name; //科室回显
          this.sourcesDeptCode = this.$route.query.selectedName.id; //科室id
        } else if (this.$route.query.type == 2) {
          this.sourcesDeptName = this.$route.query.selectedName.name; //科室回显
          this.sourcesDeptCode = this.$route.query.selectedName.id; //科室id
        }
      } else if (this.$route.query.level == 2) {
        // level 为2的时候修改运送终点
        if (this.$route.query.type == 1) {
          this.recipientPersonDept = this.$route.query.selectedName.name; //科室回显
          this.recipientPersonDeptCode = this.$route.query.selectedName.id; //科室id
          let areaValListEnd = this.$route.query.areaVal;
          if (areaValListEnd.length > 0) {
            for (let i = 0; i < areaValListEnd.length; i++) {
              // 筛选符合条件的地点  gridLevel>2
              if (areaValListEnd[i].gridLevel <= 2) {
                areaValListEnd.splice(i, 1);
              }
            }
            this.areaValListEnd = areaValListEnd;
            this.transportEndLocalCode.push(areaValListEnd[0].id);
            this.transportEndLocalName = areaValListEnd[0].parentGridName; //服务区域回显
            this.transportEndRoomName = areaValListEnd[0].ssmName; //服务房间回显
          }
        } else if (this.$route.query.type == 3) {
          this.recipientPersonDept = this.$route.query.selectedName.name; //科室回显
          this.recipientPersonDeptCode = this.$route.query.selectedName.id; //科室id
        } else if (this.$route.query.type == 2) {
          this.recipientPersonDept = this.$route.query.selectedName.name; //科室回显
          this.recipientPersonDeptCode = this.$route.query.selectedName.id; //科室id
        }
      }
    },
    init() {
      sessionStorage.pageState = false;
      this.$api
        .getTaskDetail({
          taskId: this.$route.query.id
        })
        .then(res => {
          //数据回显
          let obj = res[0];
          // 工单模版类型（3运送类；6综合类）
          this.template = obj.template;
          this.flowCode = obj.flowCode;
          let localtionIdLast = obj.localtionId.split(",")[obj.localtionId.split(",").length - 1];
          if (localtionIdLast) {
            //服务地点回显 - 区域+房间
            this.getGridInfoById(localtionIdLast);
          }
          this.sourcesDeptName = obj.sourcesDeptName || "";
          this.sourcesDeptCode = obj.sourcesDeptCode || "";
          this.reviseNum = obj.customtransportNum || 0;
          this.customtransportNum = obj.customtransportNum;
          this.questionDescription = obj.questionDescription;
          //服务事项分开处理，搬运多选逗号分隔，综合服务单选显示，其他三级正常显示
          let itemTypeObj = obj.itemType[0];
          switch (this.$route.query.workTypeCode) {
            case "3": //搬运
              this.matterlistsCarryName = obj.transportName;
              this.matterlistsCarryCode = obj.transportTypeCode;
              let transportStartLocalCodeArr = obj.transportStartLocalCode.split(",");
              this.getGridInfoById(transportStartLocalCodeArr[transportStartLocalCodeArr.length - 1]);
              let transportStartLocalOfficeCodeArr = obj.transportEndLocalCode.split(",");
              this.getGridInfoById(transportStartLocalOfficeCodeArr[transportStartLocalOfficeCodeArr.length - 1], "end");
              this.recipientPersonDept = obj.transportEndLocalOffice || ""; //obj.recipientPersonDept; //终点科室
              this.recipientPersonDeptCode = obj.transportEndLocalOfficeCode || "";
              break;
            case "6": //综合服务
              this.matterlistsName = obj.transportName; //综合服务 - 6
              this.matterlistsCode = obj.transportTypeCode;
              break;
            default:
              //其他
              if (itemTypeObj.itemServiceCode) {
                this.matterlists = itemTypeObj.itemTypeName + "-" + itemTypeObj.itemDetailName + "-" + itemTypeObj.itemServiceName;
                this.matterCodes = itemTypeObj.itemTypeCode + "," + itemTypeObj.itemDetailCode + "," + itemTypeObj.itemServiceCode;
              } else if (itemTypeObj.itemDetailCode) {
                this.matterlists = itemTypeObj.itemTypeName + "-" + itemTypeObj.itemDetailName;
                this.matterCodes = itemTypeObj.itemTypeCode + "," + itemTypeObj.itemDetailCode;
              } else {
                this.matterlists = itemTypeObj.itemTypeName;
                this.matterCodes = itemTypeObj.itemTypeCode;
              }

              if (this.template == 3) {
                // 自定义工单运送类
                this.matterlistsCarryName = obj.transportName;
                this.matterlistsCarryCode = obj.transportTypeCode;
                let transportStartLocalCodeArr = obj.transportStartLocalCode.split(",");
                this.getGridInfoById(transportStartLocalCodeArr[transportStartLocalCodeArr.length - 1]);
                let transportStartLocalOfficeCodeArr = obj.transportEndLocalCode.split(",");
                this.getGridInfoById(transportStartLocalOfficeCodeArr[transportStartLocalOfficeCodeArr.length - 1], "end");
                this.recipientPersonDept = obj.transportEndLocalOffice || ""; //obj.recipientPersonDept; //终点科室
                this.recipientPersonDeptCode = obj.transportEndLocalOfficeCode || "";
              }
              break;
          }
          this.teamInfoName = obj.designateDeptName; //服务部门
          this.teamInfoCode = obj.designateDeptCode;
        });
    },
    variation(value) {
      this.reviseNum = value;
    },
    countChange(type) {
      if (type == "add") {
        this.reviseNum++;
      } else {
        if (this.reviseNum > 0) {
          this.reviseNum--;
        }
      }
    },
    getGridInfoById(id, type) {
      const listData = this.serviceAreaTreeData;
      if (!listData || listData.length == 0) {
        // this.$message.error('请先选择服务地点')
        return;
      }
      const gridData = listData.filter(item => {
        return item.id == id;
      });
      console.log(gridData);
      // 数据重组 把 多余的前缀去除 增加选中本身的数据
      let allParentId = gridData[0].parentSpaceIds.split(",");
      let allParentName = gridData[0].parentSpaceName.split(">");
      allParentId.push(gridData[0].id);
      allParentName.push(gridData[0].ssmName);
      allParentId.shift();
      allParentId.shift();
      if (!type) {
        this.serviceSiteResId = allParentId;
        if (allParentId.length >= 4) {
          this.localtionPlaceName = allParentName.pop();
          this.areaVal = allParentName.join("/");
        } else {
          this.localtionPlaceName = this.localtionPlaceName = "";
          this.areaVal = allParentName.join("/");
        }
      } else {
        this.transportEndLocalCode = allParentId;
        if (allParentId.length >= 4) {
          this.transportEndRoomName = allParentName.pop();
          this.transportEndLocalName = allParentName.join("/");
        } else {
          this.transportEndRoomName = this.localtionPlaceName = "";
          this.transportEndLocalName = allParentName.join("/");
        }
      }
      return;
      this.axios
        .post(
          process.env.API_BASE + "/hospitalController/getHospitalGridInfoById",
          this.$qs.stringify({
            unitCode: this.loginInfo.userOffice[0].unitCode,
            hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
            id: id
          })
        )
        .then(res => {
          if (res.data.code == 200) {
            let gridData = res.data.data;
            let allParentId = gridData.allParentId.split(",");
            let allParentName = gridData.allParentName.split(">");
            if (!type) {
              this.serviceSiteResId = allParentId;
              if (allParentId.length >= 4) {
                this.localtionPlaceName = allParentName.pop();
                this.areaVal = allParentName.join("/");
              } else {
                this.localtionPlaceName = this.localtionPlaceName = "";
                this.areaVal = allParentName.join("/");
              }
            } else {
              this.transportEndLocalCode = allParentId;
              if (allParentId.length >= 4) {
                this.transportEndRoomName = allParentName.pop();
                this.transportEndLocalName = allParentName.join("/");
              } else {
                this.transportEndRoomName = this.localtionPlaceName = "";
                this.transportEndLocalName = allParentName.join("/");
              }
            }
          }
        });
    },
    //空格校验
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    //综合服务
    goToSelectService() {
      this.serviceSyn = [];
      this.$api
        .getTransportType({
          workTypeCode: this.$route.query.workTypeCode
        })
        .then(res => {
          res.forEach(item => {
            this.serviceSyn.push({
              value: item.value,
              label: item.name
            });
          });
          this.serviceShow = !this.serviceShow;
        });
    },
    /**
     * 修改科室 / 起点科室
     * areaVal : 服务地点
     * sourcesDeptName :服务科室
     */
    getTeamDepartment() {
      let _this = this;
      // if(this.areaVal.length == 0 && this.sourcesDeptName.length==0){
      //   // 科室和地点都为空
      //   this.typename=1
      // }else if(this.areaVal.length != 0 && this.sourcesDeptName.length !=0){
      //   // 科室和地点都不为空
      //   this.typename=2   //带已选地点id过去
      // }else if(this.areaVal.length != 0 && this.sourcesDeptName.length ==0){
      //   // 科室为空 地点不为空
      //   this.typename=2
      // }else{
      //   this.typename=1
      // }
      this.echoFlag = true;
      this.$router.push({
        path: "/deptName",
        query: {
          // type:this.typename,
          source: "revise",
          workTypeCode: this.$route.query.workTypeCode,
          serviceSiteResId: this.serviceSiteResId, //已选地点id
          level: 1, //为1时是起点
          id: this.$route.query.id
        }
      });
      /*
      //之前的修改逻辑(弹窗) 2.10版本修改成上方跳转页面
      if (this.serviceSiteResId.length == 0) {
        $.toast("请先选择服务区域", "text");
        return;
      }
      let gridId = this.localtionPlaceCode
        ? this.localtionPlaceCode
        : this.serviceSiteResId[this.serviceSiteResId.length - 1];
      this.axios
        .post(
          process.env.API_BASE +
            "/hospitalController/getHospitalOfficeByGridId",
          this.$qs.stringify({
            unitCode: this.loginInfo.userOffice[0].unitCode,
            hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
            gridId: gridId
          })
        )
        .then(res => {
          if (res.data.code == 200) {
            let departmentArr = [];
            res.data.data.forEach(item => {
              departmentArr.push({
                text: item.officeName,
                officeCode: item.officeCode,
                id: item.id,
                onClick: function() {
                  _this.sourcesDeptName = this.text;
                  _this.sourcesDeptCode = this.officeCode;
                }
              });
            });
            if (departmentArr.length > 0) {
              this.getDepartment(departmentArr);
            } else {
              $.toast("未找到所属科室", "text");
            }
          } else {
            $.toast(res.data.message);
          }
        });*/
    },
    getTeamDepartmentEnd() {
      // if(this.transportEndLocalName.length == 0 && this.recipientPersonDept.length==0){
      //   // 科室和地点都为空
      //   this.typename=1
      // }else if(this.transportEndLocalName.length != 0 && this.recipientPersonDept.length !=0){
      //   // 科室和地点都不为空
      //   this.typename=2   //带已选地点id过去
      // }else if(this.transportEndLocalName.length != 0 && this.recipientPersonDept.length ==0){
      //   // 科室为空 地点不为空
      //   this.typename=2
      // }else{
      //   this.typename=1
      // }
      this.$router.push({
        path: "/deptName",
        query: {
          // type:this.typename,
          source: "revise",
          workTypeCode: this.$route.query.workTypeCode,
          serviceSiteResId: this.transportEndLocalCode, //已选地点id
          level: 2,
          id: this.$route.query.id
        }
      });

      /**
       *
        let _this = this;
        //transportEndLocalName transportEndRoomName
        if (this.transportEndLocalCode.length == 0) {
          $.toast("请先选择服务区域", "text");
          return;
        }
        let gridId = this.transportEndRoomCode
          ? this.transportEndRoomCode
          : this.transportEndLocalCode[this.transportEndLocalCode.length - 1];
        this.axios
          .post(
            process.env.API_BASE +
              "/hospitalController/getHospitalOfficeByGridId",
            this.$qs.stringify({
              unitCode: this.loginInfo.userOffice[0].unitCode,
              hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
              gridId: gridId
            })
          )
          .then(res => {
            if (res.data.code == 200) {
              let departmentArr = [];
              res.data.data.forEach(item => {
                departmentArr.push({
                  text: item.officeName,
                  officeCode: item.officeCode,
                  id: item.id,
                  onClick: function() {
                    _this.recipientPersonDept = this.text;
                    _this.recipientPersonDeptCode = this.officeCode;
                  }
                });
              });
              if (departmentArr.length > 0) {
                this.getDepartmentEnd(departmentArr);
              } else {
                $.toast("未找到所属科室", "text");
              }
            } else {
              $.toast(res.data.message);
            }
          });
       */
    },
    //服务部门
    getDepartmentEnd(arr) {
      let _this = this;
      // this.$createActionSheet({
      //   data: arr,
      //   onSelect: (item, index) => {
      //     this.recipientPersonDept = item.content; //服务科室
      //     this.recipientPersonDeptCode = item.officeCode;
      //   }
      // }).show();
      $.actions({
        actions: arr
      });
    },
    getDepartment(arr) {
      let _this = this;
      $.actions({
        actions: arr
      });
      // this.$createActionSheet({
      //   data: arr,
      //   onSelect: (item, index) => {
      //     this.sourcesDeptName = item.content; //服务科室
      //     this.sourcesDeptCode = item.officeCode;
      //   }
      // }).show();
    },
    getTeamInfo() {
      let _this = this;
      if (this.serviceSiteResId.length == 0 && !this.matterCodes) {
        if (this.serviceSiteResId.length == 0) $.toast("请先选择服务区域", "text");
        else $.toast("请先选择服务事项", "text");
        return;
      }
      // console.log(this.serviceSiteResId); // 服务区域id
      // console.log(this.localtionPlaceCode); // 服务房间

      // console.log(this.matterlistsCode,'matterlistsCode'); //  服务事项-综合服务
      // console.log(this.matterlistsCarryCode, 'matterlistsCarryCode') // 服务事项-搬运
      // console.log(this.matterCodes, 'matterCodes') // 服务事项-综合维修

      let localtionId = this.localtionPlaceCode ? this.localtionPlaceCode : (this.serviceSiteResId[2]||this.serviceSiteResId[1])
      let matterId = this.matterlistsCode||this.matterlistsCarryCode||this.matterCodes
      this.dataObj = {
          workTypeCode: this.$route.query.workTypeCode,
          localtionId, // 服务区域id / 服务房间
          matterId, // 服务事项
      }
      this.showPicker=true
      // this.$api
      //   .getTeamsByTask({
      //     workTypeCode: this.$route.query.workTypeCode, //工单类型
      //     localtionId: this.serviceSiteResId.join(","), //区域ID（最后一级）
      //     itemTypeCode: this.matterCodes.split(",")[0] || ""
      //   })
      //   .then(resp => {
      //     const res = resp.list;
      //     let arr = res.map(item => {
      //       return {
      //         text: item.team_name,
      //         id: item.id,
      //         onClick: function() {
      //           _this.teamInfoName = this.text;
      //           _this.teamInfoCode = this.id;
      //         }
      //       };
      //     });
          // this.$createActionSheet({
          //   data: arr,
          //   onSelect: (item, index) => {
          //     this.teamInfoName = item.content;
          //     this.teamInfoCode = item.id;
          //   }
          // }).show();
          // $.actions({
          //   actions: arr
          // });
        // });
    },
    serviceBack(params) {
      if (this.$route.query.workTypeCode == 6) {
        this.matterlistsName = params.label;
        this.matterlistsCode = params.value;
      } else {
        this.matterlistsCarryName = params
          .map(item => {
            return item.label;
          })
          .join(",");
        this.matterlistsCarryCode = params
          .map(item => {
            return item.value;
          })
          .join(",");
      }
      this.serviceShow = !this.serviceShow;
    },
    //综合维修
    goToSelectMatterlists() {
      let workTypeCode = this.$route.query.workTypeCode;
      let source = this.kindTypeArr.indexOf(workTypeCode) >= 0 ? "revise" : "custom";
      this.$router.push({
        path: "/matterlists",
        query: {
          source: source,
          workTypeCode: this.$route.query.workTypeCode,
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode
        }
      });
    },
    // 服务地点
    toSelectArea() {
      // if(this.areaVal.length == 0 && this.sourcesDeptName.length==0){
      //   // 科室和地点都为空
      //   this.typename=2
      // }else if(this.areaVal.length != 0 && this.sourcesDeptName.length !=0){
      //   // 科室和地点都不为空
      //   // this.typename=3
      //   this.typename=1  //传科室id
      // }else if(this.areaVal.length == 0 && this.sourcesDeptName.length !=0){
      //   // 地点为空 科室不为空
      //   this.typename=1
      // }else{
      //   this.typename=2
      // }
      //级联弹窗数据还原
      this.localtionPlaceName = "";
      this.localtionPlaceCode = "";
      this.echoFlag = false;
      this.$router.push({
        path: "/selectArea",
        query: {
          source: "revise",
          workTypeCode: this.$route.query.workTypeCode,
          // type:this.typename,
          // areaValList:this.areaValList//type为1时的科室id
          sourcesDeptCode: this.sourcesDeptCode, //type为1的时候 科室code
          level: 1,
          id: this.$route.query.id
        }
      });
    },
    // 运送服务终点
    toSelectAreaEnd() {
      // if(this.transportEndLocalName.length == 0 && this.recipientPersonDept.length==0){
      //   // 科室和地点都为空
      //   this.typename=2
      // }else if(this.transportEndLocalName.length != 0 && this.recipientPersonDept.length !=0){
      //   // 科室和地点都不为空
      //   this.typename=1   //带已选地点id过去
      // }else if(this.transportEndLocalName.length != 0 && this.recipientPersonDept.length ==0){
      //   // 科室为空 地点不为空
      //   this.typename=1
      // }else{
      //   this.typename=2
      // }
      //级联弹窗数据还原
      this.transportEndRoomName = "";
      this.transportEndRoomCode = "";
      this.$router.push({
        path: "/selectArea",
        query: {
          source: "revise",
          isEnd: true,
          // type:this.typename,
          workTypeCode: this.$route.query.workTypeCode,
          sourcesDeptCode: this.recipientPersonDeptCode, //type为1的时候 科室code
          level: 2,
          id: this.$route.query.id
        }
      });
    },
    toSelectSite() {
      if (!this.serviceSiteResId[2]) {
        $.toast("获取不到服务区域id", "text");
        return;
      }
      if (this.serviceSiteResId.length == 0) {
        $.toast("请先选择服务区域", "text");
      } else {
        let gridId = "";
        if (this.serviceSiteResId.length <= 3) {
          gridId = this.serviceSiteResId[this.serviceSiteResId.length - 1];
        } else {
          gridId = this.serviceSiteResId[2];
        }
        // this.$api
        //   .cascadingQueryHospitalGridInfo({
        //     gridId: gridId
        //   })
        //   .then(this.showVal);
        this.showVal(this.serviceAreaTreeData, gridId);
      }
    },
    toSelectSiteEnd() {
      if (!this.transportEndLocalCode[2]) {
        $.toast("获取不到服务区域id", "text");
        return;
      }
      if (this.transportEndLocalCode.length == 0) {
        $.toast("请先选择终点区域", "text");
      } else {
        let gridId = "";
        if (this.transportEndLocalCode.length <= 3) {
          gridId = this.transportEndLocalCode[this.transportEndLocalCode.length - 1];
        } else {
          gridId = this.transportEndLocalCode[2];
        }
        this.showValEnd(this.serviceAreaTreeData, gridId);
        // this.$api
        //   .cascadingQueryHospitalGridInfo({
        //     gridId: gridId
        //   })
        //   .then(this.showValEnd);
      }
    },
    showValEnd(res, gridId) {
      this.allSiteDataInfo = res.filter(item => {
        return item.parentId == gridId;
      });
      //将所有地点取出放到数组中
      let siteArr = [];
      for (let i = 0; i < this.allSiteDataInfo.length; i++) {
        siteArr.push(this.allSiteDataInfo[i]);
      }
      //跳到搜索页面
      this.$router.push({
        name: "Site",
        params: {
          list: siteArr
        },
        query: {
          isEnd: true
        }
      });
    },
    showVal(res, gridId) {
      // if (res.data && res.data.code == 200) {
      //   this.allSiteDataInfo = res.data.data;
      // } else {
      //   this.allSiteDataInfo = res;
      // }
      this.allSiteDataInfo = res.filter(item => {
        return item.parentId == gridId;
      });
      //将所有地点取出放到数组中
      let siteArr = [];
      for (let i = 0; i < this.allSiteDataInfo.length; i++) {
        siteArr.push(this.allSiteDataInfo[i]);
      }
      //跳到搜索页面
      this.$router.push({
        name: "Site",
        params: {
          list: siteArr
        }
      });
    },
    requiredParameter() {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement/getIsRequired",
          this.$qs.stringify({
            hospitalCode: this.loginInfo.hospitalCode,
            unitCode: this.loginInfo.unitCode,
            workTypeCode: this.$route.query.workTypeCode
          }),
          {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.requiredCode = res.requiredCode;
          let value = this.requiredCode;
          if (this.flowCode != 4) {
            if (value.indexOf("localtion", 0) != -1 && !this.areaVal) {
              $.toast("请选择服务区域", "text");
            }
            if (value.indexOf("sourcesDept", 0) != -1 && !this.sourcesDeptName) {
              $.toast("请选择所属科室", "text");
              return;
            }
            if (value.indexOf("itemTypeCode", 0) != -1) {
              if (this.matterlistsCarryName && this.matterlistsName && !this.matterlists) {
                $.toast("请选择服务事项", "text");
                return;
              }
            }
            if (value.indexOf("designateDeptCode", 0) != -1 && !this.teamInfoName) {
              $.toast("请选择服务部门", "text");
              return;
            }
            if (value.indexOf("transportNum", 0) != -1 && !this.reviseNum) {
              $.toast("请输入数量", "text");
              return;
            }
          }
          if (this.$route.query.workTypeCode == 6) {
            //综合服务
            if (value.indexOf("localtion", 0) != -1 && !this.areaVal) {
              $.toast("请选择服务区域", "text");
            }
            if (value.indexOf("designateDeptCode", 0) != -1 && !this.teamInfoName) {
              $.toast("请选择服务部门", "text");
              return;
            }
            if (value.indexOf("sourcesDept", 0) != -1 && !this.sourcesDeptName) {
              $.toast("请选择所属科室", "text");
              return;
            }
            if (value.indexOf("itemTypeCode", 0) != -1 && !this.matterlistsName) {
              $.toast("请选择服务事项", "text");
              return;
            }
          } else if (this.$route.query.workTypeCode == 3) {
            //搬运
            if (value.indexOf("localtion", 0) != -1 && !this.areaVal) {
              $.toast("请选择服务起点", "text");
            }
            if (value.indexOf("sourcesDept", 0) != -1 && !this.sourcesDeptName) {
              $.toast("请选择起点科室", "text");
              return;
            }
            if (value.indexOf("transportEndLocal", 0) != -1 && !this.transportEndLocalName) {
              $.toast("请选择服务终点", "text");
            }
            if (value.indexOf("recipientDept", 0) != -1 && !this.recipientPersonDept) {
              $.toast("请选择终点科室", "text");
              return;
            }
            if (value.indexOf("designateDeptCode", 0) != -1 && !this.teamInfoName) {
              $.toast("请选择服务部门", "text");
              return;
            }
            if (value.indexOf("itemTypeCode", 0) != -1 && !this.matterlistsCarryName) {
              $.toast("请选择服务事项", "text");
              return;
            }
          } else {
            if (this.isItem == "Y") {
              if (value.indexOf("localtion", 0) != -1 && !this.areaVal) {
                $.toast("请选择服务区域", "text");
              }
              // if (
              //   value.indexOf("designateDeptCode", 0) != -1 &&
              //   !this.teamInfoName
              // ) {
              //   $.toast("请选择服务部门", "text");
              //   return;
              // }
              if (value.indexOf("sourcesDept", 0) != -1 && !this.sourcesDeptName) {
                $.toast("请选择所属科室", "text");
                return;
              }
              if (value.indexOf("itemTypeCode", 0) != -1 && !this.matterlists) {
                $.toast("请选择服务事项", "text");
                return;
              }
            }
          }

          this.handleSubmit();
          // return true
        });
    },
    handleSubmit() {
      let params = {
        userId: this.loginInfo.id,
        userName: this.loginInfo.staffName,
        taskId: this.$route.query.id,
        workTypeCode: this.$route.query.workTypeCode, //工单类型
        appId: process.env.WxAppid,
        //综合维修，应急保洁的服务事项
        itemTypeCode: this.matterCodes.split(",")[0] || "", //一级服务事项code
        itemTypeName: this.matterlists.split("-")[0] || "", //一级服务事项name
        itemDetailCode: this.matterCodes.split(",")[1] || "", //二级服务事项code
        itemDetailName: this.matterlists.split("-")[1] || "", //一级服务事项name
        itemServiceCode: this.matterCodes.split(",")[2] || "", //三级服务事项code
        itemServiceName: this.matterlists.split("-")[2] || "", //三级服务事项name

        //搬运+综合服务的服务事项
        transportTypeCode: this.matterlistsCarryCode || this.matterlistsCode, //搬运品种类code
        transportTypeName: this.matterlistsCarryName || this.matterlistsName, //搬运品种类name

        sourcesDept: this.sourcesDeptCode, //所属科室code
        sourcesDeptName: this.sourcesDeptName, //所属科室name

        recipientDept: this.recipientPersonDeptCode, //接收科室code
        recipientDeptName: this.recipientPersonDept, //接收科室name

        designateDeptName: this.teamInfoName, //指派班组name
        designateDeptCode: this.teamInfoCode, //指派班组code
        questionDescription: this.questionDescription //申报描述
      };
      params.transportNum = this.reviseNum; //数量
      // localtionName: this.areaVal.split("/").join(","), //服务地点/服务起点 直接拼接
      // localtion: this.serviceSiteResId.join(","), //服务地点/服务起点code ,拼接
      let areaVal = this.areaVal.split("/");
      if (this.localtionPlaceName) {
        if (this.serviceSiteResId.length == 3) {
          this.serviceSiteResId.push(this.localtionPlaceCode);
        }
        areaVal.push(this.localtionPlaceName);
      } else {
        this.localtionPlaceName = "";
        this.localtionPlaceCode = "";
      }
      //终点房间处理
      let transportEndLocalName = this.transportEndLocalName.split("/");
      if (this.transportEndRoomName) {
        if (this.transportEndLocalCode.length == 3) {
          this.transportEndLocalCode.push(this.transportEndRoomCode);
        }
        transportEndLocalName.push(this.transportEndRoomName);
      } else {
        this.transportEndRoomName = "";
        this.transportEndRoomCode = "";
      }
      params.transportEndLocalName = transportEndLocalName.join(""); //服务终点name ,拼接
      params.transportEndLocal = this.transportEndLocalCode.join(","); //服务终点code 直接拼接
      params.localtionName = areaVal.join("");
      params.localtion = this.serviceSiteResId.join(",");
      this.$api.updateTask(params).then(res => {
        // $.toast("修改成功");
        setTimeout(() => {
          this.$router.push({
            path: "/deskDetails",
            query: {
              id: this.lastQuery.id,
              source: this.lastQuery.source,
              interfaceNum: this.lastQuery.interfaceNum,
              flowcode: this.lastQuery.flowcode
            }
          });
        }, 1500);
      });
    }
  },
  components: {
    serviceItems,
    inputNumber,
    PublicServiceDepartment
  }
};
</script>
<style lang="stylus" type="stylesheet/stylus" scoped>
.matter-ipt
  display flex
  justify-content flex-end
  .ipt-pos
    position relative
    width 100%
    .matterInput
      display none
    span
      font-family serif
      text-align left
      display flex
      font-size 15px
  .arrow
    display flex
    align-items center
.finished-btn
  height 1.32rem
  padding 0.22rem 0.16rem
  box-sizing border-box
  border-top 1px solid $bgColor
  background-color #fff
  width 100%
  position fixed
  bottom 0
  z-index 1000
.cube-radio_selected .cube-radio-ui
  background-color #fc9153
.er-level
  height 44px
  line-height 44px
  padding-left 15px
.number-add img
  width 60% !important
.ellipsis
  overflow hidden
  white-space nowrap
  text-overflow ellipsis
.weui-cell__hd
  .title
    font-size calc(16px * var(--font-scale))!important
.weui-cell__bd {
  font-size: calc(16px * var(--font-scale))!important;
}
</style>
