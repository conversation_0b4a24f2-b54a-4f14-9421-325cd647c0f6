<template>
  <div class="wrapper">
    <Header title="确认到达" @backFun="goback"></Header>
    <div class="content">
      <upload-image class="bottom" style="position: relative" ref="imgs" :captureOnly="true" @getImg="getImg" @delImg="delImg" :isRequired="photoRequired == 1"></upload-image>
      <div class="time-box">
        <span>拍照时间</span>
        <span>{{ photoTime || "暂无" }}</span>
      </div>
    </div>
    <div class="btns">
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="handleSubmit">确定</button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import UploadImage from "@/common/uploadImg/uploadImg";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "arrival",
  components: {
    UploadImage
  },
  mixins: [fontSizeMixin],
  data() {
    return {
      imagesParams: [],
      photoTime: "",
      taskId: "",
      photoRequired: "",
      arrivalTime: ""
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  methods: {
    handleSubmit() {
      if (this.photoRequired == 1 && this.imagesParams.length === 0) {
        $.toast("请上传照片", "text");
        return;
      }
      let params = {
        taskId: this.taskId,
        arrivalPersonId: this.loginInfo.staffId,
        arrivalPersonName: this.loginInfo.staffName,
        arrivalTime: this.arrivalTime,
        photoTime: this.photoTime,
        arrivalPicPath: this.imagesParams.join(",")
      };
      this.$api.confirmArrival(params).then(res => {
        this.$toast.success("确认到达成功");
        this.$router.go(-1);
      });
    },
    getImg(img) {
      console.log("img", img);
      this.imagesParams.push(img);
      if (this.imagesParams.length === 1) {
        this.photoTime = this.formatDateTime(new Date());
      }
    },
    delImg(url) {
      const index = this.imagesParams.indexOf(url);
      if (index > -1) {
        this.imagesParams.splice(index, 1);
        if (this.imagesParams.length === 0) {
          this.photoTime = "";
        }
      }
    },
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    goback() {
      this.$router.go(-1);
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.taskId = this.$route.query.taskId;
    this.photoRequired = this.$route.query.photoRequired;
    this.arrivalTime = this.formatDateTime(new Date()); // 在页面加载时设置到达时间
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
.wrapper {
  background-color: #f2f4f9;
  height: 100vh;
}

.content {
  background-color: #fff;
}

.time-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  background-color: #fff;
  height: 40px;
  font-size: calc(16px * var(--font-scale))!important;
}

.btns {
  padding: 0.16rem;
  box-sizing: border-box;
  background-color: #fff;
  border-top: 1px solid $bgColor;
  display: flex;
  justify-content: flex-end;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 2;
}

.btn {
  flex: 1;
  height: 0.88rem;
  font-size: 0.36rem;
  margin: 0.1rem 0.16rem;
  line-height: inherit;
  padding: initial;
}
/deep/ .explain >span {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .explain .upload-img {
  font-size: calc(12px * var(--font-scale))!important;
}
</style>
