<template>
  <div class="container">
    <Header title="报警详情" @backFun="goBack"></Header>
    <van-tabs v-model="active" color="#3562db" @change="tabChanged">
      <van-tab title="详情">
        <van-cell class="alarm-id" title="报警ID" :value="alarmRecord.alarmId" />
        <van-cell title="报警类型" :value="alarmRecord.alarmType" />
        <!-- <van-cell title="报警生成规则" :value="alarmRecord.alarmRule" /> -->
        <van-cell title="报警等级" :value="alarmRecord.alarmLevel | alarmLevelFilter" />
        <van-cell title="报警对象名称" :value="alarmRecord.alarmObjectName" />
        <van-cell title="报警数值" :value="alarmRecord.alarmValue + (alarmRecord.alarmUnit ? alarmRecord.alarmUnit : '')" />
        <van-cell title="对象位置" :value="alarmRecord.alarmSpaceName" />
        <van-cell title="报警处置" :value="alarmRecord.alarmStatus | alarmStatusFilter" />
        <div class="item">
          <span>处置结果</span>
          <van-radio-group v-model="alarmAffirm" direction="horizontal" checked-color="#3562db"
            :disabled="[1, 2, 3, 4].includes(alarmRecord.alarmAffirm)">
            <van-radio :name="1">真实报警</van-radio>
            <van-radio :name="2">误报</van-radio>
            <van-radio :name="3">演练</van-radio>
            <van-radio :name="4">调试</van-radio>
          </van-radio-group>
        </div>
        <van-field v-model="alarmRecord.remark" class="handle" :border="true" show-word-limit maxlength="50" rows="2"
          autosize label="处置流程" type="textarea" placeholder="请输入留言"
          :disabled="[1, 2, 3, 4].includes(alarmRecord.alarmAffirm)" />
        <div class="btn-box" v-if="alarmRecord.alarmStatus !== 2">
          <!--  -->
          <van-button type="primary" size="large" color="#3562db" @click="confirAlarm"
            v-if="![1, 2, 3, 4].includes(alarmRecord.alarmAffirm)">确警</van-button>
          <van-button type="primary" size="large" color="#3562db" @click="close"
            v-if="alarmRecord.alarmStatus != 2">关闭</van-button>
        </div>
      </van-tab>
      <van-tab title="执行流程">
        <div class="flow-box">
          <nut-timeline>
            <nut-timelineitem pointType="hollow" :pointColor="'#3562db'" v-for="item in timeLineList" :key="item.id">
              <div slot="title">{{ item.createdTime.split(" ")[0] }}</div>
              <div class="time">{{ item.createdTime.split(" ")[1] }}</div>
              <div class="box-content">
                <!-- <div v-if="item.status == 0" class="time-work-order">
                  <div class="time-work-order-event">
                    {{ operationType[item.status] }}
                  </div>
                  <div class="work-order-detail">
                    <span>报警ID :</span>
                    <span style="padding-left: 10px">{{ item.limAlarmId }}</span>
                  </div>
                </div>
                <div v-if="[1, 2, 3, 4].includes(item.status)" class="time-work-order">
                  <div class="time-work-order-event">
                    {{ operationType[item.status] }}
                  </div>
                  <div class="work-order-detail">
                    <span>确警人 :</span>
                    <span
                      style="padding-left: 10px">{{ item.operationPersonName + "（" + item.operationPersonId + "）" }}</span>
                  </div>
                </div>
                <div v-if="item.status == 5" class="time-work-order">
                  <div class="time-work-order-event">
                    {{ operationType[item.status] }}
                  </div>
                </div>
                <div v-if="item.status == 6" class="time-work-order">
                  <div class="time-work-order-event">
                    {{ operationType[item.status] }}
                  </div>
                  <div class="work-order-detail">
                    <span>备注人 :</span>
                    <span
                      style="padding-left: 10px">{{ item.operationPersonName + "（" + item.operationPersonId + "）" }}</span>
                  </div>
                  <div class="work-order-detail">
                    <span>备注 :</span>
                    <span style="padding-left: 10px">{{ item.remark || "-" }}</span>
                  </div>
                </div>
                <div v-if="item.status == 7" class="time-work-order">
                  <div class="time-work-order-event" style="color: #414653">
                    {{ operationType[item.status] }}
                  </div>
                  <div class="work-order-detail">
                    <span>关闭人 :</span>
                    <span
                      style="padding-left: 10px">{{ item.operationPersonName + "（" + item.operationPersonId + "）" }}</span>
                  </div>
                </div>
                <div v-if="[8, 9, 13].includes(item.status)" class="time-work-order">
                  <div class="time-work-order-event">
                    {{ operationType[item.status] }}
                  </div>
                  <div class="work-order-detail">
                    <span>操作人 :</span>
                    <span
                      style="padding-left: 10px">{{ item.operationPersonName + "（" + item.operationPersonId + "）" }}</span>
                  </div>
                </div>
                <div v-if="item.status == 12" class="time-work-order">
                  <div class="time-work-order-event">
                    {{ operationType[item.status] }}
                  </div>
                  <div class="work-order-detail">
                    <span>操作人 :</span>
                    <span
                      style="padding-left: 10px">{{ item.operationPersonName + "（" + item.operationPersonId + "）" }}</span>
                  </div>
                  <div class="work-order-detail">
                    <span>备注 :</span>
                    <span style="padding-left: 10px">{{ item.remark || "-" }}</span>
                  </div>
                </div>
                <div v-if="item.status == 10" class="time-work-order">
                  <div class="time-work-order-event">
                    {{ operationType[item.status] }}
                  </div>
                  <div class="work-order-detail">
                    <span>派单人 :</span>
                    <span
                      style="padding-left: 10px">{{ item.operationPersonName + "（" + item.operationPersonId + "）" }}</span>
                  </div>
                  <div class="work-order-detail">
                    <span>工单号 :</span>
                    <span style="padding-left: 10px; color: #0379f1; cursor: pointer">{{ item.workNum || "-" }}</span>
                  </div>
                </div>
                <div v-if="item.status == 11" class="time-work-order">
                  <div class="time-work-order-event">
                    {{ operationType[item.status] }}
                  </div>
                  <div class="work-order-detail">
                    <span>创建人 :</span>
                    <span
                      style="padding-left: 10px">{{ item.operationPersonName + "（" + item.operationPersonId + "）" }}</span>
                  </div>
                  <div class="work-order-detail">
                    <span>工单号 :</span>
                    <span style="padding-left: 10px; color: #0379f1; cursor: pointer">{{ item.workNum || "-" }}</span>
                  </div>
                </div> -->
                <div class="time-work-order">
                  <div class="time-work-order-event">
                    {{ item.operationTypeName }}
                  </div>
                  <div class="work-order-detail">
                    <span>操作人：</span>
                    <span>{{ item.operationPersonName }}{{ item.operationPersonId ? `（${item.operationPersonId}）` : '' }}</span>
                  </div>
                  <div class="work-order-detail">
                    <span>操作端：</span>
                    <span>{{ item.operationSourceName || '-' }}</span>
                  </div>
                  <div class="work-order-detail">
                    <span>说明：</span>
                    <span>{{ item.remark }}</span>
                  </div>
                </div>
              </div>
            </nut-timelineitem>
          </nut-timeline>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import YBS from "@/assets/utils/utils.js";
export default {
  name: "alarmAck",
  data() {
    return {
      active: 0,
      alarmAffirm: "",
      message: "",
      alarmRecord: {},
      timeLineList: [],
      alarmId: "",
      operationType: {
        0: "产生报警",
        1: "真实报警",
        2: "误报",
        3: "演练",
        4: "调试",
        5: "未确认", // 不显示信息
        6: "备注",
        7: "报警关闭",
        8: "设置为经典案例",
        9: "取消设置为经典案例",
        10: "派单",
        11: "新建工单",
        12: "已屏蔽",
        13: "取消屏蔽"
      }
    };
  },
  mounted() {
    this.alarmId = this.$route.query.alarmId;
    console.log(this.alarmId, 'dsaaaaa')
    this.getAlarmDetails();
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    getAlarmDetails() {
      $.showLoading("数据加载中...");
      this.axios
        .post(
          __PATH.ALARM + "/alarm/record/selectAlarmRecordById",
          {
            alarmId: this.alarmId
          },
          {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token"),
              'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
            }
          }
        )
        .then(res => {
          $.hideLoading();
          if (res.data.code == 200) {
            this.alarmRecord = res.data.data.record;
            this.timeLineList = res.data.data.detail;
            this.alarmAffirm = res.data.data.record.alarmAffirm;
            let remark = res.data.data.detail.find(i => i.status == this.alarmAffirm).remark;
            this.alarmRecord.remark = remark ? remark : "";
            this.timeLineList.forEach(item => {
              if (item.status == 2) {
                this.message = item.remark || "";
              }
            });
          } else {
            console.log(res.data.msg);
          }
        });
    },
    confirAlarm() {
      if (this.alarmAffirm === 0) {
        $.toast("请选择处置结果", "text");
        return;
      } else {
        this.axios
          .post(
            __PATH.ALARM + "/alarm/record/updateAlarmAffirmById",
            {
              alarmId: this.alarmRecord.alarmId,
              alarmAffirm: this.alarmAffirm,
              remark: this.alarmRecord.remark,
              projectCode: this.alarmRecord.projectCode,
              operationSource: '2', //来源
            },
            {
              headers: {
                Authorization: "Bearer " + localStorage.getItem("token"),
                'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
              }
            }
          )
          .then(res => {
            if (res.data.code == 200) {
              $.toast("操作成功", "text");
              this.getAlarmDetails();
            } else {
              console.log(res.data.msg);
            }
          });
      }
    },
    // 关闭
    closeAlarmRecord() {
      this.axios
        .post(
          __PATH.ALARM + "/alarm/record/closeAlarmRecordById",
          { alarmId: this.alarmRecord.alarmId },
          {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token"),
              'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
            }
          }
        )
        .then(res => {
          if (res.data.code == 200) {
            this.goBack()
            // this.$router.go(-1);
          } else {
            $.toptip(res.data.msg, "error");
          }
        });
    },
    close() {
      if (this.alarmRecord.alarmStatus == 0) {
        this.$dialog
          .confirm({
            title: "提示",
            message: "当前报警未处理，是否确警后再进行关闭？"
          })
          .then(() => {
            this.confirAlarm();
          });
      } else {
        if (this.alarmRecord.alarmStatus == 1 && this.alarmRecord.alarmAffirm === 0) {
          this.$dialog
            .confirm({
              title: "提示",
              message: "当前报警未处理，是否确警后再进行关闭？"
            })
            .then(() => {
              this.confirAlarm();
            });
        } else {
          this.$dialog
            .confirm({
              title: "提示",
              message: "是否关闭当前报警记录？"
            })
            .then(() => {
              this.closeAlarmRecord();
            });
        }
      }
    },
    tabChanged() {
      this.getAlarmDetails();
    }
  },
  filters: {
    alarmLevelFilter(value) {
      switch (value) {
        case 0:
          return "通知";
          break;
        case 1:
          return "一般";
          break;
        case 2:
          return "紧急";
          break;
        case 3:
          return "重要";
          break;
        default:
          break;
      }
    },
    alarmStatusFilter(value) {
      switch (value) {
        case 0:
          return "未处理";
          break;
        case 1:
          return "处理中";
          break;
        case 2:
          return "已关闭";
          break;
        default:
          break;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.btn-box {
  box-sizing: border-box;
  /* position: fixed; */
  width: 100%;
  padding: 5vw;
  display: flex;
  .van-button {
    margin-left: 10px;
    &:first-child {
      margin-left: 0;
    }
  }
  /* bottom: 1vh; */
  /* border-top: 1px solid #efeff4; */
}
.item {
  margin: 15px 16px;
  margin-bottom: 0;
  font-size: 14px;
  .van-radio-group {
    margin: 10px 0;
  }
  border-bottom: 1px solid #ebedf0;
}
/deep/ .handle .van-field__label > span {
  color: #353535 !important;
  font-size: 14px;
}
>>> ::-webkit-input-placeholder {
  color: #666;
  font-size: 14px;
}
.flow-box {
  padding: 16px;
  .time {
    padding: 0 0 6px 0;
  }
}
.nut-timeline {
  padding-left: 10vw;
}
.work-order-detail {
  padding: 2px 0;
}
.time-work-order-event {
  margin-bottom: 4px;
}
::v-deep .van-cell__title {
  flex: none;
  width: 30%;
  // color: #646566 !important;
}
.van-cell__value {
  flex: none;
  width: 70%;
}
</style>
