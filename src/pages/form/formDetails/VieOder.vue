<template>
  <div class="wrapper trigger-box" :class="{ noScroll: isShowPop }">
    <Header title="工单详情" @backFun="goback"></Header>
    <div class="simplification" v-if="!simplification">
      <div
        class="item"
        v-for="(item, idx) of workOrdeInfoDate"
        :key="idx"
        @click.stop="handleFold($event, idx)"
      >
        <!--基本详情-->
        <div v-if="item.operationCode == 1">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <base-info :baseData="item"></base-info>
        </div>
        <!--已受理-->
        <div v-if="item.operationCode == 2 && item.type != 2">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <service-site
            :serviceData="item"
            :workTypeCode="workTypeCode"
          ></service-site>
        </div>
        <!--已派工-->
        <div v-if="item.operationCode == 3">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <person :persData="item"></person>
        </div>
        <!--督促-->
        <div v-if="item.operationCode == 8">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <return-visit
            :returnVisitdData="item"
            :name="item.operationType"
          ></return-visit>
        </div>
        <!--已挂单-->
        <div v-if="item.operationCode == 4">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <pending-details :pendingData="item"></pending-details>
        </div>
        <!--已转单-->
        <div v-if="item.operationCode == 9">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <change-order :changeOrderData="item"></change-order>
        </div>
        <!-- 转派 -->
        <div v-if="item.operationCode == 11">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <change-order :changeOrderData="item"></change-order>
        </div>
        <!--已完工-->
        <div v-if="item.operationCode == 6">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <evaluation-info
            ref="evaluation"
            :finishedData="item"
            :workTypeCode="workTypeCode"
          ></evaluation-info>
        </div>
            <!--回退-->
        <div v-if="item.operationCode == 30">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <retroversion :cancelData="item"></retroversion>
        </div>
        <!-- 已变更 -->
        <div v-if="item.operationCode == 10">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <changed :changedData="item" :workTypeCode="workTypeCode"></changed>
        </div>
      </div>
    </div>
    <!-- 精简工单 -->
    <simplify-work v-else :workOrdeInfoDate="workOrdeInfoDate"></simplify-work>
    <div class="vie-info" v-if="!isShow">
      <!--时间轴标题-->
      <div class="item">
        <div class="header">
          <div class="iconfont">&#xe681;</div>
          <div class="title-body">
            <span class="dot"></span>
            <span class="title">{{ designatePersonnelTitle }}</span>
            <span class="time">{{
              timeFunc(Date.parse(new Date()), ".")
            }}</span>
          </div>
        </div>
      </div>
      <!--选择指派人员信息-->
      <div class="weui-cell item notice-wrapper designate-css">
        <div class="weui-cell__hd">
          <label class="weui-label">指派人员</label>
        </div>
        <div
          class="weui-cell__bd text-wrapper"
          @click="handleDesignatePersonnel()"
          :class="{ disClick: isVieOder, disabledClick: disabled }"
        >
          <span v-if="personnelVal && workOrdeInfoDate">{{
            designatePersonnel
          }}</span>
          <span v-else>
            请选择
            <span class="iconfont">&#xe646;</span>
          </span>
        </div>
      </div>
      <div class="weui-cell item notice-wrapper" v-if="!isVieOder">
        <div class="weui-cell__hd">
          <label class="weui-label">消息通知</label>
        </div>
        <div class="weui-cell__bd text-wrapper">
          <span>{{ noticePeople }}</span>
        </div>
        <div class="weui-cell__ft"></div>
      </div>
    </div>
    <!--转单处理-->
    <change-order-handle
      v-if="handleChangeOderInfo"
      :title="handleComponentsTitle"
      :workTypeCode="workTypeCode"
      :localtionId="localtionId"
      :itemTypeCode="itemTypeCode"
      ref="changeOrder"
    ></change-order-handle>
    <!--挂单部分-->
    <pend-oder
      @goToPendOder="handleGoToPendOder"
      v-if="isPendOder"
      ref="pendingOder"
    ></pend-oder>
    <div class="height-placeholder" style="height:38px"></div>
    <div v-if="btnsGroup">
      <!--工人端和领导端抢单推送的工单详情页面，抢单-派工-挂单（领导） 抢单（工人）-->
      <div v-if="!isFinished">
        <div v-if="!ShowPendOderBtn" class="btns">
          <deal-btns
            :isLeader="isLeader"
            :canChangeOrder="canChangeOrder"
            v-if="isShow"
            :workOrdeInfoDate="workOrdeInfoDate"
            @goVieOder="handleGoToVieOder"
            @goToAssign="handleGoToAssign"
            @goToPendOder="handleGoToPendOder"
            @goToChangeOder="handleChangeOder"
            @handleReviseOder="goHandleReviseOder"
          ></deal-btns>
        </div>

        <div class="btns" v-if="!isShow">
          <button
            class="weui-btn weui-btn_primary btn"
            @click="handleSubmitVieOderClick"
            :class="{ 'weui-btn_disabled': isDis }"
            :disabled="disabled"
          >
            <span v-if="isVieOder">{{ isSure }}</span>
            <span v-else>{{ isAppoint }}</span>
          </button>
        </div>
      </div>
      <!--挂单确定显示按钮-->
      <div class="btns" v-if="ShowPendOderBtn">
        <button
          class="weui-btn weui-btn_primary btn"
          @click="handleSubmitVieOderClick"
          :class="{ 'weui-btn_disabled': isDis }"
          :disabled="disabled"
        >
          确定
        </button>
      </div>
      <div class="btns finished-btn" v-if="isFinished">
        <button
          class="weui-btn weui-btn_disabled weui-btn_primary btn"
          disabled
        >
          <span>{{ btnText }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import wx from "weixin-js-sdk";
import { mapState } from "vuex";
import global from "@/utils/Global";
import BaseInfo from "./components/BaseInfoNew";
import ServiceSite from "./components/Accepted";
import retroversion from "./components/retroversion";
import Person from "@/pages/mine/components/Dispatched";
import PendingDetails from "@/pages/mine/components/PendingDetails";
import EvaluationInfo from "@/pages/mine/components/EvaluationInfo";
import DealBtns from "./components/DealWorkOrderbtn";
import PendOder from "./components/PendingOrder";
import ReturnVisit from "@/pages/mine/components/ReturnVisit";
import ChangeOrder from "./components/ChangeOrderInfo";
import changeOrderHandle from "@/pages/mine/components/ChangeOrderHandle";
import SimplifyWork from "./components/SimplifyWork";
import Changed from "@/pages/mine/components/Changed";
import YBS from "@/assets/utils/utils.js";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "VieOderInfo",
  mixins: [fontSizeMixin],
  components: {
    BaseInfo,
    ServiceSite,
    Person,
    DealBtns,
    PendingDetails,
    ReturnVisit,
    ChangeOrder,
    changeOrderHandle,
    SimplifyWork,
    EvaluationInfo,
    Changed,
    retroversion
  },
  data() {
    return {
      isDis: false,
      disabled: false,
      workOrderId: "",
      isSure: "确定",
      isAppoint: "派工",
      workOrdeInfoDate: [],
      workTypeCode: "",
      designatePersonnelTitle: "抢单",
      isShow: true, //从推送消息进入后的指派人员等相关信息是否显示 true 不显示
      isVieOder: true, //是否显示消息通知（为派工工单） true只显示抢单同时不显示消息通知
      isLeader: false,
      isPendOder: false,
      ShowPendOderBtn: false,
      personnelVal: false,
      designatePersonnel: "",
      noticePeople: "派工人员",
      designatePersonName: "",
      designatePersonnelTel: "",
      designatePersonnelOpenId: "",
      designatePersonnelId: "",
      selectedCheckboxs: [],
      curPage: 0,
      isShowPop: false,
      btnText: "已派工",
      lists: [],
      title: "",
      isFinished: false, //是否显示已派工按钮
      role: [],
      flag: false,
      origin: 0, //1为从我的工作台进入
      //转单处理相关
      canChangeOrder: false, //是否可以转单,显示转单按钮
      handleChangeOderInfo: false, //显示添加转单处理信息
      handleComponentsTitle: "", //处理组件的title
      localtionId: "", //获取指派班组使用
      itemTypeCode: "", //获取指派班组使用
      btnsGroup: true, //控制所有按钮组显示
      simplification: true, //精简工单
      designateDeptCode: "",
      // hideSimplifyWork: false // 派工挂单等操作时隐藏精简工单
      triggerConfig: [],
      currentTriggerIndex: 0
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "addDesignatePersonnel", "isLogin"])
  },
  methods: {
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    // 添加获取triggerConfig的方法
    getTriggerConfig() {
      // 直接获取triggerConfig，不再依赖showArrival
      this.$api.getTriggerConfig({
        taskId: this.$route.query.id || this.workOrderId,
        userId: this.loginInfo.staffId
      }).then(res => {
        this.triggerConfig = res;
        // 直接显示弹框，不再判断showArrival
        this.showTriggerConfigDialog();
      });
    },
    
    // 显示triggerConfig弹框
    showTriggerConfigDialog() {
      if (this.triggerConfig && this.triggerConfig.length > 0) {
        this.currentTriggerIndex = 0;
        this.showNextTrigger();
      }
    },
    
    // 显示下一个触发器内容
    showNextTrigger() {
      if (this.currentTriggerIndex < this.triggerConfig.length) {
        const currentContent = this.triggerConfig[this.currentTriggerIndex].triggerContent;
        this.$dialog.alert({
          title: '注意事项',
          message: `<div class="trigger-content-wrapper">${currentContent}</div>`,
          messageAlign: 'left',
          confirmButtonText: '已知晓',
          confirmButtonColor: '#4061d4',
          closeOnPopstate: false,
          allowHtml: true,
          getContainer:'.trigger-box'
        }).then(() => {
          this.currentTriggerIndex++;
          // 延迟显示下一个弹框
          setTimeout(() => {
            this.showNextTrigger();
          }, 300);
        });
      }
    },
    /**
     *获取工单任务详情信息(从消息推送进入)
     */
    getOderDetailsInfo() {
      //从消息推送进入
      if (this.origin == 0) {
        let curUserInfo = JSON.parse(sessionStorage.getItem("workOrderId"));
        this.workOrderId = curUserInfo.id;
      }
      this.$api
        .getTaskDetail({
          taskId: this.workOrderId
        })
        .then(this.getOderDetailsInfoSucc);
    },
    /**
     * 时间格式化载体
     */
    timeFunc() {},
    /**
     *获取工单任务详情信息成功回调函数
     */
    getOderDetailsInfoSucc(res) {
      let newRes = res.map(item => {
        item.fold = false;
        return item;
      }); // 添加时间轴内容折叠/展开状态
      let baseInfo = newRes[0];
      newRes.forEach(item => {
        if (item.operationCode == 10) {
          item.itemType = baseInfo.itemType;
          item.transportName = baseInfo.transportName;
          item.sourcesDeptName = baseInfo.sourcesDeptName;
          item.transportEndLocalOfficeName =
            baseInfo.transportEndLocalOfficeCode;
          item.transportEndLocalOffice = baseInfo.transportEndLocalOffice;
          item.transportEndLocal = baseInfo.transportEndLocal;
          item.transportStartLocal = baseInfo.transportStartLocal;
          item.transportStartLocalOffice = baseInfo.transportStartLocalOffice;
          item.transportName = baseInfo.transportName;
          item.customtransportNum = baseInfo.customtransportNum;
          item.questionDescription = baseInfo.questionDescription;
        }
      });
      this.workOrdeInfoDate = newRes;
      this.workTypeCode = this.workOrdeInfoDate[0].workTypeCode;
      this.localtionId = this.workOrdeInfoDate[0].localtionId;
      this.itemTypeCode = this.workOrdeInfoDate[0].itemTypeCode;
      this.canChangeOrder = res[0].changeOrder;
      this.designateDeptCode = res[0].designateDeptCode;
      this.timeFunc = global.timestampToTime;
      // 天津肿瘤需求  2024年12月10日 基础更改为多班组
      // if (
      //   // !this.staffInfo.officeId.split(",").includes(res[0].designateDeptCode)
      //   this.staffInfo.teamId != res[0].designateDeptCode
      // ) {
      //   //说明已转单到别的班组 ---2019年10月16日10:35:37by wd 基础更改为多班组
      //   this.btnsGroup = false;
      // }
      if (this.workOrdeInfoDate[0].flowCode != 1) {
        if (this.workOrdeInfoDate[0].flowCode == 2) {
          this.simplification = true;
        }
        if (this.workOrdeInfoDate[0].flowCode == 5) {
          this.simplification = false;
          this.btnText = "已取消";
        }
        if (this.workOrdeInfoDate[0].flowCode == 4) {
          this.simplification = false;
          this.btnText = "已完工";
        }
        if (this.workOrdeInfoDate[0].flowCode == 3) {
          this.simplification = false;
          this.btnText = "已挂单";
        } else {
        }
        this.isFinished = true;
      } else {
        this.btnText = "已派工";
        this.simplification = true;
      }
    },
    /**
     * 班组或工人抢单页面
     */
    handleGoToVieOder() {
      let robParams = {
        // 抢单参数传给订单操作页面
        id: this.workOrderId,
        userId: this.loginInfo.id,
        realName: this.loginInfo.staffName,
        designatePersonCode: this.staffInfo.staffId,
        designatePersonName: this.staffInfo.name,
        designatePersonPhone: this.staffInfo.mobile,
        appId: this.staffInfo.appList[0].appId,
        openId: this.staffInfo.wechat,
        staffId: this.staffInfo.staffId
      };
      this.$router.push({
        path: "/OrderHandle",
        query: {
          workOrderId: this.workOrderId,
          workOrdeInfoDate: this.workOrdeInfoDate,
          personnelVal: true,
          isVieOder: true,
          robParams,
          designateDeptCode: this.designateDeptCode,
          isRobOrAppoint: true,
          handleComponentsTitle: "抢单" // 抢单或派工操作
        }
      });
      return;
      // this.isShow = false
      // this.personnelVal = true
      // this.designatePersonnel = this.staffInfo.name
      // this.designatePersonnelId = this.staffInfo.staffId
      // this.designatePersonnelTel = this.staffInfo.mobile
      // this.designatePersonnelOpenId = this.staffInfo.wechat
    },
    /**
     * 班组派工处理页面
     */
    handleGoToAssign() {
      this.$router.push({
        path: "/OrderHandle",
        query: {
          workOrderId: this.workOrderId,
          workOrdeInfoDate: this.workOrdeInfoDate,
          isVieOder: false,
          personnelVal: false,
          designateDeptCode: this.designateDeptCode,
          designatePersonnelTitle: "指派",
          isRobOrAppoint: true // 抢单或派工操作
        }
      });
      return;
      // this.isShow = false
      // this.isVieOder = false
      // this.designatePersonnelTitle = '指派'
    },
    /**
     * 班组挂单处理页面
     */
    handleGoToPendOder() {
      this.isPendOder = true;
      this.ShowPendOderBtn = true;
    },
    /**
     * 开始填写处理转单信息
     */
    handleChangeOder() {
      this.$router.push({
        path: "/OrderHandle",
        query: {
          workOrderId: this.workOrderId,
          workOrdeInfoDate: this.workOrdeInfoDate,
          handleChangeOderInfo: true,
          handleComponentsTitle: "转单处理",
          workTypeCode: this.workTypeCode,
          localtionId: this.localtionId,
          itemTypeCode: this.itemTypeCode,
          designateDeptCode: this.designateDeptCode
        }
      });

      // this.handleChangeOderInfo = true
      // this.handleComponentsTitle = "转单处理"
      // this.ShowPendOderBtn = true //释放确定按钮
    },
    /**
     * 获取职工角色信息的请求
     */
    getUserRole() {
      this.axios
        .get(__PATH.ONESTOP + "/appDisUserLoginController.do?getUserRole", {
          params: {
            staffId: this.staffInfo.staffId
          },
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(this.handleGetUserRoleSucc);
    },
    /**
     * 获取职工角色成功回调函数
     */
    handleGetUserRoleSucc(response) {
      // let res = response.data;
      // if (res.code == 200) {
      //   //判断角色来显示按钮功能
      //   let allRole = global.leaderLists;
      //   for (let i = 0; i < res.data.length; i++) {
      //     if (allRole.indexOf(res.data[i]) > -1) {
      //       //角色为组长
      //       this.isLeader = true;
      //     }
      //   }
      //   for (let i = 0; i < res.data.length; i++) {
      //     if (allRole.indexOf(res.data[i]) > -1) {
      //       //角色为组长
      //       this.isLeader = true;
      //     } else {
      //       //角色为职员
      //       this.leaguerType = false;
      //     }
      //   }
      //   if (res.data.length == 0) {
      //     //角色为职员
      //     this.leaguerType = false;
      //   }
      // }
      if(this.staffInfo.leaguerType == 1) {
         this.isLeader = true;
      }else {
         this.leaguerType = false;
      }
    },
    /**
     * 跳转到指派人员页面
     */
    handleDesignatePersonnel() {
      this.$router.push({
        path: "/personnel",
        query: {
          isShow: this.isShow,
          isVieOder: this.isVieOder,
          noticePeople: this.noticePeople,
          disabled: this.disabled,
          designateDeptCode: this.workOrdeInfoDate[0].designateDeptCode
        }
      });
    },
    /**
     * 关闭指派人员弹窗
     * @param query
     */
    handleClosePopClick(query) {
      this.isShowPop = false;
      this.selectedCheckboxs = query;
      let allAtten = "";
      let allAttenId = "";
      let allAttenNotice = "";
      let allAttenTel = "";
      let allAttenOpenId = "";
      for (let i = 0; i < this.selectedCheckboxs.length; i++) {
        allAtten += this.selectedCheckboxs[i].designatePersonName + ",";
        allAttenId += this.selectedCheckboxs[i].designatePersonCode + ",";
        allAttenTel += this.selectedCheckboxs[i].designatePersonPhone + ",";
        allAttenOpenId += this.selectedCheckboxs[i].openId + ",";
        allAttenNotice +=
          this.selectedCheckboxs[i].designatePersonName +
          this.selectedCheckboxs[i].designatePersonPhone +
          ",";
      }
      if (allAttenId == "") {
        this.personnelVal = false;
      } else {
        this.personnelVal = true;
      }
      this.designatePersonnel = allAtten.slice(0, allAtten.length - 1);
      this.designatePersonnelId = allAttenId.slice(0, allAttenId.length - 1);
      this.designatePersonnelTel = allAttenTel.slice(0, allAttenTel.length - 1);
      this.designatePersonnelOpenId = allAttenOpenId.slice(
        0,
        allAttenOpenId.length - 1
      );
      this.noticePeople = allAttenNotice.slice(0, allAttenNotice.length - 1);
      if (this.selectedCheckboxs.length == 0) {
        this.noticePeople = "派工人员";
      }
    },
    /**
     * 抢单或派工成功请求
     */
    handleSubmitVieOderClick() {
      if (this.handleChangeOderInfo) {
        //转单请求
        if (!this.$refs.changeOrder.teamInfo.name) {
          $.toast("请选择服务部门", "text");
          return;
        }
        if (!this.$refs.changeOrder.textareaValue) {
          $.toast("请填写转单说明", "text");
          return;
        }
        this.$api
          .toTeamsChangeTask({
            taskId: this.workOrderId,
            userId: this.loginInfo.id,
            designateDeptCode: this.$refs.changeOrder.teamInfo.id,
            designateDeptName: this.$refs.changeOrder.teamInfo.name,
            realName: this.loginInfo.staffName,
            appId: process.env.WxAppid,
            feedbackExplain: this.$refs.changeOrder.textareaValue, //转单说明
            deptCode: this.loginInfo.deptId //当前用户所属班组code
          })
          .then(res => {
            $.toast("转单成功", function() {
              window.location.href = process.env.WX + "/personalCenter";
            });
          });
      } else if (this.isPendOder) {
        let pendingReasonVal = this.$refs.pendingOder.pendingVal;
        let pendingSolutionVal = this.$refs.pendingOder.value;
        let pendingSolutionTime = this.$refs.pendingOder.ResolutionTime;
        //我的工作台-未处理
        this.$api
          .saveEntryOrders({
            id: this.workOrderId,
            userId: this.loginInfo.id,
            disEntryOrdersReason: pendingReasonVal,
            disEntryOrdersSolution: pendingSolutionVal,
            disPlanSolutionTime: pendingSolutionTime
          })
          .then(this.handleSubmitVieOderSucc);
      } else {
        if (this.designatePersonnelId) {
          //抢单或派工
          this.$api
            .saveScheduling({
              id: this.workOrderId,
              userId: this.loginInfo.id,
              realName: this.loginInfo.staffName,
              designatePersonCode: this.designatePersonnelId,
              designatePersonName: this.designatePersonnel,
              designatePersonPhone: this.designatePersonnelTel,
              appId: this.staffInfo.appList[0].appId,
              openId: this.designatePersonnelOpenId,
              staffId: this.staffInfo.staffId
            })
            .then(this.handleSubmitVieOderSucc);
        } else {
          $.hideLoading();
          $.toast("请选择派工人员", "text");
        }
      }
    },
    /**
     * 抢单或派工成功成功回调函数
     */
    handleSubmitVieOderSucc(res) {
      if (this.personnelVal == false) {
        $.toast("请选择指派人员", "text");
      } else {
        this.isDis = true;
        this.disabled = true;
        $.toast(res.flowtype);
        setTimeout(() => {
          window.location.href = process.env.WX + "/personalCenter";
        }, 1000);
      }
    },
    /**
     * 折叠/展开时间轴
     */
    handleFold(e, idx) {
      if (e.target.className == "header" || e.target.tagName == "I") {
        this.workOrdeInfoDate[idx].fold = !this.workOrdeInfoDate[idx].fold;
      }
    },
    /**
     * 修改逻辑 区分工单类型 综合维修， 应急保洁，综合服务，搬运
     */
    goHandleReviseOder() {
      sessionStorage.setItem("pageState", true);
      this.$router.push({
        path: "/reviseOder",
        query: {
          workTypeCode: this.workTypeCode,
          id: this.workOrderId,
          lastQuery: this.$route.query
        }
      });
    },
    arrive() {
      this.$api
        .arrive({
          taskId: this.workOrderId,
          arrivalPersonId: this.staffInfo.staffId,
          arrivalPersonName: this.staffInfo.staffName
        })
        .then(res => {});
    },
    goAlarmDetails() {
      this.$router.push({
        path: "/alarmAck",
        query: {
          alarmId: this.workOrdeInfoDate[0].sysForShort
        }
      });
    }
  },
  mounted() {
    this.getUserRole();
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if (this.$route.query.source == "myWorkbench") {
      //从我的工作台（未处理）进入抢单页面
      this.workOrderId = this.$route.params.id;
      this.origin = 1;
      //        this.getOderDetailsInfo()
    } else {
      this.origin = 0;
      //从消息推送进入抢单页面
      this.getOderDetailsInfo();
    }
    //如果有指派人员就展示，没有就展示请选择
    if (this.addDesignatePersonnel.length > 0 && this.$route.query.backParams) {
      this.isShow = this.$route.query.backParams.isShow;
      this.disabled = this.$route.query.backParams.disabled;
      this.isVieOder = this.$route.query.isVieOder;
      this.noticePeople = this.$route.query.noticePeople;

      this.personnelVal = true;
      let addDesignatePers = this.addDesignatePersonnel;
      let allAtten = "";
      let allAttenId = "";
      let allAttenNotice = "";
      let allAttenTel = "";
      let allAttenOpenId = "";
      for (let i = 0; i < addDesignatePers.length; i++) {
        allAtten += addDesignatePers[i].designatePersonName + ",";
        allAttenId += addDesignatePers[i].designatePersonCode + ",";
        allAttenTel += addDesignatePers[i].designatePersonPhone + ",";
        allAttenOpenId += addDesignatePers[i].openId + ",";
        allAttenNotice +=
          addDesignatePers[i].designatePersonName +
          addDesignatePers[i].designatePersonPhone +
          ",";
      }
      if (allAttenId == "") {
        this.personnelVal = false;
      } else {
        this.personnelVal = true;
      }
      this.designatePersonnel = allAtten.slice(0, allAtten.length - 1);
      this.designatePersonnelId = allAttenId.slice(0, allAttenId.length - 1);
      this.designatePersonnelTel = allAttenTel.slice(0, allAttenTel.length - 1);
      this.designatePersonnelOpenId = allAttenOpenId.slice(
        0,
        allAttenOpenId.length - 1
      );
      this.noticePeople = allAttenNotice.slice(0, allAttenNotice.length - 1);
    }
    //如果没有部门，无权限操作一站式，退出公众号
    // if (
    //   this.loginInfo.userOffice[0].deptCode == "" ||
    //   !this.loginInfo.userOffice[0].deptCode
    // ) {
    //   $.toast("无权限操作", "forbidden", function() {
    //     wx.closeWindow();
    //   });
    // }
  },
  activated() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    //处理从详情页返回列表页时数据展示
    this.handleChangeOderInfo = false;
    this.ShowPendOderBtn = false;
    $("#education").hide();
    
    // 添加调用getTriggerConfig方法
    this.getTriggerConfig();
    
    if (sessionStorage.selectTime) {
      sessionStorage.removeItem("selectTime");
    }
    if (this.$route.query.source) {
      //切换我的工作台的未处理
      this.workOrderId = this.$route.query.id;
      this.origin = 1;
      this.isShow = true;
      this.personnelVal = false;
      this.noticePeople = "派工人员";
      this.isVieOder = true;
      this.getOderDetailsInfo();
    } else if (this.$route.query.backParams) {
      //来源于指派人员列表
      this.handleClosePopClick(this.addDesignatePersonnel);
    }
    if (this.$route.query.from == "qualityAnalysis") {
      this.btnsGroup = false;
    }
  },
  beforeRouteEnter(to, from, next) {
    //进入后先判断是否是登录状态

    if (to.matched.some(m => m.meta.auth)) {
      //        先获取推送链接带过来的参数
      let url = location.search; //?unitCode=1212&hospitalCode=12&interfaceNum=0&id=1221
      let str = url.substr(1); //unitCode=1212&hospitalCode=12&interfaceNum=0&id=1221
      let strs = str.split("&"); //[]
      let theRequest = {};
      if (str.indexOf("project") > -1) {
        //从推送消息链接进入
        for (let i = 0; i < strs.length; i++) {
          theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
        }
        if (sessionStorage.getItem("workOrderId")) {
          sessionStorage.setItem(
            "workOrderId",
            sessionStorage.getItem("workOrderId")
          );
        } else {
          sessionStorage.setItem("workOrderId", JSON.stringify(theRequest));
        }
      }
      next(vm => {
        if (!vm.loginInfo) {
          // 未登录则跳转到登陆界面，query:{ Rurl: to.fullPath}表示把当前路由信息传递过去方便登录后跳转回来；
          next({ path: "/login", query: { Rurl: to.fullPath } });
        } else {
          if (from.name == "Login") {
            window.location.reload();
          } else {
            next();
          }
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.wrapper {
  font-size: 0.32rem;
  overflow: hidden;
  overflow-y:hidden;
  padding-bottom: 70px;


  .item {
    .header {
      height: 0.57rem;
      display: flex;
      align-items: center;
      margin: 0.15rem 0 0.15rem 0.3rem;
      background: #fff;
      position: relative;

      .iconfont {
        font-size: 0.4rem;
        color: $btnColor;
      }

      .title-body {
        height: 0.6rem;
        display: flex;
        align-items: center;
        margin-left: 10px;
        background: #eceef8;
        padding: 0 0.24rem;
        border-radius: 0.3rem;
        white-space: nowrap;

        .dot {
          display: inline-block;
          width: 0.09rem;
          height: 0.09rem;
          background: $btnColor;
        }

        .title {
          font-size: 0.28rem;
          font-weight: 700;
          margin: 0 0.45rem 0 0.08rem;
        }

        .time {
          font-size: 0.3rem;
          color: #4F87FB;
        }
      }

      .arrows-down {
        display: inline-block;
        width: 13px;
        height: 7px;
        background: url('~images/<EMAIL>');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100%;
        position: absolute;
        top: 50%;
        right: 20px;
        transform: translateY(-50%);
      }

      .arrows-up {
        background-image: url('~images/<EMAIL>');
      }
    }
  }

  .vie-info {
    .consumables-wrapper {
      background: #fff;
      overflow: hidden;

      .consumables-content {
        box-sizing: border-box;
        border-radius: 5px;
        overflow: hidden;
        margin: 0.32rem;
      }
    }

    .notice-wrapper {
      height: auto;
      min-height: 0.98rem;
      line-height: 1.5em;
      padding: 0px 0.45rem 0px 0.37rem;
      margin-left: 0.45rem;
      border-left: 1px solid #e5e5e5;
    }

    .designate-css {
      margin-top: -5px;
      padding-top: 10px;
    }
  }

  .btns {
    height: 1.32rem;
    padding: 0.22rem 0.16rem;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 2;
    border-top: 1px solid $bgColor;

    .btn {
      margin: 0 0.16rem;
      height: 0.88rem;
    }
  }

  .list {
    display: flex;
    justify-content: space-between;
    font-size: 0.3rem;
    itemBaseStyle();
    background: #f4f4f4;

    .count {
      .less {
        color: #d1d1d1;
      }

      .add {
        color: $btnColor;
      }

      .num {
        display: inline-block;
        text-align: center;
        width: 50px;
      }
    }
  }
}

.noScroll {
  overflow-y: hidden;
}

.item {
  .disClick, .disabledClick {
    pointer-events: none;
  }

  .text-wrapper {
    font-size: 0.28rem;
    color: $textColor;
    text-align: left;

    .consumablesicon {
      color: $color;
    }
  }
}

.des-pers {
  margin-bottom: $marginb;
  min-height: 0.98rem;
  line-height: 1.5;
  background-color: #fff;
  padding: 0 0.32rem;
  word-break: break-word;
}
</style>
