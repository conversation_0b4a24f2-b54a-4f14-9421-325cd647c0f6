<template>
  <div class="add-pers-wrappper">
    <div class="notice-wrapper">
      <div class="cell item border-bottom">
        <div class="title"><span>指派人员</span></div>
        <div class="content-text text-wrapper" @click="handleDesignatePersonnel()">
          <span class="line-css" v-if="personnelVal">{{designatePersonnel}}</span>
          <span v-else class="please-select">
            请选择
            <span class="iconfont">&#xe646;</span>
          </span>
        </div>
      </div>
       <div
        class="border-bottoom item notice-wrapper"
      >
        <div class="weui-cell__hd" style="margin-top: 0.32rem;">
          <label class="weui-label">消息通知</label>
        </div>
        <div class="weui-cell__bd text-wrapper" >
          <div  class="line-css" v-for="(item,i) of designateList" :key="i">{{item}}</div>
        </div>
        <div class="weui-cell__ft"></div>
      </div>
    </div>
    <personnel
      v-show="isComplete"
      class="personnel-compontent"
      ref="pers"
      :designateDeptCode="workOrdeInfoDate[0].designateDeptCode"
      :workOrderId="workOrderId"
      @backPersonnel="backPersonnel"
    ></personnel>
  </div>
</template>

<script type=text/ecmascript-6>
import global from "@/utils/Global";
import { mapState } from "vuex";
import personnel from "./personnel";
export default {
  name: "AddPersonnel",
  computed: {
    ...mapState(["addDesignatePersonnel"])
  },
  props: {
    source: {
      type: String,
      default: "pending"
    },
    isShow: {
      type: Boolean,
      default: true
    },
    isVieOder: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    workOrdeInfoDate: {
      type: Array,
      default: function() {
        return [];
      }
    },
    designatePersonnelTitle: {
      type: String,
      default: ""
    },
    workOrderId: {
      type: String,
      default: ""
    },
    source: {
      type: String,
      default: ""
    }
  },
  components: { personnel },
  data() {
    return {
      noticePeople: "", //消息通知
      personnelVal: "",
      designatePersonnel: "", //指派人员
      designatePersonnelId: "", //指派人员id
      designatePersonnelTel: "", //指派人员电话
      designatePersonnelOpenid: "", //指派人员openId
      isComplete: false,
      // designateDeptCode: this.workOrdeInfoDate[0].designateDeptCode,
      designateList:{}  //消息通知列表  仅做展示用
    };
  },
  methods: {
    /**
     * 跳转到指派人员页面
     */
    handleDesignatePersonnel() {
      if (this.source == "complete") {
        this.clearDesignateP();
        this.isComplete = true;
      } else {
        this.$router.push({
          path: "/personnel",
          query: {
            source: this.source,
            isShow: this.isShow,
            isVieOder: this.isVieOder,
            personnelVal: this.personnelVal,
            noticePeople: this.noticePeople,
            disabled: this.disabled,
            designateDeptCode: this.workOrdeInfoDate[0].designateDeptCode,
            workOrdeInfoDate: this.workOrdeInfoDate,
            designatePersonnelTitle: this.designatePersonnelTitle,
            workOrderId: this.workOrderId,
            complete: this.complete
          }
        });
      }
    },
    /**
     * 清空已选指派人员
     */
    clearDesignateP() {
      this.designatePersonnel = "";
      this.designatePersonnelId = "";
      this.designatePersonnelTel = "";
      this.designatePersonnelOpenid = "";
      this.noticePeople = "";
    },
    /**
     * 时间格式化载体
     */
    timeFunc() {},
    backPersonnel(option) {
      this.designatePersonnel = option
        .map(item => {
          return item.designatePersonName;
        })
        .join(",");
      if (this.designatePersonnel) {
        this.personnelVal = true;
      }
      this.noticePeople = option
        .map(item => {
          return item.designatePersonName + "" + item.designatePersonPhone;
        })
        .join(",");
      this.designatePersonnelId = option
        .map(item => {
          return item.designatePersonCode;
        })
        .join(",");
      this.designatePersonnelTel = option
        .map(item => {
          return item.designatePersonPhone;
        })
        .join(",");
      this.isComplete = false;
      this.designateList=this.noticePeople.split(",") 
    }
  },
  mounted() {
    console.log("添加指派人员组件");

    if (this.addDesignatePersonnel.length > 0 && this.$route.query.backParams) {
      this.havePendingPeo = true;
      this.personnelVal = true;
      let addDesignatePers = this.addDesignatePersonnel;
      let allAtten = "";
      let allAttenId = "";
      let allAttenNotice = "";
      let allAttenTel = "";
      let allAttenOpenId = "";
      for (let i = 0; i < addDesignatePers.length; i++) {
        allAtten += addDesignatePers[i].designatePersonName + ",";
        allAttenId += addDesignatePers[i].designatePersonCode + ",";
        allAttenTel += addDesignatePers[i].designatePersonPhone + ",";
        allAttenOpenId += addDesignatePers[i].openId + ",";
        allAttenNotice +=
          addDesignatePers[i].designatePersonName +
          addDesignatePers[i].designatePersonPhone +
          ",";
      }
      if (allAttenId == "") {
        this.personnelVal = false;
      } else {
        this.personnelVal = true;
      }
      this.designatePersonnel = allAtten.slice(0, allAtten.length - 1);
      this.designatePersonnelId = allAttenId.slice(0, allAttenId.length - 1);
      this.designatePersonnelTel = allAttenTel.slice(0, allAttenTel.length - 1);
      this.designatePersonnelOpenid = allAttenOpenId.slice(
        0,
        allAttenOpenId.length - 1
      );
      this.noticePeople = allAttenNotice.slice(0, allAttenNotice.length - 1);
      this.designateList=this.noticePeople.split(",")  //消息通知 仅做展示用
    }
    if (this.$route.query.source == "myWorkbench") {
      //清空指派人员信息（已填写的信息缓存）
      this.clearDesignateP();
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.personnel-compontent {
  position: absolute;
  width: 100vw;
  height: 100vh;
  top: 0;
  z-index: 100;
}

.add-pers-wrappper {
  .item {
    .header {
      height: 0.57rem;
      display: flex;
      align-items: center;
      margin: 0.15rem 0 0.15rem 0.3rem;
      background: #fff;

      .iconfont {
        font-size: 0.4rem;
        color: $btnColor;
      }

      .title-body {
        height: 0.6rem;
        display: flex;
        align-items: center;
        margin-left: 10px;
        background: #eceef8;
        padding: 0 0.24rem;
        border-radius: 0.3rem;
        white-space: nowrap;

        .dot {
          display: inline-block;
          width: 0.09rem;
          height: 0.09rem;
          background: $btnColor;
        }

        .title {
          font-size: 0.28rem;
          font-weight: 700;
          margin: 0 0.45rem 0 0.08rem;
        }

        .time {
          font-size: 0.3rem;
          color: #4F87FB;
        }
      }
    }
  }

  .notice-wrapper {
    background: #fff;
    box-sizing: border-box;
    

    .item {
      // line-height: 0.98rem;
      padding-top:0.34rem;
      padding: 0 0.32rem;
      font-size: calc(16px * var(--font-scale))!important;
      min-height: 0.98rem;
      height: auto;
      display: flex;
      // align-items: baseline;

      .text-wrapper {
        min-height: 0.98rem;
        word-wrap: break-word;
        word-break: break-all;
        line-height: 1.5em;
        text-align: left;

        .please-select {
          display :inline-block;
          width: 100%;
          display: flex; 
          justify-content: space-between; 
          color: $textColor;
          font-size: calc(16px * var(--font-scale))!important;
        }
      }
    }
  }
}
.notice-wrapper {
  .text-wrapper{
    padding-top:0.35rem;
    color: $textColor;
    font-size: calc(16px * var(--font-scale))!important;
  }
}
.line-css{
  line-height :0.4rem !important;
  color:$contentColor;
}
.cell{
  display : flex;
  .title{
    padding-top:0.34rem;
    width :105px;
  }
  .content-text{
    width : calc(100% - 105px);
    line-height :0.42rem;
    padding-top:0.35rem;
    padding-bottom:0.3rem;
    color:$contentColor;
  }
}
</style>
