<template>
  <div class="btns">
    <!--通过判断是领导还是工人来进行显示，领导都显示，工人只有抢单-->
    <button class="btn" :class="{vieOderBtn:!isLeader}" @click="goToVieOder">抢单</button>
    <button class="btn" v-if="isLeader" @click="goToAssign">派工</button>
    <!-- <button
          class="btn"
          v-if="canChangeOrder"
          :class="{vieOderBtn:canChangeOrder}"
          @click="goToChangeOder"
      >
        转派
      </button>
            <button
          class="btn"
          v-if="canChangeOrder"
          :class="{vieOderBtn:canChangeOrder}"
          @click="handleReviseOder"
      >
        修改
    </button>-->
    <span class="btn btn-more" v-if="isLeader"  @click="moreOperate">更多操作</span>
  </div>
</template>

<script type=text/ecmascript-6>
export default {
  name: "DealFormBtns",
  props: ["isLeader", "canChangeOrder","workOrdeInfoDate"],
  data() {
    return {
      actionSheet: null
    };
  },
  mounted() {
    let self = this
    window.addEventListener(
      "popstate",
      function(e) {
        if (self.actionSheet) self.actionSheet.hide();
      },
      false
    );
  },
  destroyed() {
    if (this.actionSheet) this.actionSheet.hide();
  },

  beforeRouteLeave(to, form, next) {
    if (this.actionSheet) this.actionSheet.hide();
    next();
  },
  methods: {
    goToVieOder() {
      this.$emit("goVieOder");
    },
    goToAssign() {
      this.$emit("goToAssign");
    },
    goToPendOder() {
      this.$emit("goToPendOder");
    },
    goToChangeOder() {
      this.$emit("goToChangeOder");
    },
    handleReviseOder() {
      this.$emit("handleReviseOder");
    },
    moreOperate() {
      let operateArr = [{ content: "抢单", type: "1" }];
      if (this.isLeader) {
        operateArr.push({ content: "派工", type: "2" });
        operateArr.push({ content: "转派", type: "3" });
      }
      if(this.workOrdeInfoDate[0].workTypeCode != "16" && this.isLeader) {
        operateArr.push({ content: "修改", type: "4" });
      }
      if (this.workOrdeInfoDate[0].workTypeCode == "16") {
        operateArr.push({ content: "到达", type: "6" });
        operateArr.push({ content: "报警详情", type: "7" });
      }
      this.actionSheet = this.$createActionSheet({
        data: operateArr,
        onSelect: (item, index) => {
          console.log(item);
          switch (item.type) {
            case "1":
              this.goToVieOder(); //抢单
              break;
            case "2":
              this.goToAssign(); //派工
              break;
            case "3":
              this.goToChangeOder(); //转派
              break;
            case "4":
              this.handleReviseOder(); //修改
              break;
            case "6":
              this.$parent.arrive(); //到达
              break;
            case "7":
              this.$parent.goAlarmDetails(); //报警详情
              break;
            default:
              break;
          }
        }
      }).show();
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';

.btns {
  height: 1.32rem;
  padding: 0.22rem 0.16rem;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 2;
  border-top: 1px solid $bgColor;

  .btn {
    margin: 0rem 0.16rem;
    line-height: inherit;
    flex: 1;
    height: 0.88rem;
    font-size: calc(16px * var(--font-scale))!important
    border-radius: 5px;
    color: #fff;
    background-color: $btnColor;
  }

  .btn-more {
    font-size: calc(15px * var(--font-scale))!important;
    color: #3562db;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
  }
}
</style>
