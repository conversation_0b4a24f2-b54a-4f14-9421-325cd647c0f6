<template>
  <div class="wrapper">
    <div class="first">
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd"><label class="weui-label title">紧急程度</label></div>
        <div class="weui-cell__bd">
          <span :class="{urgent: ['1','0'].includes(urgencyDegreeCode)}">{{urgencyDegree}}</span>
        </div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd"><label class="weui-label title">工单类型</label></div>
        <div class="weui-cell__bd">{{workTypeName}}</div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd"><label class="weui-label title">工单号</label></div>
        <div class="weui-cell__bd">{{workNum}}</div>
      </div>
      <div v-if="workTypeCode == 5">
        <div class="weui-cell item">
          <div class="weui-cell__hd"><label class="weui-label title">身份隐匿</label></div>
          <div class="weui-cell__bd">{{signatureFlagName}}</div>
        </div>
      </div>

    </div>
    <div class="second">
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd"><label class="weui-label title">申报科室</label></div>
        <div class="weui-cell__bd">{{sourcesDeptName}}</div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd"><label class="weui-label title">申报人</label></div>
        <div class="weui-cell__bd">{{callerName}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">职工工号</label></div>
        <div class="weui-cell__bd">{{callerJobNum}}</div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd"><label class="weui-label title">职务</label></div>
        <div class="weui-cell__bd">{{callerJob}}</div>
      </div>
      <div class="weui-cell border-rightbottom item row">
        <div class="weui-cell__hd"><label class="weui-label title">电话</label></div>
        <div class="weui-cell__bd">{{sourcesPhone}}</div>
      </div>

      <div v-if="workTypeCode == 1 || workTypeCode == 2"><!--保洁和维修-->
        <div v-for="(item,index) of itemType" :key="index">
          <div class="weui-cell item border-rightbottom border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">服务地点</label></div>
            <div class="weui-cell__bd">{{item.localtion}}</div>
          </div>
          <div class="weui-cell item border-rightbottom border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">服务事项</label></div>
            <div class="weui-cell__bd content-css questionDetail content-css" v-if="item.itemTypeName">{{item.itemTypeName}}-{{item.itemDetailName}}-{{item.itemServiceName}}</div>
            <div class="weui-cell__bd content-css questionDetail content-css" v-else>{{transportName}}</div>
          </div>
        </div>
      </div>

      <div v-if="workTypeCode == 5"><!--投诉-->
        <div class="weui-cell item border-rightbottom" v-for="(item,index) of tslocaltion" :key="index">
          <div class="weui-cell__hd"><label class="weui-label title">服务地点</label></div>
          <div class="weui-cell__bd">{{item}}</div>
        </div>
      </div>
      <div v-if="workTypeCode == 3"> <!--运输-->
        <div class="row">
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">服务起点</label></div>
            <div class="weui-cell__bd">{{transportStartLocal}}</div>
          </div>
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">服务终点</label></div>
            <div class="weui-cell__bd">{{transportEndLocal}}</div>
          </div>
        </div>
        <div class="row">
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">服务类型</label></div>
            <div class="weui-cell__bd">{{transportRangeName}}</div>
          </div>
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">服务种类</label></div>
            <div class="weui-cell__bd kind">{{transportTypeName}}</div>
          </div>
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">携带工具</label></div>
            <div class="weui-cell__bd kind">{{carryTools}}</div>
          </div>
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">需用人数</label></div>
            <div class="weui-cell__bd">{{needNum}}</div>
          </div>
        </div>
        <div class="row">
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">接收科室</label></div>
            <div class="weui-cell__bd">{{recipientPersonDept}}</div>
          </div>
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">接收人</label></div>
            <div class="weui-cell__bd">{{recipientPersonName}}</div>
          </div>
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">职务</label></div>
            <div class="weui-cell__bd">{{recipientPersonJob}}</div>
          </div>
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">职工工号</label></div>
            <div class="weui-cell__bd">{{recipientPersonJobnum}}</div>
          </div>
          <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">联系电话</label></div>
            <div class="weui-cell__bd">{{needPhone}}</div>
          </div>
        </div>
      </div>

    </div>
    <div class="third">
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd"><label class="weui-label title">申报描述</label></div>
      </div>
      <div class="weui-cell__bd">
          <textarea
              class="weui-textarea desc"
              rows="4"
              disabled
          >{{questionDescription}}</textarea>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
 const wx = require('weixin-js-sdk')
  import {mapState} from 'vuex'
  export default {
    name: "WorkBaseInfo",
    data () {
      return {
        play: true,
        urgencyDegree: '',
        urgencyDegreeCode: '',
        workTypeName:'',
        workNum:'',
        sourcesDeptName:'',
        callerName:'',
        callerJobNum:'',
        workTypeCode:1,
        transportStartLocal:'',
        transportEndLocal:'',
        transportRangeName:'',
        recipientPersonDept:'',
        transportTypeName:'',
        recipientPersonName:'',
        recipientPersonJob:'',
        recipientPersonJobnum:'',
        sourcesPhone:'',
        signatureFlag:'',
        signatureFlagName:'',
        callerJob:'',
        needPhone:'',
        itemType:[],
        tslocaltion:[],
        itemTypeName:'',
        itemDetailName:'',
        questionDescription:'',
        callerTapeUrl:'',
        isCallerTapeUrl:true,
        attachment:[],
        audioDuration:'',
        flowCode:'',
        flowType:'',
        carryTools:'',
        needNum:'',
      }
    },
    computed: {
      ...mapState(['loginInfo'])
    },
    methods:{
      /**
       * 播放音频
       */
      handlePlayAudioClick () {
        this.play = false
        const audio = this.$refs.audio
        audio.play();
//        $.showLoading("加载中……")
      },
      handleCanPlay () {
        this.audioDuration = Math.round(this.$refs.audio.duration) + '″'
      },
      /**
       * 暂停播放音频
       */
      handlePauseAudioClick () {
        this.play = true
      },
      /**
       *音频播放结束事件
       */
      handleAudioEnded () {
        this.play = true
      },
      /**
       *获取工单任务详情信息(从消息推送进入)
       */
      getOderDetailsInfo (curUserInfo) {
        let taskDetailFlag = JSON.parse(sessionStorage.getItem('hasBeenObtainedTaskDetail'))
        if (taskDetailFlag != 1) {
          $.showLoading()
          let curUserInfo = JSON.parse(sessionStorage.getItem("workOrderId"))
          this.axios.get(__PATH.ONESTOP+'/appDisOlgTask.do?getTaskDetail',{
                params:{
                  hospitalCode: curUserInfo.hospitalCode,
                   unitCode: curUserInfo.unitCode,
                   interfaceNum:curUserInfo.interfaceNum,
                   id:curUserInfo.id
                }
              })
              .then(this.getOderDetailsInfoSucc)
        }
      },
      /**
       *获取工单任务详情信息(从我的工作台（未处理）进入)
       */
      getOderDetailsInfoFromMine (par) {
        $.showLoading()
        this.axios.get(__PATH.ONESTOP+'/appDisOlgTask.do?getTaskDetail',{
              params:{
                hospitalCode: par.hospitalCode,
                unitCode: par.unitCode,
                interfaceNum:par.interfaceNum,
                id:par.id
              }
            })
            .then(this.getOderDetailsInfoSucc)
      },
      /**
       *获取工单任务详情信息成功回调函数
       */
      getOderDetailsInfoSucc (response) {
        if (response.data.code == 200) {
          $.hideLoading()
          let res = response.data.data
          this.urgencyDegree = res.urgencyDegree
          this.urgencyDegreeCode = res.urgencyDegreeCode
          this.workTypeName = res.workTypeName
          this.workTypeCode = res.workTypeCode
          this.workNum = res.workNum
          this.sourcesDeptName = res.sourcesDeptName
          this.callerName = res.callerName
          this.callerJobNum = res.callerJobNum
          this.tslocaltion = res.tslocaltion  /*投诉地点*/
          this.itemType = res.itemType   /*保洁和维修地点*/
          this.transportStartLocal = res.transportStartLocal   /*运输起点*/
          this.transportEndLocal = res.transportEndLocal   /*运输终点*/
          this.transportRangeName = res.transportRangeName
          this.transportTypeName = res.transportTypeName
          this.recipientPersonName = res.recipientPersonName
          this.recipientPersonJob = res.recipientPersonJob
          this.recipientPersonJobnum = res.recipientPersonJobnum
          this.recipientPersonDept = res.recipientPersonDept
          this.sourcesPhone = res.sourcesPhone
          this.signatureFlag = res.signatureFlag
          this.callerJob = res.callerJob
          this.needPhone = res.needPhone
          this.questionDescription = res.questionDescription
          this.callerTapeUrl = res.callerTapeUrl
          this.attachment = res.attachment
          this.flowCode = res.flowCode
          this.flowType = res.flowType
          this.carryTools = res.carryTools
          this.needNum = res.needNum
          //耗材领用consumable，指派人员designatePersonName
          this.$emit('getWorkTypeCode',
                      {workTypeCode:res.workTypeCode,flowCode:res.flowCode,flowType:res.flowType,consumable:res.consumable,designatePersonName:res.designatePersonName}
          )
          let result = response.data.data
          let obj = {}
          try{
            obj = JSON.parse(JSON.stringify(result))
          }catch (err){}
          delete obj.consumable
          sessionStorage.setItem('taskDetailInfo',JSON.stringify(obj)) //保存工单详情信息
          sessionStorage.setItem('hasBeenObtainedTaskDetail',1)//记录是否已经请求过保存工单详情信息，1为已请求过
        }
        if (this.callerTapeUrl == ''){
          this.isCallerTapeUrl = false
        }
        if (this.signatureFlag == 1) {
          this.callerName = '***'
          this.callerJobNum = '***'
          this.needPhone = '***'
          this.signatureFlagName = "匿名"
        }
        if (this.signatureFlag == 0) {
          this.signatureFlagName = "署名"
        }
      },
      handleHideLoading () {
        try{
          $.hideLoading()
        }catch(error){}
      },
    },
    mounted () {
//      this.getOderDetailsInfo()
//      this.getConfigParams()
      let result = {}
      try{
        if (JSON.parse(sessionStorage.getItem('taskDetailInfo'))) {
          result = JSON.parse(sessionStorage.getItem('taskDetailInfo'))
        }
      }catch (err){}
      if (Object.keys(result).length != 0) {
        this.urgencyDegree = result.urgencyDegree
        this.workTypeName = result.workTypeName
        this.workTypeCode = result.workTypeCode
        this.workNum = result.workNum
        this.sourcesDeptName = result.sourcesDeptName
        this.callerName = result.callerName
        this.callerJobNum = result.callerJobNum
        this.tslocaltion = result.tslocaltion
        this.itemType = result.itemType
        this.transportStartLocal = result.transportStartLocal
        this.transportEndLocal = result.transportEndLocal
        this.transportRangeName = result.transportRangeName
        this.transportTypeName = result.transportTypeName
        this.recipientPersonName = result.recipientPersonName
        this.recipientPersonJob = result.recipientPersonJob
        this.recipientPersonJobnum = result.recipientPersonJobnum
        this.recipientPersonDept = result.recipientPersonDept
        this.sourcesPhone = result.sourcesPhone
        this.signatureFlag = result.signatureFlag
        this.callerJob = result.callerJob
        this.needPhone = result.needPhone
        this.questionDescription = result.questionDescription
        this.callerTapeUrl = result.callerTapeUrl
        this.attachment = result.attachment
        this.flowCode = result.flowCode
        this.flowType = result.flowType
        this.$emit('getWorkTypeCode',
            {workTypeCode:result.workTypeCode,flowCode:result.flowCode,flowType:result.flowType,designatePersonName:result.designatePersonName}
        )
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .wrapper
    background-color: $bgColor
    .item
      itemBaseStyle()
      .urgent
        color: red
      .questionDetail
        color:$contentColor
        line-height: 1.5em

    .second
      margin: .2rem -0px
      .row
        marginBottom20()
    .third
      background-color: #fff
      marginBottom20()
      padding-bottom: .16rem
      .desc
        line-height: 28px
        padding: 0 10px
        box-sizing: border-box
        background-color: #fff
      .voice-desc
        display: flex
        align-items: center
        padding: .54rem .32rem
        .text-title
          width: 2.4rem
          color: #888
        button
          display: flex
          align-items: center
          justify-content: center
          .play-icon
            display: flex
            position: absolute
            align-items: center
            left: 0
            justify-content: center
            padding-left: 5px
            .play-img
              width: .4rem
            span
              font-size: 14px
              padding-left: 5px
      .img-desc
        .text-title
          color: #888888
          line-height: 1rem
          padding-left: .32rem
        .img-content
          background: #fff
          padding: 0 .3rem
          display: flex
          .img-wrapper
            width: 30%
            height: 1.4rem
            margin: .1rem
            position: relative
            .img-box
              height: 100%
              overflow: hidden
              .img
                width: 100%
    .audio
      display: none

  .title
    font-size: .32rem
  .kind
    line-height: 1
    height: auto
</style>