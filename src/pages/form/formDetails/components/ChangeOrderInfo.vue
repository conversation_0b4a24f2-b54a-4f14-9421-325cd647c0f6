<template>
  <div class="content" :class="{'fold': fold}">
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">服务部门</label>
      </div>
      <div class="weui-cell__bd content-css">{{ changeOrderData.designateDeptName }}</div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">服务人员</label>
      </div>
      <div class="weui-cell__bd content-css">{{ changeOrderData.designatePersonName }}</div>
    </div>
    <div class="desc-content" :class="{'fold': fold}">
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label title">转派说明</label>
        </div>
      </div>
      <div class="weui-cell__bd">
        <div
          class="weui-textarea desc content-css"
          rows="4"
          style="line-height: 30px;"
          disabled
        >{{ changeOrderData.feedbackExplain }}</div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
export default {
  name: "ChangeOrder",
  props: ["changeOrderData"],
  computed: {
    fold() {
      return this.changeOrderData.fold;
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.content {
  timelineContent();

  .item {
    itemBaseStyle();

    .content-css {
      color: $contentColor;
      font-size: calc(16px * var(--font-scale))!important
    }

    .title {
      font-size: calc(16px * var(--font-scale))!important
    }
  }

  .desc-content {
    .desc {
      line-height: 28px;
      padding: 0 10px;
      box-sizing: border-box;
      background-color: #fff;
      text-indent: 2em;
      word-break: break-all;
      font-size: calc(14px * var(--font-scale))!important
    }
  }
}

.fold {
  display: none;
}
</style>
