<template>
  <div class="lists">
    <div class="weui-cells weui-cells_checkbox sort_box">
      <div
        class="list sort_list"
        ref="lists"
        v-for="(item,index) of lists"
        :key="index"
        :class="{'sort_letter_ios':bottomDistance}"
      >
        <label
          class="weui-cell weui-check__label"
          :for="index"
        >
          <div class="weui-cell__hd">
            <input
              type="checkbox"
              class="weui-check"
              name="checkbox1"
              :value="item.designatePersonCode"
              v-model="item.checked"
              :id="index"
              :checked="checkedInput(item)"
            />
            <i class="weui-icon-checked"></i>
          </div>
          <div class="weui-cell__bd">
            <p class="num_name">{{item.designatePersonName}}</p>
          </div>
        </label>
      </div>
    </div>
    <div class="initials">
      <ul></ul>
    </div>
    <div class="person-footer weui-cells_checkbox">
      <div class="person-footer-left list">
        <label
          class="weui-cell weui-check__label"
          for="all-select"
        >
          <div class="weui-cell__hd">
            <input
              type="checkbox"
              name="all-checkbox"
              :checked="checkedInput(allSelect)"
              v-model="allSelect"
              class="weui-check"
              id="all-select"
            />
            <i class="weui-icon-checked"></i>
          </div>
          <div class="weui-cell__bd">
            <p>全选</p>
          </div>
        </label>
      </div>
      <div
        class="person-footer-right finished-btn"
        @click="handleSubmitPeople"
      >
        <button class="weui-btn weui-btn_primary btn">
          <span>确定</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
// 属性重复
import { mapState, mapMutations } from "vuex";
import global from "@/utils/Global";
export default {
  name: "DesignatePerson",
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  props: ["designateDeptCode"],
  data() {
    return {
      lists: [],
      flag: false,
      checkedNames: [],
      allSelect: false,
      selectedCheckboxs: [],
      paramsRoute: {},
      bottomDistance: false
    };
  },
  watch: {
    allSelect(value, oldVal) {
      this.lists.forEach(function(item) {
        item.checked = value;
      });
    }
  },
  methods: {
    checkedInput(item) {
      if (item.checked) return item.checked;
      else return item;
    },
    /**
     * 获取指派人员列表
     */
    handleDesignatePersonnel() {
      $.showLoading("加载中…");
      this.$api
        .getDesignatePersonList({
          id: this.designateDeptCode
        })
        .then(this.getDesignatePersonnelSucc);
    },
    /**
     * 获取指派人员成功函数
     * @param responed
     */
    getDesignatePersonnelSucc(res) {
      $.hideLoading();
      const responed = res.list
      if (responed.length == 0) {
        $.toast("当前班组没有职工", "text");
        setTimeout(() => {
          window.teamInfo = {};
          this.$router.go(-1);
        }, 1500);
      }
      responed.forEach(item => {
        item.checked = false
        item.designatePersonName = item.member_name
        item.designatePersonCode = item.id
        item.designatePersonPhone = item.phone
      });
      this.lists = responed;
      global.pyList();
    },
    /**
     * 提交所选指派人员
     */
    handleSubmitPeople() {
      this.selectedCheckboxs = [];
      this.lists.forEach(items => {
        if (items.checked) {
          this.selectedCheckboxs.push(items);
        }
      });
      if (this.selectedCheckboxs.length == 0) {
        $.toast("请选择人员", "text");
        return;
      }
      this.$emit("backPersonnel", this.selectedCheckboxs);
    },
    /**
     * 判断手机终端（安卓、ios）
     */
    downApp() {
      if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
        //Ios
        this.bottomDistance = true;
      } else if (/(Android)/i.test(navigator.userAgent)) {
        //Android终端
      }
    },
    ...mapMutations(["addDesignatePersonnel"])
  },
  mounted() {
    this.downApp();
    this.paramsRoute = this.$route.query;
    this.handleDesignatePersonnel();
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';

.lists >>> .sort_letter {
  background-color: $bgColor;
  padding-left: 0.32rem;
  font-size: 14px;
}

.lists {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 66px;
  background-color: $bgColor;
  height: calc(100% - 66px);

  .list {
    height: 0.98rem;
    line-height: 0.98rem;
    background-color: #fff !important;
  }

  .weui-cell {
    padding-top: 0;
    padding-bottom: 0;
  }

  .weui-cells {
    margin: 0;
  }

  .sort_box {
    /* margin-bottom: 67px */
    height: 100%;
    overflow: auto;
  }
}

.person-footer {
  display: flex;
  height: 1.32rem;
  padding: 0.22rem 0.16rem;
  box-sizing: border-box;
  border-top: 1px solid $bgColor;
  background-color: #fff;
  width: 100%;
  position: fixed;
  bottom: 0;

  .person-footer-left {
    flex: 1;
  }

  .person-footer-right {
    flex: 2;
  }
}

.weui-cells:after {
  border: 0;
}

.initials {
  position: fixed;
  top: 47px;
  right: 0px;
  height: 100%;
  width: 15px;
  padding-right: 10px;
  text-align: center;
  font-size: 12px;
  z-index: 99;
  background: rgba(145, 145, 145, 0);
  display: flex;
  align-items: center;
}

.sort_list, .sort_letter {
  margin-left: -30px;
}

.sort_letter_ios {
  margin-left: -10px;
}
</style>
