<template>
  <div class="consumables-wrapper" v-if="isHaveConsumables">
    <div class="consumables-content">
      <div class="list" ref="lists" v-for="(item,index) of selectedConsumables" :key="index" >
        <div class="name">{{item.depThreeTypeName}}</div>
        <div class="count">
          <div class="coustom-number"> 
            <span class="iconfont less" @click="handleLessNumClick(index,item.depThreeTypeName)" v-if="showIcon">&#xe693;</span>
            <!-- <span class="num">{{item.num}}</span> -->
            <div class="number">
              <input
              class="weui-input "
              type="text"
              maxlength="4"
              oninput="value=value.replace(/[^\d]/g,'')"
              v-model="item.num"
              @blur="validateInput(index)"
              placeholder="" />
            </div>
              <!-- onKeyUp ="if(value == '')value=1" -->
            <span class="iconfont add" @click="handleAddNumClick(index)" v-if="showIcon">&#xe691;</span>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "EchoConsumables",
    props:['selectedConsumables','originalConsumables','showIcon','isAutoConsumables'],
    data () {
      return {
        isHaveConsumables:true
      }
    },
    methods: {
      /**
       * 点击减少耗材-
       * @param index
       */
      handleLessNumClick (index) {
        let num = this.selectedConsumables[index].num
        if (this.isAutoConsumables) {
          // 自动耗材模式：最小为0，不允许删除
          if (num > 0) {
            this.selectedConsumables[index].num = --num
          }
        } else {
          // 普通模式：最小为1
          if (num == 1 || num == '') {
            this.selectedConsumables.splice(index, 1)
          } else {
            this.selectedConsumables[index].num = --num
          }
        }
        
        if (this.selectedConsumables.length == 0) {
          this.isHaveConsumables = false
          this.$emit('selectTextColor')
        }
      },
      /**
       * 点击添加耗材+
       * @param index
       */
      handleAddNumClick (index) {
        let num = this.selectedConsumables[index].num
        const current = this.selectedConsumables[index]

        // 查找对应的原始耗材记录，获取最大值限制
        const original = this.originalConsumables && this.originalConsumables.find(item =>
          item.materialCode === current.depThreeTypeId
        );

        if (original) {
          const maxCount = original.operateCount
          // 检查是否超过最大值
          if (Number(num) >= Number(maxCount)) {
            // 已达到最大值，不允许继续增加
            $.toast(`${current.depThreeTypeName}数量不能超过${maxCount}`, "text");
            return;
          }
        }

        this.selectedConsumables[index].num = ++num
      },
      /**
       * 验证输入框输入的数量
       * @param index
       */
      validateInput(index) {
        const current = this.selectedConsumables[index]
        let inputNum = Number(current.num)

        // 如果输入为空或0，在自动耗材模式下允许为0
        if (!current.num || current.num === '') {
          this.selectedConsumables[index].num = 0
          return
        }

        // 查找对应的原始耗材记录，获取最大值限制
        const original = this.originalConsumables && this.originalConsumables.find(item =>
          item.materialCode === current.depThreeTypeId
        );

        if (original) {
          const maxCount = Number(original.operateCount)
          // 如果输入值超过最大值，自动调整为最大值
          if (inputNum > maxCount) {
            this.selectedConsumables[index].num = maxCount
            $.toast(`${current.depThreeTypeName}数量不能超过${maxCount}，已自动调整`, "text");
          } else if (inputNum < 0) {
            // 如果输入负数，调整为0
            this.selectedConsumables[index].num = 0
          }
        }
      },
    },
    watch: {
      selectedConsumables (curCon) {
        if (curCon) {
          if (curCon.lenght !=0) {
            this.isHaveConsumables = true
          }else{
            this.isHaveConsumables = false
          }
        }
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .coustom-number 
    display: flex;
    justify-content: flex-end;
    width: 55%;
    float: right;
  .number 
    width: 110px
    font-size: calc(15px * var(--font-scale))!important;
    color: #353535
    input 
      text-align: center 
  .consumables-wrapper
    marginBottom20()
    background: #fff
    overflow: hidden
    .consumables-content
      box-sizing: border-box
      border-radius: 5px
      overflow: hidden
      margin: .32rem
      .list
        display: flex
        justify-content: space-between
        itemBaseStyle()
        font-size: calc(16px * var(--font-scale))!important;
        background: #f5f6fb
        .name
          width: 3rem
          overflow: hidden
          text-overflow: ellipsis
          white-space: nowrap
        .count
          .less
            color: #d1d1d1
          .add
            color: $btnColor
          .num
            display: inline-block
            text-align: center
            width: 50px

    
  
</style>