<template>
  <!-- 精简工单 -->
  <div class="container" :class="{ hideSimplifyWork: hideSimplifyWork }">
    <div
      class="weui-cell item border-rightbottom"
      v-if="SimplifyWorkOrdeInfoDate.sysForShort && SimplifyWorkOrdeInfoDate.sysForShort.length > 0 && SimplifyWorkOrdeInfoDate.sysForShort != 'icms'"
    >
      <div class="weui-cell__hd">
        <label class="weui-label title">报警ID</label>
      </div>
      <div class="weui-cell__bd content-css bj-id" @click="alarm">
        {{ SimplifyWorkOrdeInfoDate.sysForShort }}
      </div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">紧急程度</label>
      </div>
      <div class="weui-cell__bd content-css" :style="urgencyDegreeColor(SimplifyWorkOrdeInfoDate.urgencyDegreeCode)">
        {{ SimplifyWorkOrdeInfoDate.urgencyDegree }}
      </div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">联系人</label>
      </div>
      <div class="weui-cell__bd content-css">
        {{ SimplifyWorkOrdeInfoDate.callerName }}
      </div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">电话</label>
      </div>
      <div class="weui-cell__bd content-css" style="color:#1aaff1" @click="makePhoneCall(SimplifyWorkOrdeInfoDate.sourcesPhone)">
        {{ SimplifyWorkOrdeInfoDate.sourcesPhone }}
      </div>
    </div>

    <!-- <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">所属科室</label>
      </div>
      <div class="weui-cell__bd content-css">{{SimplifyWorkOrdeInfoDate.sourcesDeptName}}</div>
    </div> -->

    <div class="weui-cell item border-rightbottom" v-if="!(SimplifyWorkOrdeInfoDate.appointmentType!='预约'&& SimplifyWorkOrdeInfoDate.urgencyDegree=='一般')">
      <div class="weui-cell__hd">
        <label class="weui-label title">服务时间</label>
      </div>
      <div class="weui-cell__bd content-css" v-if="SimplifyWorkOrdeInfoDate.appointmentDate == '0'">立刻</div>
      <div class="weui-cell__bd content-css" style="color:red" v-else>{{ time || "" }}</div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">要求完工时间</label>
      </div>
      <div class="weui-cell__bd content-css" v-if="SimplifyWorkOrdeInfoDate.requireAccomplishDate">
        {{ moment(SimplifyWorkOrdeInfoDate.requireAccomplishDate).format("YYYY.MM.DD HH:mm:ss") }}
      </div>
    </div>
    <div
      v-if="
        SimplifyWorkOrdeInfoDate.workTypeCode == 1 ||
        SimplifyWorkOrdeInfoDate.workTypeCode == 2 ||
        SimplifyWorkOrdeInfoDate.workTypeCode == 6 ||
        (SimplifyWorkOrdeInfoDate.workTypeCode && SimplifyWorkOrdeInfoDate.workTypeCode.length > 2 && SimplifyWorkOrdeInfoDate.template == 6)
      "
    >
      <!--保洁和维修和综合服务  和自定义工单综合类-->
      <div v-for="(item, index) of SimplifyWorkOrdeInfoDate.itemType" :key="index">
        <div class="weui-cell item border-rightbottom border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务地点</label>
          </div>
          <div class="weui-cell__bd content-css row-css">
            {{ item.localtion }}
          </div>
        </div>
        <div class="weui-cell item border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">所属科室</label>
          </div>
          <div class="weui-cell__bd content-css">
            {{ SimplifyWorkOrdeInfoDate.sourcesDeptName }}
          </div>
        </div>

        <div class="weui-cell item border-rightbottom border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务事项</label>
          </div>
          <div
            class="weui-cell__bd content-css questionDetail"
            v-if="
              SimplifyWorkOrdeInfoDate.workTypeCode == 1 ||
              SimplifyWorkOrdeInfoDate.workTypeCode == 2 ||
              (SimplifyWorkOrdeInfoDate.workTypeCode && SimplifyWorkOrdeInfoDate.workTypeCode.length > 2)
            "
          >
            {{ serviceMatters }}
          </div>
          <div class="weui-cell__bd content-css questionDetail" v-else>
            {{ SimplifyWorkOrdeInfoDate.transportName }}
          </div>
        </div>
        <!-- 自定义工单综合类 数量 -->
        <div class="weui-cell item border-rightbottom" v-if="SimplifyWorkOrdeInfoDate.workTypeCode && SimplifyWorkOrdeInfoDate.workTypeCode.length > 2">
          <div class="weui-cell__hd">
            <label class="weui-label title">数量</label>
          </div>
          <div class="weui-cell__bd content-css">
            {{ SimplifyWorkOrdeInfoDate.customtransportNum }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="SimplifyWorkOrdeInfoDate.workTypeCode == 10">
      <div v-for="(item, index) of SimplifyWorkOrdeInfoDate.itemType" :key="index">
        <div class="weui-cell item border-rightbottom border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务地点</label>
          </div>
          <div class="weui-cell__bd content-css row-css">
            {{ item.localtion }}
          </div>
        </div>
      </div>
    </div>
    <!--运输  自定义工单运送类-->
    <div
      v-if="
        SimplifyWorkOrdeInfoDate.workTypeCode == 3 ||
        (SimplifyWorkOrdeInfoDate.workTypeCode && SimplifyWorkOrdeInfoDate.workTypeCode.length > 2 && SimplifyWorkOrdeInfoDate.template == 3)
      "
    >
      <!-- 运输类 服务事项 -->
      <div v-if="SimplifyWorkOrdeInfoDate.workTypeCode == 3" class="weui-cell item border-rightbottom border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd questionDetail content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.transportName }}
        </div>
      </div>
      <!-- 自定义工单运送类 服务事项 -->
      <div v-else class="weui-cell item border-rightbottom border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd questionDetail content-css row-css">
          {{ serviceMatters }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">携带工具</label>
        </div>
        <div class="weui-cell__bd questionDetail content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.carryTools }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">需用人数</label>
        </div>
        <div class="weui-cell__bd questionDetail content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.needNum }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务起点</label>
        </div>
        <div class="weui-cell__bd content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.transportStartLocal }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">起点科室</label>
        </div>
        <div class="weui-cell__bd content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.transportStartLocalOffice }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务终点</label>
        </div>
        <div class="weui-cell__bd content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.transportEndLocal }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">终点科室</label>
        </div>
        <div class="weui-cell__bd content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.transportEndLocalOffice }}
        </div>
      </div>
      <!-- <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">接收人</label></div>
            <div class="weui-cell__bd content-css row-css">{{SimplifyWorkOrdeInfoDate.recipientPersonName}}</div>
      </div>-->
      <!-- <div class="weui-cell item border-rightbottom">
            <div class="weui-cell__hd"><label class="weui-label title">接收人电话</label></div>
            <div class="weui-cell__bd content-css row-css">{{SimplifyWorkOrdeInfoDate.recipientPersonPhone}}</div>
      </div>-->
    </div>
    <!--公车预定-->
    <div v-if="SimplifyWorkOrdeInfoDate.workTypeCode == 15">
      <div class="weui-cell item border-rightbottom border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd questionDetail content-css row-css">公车预定</div>
      </div>
      <div class="weui-cell item border-rightbottom border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">车辆信息</label>
        </div>
        <div class="weui-cell__bd questionDetail content-css row-css"></div>
      </div>
      <div class="tableCar" v-if="SimplifyWorkOrdeInfoDate.carList && SimplifyWorkOrdeInfoDate.carList.length > 0">
        <table>
          <tr>
            <th>车辆品牌</th>
            <th>车牌号</th>
            <th>颜色</th>
            <th>座位数</th>
          </tr>
          <tr v-for="(item, ii) in SimplifyWorkOrdeInfoDate.carList" :key="ii">
            <td>{{ item.brand }}</td>
            <td>{{ item.number }}</td>
            <td>{{ item.colour }}</td>
            <td>{{ item.seatsNum }}</td>
          </tr>
        </table>
      </div>
      <div class="weui-cell item border-rightbottom border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">开始时间</label>
        </div>
        <div class="weui-cell__bd questionDetail content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.startDate | dateFilter }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">结束时间</label>
        </div>
        <div class="weui-cell__bd content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.endDate | dateFilter }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">出发地</label>
        </div>
        <div class="weui-cell__bd content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.transportStartLocal }}
        </div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">目的地</label>
        </div>
        <div class="weui-cell__bd content-css row-css">
          {{ SimplifyWorkOrdeInfoDate.transportEndLocal }}
        </div>
      </div>
      <!-- <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">终点科室</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{SimplifyWorkOrdeInfoDate.transportEndLocalOffice}}</div>
      </div> -->
    </div>
    <div class="weui-cell item border-rightbottom" v-if="SimplifyWorkOrdeInfoDate.flowCode == 2">
      <div class="weui-cell__hd">
        <label class="weui-label title">服务人员</label>
      </div>
      <div class="weui-cell__bd content-css">
        {{ SimplifyWorkOrdeInfoDate.designatePersonName }}
      </div>
    </div>
    <div class="weui-cell item border-rightbottom" v-if="SimplifyWorkOrdeInfoDate.relevanceWorkNum">
      <div class="weui-cell__hd">
        <label class="weui-label title">数量</label>
      </div>
      <div class="weui-cell__bd content-css">
        {{ SimplifyWorkOrdeInfoDate.relevanceWorkNum }}
      </div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">申报描述</label>
      </div>
      <div class="weui-cell__bd content-css">
        {{ SimplifyWorkOrdeInfoDate.questionDescription }}
      </div>
    </div>
    <!-- 录音 -->
    <!-- pc端配置不显示的时候录音隐藏   ||   公车预定不需要录音-->
    <div class="voice-desc" v-if="SimplifyWorkOrdeInfoDate.isShowCallerTape == 'true' && SimplifyWorkOrdeInfoDate.workTypeCode != 15">
      <span class="text-title title">录音</span>
      <div v-if="SimplifyWorkOrdeInfoDate.callerTapeUrl == ''" class="content-css"></div>
      <div v-else class="voice-content">
        <div v-if="isCallerTapeUrl" class="voice-btn">
          <button class="weui-btn weui-btn_primary" v-if="play" @click="handlePlayAudioClick">
            <div class="play-icon">
              <img src="~images/icon_wify2.png" class="play-img" />
              <span>{{ audioDuration }}</span>
            </div>
            点击播放
          </button>
          <button class="weui-btn weui-btn_primary" v-else @click="handlePauseAudioClick">
            <div class="play-icon">
              <img src="~images/icon_wify1.gif" class="play-img" />
              <span>{{ audioDuration }}</span>
            </div>
            正在播放
          </button>
        </div>
      </div>
    </div>
    <!--附件-->
    <div class="img-desc border-rightbottom remove-line" v-if="SimplifyWorkOrdeInfoDate.workTypeCode != 15">
      <!--<div class="text-title title">图片</div>-->
      <div class="img-desc-content">
        <span class="text-title title">图片</span>
        <span v-if="SimplifyWorkOrdeInfoDate.attachment && SimplifyWorkOrdeInfoDate.attachment.length == 0" class="content-css"></span>
      </div>
      <div class="img-content">
        <div class="img-wrapper" v-for="ele of SimplifyWorkOrdeInfoDate.attachment" :key="ele">
          <div class="img-box">
            <img
              v-preview="$YBS.imgUrlTranslation(ele)"
              :src="$YBS.imgUrlTranslation(ele)"
              class="img"
              preview-title-enable="true"
              preview-nav-enable="true"
              preview-top-title-tnable="true"
              preview-title-extend="false"
            />
          </div>
        </div>
      </div>
    </div>
    <audio class="audio" controls ref="audio" @canplay="handleCanPlay" @ended="handleAudioEnded" :src="$YBS.imgUrlTranslation(SimplifyWorkOrdeInfoDate.callerTapeUrl)"></audio>
  </div>
</template>

<script>
//20200114 和贾健沟通 - 所有 图片附件 - 语音附件改为 图片 和 录音
import global from "@/utils/Global.js";
import moment from "moment";
export default {
  name: "SimplifyWork",
  props: {
    workOrdeInfoDate: {
      type: Array,
      default: function () {
        return [];
      }
    },
    hideSimplifyWork: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moment,
      isCallerTapeUrl: true,
      play: true,
      audioDuration: "",
      appointmentDate: ""
    };
  },
  filters: {
    dateFilter(msg) {
      return moment(msg).format("YYYY-MM-DD HH:mm");
    }
  },
  mounted() {
    console.log(this.SimplifyWorkOrdeInfoDate);
  },
  computed: {
    SimplifyWorkOrdeInfoDate() {
      return { ...this.workOrdeInfoDate[0], ...this.workOrdeInfoDate[1] };
    },
    time() {
      if (this.SimplifyWorkOrdeInfoDate.appointmentDate) return global.timestampToTime(this.SimplifyWorkOrdeInfoDate.appointmentDate, ".");
      else return;
    },
    serviceMatters() {
      let matter = "";
      let itemType = this.SimplifyWorkOrdeInfoDate.itemType[0];
      if (itemType.itemServiceCode) {
        matter = itemType.itemTypeName + "-" + itemType.itemDetailName + "-" + itemType.itemServiceName;
      } else if (itemType.itemDetailCode) {
        matter = itemType.itemTypeName + "-" + itemType.itemDetailName;
      } else {
        matter = itemType.itemTypeName;
      }
      return matter;
    }
  },
  methods: {
     //点击电话号码拨打电话
     makePhoneCall(phoneNumber) {
      // 创建一个 a 元素
      const link = document.createElement('a');
      // 设置 href 属性为 tel 协议链接
      link.href = `tel:${phoneNumber}`;
      // 触发点击事件
      link.click();
    },
    urgencyDegreeColor(val) {
      return {
        color: ["1", "0"].includes(val) ? "#FF0000" : ""
      };
    },
    handlePlayAudioClick() {
      this.play = false;
      const audio = this.$refs.audio;
      audio.play();
    },
    handleCanPlay() {
      this.audioDuration = Math.round(this.$refs.audio.duration) + "″";
    },
    /**
     * 暂停播放音频
     */
    handlePauseAudioClick() {
      this.play = true;
    },
    /**
     *音频播放结束事件
     */
    handleAudioEnded() {
      this.play = true;
    },
    alarm() {
      this.$parent.goAlarmDetails();
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.bj-id {
  text-decoration: underline;
  color: #3562db !important;
}

.tableCar {
  width: calc(100% - 32px);
  margin: 0 16px;
  box-sizing: border-box;
  overflow-x: scroll;

  >table {
    // width calc(100% - 16px)
    text-align: center;

    th, td {
      font-size: 0.32rem;
      border: 1px solid #eee;
      padding: 15px 15px;
      white-space: nowrap;
    }

    th {
      background: #E8ECFF;
    }
  }
}

.container {
  .item {
    itemBaseStyle();
    font-size: calc(16px * var(--font-scale))!important
  }
}

.hideSimplifyWork {
  display: none;
}

.voice-desc {
  display: flex;
  align-items: center;
  padding: 0.54rem 0.32rem;
  background-color: #fff;
  font-size: calc(16px * var(--font-scale))!important

  .text-title {
    width: 105px;
  }

  button {
    display: flex;
    align-items: center;
    justify-content: center;

    .play-icon {
      display: flex;
      position: absolute;
      align-items: center;
      left: 0;
      justify-content: center;
      padding-left: 5px;

      .play-img {
        width: 0.4rem;
      }

      span {
        font-size: 14px;
        padding-left: 5px;
      }
    }
  }
}

.voice-content {
  width: 100%;

  .voice-btn {
    flex: 1;

    .weui-btn_primary {
      line-height: 0.78rem;
    }
  }
}

.content-css {
  color: $contentColor;
  line-height: 1.2!important;
  display: flex;
  align-items: center;
  white-space: normal;
  word-break: break-word;
  word-wrap: break-word;
  font-size: calc(16px * var(--font-scale))!important
}

.img-desc {
  background-color: #fff;
  font-size: 0.32rem;

  .img-desc-content {
    display: flex;
    justify-content: start;
    align-items: center;

    .text-title {
      display: inline-block;
      line-height: 1rem;
      padding-left: 0.32rem;
      width: 105px;
      font-size: calc(16px * var(--font-scale))!important
    }

    .content-css {
      display: inline;
    }
  }

  .img-content {
    background: #fff;
    padding: 0 0.3rem;
    display: flex;

    .img-wrapper {
      width: 30%;
      height: 1.4rem;
      margin: 0.1rem;
      position: relative;

      .img-box {
        height: 100%;
        overflow: hidden;

        .img {
          width: 100%;
        }
      }
    }
  }
}

.audio {
  display: none;
}

.remove-line:after {
  border-bottom: none !important;
}
</style>
