<template>
  <div id="wrapper">
    <div class="title">
      <hr class="line" />
      <span class="title-content">手写签字</span>
    </div>

    <div class="canvas-wrapper">
      <canvas id="canvas"></canvas>
    </div>

    <div class="btns">
      <button class="rewrite-btn" ref="clearCanvas" @click="clear">重新书写</button>
      <button class="sure-btn" ref="saveCanvas" @click="save">确认</button>
    </div>
  </div>
</template>

<script>
import fontSizeMixin from "@/mixins/fontSizeMixin";
let flag = 0;
let draw, width, height;
let preHandler = function (e) {
  e.preventDefault();
};
width = document.documentElement.clientWidth - 40;
height = document.documentElement.clientHeight * 0.5;
class Draw {
  constructor(el) {
    this.el = el;
    this.canvas = document.getElementById(this.el);
    this.cxt = this.canvas.getContext("2d");
    this.canvas.width = width;
    this.canvas.height = height;
    this.stage_info = canvas.getBoundingClientRect();
    this.showBackground = false;
    this.path = {
      beginX: 0,
      beginY: 0,
      endX: 0,
      endY: 0
    };
    // 添加文字样式配置
    this.textConfig = {
      font: "14px Arial",
      color: "#A8A8A8",
      padding: 10
    };
  }
  // 添加绘制文字的方法
  drawText(workNum, actualArr, sourcesDeptName) {
    // 如果不显示背景信息，直接返回
    if (!this.showBackground) return;
    this.cxt.font = this.textConfig.font;
    this.cxt.fillStyle = this.textConfig.color;

    let y = this.textConfig.padding;
    // 始终显示工单号字段
    this.cxt.fillText(`工单号：${workNum || ""}`, this.textConfig.padding, y + 20);
    y += 25;

    // 始终显示科室字段
    this.cxt.fillText(`所属科室：${sourcesDeptName || ""}`, this.textConfig.padding, y + 20);
    y += 25;

    // 始终显示耗材字段
    this.cxt.fillText("耗材：", this.textConfig.padding, y + 20);
    if (actualArr && actualArr.length) {
      actualArr.forEach((item, index) => {
        this.cxt.fillText(item, this.textConfig.padding + 50, y + 20);
        y += 25;
      });
    }
  }
  init(workNum, actualArr, sourcesDeptName, showBackground) {
    this.workNum = workNum;
    this.actualArr = actualArr;
    this.sourcesDeptName = sourcesDeptName;
    this.showBackground = showBackground;
    let that = this;

    this.canvas.addEventListener("touchstart", function (event) {
      document.addEventListener("touchstart", preHandler, true);
      that.drawBegin(event);
    });
    this.canvas.addEventListener("touchend", function (event) {
      document.addEventListener("touchend", preHandler, true);
      that.drawEnd();
    });
    this.clear();
    this.drawText(workNum, actualArr, sourcesDeptName);
  }
  isDrawFlag() {
    return flag;
  }
  drawBegin(e) {
    flag = 1;
    let that = this;
    window.getSelection() ? window.getSelection().removeAllRanges() : document.selection.empty();
    this.cxt.strokeStyle = "#000";

    this.cxt.beginPath();
    this.cxt.moveTo(e.changedTouches[0].clientX - this.stage_info.left, e.changedTouches[0].clientY - this.stage_info.top);
    this.path.beginX = e.changedTouches[0].clientX - this.stage_info.left;
    this.path.beginY = e.changedTouches[0].clientY - this.stage_info.top;
    canvas.addEventListener("touchmove", function () {
      that.drawing(event);
    });
  }
  drawing(e) {
    this.cxt.lineTo(e.changedTouches[0].clientX - this.stage_info.left, e.changedTouches[0].clientY - this.stage_info.top);
    this.path.endX = e.changedTouches[0].clientX - this.stage_info.left;
    this.path.endY = e.changedTouches[0].clientY - this.stage_info.top;
    this.cxt.stroke();
  }
  drawEnd() {
    document.removeEventListener("touchstart", preHandler, true);
    document.removeEventListener("touchend", preHandler, true);
    document.removeEventListener("touchmove", preHandler, true);
  }
  clear() {
    this.cxt.clearRect(0, 0, width, height);
    flag = 0;
    // 清除后重新绘制文字
    this.drawText(this.workNum, this.actualArr, this.sourcesDeptName);
  }
  //保存图片并添加背景色
  save(backgroundColor) {
    var w = canvas.width;
    var h = canvas.height;
    var data;
    if (backgroundColor) {
      let context = canvas.getContext("2d");
      data = context.getImageData(0, 0, w, h);
      var compositeOperation = context.globalCompositeOperation;
      context.globalCompositeOperation = "destination-over";
      context.fillStyle = backgroundColor;
      context.fillRect(0, 0, w, h);
    }
    var imageData = this.canvas.toDataURL("image/png");
    if (backgroundColor) {
      let context = canvas.getContext("2d");
      context.clearRect(0, 0, w, h);
      context.putImageData(data, 0, 0);
      context.globalCompositeOperation = compositeOperation;
    }

    //返回Base64编码数据url字符串
    return imageData;
  }
}
export default {
  mixins: [fontSizeMixin],
  name: "SignaturePage",
  props: {
    actualArr: {
      type: Array,
      default: () => []
    },
    workNum: {
      type: String,
      default: ""
    },
    sourcesDeptName: {
      type: String,
      default: ""
    },
    operationSignatureOrderInfo: {
      type: String,
      default: ""
    }
  },
  mounted() {
    draw = new Draw("canvas");
    draw.init(this.workNum, this.actualArr, this.sourcesDeptName, this.operationSignatureOrderInfo == '1');
    //解决微信浏览器下拉露底的问题
    document.getElementById("wrapper").addEventListener(
      "touchmove",
      e => {
        e.preventDefault();
      },
      { passive: false }
    );
  },
  methods: {
    /**
     * 清空画布
     */
    clear: function () {
      draw.clear();
    },
    /**
     * 保存图片
     */
    save: function () {
      let drawFlag = draw.isDrawFlag();
      if (drawFlag == 1) {
        //在画布上绘画了
        let data = draw.save("#fff");
        this.$emit("saveImg", data);
      }
      if (drawFlag == 0) {
        //没在画布上绘画
        this.$emit("saveImg", "");
      }
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl'
@import '~styles/mixins.styl'
#wrapper
  background-color $bgColor
  height calc(100% - 3rem)
  .title
    width 100%
    height 26px
    display flex
    justify-content center
    align-items center
    .line
      width 120px
    .title-content
      position absolute
      padding 0 7px
      background $bgColor
      font-size 14px
      color $textColor
  .canvas-wrapper
    display flex
    justify-content center
    #canvas
      background-color #fff
      border-radius 5px
  .btns
    width 100%
    text-align right
    position fixed
    bottom 0
    right 0
    itemBaseStyle()
    button
      width 104px
      height 35px
      font-size calc(16px * var(--font-scale))
      font-family unset
    .rewrite-btn
      background-color transparent
      color $color
    .sure-btn
      background-color $color
      color #fff
      border-radius 5px
</style>
