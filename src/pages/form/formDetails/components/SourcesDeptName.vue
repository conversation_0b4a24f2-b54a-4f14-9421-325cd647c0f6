<template>
  <div class="department">
    <Header title="选择科室" @backFun="goback"></Header>
    <div class="department-search">
      <div class="search-ipt" v-show="!query">
        <span class="iconfont">&#xe60a;</span>
        <input v-model="officeName" class="search" type="text" maxlength="50" placeholder="请输入关键字搜索" />
        <span class="line"></span>
        <span class="cancle" @click="switchto">取消</span>
      </div>

      <div class="info" v-if="skip == 2">
        <div class="st" @click="switchto" v-show="query">
          <span class="iconfont">&#xe60a;</span>
          <span class="search-text">请输入关键字搜索</span>
        </div>
        <ul class="content-list" v-if="query">
          <li class="item" v-for="(item, index) of officeNameList" :key="index" @click="goToRevise(item, 3)">
            {{ item.deptName }}
          </li>
        </ul>
        <ul class="content-list" v-else>
          <li class="item" v-for="(item, index) of setofficeNameList" :key="index" @click="goToRevise(item, 3)">
            {{ item.deptName }}
          </li>
        </ul>
      </div>
      <div class="info" v-if="skip == 1">
        <div class="search-tap st" v-show="query">
          <span class="tapone" @click="switchto(1)">
            <span class="iconfont">&#xe60a;</span>
            <span class="search-text">请输入关键字搜索</span>
          </span>
          <!-- <span class="taptwo" @click="switchto(2)">
            <span class="borleft"></span>
            <span class="text">科室不对</span>
          </span> -->
        </div>
        <ul class="content-list" v-show="query">
          <li class="item" v-for="(item, index) of officeNameList" :key="index" @click="goToRevise(item, 3)">
            {{ item.name }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import axios from "axios";
import { mapState } from "vuex";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "deptName",
  data() {
    return {
      skip: 1,
      query: true,
      officeName: "", //查询科室
      officeNameList: [], //科室列表
      setofficeNameList: [], //搜索科室列表
      selectedName: {}, //选中的科室
      areaVal: [],
      type: "",
      serviceid: "",
      level: "", //为2时是运送的终点
    };
  },
  mixins: [fontSizeMixin],
  computed: {
    ...mapState(["loginInfo", "staffInfo"]),
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    switchto(i) {
      if (i == 2) {
        this.skip = 2;
        this.getDeptName(3);
      } else {
        this.query = !this.query;
        this.officeName = "";
      }
    },
    getDeptName(type, officeId, localtionId, flag) {
      // this.axios
      //   .post(
      //     __PATH.ONESTOP +
      //       "/appOlgTaskManagement/getNameOffice",
      //     this.$qs.stringify({
      //       unitCode: this.loginInfo.userOffice[0].unitCode,
      //       hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
      //       type:type,
      //       officeId:officeId,  //type为1时必填  科室id
      //       localtionId:localtionId  //type为2的时候必填 地点id
      //     })
      axios
        .post(
          __PATH.BASE_API + "/departmentManager/department-manager/selectByPage",
          {
            current: 1,
            size: 999,
          },
          {
            headers: {
              unitCode: this.loginInfo.unitCode,
              hospitalCode: this.loginInfo.hospitalCode,
              Authorization: "Bearer " + localStorage.getItem("token"),
            },
          }
        )
        .then((res) => {
          res = res.data.data.records;
          res.forEach((item) => {
            item.name = item.deptName;
          });
          // if(type==3){
          this.officeNameList = res;
          // }else if(type==1){
          //   this.areaVal=res;
          // }else if(type==2){
          //   this.officeNameList=res;
          if (res.length == 0) {
            this.switchto(2);
          }
          // }
          if (flag) {
            this.go(1);
          }
        });
    },
    goToRevise(item, type) {
      console.log("item", item);
      if (type == 1) {
        this.selectedName = item;
        this.getDeptName(1, item.id, "", true);
      } else if (type == 3) {
        this.selectedName = item;
        this.go(3);
      } else if (type == 2) {
        if (item.length > 0) {
          for (let i = 0; i < item.length; i++) {
            // 筛选符合条件的地点  gridLevel>2
            if (item[i].gridLevel <= 2) {
              item.splice(i, 1);
            }
          }
        }
        this.selectedName = item;
        this.go(2);
      }
    },
    /**
     * 返回上级页面并带回数据
     */
    go(type) {
      this.$router.push({
        path: "/reviseOder",
        query: {
          workTypeCode: this.$route.query.workTypeCode,
          path: "deptName",
          areaVal: this.areaVal, //带出的关联地点
          selectedName: this.selectedName, //带出的科室
          type: type,
          level: this.level,
          id: this.$route.query.id,
        },
      });
    },
  },
  mounted() {
    this.type = this.$route.query.type;
    this.level = this.$route.query.level;
    // if(this.$route.query.type==1 ){
    //   // type= 1 展示所有科室,带出关联地点
    //   this.skip=1
    //   this.getDeptName(3)
    // }else if(this.$route.query.type==3){
    //   // type= 3 展示所有科室
    //   this.skip=2
    this.getDeptName(3);
    // }else if(this.$route.query.type==2){
    //   //type= 2  展示相关科室,不带出地点
    //   this.serviceid=this.$route.query.serviceSiteResId[0]
    //   this.getDeptName(2,"",this.serviceid)
    // }
  },
  components: {},
  watch: {
    officeName() {
      if (!this.officeName) {
        this.setofficeNameList = [];
        this.query = false;
        return;
      }
      this.skip = 2;
      // this.axios
      //   .post(
      //     __PATH.ONESTOP +
      //       "/appOlgTaskManagement/getNameOffice",
      //     this.$qs.stringify({
      //       unitCode: this.loginInfo.userOffice[0].unitCode,
      //       hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
      //       type:3,
      //       officeName:this.officeName
      //     })
      axios
        .post(
          __PATH.BASE_API + "/departmentManager/department-manager/selectByPage",
          {
            current: 1,
            size: 999,
            deptName: this.officeName,
          },
          {
            headers: {
              unitCode: this.loginInfo.unitCode,
              hospitalCode: this.loginInfo.hospitalCode,
              Authorization: "Bearer " + localStorage.getItem("token"),
            },
          }
        )
        .then((res) => {
          res = res.data.data.records;
          res.forEach((item) => {
            item.name = item.deptName;
          });
          this.setofficeNameList = res;
        });
    },
  },
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.department
  height :100vh;
  overflow : hidden
.info
  height :100vh;
  overflow : hidden
.content-list
  height :calc(100% - 1.2rem);
  overflow-y:auto;
  .item
    margin-bottom: 1px
    padding: 0px .3rem
    font-size: calc(16px * var(--font-scale))!important;
    border-bottom:1px solid #EFEFF4;
    background :#fff;
    padding:0.28rem 0.3rem;
    line-height :0.42rem;
    word-wrap:break-word;
    word-break:break-all;
.department-search
  width: 100%;
  height: 0.78rem;
  line-height: 0.78rem;
  padding: 0.2rem 0rem;
  background-color: #fff;
  border-bottom:1px solid #EFEFF4;
  .search-ipt
    position: relative;
    .iconfont
      position: absolute;
      left: 0.4rem;
      top: 0rem;
      color: $textColor;
.search
  background-color: $bgColor;
  padding-left: 0.8rem;
  padding-right: 1.1rem;
  box-sizing: border-box;
  height: 0.78rem;
  display: inline-block;
  width: 95%;
  margin-left: 2.5%;
  border-radius: 5px;
.line
  width: 1px;
  height: 0.3rem;
  background-color: #D3D3D5;
  display: inline-block;
  position: absolute;
  right: 1.1rem;
  top: 0.24rem;
.cancle
  position: absolute;
  right: 7px;
  top: 0;
  width: 1rem;
  text-align: center;

.st
  width: 95%;
  height: 0.78rem;
  margin-left: 2.5%;
  box-sizing: border-box;
  padding-left: 0.28rem;
  padding-right: .28rem;
  border-radius: 5px;
  color:$textColor;
  font-size:14px;
  background:#EFEFF4;
.search-tap
  display:flex;
  justify-content :space-between;
  .taptwo
    .borleft
      display: inline-block;
      width: 1px;
      height: 0.3rem;
      background-color: #D3D3D5;
      margin-top:.2rem;
      // position: absolute;
      // right: 1.1rem;
      // top: 0.24rem;
    .text
      color:#38C7C4;
      font-family:PingFang SC;
      font-style:italic;
.item-color{
  background : #fcfcfc ;
}
.search-text{
  font-size:.28rem
}
</style>
