<template>
  <div class="completed-wrapper" :class="{'large-font': isLargeFont}">
    <add-personnel class="personnel" ref="addPersonnel" v-if="isHadDesP" :workOrderId="workOrderId" :workOrdeInfoDate="workOrdeInfoDate" :source="'complete'"></add-personnel>
    <div class="select" v-if="workTypeCode != 3 && workTypeCode != 6 && workType.indexOf(workTypeCode) >= 0">
      <!-- <van-field right-icon="arrow" v-model="equipmentName" label="关联设备" placeholder="请选择" @click="openEquipment" input-align="right" />
      <van-popup v-model="showEquipment" position="bottom">
        <van-cascader
          v-model="equipmentName"
          title="请选择设备"
          :options="equipmentList"
          @close="closeEquipment"
          @finish="onEquipment"
          :field-names="fieldNames"
          active-color="#3562db"
        />
      </van-popup> -->
      <div class="sub-title">故障维修</div>
      <div class="select-cause" v-if="newlist.length <= 0">
        <div class="cause-trouble" @click="goSelect(-1)">
          <label class="trouble-text">故障原因</label>
          <span class="trouble-ipt"></span>
          <div class="trouble-set trouble-new">
            <span class>请选择</span>
            <span class="iconfont trouble-icon">&#xe646;</span>
          </div>
        </div>
        <div class="cause-trouble">
          <label class="trouble-text">维修方法</label>
          <ul class="troulble-list">
            <li class="trouble-ipt trouble-info"></li>
          </ul>
        </div>
      </div>

      <div class="swipeout-touching select-cause" v-for="(val, i) of newlist" :key="i">
        <div class="weui-cell weui-cell_swiped swipeout-touching">
          <div class="weui-cell__bd">
            <div class="weui-cell">
              <div class="weui-cell__bd">
                <div class="cause-trouble">
                  <label class="trouble-text">故障原因{{ i + 1 }}</label>
                  <span class="trouble-ipt">{{ val.reasonName }}</span>
                  <div class="trouble-set" @click="goSelect(i)">
                    <span class>修改</span>
                    <span class="iconfont trouble-icon">&#xe646;</span>
                  </div>
                </div>
                <div class="cause-trouble">
                  <label class="trouble-text" for>维修方法</label>
                  <ul class="troulble-list">
                    <li v-for="(item, index) of newmain[i]" :key="index" class="trouble-info">
                      {{ item.methodName }} 
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="weui-cell__ft" @click="deleteSelect(i)">
            <a class="weui-swiped-btn weui-swiped-btn_warn delete-swipeout" href="javascript:">
              <img class="delete-img" src="@/assets/images/delete2.png" />
            </a>
          </div>
        </div>
      </div>

      <div class="add" @click="goSelectAdd(-1)">
        <img class="add-img" src="@/assets/images/btn-add.png" />
        <span>新增故障原因</span>
        <span class="add-text">(左滑可删除选项)</span>
      </div>
     
    </div>
   
    <div class="content-wrapper">
      <div class="weui-cell border-rightbottom item" v-if="noCons && workTypeCode != 15 && (isAutoConsumables && selectedComConsumables.length > 0)">
        <div class="weui-cell__hd">
          <label class="weui-label title">耗材实用</label>
        </div>
        <div class="weui-cell__bd disabled-text" @click="goConsumables" v-if="disabled && !isAutoConsumables">
          <span v-if="!showCompletedCon">
            <span>请选择</span>
            <span class="iconfont">&#xe646;</span>
          </span>
          <span :class="{ consumablesicon: select }" v-else>
            <span>置换耗材</span>
            <span class="iconfont">&#xe646;</span>
          </span>
        </div>
      </div>

      <!--耗材实用-->
      <echo-consumables :selectedConsumables="selectedComConsumables" :originalConsumables="originalConsumables" :showIcon="disabled" v-if="isAutoConsumables && selectedComConsumables.length > 0" @selectTextColor="handleSelectTextColor" :isAutoConsumables="isAutoConsumables"></echo-consumables>
      <!-- <pop-bottom :dataInfo="popData" @setVal="handleVal"></pop-bottom> -->
       
      <div class="m-b" v-if="noCons"></div>

      <div class="pendOrder-desc">
        <div class="weui-cell item">
          <div class="weui-cell__hd">
            <label class="weui-label title">完工说明</label>
          </div>
        </div>
        <div class="weui-cell__bd desc border-bottom">
          <textarea
            class="weui-textarea content-css"
            rows="4"
            maxlength="500"
            v-model="value"
            placeholder="请输入完工说明，字数限制500字以内"
            @blur="handleTextareaBlurEvent"
          ></textarea>
        </div>
      </div>
      <!--图片上传-->
      <upload-image style="position: relative; margin-left: -20px" ref="imgs" :captureOnly="true" @delImg="handleDelImg" @getImg="getImg" :isRequired="completeUploadImage"></upload-image>
      <div class="m-b"></div>

      <div class="weui-cell isEval-content">
        <div class="weui-cell__hd" v-if="!isEval">
          <label class="weui-label title">签字及评价</label>
        </div>

        <div class="weui-cell__bd disabled-text padding-right" @click="toGetSatisfaction" v-if="!isEval" :class="{ disabledClick: !disabled }">
          <span>未评价</span>
          <span class="iconfont">&#xe646;</span>
        </div>
        <div class="weui-cell__bd" v-else>
          <div class="weui-cell item" @click="toGetSatisfaction">
            <div class="weui-cell__hd">
              <label class="weui-label title">满意度评价</label>
            </div>
            <div class="weui-cell__bd imgs">
              <span>
                <img src="@/assets/images/on.png" alt v-for="(item, index) of starNum" :key="index" />
                <img src="@/assets/images/off.png" alt v-for="(item, index) of offStarNum" :key="5 - index" />
              </span>
              <span class="img-text content-css">{{ disDegreeText }}</span>
            </div>
          </div>
          <div class="weui-cell item" v-if="evaluationAdvice">
            <div class="weui-cell__hd">
              <label class="weui-label title">选择意见</label>
            </div>
            <div class="weui-cell__bd imgs">
              <span class="img-text content-css">{{ evaluationAdvice }}</span>
            </div>
          </div>
          <div class="weui-cell item">
            <div class="weui-cell__hd">
              <label class="weui-label title">描述</label>
            </div>
            <div class="weui-cell__bd imgs">
              <span class="img-text content-css">{{ evaluationExplain }}</span>
            </div>
          </div>
          <div class="signature-content" v-if="imgSrc">
            <img
              class="signature-img"
              v-preview="imgSrc"
              :src="imgSrc"
              preview-title-enable="true"
              preview-nav-enable="true"
              preview-top-title-tnable="true"
              preview-title-extend="false"
            />
          </div>
        </div>
      </div>
      <div class="m-b"></div>

      <!-- 总服务费 综合服务-自定义工单没有 -->
      <!-- 自定义工单运送类型显示综合服务 -->
      <div v-if="completePriceFlag == 'true'">
        <div class="cost" v-if="(workTypeCode != 6 && workType.indexOf(workTypeCode) >= 0) || template == '3'">
          <div class="cost-from">
            <label class="cost-text">总服务费</label>
            <input
              type="number"
              class="cost-val"
              placeholder="请输入总服务费"
              @input="oninput"
              @blur="handleTextareaBlurEvent"
              maxlength="5"
              oninput="if(value.length>10)value=value.slice(0,10)"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="pers-sign" v-if="isShowSign">
      <star-page ref="stars"></star-page>
      <div v-if="isShowIdeas" class="form-box">
        <van-field class="star" readonly clickable label="选择意见" :value="evaluationAdvice" placeholder="选择意见" @click="showPicker = true" />
        <van-field
          v-model="evaluationExplain"
          rows="2"
          autosize
          label="评价描述"
          type="textarea"
          maxlength="50"
          placeholder=""
          show-word-limit
        />
      </div>
      <van-popup v-model="showPicker" round position="bottom">
        <van-picker show-toolbar :columns="columns" @cancel="showPicker = false" @confirm="onConfirm" />
      </van-popup>
      <signature-page :key="componentKey" @saveImg="handleSign" :workNum="workNum" :sourcesDeptName="sourcesDeptName" :operationSignatureOrderInfo="operationSignatureOrderInfo"></signature-page>
    </div>
  </div>
</template>

<script>
import YBS from "@/assets/utils/utils.js";
import EchoConsumables from "./EchoConsumables";
import StarPage from "@/pages/form/newForm/components/Star";
import SignaturePage from "./Signature";
import PopBottom from "@/common/PopupBottom";
import AddPersonnel from "./AddDesignatePersonnel";
import { mapState } from "vuex";
import UploadImage from "@/common/uploadImg/uploadImg";
export default {
  name: "CompletedOder",
  props: [
    "workTypeCode",
    "inputInfo",
    "isPendingOder",
    "isConfirm",
    "showCompletedConList",
    "selectedComConsumables",
    "isHadDesP",
    "time",
    "workOrderId",
    "workOrdeInfoDate",
    "localtionId",
    "isAutoConsumables",
    "originalConsumables"
  ],
  components: {
    EchoConsumables,
    StarPage,
    SignaturePage,
    AddPersonnel,
    UploadImage,
  },
  computed: {
    offStarNum() {
      const offStar = 5 - this.starNum;
      return offStar;
    },
    isLargeFont() {
      return localStorage.getItem('fontSizePreference') === 'x-large' || localStorage.getItem('fontSizePreference') === 'large';
    }
  },
  created() {
    // console.log("path", this.$route.path);
    // setInterval(() => {
    //   console.log("@@@@@@@@@@@@@@@@@", this.newlist);
    // }, 1000);
    // this.getlistName();
  },
  data() {
    return {
       lastProcessedQuery: null,
      facilityType: "", //设备id
      facilityName: "", //设备Name
      equipmentName: "",
      showEquipment: false,
      fieldNames: {
        text: "name",
        value: "id",
        children: "children"
      },
      showRenewal:false,
      equipmentList: [],
      completeUploadImage: false, //完工照片是否必填
      value: "",
      disDegreeText: "",
      isHaveConsumables: true,
      disabled: true,
      select: true,
      showCompletedCon: true,
      isShowSign: false,
      imgSrc: "",
      starNum: "",
      isEval: false,
      noCons: true,
      failureAnalysis: {}, //选择故障分析
      maintenanceMethod: {}, //选择维修方法
      popData: {},
      completePrice: "", //总服务费
      newlist: [], //故障原因
      newmain: [], //维修方法
      i: -1,
      itemTypeCode: "",
      workType: ["1", "2", "3", "4", "5", "6",'17'],
      reason: [], //提交的故障原因/维修方法
      completePriceFlag: false, //是否录入总服务费
      amount: {
        //上传图片限制
        quantity: 9,
        capital: "九"
      },
      imagesParams: [],
      imageConfig: [], //处理图片的mediaId和ossUrl
      imgFlag: false,
      accessToken: "",
      template: "", //工单模版类型（3运送类；6综合类）
      configParams: {},
      isShowIdeas: "",
      evaluationAdvice: "",
      evaluationExplain: "",
      showPicker: false,
      columns: [],
      componentKey: 0,
      workNum: "",
      sourcesDeptName: "",
      operationSignatureOrderInfo: "",
      itemServiceCode:"",
      typelist:{},
      mainlist:[]
    };
  },

  methods: {
    getDictList() {
      const params = {
        type: "1",
        spaceId: "",
        status: "0"
      };
      // if (this.localtionId) {
      //   let localtionId = this.localtionId.split(",");
      //   params.spaceId = localtionId[localtionId.length - 1];
      // } else {
      //   params.spaceId = "";
      // }
      this.$api.getDeviceCountGroupedByFacilityType(params).then(res => {
        this.processOptions(res);
      });
    },
    //只保留三级数据
    processOptions(options) {
      this.equipmentList = options
        .filter((parent) => {
          return parent.children && parent.children.some((child) => child.children && child.children.length > 0)
        })
        .map((parent) => ({
          ...parent,
          children: parent.children
            .filter((child) => {
              return child.children && child.children.length > 0
            })
            .map((child) => ({
              ...child,
              children: child.children
            }))
        }))
    },
    closeEquipment() {
      this.equipmentName = "";
      this.showEquipment = false;
    },
    openEquipment() {
      this.showEquipment = true;
    },
    onEquipment(value) {
      // console.log(value,'============');
      const namesArray = value.selectedOptions.map(obj => obj.name);
      const typeArray = value.selectedOptions.map(obj => obj.id);
      this.equipmentName = namesArray.join("/");
      this.facilityName = namesArray.join(",");
      this.facilityType = typeArray.join(",");
      this.showEquipment = false;
    },
    reloadComponent() {
      this.componentKey++;
    },
    onConfirm(value) {
      this.evaluationAdvice = value.text;
      this.showPicker = false;
    },
    getConfig() {
      this.$api.getNewSysConfigParam({}).then(res => {
        res.olgWorkPushNew.completeUploadImage == 1 ? (this.completeUploadImage = true) : (this.completeUploadImage = false);
        this.configParams = res.olgWorkPushNew;
        this.operationSignatureOrderInfo = JSON.parse(res.configBody).operationSignatureOrderInfo;
        this.columns = JSON.parse(res.olgWorkPushNew.adviceDict)
          .filter(item => item.isChecked)
          .map(item => {
            return {
              text: item.typeName,
              value: item.typeCode
            };
          });
        // 给this.columns最后面加{text:'其他',value:'other'}
        this.columns.push({ text: "其他", value: "other" });
        if (
          this.$refs.stars && this.configParams.satisfactionConfigScore * this.$refs.stars.starIdx != 0 &&
          this.configParams.satisfactionConfigScore * this.$refs.stars.starIdx <= this.configParams.satisfactionConfigLimit
        ) {
          this.isShowIdeas = true;
          this.reloadComponent();
        } else {
          this.isShowIdeas = false;
        }
      });
    },
    getImg(img) {
      // console.log("img", img);
      this.imagesParams.push(img);
    },
    handleDelImg(imgUrl) {
      console.log('this.imagesParams删除前', this.imagesParams);
      console.log('handleDelImg',imgUrl);
      this.imagesParams = this.imagesParams.filter(url => url !== imgUrl);
      console.log('this.imagesParams删除后', this.imagesParams);
    },
    rea() {
      //  故障原因/维修方法提交处理
      this.reason = [];
      for (var i = 0; i < this.newmain.length; i++) {
        for (var j = 0; j < this.newmain[i].length; j++) {
          let aaa = {};
          let objass = Object.assign(aaa, this.newlist[i], this.newmain[i][j]);
          this.reason.push(objass);
        }
      }
    },
    // 总服务费验证
    oninput(e) {
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,2})/g)[0] || null;
      this.completePrice = e.target.value;
    },
    deleteSelect(i) {
      this.newlist.splice(i, 1);
      this.newmain.splice(i, 1);
      $(".weui-cell_swiped").swipeout("close");
    },
    goSelect(i) {
      $(".weui-cell_swiped").swipeout("close");
      this.i = i;
      this.$router.push({
        path: "/optionsTrouble",
        query: {
          // newlist:JSON.stringify(this.newlist),
          // newmain:JSON.stringify(this.newmain),
          // type:i==-1?'add':'edit',
          i: i,
          itemServiceCode: this.itemServiceCode
        }
      });
    },
    goSelectAdd(i){
       $(".weui-cell_swiped").swipeout("close");
      this.i = i;
      //  this.$router.push({
      //   path: "/optionsTrouble",
      //   query: {
      //     // newlist:JSON.stringify(this.newlist),
      //     // newmain:JSON.stringify(this.newmain),
      //     // type:i==-1?'add':'edit',
      //     itemServiceCode: this.itemServiceCode,
      //      i: i,
      //   }
      //   }).then(() => {
    this.$router.push({
       path: "/renewal",
        query: {
          // newlist:JSON.stringify(this.newlist),
          // newmain:JSON.stringify(this.newmain),
          // type:i==-1?'add':'edit',
          itemServiceCode: this.itemServiceCode,
           i: i,
        }
    });
// });;
    },
    selectAnalysisData() {
      this.popData = {
        flag: 2,
        data: [],
        title: "故障分析",
        nameAndCode: true
      };
      $("#education").popup();
    },
    selectMaintenanceMenthod() {
      this.popData = {
        flag: 1,
        data: [],
        title: "维修方法",
        nameAndCode: true
      };
      $("#education").popup();
    },
    handleVal(val) {
      // console.log(val);
    },
    /**
     * 跳转耗材实用
     */
    goConsumables() {
      this.$router.push({
        name: "Consumables",
        params: {
          workTypeCode: this.workTypeCode,
          lists: this.selectedComConsumables,
          parm: "completedOder",
          inputInfo: this.inputInfo,
          showConList: this.showConList,
          isPendingOder: this.isPendingOder,
          isConfirm: this.isConfirm,
          page: this.$route.query.page
        }
      });
    },
    /**
     * 置换耗材文字颜色更改
     */
    handleSelectTextColor() {
      this.select = false;
      this.showCompletedCon = false;
    },
    /**
     * 展示满意度评价
     */
    toGetSatisfaction() {
      /*if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
          //Ios
          window.scroll(0,0);
        }*/
      window.scroll(0, 0);
      let that = this;
      setTimeout(() => {
        that.isShowSign = true;
        this.getConfig();
        setTimeout(() => {
          this.$watch(
            () => this.$refs.stars.starIdx,
            (newVal, oldVal) => {
              // 在这里处理 starIdx 的变化
              // console.log("starIdx 变化了：", newVal);
              if (this.configParams.satisfactionConfigScore * newVal <= this.configParams.satisfactionConfigLimit) {
                this.isShowIdeas = true;
                this.reloadComponent();
              } else {
                this.isShowIdeas = true;
              }
            }
          );
        }, 100);
      }, 200);
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 签字处理
     * @param signUrl
     */
    handleSign(signUrl) {
      if (this.isShowIdeas && !this.evaluationAdvice) {
        $.toast("请选择意见", "text");
        return;
      }
      let starCount = this.$refs.stars.starIdx;
      if (starCount == "") {
        this.starNum = "";
      } else {
        this.starNum = +starCount;
      }
      if (this.starNum == 0) {
        $.toast("请进行满意度评价", "text");
        return;
      }
      this.imgSrc = signUrl;
      this.isShowSign = false;
      this.isEval = true;
      switch (this.starNum) {
        case 1:
          this.disDegreeText = "非常差";
          break;
        case 2:
          this.disDegreeText = "差";
          break;
        case 3:
          this.disDegreeText = "一般";
          break;
        case 4:
          this.disDegreeText = "满意";
          break;
        case 5:
          this.disDegreeText = "非常满意";
          break;
      }
    },
    // 接收故障原因参数
    getlistName() {
      console.log(this.$route.query,'路由路由路由路由路由路由');
      if (this.$route.query.source == "OptionsTrouble") {
        this.i = this.$route.query.i;
        this.typelist = this.$route.query.oneobj || {};
        this.mainlist = this.$route.query.choose;
        if (!this.typelist.reasonId) return;
        if (this.i == -1) {
          this.newlist.push(this.typelist);
          this.newmain.push(this.mainlist);
        } 
        else if (this.$route.query.i != -1) {
          this.newlist.splice(this.i, 1,  this.typelist), this.newmain.splice(this.i, 1,  this.mainlist);
           
        }
        
      } else {
        return;
      }
    },
    // ----------------------------上传图片相关
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "BJSJTYY",
            unitCode: this.unitCode ? this.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      // console.log(params[i]);
      // console.log(localId[i]);
      // console.log(this.accessToken);
      // return
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            // __PATH.ONESTOP + "/minio.doupload",
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "BJSJTYY",
              unitCode: this.unitCode ? this.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
            // this.imagesParams.push(res.data.data.ossUrl);
            // this.imageConfig.push(res.data.data);
          });
      });
    },
    removeCache() {
      this.value = "";
      this.isEval = false;
      this.starNum = "";
    },
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getConfig();
      this.workNum = this.workOrdeInfoDate[0].workNum;
      this.sourcesDeptName = this.workOrdeInfoDate[0].sourcesDeptName;
    });
    this.template = this.$route.query.template;
    this.completePriceFlag = this.$route.query.completePriceFlag;
    //workTypeCode = 6 或者 3 不用
    // console.log(this.$route.query.workOrdeInfoDate[0].itemType[0],'*******************');
    
    if (this.$route.query.workOrdeInfoDate[0].itemType[0]) {
      this.itemServiceCode = this.$route.query.workOrdeInfoDate[0].itemType[0].itemServiceCode;
    }
    if (this.newlist) {
      this.rea();
    }
    $(".weui-cell_swiped").swipeout();
    this.showCompletedCon = this.showCompletedConList;
    // 运送工单和运送类工单完工不显示耗材
    if (this.workTypeCode == 3 || this.template == 3) {
      this.noCons = false;
    }
    // this.getDictList();
  },
  watch: {
    evaluationAdvice: function (newVal, oldVal) {
      if (newVal == "其他") {
        this.reloadComponent();
      }
    },
    value() {
      // 完工说明限制 禁止输入表情
      this.value = this.value.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g, "");
    },
    $route: {
      handler(to, from) {
        if (to.path == "/completed") {
          this.newlist = [];
        }
         this.getlistName();
        this.$nextTick(() => {
         
          $(".weui-cell_swiped").swipeout();
        });
      },
      immediate: true
    },
    newlist() {
      this.rea();

      this.$nextTick(() => {
        $(".weui-cell_swiped").swipeout();
      });
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.personnel {
  margin-top: -10px;
  margin-bottom: 10px;
}

.completed-wrapper {
  background: $bgColor;
  overflow-x: hidden;
  overflow-y: auto;
  margin-bottom: 1.32rem;

  .isEval-content {
    padding-left: 0;
    font-size: calc(16px * var(--font-scale))!important;
    line-height: 0.98rem;
    background: #fff;
    .weui-label {
      width: 120px;
    }
  }

  .header {
    height: 0.57rem;
    display: flex;
    align-items: center;
    margin: 0.15rem 0 0.15rem 0.3rem;
    background: #fff;

    .iconfont {
      font-size: 0.4rem;
      color: $btnColor;
    }

    .title-body {
      height: 0.6rem;
      display: flex;
      align-items: center;
      margin-left: 10px;
      background: #eceef8;
      padding: 0 0.24rem;
      border-radius: 0.3rem;
      white-space: nowrap;

      .dot {
        display: inline-block;
        width: 0.09rem;
        height: 0.09rem;
        background: $btnColor;
      }

      .title {
        font-size: 0.28rem;
        font-weight: 700;
        margin: 0 0.45rem 0 0.08rem;
      }

      .time {
        font-size: 0.3rem;
        color: #4F87FB;
      }
    }
  }

  .content-wrapper {
    // border-left: 1px solid #e5e5e5
    padding: 0px 0 0px 0.37rem;
    // margin: -5px 0 -5px 0rem
    background: #fff;
  }

  .pendOrder-desc {
    .desc {
      line-height: 28px;
      padding: 0 0px 10px;
      box-sizing: border-box;
      background-color: #fff;
      text-align: left;

      textarea {
        padding: 5px;
        border-radius: 5px;
        font-size: 0.28rem;
        color: #888 !important;
        box-sizing: border-box;
        word-wrap: break-word;
        background: #fff;
        // border-top: 1px solid #efefe4;
      }
    }
  }

  .disabled-text {
    color: $textColor;
    text-align: right;
    font-size: calc(16px * var(--font-scale))!important;
  }

  .item {
    itemBaseStyle();
    padding-left: 0;

    .item {
      padding: 0;
    }

    .title {
      font-size: calc(16px * var(--font-scale))!important;
    }

    .consumablesicon {
      color: $color;
    }

    .imgs {
      text-align: right;

      img {
        width: 0.34rem;
        vertical-align: middle;
        margin-right: 5px;
      }

      .img-text {
        vertical-align: middle;
      }
    }
  }

  .signature-content {
    text-align: center;

    .signature-img {
      width: 50%;
    }
  }

  .disabledClick {
    pointer-events: none;
  }

  .pers-sign {
    position: fixed;
    top: 10vh;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 3;
    background-color: #fff;
  }
}

.content-css {
  color: $contentColor;
  font-size: calc(16px * var(--font-scale))!important;
}

.designate-css {
  padding-top: 10px;

  .ipt-content {
    display: flex;
    align-items: center;

    .content-css {
      color: $contentColor;
      font-family: Arial, 'Microsoft Yahei', 'Helvetica Neue', Helvetica, sans-serif;
    }
  }
}

.select {
  background: #fff;
}

.cost {
  font-size: 0.28rem;
  position: relative;
  background: #fff;

  .cost-from {
    font-size: calc(16px * var(--font-scale))!important;
    line-height: 0.98rem;

    .cost-text {
      margin-right: 0.22rem;
    }

    .cost-val {
      font-size: calc(16px * var(--font-scale))!important;
      width: 3.9rem;
    }
  }
}

.select-cause {
  // border-bottom: 1px solid #efefe4;
}

.cause-trouble {
  min-height: 0.98rem;
  position: relative;
  margin-left: 0.37rem;
  font-size: calc(16px * var(--font-scale))!important;
  margin-right: 0.37rem;

  .trouble-text {
    position: absolute;
    top: 0;
    left: 0;
    text-align: left;
    line-height: 0.98rem;
  }

  .trouble-set {
    position: absolute;
    top: 0.332rem;
    right: 0.32rem;
    display: inline;
    font-size: calc(16px * var(--font-scale))!important;
    color: $btnColor;
  }

  .trouble-new {
    color: $textColor;
  }

  .troulble-list {
    min-height: 0.98rem;
    padding-top: 0.3rem;
  }

  .trouble-ipt {
    display: block;
    width: 3.9rem;
    min-height: 0.5rem;
    line-height: 0.4rem;
    margin-left: 1.85rem;
    padding-top: 0.3rem;
    font-size: calc(16px * var(--font-scale))!important;
    color: $contentColor;
  }

  .trouble-info {
    min-height: 0.5rem;
    width: 3.9rem;
    line-height: 0.4rem;
    margin-bottom: 0.2rem;
    margin-left: 1.85rem;
    font-size: calc(16px * var(--font-scale))!important;
    color: $contentColor;
  }
}

.add {
  width: 100%;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  background: #fff;
  color: $contentColor;
  font-size: calc(16px * var(--font-scale))!important;
  border-bottom: 1px solid #E5E6EB;

  .add-img {
    width: 0.4rem;
    height: 0.4rem;
  }

  .add-text {
    color: $textColor;
  }
}

.mint-cell-swipe-buttongroup {
  width: 1.2rem;
  height: 3rem;
}

.zuo {
  width: 210px;
}

.weui-cell {
  padding: 0;
}

.delete-img {
  vertical-align: middle;
  width: 0.48rem;
  text-align: center;
  position: relative;
  top: 38%;
  left: 0;
}

.m-b {
  height: 0.2rem;
  width: 110%;
  background: $bgColor;
  position: relative;
  top: 0;
  left: -0.37rem;
  background: $bgColor;
  z-index: 1;
}

.pl37 {
  padding-left: 0.37rem;
}

.shade {
  width: 54%;
  height: 0.92rem;
  position: absolute;
  top: 0;
  right: 0.1%;
  z-index: 100;
}

.padding-right {
  padding-right: 0.32rem;
}

textarea::placeholder {
  color: #86909C !important;
}

.sub-title {
  margin-left: 0.37rem;
  margin-bottom: 0.3rem;
}

.cause-trouble {
  background-color: #f7f8fa;
}

.trouble-text {
  padding-left: 8px;
}

.trouble-set {
  transform: translateY(0.5rem);
  z-index: 2;
}

.star {
  position: relative;
}

.star::before {
  content: '*';
  color: red;
  position: absolute;
  top: 12px;
  left: 8px;
}

/deep/ .van-field__label > span {
  font-size: calc(16px * var(--font-scale))!important;
  color: #353535;
}

// 设置placeholder的字体大小
/deep/ .van-field__control::placeholder {
  font-size: calc(16px * var(--font-scale))!important;
}

/deep/ .van-field__control {
  font-size: calc(16px * var(--font-scale))!important;
}

/deep/ .van-field__value {
  padding-left: 35px;
}

.van-cell::after {
  display: none;
}

/deep/ .van-tabs__line {
  background-color: #3562db;
}
/deep/ .explain > span ,
/deep/ .explain .upload-img {
  font-size: calc(12px * var(--font-scale))!important;
}
.large-font /deep/  .van-field .van-field__label {
  width: auto;
}
.large-font .trouble-ipt ,
.large-font .trouble-info {
  margin-left: 2.5rem;
}
</style>
