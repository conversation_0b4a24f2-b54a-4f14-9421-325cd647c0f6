<template>
  <div class="container">
    <Header title="工单完工" @backFun="goback"></Header>
    <div class="icon">
      <van-icon name="checked" color="#50b042" size="40px" />
    </div>
    <div class="tip">
      <span>工单已提交完成</span>
    </div>
    <div class="detail" @click="goDetails">查看详情</div>
    <div class="btn">
      <van-button type="primary" size="large" color="#3562DB" @click="backHome">返回首页</van-button>
      <van-button class="btn-link" icon="plus" type="primary" size="large" color="#e7effb" @click="popShow = true">创建关联工单</van-button>
    </div>
    <van-popup v-model="popShow" round position="bottom">
      <div class="pop-content">
        <div class="item" @click="goRepair">综合维修</div>
        <div class="full"></div>
        <div class="item" @click="popShow = false">取消</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: "orderFinished",
  data() {
    return {
      alarmId: "",
      workOrdeInfoDate: [],
      popShow: false
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.alarmId = this.$route.query.alarmId;
    this.workOrdeInfoDate = JSON.parse(this.$route.query.workOrdeInfoDate);
    console.log("workOrdeInfoDate", this.workOrdeInfoDate);
  },
  methods: {
    goback() {
      if(this.$route.query.pageSouce == 'apicloud') {
        this.$YBS.apiCloudCloseFrame();
      }else {
        this.$router.go(-2);
      }
    },
    goDetails() {
      this.$router.push({
        path: "/deskDetails",
        query: {
          id: this.workOrdeInfoDate[0].id
        }
      });
    },
    backHome() {
      // this.$router.push("/personalCenter");
      this.$YBS.apiCloudCloseFrame();
    },
    goRepair() {
      this.$router.push({
        path: "/repairPro",
        query: {
          alarmId: this.alarmId,
          workOrdeInfoDate: JSON.stringify(this.workOrdeInfoDate)
        }
      });
    }
  }
};
</script>

<style scoped>
.icon {
  text-align: center;
}
.tip {
  text-align: center;
  margin: 18px 0;
}
.tip span {
  font-size: 5vw;
}
.detail {
  text-align: center;
  color: #3563dc;
  font-size: 14px;
}
.btn {
  margin: 24px 5vw;
}
.title {
  padding-left: 12vw;
  font-size: 18px;
}
.create-order {
  margin-top: 16px;
  padding-left: 14vw;
  color: #38c7c4;
  font-size: 16px;
  text-decoration: underline;
}
.van-button {
  margin-bottom: 16px;
}
.btn-link .van-button__text {
  color: #3563dc;
}
.btn-link .van-icon {
  color: #3563dc;
}
.full {
  width: 100%;
  height: 10px;
  background-color: #f2f3f5;
}
.pop-content {
  padding-bottom: 30px;
}
.pop-content .item {
  height: 60px;
  line-height: 60px;
  text-align: center;
}
</style>
