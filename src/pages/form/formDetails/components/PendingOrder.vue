<template>
  <div class="pending-wrapper">
    <div class="content-wrapper">
      <div class="weui-cell item border-rightbottom" @click="isShowEntryOrdersReason">
        <div class="weui-cell__hd"><label class="weui-label title">挂单说明</label></div>
        <div class="weui-cell__bd text-wrapper">
          <input class="weui-input ipt content-css" type="text" v-model="pendingVal" readonly placeholder="点击选择挂单说明" />
          <span class="iconfont">&#xe646;</span>
        </div>
      </div>
      <div class="weui-cells_form desc-form pendOrder-desc">
        <div class="weui-cell">
          <div class="weui-cell__hd"><label class="weui-label title">解决说明</label></div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd content-css">
            <textarea
              class="weui-textarea textarea"
              placeholder="请输入解决说明，字数在120字以内"
              maxlength="120"
              rows="6"
              v-model="value"
              :readonly="isReadonly"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
      </div>
      <div class="weui-cells_form desc-form pendOrder-desc">
        <div class="weui-cell">
          <div class="weui-cell__hd"><label class="weui-label title">解决时间</label></div>
          <div class="weui-cell__bd text-wrapper">
            <input
              class="weui-input ipt content-css"
              type="text"
              ref="datetime111"
              v-model="ResolutionTime"
              :class="{ pointer: isReadonly }"
              readonly
              placeholder="点击选择解决时间"
              @click="showPickDate = true"
            />
            <span class="iconfont">&#xe646;</span>
          </div>
        </div>
      </div>
    </div>
    <van-popup v-model="showPickDate" position="bottom">
      <van-datetime-picker v-model="pickTime" type="date" title="选择年月日" :min-date="minDate" @cancel="showPickDate = false" @confirm="dateConfirm" />
    </van-popup>
  </div>
</template>

<script type=text/ecmascript-6>
import { mapState } from "vuex";
let moment = require("moment");
export default {
  name: "PendingOrder",
  props: ["time"],
  data() {
    return {
      value: "",
      pendingVal: "",
      disEntryOrdersReasonCode: "",
      ResolutionTime: "",
      btnFlag: false,
      isReadonly: false,
      entryOrdersReason: [],
      showPickDate: false,
      minDate: new Date(),
      pickTime: "",
      pendingApprovalConfig: "0"
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  methods: {
    getNewSysConfigParam() {
      this.$api.getNewSysConfigParam({}).then(res => {
        let { olgWorkPushNew } = res;
        this.pendingApprovalConfig = olgWorkPushNew.pendingApprovalConfig || "0";
      });
    },
    dateConfirm() {
      this.showPickDate = false;
      this.ResolutionTime = moment(this.pickTime).format("YYYY-MM-DD");
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 时间比较（yyyy-MM-dd）（所选时间不能大于当前时间）
     * @param startTime  当前时间
     * @param endTime   所选时间
     */
    compareTime(startTime, endTime) {
      let startTimeStyle = startTime;
      let startTimes = startTime.substring(0, 10).split("-");
      let endTimes = endTime.substring(0, 10).split("-");
      startTime = startTimes[1] + "-" + startTimes[2] + "-" + startTimes[0];
      endTime = endTimes[1] + "-" + endTimes[2] + "-" + endTimes[0];
      let thisResult = (Date.parse(endTime) - Date.parse(startTime)) / 3600 / 1000;
      if (thisResult < 0) {
        $.toast("应大于当前时间", "text");
        this.ResolutionTime = startTimeStyle;
      }
    },
    /**
     * 时间补0
     */
    add0(m) {
      return m < 10 ? "0" + m : m;
    },
    /**
     * 将时间戳时间格式化为年月日
     * @param shijianchuo  时间戳
     * @returns {string}   返回格式yyyy-MM-dd
     */
    format(shijianchuo) {
      var time = new Date(shijianchuo);
      var y = time.getFullYear();
      var m = time.getMonth() + 1;
      var d = time.getDate();
      return y + "-" + this.add0(m) + "-" + this.add0(d);
    },
    /**
     * 选择挂单说明
     */
    selectPendingExplain() {
      this.$api.getEntryOrdersReason({}).then(this.getSelectPendingExplainSucc);
    },
    /**
     * 挂单说明获取成功
     */
    getSelectPendingExplainSucc(data) {
      const that = this;
      for (let i = 0; i < data.length; i++) {
        that.entryOrdersReason.push({
          text: data[i].disEntryOrdersReason,
          onClick: function () {
            that.pendingVal = data[i].disEntryOrdersReason;
            that.disEntryOrdersReasonCode = data[i].disEntryOrdersReasonCode;
          }
        });
      }
      that.showEntryOrdersReason();
    },
    /**
     * 点击挂单说明
     */
    isShowEntryOrdersReason() {
      if (this.entryOrdersReason.length == 0) {
        this.selectPendingExplain();
      } else {
        this.showEntryOrdersReason();
      }
    },
    /**
     * 显示挂单原因
     */
    showEntryOrdersReason() {
      $.actions({
        actions: this.entryOrdersReason
      });
    }
  },
  watch: {
    pendingVal(curVal) {
      if (curVal != "" && this.value != "") {
        if (!this.btnFlag) {
          this.btnFlag = true;
          this.$emit("changeVal", true);
        }
      } else {
        this.btnFlag = false;
        this.$emit("changeVal", false);
      }
    },
    value(curVal) {
      this.value = this.value.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g, "");
      if (curVal != "" && this.pendingVal != "") {
        if (!this.btnFlag) {
          this.btnFlag = true;
          this.$emit("changeVal", true);
        }
      } else {
        this.btnFlag = false;
        this.$emit("changeVal", false);
      }
    }
  },
  mounted() {
    let _this = this;
    const nTime = new Date(); //限制时间大于当前（日)
    $(this.$refs.datetime).datetimePicker({
      title: "请选择解决时间",
      times: () => {},
      onChange: function (result) {
        let bir = [];
        result.value.forEach((i, index) => {
          bir.push(i);
        });
        let ResolutionTime = bir[0] + "-" + bir[1] + "-" + bir[2];
        _this.ResolutionTime = ResolutionTime;
        _this.compareTime(_this.format(nTime), ResolutionTime);
      }
    });
    _this.getNewSysConfigParam();
  }
};
</script>

<style lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.pending-wrapper {
  .header {
    height: 0.57rem;
    display: flex;
    align-items: center;
    margin: 0.15rem 0 0.15rem 0.3rem;
    background: #fff;

    .iconfont {
      font-size: 0.4rem;
      color: $btnColor;
    }

    .title-body {
      height: 0.6rem;
      display: flex;
      align-items: center;
      margin-left: 10px;
      background: #eceef8;
      padding: 0 0.24rem;
      border-radius: 0.3rem;
      white-space: nowrap;

      .dot {
        display: inline-block;
        width: 0.09rem;
        height: 0.09rem;
        background: $btnColor;
      }

      .title {
        font-size: 0.28rem;
        font-weight: 700;
        margin: 0 0.45rem 0 0.08rem;
      }

      .time {
        font-size: 0.3rem;
        color: #4F87FB;
      }
    }
  }

  .content-wrapper {
    timelineContent();
    margin: -5px 0 -10px 0rem;

    .item {
      font-size: calc(16px * var(--font-scale))!important;
      itemBaseStyle();
    }
  }
}

.pendOrder-desc {
  background-color: #fff;
  padding-top: 10px;

  .title {
    font-size: calc(16px * var(--font-scale))!important;
  }
}

.desc {
  line-height: 28px;
  padding: 5px 0.32rem 0;
  box-sizing: border-box;
  background-color: #fff;
}

.textarea {
  font-size: calc(16px * var(--font-scale))!important;
  background: #f5f6fb;
  border-radius: 5px;
  padding: 5px;
  box-sizing: border-box;
  word-wrap: break-word;
}

.text-wrapper {
  color: $textColor;
  font-size: 0.28rem;
  display: flex;
  align-items: center;

  .ipt {
    text-align: right;
  }
}

.pointer {
  pointer-events: none;
}

.resolution-time {
  margin-top: 0.2rem;
}
.content-css {
  font-size: calc(16px * var(--font-scale))!important;
}
</style>
