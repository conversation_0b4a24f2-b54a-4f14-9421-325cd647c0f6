<template>
  <div class="wrapper">
    <!-- <div class="title">为更好提升服务品质，请及时进行评价。</div>
    <div>
      <div class="er-level">
      <div class="weui-cells_checkbox">
        <div class="redio-content">
          <label v-for="(item,index) in evaluateDict" :key="index" class="weui-cell weui-check__label" :for="index">
            <div class="weui-cell__hd">
              <input
                  type="radio"
                  class="weui-check"
                  name="checkbox3"
                  :id="index"
                  checked="checked"
                  :value="item.value"
                  v-model="evaluate"
              >
              <i class="weui-icon-checked"></i>
            </div>
            <div class="weui-cell__bd">{{item.label}}</div>
          </label>
        </div>
      </div>
    </div>
    </div> -->
    <div class="title">满意度评价</div>
    <div class="box">
      <div
          class="star"
          v-for="n of 5"
          @click="Selected(n)"
          :class="{'on':cur>=n}"
      ></div>
      <span class="text content-css">{{text}}</span>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "StarPage",
    data () {
      return {
        cur: 5,
        flag: false,
        text:"非常满意",
        starIdx:5,
        // evaluate: 1,
        // evaluateDict: [
        //   {
        //     label: '业务熟练，维修快速',
        //     value: 1
        //   },{
        //     label: '服务态度好',
        //     value: 2
        //   },{
        //     label: 'XXXXX',
        //     value: 3
        //   },
        // ]
      }
    },
    methods: {
      Selected (n) {
        this.flag = true
        this.cur = n
        switch (n) {
          case 1:
            this.text = "非常差"
            this.starIdx = 1
            break
          case 2:
            this.text = "差"
            this.starIdx = 2
            break
          case 3:
            this.text = "一般"
            this.starIdx = 3
            break
          case 4:
            this.text = "满意"
            this.starIdx = 4
            break
          case 5:
            this.text = "非常满意"
            this.starIdx = 5
            break
        }
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  // @import '~styles/varibles.styl';
  .wrapper
    font-size: .32rem
    itemBaseStyle()
    .title
      float: left
    // .er-level
    //   align-items: left
    //   itemBaseStyle()
    //   .weui-cells_checkbox
    //     display: flex
    //     align-items: center
    //     justify-content: center
    //     justify-content: space-between
    //     width: 100%
    //     .redio-content
    //       color: $contentColor
    //       font-size: .3rem
    //       .weui-cell {
    //         padding: 0
    //       }
    .box
      display: flex
      align-items: center
      float: right
      height 48px
      .on
        background: url('../../../../assets/images/on.png') no-repeat
        background-size: cover
      div
        width: .38rem
        height: .37rem
        float: left
        background: url('../../../../assets/images/off.png') no-repeat
        background-size: cover
        margin-right: .18rem
      .text
        vertical-align sub
        display: inline-block
        width: 80px
        text-align: center

</style>
