<template>
  <div class="wrapper serve-radio">
    <div class="content content-background">
      <div class="radio-group">
        <cube-radio-group v-if="type == 6" v-model="selected" :options="list" />
        <cube-checkbox-group v-else v-model="checkList" :options="list" />
      </div>
      <div class="finished-btn" @click="handleComplate">
        <button class="weui-btn weui-btn_primary btn">
          <span>确定</span>
        </button>
      </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  name: "",
  data() {
    return {
      selected: "",
      checkList: [],
      options: []
    };
  },
  props: {
    type: {
      type: String,
      default: "3" //(3运输；6综合)
    },
    list: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {
    handleComplate() {
      let params;
      if (this.type == 6) {
        let index = this.list.findIndex((item)=>{return this.selected == item.value});
        params = this.list[index];
        this.$emit("back", params);
      }else{
        params = []
        this.checkList.forEach(item=>{
            params.push(this.list.find(result=>{
                return result.value == item
            }))
        })
        this.$emit("back", params);
      }
    }
  },
  components: {}
};
</script>
<style lang="stylus" type="stylesheet/stylus">
.finished-btn {
  height: 1.32rem;
  padding: 0.22rem 0.16rem;
  box-sizing: border-box;
  border-top: 1px solid $bgColor;
  background-color: #fff;
  width: 100%;
  position: fixed;
  bottom: 0;
}

.cube-radio_selected .cube-radio-ui {
  background-color: #38c7c4;
}

.cube-checkbox_checked .cube-checkbox-ui i {
  color: #38c7c4;
}

.content-background {
  background: #fff;
}

.serve-radio {
  position: absolute;
  top: 0;
  z-index: 1000;
  height: 100vh;
  width: 100vw;

  .content {
    height: 100%;

    .radio-group {
      height: calc(100% - 75px);
      overflow: auto;
    }
  }
}
</style>