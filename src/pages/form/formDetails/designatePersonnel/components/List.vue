<template>
  <div class="list" ref="wrapper">
    <div>
      <div
          class="pers"
          v-for="(item,key) of allPers"
          :key="key"
          :ref="key"
      >
        <div class="item-list">
          <div
              class="item border-bottom"
              v-for="innerItem of item"
              :key="innerItem.id"
              @click="handleHospClick(innerItem)"
          >
            <p>{{ innerItem.hospitalName }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import BScroll from 'better-scroll'
  import { mapMutations } from 'vuex'
  export default {
    name: "HospitalList",
    props: ["allPers"],
    methods:{
      handleHospClick (innerItem) {
        // this.changeHosp(innerItem)
        this.$router.push(this.pathName)
      },
      // ...mapMutations(['changeHosp'])
    },
    watch: {
      letter () {
        if (this.letter) {
          const element = this.$refs[this.letter][0]
          this.scroll.scrollToElement(element)
        }
      }
    },
    mounted () {
      this.scroll = new BScroll(this.$refs.wrapper,{click:true})
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  .list
    overflow: hidden
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    background-color: $bgColor
    .item-list
      .item
        line-height: 1.4rem
        padding-left: .2rem
        font-size: .36rem
        color: #353535
        background-color: #fff
        display: flex
        align-items: center
</style>