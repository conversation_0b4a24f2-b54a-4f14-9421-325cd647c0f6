<template>
  <div class="list" ref="wrapper">
    <Header title="选择耗材" @backFun="goback"></Header>
    <consumablesList
      v-show="showList"
      @enterDetailsInfo="handleEnterDetails"
      :workTypeCode="workTypeCode"
    ></consumablesList>
    <select-consumables
      v-if="!showList"
      :id="id"
      :productName="productName"
      :selectedNum="selectedNum"
      :selectedItem="selectedItem"
      :workTypeCode="workTypeCode"
      ref="consumablesInfo"
      @changeNum="handleChangeNum"
    ></select-consumables>
    <div class="submit-btn">
      <a
        href="javascript:;"
        class="weui-tabbar__item weui-bar__item--on badge"
        @click="showPop"
      >
        <span class="weui-badge num">{{ selectedNum }}</span>
        <div class="weui-tabbar__icon icon-wrapper">
          <span class="iconfont icon">&#xe609;</span>
        </div>
      </a>
      <div class="btns">
        <button
          class="cancle-btn"
          v-if="showList"
          @click="handleCancleSelectedClick"
        >
          取消
        </button>
        <button
          class="sure-btn"
          v-if="showList"
          @click="handleGetSelectedConsumables"
        >
          完成
        </button>
        <button class="sure-btn" v-else @click="handleGoBackList">确定</button>
      </div>
    </div>

    <pollup-box
      v-if="isShowPop"
      :selectedItem="selectedItem"
      :selectedNum="selectedNum"
      @handleAddCount="handleAddSelectedNum"
      @handleLessCount="handleLessSelectedNum"
      @closePop="handleClosePop"
      @cancelStyle="handleCancelStyle"
    ></pollup-box>
  </div>
</template>

<script>
import SelectConsumables from "./components/ConsumablesDetails";
import ConsumablesList from "./components/ConsumablesList";
import PollupBox from "@/common/customize/PollupBox";
export default {
  name: "Consumables",
  components: {
    PollupBox,
    SelectConsumables,
    ConsumablesList
  },
  data() {
    return {
      selectedNum: 0,
      selectedItem: [],
      showList: true,
      id: "",
      productName: "", //二级中的搜索内容
      parentComponents: true,
      isShowPop: false,
      workTypeCode: "",
      isShow: true,
      isVieOder: true,
      designatePersonnel: "请选择",
      noticePeople: "派工人员",
      designatePersonnelId: "",
      designatePersonnelTel: "",
      parm: "",
      inputInfo: false,
      showConList: false,
      isPendingOder: false,
      isConfirm: false,
      selectedData: [],
      pendingReasonVal: "",
      pendingSolutionVal: "",
      origin: ""
    };
  },
  methods: {
    goback() {
      // this.$router.go(-1);
      this.handleCancleSelectedClick()
    },
    /**
     * 点击列表项进入详情页
     * @param name
     */
    handleEnterDetails(itemId, productName) {
      this.showList = !this.showList;
      this.id = itemId ? itemId.id : ""; //从二级点击进入三级中是有id，在二级中所搜是没有id
      this.productName = productName; //从二级点击进入三级中是有搜索的内容的
      this.parentComponents = false;
    },
    /**
     * 接收子组件发射的事件所带的参数
     */
    handleChangeNum(paramsObj) {
      this.selectedNum = paramsObj.selectedNum;
      this.selectedItem = paramsObj.selectedItem; //若购物车中已有只叠加，不在单独显示
    },
    /**
     * 添加耗材（数据显示）
     */
    handleAddSelectedNum() {
      let num = this.selectedNum;
      this.selectedNum = ++num;
    },
    /**
     * 取消选择耗材（数据显示）
     */
    handleLessSelectedNum() {
      let num = this.selectedNum;
      this.selectedNum = --num;
    },
    /**
     * 当弹出框中某项被减至0时，去掉选中样式
     */
    handleCancelStyle(name) {
      if (this.$refs.consumablesInfo) {
        this.$refs.consumablesInfo.handleCancelStyle(name);
      }
    },
    /**
     * 点击详情里的确定按钮返回列表页的操作
     */
    handleGoBackList() {
      this.showList = true;
      this.isShowPop = false;
    },
    /**
     * 显示弹窗
     */
    showPop() {
      this.isShowPop = true;
    },
    /**
     * 关闭弹窗
     */
    handleClosePop() {
      this.isShowPop = false;
    },
    /**
     * 将已选的所有耗材回显到工单详情
     */
    handleGetSelectedConsumables() {
      // this.$router.go(-1)
      // return
      if (this.parm == "pendingOder") {
        this.$router.replace({
          name: "CompletedInfo",
          // name: '/OrderHandle',
          params: {
            pendLists: this.selectedItem,
            inputInfo: this.inputInfo,
            style: "pendingOder",
            pendingReasonVal: this.pendingReasonVal,
            pendingSolutionVal: this.pendingSolutionVal,
            isPendingOder: this.isPendingOder
          }
        });
      } else if (this.parm == "completedOder") {
        this.$router.replace({
          // name:'CompletedInfo',
          name: "OrderHandle",
          params: {
            comLists: this.selectedItem,
            inputInfo: this.inputInfo,
            showConList: this.showConList,
            isPendingOder: this.isPendingOder,
            isConfirm: this.isConfirm,
            selectedData: this.selectedData,
            style: "completedOder"
          }
        }).then(() => {
          this.$router.go(-1);
        });
      } else {
        this.$router.push({
          name: "VieOderInfo",
          params: {
            list: this.selectedItem,
            isShow: this.isShow,
            isVieOder: this.isVieOder,
            designatePersonnel: this.designatePersonnel,
            noticePeople: this.noticePeople,
            designatePersonnelId: this.designatePersonnelId,
            designatePersonnelTel: this.designatePersonnelTel,
            flag: true,
            origin: this.origin
          }
        });
      }
    },
    /**
     * 取消本次选择耗材的操作
     */
    handleCancleSelectedClick() {
      let rtParams = JSON.parse(sessionStorage.getItem("putConsumables"));
      if (this.parm == "pendingOder") {
        this.$router.push({
          name: "CompletedInfo",
          params: {
            pendLists: rtParams.lists,
            style: "pendingOder"
          }
        });
      } else if (this.parm == "completedOder") {
        this.$router.replace({
          name: "OrderHandle",
          params: {
            comLists: rtParams.lists,
            inputInfo: rtParams.inputInfo,
            showConList: rtParams.showConList,
            isPendingOder: rtParams.isPendingOder,
            isConfirm: rtParams.isConfirm,
            selectedData: rtParams.selectedData,
            style: "completedOder"
          }
        }).then(() => {
          this.$router.go(-1);
        });
      } else {
        this.$router.push({
          name: "VieOderInfo",
          params: {
            list: rtParams.lists,
            isShow: rtParams.isShow,
            isVieOder: rtParams.isVieOder,
            designatePersonnel: rtParams.designatePersonnel,
            noticePeople: rtParams.noticePeople,
            designatePersonnelId: rtParams.designatePersonnelId,
            designatePersonnelTel: rtParams.designatePersonnelTel,
            flag: true
          }
        });
      }
    }
  },
  mounted() {
    //置换耗材时，已选耗材在购物车中回显
    let routerParams = this.$route.params;
    sessionStorage.setItem("putConsumables", JSON.stringify(routerParams));

    this.workTypeCode = this.$route.params.workTypeCode;
    this.origin = this.$route.params.origin;
    this.selectedItem = this.$route.params.lists;
    this.isShow = this.$route.params.isShow;
    this.isVieOder = this.$route.params.isVieOder;
    this.designatePersonnel = this.$route.params.designatePersonnel;
    this.noticePeople = this.$route.params.noticePeople;
    this.designatePersonnelId = this.$route.params.designatePersonnelId;
    this.designatePersonnelTel = this.$route.params.designatePersonnelTel;

    this.parm = this.$route.params.parm;
    this.inputInfo = this.$route.params.inputInfo;
    this.showConList = this.$route.params.showConList;
    this.isPendingOder = this.$route.params.isPendingOder;
    this.isConfirm = this.$route.params.isConfirm;
    this.selectedData = this.$route.params.selectedData;
    //挂单
    this.pendingReasonVal = this.$route.params.pendingReasonVal;
    this.pendingSolutionVal = this.$route.params.pendingSolutionVal;
    this.isPendingOder = this.$route.params.isPendingOder;

    let lastTotal = 0;
    for (let i = 0; i < this.selectedItem.length; i++) {
      lastTotal += this.selectedItem[i].num * 1;
    }
    this.selectedNum = lastTotal;
  },
  watch: {
    selectedNum(val) {
      if (val == 0) {
        this.isShowPop = false;
      }
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/varibles.styl"
.border-bottom
  &:before
    border-color: #ccc

.list
  position: absolute
  top: 0
  left: 0
  right: 0
  bottom: 0
  background-color: $bgColor
  .item
    display: flex
    justify-content: space-between
    line-height: .98rem
    padding: 0 .3rem
    font-size: .36rem
    background-color: #fff
    margin-bottom: 1px
    .num
      display: inline-block
      width: 15px
      height: 15px
      margin-left: .2rem
      border-radius: 50%
      background: red
      color: #fff
      font-size: 12px
      text-align: center
      line-height: 17px
    .iconfont
      color: #e5e5e5
    &:active
      background-color: transparent
.submit-btn
  padding: 0.22rem 0.32rem
  background-color: #fff
  width: 100%
  box-sizing: border-box
  height: 58px
  margin-top: -58px
  position: fixed
  bottom: 0
  .num
    position: absolute;
    top: -.4em;
    right: -.5em;
  .icon-wrapper
    width: .64rem
    height: .64rem
    border-radius: 50%
    text-align: center
    line-height: .64rem
    background-color: $bgColor
    .icon
      font-size: .45rem
      color: $color
  .badge
    width: .64rem
    float: left
  .btns
    float: right
    button
      width: 104px
      height: 35px
      font-size: 18px
      font-family: unset
    .cancle-btn
      background-color: transparent
      color: $color
    .sure-btn
      background-color: $color
      color: #fff
      border-radius: 5px
</style>
