<template>
  <div :class="['content',id ? '' : 'no-search']">
    <!--<div class="item-consum">
      <div class="weui-cell border-rightbottom title">
        <div class="weui-cell__hd"><label class="weui-label item">{{title}}</label></div>
      </div>
    </div>-->
    <div class="search-wrapper" v-if="id">
      <input
       type="text"  
       class="search-content" 
       placeholder="请输入关键字搜索" 
       oninput="if(value.length>50)value=value.slice(0,50)"
       v-model="iptConsumableName">
      <div class="search-text">
        <span class="iconfont" v-if="iptConsumableName" @click="clearText">&#xe61f;</span>
        <span class="search" @click="getDepotManagementList('search')">搜索</span>
      </div>
    </div>
    <pull-to :bottom-load-method="refresh" v-if="lists.length">
      <div class="list-wrapper">
        <button
            class="ele"
            v-for="(item,index) of lists"
            :key="index"
            :class="{active:item.check}"
            @click="selected(index)"
        >{{item.depThreeTypeName}}
        </button>
      </div>
    </pull-to>
    <div class="weui-loadmore weui-loadmore_line" v-if="noData">
      <span class="weui-loadmore__tips">暂无数据</span>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import PullTo from 'vue-pull-to'
  import {mapState} from 'vuex'
  export default {
    name: "SelectConsumables",
    props: ['id', 'productName', 'selectedNum', 'selectedItem','workTypeCode'],
    components: {
      PullTo
    },
    data () {
      return {
        isShowPop: false,
        selecNum: 0,
        flag:false,
        noData:false,
        lists: [],
        curPage: 0,
        sysName:false, //安卓
        iptConsumableName: '',//搜索的文字
      }
    },
    computed: {
      ...mapState(['loginInfo'])
    },
    methods: {
      /**
       * 清空输入框中的内容
       */
      clearText () {
        this.iptConsumableName = ""
      },
      /**
       * 上拉加载更多
       */
      refresh(loaded){
        if (this.flag) {
          this.getDepotManagementList()
          loaded('done')
          this.flag = false
        }else{
          loaded('done')
          $.toast("没有更多数据啦","text")
        }
      },
      /**
       * 获取耗材三级列表
       */
      getDepotManagementList (type) {
      let productName = ""
        console.log(type);
        if(type == "search"){
          this.curPage = "1"
          productName = this.iptConsumableName
        }else{
          let lastPage = this.curPage
          this.curPage = ++lastPage
          productName = this.productName
        }
        this.$api.getDepotManagementList({
          productName: productName,
          workTypeCode:this.workTypeCode,
          depTwoTypeId:this.id,
          curPage: this.curPage,
          pageSize: 30,
          flag:"1"
        }).then(res => {
          if(type == "search"){
            this.lists = []
          }
          for (let i = 0; i < res.length; i++) {
            res[i].check = false
            res[i].num = 1
          }
          this.lists = this.lists.concat(res)
          if(res.length == 0 && this.lists.length == 0){
            this.noData = true
          }
          if(res.length < 30){
            this.flag = false
          }else{
            this.flag = true
          }
        })
      },
      /**
       * 选择单项耗材（样式显示）
       * @param index
       * @param idx
       */
      selected(index){
        let check = this.lists[index].check
        let selectedNum = this.selectedNum
        if (check) {
          this.lists[index].check = false
          this.selecNum = --selectedNum
          this.selectedItem.splice(this.lists[index], 1)
        } else {
          this.lists[index].check = true
          this.selecNum = ++selectedNum

          //判断已选耗材是否在购物车中存在，存在则叠加，不存在则新增
          let flag = true
          for (let i = 0; i < this.selectedItem.length; i++) {
            if (this.selectedItem[i].depThreeTypeName == this.lists[index].depThreeTypeName) {
              this.selectedItem[i].num++
              flag = false
            }
          }
          if (flag) {
            this.selectedItem.push(this.lists[index])
          }

        }
        this.$emit('changeNum', {selectedNum: this.selecNum, selectedItem: this.selectedItem})
      },
      /**
       * 当弹出框中某项被减至0时，去掉选中样式
       */
      handleCancelStyle (name) {
        for (let i = 0; i < this.lists.length; i++) {
          if (this.lists[i].depThreeTypeName == name) {
            this.lists[i].check = false
          }
        }
      },
      /**
       * 显示弹窗
       */
      showPop () {
        this.isShowPop = true
      },
      /**
       * 关闭弹窗
       */
      handleClosePop () {
        this.isShowPop = false
      },
      justSysName () {
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
          //Ios
          this.sysName = true
        } else if (/(Android)/i.test(navigator.userAgent)) {
          //Android终端
          this.sysName = false
        }
      }
    },
    mounted () {
      this.getDepotManagementList()
      this.justSysName()
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
    .content >>> .vue-pull-to-wrapper
      background-color: $bgColor
    .content
      box-sizing: border-box
      min-height: 100%
      padding-bottom: 68px
      background-color: $bgColor
      height: calc(100% - 58px - .68rem)
      padding-top: 10px
      .item-consum
        margin-bottom: .1rem
        padding: 0 .17rem
        background-color: #fff
        .title
          itemBaseStyle()
          position: fixed
          top: 0
          left: 0
          width: 100%
          z-index: 2
          .item
            font-size: .32rem

      .weui-loadmore__tips
        background-color: $bgColor
      .weui-label
        width: auto
    .list-wrapper
      background-color: #fff
      .ele
        line-height: 1
        border: 0
        background-color: #f4f4f4
        padding: .2rem .45rem
        margin: .1rem .15rem
        border-radius: .1rem
        color: #353535
        font-family: unset
      .active
        color: $color
        background-color: #ebf9f9
  .search-wrapper
    padding: .18rem .32rem
    position: fixed
    top: 0
    width: 100%
    z-index: 2
    box-sizing: border-box
    background-color: #fff
    .search-content
      background: $bgColor url('~images/newForm/search.png') no-repeat .21rem
      background-size: .26rem
      height: .68rem
      border-radius .34rem
      padding-left: .66rem
      width: 100%
      box-sizing: border-box
      padding-right: 1rem
    .search-text
      position: absolute
      top: .35rem
      right: 0.55rem
      color: #898991
      .search
        border-left: 1px solid #d7d7d7
        padding-left: .16rem
  .no-search
    padding-top: 0
</style>
