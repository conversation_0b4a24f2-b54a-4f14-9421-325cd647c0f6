<template>
  <div class="lists">
    <div class="search-wrapper">
      <input type="text" class="search-content" placeholder="请输入关键字搜索" oninput="if(value.length>50)value=value.slice(0,50)" v-model="iptConsumableName" />
      <div class="search-text">
        <span class="iconfont" v-if="iptConsumableName" @click="clearText">&#xe61f;</span>
        <span class="search" @click="handleSearchClick">搜索</span>
      </div>
    </div>
    <pull-to :bottom-load-method="refresh">
      <ul>
        <li class="item" v-for="(item, index) of depTwoType" :key="index" @click="enterDetails({ id: item.depTwoTypeId })">
          <span>
            <span>{{ item.depTwoTypeName }}</span>
            <span class="num" v-if="item.selectedCount">{{ item.selectedCount }}</span>
          </span>
          <span class="iconfont">&#xe646;</span>
        </li>
      </ul>
    </pull-to>
  </div>
</template>

<script>
import PullTo from "vue-pull-to";
import { mapState } from "vuex";
export default {
  name: "MaterialList",
  props: ["workTypeCode"],
  data() {
    return {
      depTwoType: [],
      selectedNum: 0,
      isShowPop: false,
      flag: false,
      curPage: 0,
      iptConsumableName: "", //搜索的文字
    };
  },
  components: {
    PullTo,
  },
  methods: {
    /**
     * 清空输入框中的内容
     */
    clearText() {
      this.iptConsumableName = "";
    },
    /**
     * 点击搜索
     */
    handleSearchClick() {
      this.enterDetails("", this.iptConsumableName);
    },
    /**
     * 上拉加载更多
     */
    refresh(loaded) {
      this.getConsumableList();
      if (this.flag) {
        loaded("done");
        this.flag = false;
      }
    },
    /**
     * 向父组件发射点进入详情页事件
     * @param itemId
     */
    enterDetails(itemId, productName) {
      this.$emit("enterDetailsInfo", itemId, productName);
    },
    /**
     * 请求耗材二级列表
     */
    getConsumableList() {
      let lastPage = this.curPage;
      this.curPage = ++lastPage;
      this.$api
        .getDepotCatalogueList({
          curPage: this.curPage,
          pageSize: 15,
          workTypeCode: this.workTypeCode,
        })
        .then(this.getConsumableListSucc);
    },
    /**
     * 请求耗材二级列表成功回调函数
     * @param res
     */
    getConsumableListSucc(data) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        item.selectedCount = 0;
        this.depTwoType.push(item);
      }
      this.flag = true;
    },
  },
  computed: {
    ...mapState(["loginInfo"]),
  },
  mounted() {
    setTimeout(() => {
      this.getConsumableList();
    }, 100);
  },
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/varibles.styl"
@import "~styles/mixins.styl"
  .lists
    height: calc(100% - 116px)
    // padding-top: 1vh
    .item
      display: flex
      justify-content: space-between
      line-height: .98rem
      padding: 0 .3rem
      font-size: .36rem
      background-color: #fff
      margin-bottom: 1px
      .num
        display: inline-block
        width: 15px
        height: 15px
        margin-left: .2rem
        border-radius: 50%
        background: red
        color: #fff
        font-size: 12px
        text-align: center
        line-height: 17px
      .iconfont
        color: #e5e5e5
      &:active
        background-color: transparent
    .search-wrapper
      padding: .18rem .32rem
      position: fixed
      top: 0
      width: 100%
      z-index: 2
      box-sizing: border-box
      background-color: #fff
      .search-content
        background: $bgColor url('~images/newForm/search.png') no-repeat .21rem
        background-size: .26rem
        height: .68rem
        border-radius .34rem
        padding-left: .66rem
        width: 100%
        box-sizing: border-box
        padding-right: 1rem
      .search-text
        position: absolute
        top: .35rem
        right: 0.55rem
        color: #898991
        .search
          border-left: 1px solid #d7d7d7
          padding-left: .16rem
</style>
