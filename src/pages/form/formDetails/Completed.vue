<template>
  <div class="wrapper trigger-box">
    <Header title="工单详情" @backFun="goback"></Header>
    <div class="simplification" :class="{ 'large-font': isLargeFont }">
      <div class="item" v-for="(item, idx) of workOrdeInfoDate" :key="idx" @click.stop="handlefold($event, idx)">
        <!--基本详情-->
        <div v-if="item.operationCode == 1">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <base-info :baseData="item"></base-info>
        </div>
        <!--已受理-->
        <div v-if="item.operationCode == 2 && item.type != 2">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <service-site :serviceData="item" :workTypeCode="workTypeCode" :template="template"></service-site>
        </div>
        <!--已派工-->
        <div v-if="item.operationCode == 3">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <person :persData="item"></person>
        </div>
        <!--已挂单-->
        <div v-if="item.operationCode == 4">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <pending-details :pendingData="item"></pending-details>
        </div>
        <!--督促-->
        <div v-if="item.operationCode == 8">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <return-visit :returnVisitdData="item" :name="item.operationType"></return-visit>
        </div>
        <!--已转派-->
        <div v-if="item.operationCode == 9">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <change-order :changeOrderData="item"></change-order>
        </div>
        <!-- 转派 -->
        <div v-if="item.operationCode == 11">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <change-order :changeOrderData="item"></change-order>
        </div>
        <!--已完工-->
        <div v-if="item.operationCode == 6">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <evaluation-info ref="evaluation" :finishedData="item" :workTypeCode="workTypeCode"></evaluation-info>
        </div>
        <!--回退-->
        <div v-if="item.operationCode == 30">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <retroversion :cancelData="item"></retroversion>
        </div>
        <!-- 已变更 -->
        <div v-if="item.operationCode == 10">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <changed :changedData="item" :workTypeCode="workTypeCode" :template="template"></changed>
        </div>
        <!-- 到达 -->
        <div v-if="item.operationCode == 31">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <arrival :persData="item"></arrival>
        </div>
      </div>
    </div>

    <!-- 精简工单 -->
    <!-- <simplify-work v-else :workOrdeInfoDate="workOrdeInfoDate"></simplify-work> -->
    <div class="height-placeholder" style="height: 38px"></div>
    <div class="vie-info">
      <!--挂单和完工-->
      <div v-if="inputInfo">
        <!--挂单-->
        <!-- <pending-order
            v-if="isPendingOder"
            ref="pendingOder"
            :time="timeFunc(Date.parse(new Date()),'.')"
            @changeVal="handleChildChangeVal"
        ></pending-order>-->
        <!--完工-->
        <!-- <completed-oder
            v-if="!isPendingOder"
            ref="finished"
            :workTypeCode="workTypeCode"
            :inputInfo="inputInfo"
            :showCompletedConList="showCompletedConList"
            :isPendingOder="isPendingOder"
            :isConfirm="isConfirm"
            :selectedComConsumables="selectedComConsumables"
            :time="timeFunc(Date.parse(new Date()),'.')"
        ></completed-oder>-->
      </div>
    </div>
    <!--派工信息-->
    <!--<div class="vie-info">-->
    <!--一体机自动派工和我的中的除了完工和取消工单中组长可以派工-->
    <!--v-if="showDesignate || (isLeader && !forwardInfo)"-->
    <add-personnel ref="addPersonnel" source="pending" v-if="showDesignate"></add-personnel>
    <!--</div>-->
    <!--转派处理-->
    <change-order-handle v-if="handleChangeOderInfo" :title="handleComponentsTitle" :workTypeCode="workTypeCode"
      :localtionId="localtionId" :itemTypeCode="itemTypeCode" ref="changeOrder"></change-order-handle>
    <div class="height-placeholder" style="height: 38px"></div>
    <div v-if="isShow">
      <div v-if="!isFinished && workOrdeInfoDate.length != 0">
        <div>
          <div class="btns" v-if="olgWorkPushNew.workOrderArrivalConfig == 1 && !isConfirmOrder && showArrival">
            <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="handleArrival">确认到达</button>
            <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="handleChangeOder">转派</button>
            <button v-if="isConditionMet" class="weui-btn weui-btn_mini weui-btn_primary btn"
              @click="materialRequisition">物料领/退</button>
          </div>

          <div class="btns" v-else-if="isConfirm && !isConfirmOrder">
            <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="cancelApplication"
              v-if="approvalResult">挂单审批中,取消挂单申请</button>
            <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="handleShowCompletedOder"
              :class="{ 'confirm-btn': !isNotHanged }" v-if="!approvalResult">
              完工
            </button>
            <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="handleShowPendingOder"
              v-if="isNotHanged && workTypeCode != '16' && !approvalResult">挂单</button>
            <span class="btn btn-more" v-if="!approvalResult" @click="moreOperate">更多操作</span>
          </div>
          <div v-else-if="isConfirm && isConfirmOrder" class="btns">
            <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="handleConfirm">确认接收</button>
          </div>
          <div class="btns" v-else>
            <button class="weui-btn weui-btn_primary confirm-btn btn" @click="handleSubmitClick"
              :class="{ 'weui-btn_disabled': isDis, active: canShow }" :disabled="disab" v-show="isOriginHei">
              确定
            </button>
          </div>
        </div>
      </div>
      <div v-if="originStyle && workOrdeInfoDate.length != 0">
        <!--判断来源是推送，还是挂单页面-->
        <div class="btns finished-btn" v-if="isFinished">
          <button class="weui-btn weui-btn_disabled weui-btn_primary" disabled>
            <span>{{ oderStatus }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import BaseInfo from "./components/BaseInfoNew";
  import ServiceSite from "./components/Accepted";
  import Person from "@/pages/mine/components/Dispatched";
  import PendingDetails from "@/pages/mine/components/PendingDetails";
  import CompletedOder from "./components/CompletedOder";
  import PendingOrder from "./components/PendingOrder";
  import retroversion from "./components/retroversion";

  import EvaluationInfo from "@/pages/mine/components/EvaluationInfo";
  import ReturnVisit from "@/pages/mine/components/ReturnVisit";
  import AddPersonnel from "./components/AddDesignatePersonnel";
  import ChangeOrder from "./components/ChangeOrderInfo";
  import Changed from "@/pages/mine/components/Changed";
  import changeOrderHandle from "@/pages/mine/components/ChangeOrderHandle";
  import global from "@/utils/Global";
  import {
    mapState
  } from "vuex";
  import SimplifyWork from "./components/SimplifyWork";
  import Arrival from "@/pages/mine/components/arrival";
  import YBS from "@/assets/utils/utils.js";
  import fontSizeMixin from "@/mixins/fontSizeMixin";
  export default {
    name: "CompletedInfo",
    mixins: [fontSizeMixin],
    components: {
      CompletedOder,
      BaseInfo,
      ServiceSite,
      Person,
      PendingDetails,
      PendingOrder,
      retroversion,
      EvaluationInfo,
      ReturnVisit,
      AddPersonnel,
      ChangeOrder,
      changeOrderHandle,
      SimplifyWork,
      Changed,
      Arrival
    },
    data() {
      return {
        olgWorkPushNew: {},
        isOriginHei: true,
        screenHeight: document.documentElement.clientHeight,
        originHeight: document.documentElement.clientHeight,
        canShow: false,
        workOrdeInfoDate: [],
        workOrderId: "",
        disabled: false,
        isDis: false,
        disab: false,
        showDesignate: false,
        //        personnelVal: false,
        actionSheet: null,
        havePendingPeo: false,
        inputInfo: false,
        showConList: true,
        isNotHanged: true,
        originStyle: true,
        showCompletedConList: false,
        workTypeCode: 1,
        template: "", //工单模版 6综合类/3运送类
        isPendingOder: false,
        isConfirm: true,
        designatePersonName: "",
        selectedComConsumables: [],
        selectedConsumables: [],
        imgSrc: "",
        isFinished: false,
        oderStatus: "",
        pendingReasonVal: "",
        pendingSolutionVal: "",
        btnFlagCount: false,
        disEntryOrdersReason: "", //挂单详情（从我的工作台-挂单进入）
        disEntryOrdersSolution: "",
        disPlanSolutionTime: "",
        designatePersonnelId: "",
        designatePersonnelTel: "",
        isDispatching: "",
        isLeader: false, //角色是否为组长
        forwardInfo: false, //true:来源于推送，false: 来源于我的
        //转派处理相关
        canChangeOrder: false, //是否可以转派,显示转派按钮
        handleChangeOderInfo: false, //显示添加转派处理信息
        handleComponentsTitle: "", //处理组件的title
        localtionId: "", //获取指派班组使用
        itemTypeCode: "", //获取指派班组使用
        simplification: false, //精简工单
        designateDeptCode: "", //当前班组ID，用于转派
        completePriceFlag: false, //是否展示完工服务费
        isShow: true,
        isConfirmOrder: false,
        timer: "",
        workNum: "",
        approvalResult: false,
        showArrival: false,
        triggerConfig: [],
        currentTriggerIndex: 0,
      };
    },
    computed: {
      ...mapState(["loginInfo", "staffInfo", "addDesignatePersonnel", "isLogin"]),
      isConditionMet() {
        // 检查 workOrdeInfoDate 是否有元素
        if (!this.workOrdeInfoDate || this.workOrdeInfoDate.length === 0) {
          return false;
        }
        const firstItem = this.workOrdeInfoDate[0];
        // 检查 flowCode 条件
        const flowCodeCondition = firstItem.flowCode == 2 || firstItem.flowCode == 3;
        // 定义允许的 workTypeCode 值数组
        const allowedWorkTypeCodes = ['1', '2', '6', '9', '10', '11', '17', 'b258dc94124d4984b4d286b310cd0523',
          'f83312bc7c6b4cfdb2c5ba4accb55788'
        ];
        // 检查 workTypeCode 条件
        const workTypeCodeCondition = allowedWorkTypeCodes.includes(firstItem.workTypeCode);
        // 返回最终的条件判断结果
        return flowCodeCondition && workTypeCodeCondition;
      },
      isLargeFont() {
        return localStorage.getItem('fontSizePreference') === 'x-large' || localStorage.getItem('fontSizePreference') === 'large';
      }
    },

    methods: {
      getTriggerConfig() {
        // 先调用queryArrival接口
        this.$api.queryArrival({
          taskId: this.$route.query.id,
          hospitalCode: this.loginInfo.hospitalCode,
          unitCode: this.loginInfo.unitCode
        }).then(res => {
          console.log("queryArrival", res);
          this.showArrival = res;

          // 在获取到showArrival的值后，再获取triggerConfig
          this.$api.getTriggerConfig({
            taskId: this.$route.query.id,
            userId: this.loginInfo.staffId
          }).then(res => {
            this.triggerConfig = res;
            console.log("getTriggerConfig", this.triggerConfig);
            // 此时showArrival已经有值了，可以安全判断
            if (this.showArrival) {
              this.showTriggerConfigDialog();
            }
          });
        });
      },

      // 显示triggerConfig弹框
      showTriggerConfigDialog() {
        if (this.triggerConfig && this.triggerConfig.length > 0) {
          this.currentTriggerIndex = 0;
          this.showNextTrigger();
        }
      },

      // 显示下一个触发器内容
      showNextTrigger() {
        if (this.currentTriggerIndex < this.triggerConfig.length) {
          const currentContent = this.triggerConfig[this.currentTriggerIndex].triggerContent;
          this.$dialog.alert({
            title: '注意事项',
            message: `<div class="trigger-content-wrapper">${currentContent}</div>`,
            messageAlign: 'left',
            confirmButtonText: '已知晓',
            confirmButtonColor: '#4061d4',
            closeOnPopstate: false,
            allowHtml: true,
            getContainer:'.trigger-box'
          }).then(() => {
            this.currentTriggerIndex++;
            // 延迟显示下一个弹框
            setTimeout(() => {
              this.showNextTrigger();
            }, 300);
          });
        }
      },

      queryArrival() {
        this.$api.queryArrival({
          taskId: this.$route.query.id,
          hospitalCode: this.loginInfo.hospitalCode,
          unitCode: this.loginInfo.unitCode
        }).then(res => {
          console.log("queryArrival", res);
          this.showArrival = res;
        });
      },
      handleArrival() {
        this.$router.push({
          path: '/arrival',
          query: {
            taskId: this.$route.query.id,
            photoRequired: this.olgWorkPushNew.onsiteImageRequired
          }
        })
      },
      getConfig() {
        this.$api.getNewSysConfigParam({}).then(res => {
          console.log('获取pc配置', res)
          this.olgWorkPushNew = res.olgWorkPushNew
        })
      },
      cancelApplication() {
        this.$router.push({
          path: "/cancelApplication",
          query: {
            workNum: this.workNum
          }
        });
      },
      handleConfirm() {
        this.$api
          .confirmOrder({
            taskId: this.$route.query.id
          })
          .then(res => {
            $.toast("确认接收成功", () => {
              this.isConfirmOrder = false;
            });
          });
      },
      goback() {
        if(localStorage.getItem('workProcessing')){
          localStorage.removeItem('workProcessing');
        }
        let pageSouce = this.$route.query.pageSouce;
        console.log(pageSouce);

        if (pageSouce == "apicloud") {
          this.$YBS.apiCloudCloseFrame();
        } else if (pageSouce == "workProcessing") {
          this.$router.push({
          path: "/workbench",
        });
        } else {
          this.$router.go(-1);
        }
      },
      /**
       *获取工单任务详情信息
       */
      getOderDetailsInfo() {
        //从消息推送进入
        if (this.forwardInfo) {
          let curUserInfo = JSON.parse(sessionStorage.getItem("workOrderId"));
          this.workOrderId = curUserInfo.id;
        }
        this.timer = setInterval(() => {
          if (this.staffInfo) {
            clearInterval(this.timer);
            this.$api
              .getTaskDetail({
                taskId: this.workOrderId
              })
              .then(this.getOderDetailsInfoSucc);
          }
        }, 50);
      },
      /**
       * 开始填写处理转派信息
       */
      handleChangeOder() {
        this.$router.push({
          path: "/OrderHandle",
          query: {
            workOrderId: this.workOrderId,
            workOrdeInfoDate: this.workOrdeInfoDate,
            handleChangeOderInfo: true,
            handleComponentsTitle: "转派处理",
            workTypeCode: this.workTypeCode,
            localtionId: this.localtionId,
            itemTypeCode: this.itemTypeCode,
            designateDeptCode: this.designateDeptCode,
            completePriceFlag: this.completePriceFlag
          }
        });
        //
        // this.handleChangeOderInfo = true
        // this.handleComponentsTitle = "转派处理"
        // this.isConfirm = false //释放确定按钮
      },
      /**
       * 修改工单的逻辑
       */
      handleReviseOder() {
        sessionStorage.setItem("pageState", true);
        this.$router.push({
          path: "/reviseOder",
          query: {
            workTypeCode: this.workTypeCode,
            id: this.workOrderId,
            lastQuery: this.$route.query
          }
        });
      },
      /**
       * 获取指派人员
       */
      getDesignatePersonnel() {
        //如果有指派人员就展示，没有就展示请选择
        if (this.addDesignatePersonnel.length > 0 && this.$route.query.backParams) {
          this.havePendingPeo = true;
          this.$refs.addPersonnel.personnelVal = true;
        }
      },
      /**
       * 显示处（添加）指派人员,并显示确定按钮用于派工用  改-跳转到工单操作页面
       */
      showAddPersonnel() {
        this.$router.push({
          path: "/OrderHandle",
          query: {
            source: "pending",
            handleComponentsTitle: "工单派工",
            workOrderId: this.workOrderId,
            workOrdeInfoDate: this.workOrdeInfoDate,
            simplification: false //已派工选项卡返回此页面时展示精简工单
          }
        });
      },
      /**
       * 派工成功请求
       */
      handleSubmitVieOderClick() {
        if (!this.$refs.addPersonnel.designatePersonnel) {
          $.toast("指派人员不能为空", "text");
          return false;
        }
        this.$api
          .saveScheduling({
            id: this.workOrderId,
            userId: this.loginInfo.id,
            realName: this.loginInfo.staffName,
            designatePersonCode: this.$refs.addPersonnel.designatePersonnelId,
            designatePersonName: this.$refs.addPersonnel.designatePersonnel,
            designatePersonPhone: this.$refs.addPersonnel.designatePersonnelTel,
            appId: process.env.WxAppid, //用于区分公众号推送
            openId: this.$refs.addPersonnel.designatePersonnelOpenid,
            staffId: this.staffInfo.staffId,
            reAssignment: this.isDispatching == "0" ? "1" : "2"
          })
          .then(this.handleSubmitVieOderSucc);
      },
      /**
       * 派工成功成功回调函数
       */
      handleSubmitVieOderSucc(res) {
        let _this = this;
        $.toast("工单派工成功", function () {
          window.location.href = process.env.WX + "/personalCenter";
        });
      },
      /**
       * 时间格式化载体
       */
      timeFunc() {},
      /**
       *获取工单任务详情信息成功回调函数
       */
      getOderDetailsInfoSucc(res) {
        //通过判断角色和工单入口来源判断是否显示重新指派功能
        this.getUserRole();
        let newRes = res.map(item => {
          item.fold = false;
          return item;
        }); // 时间轴内容折叠/展开
        let baseInfo = newRes[0];
        newRes.forEach(item => {
          if (item.operationCode == 10) {
            item.itemType = baseInfo.itemType;
            item.transportName = baseInfo.transportName;
            item.sourcesDeptName = baseInfo.sourcesDeptName;
            item.transportEndLocalOfficeName = baseInfo.transportEndLocalOfficeCode;
            item.transportEndLocalOffice = baseInfo.transportEndLocalOffice;
            item.transportEndLocal = baseInfo.transportEndLocal;
            item.transportStartLocal = baseInfo.transportStartLocal;
            item.transportStartLocalOffice = baseInfo.transportStartLocalOffice;
            item.transportName = baseInfo.transportName;
            item.customtransportNum = baseInfo.customtransportNum;
            item.questionDescription = baseInfo.questionDescription;
          }
        });
        // newRes[0].sysForShort = 'BJ2023420175851681984685267' //临时写死
        this.workOrdeInfoDate = newRes;
        console.log(this.workOrdeInfoDate);

        if (this.workOrdeInfoDate[0].flowCode == 3) {
          this.isNotHanged = false;
        } else {
          this.isNotHanged = true;
        }
        let flag = false;
        if (this.workOrdeInfoDate[0].callerCode == this.staffInfo.staffId && this.workOrdeInfoDate[0].flowCode == "4") {
          flag = true;
        }
        if (this.workOrdeInfoDate[0].sourcesPhone) {
          if (
            this.workOrdeInfoDate[0].sourcesPhone == this.staffInfo.mobile &&
            this.workOrdeInfoDate[0].designatePersonCode &&
            !this.workOrdeInfoDate[0].designatePersonCode.split(",").includes(this.staffInfo.teamPersonId)
          ) {
            flag = true;
          }
        } else if (
          this.workOrdeInfoDate[0].createBy == this.staffInfo.staffId &&
          this.workOrdeInfoDate[0].designatePersonCode &&
          !this.workOrdeInfoDate[0].designatePersonCode.split(",").includes(this.staffInfo.teamPersonId)
        ) {
          flag = true;
        }
        if (flag) {
          this.$router.push({
            path: "/details",
            query: {
              id: this.workOrdeInfoDate[0].taskId,
              flowCode: this.workOrdeInfoDate[0].flowCode,
              isRedirect: true
            }
          });
          return;
        }
        if (this.workOrdeInfoDate[0].flowCode == 5 || this.workOrdeInfoDate[0].flowCode == 15 || this.workOrdeInfoDate[
            0].flowCode == 4) {
          // 跳转到deskDetails
          this.$router.push({
            path: "/deskDetails",
            query: {
              id: this.$route.query.id,
              flowcode: this.$route.query.flowcode,
              pageSouce: this.$route.query.pageSouce
            }
          });
          return;
        }
        if (this.workOrdeInfoDate[0].flowCode == 1) {
          this.$router.push({
            path: "/vieform",
            query: {
              id: this.$route.query.id,
              flowcode: this.$route.query.flowcode,
              pageSouce: this.$route.query.pageSouce,
              source: "myWorkbench"
            }
          });
          return;
        }
        setTimeout(() => {
          console.log(this.workOrdeInfoDate);
        }, 3000);
        this.isDispatching = this.workOrdeInfoDate[0].isDispatching;
        this.workTypeCode = this.workOrdeInfoDate[0].workTypeCode;

        // 工单模版类型（3运送类；6综合类）
        this.template = this.workOrdeInfoDate[0].template;

        this.localtionId = this.workOrdeInfoDate[0].localtionId;
        this.workNum = this.workOrdeInfoDate[0].workNum;
        this.approvalResult = this.workOrdeInfoDate[2].approvalResult;
        this.itemTypeCode = this.workOrdeInfoDate[0].itemTypeCode;
        this.canChangeOrder = res[0].changeOrder;
        this.designateDeptCode = res[0].designateDeptCode;
        this.timeFunc = global.timestampToTime;
        this.completePriceFlag = res[0].completePriceFlag;
        this.handleGetWorkTypeCode(this.workOrdeInfoDate[0].flowCode);
        this.oderStatus = this.workOrdeInfoDate[0].flowType;
        // console.log('isFinished', this.isFinished);

        // 2024.12.10天津肿瘤需求
        // if (!this.staffInfo.teamId.split(",").includes(res[0].designateDeptCode)) {
        //   //说明已转派到别的班组 --- 2019年10月16日10:35:37by wd 基础更改为多班组
        //   this.isFinished = true;
        // }
        if (this.workOrdeInfoDate[0].flowCode == 3 && this.forwardInfo) {
          this.isFinished = true;
          this.originStyle = true;
          this.simplification = false;
          this.oderStatus = "已挂单";
        }
        if (this.workOrdeInfoDate[0].flowCode == 4 && this.forwardInfo) {
          this.isFinished = true;
          this.originStyle = true;
          this.oderStatus = "已完工";
          this.simplification = false;
        }
        if (this.workOrdeInfoDate[0].flowCode == 5 && this.forwardInfo) {
          this.isFinished = true;
          this.originStyle = true;
          this.oderStatus = "已取消";
          this.simplification = false;
        }
        if (this.workOrdeInfoDate[0].flowCode == 1 || this.workOrdeInfoDate[0].flowCode == 2) {
          this.simplification = true;
        }
        console.log("isFinished", this.isFinished);
      },
      /**
       * 获取工单类型等参数
       * @param prams
       */
      handleGetWorkTypeCode(prams) {
        return;
        if (!JSON.parse(sessionStorage.getItem("temp"))) {
          if (prams == 3) {
            this.isFinished = true;
            this.oderStatus = "已挂单";
          }
          if (prams == 4) {
            this.isFinished = true;
            this.oderStatus = "已完工";
          }
          if (prams == 5) {
            this.isFinished = true;
            this.oderStatus = "已取消";
          }
        }
      },
      /**
       * 点击挂单按钮
       */
      handleShowPendingOder() {
        this.$router.push({
          path: "/OrderHandle",
          query: {
            inputInfo: true,
            isPendingOder: true,

            handleComponentsTitle: "挂单",
            workOrderId: this.workOrderId,
            workOrdeInfoDate: this.workOrdeInfoDate,
            simplification: false //已派工选项卡返回此页面时展示精简工单
          }
        });

        //
        // this.inputInfo = true
        // this.isPendingOder = true
        // this.isConfirm = false
        // this.timeFunc = global.timestampToTime
        // if (this.workOrdeInfoDate[0].designatePersonCode){ //判断是否已有指派人员，
        //   this.forwardInfo = true //目的是不显示指派人员处理
        // }
      },
      /**
       * 点击完工按钮
       */
      handleShowCompletedOder() {
        let isHadDesP = false; //是否多次指派中已经有指派人员了
        // this.workOrdeInfoDate.forEach((item => {
        //   if(item.operationCode == 3 && item.designatePersonCode){
        //     isHadDesP = true
        //   }
        // }))
        // if (!isHadDesP && !this.designatePersonnel) {//指派人员code为空时为一体机自动接单，显示选择指派人员

        //
        // if (!this.workOrdeInfoDate[0].designatePersonCode) {//指派人员code为空时为一体机自动接单，显示选择指派人员
        //   this.showDesignate = true
        //   this.inputInfo = true
        //   this.showConList = false
        //   this.isPendingOder = false
        //   this.isConfirm = false
        // }else{
        //   this.inputInfo = true
        //    this.showConList = false
        //    this.isPendingOder = false
        //    this.isConfirm = false
        //    // 点击完工按钮时，就可以直接完工（所有完工项均为非必填）
        //    this.disab = false
        //    this.isDis = false
        //    this.forwardInfo = true //目的是不显示指派人员处理
        // }
        let parameterObj = {
          inputInfo: true,
          isPendingOder: false,
          showCompletedConList: this.showCompletedConList,
          workTypeCode: this.workTypeCode,
          workOrderId: this.workOrderId,
          isConfirm: this.isConfirm,
          selectedComConsumables: this.selectedComConsumables,
          workOrdeInfoDate: this.workOrdeInfoDate,
          simplification: false //已派工选项卡返回此页面时展示精简工单
        };
        sessionStorage.setItem("parameterObj", JSON.stringify(parameterObj));
        if (this.workTypeCode == "16") {
          //完工
          let res = this.workOrdeInfoDate[0];
          let personCode = "";
          let personName = "";
          let personTel = "";
          if (!this.workOrdeInfoDate[0].designatePersonCode && !this.$refs.addPersonnel.designatePersonnelId) {
            $.toast("请选择指派人员", "text");
            return false;
          } else if (!this.workOrdeInfoDate[0].designatePersonCode && this.$refs.addPersonnel.designatePersonnelId) {
            //判断是否为一体机自动接单的单子来确定指派人员信息的字段
            personCode = this.$refs.addPersonnel.designatePersonnelId;
            personName = this.$refs.addPersonnel.designatePersonnel;
            personTel = this.$refs.addPersonnel.designatePersonnelTel;
          } else {
            personCode = this.workOrdeInfoDate[0].designatePersonCode;
            personName = this.workOrdeInfoDate[0].designatePersonName;
            personTel = this.workOrdeInfoDate[0].designatePersonnelTel;
          }
          // let starNum = this.$refs.finished.starNum;
          // this.imgSrc = this.$refs.finished.imgSrc;
          // let val = this.$refs.finished.value;
          let timestamp = new Date().getTime();
          let time = this.timestampToTime(timestamp);
          this.$api
            .saveComplete({
              id: this.workOrderId, //调度工单id
              workNum: res.workNum, //工单号
              userId: this.loginInfo.id, //当前用户id
              workSources: res.workSources, //工单来源
              designatePersonCode: personCode, //指派人员code
              designatePersonName: personName, //指派人员名字
              designatePersonPhone: personTel, //指派人员电话
              sourcesAddressCode: res.sourcesAddressCode, //(申报人)所在楼层code
              sourcesDeptName: res.sourcesDeptName, //使用(申报人)科室
              sourcesDeptCode: res.sourcesDeptCode, //使用(申报人)科室code
              actual: JSON.stringify(this.selectedComConsumables), //实际耗材消耗json[]
              disDegree: "", //满意度
              disFinishTime: time, //完工时间(yyyy-MM-ddHH:mm:ss)
              disFinishRemark: "", //备注说明
              imgBase: "", //完工工单附件
              appId: this.staffInfo.appList[0].appId, //用于区分公众号推送
              staffId: this.staffInfo.staffId
            })
            .then(res => {
              this.$router.push({
                path: "/orderFinished",
                query: {
                  alarmId: this.workOrdeInfoDate[0].sysForShort,
                  workOrdeInfoDate: JSON.stringify(this.workOrdeInfoDate),
                  pageSouce: this.$route.query.pageSouce
                }
              });
            });
        } else {
          this.$router.push({
            path: "/OrderHandle",
            query: {
              handleComponentsTitle: "完工",
              //  parameterObj:JSON.stringify(parameterObj)
              completePriceFlag: this.completePriceFlag,
              template: this.template,
              isMy: this.$route.query.isMy
            }
          });
        }
      },
      //物料申领
      materialRequisition() {
        this.$router.push({
          path: "/applicationForm",
          query: {
            workNum: this.workOrdeInfoDate[0].workNum,
            outboundName: '',
            outboundType: '',
          }
        });
      },
      /**
       * 数据提交
       */
      /**
       * 更多操作
       *  对应数字码：1：完工，2：挂单，3：派工，4：转派，5：修改
       */
      moreOperate() {
        let operateArr = [{
          content: "完工",
          type: "1"
        }];

        if (this.isNotHanged && this.workTypeCode != "16") {
          operateArr.push({
            content: "挂单",
            type: "2"
          });
        }
        if (this.isLeader && !this.forwardInfo) {
          operateArr.push({
            content: "派工",
            type: "3"
          });
        }
        if (this.staffInfo.leaguerType) {
          operateArr.push({
            content: "转派",
            type: "4"
          });
        }
        // operateArr.push({ content: "转派", type: "4" });
        if (this.isLeader && this.workTypeCode != 15 && this.workTypeCode != "16") {
          operateArr.push({
            content: "修改",
            type: "5"
          });
        }
        if (this.workTypeCode == "16") {
          operateArr.push({
            content: "到达",
            type: "6"
          });
          operateArr.push({
            content: "报警详情",
            type: "7"
          });
        }
        if ((this.workOrdeInfoDate[0].flowCode == 2 || this.workOrdeInfoDate[0].flowCode == 3) && this.workOrdeInfoDate[
            0]
          .workTypeCode == 1 || this.workOrdeInfoDate[0].workTypeCode == 2 || this.workOrdeInfoDate[0].workTypeCode ==
          6 || this.workOrdeInfoDate[0].workTypeCode == 9 || this.workOrdeInfoDate[0].workTypeCode == 10 || this
          .workOrdeInfoDate[0].workTypeCode == 11 || this.workOrdeInfoDate[0].workTypeCode == 17 || this
          .workOrdeInfoDate[
            0].workTypeCode == 'b258dc94124d4984b4d286b310cd0523' || this.workOrdeInfoDate[0].workTypeCode ==
          'f83312bc7c6b4cfdb2c5ba4accb55788') {
          operateArr.push({
            content: "物料领/退",
            type: "8"
          });
        }
        this.actionSheet = this.$createActionSheet({
          data: operateArr,
          onSelect: (item, index) => {
            switch (item.type) {
              case "1":
                this.handleShowCompletedOder(); //完工
                break;
              case "2":
                this.handleShowPendingOder(); //挂单
                break;
              case "3":
                this.showAddPersonnel(); //派工
                break;
              case "4":
                this.handleChangeOder(); //转派
                break;
              case "5":
                this.handleReviseOder(); //修改
                break;
              case "6":
                this.arrive(); //到达
                break;
              case "7":
                this.goAlarmDetails(); //报警详情
                break;
              case "8":
                this.materialRequisition(); //物料申领
                break;
              default:
                break;
            }
          }
        }).show();
      },

      handleSubmitClick() {
        let res = this.workOrdeInfoDate[0];
        if (this.handleChangeOderInfo) {
          //转派请求
          if (!this.$refs.changeOrder.teamInfo.name) {
            $.toast("请选择服务部门", "text");
            return;
          }
          // if (!this.$refs.changeOrder.textareaValue) {
          //   $.toast("请填写转派说明", "text");
          //   return;
          // }
          this.$api
            .toTeamsChangeTask({
              taskId: this.workOrderId,
              userId: this.loginInfo.id,
              designateDeptCode: this.$refs.changeOrder.teamInfo.id,
              designateDeptName: this.$refs.changeOrder.teamInfo.name,
              realName: this.loginInfo.staffName,
              appId: process.env.WxAppid,
              feedbackExplain: this.$refs.changeOrder.textareaValue, //转派说明
              deptCode: this.loginInfo.userOffice[0].deptCode //当前用户所属班组code
            })
            .then(res => {
              $.toast("转派成功", function () {
                window.location.href = process.env.WX + "/personalCenter";
              });
            });
        } else if (this.showDesignate) {
          //进行派工
          this.handleSubmitVieOderClick();
        } else if (this.isPendingOder) {
          //挂单
          this.pendingReasonVal = this.$refs.pendingOder.pendingVal;
          this.disEntryOrdersReasonCode = this.$refs.pendingOder.disEntryOrdersReasonCode;
          this.pendingSolutionVal = this.$refs.pendingOder.value;
          let pendingSolutionTime = this.$refs.pendingOder.ResolutionTime;
          this.$api
            .saveEntryOrders({
              id: this.workOrderId, //调度工单id
              userId: this.loginInfo.id, //当前用户id
              disEntryOrdersReason: this.pendingReasonVal,
              disEntryOrdersReasonCode: this.disEntryOrdersReasonCode,
              disEntryOrdersSolution: this.pendingSolutionVal,
              disPlanSolutionTime: pendingSolutionTime,
              realName: this.staffInfo.name,
              deptCode: this.staffInfo.officeId,
              deptName: this.staffInfo.officeName
            })
            .then(this.handleSucc);
        } else if (!this.isPendingOder) {
          //完工
          let personCode = "";
          let personName = "";
          let personTel = "";
          if (!this.workOrdeInfoDate[0].designatePersonCode && !this.$refs.addPersonnel.designatePersonnelId) {
            $.toast("请选择指派人员", "text");
            return false;
          } else if (!this.workOrdeInfoDate[0].designatePersonCode && this.$refs.addPersonnel.designatePersonnelId) {
            //判断是否为一体机自动接单的单子来确定指派人员信息的字段
            personCode = this.$refs.addPersonnel.designatePersonnelId;
            personName = this.$refs.addPersonnel.designatePersonnel;
            personTel = this.$refs.addPersonnel.designatePersonnelTel;
          } else {
            personCode = this.workOrdeInfoDate[0].designatePersonCode;
            personName = this.workOrdeInfoDate[0].designatePersonName;
            personTel = this.workOrdeInfoDate[0].designatePersonnelTel;
          }
          let starNum = this.$refs.finished.starNum;
          this.imgSrc = this.$refs.finished.imgSrc;
          let val = this.$refs.finished.value;
          let timestamp = new Date().getTime();
          let time = this.timestampToTime(timestamp);
          this.$api
            .saveComplete({
              id: this.workOrderId, //调度工单id
              workNum: res.workNum, //工单号
              userId: this.loginInfo.id, //当前用户id
              workSources: res.workSources, //工单来源
              designatePersonCode: personCode, //指派人员code
              designatePersonName: personName, //指派人员名字
              designatePersonPhone: personTel, //指派人员电话
              sourcesAddressCode: res.sourcesAddressCode, //(申报人)所在楼层code
              sourcesDeptName: res.sourcesDeptName, //使用(申报人)科室
              sourcesDeptCode: res.sourcesDeptCode, //使用(申报人)科室code
              actual: JSON.stringify(this.selectedComConsumables), //实际耗材消耗json[]
              disDegree: starNum, //满意度
              disFinishTime: time, //完工时间(yyyy-MM-ddHH:mm:ss)
              disFinishRemark: val, //备注说明
              imgBase: this.imgSrc, //完工工单附件
              appId: this.staffInfo.appList[0].appId, //用于区分公众号推送
              staffId: this.staffInfo.staffId
            })
            .then(this.handleSucc);
        }
      },
      /**
       * 成功提交数据
       */
      handleSucc(res) {
        $.toast("工单提交成功", () => {
          window.location.href = process.env.WX + "/personalCenter";
          sessionStorage.removeItem("taskDetailInfo");
          sessionStorage.removeItem("hasBeenObtainedTaskDetail");
          sessionStorage.removeItem("putConsumables");
          sessionStorage.removeItem("parameterObj");
        });
      },
      /**
       * 判断挂单是否完成（按钮显示）
       */
      judgeIsPendOderFinished() {
        if ((this.isPendingOder && this.this.isPendingOder == 2) || (!this.isPendingOder && this.btnFlagCount == 3)) {
          this.isDis = true;
          this.disab = true;
        } else {
          this.isDis = false;
          this.disab = false;
        }
      },
      /**
       * 挂单的val值监测（按钮显示）
       * @param par
       */
      handleChildChangeVal(par) {
        if (par) {
          this.isDis = false;
          this.disab = false;
        } else {
          this.isDis = true;
          this.disab = true;
        }
      },
      /**
       * 时间格式换算
       * @param timestamp 时间戳
       * @returns {string}
       */
      timestampToTime(timestamp) {
        let date = new Date(timestamp); //如果date为10位不需要乘1000
        let Y = date.getFullYear() + "-";
        let M = (date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) + "-";
        let D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
        return Y + M + D;
      },
      /**
       * 获取职工角色信息的请求
       */
      getUserRole() {
        this.axios
          .get(__PATH.ONESTOP + "/appDisUserLoginController.do?getUserRole", {
            params: {
              staffId: this.staffInfo.staffId
            },
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          })
          .then(this.handleGetUserRoleSucc);
      },
      /**
       * 获取职工角色成功回调函数
       */
      handleGetUserRoleSucc(response) {
        // let res = response.data;
        // if (res.code == 200) {
        //   //判断角色来显示按钮功能
        //   let allRole = global.leaderLists;
        //   for (let i = 0; i < res.data.length; i++) {
        //     if (allRole.includes(res.data[i])) {
        //       //角色为组长
        //       this.isLeader = true;
        //     }
        //   }
        // }
        if (this.staffInfo.leaguerType == 1) {
          this.isLeader = true;
        }
      },
      /**
       * 折叠/展开时间轴
       */
      handlefold(e, idx) {
        if (e.target.className == "header" || e.target.tagName == "I") {
          this.workOrdeInfoDate[idx].fold = !this.workOrdeInfoDate[idx].fold;
        }
      },
      arrive() {
        this.$api
          .arrive({
            taskId: this.workOrderId,
            arrivalPersonId: this.staffInfo.staffId,
            arrivalPersonName: this.staffInfo.staffName
          })
          .then(res => {});
      },
      goAlarmDetails() {
        this.$router.push({
          path: "/alarmAck",
          query: {
            alarmId: this.workOrdeInfoDate[0].sysForShort
          }
        });
      }
    },
    mounted() {

      let self = this;
      try {
        this.$YBS.apiCloudEventKeyBack(this.goback);
      } catch (error) {}
      sessionStorage.removeItem("parameterObj");
      window.addEventListener(
        "popstate",
        function (e) {
          if (self.actionSheet) self.actionSheet.hide();
        },
        false
      );
      window.onresize = function () {
        return (function () {
          self.screenHeight = document.documentElement.clientHeight;
        })();
      };
      // if (this.$route.query.source == "myWorkbench") {
      //   this.workOrderId = this.$route.query.id;

      //   //判断是否是从我的工作台挂单选项卡中进入
      //   if (this.$route.query.hanged) {
      //     //从我的工作台挂单选项卡中进入(只能完成)
      //     JSON.stringify(sessionStorage.setItem("temp", '{"hanged":true}'));
      //     //隐藏挂单按钮
      //     this.isNotHanged = false;
      //     this.isFinished = false;
      //     this.originStyle = false;
      //   } else if (!this.$route.query.hanged) {
      //     //从我的工作台已派工进入
      //     this.isNotHanged = true;
      //   }
      // }
      if (this.$route.query.style == "pendingOder") {
        this.selectedConsumables = this.$route.query.pendLists;
        this.inputInfo = this.$route.query.inputInfo;
        this.pendingSolutionVal = this.$route.query.pendingSolutionVal;
        this.pendingReasonVal = this.$route.query.pendingReasonVal;
      }
    },
    activated() {
      this.getTriggerConfig();
      this.getConfig();
      this.queryArrival();
      sessionStorage.removeItem("parameterObj");
      //这里处理actionSheet 中的按钮逻辑
      try {
        this.$YBS.apiCloudEventKeyBack(this.goback);
      } catch (error) {}
      this.handleChangeOderInfo = false;
      // 每次进入缓存的时候需要重新复制状态，目前发现进入过其他已派工再进入其他状态都为已派工，修改此状态参数
      this.isFinished = false;
      $("#education").hide();
      this.getDesignatePersonnel();
      if (location.search) {
        //从选择完耗材返回到此页面时，链接后没有参数
        if (this.$route.query.id) {
          this.workOrderId = this.$route.query.id;
          this.getOderDetailsInfo();
        }
      }

      if (this.$route.params.style == "completedOder") {
        //        从耗材选择进入
        this.selectedComConsumables = this.$route.params.comLists;
        let preArrayCom = this.selectedComConsumables;
        let arr = [];
        for (let i = 0; i < preArrayCom.length; i++) {
          arr[i] = {
            depThreeTypeName: preArrayCom[i].depThreeTypeName,
            depThreeTypeId: preArrayCom[i].depThreeTypeId,
            num: preArrayCom[i].num,
            price: preArrayCom[i].price,
            specification: preArrayCom[i].specification
          };
        }
        this.selectedComConsumables = arr;
        this.selectedConsumables = this.$route.params.comLists;
        if (this.selectedComConsumables.length > 0) {
          this.showCompletedConList = true;
          this.$refs.finished.showCompletedCon = true;
        }
        this.inputInfo = this.$route.params.inputInfo;
        this.showConList = this.$route.params.showConList;
        this.isPendingOder = this.$route.params.isPendingOder;
        this.isConfirm = this.$route.params.isConfirm;
      }
      if (this.$route.query.source == "myWorkbench") {
        this.workOrderId = this.$route.query.id;
        this.inputInfo = false;
        this.isConfirm = true;
        this.forwardInfo = false;
      }
      if (this.$route.query.from == "qualityAnalysis") {
        this.isShow = false;
      }
      console.log("isConfirm", this.isConfirm);
      // 如果工单状态是已派工，调一个接口
      if (this.$route.query.flowcode == 2 || this.$route.query.flowcode == 3) {
        this.$api
          .isConfirmOrder({
            taskId: this.$route.query.id
          })
          .then(res => {
            console.log("isConfirmOrder", res);
            this.isConfirmOrder = res;
          });
      }
    },
    watch: {
      screenHeight(val) {
        if (this.originHeight > val + 100) {
          //加100为了兼容华为的返回键
          this.isOriginHei = false;
        } else {
          this.isOriginHei = true;
        }
      }
    },
    created() {
      //判断刷新过没有  刷新
      // if (!sessionStorage.getItem("refresh")) {
      //   sessionStorage.setItem("refresh", 1);
      //   this.$router.go();
      // }
      //      先获取推送链接带过来的参数
      let url = location.search; //?unitCode=1212&hospitalCode=12&interfaceNum=0&id=1221
      let str = url.substr(1); //unitCode=1212&hospitalCode=12&interfaceNum=0&id=1221
      let strs = str.split("&"); //[]
      let theRequest = {};
      if (str.includes("ioms")) {
        //从推送消息链接进入
        this.forwardInfo = true;
        for (let i = 0; i < strs.length; i++) {
          theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
        }
        if (sessionStorage.getItem("workOrderId")) {
          sessionStorage.setItem("workOrderId", sessionStorage.getItem("workOrderId"));
        } else {
          sessionStorage.setItem("workOrderId", JSON.stringify(theRequest));
        }
      }
    },
    beforeRouteEnter(to, from, next) {
      //进入后先判断是否是登录状态

      if (to.matched.some(m => m.meta.auth)) {
        next(vm => {
          if (!vm.loginInfo) {
            // 未登录则跳转到登陆界面，query:{ Rurl: to.fullPath}表示把当前路由信息传递过去方便登录后跳转回来；
            next({
              path: "/login",
              query: {
                Rurl: to.fullPath
              }
            });
          } else {
            if (from.path == "/workbench" && to.query.flowcode == 2) {
              // 从工单列表选择已派工选项卡进入
              vm.simplification = true;
            } else if (from.path == "/workbench" && to.query.flowcode == 3) {
              // 从工单列表选择已挂单选项卡进入
              vm.simplification = false;
            } else if (from.path == "OrderHandle") {
              // 从工单页面返回设置工单样式
              vm.simplification = from.query.simplification;
            }
            next();
          }
        });
      }
    }
  };

</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import '~styles/mixins.styl';
  @import '~styles/varibles.styl';

  .wrapper {
    padding-bottom: 70px;

    .item {
      .header {
        height: 0.57rem;
        display: flex;
        align-items: center;
        margin: 0.15rem 0 0.15rem 0.3rem;
        background: #fff;
        position: relative;

        .iconfont {
          font-size: 0.4rem;
          color: $btnColor;
        }

        .title-body {
          min-width: 73%;
          max-width: 73%;
          height: calc(30px * var(--font-scale));
          display: flex;
          align-items: center;
          margin-left: 10px;
          background: #eceef8;
          padding: 0 0.24rem;
          border-radius: 0.3rem;
          // white-space: nowrap;

          .dot {
            display: inline-block;
            width: 0.09rem;
            height: 0.09rem;
            background: $btnColor;
          }

          .title {
            font-size: calc(14px * var(--font-scale));
            font-weight: 700;
            margin: 0 0.45rem 0 0.08rem;
          }

          .time {
            font-size: calc(15px * var(--font-scale));
            color: #4F87FB;
            max-width: 65%;
          }
        }

        .arrows-down {
          display: inline-block;
          width: 13px;
          height: 7px;
          background: url('~images/<EMAIL>');
          background-position: center;
          background-repeat: no-repeat;
          background-size: 100%;
          position: absolute;
          top: 50%;
          right: 20px;
          transform: translateY(-50%);
        }

        .arrows-up {
          background-image: url('~images/<EMAIL>');
        }
      }
    }

    .vie-info {
      margin: 0.2rem 0px;

      .disabledClick {
        pointer-events: none;
      }

      .text-wrapper {
        text-align: right;

        .disabled-text {
          color: $textColor;
        }
      }

      .consumablesicon {
        color: $color;
      }

      .desc {
        line-height: 28px;
        padding: 0 10px;
        box-sizing: border-box;
        background-color: #fff;
      }
    }

    .btns {
      padding: 0.16rem;
      box-sizing: border-box;
      background-color: #fff;
      border-top: 1px solid $bgColor;
      display: flex;
      justify-content: flex-end;
      position: fixed;
      bottom: 0;
      width: 100%;
      z-index: 2;
    }

    .btn {
      flex: 1;
      height: 0.88rem;
      font-size: calc(16px * var(--font-scale))!important
      margin: 0.1rem 0.16rem;
      line-height: inherit;
      padding: initial;
      white-space: nowrap;
    }

    .confirm-btn,
    .finished-btn,
    .disabled-btn,
    .disabled-sure-btn {
      width: 100%;
      line-height: 100%;
    }
  }

  .des-pers {
    margin-bottom: $marginb;
    min-height: 0.98rem;
    line-height: 1.5;
    background-color: #fff;
    padding: 0 0.32rem;
    word-break: break-word;
  }

  .notice-wrapper {
    timelineContent();
    line-height: 0.98rem;
    margin: -10px 0 -5px 0.45rem;
    box-sizing: border-box;

    .item {
      line-height: 0.98rem;
      padding: 0 0.32rem;
      font-size: 0.32rem;
      min-height: 0.98rem;
      height: auto;
      display: flex;
      align-items: baseline;

      .text-wrapper {
        min-height: 0.98rem;
        word-wrap: break-word;
        word-break: break-all;
        line-height: 1.5em;
      }
    }
  }

  .wrapper .btn-more {
    font-size: 15px;
    color: #4061d4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .trigger-content {
    padding: 15px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .dialog-footer {
    padding: 10px 15px;
  }

</style>
<style lang="scss">
  .cube-popup-content {
    border-radius: 8px 8px 0 0;
    overflow: hidden;
  }

  .cube-popup-content .cube-action-sheet-space {
    background-color: #f2f3f5;
  }

  .cube-action-sheet-item {
    color: #1d2129;
  }

  .cube-action-sheet-cancel span {
    color: #1d2129;
  }

  .cube-popup-content .border-bottom-1px:after {
    border-bottom: 1px solid #e5e6eb;
  }
  .large-font .time {
    max-width: 55%!important;
  }
</style>
