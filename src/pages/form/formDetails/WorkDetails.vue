<template>
  <div class="wrapper">
    <!--工单基本信息-->
    <base-info></base-info>
    <!--工人端和领导端抢单推送的工单详情页面，抢单-派工-挂单（领导） 抢单（工人）-->
    <deal-btns :isLeader="isLeader"></deal-btns>
  </div>
</template>

<script type=text/ecmascript-6>
  import axios from 'axios'
  import {mapState} from 'vuex'
  import BaseInfo from './components/BaseInfo'
  import DealBtns from './components/DealWorkOrderbtn'
  import globalUser from '@/common/customize/Global'
  export default {
    name: "WordDetailsInfo",
    components:{
      BaseInfo,
      DealBtns
    },
    data () {
      return {
        allRole:globalUser.userRoleLists,
        isLeader:true
      }
    },
    computed: {
      ...mapState(['staffInfo','isLogin'])
    },
    methods: {
      /**
       * 获取职工角色信息的请求
       */
      getUserRole () {
        return ['	yycleandis','yyoperator']
        /*axios.get(__PATH.ONESTOP + '/appDisUserLoginController.do?getUserRole', {
              params: {
                staffId: this.staffInfo.staffId
              }
            })
            .then(this.handleGetUserRoleSucc)*/
      },
      /**
       * 获取职工角色成功回调函数
       */
      handleGetUserRoleSucc (response) {
        let res = response.data
        if (res.code == 200) {
          return res.code
        }
      }
    },
    mounted () {
      /*for (let i = 0; i < this.getUserRole().length; i++) {
        if (this.allRole.indexOf(this.getUserRole()[i]) > -1) {
          //角色为组长
          this.isLeader = true
        }
      }*/
    },
    beforeRouteEnter(to, from, next) {   //进入后先判断是否是登录状态
      if (to.matched.some(m => m.meta.auth)) {
        next(vm => {
          // 对路由进行验证
          if (!vm.isLogin) { //0为未登录状态
            // 未登录则跳转到登陆界面，query:{ Rurl: to.fullPath}表示把当前路由信息传递过去方便登录后跳转回来；
            next({path: '/login', query: {Rurl: to.fullPath}})
          }
        });
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .wrapper
    background-color: $bgColor
</style>
