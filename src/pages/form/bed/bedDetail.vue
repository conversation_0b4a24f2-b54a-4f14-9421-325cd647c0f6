<template>
  <div class="content">
    <Header title="床位当前入住人信息" :showLeftBtn="$route.query.source === 'app'" @backFun="goback"> </Header>
    <div class="pattern">
      <div style="background-color: #fff" class="header">
        <div>
          <img class="dormImg" src="../../../assets/images/<EMAIL>" alt="" />
        </div>
        <div class="dormInfo">
          <div class="floor">{{ dormInfo.spaceLevel }}</div>
          <div class="dorm">
            <div class="dorm_l">{{ dormInfo.dormName }}-{{ dormInfo.bedName }}</div>
            <div class="dorm_r" v-if="!checkInUserInfo && !checkInInfo && !changeDormInfo">空闲</div>
          </div>
        </div>
      </div>
    </div>
    <div class="card" v-if="checkInUserInfo">
      <div class="title">当前入住人信息</div>
      <div class="item">
        <div class="item_label">人员姓名</div>
        <div class="item_value">{{ checkInUserInfo.userName }}</div>
      </div>
      <div class="item">
        <div class="item_label">性别</div>
        <div class="item_value">{{ checkInUserInfo.userSex }}</div>
      </div>
      <div class="item">
        <div class="item_label">工号</div>
        <div class="item_value">{{ checkInUserInfo.userJobNumber }}</div>
      </div>
      <div class="item">
        <div class="item_label">手机号</div>
        <div class="item_value">{{ checkInUserInfo.userPhone }}</div>
      </div>
      <div class="item">
        <div class="item_label">身份证号</div>
        <div class="item_value">{{ checkInUserInfo.userIdCard }}</div>
      </div>
      <div class="item">
        <div class="item_label">所属部门</div>
        <div class="item_value">{{ checkInUserInfo.deptName }}</div>
      </div>
      <div class="item">
        <div class="item_label">人员类型</div>
        <div class="item_value">{{ checkInUserInfo.userType }}</div>
      </div>
    </div>
    <div class="card" v-if="checkInInfo">
      <div class="title">入住信息</div>
      <div class="item">
        <div class="item_label">入住日期</div>
        <div class="item_value">{{ checkInInfo.resideTime }}</div>
      </div>
      <div class="item">
        <div class="item_label">预计搬离日期</div>
        <div class="item_value">{{ checkInInfo.predictLeaveTime }}</div>
      </div>
    </div>
    <div class="card" v-if="changeDormInfo">
      <div class="title">调换宿舍信息</div>
      <div class="item">
        <div class="item_label">原宿舍</div>
        <div class="item_value">{{ changeDormInfo.oldDormName }}</div>
      </div>
      <div class="item">
        <div class="item_label">床位名称</div>
        <div class="item_value">{{ changeDormInfo.oldBedName }}</div>
      </div>
      <div class="item">
        <div class="item_label">调换搬离日期</div>
        <div class="item_value">{{ changeDormInfo.exchangeTime }}</div>
      </div>
    </div>
    <template v-if="!checkInUserInfo && !checkInInfo && !changeDormInfo">
      <div class="empty"></div>
    </template>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      detail: {
        id: "123"
      },
      // 宿舍信息
      dormInfo: {
        spaceLevel: "",
        dormName: "",
        bedName: ""
      },
      // 当前入住人信息
      checkInUserInfo: {
        userName: "",
        userSex: "",
        userJobNumber: "",
        userPhone: "",
        userIdCard: "",
        deptName: "",
        userType: ""
      },
      // 入住信息
      checkInInfo: {
        resideTime: "",
        predictLeaveTime: ""
      },
      // 调换宿舍信息
      changeDormInfo: {
        oldDormName: "",
        oldBedName: "",
        exchangeTime: ""
      }
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getDetail(this.$route.query.id);
  },
  methods: {
    //上次抄表详情
    getDetail(id) {
      this.$api.bedUserInfo({ bedId: id }).then(res => {
        this.dormInfo = res.dormInfo;
        this.checkInUserInfo = res.checkInUserInfo;
        this.checkInInfo = res.checkInInfo;
        this.changeDormInfo = res.changeDormInfo;
      });
    },

    goback() {
      // this.$router.go(-1);
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  overflow: auto;
  background-color: #f2f4f9;

  .pattern {
    padding: 10px 16px;
    background-color: #fff;

    .header {
      display: flex;
      height: 45px;
      .dormImg {
        width: 45px;
        height: 45px;
        margin-right: 16px;
      }

      .dormInfo {
        width: calc(100% - 45px);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .floor {
          width: 90%;
          white-space: nowrap; // 强制一行显示
          overflow: hidden; // 超出隐藏
          text-overflow: ellipsis; // 省略号

          // line-height: 25px;
          font-weight: 300;
          color: #4e5969;
        }

        .dorm {
          width: 95%;

          line-height: 25px;
          display: flex;
          justify-content: space-between;
          .dorm_l {
            width: 75%;
            white-space: nowrap; // 强制一行显示
            overflow: hidden; // 超出隐藏
            text-overflow: ellipsis; // 省略号
            color: #1d2129;
            font-size: 18px;
            font-weight: bold;
          }
          .dorm_r {
            width: 40px;
            text-align: center;
            border-radius: 2px 2px 2px 2px;
            background: #e8ffea;
            color: #00b42a;
            font-size: 14px;
          }
        }
      }
    }
  }
  .card {
    margin-top: 10px;
    background: #fff;
    padding: 16px;

    .title {
      font-weight: bold;
      line-height: 25px;
      color: #1d2129;
      margin-bottom: 10px;
    }

    .title::before {
      content: "i";
      color: #3562db;
      background-color: #3562db;
      margin-right: 8px;
    }
    .item {
      padding: 5px 0;
      height: 22px;
      display: flex;
      align-items: center;
      &_label {
        width: 100px;
        color: #4e5969;
      }
      &_value {
        color: #1d2129;
      }
    }
  }
  .empty {
    height: calc(100% - 84px - 62px - 10px);
    margin-top: 10px;
    background: #fff url("../../../assets/images/<EMAIL>") no-repeat center center / 180px 130px;
  }
}
</style>
