<template>
  <div class="content">
    <Header title="抄表管理" @backFun="goback"> </Header>
    <div class="pattern">
      <div style="background-color: #fff">
        <div class="child">
          <span class="child-text">建筑楼层</span>
          <span class="child-value">{{ baseForm.spaceLevel || "-" }}</span>
        </div>
        <div class="child">
          <span class="child-text">宿舍名称</span>
          <span class="child-value">{{ baseForm.dormName || "-" }}</span>
        </div>
      </div>
    </div>

    <div class="pattern" style="margin-top: 0.1875rem" v-if="baseForm.dormName">
      <div class="title">上次抄表信息</div>
      <div class="child">
        <span class="child-text">电表抄表数</span>
        <span class="child-value">{{ oldInfo.powerNum || "-" }}kw·h</span>
      </div>
      <div class="child">
        <span class="child-text">水表抄表数</span>
        <span class="child-value">{{ oldInfo.waterNum || "-" }}m³</span>
      </div>
      <div class="child">
        <span class="child-text">电表剩余金额</span>
        <span class="child-value">{{ oldInfo.surplusPowerPrice || "-" }}元</span>
      </div>
      <div class="child">
        <span class="child-text">电表照片</span>
        <van-image width="100" height="100" :src="oldInfo.powerImage" error-icon="photo-o" @click="dkImg(oldInfo.powerImage)" />
      </div>

      <div class="child">
        <span class="child-text">水表照片</span>
        <van-image width="100" height="100" :src="oldInfo.waterImage" error-icon="photo-o" @click="dkImg(oldInfo.waterImage)" />
      </div>
      <div class="child">
        <span class="child-text">类型</span>
        <span class="child-value">{{ oldInfo.recordFormTypeName || "-" }}</span>
      </div>
      <div class="child">
        <span class="child-text">抄表日期</span>
        <span class="child-value">{{ oldInfo.recordFormDate || "-" }}</span>
      </div>
      <div class="child" v-if="oldInfo.recordFormType === '2' || oldInfo.recordFormType === '3'">
        <span class="child-text">{{ oldInfo.recordFormType === "2" ? "入住人员" : "搬离人员" }}</span>
        <span class="child-value">{{ oldInfo.userName || "-" }}</span>
      </div>
    </div>
    <div class="pattern2" style="margin-top: 0.1875rem">
      <div class="title title2">本次抄表信息</div>
      <van-form @submit="onSubmit" label-width="120px">
        <van-field required type="number" v-model="newInfo.powerNum" label="电表抄表数" placeholder="请输入" :rules="[{ required: true, message: '请输入电表数' }]">
          <template #extra> kw·h </template>
        </van-field>
        <van-field required type="number" v-model="newInfo.waterNum" label="水表抄表数" placeholder="请输入" :rules="[{ required: true, message: '请输入水表数' }]">
          <template #extra> m³ </template>
        </van-field>
        <van-field type="number" v-model="newInfo.surplusPowerPrice" label="电表剩余金额" placeholder="请输入">
          <template #extra> 元 </template>
        </van-field>
        <van-field label="电表照片">
          <template #input>
            <van-uploader
              :before-read="beforeRead"
              :after-read="e => afterRead(e, 'power')"
              v-model="powerFileList"
              :max-size="maxSize * 1024 * 1024"
              @oversize="onOversize"
              @delete="e => beforeDel(e, 'power')"
              upload-text="添加图片"
              accept="image/png, image/jpeg, image/jpg"
              :disabled="powerMsg == '上传中'"
              max-count="1"
            >
              <template v-if="powerMsg == '上传成功'" #preview-cover="{ file }">
                <!-- <div class="preview-cover van-ellipsis">上传成功</div> -->
              </template>
            </van-uploader>
            <div class="upload-tip">注：支持上传50M以内格式为jpg/jpeg/png的照片</div>
          </template>
        </van-field>
        <van-field label="水表照片">
          <template #input>
            <van-uploader
              :before-read="beforeRead"
              :after-read="e => afterRead(e, 'water')"
              v-model="waterFileList"
              :max-size="maxSize * 1024 * 1024"
              @oversize="onOversize"
              @delete="e => beforeDel(e, 'water')"
              upload-text="添加图片"
              accept="image/png, image/jpeg, image/jpg"
              :disabled="waterMsg == '上传中'"
              max-count="1"
            >
              <template v-if="waterMsg == '上传成功'" #preview-cover="{ file }">
                <!-- <div class="preview-cover van-ellipsis">上传成功</div> -->
              </template>
            </van-uploader>
            <div class="upload-tip">注：支持上传50M以内格式为jpg/jpeg/png的照片</div>
          </template>
        </van-field>
        <van-field
          required
          readonly
          clickable
          :value="newInfo.recordFormTypeName"
          label="类型"
          right-icon="arrow"
          placeholder="请选择类型"
          @click="showType = true"
          :rules="[{ required: true, message: '请选择类型' }]"
        />
        <van-popup v-model="showType" position="bottom">
          <van-picker value-key="name" show-toolbar :columns="recordFormTypeList" @confirm="typeConfim" @cancel="showType = false" />
        </van-popup>
        <van-field
          v-if="newInfo.recordFormType === '2' || newInfo.recordFormType === '3'"
          required
          readonly
          clickable
          :value="newInfo.inOUtUserName"
          :label="`${newInfo.recordFormType === '2' ? '入住' : '搬离'}人员`"
          right-icon="arrow"
          placeholder="请选择人员"
          @click="showUser = true"
          :rules="[{ required: true, message: '请选择人员' }]"
        />
        <van-popup v-model="showUser" position="bottom">
          <van-picker value-key="userName" show-toolbar :columns="userList" @confirm="userConfim" @cancel="showUser = false" />
        </van-popup>

        <div style="margin: 16px">
          <van-button color="#3562DB" block type="info" native-type="submit">提交</van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Vue from "vue";
import { Image as VanImage } from "vant";
import { ImagePreview } from "vant";
import YBS from "@/assets/utils/utils.js";

Vue.use(VanImage);
export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  data() {
    return {
      showType: false,
      showUser: false, //
      recordFormTypeList: [
        {
          id: "1",
          name: "日常抄表"
        },
        {
          id: "2",
          name: "人员入住抄表"
        },
        {
          id: "3",
          name: "人员搬离抄表"
        }
      ],
      userList: [],

      powerFileList: [],
      waterFileList: [],
      powerMsg: "",
      waterMsg: "",

      maxSize: 50,

      newInfo: {
        powerNum: "",
        waterNum: "",
        surplusPowerPrice: "",
        recordFormType: "",
        powerImage: "",
        waterImage: "",
        inOUtUserId: ""
      },
      oldInfo: {
        powerNum: "",
        waterNum: "",
        surplusPowerPrice: "",
        recordFormType: "",
        powerImage: "",
        waterImage: "",
        inOUtUserId: ""
      },
      baseForm: {
        spaceLevel: "111",
        dormName: "222"
      }
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    // 获取储存、相机权限
    if (!YBS.hasPermission("storage")) {
      YBS.reqPermission(["storage"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          if (!YBS.hasPermission("camera")) {
            YBS.reqPermission(["camera"], function(ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              }
            });
            return;
          } else {
          }
        }
      });
      return;
    }
    if (!YBS.hasPermission("camera")) {
      YBS.reqPermission(["camera"], function(ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
        }
      });
      return;
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.baseForm = this.$route.query;
    if (this.baseForm.dormId) {
      // 请求上次的抄表信息
      this.getLastDialDetail(this.baseForm.dormId);
    }
  },
  methods: {
    //上次抄表详情
    getLastDialDetail(id) {
      this.$api.dialInfo({ id: id }).then(res => {
        this.oldInfo = res;
      });
    },
    typeConfim(item) {
      this.newInfo.recordFormType = item.id;
      this.newInfo.recordFormTypeName = item.name;
      if (item.id !== "1") {
        this.getUserList(item.id);
      }
      this.showType = false;
    },

    // 获取人员列表
    getUserList(val) {
      this.$api.userList({ dormId: this.baseForm.dormId, inOutType: val === "2" ? "1" : "0" }).then(res => {
        this.userList = res;
      });
    },
    // 人员选择确认
    userConfim(item) {
      this.newInfo.inOUtUserId = item.userId;
      this.newInfo.inOUtUserName = item.userName;
      this.showUser = false;
    },
    onSubmit() {
      let params = {
        ...this.baseForm,
        ...this.newInfo
      };
      this.$api.addDial(params).then(res => {
        this.$toast.success("添加成功");
        this.goback();
      });
    },
    onOversize() {
      this.$toast.fail(`图片大小不能超过${this.maxSize}M`);
    },

    // 上传之前
    beforeRead(file) {
      const isJPG = ["image/png", , "image/jpg", "image/jpeg"].includes(file.type);
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isJPG) {
        this.$toast.fail("上传图片只能是 png,jpg,jpeg 格式!");
      }
      if (!isLt50M) {
        this.$toast.fail("上传图片大小不能超过 50MB!");
      }
      return isJPG && isLt50M;
    },
    afterRead(data, type) {
      (data.status = "uploading"), (data.message = "上传中");
      this[`${type}Msg`] = "上传中";
      const { file } = data;
      this.handleUploadImg(file, data, type);
    },
    handleUploadImg(file, data, type) {
      let formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      axios({
        method: "post",
        url: __PATH.DORM + "/minio/upload",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: localStorage.getItem("token")
        }
      }).then(res => {
        if (res.data.code == 200) {
          this[`${type}Msg`] = "上传成功";
          (data.status = "done"), (data.message = "上传成功");
          this.newInfo[`${type}Image`] = res.data.data.fileKey;
        } else {
          this[`${type}Msg`] = "上传失败";
          (data.status = "failed"), (data.message = "上传失败");
        }
      });
    },
    beforeDel(e, type) {
      this.newInfo[`${type}Image`] = "";
    },
    dkImg(img) {
      ImagePreview([img]);
    },
    goback() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="stylus" scoped>
.content {
  height: 100%;
  overflow: auto;
  background-color: #F2F4F9;

  .pattern {
    padding: 10px 16px;
    background-color: #fff;
  }

  .pattern2 {
    padding: 10px 0;
    background-color: #fff;
  }
}

.child {
  font-size: 14px;
  margin-top: 0.1875rem;
  display: flex;

  >span {
    line-height: 20px;
  }

  .child-text {
    font-size: 14px;
    width: 120px;
    color: #4E5969;
    // color:#b5bac0 !important;
  }

  .child-value {
    color: #1D2129;
  }
}

.title {
  font-weight: bold;
  line-height: 25px;
  color: #1D2129;
}

.title::before {
  content: 'i';
  color: #3562DB;
  background-color: #3562DB;
  margin-right: 5px;
}

.title2 {
  padding-left: 15px;
}

.upload-tip {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #696969;
  line-height: 20px;
}
</style>
