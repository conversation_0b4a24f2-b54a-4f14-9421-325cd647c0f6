<template>
  <div class="content">
    <Header title="抄表管理" @backFun="goback"></Header>
    <van-search v-model="dormName" placeholder="请输入宿舍名称" @input="onSearch" />
    <div v-if="list.length > 0">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div v-for="(item, index) in list" :key="index" class="item">
            <van-empty description="暂无数据" v-if="!item" />
            <div v-else @click="goTaskDetail(item)">
              <div class="child" style="display: flex; justify-content: space-between">
                <div>
                  <span><img src="../../../assets/images/宿舍.png" alt=""/></span>&nbsp;
                  <span class="child-value">{{ item.dormName || "-" }}</span>
                </div>
                <div><van-icon name="arrow" color="#C9CDD4" /></div>
              </div>
              <div class="child">
                <span class="child-text">建筑楼层：</span>
                <span class="child-value">{{ item.spaceLevel || "-" }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dormName: "", // 宿舍名称
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      params: {
        page: 1,
        size: 10
      },
      list: []
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.params.page = 1;
    this.getDormList();
    window.onscroll = this.isscroll;
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    goTaskDetail(row) {
      let params = {
        dormId: row.dormId,
        dormName: row.dormName,
        spaceLevel: row.spaceLevel,
        spaceLevelCode: row.spaceLevelCode
      };
      this.$router.push({
        path: "/meterReadingDetailed",
        query: params
      });
    },
    onRefresh() {
      this.params.page = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getDormList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.params.page++;
      this.getDormList();
    },
    getDormList() {
      let data = {
        dormName: this.dormName,
        ...this.params
      };
      this.loading = true;
      this.$api.dormDialList(data).then(res => {
        this.loading = false;
        res.records.forEach(item => {
          this.list.push(item);
        });
        if (this.list.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    onSearch() {
      this.list = [];
      this.finished = false;
      this.refreshing = false;
      this.params.page = 1;
      this.getDormList();
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="stylus" scoped>
.content {
  height: 100%;
  overflow: auto;
  background-color: #F2F4F9;

  .item {
    background-color: #fff;
    margin: 0.1875rem;
    padding: 0.3125rem;
    border-radius: 0.125rem;
    font-size: 16px;
    color: #1D2129;
    font-family: PingFang SC-Medium;
    line-height: 20px;

    .accoutName {
      display: flex;
      justify-content: space-between;

      >div:nth-child(1) {
        flex: 1;
      }
    }

    .child {
      margin-top: 0.1875rem;
      display: flex;

      .child-text {
        color: #b5bac0 !important;
      }
    }
  }
}

.notList {
  position: relative;
  height: calc(90% - 1.24rem);

  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url('../../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
    background-size: 100% auto;

    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
