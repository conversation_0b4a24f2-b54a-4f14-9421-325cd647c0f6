<template>
  <div class="wrapper">
    <div class="content">
      <!-- 工单提醒 -->
      <!-- <div class="reminder" v-if="hint">
        <img class="reminder-img" src="@/assets/images/reminder.png" alt="">
        <span class="reminder-text">{{hint}}</span>
      </div> -->

      <!--手机号&&验证码-->
      <!--已开通的医院才允许游客报修-->
      <div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">手机号</label>
          </div>
          <phone-text
            :isShowPhoneText="false"
            @getSmsCode="handleGetSmsCode"
            @getPhoneNum="getPhoneNum"
            style="margin-right:-0.6em;padding:0"
          ></phone-text>
        </div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">验证码</label>
          </div>
          <input
            class="weui-input"
            maxlength="6"
            type="text"
            v-model="iptSmsCode"
            placeholder="请输入验证码"
          />
        </div>
      </div>

      <!--申报描述-->
      <div class="weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea"
              :placeholder="placeholder"
              maxlength="500"
              rows="5"
              ref="textarea"
              v-model="value"
              @keydown="keydown($event)"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
        <upload-voice
          ref="voices"
          @getvVoiceParams="getVoice"
          @voiceFlag="deleteVoice"
        ></upload-voice>
      </div>

      <!--图片上传-->
      <upload-image
        class="bottom"
        style="position:relative"
        ref="imgs"
        v-bind:num="this.amount"
        @getImgParams="getImages"
        @deleteImg="deleteImg"
      ></upload-image>
    </div>

    <div
      class="submit-btn"
      :class="{active: canShow}"
    >
      <button
        class="weui-btn weui-btn_primary"
        @click="submitAfterVrification"
      >提交</button>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
const wx = require("weixin-js-sdk");
import utils from "@/utils/Global";
import { mapState } from "vuex";
import UploadImage from "@/common/uploadImg/uploadImg";
import UploadVoice from "@/common/uploadVoice/UploadVoice";
import Popups from "@/common/customize/Popups";
import PhoneText from "@/common/PhoneText";
export default {
  name: "RepairAtWill",
  components: {
    UploadImage,
    UploadVoice,
    Popups,
    PhoneText
  },
  data() {
    return {
      phone: "", //需联系电话和临时用户需发送验证码的手机号
      value: "", //申报描述
      canShow: false,
      voiceParams: "",
      imagesParams: [],
      imageConfig: [], //处理图片的mediaId和ossUrl
      imgFlag: false,
      accessToken: "",
      isOnlyVoice: false,
      first: true, //是否首次打开此页面，只有第一次才自动定位
      smsCode: "", //临时用户需发送验证码，验证码成功后才可以报修
      iptSmsCode: "", //临时用户需发送验证码，验证码成功后才可以报修(用户输入的)
      isRegister: 0, //临时用户报修的医院是否为开放状态
      requireCode: 2, //	移动申报自动派工标识(默认关闭-非必填)
      isSubmit: true, //节流标示
      placeholder:
        "请输入您要申报的内容描述、语音，字数在500字以内，语音限制60秒",
      hint:"" ,//工单配置提醒
      unitCode: "",
      hospitalCode: "",
      amount:{    //上传图片限制
        quantity:3,
        capital:"三"
      }
    };
  },
  computed: {
    ...mapState([
      // "loginInfo",
    ])
  },
  methods: {
    /**
     * 获取手机短信验证码
     */
    handleGetSmsCode(smscode, phone) {
      this.phone = phone;
      this.smsCode = smscode;
    },
    /**
     * 当电话号改变时接收电话号码
     */
    getPhoneNum (num) {
      this.phone = num
    },
    handleTextareaBlurEvent() {
      let scrollHeight =
        document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 获取录音路径
     * @param params
     */
    getVoice(params) {
      if (this.isOnlyVoice) {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode
                ? this.hospitalCode
                : "BJSJTYY",
              unitCode: this.unitCode
                ? this.unitCode
                : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params,
              type: "1"
            })
          )
          .then(res => {
            this.voiceParams = res.data.data.ossUrl;
            this.requiredParameter();
          });
      }
    },
    /**
     * 删除录音
     */
    deleteVoice() {
      this.voiceParams = "";
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode
              ? this.hospitalCode
              : "BJSJTYY",
            unitCode: this.unitCode
              ? this.unitCode
              : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: this.voiceParams
          })
        )
        .then(res => {});
    },
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode
              ? this.hospitalCode
              : "BJSJTYY",
            unitCode: this.unitCode
              ? this.unitCode
              : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode
                ? this.hospitalCode
                : "BJSJTYY",
              unitCode: this.unitCode
                ? this.unitCode
                : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
            // this.imagesParams.push(res.data.data.ossUrl);
            // this.imageConfig.push(res.data.data);
          });
      });
    },
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    },
    /**
     * 处理提交按钮事件
     */
    handleSubmitClick() {
      if (
        this.$refs.imgs.imgServerId.length != 0 &&
        this.$refs.voices.hasVoice != "recorded"
      ) {
        //上传图片、没有录音
        let imgLen = () => {
          if (this.imagesParams.length != 0) {
            this.requiredParameter();
          } else {
            setTimeout(() => {
              imgLen();
            }, 500);
          }
        };
        imgLen();
      } else if (
        this.$refs.imgs.imgServerId.length != 0 &&
        this.$refs.voices.hasVoice == "recorded"
      ) {
        //上传图片、录音
        let imgLen = () => {
          if (this.imagesParams.length != 0) {
            this.isOnlyVoice = true;
            this.$refs.voices.saveVoice();
          } else {
            setTimeout(() => {
              imgLen();
            }, 500);
          }
        };
        imgLen();
      } else if (
        this.$refs.imgs.imgServerId.length == 0 &&
        this.$refs.voices.hasVoice == "recorded"
      ) {
        //不需上传图片、但有录音
        this.isOnlyVoice = true;
        this.$refs.voices.saveVoice();
      } else if (
        this.$refs.imgs.imgServerId.length == 0 &&
        this.$refs.voices.hasVoice == "unvoice"
      ) {
        //不需上传图片、没有录音
        this.requiredParameter();
      }
    },
    /**
     * 获取签名
     */
    getConfigParams() {
      // 进行签名的时候  Android 不用使用之前的链接， ios 需要
      let signLink = /(Android)/i.test(navigator.userAgent)
        ? location.href.split("#")[0]
        : window.entryUrl;
      let paramValue = {
        localUrl: signLink,
        appId: process.env.WxAppid
      };
      this.axios
        .get(process.env.API_HOST + "/accessToken/getSignature", {
          params: paramValue
        })
        .then(this.setAllConfig);
    },
    /**
     * 设置微信config参数
     * @param res
     */
    setAllConfig(res) {
      this.$refs.voices.setConfig(res);
      this.$refs.voices.initRecord();
      this.$refs.imgs.setConfig(res);
      this.accessToken = res.data.data.accessToken;
    },
    /**
     * 提交前判断
     */
    getHttp() {
      this.submitFun();
    },
    /**
     * 工单提交请求函数
     */
    submitFun() {
        if (!this.isSubmit) {
          $.toast("该工单已经提交,请勿重复提交", "text");
          return;
        }
        if (this.$refs.imgs.imgLocalIds.length != this.imagesParams.length) {
          $.toast("请等待图片上传完毕", "text");
          return;
        }
        this.isSubmit = false;
        $.showLoading("提交中…");

        let param = null;

        let params = {
          unitCode: this.unitCode,
          hospitalCode: this.hospitalCode,
          workTypeCode: "11", //工单类型：综合维修
          workSources: "1", //申报来源：App
          typeSources: "1", //随手拍(医务报修)
          type: 2, //区分院内/院外

          questionDescription: this.value,
          accessToken: this.accessToken,
          contactNumber: this.phone, //未登录扫描报修加入的联系人电话
          dispatchingConfig: this.requireCode //	移动申报自动派工标识

        };
        params.callerTape = this.voiceParams;
        params.attachment = JSON.stringify(this.imagesParams);

        param = this.$qs.stringify(params);
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat",
            param
          )
          .then(this.handleRequestSucc);

    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      if (res.data.code == "200") {
        $.alert("感谢您的监督反馈，我们会及时处理！", "提交成功", function() {
          window.wx.closeWindow();
        });
      } else {
        // $.toast(res.data.message, "text");
        $.alert(res.data.message, "提示", function() {
          window.wx.closeWindow();
        });
        if (res.data.code == 40001) {
          this.voiceParams = "";
          this.$refs.voices.handleDelVoiceClick();
        }
      }
      $.hideLoading();
    },
    /**
     * 必选项验证
     */
    requiredParameter() {
      let reg = /^((13|14|15|17|18)[0-9]{9})$/;
      if (!this.phone) {
        if (this.phone == "") {
          $.toast("请填写手机号及验证码", "text");
          return
        } else if (this.phone.length<11) {
          //2020-5-19 修改验证方式为11位长度
          $.toast("请输入正确的手机号", "text");
          return
        } else if (this.iptSmsCode == "") {
          $.toast("请输入验证码", "text");
          return
        }
      } else if (this.iptSmsCode == "") {
        $.toast("请输入验证码", "text");
          return
      } else if (this.iptSmsCode != this.smsCode) {
        $.toast("验证码不正确", "text");
          return
      }
      if (!this.value && !this.voiceParams) {
        $.toast("请填写申报描述或录入语音", "text");
        return false;
      }  else {
        this.getHttp();
      }
    },
    /**
     * 校验联系电话的格式
     */
    submitAfterVrification() {
      if (this.phone.length<11 && this.phone != "") {
        $.toast("请输入正确的电话号", "text");
      } else {
        this.handleSubmitClick();
      }
    },

    /**
     * 判断该医院是否开放注册
     */
    getHospitalIsRisterByCode(unitCode, hospitalCode) {
      this.$api
        .getHospitalIsRisterByCode({
          unitCode: unitCode,
          hospitalCode: hospitalCode
        })
        .then(res => {
          this.isRegister = res.isRegister;
        });
    },
    //空格校验
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    }
  },
  mounted() {
    //存储appId
    localStorage.wxAppId = process.env.WxAppid;
    let routeQuery = this.$route.query;
    this.unitCode = routeQuery.unitCode;
    this.hospitalCode = routeQuery.hospitalCode;

  },
  activated() {
    //设置title
    document.title = "随手拍";
    this.getConfigParams();
  },
  watch: {
    value(value, oldValue) {
      this.value = this.value.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,'');
      if (oldValue && !value) {
        this.$refs.textarea.click();
      }
    },
    hospitalInfo(info) {
      this.getHospitalDispatchingConfig(info.hospitalCode, info.unitCode);
    }
  },
  beforeRouteEnter(to, from, next) {
    if (to.matched.some(m => m.meta.auth)) {
      next(vm => {
        if (from.path == "/hospListNew") {
          vm.first = false;
        }
        vm.getConfigParams();
      });
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper {
  height: 100%;
  background-color: $bgColor;

  .content {
    box-sizing: border-box;
    min-height: 100%;
    padding-bottom: 76px !important;
    background-color: $bgColor;

    .reservation-btns {
      marginBottom20();
      text-align: center;

      span {
        display: inline-block;
        width: 1.24rem;
        margin: 0 0.36rem;
      }

      .active {
        color: $color;
        border-bottom: 2px solid $color;
      }
    }

    .other-location-name {
      marginBottom20();

      .ipt-content {
        justify-content: flex-end;
        line-height: 1.2;
        text-align: left;
      }
    }

    .adjustable {
      padding: 0 0.32rem;

      > div {
        text-align: center;
      }

      img {
        width: 0.72rem;
      }

      .down {
        transition: all 0.3s;
      }

      .up {
        transform: rotateZ(180deg);
        transition: all 0.3s;
      }
    }

    .up-content {
      transform: rotateX(90deg);
      transform-origin: center top;
      height: 0;
      transition: height 0.3s;
      transition: all 0.3s;
    }

    .down-content {
      transition: all 0.3s;
      transform-origin: center top;
      margin-bottom: 10px;
    }

    .er-level {
      display: flex;
      align-items: center;
      itemBaseStyle();

      .weui-cells_checkbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .redio-content {
          display: flex;
          color: $contentColor;

          .declare-attr {
            padding: 0;
          }
        }
      }
    }

    .hosp {
      marginBottom20();
    }

    .item-style {
      itemBaseStyle();

      .login-tips {
        line-height: 1;
        white-space: nowrap;
        font-size: 0.28rem;
        font-style: italic;
        color: $color;
      }
    }

    .matter-style {
      min-height: 0.98rem;
      line-height: initial;
    }

    .desc-form {
      margin-top: 0;
      background-color: #fff;

      .desc {
        padding: 0.32rem 0.32rem 0;
        font-size: 0.28rem;
        line-height: 1.5em;
        margin-bottom: 0.2rem;
        color: $contentColor;

        textarea {
          display: inline-block;
          text-indent: 2em;
          font-size: 0.3rem;
        }
      }
    }

    .tips {
      color: $contentColor;
      text-align: center;
      margin: 0.6rem 0;

      .key-tips {
        color: $color;
      }
    }
  }
}

.btn {
  padding: 0 1.12rem 0.3rem;
  position: relative;
  user-select: none;

  .delete-voice {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.5rem;
  }

  .play-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
    }
  }
}

.recording {
  position: fixed;
  z-index: 2;
  top: 2rem;
  left: 50%;
  margin-left: -1.2rem;

  img {
    width: 2.4rem;
    opacity: 0.7;
  }
}

.title-content {
  itemBaseStyle();
  margin-top: $marginb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ipt-content {
  text-align: right;
  display: flex;
  align-items: center;
  color: #999;
}

.matter-ipt {
  display: flex;
  justify-content: flex-end;

  .ipt-pos {
    position: relative;
    width: 100%;

    .matterInput {
      display: none;
    }

    span {
      font-family: serif;
      text-align: left;
      display: flex;
      font-size: 15px;
    }
  }
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;

  .img-wrapper {
    flex: 1;
    width: 0;
    margin: $marginb;
    position: relative;

    img {
      width: 100%;
    }

    .icon-img {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
}

.popups-content {
  width: 80%;
  margin: 0 auto;
  position: fixed;
  max-width: 300px;
  border-radius: 3px;
  overflow: hidden;

  & > div {
    margin: 0 auto;
    background: #fff;
    padding-bottom: 15px;
    border-bottom: 1px solid #efefef;
    height: 1.18rem;
    align-items: flex-end;
    box-sizing: border-box;
  }

  .cfm-phone-num-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 48px;
    font-size: 18px;
    padding: 0;
    color: #38C7C4;
  }
}

.play-void-img {
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
}

.title {
  font-size: 0.32rem;
}

.er-level-redio {
  display: flex;
  background-color: #fff;
  font-size: 0.3rem;
  color: $contentColor;
}

.redio-content {
  position: relative;

  .select-time {
    position: absolute;
    left: 1rem;
    bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 5em);

    .text {
      width: 50px;
      line-height: initial;
    }

    .ipt {
      flex: 1;
      text-align: right;
    }
  }

  .textColor {
    color: #999;
  }

  .disPointer {
    pointer-events: none;
  }
}

.hint {
  display: inline-block;
  margin: 0 20px;
}

.reminder{
  height:0.68rem;
  background:#FAECE9;
  padding-left:.32rem;
  line-height:.68rem;
  .reminder-img{
    width:.28rem;
  }
  .reminder-text{
    margin-left:.24rem;
    color:#FF7859;
    font-size:.26rem;
  }

}

.ellipsis{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}


</style>
