<template>
  <div class="wrapper">
    <Header title="随手拍" @backFun="goback"></Header>
    <div class="content">
      <!-- 工单提醒 -->
      <!-- <div class="reminder" v-if="hint">
        <img class="reminder-img" src="@/assets/images/reminder.png" alt="">
        <span class="reminder-text">{{hint}}</span>
      </div> -->

      <!--手机号&&验证码-->
      <!--已开通的医院才允许游客报修-->
      <!-- <div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">手机号</label>
          </div>
          <phone-text
            :isShowPhoneText="false"
            @getSmsCode="handleGetSmsCode"
            @getPhoneNum="getPhoneNum"
            style="margin-right:-0.6em;padding:0"
          ></phone-text>
        </div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">验证码</label>
          </div>
          <input
            class="weui-input"
            maxlength="6"
            type="text"
            v-model="iptSmsCode"
            placeholder="请输入验证码"
          />
        </div>
      </div> -->
      <!--申报描述-->
      <div class="weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea"
              :placeholder="placeholder"
              maxlength="500"
              rows="5"
              ref="textarea"
              v-model="value"
              @keydown="keydown($event)"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
        <sounds-recording v-if="h5Mode == 'apicloud'" @getRecord="getRecord" @getRecordFile="getRecordFile" @getDownLoad="getDownLoad" ref="voices" />
        <upload-voice v-else ref="voices" @getvVoiceParams="getRecord" @getVoiceLocalId="translateVoice"></upload-voice>
      </div>

      <!-- 服务区域 -->
      <div class="weui-cell border-rightbottom item-style">
        <div class="weui-cell__hd">
          <label class="weui-label title star">服务区域</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input @click="showAreaPicker" class="weui-input ipt ellipsis right-aligned-input" type="text" readonly v-model="areaVal" placeholder="请选择服务区域" />
          <van-icon style="margin: 0 8px" name="arrow" />
          <van-icon style="margin-right: 10px" name="search" @click="toSelectArea" />
          <span class="iconfont icon-saomiao" @click="scanRepair" style="color: #4e5969; padding-right: 10px; font-size: 20px"></span>
        </div>
      </div>
      <!-- 服务房间 -->
      <div class="weui-cell border-rightbottom item-style">
        <div class="weui-cell__hd">
          <label class="weui-label title star">服务房间</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input class="weui-input ipt" readonly type="text" @click="toSelectSite" v-model="localtionPlaceName" placeholder="请选择服务房间" />
          <van-icon style="margin: 0 8px" name="arrow" />
        </div>
      </div>
      <van-popup v-model="showAreaPickerFlag" position="bottom">
        <div class="search-box">
          <van-search v-model="areaSearchValue" placeholder="请输入搜索关键词" />
        </div>
        <van-picker show-toolbar :columns="filteredAreaList" value-key="showname" @confirm="onAreaConfirm" @cancel="showAreaPickerFlag = false" title="选择服务区域" />
      </van-popup>

      <div class="weui-cell border-rightbottom item-style">
        <div class="weui-cell__hd">
          <label class="weui-label title">所属科室</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input class="weui-input ipt ellipsis right-aligned-input" type="text" readonly @click="showDeptPicker" v-model="sourcesDeptName" placeholder="请选择所属科室" />
          <van-icon style="margin: 0 8px" name="arrow" />
          <van-icon style="margin-right: 10px" name="search" @click="toSelectDept" />
        </div>
      </div>
      <van-popup v-model="showDeptPickerFlag" position="bottom">
        <div class="search-box">
          <van-search v-model="deptSearchValue" placeholder="请输入搜索关键词" />
        </div>
        <van-picker show-toolbar :columns="filteredDeptList" value-key="name" @confirm="onDeptConfirm" @cancel="showDeptPickerFlag = false" title="选择所属科室" />
      </van-popup>

      <div class="weui-cell border-rightbottom item-style">
        <div class="weui-cell__hd">
          <label class="weui-label title star">服务部门</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input class="weui-input ipt" readonly type="text" @click="handleDeptClick" v-model="designateDeptName" placeholder="请选择服务部门" />
          <van-icon style="margin: 0 8px" name="arrow" />
        </div>
      </div>
      <van-field
        readonly
        clickable
        name="datetimePicker"
        :value="requireAccomplishDate"
        label="要求完工时间"
        right-icon="arrow"
        input-align="left"
        placeholder="请选择时间"
        @click="closingTimeFn"
      />
      <!-- 选择要求完工时间弹框 -->
      <van-popup v-model="timeShow" position="bottom" :style="{ height: '40%' }">
        <van-datetime-picker v-model="currentDate" type="datetime" title="选择要求完工时间" @confirm="dateComfirm" @cancel="dateCancel" :min-date="minDate" />
      </van-popup>
      <!--图片上传-->
      <upload-image class="bottom" style="position: relative" ref="imgs" @getImg="getImg" @delImg="delImg"></upload-image>
    </div>

    <div class="submit-btn" :class="{ active: canShow }">
      <button class="weui-btn weui-btn_primary" @click="submitAfterVrification">提交</button>
    </div>
    <!-- <van-popup v-model="showPicker" round position="bottom">
      <van-picker show-toolbar :columns="teamList" @cancel="showPicker = false" @confirm="onConfirm" value-key="team_name" />
    </van-popup> -->
    <PublicServiceDepartment
      :showPicker="showPicker"
      :dataObj="{ workTypeCode: '11', localtionId: localtion }"
      @cancel="showPicker = false"
      @confirm="onConfirm"
    ></PublicServiceDepartment>
  </div>
</template>

<script>
var h5Mode = localStorage.getItem("h5Mode");
if (h5Mode === "apicloud") {
  // 如果当前项目为apicloud内嵌H5页面，返回至原生
  var txFlashFileRecognize = api.require("txFlashFileRecognize");
}
import utils from "@/utils/Global";
import { mapState } from "vuex";
import UploadImage from "@/common/uploadImg/uploadImg";
// import UploadVoice from "@/common/uploadVoice/UploadVoice";
import Popups from "@/common/customize/Popups";
import PhoneText from "@/common/PhoneText";
import moment from "moment";
import PublicServiceDepartment from "./components/publicServiceDepartment.vue";
import YBS from "@/assets/utils/utils.js";

export default {
  name: "RepairAtWill",
  components: {
    UploadImage,
    // UploadVoice,
    Popups,
    PhoneText,
    PublicServiceDepartment
  },
  data() {
    return {
      phone: "", //需联系电话和临时用户需发送验证码的手机号
      value: "", //申报描述
      canShow: false,
      voiceParams: "",
      imagesParams: [],
      imageConfig: [], //处理图片的mediaId和ossUrl
      imgFlag: false,
      accessToken: "",
      isOnlyVoice: false,
      first: true, //是否首次打开此页面，只有第一次才自动定位
      smsCode: "", //临时用户需发送验证码，验证码成功后才可以报修
      iptSmsCode: "", //临时用户需发送验证码，验证码成功后才可以报修(用户输入的)
      isRegister: 0, //临时用户报修的医院是否为开放状态
      requireCode: 2, //	移动申报自动派工标识(默认关闭-非必填)
      isSubmit: true, //节流标示
      placeholder: "请输入您要申报的内容描述、语音，字数在500字以内，语音限制60秒",
      hint: "", //工单配置提醒
      unitCode: "",
      hospitalCode: "",
      amount: {
        //上传图片限制
        quantity: 3,
        capital: "三"
      },
      recordingInfo: "",
      showPicker: false,
      designateDeptName: "",
      designateDeptCode: "",
      teamList: [],
      timeShow: false,
      currentDate: new Date(),
      requireAccomplishDate: "", //要求完工时间
      minDate: new Date(),
      areaVal: "", // 服务区域name
      serviceSiteResId: "", // 服务区域id
      localtionPlaceName: "", // 服务房间name
      localtionPlaceCode: "", // 服务房间id
      localtion: "", // 服务区域id+服务房间id
      localtionName: "", // 服务区域name+服务房间name
      isExist: "", // 是否存在服务房间
      allSiteDataInfo: [], // 服务房间数据
      showDeptPickerFlag: false, // 控制科室选择器显示
      deptSearchValue: "", // 科室搜索关键词
      deptList: [], // 科室列表
      sourcesDeptName: "", // 所属科室name
      sourcesDept: "", // 所属科室id
      showAreaPickerFlag: false, // 控制服务区域选择器显示
      areaSearchValue: "", // 服务区域搜索关键词
      areaList: [], // 服务区域列表
      olgWorkPushNew: {} // 工单配置
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "h5Mode", "serviceAreaTreeData", "changeLocation"]),
    // 过滤后的区域列表
    filteredAreaList() {
      if (!this.areaSearchValue) {
        return this.areaList;
      }
      return this.areaList.filter(item => item.name && item.name.toLowerCase().includes(this.areaSearchValue.toLowerCase()));
    },
    // 过滤后的科室列表
    filteredDeptList() {
      if (!this.deptSearchValue) {
        return this.deptList;
      }
      return this.deptList.filter(item => item.name && item.name.toLowerCase().includes(this.deptSearchValue.toLowerCase()));
    }
  },
  methods: {
    getConfig() {
      this.$api.getNewSysConfigParam({}).then(res => {
        console.log("获取pc配置", res);
        this.olgWorkPushNew = res.olgWorkPushNew;
      });
    },
    getDeptListByLocation(configType) {
      this.$api
        .getDeptListByLocation({
          localtionId: this.changeLocation.localtionPlaceCode ? this.localtion.replace(/,/g, "_") + "_" + this.changeLocation.localtionPlaceCode : this.localtion.replace(/,/g, "_")
        })
        .then(res => {
          console.log("根据地点获取科室列表", res);
          this.deptList = res;
          if (configType == "1" || (configType == "2" && !this.sourcesDeptName && res && res.length > 0)) {
            this.sourcesDeptName = res[0].name;
            this.sourcesDept = res[0].id;
          }
        });
    },
    getLocationListByDept(configType) {
      this.$api
        .getLocationListByDept({
          deptId: this.sourcesDept
        })
        .then(res => {
          console.log("根据科室获取地点列表", res);
          // 处理数据，拼接名称和ID
          this.areaList = res.spaceInfoByDeptIdList.map(item => {
            // 处理名称：拼接allParentName和gridName，并去掉所有的>符号
            const name = (item.allParentName + item.gridName).replace(/>/g, "");
            const showname = item.allParentName + ">" + item.gridName;
            // 直接修改item的allParentId属性
            if (item.allParentId && item.allParentId.startsWith("#,")) {
              item.allParentId = item.allParentId.substring(2); // 去掉#和逗号
            } else if (item.allParentId && item.allParentId.startsWith("#")) {
              item.allParentId = item.allParentId.substring(1); // 只去掉#
            }

            return {
              ...item,
              name,
              showname
            };
          });
          if (configType == "0" || (configType == "2" && !this.areaVal)) {
            this.areaVal = this.areaList[0].allParentName.replace(/>/g, "");
            this.localtionPlaceName = this.areaList[0].gridName;
            this.localtionName = this.areaVal + this.localtionPlaceName;
            this.localtion = this.areaList[0].allParentId + "," + this.areaList[0].id;
            this.serviceSiteResId = this.areaList[0].parentGridId;
          }
        });
    },
    onAreaConfirm(value) {
      this.areaVal = value.allParentName.replace(/>/g, "");
      this.localtionPlaceName = value.gridName;
      this.localtionName = this.areaVal + this.localtionPlaceName;
      this.localtion = value.allParentId + "," + value.id;
      this.serviceSiteResId = value.parentGridId;
      this.showAreaPickerFlag = false;
    },
    showAreaPicker() {
      this.areaSearchValue = ""; // 清空搜索值
      this.showAreaPickerFlag = true;
    },
    onDeptConfirm(value) {
      this.sourcesDeptName = value.name; // 使用 name 字段作为显示值
      this.sourcesDept = value.id; // 使用 id 字段作为实际值
      this.showDeptPickerFlag = false;
    },
    showDeptPicker() {
      this.deptSearchValue = ""; // 清空搜索值
      this.showDeptPickerFlag = true;
    },
    toSelectDept() {
      this.$router.push({
        path: "/deptSelectedPage",
        query: {
          fromPath: "RepairAtWill"
        }
      });
    },
    // 扫码处理回调
    setLocaltionData(roomCode) {
      this.$api.lookUpById({ id: roomCode }).then(res => {
        this.areaVal = res.simName.split(">").slice(-3).join("");
        // 房间id及name赋值
        this.localtionPlaceName = res.localSpaceName || "";
        this.localtionPlaceCode = res.id;
        this.$store.commit("changeLocation", {
          localtionPlaceName: this.localtionPlaceName,
          localtionPlaceCode: this.localtionPlaceCode
        });
        // 提交传参全集拼接
        this.localtionName = this.areaVal + this.localtionPlaceName;
        this.localtion = [...res.simCode.split(",").slice(-3), this.localtionPlaceCode].join(",");
        // 楼层id赋值
        this.serviceSiteResId = this.localtion.split(",")[2];
        this.serviceSiteResId ? (this.isExist = true) : "";
      });
    },
    // 扫码回调
    scanRepairCallBackNew(scanData) {
      let roomCode = scanData[2]; // 扫码获取的id
      if (roomCode) {
        this.setLocaltionData(roomCode);
      }
    },

    // 服务房间请求函数
    toSelectSite() {
      // if (!this.isExist) return $.toast("不支持选择房间！", "text");
      let arr = this.serviceSiteResId.split(",");
      if (this.serviceSiteResId == "") {
        $.toast("请先选择服务区域", "text");
      } else {
        this.showVal(this.serviceAreaTreeData, arr[arr.length - 1]);
        return;
      }
    },

    // 仅供服务房间展示，调起弹窗并设置相关参数
    showVal(res, gridId) {
      this.allSiteDataInfo = res.filter(item => {
        return item.parentId == gridId;
      });
      //将所有地点取出放到数组中
      let siteArr = [];
      for (let i = 0; i < this.allSiteDataInfo.length; i++) {
        siteArr.push(this.allSiteDataInfo[i]);
      }
      //跳到搜索页面
      this.$router.push({
        name: "Site",
        params: {
          list: siteArr
        }
      });
    },
    // 扫码
    scanRepair() {
      if (!YBS.hasPermission("camera")) {
        var pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {});
        });
        return;
      }

      try {
        YBS.scanCodeNew(true).then(
          item => {
            console.log(item, "11111111");
            if (item[0] == "KJ") {
              this.scanRepairCallBackNew(item);
            } else {
              this.$toast.fail("无效二维码!");
            }
          },
          () => {
            this.$toast.fail("无效二维码!");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    //  区域选择
    toSelectArea() {
      //级联弹窗数据还原
      this.localtionPlaceName = "";
      this.localtionPlaceCode = "";
      this.$store.commit("changeLocation", {
        localtionPlaceName: "",
        localtionPlaceCode: ""
      });
      this.$router.push({
        path: "/selectArea",
        query: {
          routerPath: this.$route.path
        }
      });
    },

    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
    getDialogShow() {
      let params = {
        userId: this.loginInfo.staffId,
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getNotCommentWarn(params).then(res => {
        if (res.commentCount > 0 && res.commentStatus == "1") {
          this.$dialog
            .confirm({
              message: "您当前有未评价的工单！",
              confirmButtonText: "去评价",
              cancelButtonText: "忽略",
              confirmButtonColor: "#3562db"
            })
            .then(() => {
              this.$router.push({
                path: "/orderAcceptance"
              });
            })
            .catch(() => {});
        }
      });
    },
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    //时间弹窗

    dateComfirm(e) {
      this.requireAccomplishDate = this.formatDate(e);
      this.dateCancel();
    },
    dateCancel() {
      this.timeShow = false;
    },
    closingTimeFn() {
      this.timeShow = true;
    },
    getOCTeamInfo() {
      const params = {
        localtionId: "",
        workTypeCode: "11",
        // itemTypeCode: this.selectRowId,
        matterId: ""
      };
      this.$api.getTeamsByTask(params).then(res => {
        console.log("getOCTeamInfo", res);
        this.teamList = res.list;
      });
    },
    handleDeptClick() {
      this.showPicker = true;
    },
    onConfirm(val) {
      this.designateDeptName = val.team_name;
      this.designateDeptCode = val.id;
      this.showPicker = false;
    },
    getImg(img) {
      console.log("img", img);
      this.imagesParams.push(img);
    },
    delImg(url) {
      this.imagesParams = this.imagesParams.filter(item => {
        return item != url;
      });
    },
    getRecord(info) {
      this.recordingInfo = info;
      this.voiceParams = info;
      console.log("recordingInfo", this.recordingInfo);
      if (this.h5Mode == "apicloud") {
        this.voiceParams = info;
      } else {
        this.$api
          .voiceId2url({
            voiceUrl: info,
            sourcesFlag: "1"
          })
          .then(res => {
            console.log("voiceRes", res);
            this.voiceParams = res.fileUrlList;
            this.handleSubmitClick();
          });
      }
    },
    translateVoice(localId) {
      parent.wx.translateVoice({
        localId: localId, // 需要识别的音频的本地Id，由录音相关接口获得，音频时长不能超过60秒
        isShowProgressTips: 1, // 默认为1，显示进度提示
        success: res => {
          // alert(res.translateResult); // 语音识别的结果
          this.value = res.translateResult;
        }
      });
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
      console.log("recordingInfoFile", this.recordingInfoFile);
    },
    /**
     * 获取手机短信验证码
     */
    handleGetSmsCode(smscode, phone) {
      this.phone = phone;
      this.smsCode = smscode;
    },
    /**
     * 当电话号改变时接收电话号码
     */
    getPhoneNum(num) {
      this.phone = num;
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 获取录音路径
     * @param params
     */
    getVoice(params) {
      if (this.isOnlyVoice) {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode ? this.hospitalCode : "BJSJTYY",
              unitCode: this.unitCode ? this.unitCode : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params,
              type: "1"
            })
          )
          .then(res => {
            this.voiceParams = res.data.data.ossUrl;
            this.requiredParameter();
          });
      }
    },
    /**
     * 删除录音
     */
    deleteVoice() {
      this.voiceParams = "";
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode ? this.hospitalCode : "BJSJTYY",
            unitCode: this.unitCode ? this.unitCode : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: this.voiceParams
          })
        )
        .then(res => {});
    },
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode ? this.hospitalCode : "BJSJTYY",
            unitCode: this.unitCode ? this.unitCode : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode ? this.hospitalCode : "BJSJTYY",
              unitCode: this.unitCode ? this.unitCode : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
            // this.imagesParams.push(res.data.data.ossUrl);
            // this.imageConfig.push(res.data.data);
          });
      });
    },
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    },
    /**
     * 处理提交按钮事件
     */
    handleSubmitClick() {
      // if (this.$refs.imgs.imgServerId.length != 0 && this.$refs.voices.hasVoice != "recorded") {
      //   //上传图片、没有录音
      //   let imgLen = () => {
      //     if (this.imagesParams.length != 0) {
      //       this.requiredParameter();
      //     } else {
      //       setTimeout(() => {
      //         imgLen();
      //       }, 500);
      //     }
      //   };
      //   imgLen();
      // } else if (this.$refs.imgs.imgServerId.length != 0 && this.$refs.voices.hasVoice == "recorded") {
      //   //上传图片、录音
      //   let imgLen = () => {
      //     if (this.imagesParams.length != 0) {
      //       this.isOnlyVoice = true;
      //       this.$refs.voices.saveVoice();
      //     } else {
      //       setTimeout(() => {
      //         imgLen();
      //       }, 500);
      //     }
      //   };
      //   imgLen();
      // } else if (this.$refs.imgs.imgServerId.length == 0 && this.$refs.voices.hasVoice == "recorded") {
      //   //不需上传图片、但有录音
      //   this.isOnlyVoice = true;
      //   this.$refs.voices.saveVoice();
      // } else if (this.$refs.imgs.imgServerId.length == 0 && this.$refs.voices.hasVoice == "unvoice") {
      //   //不需上传图片、没有录音
      //   this.requiredParameter();
      // }
      this.requiredParameter();
    },
    /**
     * 获取签名
     */
    getConfigParams() {
      // 进行签名的时候  Android 不用使用之前的链接， ios 需要
      let signLink = /(Android)/i.test(navigator.userAgent) ? location.href.split("#")[0] : window.entryUrl;
      let paramValue = {
        localUrl: signLink,
        appId: process.env.WxAppid
      };
      this.axios
        .get(process.env.API_HOST + "/accessToken/getSignature", {
          params: paramValue
        })
        .then(this.setAllConfig);
    },
    /**
     * 设置微信config参数
     * @param res
     */
    setAllConfig(res) {
      this.$refs.voices.setConfig(res);
      this.$refs.voices.initRecord();
      this.$refs.imgs.setConfig(res);
      this.accessToken = res.data.data.accessToken;
    },
    /**
     * 提交前判断
     */
    getHttp() {
      this.submitFun();
    },
    /**
     * 工单提交请求函数
     */
    submitFun() {
      if (!this.isSubmit) {
        $.toast("该工单已经提交,请勿重复提交", "text");
        return;
      }
      // if (this.$refs.imgs.imgLocalIds.length != this.imagesParams.length) {
      //   $.toast("请等待图片上传完毕", "text");
      //   return;
      // }
      this.isSubmit = false;
      $.showLoading("提交中…");

      if (this.localtionPlaceCode == "" ) {
        //如果值为ipms巡检，不需要设置这两个值，直接带过来的
        if (this.changeLocation.localtionPlaceCode) {
          let arr = this.localtion.split(",");
          if (arr.length == 4) {
            arr.splice(arr.length - 1, 1);
            let str = arr.join(",");
            this.localtion = str + "," + this.changeLocation.localtionPlaceCode;
          } else {
            this.localtion = this.localtion + "," + this.changeLocation.localtionPlaceCode;
          }

          this.localtionName = this.areaVal + this.localtionPlaceName;
        }
      }

      let param = null;
      let unitCode = "";
      let hospitalCode = "";
      unitCode = this.loginInfo ? this.loginInfo.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "";
      hospitalCode = this.loginInfo ? this.loginInfo.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "";
      let params = {
        unitCode: unitCode,
        hospitalCode: hospitalCode,
        workTypeCode: "11", //工单类型：综合维修
        workSources: "1", //申报来源：App
        typeSources: "1", //随手拍(医务报修)
        type: this.staffInfo.teamId ? 2 : 1, //区分院内/院外

        questionDescription: this.value,
        accessToken: this.accessToken,
        contactNumber: this.phone, //未登录扫描报修加入的联系人电话
        dispatchingConfig: this.requireCode, //	移动申报自动派工标识
        phoneNumber: this.loginInfo.phone,
        realName: this.loginInfo.staffName,
        jobNumber: this.staffInfo.staffNumber,
        staffId: this.staffInfo.staffId,
        deptName: "",
        deptCode: "",
        sourcesDept: this.sourcesDept,
        sourcesDeptName: this.sourcesDeptName,
        userId: this.loginInfo.id,
        designateDeptName: this.designateDeptName,
        designateDeptCode: this.designateDeptCode,
        requireAccomplishDate: this.requireAccomplishDate, //要求完工时间
        localtion: this.localtion,
        localtionName: this.localtionName
      };
      params.callerTape = this.voiceParams;
      params.attachment = JSON.stringify(this.imagesParams);
      // return console.log("param", params);
      param = this.$qs.stringify(params);
      this.axios
        .post(__PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat", param, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(this.handleRequestSucc);
    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      $.hideLoading();
      if (res.data.code == "200") {
        $.toast("工单提交成功", "success", () => {
          sessionStorage.removeItem("hospitalCode");
          sessionStorage.removeItem("unitCode");
          sessionStorage.removeItem("url");
        });
        setTimeout(() => {
          // api.closeFrame();
          this.$YBS.apiCloudCloseFrame();
        }, 1000);
      } else {
        $.toast(res.data.message, "text");
        if (res.data.code == 40001) {
          this.voiceParams = "";
          this.$refs.voices.handleDelVoiceClick();
        }
      }
    },
    /**
     * 必选项验证
     */
    requiredParameter() {
      // let reg = /^((13|14|15|17|18)[0-9]{9})$/;
      // if (!this.phone) {
      //   if (this.phone == "") {
      //     $.toast("请填写手机号及验证码", "text");
      //     return;
      //   } else if (this.phone.length < 11) {
      //     //2020-5-19 修改验证方式为11位长度
      //     $.toast("请输入正确的手机号", "text");
      //     return;
      //   } else if (this.iptSmsCode == "") {
      //     $.toast("请输入验证码", "text");
      //     return;
      //   }
      // } else if (this.iptSmsCode == "") {
      //   $.toast("请输入验证码", "text");
      //   return;
      // } else if (this.iptSmsCode != this.smsCode) {
      //   $.toast("验证码不正确", "text");
      //   return;
      // }
      if (!this.value && !this.voiceParams) {
        $.toast("请填写申报描述或录入语音", "text");
        return false;
      } else {
        this.getHttp();
      }
    },
    /**
     * 校验联系电话的格式
     */
    submitAfterVrification() {
      if (this.phone.length < 11 && this.phone != "") {
        $.toast("请输入正确的电话号", "text");
      } else if (this.h5Mode == "apicloud") {
        this.handleSubmitClick();
      } else {
        if (this.$refs.voices.hasVoice == "recorded") {
          this.$refs.voices.saveVoice();
        } else {
          this.handleSubmitClick();
        }
      }
    },

    /**
     * 判断该医院是否开放注册
     */
    getHospitalIsRisterByCode(unitCode, hospitalCode) {
      this.$api
        .getHospitalIsRisterByCode({
          unitCode: unitCode,
          hospitalCode: hospitalCode
        })
        .then(res => {
          this.isRegister = res.isRegister;
        });
    },
    //空格校验
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    },
    recognize(path) {
      // console.log(txFlashFileRecognize);
      txFlashFileRecognize.recognize(
        {
          path: path
        },
        function (ret) {}
      );
    },
    getDownLoad(path) {
      this.recognize(path);
    }
  },
  mounted() {
    this.getConfig();
    this.getDialogShow();

    this.getOCTeamInfo();
    let _this = this;
    if (h5Mode === "apicloud") {
      txFlashFileRecognize = api.require("txFlashFileRecognize");
      txFlashFileRecognize.init({
        appid: "1300358855",
        secretId: "AKIDwe7MTUyrLFtJbMh76Wgbx7QUQAE7lmvp",
        secretKey: "nIeR2OTN2bClz1mIpAgDqKi23CBzbYHA"
      });
      txFlashFileRecognize.addEventListener(function (ret) {
        // api.alert({ msg: JSON.stringify(ret) });
        console.log(ret);
        if (ret.eventType == "didRecognizeSuccess") {
          _this.value += ret.text;
        }
      });
    }
    //存储appId
    localStorage.wxAppId = process.env.WxAppid;
    let routeQuery = this.$route.query;
    this.unitCode = routeQuery.unitCode;
    this.hospitalCode = routeQuery.hospitalCode;
  },
  activated() {
    setTimeout(() => {
      this.$YBS.apiCloudEventKeyBack(this.goback);
    }, 100);
    //设置title
    document.title = "随手拍";
    // this.getConfigParams();
    console.log(this.$route.query, "1111111");
    //选择的服务房间与当前的服务房间不一致时，更改服务房间（前提是已经另选择了服务房间）
    if (this.localtionPlaceName != this.changeLocation.localtionPlaceName && this.changeLocation.localtionPlaceName) {
      this.localtionPlaceName = this.changeLocation.localtionPlaceName;
    }
    if (this.$route.query.selectedOffice) {
      this.sourcesDeptName = this.$route.query.selectedOffice.officeName;
      this.sourcesDept = this.$route.query.selectedOffice.id;
    }
    if (JSON.stringify(this.$route.query).includes("isExist")) {
      //从选择服务区域页面跳转回来携带参数
      let params = this.$route.query;
      this.serviceSiteResId = params.code[2];
      this.localtion = params.code.join(",");
      //        判断服务区域是数组还是字符串
      if (Array.isArray(params.name)) {
        this.areaVal = params.name.join("");
        this.localtionName = params.name.join("");
      } else {
        //          最近服务区域返回的信息
        this.areaVal = params.name;
        this.localtionName = params.name;
      }
      if (this.$route.query.isExist == "false") {
        this.isExist = false;
      } else if (this.$route.query.isExist == "true") {
        this.isExist = true;
      } else {
        this.isExist = this.$route.query.isExist;
        let isHaveRoomName = this.$route.query.isHaveRoomName;
        if (this.isExist && this.changeLocation.localtionPlaceName && !isHaveRoomName) {
          this.localtionPlaceName = "";
          this.localtionPlaceCode = "";
        }
      }
      if (!this.serviceSiteResId) {
        //没选楼层
        let localtionData = this.localtion;
        this.localtion = localtionData.substr(0, localtionData.length - 1);
      }
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "1" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getDeptListByLocation(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    }
  },
  watch: {
    value(value, oldValue) {
      this.value = this.value.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g, "");
      if (oldValue && !value) {
        this.$refs.textarea.click();
      }
    },
    hospitalInfo(info) {
      this.getHospitalDispatchingConfig(info.hospitalCode, info.unitCode);
    },
    localtionPlaceName(val) {
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "1" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getDeptListByLocation(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    },
    sourcesDept(val) {
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "0" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getLocationListByDept(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    if (to.matched.some(m => m.meta.auth)) {
      next(vm => {
        if (from.path == "/hospListNew") {
          vm.first = false;
        }
        // vm.getConfigParams();
      });
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper {
  height: 100%;
  background-color: $bgColor;

  .content {
    box-sizing: border-box;
    min-height: 90%;
    width: 100%;
    overflow: hidden;
    padding-bottom: 76px !important;
    background-color: $bgColor;

    .reservation-btns {
      marginBottom20();
      text-align: center;

      span {
        display: inline-block;
        width: 1.24rem;
        margin: 0 0.36rem;
      }

      .active {
        color: $color;
        border-bottom: 2px solid $color;
      }
    }

    .other-location-name {
      marginBottom20();

      .ipt-content {
        justify-content: flex-end;
        line-height: 1.2;
        text-align: left;
      }
    }

    .adjustable {
      padding: 0 0.32rem;

      > div {
        text-align: center;
      }

      img {
        width: 0.72rem;
      }

      .down {
        transition: all 0.3s;
      }

      .up {
        transform: rotateZ(180deg);
        transition: all 0.3s;
      }
    }

    .up-content {
      transform: rotateX(90deg);
      transform-origin: center top;
      height: 0;
      transition: height 0.3s;
      transition: all 0.3s;
    }

    .down-content {
      transition: all 0.3s;
      transform-origin: center top;
      margin-bottom: 10px;
    }

    .er-level {
      display: flex;
      align-items: center;
      itemBaseStyle();

      .weui-cells_checkbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .redio-content {
          display: flex;
          color: $contentColor;

          .declare-attr {
            padding: 0;
          }
        }
      }
    }

    .hosp {
      marginBottom20();
    }

    .item-style {
      itemBaseStyle();

      .login-tips {
        line-height: 1;
        white-space: nowrap;
        font-size: 0.28rem;
        font-style: italic;
        color: $color;
      }
    }

    .matter-style {
      min-height: 0.98rem;
      line-height: initial;
    }

    .desc-form {
      margin-top: 0;
      background-color: #fff;

      .desc {
        padding: 0.32rem 0.32rem 0;
        font-size: 0.28rem;
        line-height: 1.5em;
        margin-bottom: 0.2rem;
        color: $contentColor;

        textarea {
          display: inline-block;
          text-indent: 2em;
          font-size: 0.3rem;
        }
      }
    }

    .tips {
      color: $contentColor;
      text-align: center;
      margin: 0.6rem 0;

      .key-tips {
        color: $color;
      }
    }
  }
}

.btn {
  padding: 0 1.12rem 0.3rem;
  position: relative;
  user-select: none;

  .delete-voice {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.5rem;
  }

  .play-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
    }
  }
}

.recording {
  position: fixed;
  z-index: 2;
  top: 2rem;
  left: 50%;
  margin-left: -1.2rem;

  img {
    width: 2.4rem;
    opacity: 0.7;
  }
}

.title-content {
  itemBaseStyle();
  margin-top: $marginb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ipt-content {
  text-align: right;
  display: flex;
  align-items: center;
  color: #999;
}

.matter-ipt {
  display: flex;
  justify-content: flex-end;

  .ipt-pos {
    position: relative;
    width: 100%;

    .matterInput {
      display: none;
    }

    span {
      font-family: serif;
      text-align: left;
      display: flex;
      font-size: 15px;
    }
  }
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;

  .img-wrapper {
    flex: 1;
    width: 0;
    margin: $marginb;
    position: relative;

    img {
      width: 100%;
    }

    .icon-img {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
  position: fixed;
  bottom: 0;
  z-index: 999;
}

.popups-content {
  width: 80%;
  margin: 0 auto;
  position: fixed;
  max-width: 300px;
  border-radius: 3px;
  overflow: hidden;

  & > div {
    margin: 0 auto;
    background: #fff;
    padding-bottom: 15px;
    border-bottom: 1px solid #efefef;
    height: 1.18rem;
    align-items: flex-end;
    box-sizing: border-box;
  }

  .cfm-phone-num-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 48px;
    font-size: 18px;
    padding: 0;
    color: #38C7C4;
  }
}

.play-void-img {
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
}

.title {
  font-size: 0.32rem;
}

.er-level-redio {
  display: flex;
  background-color: #fff;
  font-size: 0.3rem;
  color: $contentColor;
}

.redio-content {
  position: relative;

  .select-time {
    position: absolute;
    left: 1rem;
    bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 5em);

    .text {
      width: 50px;
      line-height: initial;
    }

    .ipt {
      flex: 1;
      text-align: right;
    }
  }

  .textColor {
    color: #999;
  }

  .disPointer {
    pointer-events: none;
  }
}

.hint {
  display: inline-block;
  margin: 0 20px;
}

.reminder {
  height: 0.68rem;
  background: #FAECE9;
  padding-left: 0.32rem;
  line-height: 0.68rem;

  .reminder-img {
    width: 0.28rem;
  }

  .reminder-text {
    margin-left: 0.24rem;
    color: #FF7859;
    font-size: 0.26rem;
  }
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

>>> .van-field__label {
  font-size: 0.31rem;
  color: #353535;
}

>>> .van-field__control {
  font-size: 0.3rem;
}
</style>
