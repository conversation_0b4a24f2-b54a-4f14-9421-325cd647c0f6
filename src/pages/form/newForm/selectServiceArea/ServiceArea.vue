<template>
  <div class="service-area-wrapper" ref="all">
    <Header title="服务区域" @backFun="goback"></Header>
    <!-- <div class="recent-info" ref="top" v-if="loginInfo" v-show="!typename">
      <!~~ 修改时不显示 ~~>
      <div class="title">
        <span class="iconfont">&#xe681;</span>
        <span class="text">最近</span>
      </div>
      <ul class="recent-area-content">
        <li
          class="recent-area"
          v-for="(item,idx) of recentLocatios"
          @click="handleSelectRecentLocation(item)"
          :key="idx"
        >{{ item.localtionName }}</li>
        <li v-if="recentLocatios.length == 0" class="no-data-tips">暂无最近数据</li>
      </ul>
    </div> -->
    <div v-if="type" class="whole-hospital">
      <div class="title title-pl">
        <span class="iconfont">&#xe66b;</span>
        <span class="text">全院楼宇</span>
      </div>
      <div class="linkage-area">
        <div class="top">
          <div :class="{ active: curArea === 'region', disClick: isClick }" @click="switchOper('region')">区域</div>
          <div :class="{ active: curArea === 'building', disClick: isClick1 }" @click="switchOper('building')" v-if="navshow2">楼宇</div>
          <div :class="{ active: curArea === 'floor', disClick: isClick2 }" @click="switchOper('floor')" v-if="navshow3">楼层</div>
        </div>

        <div class="content-null" v-show="nullstyle">
          <img class="nullimg" src="@/assets/images/noDataDefault/search-empty.png" />
          <p class="nulltext">暂无数据</p>
        </div>

        <div class="content">
          <span
            class="triangle"
            :class="{
              triangle0: curArea === 'region',
              triangle1: curArea === 'building',
              triangle2: curArea === 'floor'
            }"
          ></span>
          <div v-if="curArea === 'region'" class="cur-area-box">
            <div
              class="item-area"
              :for="index"
              v-for="(item, index) of areaData"
              :key="item.id"
              @click.prevent="switchOper('building', item.ssmName, item.id)"
              :class="{ one: index % 2 == 0, two: index % 2 != 0 }"
            >
              <div>{{ item.ssmName }}</div>
            </div>
          </div>
          <div v-if="curArea === 'building'" class="cur-area-box">
            <!--2019年8月6日15:24:30 by wd 不能直选到区-->
            <!--<div
                @click.stop="submitAreaInfo()"
            >
              <div class="item-area">
                暂不选择
              </div>
            </div>-->
            <div
              class="item-area"
              :for="index"
              v-for="(item, index) of buildingData"
              :key="item.id"
              @click.prevent="switchOper('floor', item.ssmName, item.id)"
              :class="{ one: index % 2 == 0, two: index % 2 != 0 }"
            >
              <div>{{ item.ssmName }}</div>
            </div>
          </div>
          <div v-if="curArea === 'floor'" class="cur-area-box">
            <div @click.stop="submitAreaInfo()">
              <div class="item-area">暂不选择</div>
            </div>
            <div
              class="item-area"
              :for="index"
              v-for="(item, index) of floorData"
              :key="item.id"
              @click="submitAreaInfo(item.ssmName, item.id)"
              :class="{ one: index % 2 == 0, two: index % 2 != 0 }"
            >
              <div>{{ item.ssmName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!type" class="associated-service">
      <div class="switch" @click="trigger">服务地点不对</div>
      <ul class="associated-list">
        <li class="list-text" v-for="(item, i) of parentGridList" :key="i" @click="siteChanges(item)">
          <!-- @click="submitAreaInfo(item.parentGridName,item.id)" -->
          <span>{{ item.parentGridName }}>{{ item.ssmName }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { mapState, mapMutations } from "vuex";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "ServiceArea",
  mixins: [fontSizeMixin],
  computed: {
    ...mapState(["loginInfo", "serviceAreaTreeData"])
  },
  data() {
    return {
      curArea: "region", //当前选项
      isClick: true,
      isClick1: true,
      isClick2: true,
      areaData: [],
      buildingData: [],
      floorData: [],
      resNameData: [],
      resCodeData: [],
      isExist: true, //是否显示服务地点（选暂不选择时为false）
      bottomHeight: 0, //全院楼宇部分的总高度
      bottomContentHeight: 0, //区域、楼宇、楼层展示区域的高度
      workTypeCode: "", //工单类型
      recentLocatios: [], //最近服务地点
      style: "", //工单类型，3为运输服务起点，4为服务终点
      navshow2: false,
      navshow3: false,
      nullstyle: false,
      typename: "", //修改方式
      parentGridList: [], //修改传过来的地点列表
      type: true,
      officeNameList: {}, //查询到的科室
      level: "", //为2 的时候是运送的终点区域
      levelDataList: [], // 所有等级列表
      timer: ""
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    ...mapMutations(["changeLocation", "changeLocationEnd", "transLocationStart", "transLocationEnd"]),
    goback() {
      this.$router.go(-1);
    },
    // 切换显示样式
    trigger() {
      this.type = true;
    },
    getLocationTree() {
      const treeData = this.serviceAreaTreeData;
      this.levelDataList.push(this.getLevelData(2, treeData), this.getLevelData(3, treeData), this.getLevelData(4, treeData), this.getLevelData(5, treeData));
      // console.log(this.levelDataList)
      // this.$api.getGridTreeData().then(res=>{
      //   console.log(res);
      // })
      this.setShowLocationData();
    },
    setShowLocationData(id) {
      if (this.curArea == "region") {
        this.areaData = this.levelDataList[0];
      }
      if (this.curArea == "building") {
        this.buildingData = this.levelDataList[1].filter(e => e.parentId == id);
        if (this.buildingData.length === 0) {
          this.nullstyle = true;
        } else {
          this.nullstyle = false;
        }
      }
      if (this.curArea == "floor") {
        this.floorData = this.levelDataList[2].filter(e => e.parentId == id);
      }
    },
    getLevelData(level, data) {
      const filterData = data.filter(e => e.ssmType === level);
      if (filterData.length) {
        return filterData;
      } else {
        return [];
      }
    },
    /**
     * 区域联动请求函数
     */
    getLinkageRes(id) {
      // if (this.loginInfo) {
      //   this.$api
      //     .cascadingQueryHospitalGridInfo({
      //       gridId: id
      //     })
      //     .then(this.showVal);
      // } else {
      //   $.showLoading();
      //   this.axios
      //     .post(
      //       process.env.API_BASE +
      //         "/hospitalController/cascadingQueryHospitalGridInfo",
      //       this.$qs.stringify({
      //         unitCode: sessionStorage.getItem("unitCode"),
      //         hospitalCode: sessionStorage.getItem("hospitalCode"),
      //         gridId: id
      //       })
      //     )
      //     .then(this.showVal);
      // }
    },
    /**
     * 返回数据展示
     */
    showVal(res) {
      $.hideLoading();
      if (res.data && res.data.code == 200) {
        res = res.data.data;
      }
      if (this.curArea == "region") {
        this.areaData = res;
      }
      if (this.curArea == "building") {
        this.buildingData = res;
        if (this.buildingData.length === 0) {
          this.nullstyle = true;
        } else {
          this.nullstyle = false;
        }
      }
      if (this.curArea == "floor") {
        this.floorData = res;
      }
    },
    /**
     * 点击每一项后进入下一组数据
     * @param par
     */
    switchOper(style, name, code) {
      this.curArea = style;
      if (code) {
        // this.getLinkageRes(code);
        this.setShowLocationData(code);
      }
      if (style == "region") {
        this.navshow2 = false;
        this.navshow3 = false;
        this.nullstyle = false;
      }
      if (style == "building") {
        this.isClick = false;
        this.isClick1 = false;
        this.navshow2 = true;
        this.navshow3 = false;
        if (name) {
          this.resNameData[0] = name;
          this.resCodeData[0] = code;
        }
      }
      if (style == "floor") {
        this.navshow3 = true;
        this.isClick1 = false;
        this.isClick2 = false;
        this.resNameData[1] = name;
        this.resCodeData[1] = code;
      }
    },
    /**
     * 点击楼层关闭弹窗并携带回数据
     * @param name 楼层名称
     * @param code 楼层code
     */
    submitAreaInfo(name, code) {
      if (name == undefined && code == undefined) {
        // 天津肿瘤需求改版
        // this.resNameData[2] = "";
        // this.resCodeData[2] = "";
        this.isExist = false;
      } else {
        this.resNameData[2] = name;
        this.resCodeData[2] = code;
        this.isExist = true;
      }
      this.returnPrevPage(this.resNameData, this.resCodeData, false, this.isExist);
    },
    /**
     * 与科室关联的地点列表
     * 带回数据的格式处理
     * gridLevel为5时gridName为房间 小于5时没有房间
     */
    siteChanges(item) {
      if (item.gridLevel < 5) {
        this.resNameData = item.parentGridName + ">" + item.ssmName;
        this.resCodeData = item.allParentId.split(",");
        this.returnPrevPage(this.resNameData.split(">"), this.resCodeData, false, this.isExist);
      } else if (item.gridLevel == 5) {
        this.resNameData = item.parentGridName;
        this.resCodeData = item.allParentId.split(",");
        this.localtionPlaceName = item.ssmName;
        this.returnPrevPage(this.resNameData.split(">"), this.resCodeData, false, this.isExist, this.localtionPlaceName);
      }
    },
    /**
     * 返回上一页（创建工单页面）
     */
    returnPrevPage (name, code, isHaveRoomName, isExist, localtionPlaceName) {
      // console.log(this.$route.query,'222222222222');
      
      if (this.$route.query.source != "revise") {
        this.$router.replace({
          path: this.$route.query.routerPath,
          query: {
            name: name,
            code: code,
            isHaveRoomName: isHaveRoomName,
            isExist: isExist,
            style: this.style
          }
        });
        this.$router.go(-1);
      } else {
        if (this.$route.query.isEnd) {
          this.$store.commit("reviseServiceAreaEnd", {
            name: name,
            code: code
          });
          this.$store.commit("setTransportEndRoomName", {
            name: localtionPlaceName,
            code: code
          }); //终点房间
        } else {
          this.$store.commit("reviseServiceArea", { name: name, code: code });
          this.$store.commit("setLocaltionPlaceName", {
            name: localtionPlaceName,
            code: code
          });
          if (this.typename == 2) {
            this.getparentGridName(2, "", code[0]);
          }
        }
        this.$router.go(-1);
      }
    },
    /**
     * 地点最近的每一项
     */
    handleSelectRecentLocation(list) {
      let location = list.localtion.split(",");
      if (location.length == 4) {
        //到房间
        //将房间信息提取出来
        let roomName = list.localtionName.split(">")[1];
        let localtionName = list.localtionName.split(">")[0];
        let roomCode = location[3];
        location.splice(location.length - 1, 1);
        if (this.style == 3) {
          this.transLocationStart({
            style: "3",
            startPlaceCode: roomCode,
            startPlaceName: roomName
          }); //运输(起点3)
        } else if (this.style == 4) {
          this.transLocationEnd({
            style: "4",
            endPlaceCode: roomCode,
            endPlaceName: roomName
          }); //运输(终点4)
        } else {
          if (this.$route.query.isEnd) {
            this.changeLocationEnd({
              localtionPlaceName: roomName,
              localtionPlaceCode: roomCode
            });
          } else {
            this.changeLocation({
              localtionPlaceName: roomName,
              localtionPlaceCode: roomCode
            });
          }
        }
        this.returnPrevPage(localtionName, location, true, true);
      } else if (location.length == 3) {
        //到楼层
        this.returnPrevPage(list.localtionName, location, false, true);
      } else if (location.length == 2) {
        //到楼
        this.returnPrevPage(list.localtionName, location, false, false);
      }
    },
    /**
     * 获取最近服务地点
     */
    getRecentTreeLocations() {
      switch (this.$route.query.routerPath) {
        case "/cleaning":
          this.workTypeCode = "2";
          break;
        case "/repair":
          this.workTypeCode = "1";
          break;
        case "/transport":
          this.workTypeCode = "3";
          break;
        case "/service":
          this.workTypeCode = "6";
          break;
        default:
          this.workTypeCode = this.$route.query.workTypeCode;
          break;
      }
      this.$api
        .getLastThreeLocations({
          userId: this.loginInfo.id,
          workTypeCode: this.workTypeCode
        })
        .then(res => {
          this.recentLocatios = res;
        });
    },
    /**
     * 修改地点
     */
    getparentGridName(type, officeId, localtionId) {
      // this.axios
      //   .post(
      //     __PATH.ONESTOP +
      //       "/appOlgTaskManagement/getNameOffice",
      //     this.$qs.stringify({
      //       unitCode: this.loginInfo.userOffice[0].unitCode,
      //       hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
      //       type:type,
      //       officeId:officeId,//type为1的时候必填
      //       localtionId:localtionId,//type为2是必填
      //     })
      axios
        .post(
          process.env.API_BASE + "/departmentManager/department-manager/selectByPage",
          {
            current: 1,
            size: 999
          },
          {
            headers: {
              unitCode: this.loginInfo.userOffice[0].unitCode,
              hospitalCode: this.loginInfo.userOffice[0].hospitalCode
            }
          }
        )
        .then(res => {
          res = res.data.data.records;
          res.foreach(item => {
            item.name = item.deptName;
          });
          if (res.length == 0) {
            this.trgger();
          }
          if (type == 1) {
            if (res.length > 0) {
              for (let i = 0; i < res.length; i++) {
                // 筛选符合条件的地点  gridLevel<2
                if (res[i].gridLevel <= 2) {
                  res.splice(i, 1);
                }
              }
            } else if (res.length == 0) {
              return this.trigger();
            }
            this.parentGridList = res;
            if (this.parentGridList.length == 0) {
              return this.trigger();
            }
          } else if (type == 2) {
            this.officeNameList = res[0];
            // 保存数据到vuex
            this.$store.commit("setDepartments", res[0]);
          }
        });
    }
  },
  created() {
    // 提示加载中
    this.$toast.loading({
      message: "加载中...",
      forbidClick: true,
      overlay: false,
      duration: 0
    });
    if (this.serviceAreaTreeData && this.serviceAreaTreeData.length > 0) {
      this.$toast.clear();
      this.getLocationTree();
    } else {
      this.timer = setInterval(() => {
        if (this.serviceAreaTreeData && this.serviceAreaTreeData.length > 0) {
          this.$toast.clear();
          this.getLocationTree();
          clearInterval(this.timer);
        }
      }, 100);
    }
    // this.getLinkageRes("");
    this.$nextTick(() => {
      this.style = this.$route.query.style;
      // if (this.loginInfo) {
      //获取工单类型参数并获取最近三次服务地点
      // this.getRecentTreeLocations();
      // }
    });
    // 保存修改页面传过来的参数
    this.typename = this.$route.query.type;
    this.level = this.$route.query.level;
    return;
    if (this.$route.query.source == "revise") {
      // 从修改页面过来的
      if (this.$route.query.type == 1) {
        this.type = false;
        this.getparentGridName(1, this.$route.query.sourcesDeptCode);
      } else if (this.$route.query.type == 3) {
        this.type = true;
      } else if (this.$route.query.type == 2) {
        this.type = true;
      }
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.service-area-wrapper {
  height: 100%;
  background-color: $bgColor;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;

  .recent-info {
    padding: 0.1rem 0.32rem;
    background-color: #fff;
    marginBottom20();

    .recent-area-content {
      padding: 0.2rem 0.32rem 0;
      max-height: 150px;
      overflow: auto;
      .recent-area {
        margin-bottom: 0.2rem;
        font-size: 0.3rem;
        color: #353535;
        padding: 0.2rem 0.34rem;
        border-radius: 0.1rem;
        background-color: #f4f4f4;
        width: fit-content;
      }
    }

    .no-data-tips {
      text-align: center;
      padding-bottom: 10px;
      color: #999;
    }
  }

  .whole-hospital {
    padding: 0.2rem 0rem;
    background-color: #fff;
    min-height: 2rem !important;
    height: 7.7rem;
    flex: 1;

    .linkage-area {
      margin-top: 0.2rem;
      height: calc(100% - 0.45rem);

      .top {
        color: #999;
        font-size: calc(16px * var(--font-scale))!important;
        padding-left: 0.33rem;
        border-bottom: 1px solid #E5E5E5;
        line-height: 1rem;

        > div {
          // width 1.27rem
          margin-right: 0.8rem;
          text-align: left;
          display: inline-block;
          text-align: center;
          font-weight: 500;
        }
      }

      .content {
        background-color: #fff;
        color: $contentColor;
        font-size: 0.3rem;
        position: relative;
        height: calc(100% - 1.2rem);
        // max-height: 7rem;
        overflow: auto;

        .triangle {
          height: 0;
          border-width: 0 0.1rem 0.1rem;
          border-style: solid;
          border-color: transparent transparent $bgColor;
          position: absolute;
          top: -0.1rem;
        }

        .triangle0 {
          left: 0.45rem;
          transition: all 0.3s;
        }

        .triangle1 {
          transition: all 0.3s;
          left: 1.75rem;
        }

        .triangle2 {
          transition: all 0.3s;
          left: 3.15rem;
        }

        .cur-area-box {
          overflow: auto;

          .item-area {
            line-height: 1rem;
            padding-left: 0.3rem;
            font-size: calc(16px * var(--font-scale))!important;
            // padding: .3rem .3rem
          }
        }
      }
    }
  }
}

.content-null {
  width: 100%;
  text-align: center;
  margin-top: 1.5rem;

  .nullimg {
    width: 2.68rem;
  }

  .nulltext {
    margin-top: 0.75rem;
    color: #999;
    font-size: 0.3rem;
    text-align: center;
  }
}

.title-pl {
  padding-left: 0.32rem;
}

.title {
  color: $textColor;
  padding-top: 0.1rem;
  padding-bottom: 0.1rem;

  .iconfont {
    font-size: 0.36rem;
    color: #d6d6d8;
    vertical-align: middle;
  }

  .text {
    font-size: 0.28rem;
    vertical-align: middle;
  }
}

.active {
  color: #3562DB;
  border-bottom: 2px solid #3562DB;
}

.disClick {
  pointer-events: none;
}

.one {
  background-color: #FCFCFC;
}

.two {
  background-color: #fff;
}

.associated-service{
  // height :100vh;
  overflow :auto;
  background:#fff;
  .switch{
    height :.98rem;
    line-height :.98rem;
    padding-right:.28rem;
    text-align :right;
    border-bottom:1px solid #EFEFF4;
    color:#38C7C4;
    font-family:PingFang SC;
    font-style:italic;
  }
  .associated-list{
    .list-text{
      min-height :.98rem;
      border-bottom:1px solid #EFEFF4;
      span{
        display :block;
        padding:0.33rem .54rem .23rem 0.32rem;
        line-height :.42rem;
      }
    }
  }
}
</style>
