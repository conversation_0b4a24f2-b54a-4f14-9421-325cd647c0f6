<template>
  <div class="wrapper">
    <div class="content">
      <!-- 工单提醒 -->
      <div class="reminder" v-if="hint">
        <img class="reminder-img" src="@/assets/images/reminder.png" alt="" />
        <span class="reminder-text">{{ hint }}</span>
      </div>
      <!--临时用户不显示立刻、预约-->
      <div class="reservation-btns item-style" v-if="loginInfo">
        <span
          :class="{
            active: idx == appointmentType ? !reservation : reservation
          }"
          @click="handleClickTimeText(idx)"
          v-for="(item, idx) of serviceHoursText"
          :key="idx"
          >{{ item.dictLabel }}</span
        >
      </div>
      <!--选择医院（只有临时用户显示，扫描报修不显示医院(优先级最高)）-->
      <!-- <temp-select-hosp
        :hospitalInfo="hospitalInfo"
        :isFirst="first"
        v-if="!loginInfo && (projectName == 'repair') && !isScan"
        @locationHopsInfo="handleLocationHopsInfo"
      ></temp-select-hosp> -->
      <div v-if="!loginInfo">
        <!--只有游客（包括扫码报修）身份显示，并且已开放注册的医院才允许游客报修-->
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">手机号</label>
          </div>
          <phone-text
            :isShowPhoneText="false"
            @getSmsCode="handleGetSmsCode"
            @getPhoneNum="getPhoneNum"
            style="margin-right:-0.6em;padding:0"
          ></phone-text>
        </div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">验证码</label>
          </div>
          <input
            class="weui-input"
            maxlength="6"
            type="text"
            v-model="iptSmsCode"
            placeholder="请输入验证码"
          />
          <em class="login-tips" @click="$router.replace('/login')"
            >登录后,无需验证码</em
          >
        </div>
      </div>

      <!--其他项目只展示，不能选择-->
      <div v-if="otherProject == 'ipms'" class="other-location-name">
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务房间</label>
          </div>
          <div class="weui-cell__bd ipt-content">{{ localtionName }}</div>
        </div>
      </div>
      <!--根据派工工单计算显示属性-->
      <div v-if="requireCode != 2">
        <!--服务区域-->
        <div v-if="otherProject == ''">
          <div
            class="weui-cell border-rightbottom item-style"
            style="padding-right:0"
          >
            <div class="weui-cell__hd">
              <label class="weui-label title">服务区域</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input
                @click="toSelectArea"
                class="weui-input ipt ellipsis"
                type="text"
                readonly
                v-model="areaVal"
                placeholder="请选择服务区域"
              />
              <span
                class="iconfont icon-saomiao"
                @click="scanRepair"
                style="color:#38c7c4;padding-right:10px"
              ></span>
            </div>
          </div>
          <!--服务房间-->
          <div class="weui-cell border-rightbottom item-style site">
            <div class="weui-cell__hd">
              <label class="weui-label title">服务房间</label>
            </div>
            <div
              class="weui-cell__bd ipt-content "
              @click="toSelectSite"
              v-if="isExist"
            >
              <input
                class="weui-input ipt ellipsis"
                type="text"
                readonly
                v-model="localtionPlaceName"
                placeholder="请选择服务房间"
              />
              <span class="iconfont arrow">&#xe646;</span>
            </div>
          </div>
        </div>
        <!--服务事项-->
        <div
          class="weui-cell item-style matter-style border-rightbottom"
          v-if="this.isItem == 'Y'"
          @click="goToSelectMatterlists"
        >
          <div class="weui-cell__hd">
            <label class="weui-label title">服务事项</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt">
            <div class="ipt-pos">
              <input
                class="weui-input ipt ellipsis"
                type="text"
                v-model="matterlists"
                :class="{ matterInput: matterlistsState }"
                readonly
                placeholder="请选择服务事项"
              />
              <!-- <span v-text="matterlists"></span> -->
            </div>
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>
      </div>
      <!--未登录扫描报修，加入联系人、联系电话-->
      <!-- <div v-if="isScan && !loginInfo">
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">联系人</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input
              class="weui-input ipt"
              type="text"
              v-model="contactName"
              placeholder="请输入联系人"
            />
          </div>
        </div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">联系电话</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input
              class="weui-input ipt"
              type="tel"
              v-model="phone"
              readonly
              oninput="if(value.length>13)value=value.slice(0,13)"
              @blur="handleTextareaBlurEvent"
              placeholder
            />
          </div>
        </div>
      </div> -->
      <!--预约时间-->
      <service-hours
        ref="serviceHours"
        v-if="reservation && loginInfo"
      ></service-hours>

      <!--根据派工工单计算显示属性-->
      <div v-if="loginInfo && requireCode == 2">
        <!--工单类型为综合维修，显示服务区域-->
        <div v-if="projectName == 'repair'">
          <div
            class="weui-cell border-rightbottom item-style"
            style="padding-right:0"
          >
            <div class="weui-cell__hd">
              <label class="weui-label title">服务区域</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input
                class="weui-input ipt ellipsis"
                type="text"
                readonly
                @click="toSelectArea"
                v-model="areaVal"
                placeholder="请选择服务区域"
              />
              <span
                class="iconfont icon-saomiao"
                @click="scanRepair"
                style="color:#38c7c4;padding-right:10px"
              ></span>
            </div>
          </div>
        </div>
      </div>

      <!--申报描述-->
      <div class="weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea"
              :placeholder="placeholder"
              maxlength="500"
              rows="5"
              ref="textarea"
              v-model="value"
              @keydown="keydown($event)"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
        <upload-voice
          ref="voices"
          @getvVoiceParams="getVoice"
          @voiceFlag="deleteVoice"
        ></upload-voice>
      </div>
      <!--图片上传-->
      <upload-image
        class="bottom"
        style="position:relative"
        ref="imgs"
        v-bind:num="this.amount"
        @getImgParams="getImages"
        @deleteImg="deleteImg"
      ></upload-image>
      <div :class="[rotate ? 'down-content' : 'up-content']">
        <!--根据派工工单计算显示属性-->
        <div v-if="requireCode == 2">
          <!--非综合维修工单，此处显示服务区域-->
          <div v-if="otherProject == ''">
            <!--非综合维修工单，此处显示服务区域-->
            <div
              class="weui-cell border-rightbottom item-style"
              style="padding-right:0"
              v-if="projectName != 'repair'"
            >
              <div class="weui-cell__hd">
                <label class="weui-label title">服务区域</label>
              </div>
              <div class="weui-cell__bd ipt-content">
                <input
                  class="weui-input ipt ellipsis"
                  type="text"
                  readonly
                  @click="toSelectArea"
                  v-model="areaVal"
                  placeholder="请选择服务区域"
                />
                <span
                  class="iconfont icon-saomiao"
                  @click="scanRepair"
                  style="color:#38c7c4;padding-right:10px"
                ></span>
              </div>
            </div>
            <!--服务房间-->
            <div class="weui-cell border-rightbottom item-style site">
              <div class="weui-cell__hd">
                <label class="weui-label title">服务房间</label>
              </div>
              <div
                class="weui-cell__bd ipt-content "
                @click="toSelectSite"
                v-if="isExist"
              >
                <input
                  class="weui-input ipt ellipsis"
                  type="text"
                  readonly
                  v-model="localtionPlaceName"
                  placeholder="请选择服务房间"
                />
                <span class="iconfont arrow">&#xe646;</span>
              </div>
            </div>
          </div>
          <!--服务事项-->
          <div
            class="weui-cell item-style matter-style border-rightbottom"
            v-if="this.isItem == 'Y'"
            @click="goToSelectMatterlists"
          >
            <div class="weui-cell__hd">
              <label class="weui-label title">服务事项</label>
            </div>
            <div class="weui-cell__bd ipt-content matter-ipt">
              <div class="ipt-pos">
                <input
                  class="weui-input ipt ellipsis"
                  type="text"
                  v-model="matterlists"
                  :class="{ matterInput: matterlistsState }"
                  readonly
                  placeholder="请选择服务事项"
                />
                <!-- <span v-text="matterlists"></span> -->
              </div>
              <span class="iconfont arrow">&#xe646;</span>
            </div>
          </div>
        </div>
        <!--紧急程度-->
        <urgent-level ref="urgent" :urgentArr="urgentArr"></urgent-level>
        <!--申报属性-->
        <declare-attribute
          v-if="otherProject != 'ipms'"
          ref="attribute"
          :declareAttributeArr="declareAttributeArr"
        ></declare-attribute>
        <!--联系电话-->
        <!-- <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">联系电话</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input
              class="weui-input ipt"
              type="tel"
              v-model="phone"
              oninput="if(value.length>13)value=value.slice(0,13)"
              @blur="handleTextareaBlurEvent"
              placeholder="请输入联系人电话"
            />
          </div>
        </div>-->
        <!--是否返修-->
        <is-rework ref="isRework"></is-rework>
      </div>
      <!--收起和完善信息-->
      <div class="adjustable" v-if="loginInfo">
        <div @click="switchBtn" style="color:#38c7c4">
          <span class="hint">信息越详细</span>
          <img
            src="~images/newForm/btn-fold.gif"
            :class="[!rotate ? 'down' : 'up']"
          />
          <span class="hint">报修更快速</span>
        </div>
      </div>
      <div class="tips" v-else>
        点我“
        <span class="key-tips" @click="toRegister">注册</span
        >”，实现工单跟踪，享受更多服务。
      </div>
    </div>

    <div class="submit-btn" :class="{ active: canShow }">
      <button class="weui-btn weui-btn_primary" @click="submitAfterVrification">
        提交
      </button>
    </div>
  </div>
</template>

<script>
const wx = require("weixin-js-sdk");
import utils from "@/utils/Global";
import { mapState } from "vuex";
import { preView } from "@/common/lib/util.js";
import UploadImage from "@/common/uploadImg/uploadImg";
import UploadVoice from "@/common/uploadVoice/UploadVoice";
// import TempSelectHosp from "./components/TempSelectHosp";
import UrgentLevel from "./components/UrgentLevel";
import ServiceHours from "./components/ServiceHours";
import DeclareAttribute from "./components/DeclareAttribute";
import IsRework from "./components/IsRework";
import Popups from "@/common/customize/Popups";
import PhoneText from "@/common/PhoneText";
export default {
  name: "CleaningWorkOrder",
  components: {
    UploadImage,
    UploadVoice,
    // TempSelectHosp,
    UrgentLevel,
    ServiceHours,
    DeclareAttribute,
    IsRework,
    Popups,
    PhoneText
  },
  data() {
    return {
      value: "",
      phone: "", //需联系电话和临时用户需发送验证码的手机号
      isExist: true,
      canShow: false,
      voiceParams: "",
      imagesParams: [],
      imageConfig: [], //处理图片的mediaId和ossUrl
      imgFlag: false,
      accessToken: "",
      isOnlyVoice: false,
      text: "",
      areaVal: "",
      localtionName: "",
      localtion: "",
      localtionPlaceName: "",
      localtionPlaceCode: "",
      serviceSiteResId: "",
      allSiteDataInfo: [], //请求返回所有的相关地点的信息
      popData: {},
      matterlistsObj: {}, //服务事项
      matterlistsState: "", //服务事项(展示ipt)
      matterlists: "", //服务事项(展示)
      workId: "", //用于巡检
      otherProject: "", //是否为其他项目（仅调用该页面）
      projectName: "", //值为service时是便民服务工单，repair是报修
      rotate: true, //完善申报信息时候旋转
      tempSelectHosp: {}, //临时用户所选或定位的医院
      isTTYY: false, //临时用户是否为天坛医院公众号使用者，天坛医院不显示显示医院，直接为默认hospitalCode、unitCode
      reservation: false, // 点击预约
      first: true, //是否首次打开此页面，只有第一次才自动定位
      unitCode: "", //记录扫码报修的医院信息，定位的医院信息存到浏览器中了
      hospitalCode: "", //记录扫码报修的医院信息，定位的医院信息存到浏览器中了
      isScan: false, //是否为扫描报修，扫描报修不显示选择医院
      urgentArr: [], //紧急程度字典项
      declareAttributeArr: [], //申报属性字典项
      serviceHoursText: [], //服务时间（预约、立刻）字典项
      appointmentType: "", //服务时间（预约、立刻）字典项0 或1
      contactName: "", //未登录，扫描报修需添加联系人和联系人电话
      smsCode: "", //临时用户需发送验证码，验证码成功后才可以报修
      iptSmsCode: "", //临时用户需发送验证码，验证码成功后才可以报修(用户输入的)
      isRegister: 0, //临时用户报修的医院是否为开放状态
      requireCode: 2, //默认关闭-非必填
      isSubmit: true, //节流标示
      placeholder:
        "请输入您要申报的内容描述、语音，字数在500字以内，语音限制60秒",
      requiredCode: "", //必选项内容
      hint: "", //工单配置提醒
      isItem: "", //服务事项展示判断  y展示  n 不展示
      amount: {
        //上传图片限制
        quantity: 3,
        capital: "三"
      },
      userType: "", //用户类型
      alarmId: "",
      workOrdeInfoDate: []
    };
  },
  computed: {
    ...mapState([
      "loginInfo",
      "staffInfo",
      "changeLocation",
      "hospitalInfo",
      "openId",
      "serviceAreaTreeData"
    ])
  },
  methods: {
    /**
     * 工单配置提醒接口
     */
    getTaskWorkConfiguration(hospitalCode, unitCode) {
      let code = "";
      if (this.$route.path.indexOf("repair") != -1) {
        code = "1";
      } else if (this.$route.path.indexOf("service") != -1) {
        code = "6";
      } else if (this.$route.path.indexOf("cleaning") != -1) {
        code = "2";
      }
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?getTaskWorkConfiguration",
          this.$qs.stringify({
            hospitalCode,
            unitCode,
            workTypeCode: code
            // this.$route.path.indexOf("repair")!=-1
            //   ? "1"
            //   : this.$route.path.indexOf("service")!=-1
            //   ? "6"
            //   : "2" //6是便民服务，2是保洁，1是维修
          }),
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.isItem = res.isItem;
          this.hint = res.hint;
          this.workCode = res.workTypeCode;
        });
    },
    /**
     * 获取手机短信验证码
     */
    handleGetSmsCode(smscode, phone) {
      this.phone = phone;
      this.smsCode = smscode;
    },
    /**
     * 当电话号改变时接收电话号码
     */
    getPhoneNum(num) {
      this.phone = num;
    },
    /**
     * 点击注册
     */
    toRegister() {
      this.$router.push({
        path: "/registerNew"
      });
    },
    /**
     * 点击立刻
     */
    handleClickTimeText(index) {
      if (index == 0) {
        this.reservation = false;
      } else {
        this.reservation = true;
      }
    },
    handleTextareaBlurEvent() {
      let scrollHeight =
        document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 是否展示除地点以外的信息
     */
    switchBtn() {
      this.rotate = !this.rotate;
    },
    /**
     * 区域选择
     */
    toSelectArea() {
      //级联弹窗数据还原
      this.localtionPlaceName = "";
      this.localtionPlaceCode = "";
      this.$router.push({
        path: "/selectArea",
        query: {
          routerPath: this.$route.path
        }
      });
    },
    /**
     * 服务房间请求函数
     */
    toSelectSite() {
      console.log("qq", this.serviceAreaTreeData);
      let arr = this.serviceSiteResId.split(",");
      if (this.serviceSiteResId == "") {
        $.toast("请先选择服务区域", "text");
      } else {
        this.showVal(this.serviceAreaTreeData, arr[arr.length - 1]);
        return;
        if (this.loginInfo && !location.href.includes("hospitalCode")) {
          //注册并已登录用户,而且不是通过扫码进行报修的
          this.$api
            .cascadingQueryHospitalGridInfo({
              gridId: arr[arr.length - 1]
            })
            .then(this.showVal);
        } else {
          //临时用户
          this.axios
            .post(
              process.env.API_BASE +
                "/hospitalController/cascadingQueryHospitalGridInfo",
              this.$qs.stringify({
                unitCode: this.unitCode
                  ? this.unitCode
                  : sessionStorage.unitCode,
                hospitalCode: this.hospitalCode
                  ? this.hospitalCode
                  : sessionStorage.hospitalCode,
                gridId: arr[arr.length - 1]
              })
            )
            .then(this.showVal);
        }
      }
    },
    /**
     * 仅供服务房间展示，调起弹窗并设置相关参数
     */
    showVal(res, gridId) {
      // if (res.data && res.data.code == 200) {
      //   this.allSiteDataInfo = res.data.data;
      // } else {
      //   this.allSiteDataInfo = res;
      // }
      this.allSiteDataInfo = res.filter(item => {
        return item.parentId == gridId;
      });
      //将所有地点取出放到数组中
      let siteArr = [];
      for (let i = 0; i < this.allSiteDataInfo.length; i++) {
        siteArr.push(this.allSiteDataInfo[i]);
      }
      //跳到搜索页面
      this.$router.push({
        name: "Site",
        params: {
          list: siteArr
        }
      });
    },
    /**
     * 获取录音路径
     * @param params
     */
    getVoice(params) {
      if (this.isOnlyVoice) {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode
                ? this.hospitalCode
                : sessionStorage.hospitalCode
                ? sessionStorage.getItem("hospitalCode")
                : "BJSJTYY",
              unitCode: this.unitCode
                ? this.unitCode
                : sessionStorage.unitCode
                ? sessionStorage.getItem("unitCode")
                : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params,
              type: "1"
            })
          )
          .then(res => {
            this.voiceParams = res.data.data.ossUrl;
            this.isMissingItem();
          });
      }
    },
    /**
     * 删除录音
     */
    deleteVoice() {
      this.voiceParams = "";
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode
              ? this.hospitalCode
              : sessionStorage.hospitalCode
              ? sessionStorage.getItem("hospitalCode")
              : "BJSJTYY",
            unitCode: this.unitCode
              ? this.unitCode
              : sessionStorage.unitCode
              ? sessionStorage.getItem("unitCode")
              : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: this.voiceParams
          })
        )
        .then(res => {});
    },
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode
              ? this.hospitalCode
              : sessionStorage.hospitalCode
              ? sessionStorage.getItem("hospitalCode")
              : "BJSJTYY",
            unitCode: this.unitCode
              ? this.unitCode
              : sessionStorage.unitCode
              ? sessionStorage.getItem("unitCode")
              : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      // console.log(params[i]);
      // console.log(localId[i]);
      // console.log(this.accessToken);
      // return
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode
                ? this.hospitalCode
                : sessionStorage.hospitalCode
                ? sessionStorage.getItem("hospitalCode")
                : "BJSJTYY",
              unitCode: this.unitCode
                ? this.unitCode
                : sessionStorage.unitCode
                ? sessionStorage.getItem("unitCode")
                : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
            // this.imagesParams.push(res.data.data.ossUrl);
            // this.imageConfig.push(res.data.data);
          });
      });
    },
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    },
    /**
     * 处理提交按钮事件
     */
    handleSubmitClick() {
      if (
        this.$refs.imgs.imgServerId.length != 0 &&
        this.$refs.voices.hasVoice != "recorded"
      ) {
        //上传图片、没有录音
        let imgLen = () => {
          if (this.imagesParams.length != 0) {
            this.isMissingItem();
          } else {
            setTimeout(() => {
              imgLen();
            }, 500);
          }
        };
        imgLen();
      } else if (
        this.$refs.imgs.imgServerId.length != 0 &&
        this.$refs.voices.hasVoice == "recorded"
      ) {
        //上传图片、录音
        let imgLen = () => {
          if (this.imagesParams.length != 0) {
            this.isOnlyVoice = true;
            this.$refs.voices.saveVoice();
          } else {
            setTimeout(() => {
              imgLen();
            }, 500);
          }
        };
        imgLen();
      } else if (
        this.$refs.imgs.imgServerId.length == 0 &&
        this.$refs.voices.hasVoice == "recorded"
      ) {
        //不需上传图片、但有录音
        this.isOnlyVoice = true;
        this.$refs.voices.saveVoice();
      } else if (
        this.$refs.imgs.imgServerId.length == 0 &&
        this.$refs.voices.hasVoice == "unvoice"
      ) {
        //不需上传图片、没有录音
        this.isMissingItem();
      }
    },
    /**
     * 获取签名
     */
    getConfigParams() {
      // 进行签名的时候  Android 不用使用之前的链接， ios 需要
      let signLink = /(Android)/i.test(navigator.userAgent)
        ? location.href.split("#")[0]
        : window.entryUrl;
      let paramValue = {
        localUrl: signLink,
        appId: process.env.WxAppid
      };
      this.axios
        .get(process.env.API_HOST + "/accessToken/getSignature", {
          params: paramValue
        })
        .then(this.setAllConfig);
    },
    /**
     * 设置微信config参数
     * @param res
     */
    setAllConfig(res) {
      this.$refs.voices.setConfig(res);
      this.$refs.voices.initRecord();
      this.$refs.imgs.setConfig(res);
      this.accessToken = res.data.data.accessToken;
    },
    /**
     * 跳转选择服务事项页面
     */
    goToSelectMatterlists() {
      let unitCode = this.unitCode
        ? this.unitCode
        : sessionStorage.unitCode
        ? sessionStorage.getItem("unitCode")
        : "";
      let hospitalCode = this.hospitalCode
        ? this.hospitalCode
        : sessionStorage.hospitalCode
        ? sessionStorage.getItem("hospitalCode")
        : "";
      this.$router.push({
        path: "/matterlists",
        query: {
          workTypeCode: "1",
          projectName: "repairPro",
          unitCode: unitCode,
          hospitalCode: hospitalCode
        }
      });
    },
    /**
     * 提交前判断
     */
    getHttp() {
      if (!this.loginInfo) {
        //游客当前选择医院的信息为“该医院未开放注册”
        let that = this;
        if (!this.hospitalInfo.hospitalCode && !this.isScan) {
          $.toast("请选择医院", "text");
          return;
        } else if (
          (this.hospitalInfo.isRegister && this.hospitalInfo.isRegister == 0) ||
          (!this.hospitalInfo.isRegister && this.isRegister == 0)
        ) {
          $.toast("该医院未开放，请联系管理员", "text", function() {
            that.$router.replace("/login");
          });
        } else {
          this.submitFun();
        }
      } else {
        this.submitFun();
      }
    },
    /**
     * 工单提交请求函数
     */
    submitFun() {
      if (!this.isSubmit) {
        $.toast("该工单已经提交,请勿重复提交", "text");
        return;
      }
      if (this.$refs.imgs.imgLocalIds.length != this.imagesParams.length) {
        $.toast("请等待图片上传完毕", "text");
        return;
      }
      this.isSubmit = false;
      $.showLoading("提交中…");
      let sourcesFloorName, sourcesFloor;
      // if (!this.staffInfo || this.staffInfo.gridList.length == 0) {
      //   sourcesFloorName = "";
      //   sourcesFloor = "";
      // } else {
      //   let params = this.staffInfo.gridList[0];
      //   sourcesFloorName =
      //     params.parentGridName.replace(">", "") +
      //     this.staffInfo.gridList[0].gridName;
      //   sourcesFloor = this.staffInfo.gridList[0].allParentId;
      // }
      if (this.otherProject == "") {
        //如果值为ipms巡检，不需要设置这两个值，直接带过来的
        if (this.changeLocation.localtionPlaceCode) {
          let arr = this.localtion.split(",");
          if (arr.length == 4) {
            arr.splice(arr.length - 1, 1);
            let str = arr.join(",");
            this.localtion = str + "," + this.changeLocation.localtionPlaceCode;
          } else {
            this.localtion =
              this.localtion + "," + this.changeLocation.localtionPlaceCode;
          }

          this.localtionName = this.areaVal + this.localtionPlaceName;
        }
      }

      let param = null;
      let unitCode = "";
      let hospitalCode = "";
      if (this.isScan) {
        //扫码报修，无论登录不登录都是用二维码中的医院信息
        unitCode = this.unitCode;
        hospitalCode = this.hospitalCode;
      } else {
        unitCode = this.loginInfo
          ? this.loginInfo.userOffice[0].unitCode
          : sessionStorage.unitCode
          ? sessionStorage.getItem("unitCode")
          : "";
        hospitalCode = this.loginInfo
          ? this.loginInfo.userOffice[0].hospitalCode
          : sessionStorage.hospitalCode
          ? sessionStorage.getItem("hospitalCode")
          : "";
      }
      let params = {
        hospitalCode: hospitalCode,
        deptCode: "",
        unitCode: unitCode,
        userId: this.loginInfo.id,
        phoneNumber: this.loginInfo.phone,
        realName: this.loginInfo.staffName,
        workTypeCode:
          this.projectName == "repair"
            ? "1"
            : this.projectName == "service"
            ? "6"
            : "2", //6是便民服务，2是保洁，1是维修
        deptName: this.staffInfo.officeName,
        questionDescription: this.value,
        urgencyDegreeCode: this.$refs.urgent.urgent,
        jobNumber: this.staffInfo.staffNumber,
        accessToken: this.accessToken,
        // type: this.loginInfo ? this.loginInfo.type : 1,
        type: this.userType,
        job: this.staffInfo.administrativePost,
        appointmentType: this.loginInfo ? (this.reservation ? "1" : "0") : 0,
        appointmentDate: this.loginInfo
          ? this.reservation
            ? this.$refs.serviceHours.reservation
            : ""
          : "",
        localtionName: this.localtionName,
        localtion: this.localtion,
        contactNumber: this.phone,
        itemDetailCode: this.matterlistsObj.itemDetailCode,
        itemDetailName: this.matterlistsObj.itemDetailName,
        itemServiceCode: this.matterlistsObj.itemServiceCode,
        itemServiceName: this.matterlistsObj.itemServiceName,
        itemTypeCode: this.matterlistsObj.itemTypeCode,
        itemTypeName: this.matterlistsObj.itemTypeName,
        sourcesFloorName: sourcesFloorName,
        sourcesFloor: sourcesFloor,
        repairWork: this.$refs.isRework.isRework,
        staffId: this.staffInfo.staffId,
        workSources: "1", //巡检为3,其余为1
        callerCompanyCode: this.loginInfo
          ? this.loginInfo.userOffice[0].companyCode
          : "", //用于巡检
        callerCompanyName: this.loginInfo
          ? this.loginInfo.userOffice[0].companyName
          : "", //用于巡检
        typeSources:
          this.otherProject == "ipms" ? "3" : this.$refs.attribute.attr, //巡检为3
        contactName: this.contactName, //未登录扫描报修加入的联系人
        dispatchingConfig: this.requireCode //	移动申报自动派工标识
      };
      params.sysForShort = this.alarmId;
      params.callerTape = this.voiceParams;
      params.attachment = JSON.stringify(this.imagesParams);
      param = this.$qs.stringify(params);
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat",
          param,
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(this.handleRequestSucc);
    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      if (res.data.code == "200") {
        this.axios
          .post(process.env.API_BJZX + "/alarm/record/limWorkInfo", {
            workNum: res.data.data.workNum,
            workType: "1",
            workTypeName: "综合维修",
            limAlarmId: this.alarmId
          })
          .then(res => {
            if (res.data.code == 200) {
            } else {
              console.log(res.data.msg);
            }
          });
        $.toast("工单提交成功", "success", () => {
          sessionStorage.removeItem("hospitalCode");
          sessionStorage.removeItem("unitCode");
          sessionStorage.removeItem("url");
        });
        if (this.detector.browser.name != "micromessenger") {
          api.closeWin();
        } else {
          //判断进入来源页面是公众号还是其他项目
          if (this.otherProject == "ipms") {
            //其他项目登录
            if (localStorage.prevLinkUrl) {
              window.location.href =
                localStorage.prevLinkUrl +
                "?workNum=" +
                res.data.data.workNum +
                "&workId=" +
                this.workId;
            }
          } else {
            window.location.href = process.env.WX + "/personalCenter";
          }
        }
      } else {
        $.toast(res.data.message, "text");
        if (res.data.code == 40001) {
          this.voiceParams = "";
          this.$refs.voices.handleDelVoiceClick();
        }
      }
      $.hideLoading();
    },
    /**
     * 校验必填选项
     */
    isMissingItem() {
      if (!this.loginInfo) {
        let params = {
          hospitalCode: this.hospitalInfo.hospitalCode,
          unitCode: this.hospitalInfo.unitCode,
          workTypeCode:
            this.projectName == "repair"
              ? "1"
              : this.projectName == "service"
              ? "6"
              : "2"
        };
        this.getIsRequired(params);
      } else {
        let params = {
          hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
          unitCode: this.loginInfo.userOffice[0].unitCode,
          workTypeCode:
            this.projectName == "repair"
              ? "1"
              : this.projectName == "service"
              ? "6"
              : "2"
        };
        this.getIsRequired(params);
      }
    },
    getIsRequired(params) {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement/getIsRequired",
          this.$qs.stringify(params),
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.requiredCode = res.requiredCode;
          this.requiredParameter(res.requiredCode);
        });
    },
    /**
     * 必选项验证
     */
    requiredParameter(value) {
      let reg = /^((13|14|15|17|18)[0-9]{9})$/;
      if (
        !this.loginInfo &&
        !this.phone &&
        ((this.hospitalInfo.isRegister && this.hospitalInfo.isRegister == 1) ||
          (!this.hospitalInfo.isRegister && this.isRegister == 1))
      ) {
        if (this.phone == "") {
          $.toast("请填写手机号及验证码", "text");
          return;
          // } else if (!reg.test(this.phone)) {
        } else if (this.phone.length < 11) {
          //2020-5-19 修改验证方式为11位长度
          $.toast("请输入正确的手机号", "text");
          return;
        } else if (this.iptSmsCode == "") {
          $.toast("请输入验证码", "text");
          return;
        }
      } else if (!this.loginInfo && this.iptSmsCode == "") {
        $.toast("请输入验证码", "text");
        return;
      } else if (this.iptSmsCode != this.smsCode) {
        $.toast("验证码不正确", "text");
        return;
      }
      if (value.indexOf("localtion", 0) != -1 && this.areaVal == "") {
        if (!this.loginInfo) {
          if (this.otherProject == "ipms" || this.requireCode == 2) {
            console.log("");
          } else {
            $.toast("请选择服务区域", "text");
            return;
          }
        } else {
          if (this.otherProject == "ipms") return true;
          else $.toast("请选择服务区域", "text");
          return;
        }
      }
      if (!this.value && !this.voiceParams) {
        $.toast("请填写申报描述或录入语音", "text");
        return false;
      }
      // else if (
      //   value.indexOf("itemTypeCode", 0) != -1 &&
      //   !this.matterlistsObj.itemTypeCode &&
      //   this.isItem == "Y"
      // ) {
      //   $.toast("请选择服务事项", "text");
      //   return false;
      // }
      else if (
        this.appointmentType == 1 &&
        !this.$refs.serviceHours.reservation
      ) {
        $.toast("请填写预约时间", "text");
        return false;
      } else {
        this.getHttp();
      }
    },
    /**
     * 校验联系电话的格式
     */
    submitAfterVrification() {
      // var reg = /^((0\d{2,3}-\d{7,8})|(1[358479]\d{9})|(\d{7,8}))$/;
      // if (!reg.test(this.phone) && this.phone != "") {
      //2020-5-19 修改为仅验证长度11位
      if (this.phone.length < 11 && this.phone != "") {
        $.toast("请输入正确的电话号", "text");
      } else {
        this.handleSubmitClick();
      }
    },

    /**
     * 临时用户定位的医院信息
     * @param par
     */
    // handleLocationHopsInfo(par) {
    //   this.tempSelectHosp = par;
    //   this.getHospitalDispatchingConfig(par.unitCode, par.hospitalCode);
    // },
    /**
     * 判断该医院是否开放注册
     */
    getHospitalIsRisterByCode(unitCode, hospitalCode) {
      this.$api
        .getHospitalIsRisterByCode({
          unitCode: unitCode,
          hospitalCode: hospitalCode
        })
        .then(res => {
          this.isRegister = res.isRegister;
        });
    },
    /**
     * 扫码报修获取地点
     */
    scanGetLocation() {
      this.isScan = true;
      let query = this.$route.query;
      //所有参数通过扫描中获取
      this.serviceSiteResId = query.id; //二维码中给的是楼宇ID
      this.unitCode = query.unitCode;
      this.hospitalCode = query.hospitalCode;

      this.getHospitalIsRisterByCode(query.unitCode, query.hospitalCode);
      this.getHospitalDispatchingConfig(query.unitCode, query.hospitalCode);

      this.getConfigParams();
      this.axios
        .post(
          process.env.API_BASE + "/hospitalController/getHospitalGridInfoById",
          this.$qs.stringify({
            unitCode: this.unitCode,
            hospitalCode: this.hospitalCode,
            id: this.serviceSiteResId
          })
        )
        .then(this.getHospitalGridInfoByIdCallBack);
    },
    getHospitalGridInfoByIdCallBack(res) {
      if (res.data.data.gridLevel == "3" || res.data.data.gridLevel == "2") {
        //4为楼层,2为区
        //二维码中ID为楼宇，不显示服务房间
        this.isExist = false;
      }
      let staffGrid = res.data.data;
      this.serviceSiteResId = staffGrid.id; //服务区域id
      this.localtion = staffGrid.allParentId; //服务区域id
      this.areaVal = staffGrid.allParentName.replaceAll(">", "", false); //服务区域名称,展示的
      this.localtionName = staffGrid.allParentName.replaceAll(">", "", false); //服务区域名称
    },
    /**
     * 判断是否已经选择了医院
     */
    isSelectHosp() {
      //临时用户如果不选医院则不能完成其他操作，没有hospitalCode
      if (!sessionStorage.getItem("hospitalCode") && !this.loginInfo) {
        return false;
      } else {
        return true;
      }
    },
    /**
     * 获取医院自定义的字典项接口
     */
    getPersonnelDictionaryFun(type) {
      let params = {
        type: type
      };
      if (type == 2) {
        params.states = 2;
      }
      this.$api.getPersonnelDictionary(params).then(res => {
        switch (type) {
          case 1:
            this.urgentArr = res.reverse();
            break;
          case 2:
            this.declareAttributeArr = res;
            break;
          case 4:
            this.serviceHoursText = res;
            this.appointmentType = res[0].dictValue;
            if (this.appointmentType == 1) {
              this.reservation = true;
            }
            break;
        }
      });
    },
    getHospitalDispatchingConfig(hospitalCode, unitCode) {
      this.$api
        .getHospitalDispatchingConfig({
          hospitalCode: hospitalCode,
          unitCode: unitCode
        })
        .then(res => {
          //需要必填字段
          this.requireCode = res;
        });
    },
    //空格校验
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    },
    // 接收服务事项的数据
    getlistTypeName() {
      if (this.$route.query.source == "service") {
        let typelist = this.$route.query.item || {};
        if (!typelist.itemServiceName) return;
        this.matterlistsObj = typelist;
        this.matterlists =
          typelist.itemTypeName +
          "-" +
          typelist.itemDetailName +
          "-" +
          typelist.itemServiceName;
      } else {
        return;
      }
    },
    scanRepairCallBack(scanData) {
      let result = scanData;
      let unitCode = this.unitCode ? this.unitCode : sessionStorage.unitCode;
      let hospitalCode = this.hospitalCode
        ? this.hospitalCode
        : sessionStorage.hospitalCode;
      if (result[0] == "ihsp") {
        this.axios
          .post(
            process.env.API_BASE +
              "/hospitalController/getHospitalGridInfoById",
            this.$qs.stringify({
              unitCode: unitCode || this.loginInfo.userOffice[0].unitCode,
              hospitalCode:
                hospitalCode || this.loginInfo.userOffice[0].hospitalCode,
              gridCode: result[result.length - 1]
            })
          )
          .then(data => {
            let res = data.data;
            if (res.code == 200) {
              let staffGrid = res.data;
              if (staffGrid.allParentId) {
                let placeNameArr = staffGrid.allParentName.split(">");
                let placeCodeArr = staffGrid.allParentId.split(",");
                //增加未完工工单列表
                let lastAreaCode = placeCodeArr[placeCodeArr.length - 1];
                this.$api
                  .getUnfinishedMineTask({
                    userId: this.loginInfo.id,
                    // staffType: this.loginInfo.type,
                    staffType: this.userType,
                    type: "1,2,3,4",
                    curPage: 1,
                    pageSize: 999,
                    startTime: "",
                    endTime: "",
                    areaCode: lastAreaCode,
                    staffId: this.staffInfo.staffId
                  })
                  .then(resP => {
                    if (this.userType == 2) {
                      if (resP.details.length > 0) {
                        $.toast(
                          "当前区域中有未完成工单，待完工后再进行扫码申报！",
                          "text"
                        );
                        this.$router.push(
                          "/notCompleteOrderList?areaCode=" + lastAreaCode
                        );
                      }
                    } else if (this.userType == 1) {
                      if (resP.length > 0) {
                        $.toast(
                          "当前区域中有未完成工单，待完工后再进行扫码申报！",
                          "text"
                        );
                        this.$route.push(
                          "/notCompleteOrderList?areaCode=" + lastAreaCode
                        );
                      }
                    }
                  });

                this.serviceSiteResId = staffGrid.id; //服务区域id
                this.localtion = staffGrid.allParentId; //服务区域id
                if (placeCodeArr.length == 4) {
                  this.localtionPlaceName = placeNameArr.pop();
                } else {
                  this.localtionPlaceName = "";
                }
                this.areaVal = placeNameArr.join(""); //服务区域名称,展示的
                this.localtionName = staffGrid.allParentName.replaceAll(
                  ">",
                  "",
                  false
                ); //服务区域名称
              } else {
                $.toast("未找到相关位置", "text");
              }
            }
          });
      } else {
        $.toast("请检查二维码是否正确", "text");
      }
    },
    scanRepair() {
      // this.scanRepairCallBack(
      // "ihsp,ZXYSZGDW,ZKYXYY,ZKYXYY0200402002".split(",")
      // );
      window.wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ["qrCode", "barCode"],
        success: res => {
          let result = res.resultStr.split(","); // 当needResult 为 1 时，扫码返回的结果
          this.scanRepairCallBack(result);
        }
      });
    }
  },

  mounted() {
    this.workOrdeInfoDate =
      JSON.parse(this.$route.query.workOrdeInfoDate) || [];
    console.log("ooo", this.workOrdeInfoDate);
    setTimeout(() => {
      this.value = this.workOrdeInfoDate[0].questionDescription;
      this.$refs.isRework.changeRework(this.workOrdeInfoDate[0].repairWork);
      this.$refs.urgent.changeUrgent(
        this.workOrdeInfoDate[0].urgencyDegreeCode
      );
      this.$refs.attribute.changeAttr(this.workOrdeInfoDate[0].typeSourcesCode);
      this.handleClickTimeText(this.workOrdeInfoDate[0].appointmentDate);
      this.matterlists =
        this.workOrdeInfoDate[0].itemType[0].itemTypeName +
        "-" +
        this.workOrdeInfoDate[0].itemType[0].itemDetailName +
        "-" +
        this.workOrdeInfoDate[0].itemType[0].itemServiceName;
      this.matterlistsObj = this.workOrdeInfoDate[0].itemType[0];
      if (this.workOrdeInfoDate[0].localtionId.split(",").length == 3) {
        this.areaVal = this.workOrdeInfoDate[0].itemType[0].localtion;
        this.localtionName = this.workOrdeInfoDate[0].itemType[0].localtion;
        this.location = this.workOrdeInfoDate[0].localtionId;
      } else {
        let lastId = this.workOrdeInfoDate[0].localtionId.split(",")[
          this.workOrdeInfoDate[0].localtionId.split(",").length - 1
        ];
        this.axios
          .get(process.env.API_BASE + "/space/spaceInfo/lookUpById", {
            params: {
              id: lastId
            },
            headers: {
              hospitalCode: "BJSJTYY",
              unitCode: "BJSYGJ"
            }
          })
          .then(res => {
            if (res.data.code == 200) {
              this.location = this.workOrdeInfoDate[0].localtionId;
              this.localtionName = this.workOrdeInfoDate[0].itemType[0].localtion;
              this.areaVal = res.data.data.simName.replaceAll(">", "", false);
              this.localtionPlaceName = res.data.data.ssmName || "";
            }
          });
      }
    }, 500);
    this.alarmId = this.$route.query.alarmId || "";
    // console.log(this?.staffInfo?.teamId);
    this.userType = this.staffInfo.teamId ? 2 : 1;
    // 接收服务事项的数据
    // this.getlistTypeName()
    /*获取字典项*/
    if (this.loginInfo) {
      this.getPersonnelDictionaryFun(1);
      this.getPersonnelDictionaryFun(2);
      this.getPersonnelDictionaryFun(4);
    }
    //      else{
    //        this.isTTYY = process.env.WxAppcode == "ZJT" ? true : false
    /*if(this.isTTYY){
          sessionStorage.setItem("unitCode",process.env.UnitCode)
          sessionStorage.setItem("hospitalCode","BJTTYY")
          this.getHospitalIsRisterByCode(process.env.UnitCode,"BJTTYY")
        }*/
    //      }
    //存储appId
    localStorage.wxAppId = process.env.WxAppid;
    let _this = this;
    if (this.loginInfo) {
      //已经登录的用户直接获取
      this.getHospitalDispatchingConfig(
        this.loginInfo.userOffice[0].hospitalCode,
        this.loginInfo.userOffice[0].unitCode
      );
    } else {
      //未登录 可以定位成功的用户
      if (this.hospitalInfo.hospitalCode) {
        this.getHospitalDispatchingConfig(
          this.hospitalInfo.hospitalCode,
          this.hospitalInfo.unitCode
        );
      }
    }
    //设置服务房间和区域回显
    String.prototype.replaceAll = function(reallyDo, replaceWith, ignoreCase) {
      if (!RegExp.prototype.isPrototypeOf(reallyDo)) {
        return this.replace(
          new RegExp(reallyDo, ignoreCase ? "gi" : "g"),
          replaceWith
        );
      } else {
        return this.replace(reallyDo, replaceWith);
      }
    };
    // if (!this.$route.query.unitCode && this.loginInfo.type == 1) {
    //   //只有院内的人才自动带出服务房间
    //   let staffGrid = this.staffInfo.gridList[0];
    //   if (staffGrid.gridLevel == 5) {
    //     let arr = staffGrid.allParentId.split(",");
    //     arr.splice(arr.length - 1, 1);
    //     arr.join(",");
    //     let localtionIdStr = arr.join(",");
    //     this.localtionPlaceName = staffGrid.gridName;
    //     this.localtionPlaceCode = staffGrid.id;
    //     this.areaVal = staffGrid.parentGridName.replaceAll(">", "", false); //展示的
    //     this.localtionName =
    //       staffGrid.parentGridName.replaceAll(">", "", false) +
    //       staffGrid.gridName; //传递的
    //     this.serviceSiteResId = localtionIdStr;
    //     this.localtion = localtionIdStr + "," + staffGrid.id;
    //   } else {
    //     this.serviceSiteResId = staffGrid.id; //服务区域id
    //     this.localtion = staffGrid.allParentId; //服务区域id
    //     this.areaVal =
    //       staffGrid.parentGridName.replaceAll(">", "", false) +
    //       staffGrid.gridName; //服务区域名称,展示的
    //     this.localtionName =
    //       staffGrid.parentGridName.replaceAll(">", "", false) +
    //       staffGrid.gridName; //服务区域名称
    //     //不显示房间
    //     if (staffGrid.gridLevel != 4) {
    //       this.isExist = false;
    //     }
    //   }
    // }
  },
  activated() {
    if (this.loginInfo) {
      // 工单配置提醒
      this.getTaskWorkConfiguration(
        this.loginInfo.userOffice[0].hospitalCode,
        this.loginInfo.userOffice[0].unitCode
      );
    }
    //设置title
    let url = "";
    try {
      url = sessionStorage.getIem("url");
    } catch (e) {}
    if (this.$route.path.includes("service") || url == "/service") {
      document.title = "综合服务";
      this.projectName = "service";
    } else if (this.$route.path.includes("repair") || url == "/repair") {
      document.title = "综合维修";
      this.projectName = "repair";
    } else {
      document.title = "应急保洁";
      this.projectName = "cleaning";
    }
    //选择的服务房间与当前的服务房间不一致时，更改服务房间（前提是已经另选择了服务房间）
    if (
      this.localtionPlaceName != this.changeLocation.localtionPlaceName &&
      this.changeLocation.localtionPlaceName
    ) {
      this.localtionPlaceName = this.changeLocation.localtionPlaceName;
    }
    // let getMatterlistsO = this.$route.query.matterlistsObj

    // if (getMatterlistsO) {
    //   this.matterlistsObj = getMatterlistsO
    //   if (getMatterlistsO.itemTypeName) {
    //     this.matterlists = getMatterlistsO.itemTypeName + '/' + getMatterlistsO.itemDetailName + '/' + getMatterlistsO.itemServiceName
    //   }else{
    //     this.matterlists = ""
    //   }
    //   this.matterlistsState = ' '
    // }
    if (JSON.stringify(this.$route.query).includes("isExist")) {
      //从选择服务区域页面跳转回来携带参数
      let params = this.$route.query;
      this.serviceSiteResId = params.code[2];
      this.localtion = params.code.join(",");
      //        判断服务区域是数组还是字符串
      if (Array.isArray(params.name)) {
        this.areaVal = params.name.join("");
        this.localtionName = params.name.join("");
      } else {
        //          最近服务区域返回的信息
        this.areaVal = params.name;
        this.localtionName = params.name;
      }
      if (this.$route.query.isExist == "false") {
        this.isExist = false;
      } else if (this.$route.query.isExist == "true") {
        this.isExist = true;
      } else {
        this.isExist = this.$route.query.isExist;
        let isHaveRoomName = this.$route.query.isHaveRoomName;
        if (
          this.isExist &&
          this.changeLocation.localtionPlaceName &&
          !isHaveRoomName
        ) {
          this.localtionPlaceName = "";
          this.localtionPlaceCode = "";
        }
      }
      if (!this.serviceSiteResId) {
        //没选楼层
        let localtionData = this.localtion;
        this.localtion = localtionData.substr(0, localtionData.length - 1);
      }
    }
    let routeQuery = this.$route.query;
    // console.log(routeQuery)
    if (routeQuery.hospitalCode && routeQuery.id) {
      this.scanGetLocation();
      sessionStorage.setItem("unitCode", routeQuery.unitCode);
      sessionStorage.setItem("hospitalCode", routeQuery.hospitalCode);
    } else {
      this.getConfigParams();
    }
  },
  watch: {
    value(value, oldValue) {
      this.value = this.value.replace(
        /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,
        ""
      );
      if (oldValue && !value) {
        this.$refs.textarea.click();
      }
    },
    //如果route发生变化,再次执行getlistTypeName
    $route(to, from) {
      this.getlistTypeName();
    },
    hospitalInfo(info) {
      this.tempSelectHosp = info;
      this.getHospitalDispatchingConfig(info.hospitalCode, info.unitCode);
    }
  },
  beforeRouteEnter(to, from, next) {
    if (to.matched.some(m => m.meta.auth)) {
      next(vm => {
        if (from.path == "/hospListNew") {
          vm.first = false;
        }
        //判断是否从巡检项目进入，并传参
        if (location.search.includes("ipms")) {
          vm.otherProject = "ipms";
          let str = location.search;
          let strs = str.substr(1).split("&"); //[]
          let theRequest = {};
          for (let i = 0; i < strs.length; i++) {
            theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
          }

          if (theRequest.workId) {
            vm.workId = theRequest.workId;
            vm.localtionName = decodeURI(theRequest.localtionName);
            vm.localtion = theRequest.localtion;
          }
        }
        vm.getConfigParams();
      });
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper {
  height: 100%;
  background-color: $bgColor;

  .content {
    box-sizing: border-box;
    min-height: 100%;
    padding-bottom: 76px !important;
    background-color: $bgColor;

    .reservation-btns {
      marginBottom20();
      text-align: center;

      span {
        display: inline-block;
        width: 1.24rem;
        margin: 0 0.36rem;
      }

      .active {
        color: $color;
        border-bottom: 2px solid $color;
      }
    }

    .other-location-name {
      marginBottom20();

      .ipt-content {
        justify-content: flex-end;
        line-height: 1.2;
        text-align: left;
      }
    }

    .adjustable {
      padding: 0 0.32rem;

      > div {
        text-align: center;
      }

      img {
        width: 0.72rem;
      }

      .down {
        transition: all 0.3s;
      }

      .up {
        transform: rotateZ(180deg);
        transition: all 0.3s;
      }
    }

    .up-content {
      transform: rotateX(90deg);
      transform-origin: center top;
      height: 0;
      transition: height 0.3s;
      transition: all 0.3s;
    }

    .down-content {
      transition: all 0.3s;
      transform-origin: center top;
      margin-bottom: 10px;
    }

    .er-level {
      display: flex;
      align-items: center;
      itemBaseStyle();

      .weui-cells_checkbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .redio-content {
          display: flex;
          color: $contentColor;

          .declare-attr {
            padding: 0;
          }
        }
      }
    }

    .hosp {
      marginBottom20();
    }

    .item-style {
      itemBaseStyle();

      .login-tips {
        line-height: 1;
        white-space: nowrap;
        font-size: 0.28rem;
        font-style: italic;
        color: $color;
      }
    }

    .matter-style {
      min-height: 0.98rem;
      line-height: initial;
    }

    .desc-form {
      margin-top: 0;
      background-color: #fff;

      .desc {
        padding: 0.32rem 0.32rem 0;
        font-size: 0.28rem;
        line-height: 1.5em;
        margin-bottom: 0.2rem;
        color: $contentColor;

        textarea {
          display: inline-block;
          text-indent: 2em;
          font-size: 0.3rem;
        }
      }
    }

    .tips {
      color: $contentColor;
      text-align: center;
      margin: 0.6rem 0;

      .key-tips {
        color: $color;
      }
    }
  }
}

.btn {
  padding: 0 1.12rem 0.3rem;
  position: relative;
  user-select: none;

  .delete-voice {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.5rem;
  }

  .play-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
    }
  }
}

.recording {
  position: fixed;
  z-index: 2;
  top: 2rem;
  left: 50%;
  margin-left: -1.2rem;

  img {
    width: 2.4rem;
    opacity: 0.7;
  }
}

.title-content {
  itemBaseStyle();
  margin-top: $marginb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ipt-content {
  text-align: right;
  display: flex;
  align-items: center;
  color: #999;
}

.matter-ipt {
  display: flex;
  justify-content: flex-end;

  .ipt-pos {
    position: relative;
    width: 100%;

    .matterInput {
      display: none;
    }

    span {
      font-family: serif;
      text-align: left;
      display: flex;
      font-size: 15px;
    }
  }
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;

  .img-wrapper {
    flex: 1;
    width: 0;
    margin: $marginb;
    position: relative;

    img {
      width: 100%;
    }

    .icon-img {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
}

.popups-content {
  width: 80%;
  margin: 0 auto;
  position: fixed;
  max-width: 300px;
  border-radius: 3px;
  overflow: hidden;

  & > div {
    margin: 0 auto;
    background: #fff;
    padding-bottom: 15px;
    border-bottom: 1px solid #efefef;
    height: 1.18rem;
    align-items: flex-end;
    box-sizing: border-box;
  }

  .cfm-phone-num-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 48px;
    font-size: 18px;
    padding: 0;
    color: #38C7C4;
  }
}

.play-void-img {
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
}

.title {
  font-size: 0.32rem;
}

.er-level-redio {
  display: flex;
  background-color: #fff;
  font-size: 0.3rem;
  color: $contentColor;
}

.redio-content {
  position: relative;

  .select-time {
    position: absolute;
    left: 1rem;
    bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 5em);

    .text {
      width: 50px;
      line-height: initial;
    }

    .ipt {
      flex: 1;
      text-align: right;
    }
  }

  .textColor {
    color: #999;
  }

  .disPointer {
    pointer-events: none;
  }
}

.hint {
  display: inline-block;
  margin: 0 20px;
}

.reminder{
  height:0.68rem;
  background:#FAECE9;
  padding-left:.32rem;
  line-height:.68rem;
  .reminder-img{
    width:.28rem;
  }
  .reminder-text{
    margin-left:.24rem;
    color:#FF7859;
    font-size:.26rem;
  }

}

.ellipsis{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
