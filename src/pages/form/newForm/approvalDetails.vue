<template>
  <div class="inner">
    <Header title="审批详情" @backFun="goBack"></Header>
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left"><span class="lineTitle"></span><span>挂单申请</span></div>
            <div class="state">
              <span :class="['approvalStatus', `approvalStatus${approvalDetails.approvalStatus}`]"> {{ approvalType(approvalDetails.approvalStatus) }}</span>
            </div>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">申请编号</span>
            <span>{{ approvalDetails.applicationNumber }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">发起人</span>
            <span class="list-itemContent">{{ approvalDetails.creatorName }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">发起时间</span>
            <span class="list-itemContent">{{ approvalDetails.startTime }}</span>
          </div>
          <div class="list-item" @click="goWorkOrderDetail(approvalDetails)">
            <span class="list-itemTitle">工单号</span>
            <span class="list-itemContent text-color">{{ approvalDetails.businessId }}</span>
          </div>

          <div class="list-item">
            <span class="list-itemTitle">挂单说明</span>
            <span class="list-itemContent">{{ approvalDetails.disEntryOrdersReason }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">解决说明</span>
            <span class="list-itemContent">{{ approvalDetails.disEntryOrdersSolution }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">预计解决时间</span>
            <span class="list-itemContent">{{ approvalDetails.disPlanSolutionTime }}</span>
          </div>
        </div>
      </div>
      <van-steps direction="vertical" :active="approvalDetails.currentApprovalNode" active-icon="checked" active-color="#3562db">
        <van-step v-for="(item, index) in approvalDetails.approvalDetailResponseList" :key="index">
          <div style="font-weight: bold" class="heightRow state">
            <span
              :class="[
                'approvalStatus',
                item.approvalDetailNodeInfo == '已提交' || item.approvalDetailNodeInfo == '已通过'
                  ? 'approvalStatus1'
                  : item.approvalDetailNodeInfo == '审批中'
                  ? 'approvalStatus0'
                  : item.approvalDetailNodeInfo == '已驳回'
                  ? 'approvalStatus2'
                  : 'approvalStatus3'
              ]"
              >{{ item.approvalDetailNodeInfo }}</span
            >
            &nbsp;&nbsp;&nbsp;&nbsp;<span :style="approvalDetails.currentApprovalNode > index || approvalDetails.approvalStatus !== 0 ? 'color:#1d2129' : ''">{{
              item.approverName
            }}</span>
          </div>
          <p class="heightRow" style="color: #9ca2a9">{{ item.approvalComment }}</p>
          <p class="heightRow" style="color: #9ca2a9">{{ item.operationTime }}</p>
        </van-step>
      </van-steps>
    </div>
    <div class="btn" v-if="approvalDetails.approval">
      <van-button style="width: 45%" color="#e6effc" @click="approve(2)"><span style="color: #3d67c1">驳回</span></van-button
      ><van-button style="width: 45%" color="#3562db" @click="approve(1)">通过</van-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      approvalDetails: {}
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getQueryDetail(this.$route.query.id);
  },
  methods: {
    getQueryDetail(id) {
      let params = {
        approvalControlId: id,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId
      };
      this.$api.queryDetailList(params).then(res => {
        this.approvalDetails = res;
      });
    },
    approve(type) {
      this.$router.push({
        path: "/BillApproval",
        query: {
          type: type,
          id: this.$route.query.id
        }
      });
    },
    approvalType(approvalState) {
      const approvalStatusMap = {
        0: "审批中",
        1: "已通过",
        2: "已驳回",
        3: "已取消"
      };
      return approvalStatusMap[approvalState];
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    // 跳转工单详情
    goWorkOrderDetail(row) {
      const { businessId } = row;
      if (!businessId) return $.toast("暂无工单信息!");
      this.$router.push({
        path: "/workOrderDetail",
        query: {
          detail: {
            workNum: businessId,
            taskId: ""
          }
        }
      });
    }
  }
};
</script>

<style  lang="scss" scoped>
.inner {
  width: 100%;
  height: 100vh;
  background-color: #f2f4f9;
  overflow-y: scroll;
  color: #353535;

  .heightRow {
    line-height: 25px;
  }
  .content {
    margin: 5px 5px 10px 5px;
    // height: calc(100vh - 140px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-taskInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-documentInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      font-size: 14px;
      color: #353535;
      .list-wrap {
        margin-bottom: 10px;
        .list-item {
          font-size: 16px;
          padding: 7px 0;
          display: flex;
          .list-itemTitle {
            min-width: 102px;
            color: #353535;
          }
          .list-itemContent {
            color: #888;
            overflow: scroll;
            white-space: nowrap;
          }
        }
      }
      .list-flex {
        display: flex;
        justify-content: space-between;
        .list-itemContent {
          color: #86909c;
          font-size: 16px;
        }
      }
      .copywriting {
        margin-top: 10px;
        width: 100%;
        word-break: break-all;
        text-overflow: ellipsis;
      }
    }
    .content-effectInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .radioListItem {
        padding: 0px 0px 12px 0;
        border-bottom: 1px solid #e5e6eb;
        .radioListItem-title {
          margin: 10px 0;
          color: #1d2129;
        }
      }
    }
    .content-evaluateInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      .list-wrap {
        display: flex;
        justify-content: flex-start;
      }
      .evaluateTitle {
        font-size: 16px;
      }
    }
    .attachmentsLists {
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      .upload {
        color: #3562db;
      }
    }
    .list-wrap {
      .list-title {
        font-size: 16px;
        color: #1d2129;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        height: 0.58rem;
        .state {
          .approvalStatus {
            padding: 2px 8px;
            border-radius: 4px;
          }
          .approvalStatus0 {
            background: #e6effc;
            color: #3562db;
          }
          .approvalStatus1 {
            background: #e8ffea;
            color: #00b42a;
          }

          .approvalStatus2 {
            background: #ffece8;
            color: #f53f3f;
          }
          .approvalStatus3 {
            background: #f2f3f5;
            color: #4e5969;
          }
        }
        .title-left {
          margin: auto 0;
          .lineTitle {
            height: 0.35rem;
            width: 0.1rem;
            background-color: #3562db;
            display: inline-block;
            margin-right: 6px;
            vertical-align: bottom;
          }
        }
      }
      .state {
        .taskState {
          padding: 6px 8px;
          border-radius: 4px;
          font-size: 14px !important;
          display: inline-block;
        }
        .taskState0 {
          background: #ffece8;
          color: #f53f3f;
        }
        .taskState1 {
          background: #e8ffea;
          color: #00b42a;
        }
      }
      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 110px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
        .text-color {
          color: #02a7f0;
        }
      }
    }
  }
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.state {
  .approvalStatus {
    padding: 2px 8px;
    border-radius: 4px;
  }
  .approvalStatus0 {
    background: #e6effc;
    color: #3562db;
  }
  .approvalStatus1 {
    background: #e8ffea;
    color: #00b42a;
  }

  .approvalStatus2 {
    background: #ffece8;
    color: #f53f3f;
  }
  .approvalStatus3 {
    background: #f2f3f5;
    color: #4e5969;
  }
}
</style>
