<template>
  <div class="wrapper">
    <Header :title="pageTitle" @backFun="goback"></Header>
    <div class="content">
      <!-- 工单提醒 -->
      <div class="reminder" v-if="hint">
        <img class="reminder-img" src="@/assets/images/reminder.png" alt="" />
        <span class="reminder-text">{{ hint }}</span>
      </div>
      <!--临时用户不显示立刻、预约-->
      <div class="reservation-btns item-style" v-if="loginInfo">
        <span
          :class="{
            active: idx == appointmentType ? !reservation : reservation
          }"
          @click="handleClickTimeText(idx)"
          v-for="(item, idx) of serviceHoursText"
          :key="idx"
          >{{ item.dictLabel }}</span
        >
      </div>
      <!--根据派工工单计算显示属性-->
      <div v-if="requireCode != 2">
        <!--服务地点-->
        <div v-if="otherProject == ''">
          <!--服务事项-->
          <div class="weui-cell item-style matter-style border-rightbottom" @click="goToSelectMatterlists">
            <div class="weui-cell__hd fa">
              <label :class="['weui-label', 'title', 'star-l']">服务事项</label>
            </div>
            <div class="weui-cell__bd ipt-content matter-ipt">
              <div class="ipt-pos">
                <input
                  class="weui-input ipt ellipsis right-aligned-input ipt-left-align"
                  type="text"
                  v-model="matterlists"
                  :class="{ matterInput: matterlistsState }"
                  readonly
                  placeholder="请选择服务事项"
                />
                <!-- <span v-text="matterlists"></span> -->
              </div>
              <!-- <span class="iconfont arrow" style="margin-left: 8px">&#xe646;</span> -->
              <van-icon style="margin: 0 8px" name="arrow" />
            </div>
          </div>
          <!--所属科室-->
          <div class="weui-cell border-rightbottom item-style">
            <div class="weui-cell__hd">
              <label class="weui-label title">所属科室</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input class="weui-input ipt" readonly type="text" @click="showPicker = true" v-model="sourceDeptName" placeholder="请选择所属科室" />
            </div>
            <van-icon style="margin: 0 8px" color="#999" name="arrow" />
          </div>
          <div class="weui-cell border-rightbottom item-style" style="padding-right: 0">
            <div class="weui-cell__hd fa">
              <label :class="['weui-label', 'title']">服务地点</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input @click="toSelectArea" class="weui-input ipt ellipsis right-aligned-input ipt-left-align" type="text" readonly v-model="areaVal" placeholder="请选择服务地点" />
              <van-icon style="margin: 0 24px" name="arrow" />
              <!-- <span class="iconfont icon-saomiao" @click="scanRepair" style="color: #4e5969; padding-right: 10px"></span> -->
            </div>
          </div>
          <!--服务房间-->
          <!-- <div class="weui-cell border-rightbottom item-style site">
            <div class="weui-cell__hd">
              <label class="weui-label title">服务房间</label>
            </div>
            <div
              class="weui-cell__bd ipt-content "
              @click="toSelectSite"
              v-if="isExist"
            >
              <input
                class="weui-input ipt ellipsis"
                type="text"
                readonly
                v-model="localtionPlaceName"
                placeholder="请选择服务房间"
              />
              <span class="iconfont arrow">&#xe646;</span>
            </div>
          </div> -->
        </div>
      </div>
      <!--预约时间-->
      <service-hours ref="serviceHours" v-if="reservation && loginInfo"></service-hours>
      <van-field
        readonly
        clickable
        name="datetimePicker"
        :value="requireAccomplishDate"
        label="要求完工时间"
        right-icon="arrow"
        input-align="left"
        placeholder="请选择时间"
        @click="closingTimeFn"
      />
      <van-popup v-model="timeShow" position="bottom" :style="{ height: '40%' }">
        <van-datetime-picker v-model="currentDate" type="datetime" title="选择要求完工时间" @confirm="dateComfirm" @cancel="dateCancel" />
      </van-popup>
      <van-popup v-model="showPicker" round position="bottom">
        <van-search placeholder="请输入" v-model="searchVal" />
        <van-picker show-toolbar :columns="searchOptions" @cancel="showPicker = false" @confirm="onConfirm" value-key="label" />
      </van-popup>
      <van-popup v-model="showPicker2" round position="bottom">
        <van-picker show-toolbar :columns="teamList" @cancel="showPicker2 = false" @confirm="onConfirm2" value-key="team_name" />
      </van-popup>
      <van-popup v-model="showPicker3" round position="bottom" :style="{ maxHeight: '60%' }">
        <van-checkbox-group v-model="checkboxVal">
          <van-cell-group>
            <van-cell v-for="(item, index) in personList" clickable :key="item.id" :title="item.member_name" @click="toggle(index)">
              <template #right-icon>
                <van-checkbox :name="item.id" ref="checkboxes" checked-color="#3562db" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </van-popup>

      <!--未登录扫描报修，加入联系人、联系电话-->
      <div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">工号</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input class="weui-input ipt" type="text" v-model="callerJobNum" maxlength="20" placeholder="请输入工号" @input="numValidate" />
          </div>
        </div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title star">联系人</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input class="weui-input ipt" type="text" v-model="contactName" maxlength="20" placeholder="请输入联系人" />
          </div>
        </div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">电话</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input class="weui-input ipt" type="tel" v-model="phone" @input="telValidate" @blur="handleTextareaBlurEvent" placeholder="请输入电话" />
          </div>
        </div>
      </div>

      <!--根据派工工单计算显示属性-->
      <div v-if="loginInfo && requireCode == 2">
        <!--工单类型为综合维修，显示服务地点-->
        <div v-if="projectName == 'repair'">
          <div class="weui-cell border-rightbottom item-style" style="padding-right: 0">
            <div class="weui-cell__hd fa">
              <label :class="['weui-label', 'title', projectName == 'repair' ? 'star' : '']">服务地点</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input class="weui-input ipt ellipsis right-aligned-input" type="text" readonly @click="toSelectArea" v-model="areaVal" placeholder="请选择服务地点" />
              <van-icon style="margin: 0 8px" name="arrow" />
              <span class="iconfont icon-saomiao" @click="scanRepair" style="color: #4e5969; padding-right: 10px"></span>
            </div>
          </div>
        </div>
      </div>

      <!--申报描述-->
      <div class="weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea"
              :placeholder="placeholder"
              maxlength="500"
              rows="5"
              ref="textarea"
              v-model="value"
              @keydown="keydown($event)"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
        <div class="weui-cell border-rightbottom item-style" style="margin-bottom: 0.2rem">
          <div class="weui-cell__hd">
            <label class="weui-label title star">服务部门</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input class="weui-input ipt" readonly type="text" @click="handleDeptClick" v-model="designateDeptName" placeholder="请选择服务部门" />
          </div>
        </div>
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">指派人员</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input class="weui-input ipt" readonly type="text" @click="handlePersonClick" v-model="designatePersonName" placeholder="请选择人员" />
          </div>
        </div>
        <!-- <sounds-recording @getRecord="getRecord" @getRecordFile="getRecordFile" @getDownLoad="getDownLoad" ref="voices" /> -->
      </div>
      <!--图片上传-->
      <!-- <upload-image class="bottom" style="position: relative" ref="imgs" @getImg="getImg"></upload-image> -->
      <div :class="[rotate ? 'down-content' : 'up-content']">
        <!--根据派工工单计算显示属性-->
        <div v-if="requireCode == 2">
          <!--非综合维修工单，此处显示服务地点-->
          <div v-if="otherProject == ''">
            <!--非综合维修工单，此处显示服务地点-->
            <div class="weui-cell border-rightbottom item-style" style="padding-right: 0" v-if="projectName != 'repair'">
              <div class="weui-cell__hd fa">
                <label :class="['weui-label', 'title', projectName == 'repair' ? 'star' : '']">服务地点</label>
              </div>
              <div class="weui-cell__bd ipt-content">
                <input class="weui-input ipt ellipsis right-aligned-input" type="text" readonly @click="toSelectArea" v-model="areaVal" placeholder="请选择服务地点" />
                <van-icon style="margin: 0 8px" name="arrow" />
                <span class="iconfont icon-saomiao" @click="scanRepair" style="color: #4e5969; padding-right: 10px"></span>
              </div>
            </div>
            <!--服务房间-->
            <!-- <div class="weui-cell border-rightbottom item-style site">
              <div class="weui-cell__hd">
                <label class="weui-label title">服务房间</label>
              </div>
              <div
                class="weui-cell__bd ipt-content "
                @click="toSelectSite"
                v-if="isExist"
              >
                <input
                  class="weui-input ipt ellipsis"
                  type="text"
                  readonly
                  v-model="localtionPlaceName"
                  placeholder="请选择服务房间"
                />
                <span class="iconfont arrow">&#xe646;</span>
              </div>
            </div> -->
          </div>
          <!--服务事项-->
          <div class="weui-cell item-style matter-style border-rightbottom" v-if="this.isItem == 'Y'" @click="goToSelectMatterlists">
            <div class="weui-cell__hd fa">
              <label :class="['weui-label', 'title', projectName == 'repair' ? 'star' : '']">服务事项</label>
            </div>
            <div class="weui-cell__bd ipt-content matter-ipt">
              <div class="ipt-pos">
                <input
                  class="weui-input ipt ellipsis right-aligned-input"
                  type="text"
                  v-model="matterlists"
                  :class="{ matterInput: matterlistsState }"
                  readonly
                  placeholder="请选择服务事项"
                />
                <!-- <span v-text="matterlists"></span> -->
              </div>
              <span class="iconfont arrow" style="margin-left: 8px">&#xe646;</span>
            </div>
          </div>
        </div>
        <!--紧急程度-->
        <urgent-level ref="urgent" :urgentArr="urgentArr"></urgent-level>
        <!--申报属性-->
        <declare-attribute v-if="otherProject != 'ipms'" ref="attribute" :declareAttributeArr="declareAttributeArr"></declare-attribute>
        <!--联系电话-->
        <!-- <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">联系电话</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input
              class="weui-input ipt"
              type="tel"
              v-model="phone"
              oninput="if(value.length>13)value=value.slice(0,13)"
              @blur="handleTextareaBlurEvent"
              placeholder="请输入联系人电话"
            />
          </div>
        </div>-->
        <!--是否返修-->
        <is-rework ref="isRework"></is-rework>
      </div>
      <!--收起和完善信息-->
      <!-- <div class="adjustable" v-if="loginInfo">
        <div @click="switchBtn" style="color: #3562db" class="hide-box" :style="`width: ${!rotate ? '60%' : '82px'}`">
          <span class="hint">{{ !rotate ? "信息越详细报修更快捷" : "收起" }}</span>
          <img src="~images/more_down.png" :class="[!rotate ? 'down' : 'up']" />
        </div>
      </div> -->
    </div>

    <div class="submit-btn" :class="{ active: canShow }">
      <button class="weui-btn weui-btn_primary" @click="submitAfterVrification">提交</button>
    </div>
  </div>
</template>

<script>
// var txFlashFileRecognize = api.require("txFlashFileRecognize");
import { mapState } from "vuex";
import UploadImage from "@/common/uploadImg/uploadImg";
import UploadVoice from "@/common/uploadVoice/UploadVoice";
import UrgentLevel from "./components/UrgentLevel";
import ServiceHours from "./components/ServiceHours";
import DeclareAttribute from "./components/DeclareAttribute";
import IsRework from "./components/IsRework";
import Popups from "@/common/customize/Popups";
import PhoneText from "@/common/PhoneText";
import YBS from "@/assets/utils/utils.js";
import moment from "moment";

export default {
  name: "newOrder",
  components: {
    UploadImage,
    UploadVoice,
    // TempSelectHosp,
    UrgentLevel,
    ServiceHours,
    DeclareAttribute,
    IsRework,
    Popups,
    PhoneText
  },
  data() {
    return {
      timeShow: false,
      currentDate: new Date(),
      requireAccomplishDate: "", //要求完工时间
      value: "",
      phone: "", //需联系电话和临时用户需发送验证码的手机号
      isExist: true,
      canShow: false,
      voiceParams: "",
      imagesParams: [],
      imageConfig: [], //处理图片的mediaId和ossUrl
      imgFlag: false,
      accessToken: "",
      isOnlyVoice: false,
      text: "",
      areaVal: "",
      localtionName: "",
      localtion: "",
      localtionPlaceName: "",
      localtionPlaceCode: "",
      serviceSiteResId: "",
      allSiteDataInfo: [], //请求返回所有的相关地点的信息
      popData: {},
      matterlistsObj: {}, //服务事项
      matterlistsState: "", //服务事项(展示ipt)
      matterlists: "", //服务事项(展示)
      workId: "", //用于巡检
      otherProject: "", //是否为其他项目（仅调用该页面）
      projectName: "", //值为service时是便民服务工单，repair是报修
      rotate: true, //完善申报信息时候旋转
      tempSelectHosp: {}, //临时用户所选或定位的医院
      isTTYY: false, //临时用户是否为天坛医院公众号使用者，天坛医院不显示显示医院，直接为默认hospitalCode、unitCode
      reservation: false, // 点击预约
      first: true, //是否首次打开此页面，只有第一次才自动定位
      unitCode: "", //记录扫码报修的医院信息，定位的医院信息存到浏览器中了
      hospitalCode: "", //记录扫码报修的医院信息，定位的医院信息存到浏览器中了
      isScan: false, //是否为扫描报修，扫描报修不显示选择医院
      urgentArr: [], //紧急程度字典项
      declareAttributeArr: [], //申报属性字典项
      serviceHoursText: [], //服务时间（预约、立刻）字典项
      appointmentType: "", //服务时间（预约、立刻）字典项0 或1
      contactName: "", //未登录，扫描报修需添加联系人和联系人电话
      smsCode: "", //临时用户需发送验证码，验证码成功后才可以报修
      iptSmsCode: "", //临时用户需发送验证码，验证码成功后才可以报修(用户输入的)
      isRegister: 0, //临时用户报修的医院是否为开放状态
      requireCode: 2, //默认关闭-非必填
      isSubmit: true, //节流标示
      placeholder: "请输入申报描述,500字以内",
      requiredCode: "", //必选项内容
      hint: "", //工单配置提醒
      isItem: "", //服务事项展示判断  y展示  n 不展示
      amount: {
        //上传图片限制
        quantity: 3,
        capital: "三"
      },
      userType: "", //用户类型
      alarmId: "",
      pageTitle: "",
      recordingInfo: "",
      recordingInfoFile: "",
      sourcesDeptOptions: [],
      callerJobNum: "",
      showPicker: false,
      sourceDept: "",
      sourceDeptName: "",
      teamList: [],
      designateDeptName: "",
      designateDeptCode: "",
      showPicker2: false,
      showPicker3: false,
      personList: [],
      checkboxVal: [],
      searchVal: "",
      searchOptions: []
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "changeLocation", "hospitalInfo", "openId", "serviceAreaTreeData"]),
    designatePersonName() {
      // 遍历checkboxVal，通过里面的值，去personList里面找到对应的人员名称
      let personName = "";
      this.checkboxVal.forEach(item => {
        this.personList.forEach(person => {
          if (item == person.id) {
            personName += person.member_name + ",";
          }
        });
      });
      return personName.slice(0, personName.length - 1);
    },
    designatePersonCode() {
      // 获取checkboxVal里面的值，多个用逗号分隔
      return this.checkboxVal.join(",");
    },
    designatePersonPhone() {
      // 遍历checkboxVal，通过里面的值，去personList里面找到对应的人员手机号
      let personPhone = "";
      this.checkboxVal.forEach(item => {
        this.personList.forEach(person => {
          if (item == person.id) {
            personPhone += person.phone + ",";
          }
        });
      });
      return personPhone.slice(0, personPhone.length - 1);
    }
  },
  methods: {
    getDialogShow() {
      let params = {
        userId: this.loginInfo.staffId,
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getNotCommentWarn(params).then(res => {
        if (res.commentCount > 0 && res.commentStatus == "1") {
          this.$dialog
            .confirm({
              message: "您当前有未评价的工单！",
              confirmButtonText: "去评价",
              cancelButtonText: "忽略",
              confirmButtonColor: "#3562db"
            })
            .then(() => {
              this.$router.push({
                path: "/orderAcceptance"
              });
            })
            .catch(() => {});
        }
      });
    },
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    //时间弹窗

    dateComfirm(e) {
      this.requireAccomplishDate = this.formatDate(e);
      this.dateCancel();
    },
    dateCancel() {
      this.timeShow = false;
    },
    closingTimeFn() {
      this.timeShow = true;
    },
    // numValidate方法，校验工号，只支持数字和字母
    numValidate() {
      this.callerJobNum = this.callerJobNum.replace(/[^0-9a-zA-Z]/g, "");
    },
    telValidate() {
      this.phone = this.phone.replace(/\D/g, "").slice(0, 11);
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle();
    },
    getPersonList() {
      this.$api
        .getDesignatePersonList({
          id: this.designateDeptCode
        })
        .then(res => {
          // console.log("人员list", res);
          this.personList = res.list;
        });
    },
    handlePersonClick() {
      // 校验是否选择服务部门
      if (!this.designateDeptCode) {
        $.toast("请选择服务部门", "text");
        return;
      }
      this.showPicker3 = true;
    },
    handleDeptClick() {
      // 校验是否选择服务事项
      if (!this.matterlistsObj.itemDetailCode) {
        $.toast("请选择服务事项", "text");
        return;
      }
      this.showPicker2 = true;
    },
    onConfirm2(value) {
      console.log(value);
      this.designateDeptCode = value.id;
      this.designateDeptName = value.team_name;
      this.showPicker2 = false;
      this.getPersonList();
    },
    getOCTeamInfo(matterId) {
      const params = {
        localtionId: "",
        workTypeCode: "1",
        // itemTypeCode: this.selectRowId,
        matterId
      };
      this.$api.getTeamsByTask(params).then(res => {
        console.log("getOCTeamInfo", res);
        this.teamList = res.list;
      });
    },
    onConfirm(value) {
      console.log(value);
      // this.sourceDept = value.value + "_" + value.label;
      this.sourceDept = value.value;
      this.sourceDeptName = value.label;
      this.showPicker = false;
    },
    getAllOffice() {
      this.$api.getAllOffice({}).then(res => {
        console.log("getAllOffice", res);
        this.sourcesDeptOptions = res.body.result;
        this.searchOptions = res.body.result;
      });
    },
    goback() {
      // api.closeFrame();
      // this.$YBS.apiCloudCloseFrame();
      this.$router.go(-1);
    },
    getImg(img) {
      console.log("img", img);
      this.imagesParams.push(img);
    },
    getRecord(info) {
      this.recordingInfo = info;
      this.voiceParams = info;
      console.log("recordingInfo", this.recordingInfo);
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
      console.log("recordingInfoFile", this.recordingInfoFile);
    },
    /**
     * 工单配置提醒接口
     */
    getTaskWorkConfiguration(hospitalCode, unitCode) {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?getTaskWorkConfiguration",
          this.$qs.stringify({
            hospitalCode,
            unitCode,
            workTypeCode: "1"
            // this.$route.path.indexOf("repair")!=-1
            //   ? "1"
            //   : this.$route.path.indexOf("service")!=-1
            //   ? "6"
            //   : "2" //6是便民服务，2是保洁，1是维修
          }),
          {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.isItem = res.isItem;
          this.hint = res.hint;
          this.workCode = res.workTypeCode;
        });
    },
    /**
     * 获取手机短信验证码
     */
    handleGetSmsCode(smscode, phone) {
      this.phone = phone;
      this.smsCode = smscode;
    },
    /**
     * 当电话号改变时接收电话号码
     */
    getPhoneNum(num) {
      this.phone = num;
    },
    /**
     * 点击注册
     */
    toRegister() {
      this.$router.push({
        path: "/registerNew"
      });
    },
    /**
     * 点击立刻
     */
    handleClickTimeText(index) {
      if (index == 0) {
        this.reservation = false;
      } else {
        this.reservation = true;
      }
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 是否展示除地点以外的信息
     */
    switchBtn() {
      this.rotate = !this.rotate;
    },
    /**
     * 区域选择
     */
    toSelectArea() {
      //级联弹窗数据还原
      this.localtionPlaceName = "";
      this.localtionPlaceCode = "";
      let obj = { routerPath: this.$route.path };
      if (this.sourceDept) {
        obj.deptId = this.sourceDept;
      }
      this.$router.push({
        path: "/selectAreaNew",
        query: obj
      });
    },
    /**
     * 服务房间请求函数
     */
    toSelectSite() {
      let arr = this.serviceSiteResId.split(",");
      if (this.serviceSiteResId == "") {
        $.toast("请先选择服务地点", "text");
      } else {
        this.showVal(this.serviceAreaTreeData, arr[arr.length - 1]);
        return;
      }
    },
    /**
     * 仅供服务房间展示，调起弹窗并设置相关参数
     */
    showVal(res, gridId) {
      // if (res.data && res.data.code == 200) {
      //   this.allSiteDataInfo = res.data.data;
      // } else {
      //   this.allSiteDataInfo = res;
      // }
      this.allSiteDataInfo = res.filter(item => {
        return item.parentId == gridId;
      });
      //将所有地点取出放到数组中
      let siteArr = [];
      for (let i = 0; i < this.allSiteDataInfo.length; i++) {
        siteArr.push(this.allSiteDataInfo[i]);
      }
      //跳到搜索页面
      this.$router.push({
        name: "Site",
        params: {
          list: siteArr
        }
      });
    },
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "BJSJTYY",
            unitCode: this.unitCode ? this.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      // console.log(params[i]);
      // console.log(localId[i]);
      // console.log(this.accessToken);
      // return
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "BJSJTYY",
              unitCode: this.unitCode ? this.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
            // this.imagesParams.push(res.data.data.ossUrl);
            // this.imageConfig.push(res.data.data);
          });
      });
    },
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    },
    /**
     * 处理提交按钮事件
     */
    handleSubmitClick() {
      this.isMissingItem();
      // if (
      //   this.imagesParams.length != 0 &&
      //   this.$refs.voices.UploadVoice.hasVoice != "recorded"
      // ) {
      //   //上传图片、没有录音
      //   let imgLen = () => {
      //     if (this.imagesParams.length != 0) {
      //       this.isMissingItem();
      //     } else {
      //       setTimeout(() => {
      //         imgLen();
      //       }, 500);
      //     }
      //   };
      //   imgLen();
      // } else if (
      //   this.imagesParams.length != 0 &&
      //   this.$refs.voices.UploadVoice.hasVoice == "recorded"
      // ) {
      //   //上传图片、录音
      //   let imgLen = () => {
      //     if (this.imagesParams.length != 0) {
      //       this.isOnlyVoice = true;
      //       // this.$refs.voices.saveVoice();
      //     } else {
      //       setTimeout(() => {
      //         imgLen();
      //       }, 500);
      //     }
      //   };
      //   imgLen();
      // } else if (
      //   this.imagesParams.length == 0 &&
      //   this.$refs.voices.UploadVoice.hasVoice == "recorded"
      // ) {
      //   //不需上传图片、但有录音
      //   this.isOnlyVoice = true;
      //   // this.$refs.voices.saveVoice();
      // } else if (
      //   this.imagesParams.length == 0 &&
      //   this.$refs.voices.UploadVoice.hasVoice == "unvoice"
      // ) {
      //   //不需上传图片、没有录音
      //   this.isMissingItem();
      // }
    },
    /**
     * 设置微信config参数
     * @param res
     */
    setAllConfig(res) {
      this.$refs.voices.setConfig(res);
      this.$refs.voices.initRecord();
      this.$refs.imgs.setConfig(res);
      this.accessToken = res.data.data.accessToken;
    },
    /**
     * 跳转选择服务事项页面
     */
    goToSelectMatterlists() {
      let unitCode = this.unitCode ? this.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "";
      let hospitalCode = this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "";
      this.$router.push({
        path: "/matterlists",
        query: {
          workTypeCode: "1",
          projectName: "newOrder",
          unitCode: unitCode,
          hospitalCode: hospitalCode
        }
      });
    },
    /**
     * 提交前判断
     */
    getHttp() {
      if (!this.loginInfo) {
        //游客当前选择医院的信息为“该医院未开放注册”
        let that = this;
        if (!this.hospitalInfo.hospitalCode && !this.isScan) {
          $.toast("请选择医院", "text");
          return;
        } else if ((this.hospitalInfo.isRegister && this.hospitalInfo.isRegister == 0) || (!this.hospitalInfo.isRegister && this.isRegister == 0)) {
          $.toast("该医院未开放，请联系管理员", "text", function () {
            that.$router.replace("/login");
          });
        } else {
          this.submitFun();
        }
      } else {
        this.submitFun();
      }
    },
    /**
     * 工单提交请求函数
     */
    submitFun() {
      if (!this.isSubmit) {
        $.toast("该工单已经提交,请勿重复提交", "text");
        return;
      }
      // if (this.$refs.imgs.imgLocalIds.length != this.imagesParams.length) {
      //   $.toast("请等待图片上传完毕", "text");
      //   return;
      // }
      this.isSubmit = false;
      $.showLoading("提交中…");
      let sourcesFloorName, sourcesFloor;
      // if (!this.staffInfo || this.staffInfo.gridList.length == 0) {
      //   sourcesFloorName = "";
      //   sourcesFloor = "";
      // } else {
      //   let params = this.staffInfo.gridList[0];
      //   sourcesFloorName =
      //     params.parentGridName.replace(">", "") +
      //     this.staffInfo.gridList[0].gridName;
      //   sourcesFloor = this.staffInfo.gridList[0].allParentId;
      // }
      if (this.otherProject == "") {
        //如果值为ipms巡检，不需要设置这两个值，直接带过来的
        if (this.changeLocation.localtionPlaceCode) {
          let arr = this.localtion.split(",");
          if (arr.length == 4) {
            arr.splice(arr.length - 1, 1);
            let str = arr.join(",");
            this.localtion = str + "," + this.changeLocation.localtionPlaceCode;
          } else {
            this.localtion = this.localtion + "," + this.changeLocation.localtionPlaceCode;
          }

          this.localtionName = this.areaVal + this.localtionPlaceName;
        }
      }

      let param = null;
      let unitCode = "";
      let hospitalCode = "";
      if (this.isScan) {
        //扫码报修，无论登录不登录都是用二维码中的医院信息
        unitCode = this.unitCode;
        hospitalCode = this.hospitalCode;
      } else {
        unitCode = this.loginInfo ? this.loginInfo.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "";
        hospitalCode = this.loginInfo ? this.loginInfo.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "";
      }
      let params = {
        // callerName:this.loginInfo.staffName,
        hospitalCode: hospitalCode,
        deptCode: this.loginInfo.deptId,
        unitCode: unitCode,
        userId: this.loginInfo.id,
        phoneNumber: this.loginInfo.phone,
        realName: this.loginInfo.staffName,
        workTypeCode: "1",
        deptName: this.staffInfo.officeName,
        questionDescription: this.value,
        urgencyDegreeCode: this.$refs.urgent.urgent,
        jobNumber: this.staffInfo.staffNumber,
        accessToken: this.accessToken,
        // type: this.loginInfo ? this.loginInfo.type : 1,
        type: this.userType,
        job: this.staffInfo.administrativePost,
        appointmentType: this.loginInfo ? (this.reservation ? "1" : "0") : 0,
        appointmentDate: this.loginInfo ? (this.reservation ? this.$refs.serviceHours.reservation : "") : "",
        localtionName: this.localtionName,
        localtion: this.localtion,
        contactNumber: this.phone,
        itemDetailCode: this.matterlistsObj.itemDetailCode,
        itemDetailName: this.matterlistsObj.itemDetailName,
        itemServiceCode: this.matterlistsObj.itemServiceCode,
        itemServiceName: this.matterlistsObj.itemServiceName,
        itemTypeCode: this.matterlistsObj.itemTypeCode,
        itemTypeName: this.matterlistsObj.itemTypeName,
        sourcesFloorName: sourcesFloorName,
        sourcesFloor: sourcesFloor,
        repairWork: this.$refs.isRework.isRework,
        staffId: this.staffInfo.staffId,
        workSources: "1", //巡检为3,其余为1
        callerCompanyCode: this.loginInfo ? this.loginInfo.companyCode : "", //用于巡检
        callerCompanyName: this.loginInfo ? this.loginInfo.companyName : "", //用于巡检
        typeSources: this.otherProject == "ipms" ? "3" : this.$refs.attribute.attr, //巡检为3
        contactName: this.contactName, //未登录扫描报修加入的联系人
        dispatchingConfig: this.requireCode, //	移动申报自动派工标识
        jobNumber: this.callerJobNum,
        sourcesDept: this.sourceDept,
        sourcesDeptName: this.sourceDeptName,
        designateDeptCode: this.designateDeptCode,
        designateDeptName: this.designateDeptName,
        designatePersonCode: this.designatePersonCode,
        designatePersonName: this.designatePersonName,
        designatePersonPhone: this.designatePersonPhone,
        requireAccomplishDate: this.requireAccomplishDate //要求完工时间
      };
      // if (this.alarmId) {
      //   params.sysForShort = this.alarmId;
      // }
      // params.callerTape = this.voiceParams;
      // params.attachment = JSON.stringify(this.imagesParams);
      // return console.log(params);
      param = this.$qs.stringify(params);
      this.axios
        .post(__PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat", param, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(this.handleRequestSucc);
    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      $.hideLoading();
      if (res.data.code == "200") {
        $.toast("工单提交成功", "success", () => {
          sessionStorage.removeItem("hospitalCode");
          sessionStorage.removeItem("unitCode");
          sessionStorage.removeItem("url");
        });
        setTimeout(() => {
          // api.closeFrame();
          this.$YBS.apiCloudCloseFrame();
        }, 1000);
      } else {
        $.toast(res.data.message, "text");
        if (res.data.code == 40001) {
          this.voiceParams = "";
          this.$refs.voices.handleDelVoiceClick();
        }
      }
      // $.hideLoading();
    },
    /**
     * 校验必填选项
     */
    isMissingItem() {
      if (!this.loginInfo) {
        let params = {
          hospitalCode: this.hospitalInfo.hospitalCode,
          unitCode: this.hospitalInfo.unitCode,
          workTypeCode: "1"
        };
        this.getIsRequired(params);
      } else {
        let params = {
          hospitalCode: this.loginInfo.hospitalCode,
          unitCode: this.loginInfo.unitCode,
          workTypeCode: "1"
        };
        this.getIsRequired(params);
      }
    },
    getIsRequired(params) {
      this.axios
        .post(__PATH.ONESTOP + "/appOlgTaskManagement/getIsRequired", this.$qs.stringify(params), {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          res = res.data.data;
          this.requiredCode = res.requiredCode;
          this.requiredParameter(res.requiredCode);
        });
    },
    /**
     * 必选项验证
     */
    requiredParameter(value) {
      let reg = /^((13|14|15|17|18)[0-9]{9})$/;
      // if (!this.loginInfo && !this.phone && ((this.hospitalInfo.isRegister && this.hospitalInfo.isRegister == 1) || (!this.hospitalInfo.isRegister && this.isRegister == 1))) {
      //   if (this.phone == "") {
      //     $.toast("请填写手机号及验证码", "text");
      //     return;
      //     // } else if (!reg.test(this.phone)) {
      //   } else if (this.phone.length < 11) {
      //     //2020-5-19 修改验证方式为11位长度
      //     $.toast("请输入正确的手机号", "text");
      //     return;
      //   } else if (this.iptSmsCode == "") {
      //     $.toast("请输入验证码", "text");
      //     return;
      //   }
      // } else if (!this.loginInfo && this.iptSmsCode == "") {
      //   $.toast("请输入验证码", "text");
      //   return;
      // } else if (this.iptSmsCode != this.smsCode) {
      //   $.toast("验证码不正确", "text");
      //   return;
      // }
      // if (value.indexOf("localtion", 0) != -1 && this.areaVal == "") {
      //   if (!this.loginInfo) {
      //     if (this.otherProject == "ipms" || this.requireCode == 2) {
      //       console.log("");
      //     } else {
      //       $.toast("请选择服务地点", "text");
      //       return;
      //     }
      //   } else {
      //     if (this.otherProject == "ipms") return true;
      //     else $.toast("请选择服务地点", "text");
      //     return;
      //   }
      // }
      // 校验服务事项
      if (!this.matterlistsObj.itemTypeCode) {
        $.toast("请选择服务事项", "text");
        return;
      }
      // 校验联系人
      if (!this.contactName) {
        $.toast("请填写联系人", "text");
        return;
      }
      // 校验服务部门
      if (!this.designateDeptCode) {
        $.toast("请选择服务部门", "text");
        return;
      }
      // if (!this.value && !this.voiceParams) {
      //   $.toast("请填写申报描述或录入语音", "text");
      //   return false;
      // }
      // else if (
      //   value.indexOf("itemTypeCode", 0) != -1 &&
      //   !this.matterlistsObj.itemTypeCode &&
      //   this.isItem == "Y"
      // ) {
      //   $.toast("请选择服务事项", "text");
      //   return false;
      // }
      else if (this.appointmentType == 1 && !this.$refs.serviceHours.reservation) {
        $.toast("请填写预约时间", "text");
        return false;
      } else {
        this.getHttp();
      }
    },
    /**
     * 校验联系电话的格式
     */
    submitAfterVrification() {
      // var reg = /^((0\d{2,3}-\d{7,8})|(1[358479]\d{9})|(\d{7,8}))$/;
      // if (!reg.test(this.phone) && this.phone != "") {
      //2020-5-19 修改为仅验证长度11位
      if (this.phone.length < 11 && this.phone != "") {
        $.toast("请输入正确的电话号", "text");
      } else {
        this.handleSubmitClick();
      }
    },

    /**
     * 临时用户定位的医院信息
     * @param par
     */
    // handleLocationHopsInfo(par) {
    //   this.tempSelectHosp = par;
    //   this.getHospitalDispatchingConfig(par.unitCode, par.hospitalCode);
    // },
    /**
     * 判断该医院是否开放注册
     */
    getHospitalIsRisterByCode(unitCode, hospitalCode) {
      this.$api
        .getHospitalIsRisterByCode({
          unitCode: unitCode,
          hospitalCode: hospitalCode
        })
        .then(res => {
          this.isRegister = res.isRegister;
        });
    },
    /**
     * 扫码报修获取地点
     */
    scanGetLocation() {
      this.isScan = true;
      let query = this.$route.query;
      //所有参数通过扫描中获取
      this.serviceSiteResId = query.id; //二维码中给的是楼宇ID
      this.unitCode = query.unitCode;
      this.hospitalCode = query.hospitalCode;

      this.getHospitalIsRisterByCode(query.unitCode, query.hospitalCode);
      this.getHospitalDispatchingConfig(query.unitCode, query.hospitalCode);
      this.axios
        .post(
          __PATH.BASE_API + "/hospitalController/getHospitalGridInfoById",
          this.$qs.stringify({
            unitCode: this.unitCode,
            hospitalCode: this.hospitalCode,
            id: this.serviceSiteResId
          }),
          {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(this.getHospitalGridInfoByIdCallBack);
    },
    getHospitalGridInfoByIdCallBack(res) {
      if (res.data.data.gridLevel == "3" || res.data.data.gridLevel == "2") {
        //4为楼层,2为区
        //二维码中ID为楼宇，不显示服务房间
        this.isExist = false;
      }
      let staffGrid = res.data.data;
      this.serviceSiteResId = staffGrid.id; //服务地点id
      this.localtion = staffGrid.allParentId; //服务地点id
      this.areaVal = staffGrid.allParentName.replaceAll(">", "", false); //服务地点名称,展示的
      this.localtionName = staffGrid.allParentName.replaceAll(">", "", false); //服务地点名称
    },
    /**
     * 判断是否已经选择了医院
     */
    isSelectHosp() {
      //临时用户如果不选医院则不能完成其他操作，没有hospitalCode
      if (!sessionStorage.getItem("hospitalCode") && !this.loginInfo) {
        return false;
      } else {
        return true;
      }
    },
    /**
     * 获取医院自定义的字典项接口
     */
    getPersonnelDictionaryFun(type) {
      let params = {
        type: type
      };
      if (type == 2) {
        params.states = 2;
      }
      this.$api.getPersonnelDictionary(params).then(res => {
        switch (type) {
          case 1:
            this.urgentArr = res.reverse();
            break;
          case 2:
            this.declareAttributeArr = res;
            break;
          case 4:
            this.serviceHoursText = res;
            this.appointmentType = res[0].dictValue;
            if (this.appointmentType == 1) {
              this.reservation = true;
            }
            break;
        }
      });
    },
    getHospitalDispatchingConfig(hospitalCode, unitCode) {
      this.$api
        .getHospitalDispatchingConfig({
          hospitalCode: hospitalCode,
          unitCode: unitCode
        })
        .then(res => {
          //需要必填字段
          // this.requireCode = res;
          this.requireCode = 1;
        });
    },
    //空格校验
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    },
    // 接收服务事项的数据
    getlistTypeName() {
      if (this.$route.query.source == "service") {
        let typelist = this.$route.query.item || {};
        if (!typelist.itemServiceName) return;
        this.matterlistsObj = typelist;
        this.matterlists = typelist.itemTypeName + "-" + typelist.itemDetailName + "-" + typelist.itemServiceName;
        this.getOCTeamInfo(typelist.itemTypeCode);
      } else {
        return;
      }
    },
    scanRepairCallBack(scanData) {
      let result = scanData;
      let unitCode = this.unitCode ? this.unitCode : sessionStorage.unitCode;
      let hospitalCode = this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode;
      if (result[1]) {
        this.axios
          .post(
            __PATH.BASE_API + "/space/api/getHospitalSpaceInfoById",
            this.$qs.stringify({
              unitCode: unitCode || this.loginInfo.unitCode,
              hospitalCode: hospitalCode || this.loginInfo.hospitalCode,
              id: result[1]
            }),
            {
              headers: {
                Authorization: "Bearer " + localStorage.getItem("token")
              }
            }
          )
          .then(data => {
            let res = data.data;
            if (res.code == 200) {
              let staffGrid = res.data;
              if (staffGrid.allParentId) {
                let placeNameArr = staffGrid.allParentName.split(">");
                let placeCodeArr = staffGrid.allParentId.split(",");
                //增加未完工工单列表
                let lastAreaCode = placeCodeArr[placeCodeArr.length - 1];
                this.$api
                  .getUnfinishedMineTask({
                    userId: this.loginInfo.id,
                    // staffType: this.loginInfo.type,
                    staffType: this.userType,
                    type: "1,2,3,4",
                    curPage: 1,
                    pageSize: 999,
                    startTime: "",
                    endTime: "",
                    areaCode: lastAreaCode,
                    staffId: this.staffInfo.staffId
                  })
                  .then(resP => {
                    if (this.userType == 2) {
                      if (resP.details.length > 0) {
                        $.toast("当前区域中有未完成工单，待完工后再进行扫码申报！", "text");
                        this.$router.push("/notCompleteOrderList?areaCode=" + lastAreaCode);
                      }
                    } else if (this.userType == 1) {
                      if (resP.length > 0) {
                        $.toast("当前区域中有未完成工单，待完工后再进行扫码申报！", "text");
                        this.$router.push("/notCompleteOrderList?areaCode=" + lastAreaCode);
                      }
                    }
                  });

                this.serviceSiteResId = staffGrid.id; //服务地点id
                this.localtion = staffGrid.allParentId; //服务地点id
                if (placeCodeArr.length == 4) {
                  this.localtionPlaceName = placeNameArr.pop();
                } else {
                  this.localtionPlaceName = "";
                }
                this.areaVal = placeNameArr.join(""); //服务地点名称,展示的
                this.localtionName = staffGrid.allParentName.replaceAll(">", "", false); //服务地点名称
              } else {
                $.toast("未找到相关位置", "text");
              }
            }
          });
      } else {
        $.toast("请检查二维码是否正确", "text");
      }
    },
    scanRepairCallBackNew(scanData) {
      let roomCode = scanData[2];
      if (roomCode) {
        this.$api.lookUpById({id: roomCode}).then(res => {
          this.localtion = res.simCode.split(",").slice(-3).join(",");
          this.areaVal = res.simName.split(">").slice(-3).join("");
          this.localtionName = this.areaVal;
        })
        return
        this.axios
          .get(__PATH.BASE_API + "/space/spaceInfo/lookUpById", {
            params: {
              id: roomCode
            },
            headers: {
              hospitalCode: this.hospitalInfo.hospitalCode,
              unitCode: this.hospitalInfo.unitCode
            }
          })
          .then(res => {
            if (res.data.code == 200) {
              this.localtion = res.data.data.simCode.split(",").slice(-3).join(",");
              // this.localtionName = this.workOrdeInfoDate[0].itemType[0].localtion;
              this.areaVal = res.data.data.simName.split(">").slice(-3).join("");
              this.localtionName = this.areaVal;
              // this.localtionPlaceName = res.data.data.ssmName || "";
            }
          });
      }
    },
    scanRepair() {
      if (!YBS.hasPermission("storage")) {
        YBS.reqPermission(["storage"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
            } else {
            }
          }
        });
        return;
      }
      if (!YBS.hasPermission("camera")) {
        YBS.reqPermission(["camera"], function (ret) {
          console.log(ret);
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
          }
        });
        return;
      }

      try {
        YBS.scanCodeNew(true).then(
          item => {
            console.log(item, "ittt");
            if (item[0] == "KJ") {
              this.scanRepairCallBackNew(item);
            } else {
              this.scanRepairCallBack(item);
            }
          },
          () => {
            this.$toast.fail("无效二维码!");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    recognize(path) {
      // console.log(txFlashFileRecognize);
      txFlashFileRecognize.recognize(
        {
          path: path
        },
        function (ret) {}
      );
    },
    getDownLoad(path) {
      this.recognize(path);
    }
  },
  mounted() {
    this.getDialogShow();

    let _this = this;
    // txFlashFileRecognize = api.require("txFlashFileRecognize");
    // txFlashFileRecognize.init({
    //   appid: "1300358855",
    //   secretId: "AKIDwe7MTUyrLFtJbMh76Wgbx7QUQAE7lmvp",
    //   secretKey: "nIeR2OTN2bClz1mIpAgDqKi23CBzbYHA"
    // });
    // txFlashFileRecognize.addEventListener(function(ret) {
    //   // api.alert({ msg: JSON.stringify(ret) });
    //   console.log(ret);
    //   if (ret.eventType == "didRecognizeSuccess") {
    //     _this.value = ret.text;
    //   }
    // });
    // this.axios
    //   .get(__PATH.ONESTOP + "/iHCRSStatisticsController/getNewSpaceInfoByDeptId", {
    //     params: {
    //       hospitalCode: this.hospitalInfo.hospitalCode,
    //       unitCode: this.hospitalInfo.unitCode,
    //       deptId: this.loginInfo.deptId.split(",")[0],
    //     },
    //   })
    //   .then((res) => {
    //     console.log(res);
    //     if (res.data.code == 200 && res.data.data.spaceInfoByDeptIdList.length > 0) {
    //       this.localtion = res.data.data.spaceInfoByDeptIdList[0].allParentId.split(",").slice(-3).join(",");
    //       this.areaVal = res.data.data.spaceInfoByDeptIdList[0].allParentName.split(">").slice(-3).join("");
    //       this.localtionName = this.areaVal;
    //     }
    //   });
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if (systemType === "ios") {
      document.querySelector(".content").style.minHeight = "88%";
    }
    this.alarmId = this.$route.query.alarmId || "";
    // console.log(this?.staffInfo?.teamId);
    this.userType = this.staffInfo.teamId ? 2 : 1;
    /*获取字典项*/
    if (this.loginInfo) {
      this.getPersonnelDictionaryFun(1);
      this.getPersonnelDictionaryFun(2);
      this.getPersonnelDictionaryFun(4);
    }
    this.getAllOffice();
    if (this.loginInfo) {
      //已经登录的用户直接获取
      this.getHospitalDispatchingConfig(this.loginInfo.hospitalCode, this.loginInfo.unitCode);
    } else {
      //未登录 可以定位成功的用户
      if (this.hospitalInfo.hospitalCode) {
        this.getHospitalDispatchingConfig(this.hospitalInfo.hospitalCode, this.hospitalInfo.unitCode);
      }
    }

    //设置服务房间和区域回显
    String.prototype.replaceAll = function (reallyDo, replaceWith, ignoreCase) {
      if (!RegExp.prototype.isPrototypeOf(reallyDo)) {
        return this.replace(new RegExp(reallyDo, ignoreCase ? "gi" : "g"), replaceWith);
      } else {
        return this.replace(reallyDo, replaceWith);
      }
    };
  },
  activated() {
    setTimeout(() => {
      this.$YBS.apiCloudEventKeyBack(this.goback);
    }, 100);
    if (this.loginInfo) {
      // 工单配置提醒
      this.getTaskWorkConfiguration(this.loginInfo.hospitalCode, this.loginInfo.unitCode);
    }
    this.pageTitle = "维修工单补录";
    this.projectName = "cleaning";
    //选择的服务房间与当前的服务房间不一致时，更改服务房间（前提是已经另选择了服务房间）
    if (this.localtionPlaceName != this.changeLocation.localtionPlaceName && this.changeLocation.localtionPlaceName) {
      this.localtionPlaceName = this.changeLocation.localtionPlaceName;
    }
    if (JSON.stringify(this.$route.query).includes("isExist")) {
      //从选择服务区域页面跳转回来携带参数
      let params = this.$route.query;
      this.serviceSiteResId = params.code[2];
      this.localtion = params.code.join(",");
      //        判断服务区域是数组还是字符串
      if (Array.isArray(params.name)) {
        this.areaVal = params.name.join("");
        this.localtionName = params.name.join("");
      } else {
        //          最近服务区域返回的信息
        this.areaVal = params.name;
        this.localtionName = params.name;
      }
      if (this.$route.query.isExist == "false") {
        this.isExist = false;
      } else if (this.$route.query.isExist == "true") {
        this.isExist = true;
      } else {
        this.isExist = this.$route.query.isExist;
        let isHaveRoomName = this.$route.query.isHaveRoomName;
        if (this.isExist && this.changeLocation.localtionPlaceName && !isHaveRoomName) {
          this.localtionPlaceName = "";
          this.localtionPlaceCode = "";
        }
      }
      if (!this.serviceSiteResId) {
        //没选楼层
        let localtionData = this.localtion;
        this.localtion = localtionData.substr(0, localtionData.length - 1);
      }
    }
    let routeQuery = this.$route.query;
    // console.log(routeQuery)
    if (routeQuery.hospitalCode && routeQuery.id) {
      this.scanGetLocation();
      sessionStorage.setItem("unitCode", routeQuery.unitCode);
      sessionStorage.setItem("hospitalCode", routeQuery.hospitalCode);
    }
  },
  watch: {
    searchVal(val) {
      // 根据val值，对比searchOptions的label的值进行筛选
      this.searchOptions = this.sourcesDeptOptions.filter(item => {
        return item.label.indexOf(val) > -1;
      });
    },
    value(value, oldValue) {
      this.value = this.value.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g, "");
      if (oldValue && !value) {
        this.$refs.textarea.click();
      }
    },
    //如果route发生变化,再次执行getlistTypeName
    $route(to, from) {
      this.getlistTypeName();
    },
    hospitalInfo(info) {
      this.tempSelectHosp = info;
      this.getHospitalDispatchingConfig(info.hospitalCode, info.unitCode);
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper {
  height: 100%;
  background-color: $bgColor;

  .content {
    box-sizing: border-box;
    min-height: 92%;
    padding-bottom: 76px !important;
    background-color: $bgColor;

    .reservation-btns {
      marginBottom20();
      text-align: center;

      span {
        display: inline-block;
        width: 1.24rem;
        margin: 0 0.36rem;
      }

      .active {
        color: $color;
        border-bottom: 2px solid $color;
      }
    }

    .other-location-name {
      marginBottom20();

      .ipt-content {
        justify-content: flex-end;
        line-height: 1.2;
        text-align: left;
      }
    }

    .adjustable {
      padding: 0 0.32rem;

      > div {
        text-align: center;
      }

      img {
        // width: 0.48rem;
        width: 14px;
      }

      .down {
        transition: all 0.3s;
      }

      .up {
        transform: rotateZ(180deg);
        transition: all 0.3s;
      }
    }

    .up-content {
      transform: rotateX(90deg);
      transform-origin: center top;
      height: 0;
      transition: height 0.3s;
      transition: all 0.3s;
    }

    .down-content {
      transition: all 0.3s;
      transform-origin: center top;
      margin-bottom: 10px;
    }

    .er-level {
      display: flex;
      align-items: center;
      itemBaseStyle();

      .weui-cells_checkbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .redio-content {
          display: flex;
          color: $contentColor;

          .declare-attr {
            padding: 0;
          }
        }
      }
    }

    .hosp {
      marginBottom20();
    }

    .item-style {
      itemBaseStyle();

      .login-tips {
        line-height: 1;
        white-space: nowrap;
        font-size: 0.28rem;
        font-style: italic;
        color: $color;
      }
    }

    .matter-style {
      min-height: 0.98rem;
      line-height: initial;
    }

    .desc-form {
      margin-top: 0;
      background-color: #fff;

      .desc {
        padding: 0.32rem 0.32rem 0;
        font-size: 0.28rem;
        line-height: 1.5em;
        // margin-bottom: 0.2rem;
        color: $contentColor;

        textarea {
          display: inline-block;
          text-indent: 2em;
          font-size: 0.3rem;
        }
      }
    }

    .tips {
      color: $contentColor;
      text-align: center;
      margin: 0.6rem 0;

      .key-tips {
        color: $color;
      }
    }
  }
}

.btn {
  padding: 0 1.12rem 0.3rem;
  position: relative;
  user-select: none;

  .delete-voice {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.5rem;
  }

  .play-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
    }
  }
}

.recording {
  position: fixed;
  z-index: 2;
  top: 2rem;
  left: 50%;
  margin-left: -1.2rem;

  img {
    width: 2.4rem;
    opacity: 0.7;
  }
}

.title-content {
  itemBaseStyle();
  margin-top: $marginb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ipt-content {
  text-align: right;
  display: flex;
  align-items: center;
  color: #999;
}

.matter-ipt {
  display: flex;
  justify-content: flex-end;

  .ipt-pos {
    position: relative;
    width: 100%;

    .matterInput {
      display: none;
    }

    span {
      font-family: serif;
      text-align: left;
      display: flex;
      font-size: 15px;
    }
  }
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;

  .img-wrapper {
    flex: 1;
    width: 0;
    margin: $marginb;
    position: relative;

    img {
      width: 100%;
    }

    .icon-img {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
  position: fixed;
  bottom: 0;
  z-index: 999;
}

.popups-content {
  width: 80%;
  margin: 0 auto;
  position: fixed;
  max-width: 300px;
  border-radius: 3px;
  overflow: hidden;

  & > div {
    margin: 0 auto;
    background: #fff;
    padding-bottom: 15px;
    border-bottom: 1px solid #efefef;
    height: 1.18rem;
    align-items: flex-end;
    box-sizing: border-box;
  }

  .cfm-phone-num-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 48px;
    font-size: 18px;
    padding: 0;
    color: #3562DB;
  }
}

.play-void-img {
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
}

.title {
  font-size: 0.32rem;
}

.er-level-redio {
  display: flex;
  background-color: #fff;
  font-size: 0.3rem;
  color: $contentColor;
}

.redio-content {
  position: relative;

  .select-time {
    position: absolute;
    left: 1rem;
    bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 5em);

    .text {
      width: 50px;
      line-height: initial;
    }

    .ipt {
      flex: 1;
      text-align: right;
    }
  }

  .textColor {
    color: #999;
  }

  .disPointer {
    pointer-events: none;
  }
}

.hint {
  display: inline-block;
  margin: 0 3px;
}

.reminder{
  height:0.68rem;
  background:#FAECE9;
  padding-left:.32rem;
  line-height:.68rem;
  .reminder-img{
    width:.28rem;
  }
  .reminder-text{
    margin-left:.24rem;
    color:#FF7859;
    font-size:.26rem;
  }

}

.ellipsis{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.hide-box {
  height: 36px;
  background-color: #fff;
  border-radius: 100px 100px 100px 100px;
  border: 1px solid #E5E6EB;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.icon-saomiao {
  font-size: 20px;
}
.right-aligned-input::placeholder {
  text-align: right;
  color: #86909C;
}
.weui-textarea::placeholder {
  color: #86909C;
}
.star::before {
  position: absolute;
  left: 6px;
  top: 4px;
  content:'*';
  color:red;
}
.star-l::before {
  position: absolute;
  left: -9px;
  top: 5px;
  content:'*';
  color:red;
}
.fa {
  position: relative;
}
.ipt-left-align::placeholder {
  text-align: left;
  color: #888;
}
>>> .van-field__label{
  font-size:0.32rem;
  color:#353535
}
>>> .van-field__control{
  font-size:0.30rem;
}
</style>
