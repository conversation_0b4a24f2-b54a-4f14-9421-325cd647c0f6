<template>
  <div class="wrapper">
    <Header title="后勤投诉" @backFun="goback"></Header>
    <div class="content">
      <!-- 工单提醒 -->
      <div class="reminder" v-if="hint">
        <img class="reminder-img" src="@/assets/images/reminder.png" alt="" />
        <span class="reminder-text">{{ hint }}</span>
      </div>
      <div class="er-level border-topbottom">
        <div class="weui-cells_checkbox">
          <div class="er-text title">身份隐匿</div>
          <div class="redio-content content-css">
            <label
              class="weui-cell weui-check__label"
              :for="item.dictValue + 8"
              v-for="item of signatureFlagArr"
            >
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  name="checkbox3"
                  class="weui-check"
                  :id="item.dictValue + 8"
                  :value="item.dictValue"
                  v-model="signatureFlag"
                />
                <i class="weui-icon-checked"></i>
              </div>
              <div class="weui-cell__bd">{{ item.dictLabel }}</div>
            </label>
          </div>
        </div>
      </div>
      <!--关联工单-->
      <div class="er-level relevance">
        <div class="er-text title weui-label" style="width:30vw">关联工单</div>
        <div class="weui-cell__bd work-num" v-if="isShowWorkOrder">
          {{ relevanceWorkNum }}
        </div>
        <div
          class="weui-cell__bd relevance-workNum"
          v-else
          @click="getRelevanceWorkNum"
        >
          <input
            type="text"
            class="content-css"
            placeholder="点击选择关联工单号"
            v-model="relevanceWorkNum"
          />
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>

      <div class="weui-cells complain-title">
        <div class="er-level border-bottom">
          <div class="er-text title">投诉标题</div>
        </div>
        <div class="weui-cell desc weui-cells_form textarea">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea content-css"
              placeholder="请输入投诉标题，字数限制20字以内"
              maxlength="20"
              rows="2"
              v-model="complaintTitle"
            ></textarea>
          </div>
        </div>
      </div>

      <div class="weui-cells weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea content-css"
              placeholder="请输入您要申报的内容描述、语音，字数限制500字以内，语音限制60秒"
              maxlength="500"
              rows="8"
              v-model="value"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
        <sounds-recording
          @getRecord="getRecord"
          @getRecordFile="getRecordFile"
          ref="voices"
        />
      </div>

      <upload-image
        class="bottom upload-img-css"
        style="position:relative"
        ref="imgs"
        @getImg="getImg"
      ></upload-image>
    </div>
    <div class="submit-btn">
      <button class="weui-btn weui-btn_primary" @click="handleSubmitClick">
        提交
      </button>
    </div>
  </div>
</template>

<script>
import wx from "weixin-js-sdk";
import { mapState } from "vuex";
import { preView } from "@/common/lib/util.js";
import UploadImage from "@/common/uploadImg/uploadImg";
import UploadVoice from "@/common/uploadVoice/UploadVoice";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  mixins: [fontSizeMixin],
  name: "ComplainWorkOrder",
  components: {
    UploadImage,
    UploadVoice
  },
  data() {
    return {
      recordingInfo: "",
      recordingInfoFile: "",
      value: "",
      relevanceWorkNum: "",
      signatureFlag: "",
      isShowWorkOrder: false,
      voiceParams: "",
      imagesParams: [],
      imageConfig: [], //处理图片的mediaId和ossUrl
      imagesParams: [],
      voiceFlag: false,
      imgFlag: false,
      accessToken: "",
      isOnlyVoice: false,
      complaintTitle: "",
      designateDeptCode: "",
      designateDeptName: "",
      signatureFlagArr: [], //隐匿类型，从字典值获取
      requiredCode: "", //提交工单必填项
      hint: "", //工单配置提醒
      amount: {
        //上传图片限制
        quantity: 3,
        capital: "三"
      }
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  methods: {
    goback() {
      if (this.$route.query.workNum) {
        this.$router.go(-1);
      } else {
        // api.closeFrame();
        this.$YBS.apiCloudCloseFrame();
      }
    },
    getImg(img) {
      console.log("img", img);
      this.imagesParams.push(img);
    },
    getRecord(info) {
      this.recordingInfo = info;
      this.voiceParams = info;
      console.log("recordingInfo", this.recordingInfo);
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
      console.log("recordingInfoFile", this.recordingInfoFile);
    },
    /**
     * 工单配置提醒接口
     */
    getTaskWorkConfiguration(hospitalCode, unitCode) {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?getTaskWorkConfiguration",
          this.$qs.stringify({
            hospitalCode,
            unitCode,
            workTypeCode: 5 //后勤投诉
          }),
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.hint = res.hint;
          this.workCode = res.workTypeCode;
        });
    },
    /**
     * 获取关联工单号（跳转我的-后勤投诉列表）
     */
    getRelevanceWorkNum() {
      try {
        //清空之前选择的时间记录
        sessionStorage.removeItem("selectTime");
      } catch (err) {}
      localStorage.prevLinkUrl = "/complain";
      this.$router.push({
        path: "/myorder",
        query: {
          source: "newComplainOrder",
          type: ""
        }
      });
    },
    handleTextareaBlurEvent() {
      let scrollHeight =
        document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 获取录音路径
     * @param params
     */
    getVoice(params) {
      if (this.isOnlyVoice) {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode
                ? this.hospitalCode
                : sessionStorage.hospitalCode
                ? sessionStorage.getItem("hospitalCode")
                : "BJSJTYY",
              unitCode: this.unitCode
                ? this.unitCode
                : sessionStorage.unitCode
                ? sessionStorage.getItem("unitCode")
                : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params,
              type: "1"
            })
          )
          .then(res => {
            this.voiceParams = res.data.data.ossUrl;
            this.isMissingItem();
          });
      }
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode
                ? this.hospitalCode
                : sessionStorage.hospitalCode
                ? sessionStorage.getItem("hospitalCode")
                : "BJSJTYY",
              unitCode: this.unitCode
                ? this.unitCode
                : sessionStorage.unitCode
                ? sessionStorage.getItem("unitCode")
                : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
            // this.imagesParams.push(res.data.data.ossUrl);
            // this.imageConfig.push(res.data.data);
          });
      });
    },
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    },
    //删除录音
    deleteVoice() {
      this.voiceParams = "";
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode
              ? this.hospitalCode
              : sessionStorage.hospitalCode
              ? sessionStorage.getItem("hospitalCode")
              : "BJSJTYY",
            unitCode: this.unitCode
              ? this.unitCode
              : sessionStorage.unitCode
              ? sessionStorage.getItem("unitCode")
              : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: this.voiceParams
          })
        )
        .then(res => {});
    },
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempIdx = "";
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode
              ? this.hospitalCode
              : sessionStorage.hospitalCode
              ? sessionStorage.getItem("hospitalCode")
              : "BJSJTYY",
            unitCode: this.unitCode
              ? this.unitCode
              : sessionStorage.unitCode
              ? sessionStorage.getItem("unitCode")
              : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**
     * 处理提交按钮事件
     */
    handleSubmitClick() {
      this.isMissingItem();
      // if (
      //   this.$refs.imgs.imgServerId.length != 0 &&
      //   this.$refs.voices.hasVoice != "recorded"
      // ) {
      //   //需上传图片、没有录音
      //   let imgLen = () => {
      //     if (this.imagesParams.length != 0) {
      //       this.isMissingItem();
      //     } else {
      //       setTimeout(() => {
      //         imgLen();
      //       }, 500);
      //     }
      //   };
      //   imgLen();
      // } else if (
      //   this.$refs.imgs.imgServerId.length != 0 &&
      //   this.$refs.voices.hasVoice == "recorded"
      // ) {
      //   //需上传图片、录音
      //   let imgLen = () => {
      //     if (this.imagesParams.length != 0) {
      //       this.isOnlyVoice = true;
      //       this.$refs.voices.saveVoice();
      //     } else {
      //       setTimeout(() => {
      //         imgLen();
      //       }, 500);
      //     }
      //   };
      //   imgLen();
      // } else if (
      //   this.$refs.imgs.imgServerId.length == 0 &&
      //   this.$refs.voices.hasVoice == "recorded"
      // ) {
      //   //不需上传图片、但有录音
      //   this.isOnlyVoice = true;
      //   this.$refs.voices.saveVoice();
      // } else if (
      //   this.$refs.imgs.imgServerId.length == 0 &&
      //   this.$refs.voices.hasVoice == "unvoice"
      // ) {
      //   //不需上传图片、没有录音
      //   this.isMissingItem();
      // }
    },
    /**
     * 设置微信config参数
     * @param res
     */
    setAllConfig(res) {
      this.$refs.voices.setConfig(res);
      this.$refs.imgs.setConfig(res);
      this.accessToken = res.data.data.accessToken;
    },
    /**
     * 工单提交请求函数
     */
    getHttp() {
      // this.isMissingItem()
      console.log("进去了123");
      // if (this.requiredParameter()) {
      console.log("进去了");

      $.showLoading("提交中…");
      let sourcesFloorName, sourcesFloor;
      // if (this.staffInfo.gridList.length == 0) {
      //   sourcesFloorName = "";
      //   sourcesFloor = "";
      // } else {
      //   sourcesFloorName = this.staffInfo.gridList[0].gridName;
      //   sourcesFloor = this.staffInfo.gridList[0].parentGridId;
      // }
      let param = null;
      let params = {
        hospitalCode: this.loginInfo.hospitalCode,
        deptCode: this.loginInfo.deptId,
        unitCode: this.loginInfo.unitCode,
        userId: this.loginInfo.id,
        phoneNumber: this.loginInfo.phone,
        realName: this.loginInfo.staffName,
        workTypeCode: "5",
        relevanceWorkNum: this.relevanceWorkNum,
        signatureFlag: this.signatureFlag,
        deptName: this.loginInfo.deptName,
        questionDescription: this.value,
        callerTape: this.voiceParams,
        attachment: JSON.stringify(this.imagesParams),
        jobNumber: this.staffInfo.staffNumber,
        accessToken: this.accessToken,
        // type: this.loginInfo.type,
        type: this.staffInfo.teamId ? 2 : 1,
        complaintTitle: this.complaintTitle,
        job: this.staffInfo.administrativePost,
        designateDeptCode: this.designateDeptCode,
        designateDeptName: this.designateDeptName,
        sourcesFloorName: sourcesFloorName,
        sourcesFloor: sourcesFloor,
        staffId: this.staffInfo.staffId,
        workSources: "1"
      };
      param = this.$qs.stringify(params);
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat",
          param,
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(this.handleRequestSucc);
      // if (this.detector.browser.name != "micromessenger") {
      //   let imgArr = this.$refs.imgs.imgLocalIds;
      //   let blobArr = preView(imgArr);
      //   params.attachmentBlob1 = blobArr[0] || "";
      //   params.attachmentBlob2 = blobArr[1] || "";
      //   params.attachmentBlob3 = blobArr[2] || "";
      //   //params.callerTape1 = this.$refs.voices.base64Path.filedata[0] || "";
      //   params.workSources = 1;
      //   params.type = 1;
      //   params.repairWork = 1;
      //   (params.callerTape = null),
      //     (params.attachment = null),
      //     console.log(params.callerTape1);
      //   let formdata = new FormData();
      //   for (var item in params) {
      //     formdata.append(item, params[item] || "");
      //   }
      //   param = formdata;
      //   this.axios
      //     .post(__PATH.ONESTOP + "/appOlgTaskManagement.do?saveTask", param)
      //     .then(this.handleRequestSucc);
      // } else {
      //   param = this.$qs.stringify(params);
      //   this.axios
      //     .post(
      //       __PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat",
      //       param
      //     )
      //     .then(this.handleRequestSucc);
      // }
      // }
    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      $.hideLoading();
      if (res.data.code == "200") {
        $.toast("工单提交成功", "success", () => {
          // api.closeFrame();
          this.$YBS.apiCloudCloseFrame();
        });
      } else {
        $.toast(res.data.message, "text");
        if (res.data.code == 40001) {
          this.voiceParams = "";
          this.$refs.voices.handleDelVoiceClick();
        }
      }
    },
    /**
     * 校验必填选项
     */
    isMissingItem() {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement/getIsRequired",
          this.$qs.stringify({
            hospitalCode: this.loginInfo.hospitalCode,
            unitCode: this.loginInfo.unitCode,
            workTypeCode: 5
          }),
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.requiredCode = res.requiredCode;
          this.requiredParameter(this.requiredCode);
        });
    },
    /**
     * 必选项
     */
    requiredParameter(value) {
      if (
        value.indexOf("complaintTitle", 0) != -1 &&
        this.complaintTitle == ""
      ) {
        $.toast("请填写投诉标题", "text");
        return;
      } else if (!this.value && !this.voiceParams) {
        $.toast("请填写申报描述或录入语音", "text");
        return;
      } else if (
        value.indexOf("relevanceWorkNum", 0) != -1 &&
        this.relevanceWorkNum == ""
      ) {
        $.toast("请选择关联工单", "text");
        return;
      } else {
        this.getHttp();
        return true;
      }
    },
    /**
     * 获取医院自定义的字典项接口（隐匿类型）
     */
    getPersonnelDictionaryFun() {
      this.$api
        .getPersonnelDictionary({
          type: 10
        })
        .then(res => {
          this.signatureFlagArr = res.reverse();
        });
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if (systemType === "ios") {
      document.querySelector(".content").style.minHeight = "88%";
    }
    if (this.$route.query.source == 0) {
      //如果关联工单字段始终存在，则在公众号菜单的链接中去掉参数
      //        从投诉入口投诉
      this.isShowWorkOrder = false;
    }
    if (location.search.indexOf("workOrderDetails") != -1) {
      // 从工单进行投诉
      var str = location.search;
      var strs = str.substr(1).split("&"); //[]
      var theRequest = {};
      for (var i = 0; i < strs.length; i++) {
        theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
      }
      this.relevanceWorkNum = theRequest.workNum;
      this.designateDeptCode = theRequest.designateDeptCode;
      this.designateDeptName = decodeURI(theRequest.designateDeptName);
      this.isShowWorkOrder = true;
    }
    //清空localStorage中的prevLinkUrl，以判断我的工单中是否显示单选框（单选框作用是选择关联工单号）
    localStorage.prevLinkUrl = "";
  },
  activated() {
    this.getTaskWorkConfiguration(
      this.loginInfo.hospitalCode,
      this.loginInfo.unitCode
    );
    if (this.$route.params.workNum) {
      //来源于筛选关联工单（我的工单列表）
      this.relevanceWorkNum = this.$route.params.workNum;
      this.designateDeptCode = this.$route.params.designateDeptCode;
      this.designateDeptName = this.$route.params.designateDeptName;
      localStorage.prevLinkUrl = "";
    }
    this.getPersonnelDictionaryFun();
  },
  watch: {
    signatureFlagArr(val) {
      this.signatureFlag = this.signatureFlagArr[0].dictValue;
    },
    complaintTitle() {
      // 禁止输入表情
      this.complaintTitle = this.complaintTitle.replace(
        /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,
        ""
      );
    },
    value() {
      this.value = this.value.replace(
        /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,
        ""
      );
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper
  height: 100%;
  background-color: #F2F4F9;
  .content
    box-sizing: border-box;
    min-height: 90%;
    padding-bottom: 76px;
    background-color: #F2F4F9;
    .er-level
      display: flex;
      align-items: center;
      itemBaseStyle();
      .weui-cells_checkbox
        display: flex;
        align-items: center;
        justify-content: center;
        justify-content: space-between;
        width: 100%;
        .redio-content
          display: flex;
          color: $contentColor
    .complain-title
      margin-top: 0.2rem;
    .desc-form
      margin-top: 0.2rem;
      .desc
        padding: 0.32rem 0.32rem 0;
        textarea
          text-indent 2em
    .relevance
      margin-top: 0.2rem;
      .work-num
        text-align: right;
        font-size: calc(16px * var(--font-scale))!important;
        line-height: 1.2;
      .relevance-workNum
        display: flex;
        input
          flex: 1;
          text-align: right;
        .iconfont
          width: 16px;
          float: right;
    .hosp
      margin: 0
      marginBottom20()
.btn
  padding: 0 1.12rem 0.3rem;
  position: relative;
  user-select: none;
  .delete-voice
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.5rem;
  .play-btn
    display: flex;
    justify-content: center;
    align-items: center;
    img
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
.recording
  position: fixed;
  z-index: 2;
  top: 2rem;
  left: 50%;
  margin-left: -1.2rem;
  img
    width: 2.4rem;
    opacity: 0.7;
.title-content
  itemBaseStyle();
  margin-top: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .ipt-content
    text-align: right;
    color: $textColor;
.img-content
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;
  .img-wrapper
    flex: 1;
    width: 0;
    margin: 0.2rem;
    position: relative;
    img
      width: 100%;
    .icon-img
      position: absolute;
      top: 0;
      right: 0;
.submit-btn
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
  .weui-btn
    font-size: calc(16px * var(--font-scale))!important;
    height: 50px;
    line-height: 50px;
.play-void-img
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
.title
  font-size: calc(16px * var(--font-scale));
.reminder{
  height:0.68rem;
  background:#FAECE9;
  padding-left:.32rem;
  line-height:.68rem;
  .reminder-img{
    width:.28rem;
  }
  .reminder-text{
    margin-left:.24rem;
    color:#FF7859;
    font-size:.26rem;
  }

}
.content-css {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .sounds-div span {
  font-size: calc(15px * var(--font-scale))!important;
}
/deep/.upload-img-css .explain > span {
  font-size: calc(15px * var(--font-scale))!important;
}
/deep/ .upload-img-css .explain  .upload-img{
  font-size: calc(12px * var(--font-scale))!important;
}
</style>
