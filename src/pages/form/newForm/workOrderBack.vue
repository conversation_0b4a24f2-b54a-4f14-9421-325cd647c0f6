<template>
  <div class="inner">
    <Header title="不通过" @backFun="goBack"></Header>

    <van-field v-model="rollbackExplain" rows="2" autosize label="原因说明" type="textarea" maxlength="500" placeholder="请输入原因说明" show-word-limit />
    <!--图片上传-->
    <upload-image ref="imgs" @getImg="getImg" @delImg="delImg"></upload-image>
    <div class="btn"><van-button style="width:90%" color="#3562db" @click="confirm">确定</van-button></div>
  </div>
</template>

<script>
import UploadImage from "@/common/uploadImg/uploadImg";
import fontSizeMixin from "@/mixins/fontSizeMixin";

export default {
  mixins: [fontSizeMixin],
  components: {
    UploadImage
  },
  data() {
    return {
      rollbackExplain: "",
      imagesParams: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    confirm() {
      console.log('图片数量', this.$refs.imgs.fileList.length)
      console.log('已上传图片数量', this.imagesParams.length)
      if(this.$refs.imgs.fileList.length != this.imagesParams.length) {
        return this.$toast("请等待图片上传完成");
      }
      let params = {
        taskId: this.$route.query.id,
        rollbackExplain: this.rollbackExplain,
        rollbackImage: this.imagesParams.toString()
      };
      this.$api.appRollbackTask(params).then(res => {
        this.$toast.success("回退成功");
        this.goBack();
      });
    },
    getImg(img) {
      console.log("img", img);
      this.imagesParams.push(img);
    },
    delImg(url) {
      this.imagesParams = this.imagesParams.filter(item => {
        return item != url;
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style style lang="scss" scoped>
.inner {
  background-color: #fff;
  min-height: 100%;
}
.btn {
  width: 100%;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
  .van-button {
    font-size: calc(16px * var(--font-scale))!important;
  }
}
.van-cell {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .explain span {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .explain .upload-img {
  font-size: calc(12px * var(--font-scale))!important;
}
</style>
