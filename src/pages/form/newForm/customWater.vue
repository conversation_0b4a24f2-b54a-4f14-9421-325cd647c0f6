<template>
  <div class="wrapper">
    <!-- 工单提醒 -->
    <div class="reminder" v-if="hint">
      <img class="reminder-img" src="@/assets/images/reminder.png" alt="" />
      <span class="reminder-text">{{ hint }}</span>
    </div>
    <div class="content">
      <!-- 顶部配置项 -->
      <div class="reservation-btns item-style">
        <span
          :class="{ active: idx == appointmentType }"
          @click="handleClickTimeText(idx)"
          v-for="(item, idx) of serviceHoursText"
          :key="idx"
          >{{ item.dictLabel }}</span
        >
      </div>
      <service-hours ref="serviceHours" v-if="reservation"></service-hours>

      <!-- 桶装水设置不隐藏的内容 -->
      <div v-else>
        <div
          class="weui-cell border-rightbottom item-style"
          style="padding-right:0"
        >
          <div class="weui-cell__hd">
            <label class="weui-label title">服务区域</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input
              @click="toSelectArea"
              class="weui-input ipt"
              type="text"
              readonly
              v-model="localtionName"
              placeholder="请选择服务区域"
            />
            <span
              class="iconfont icon-saomiao"
              @click="scanRepair"
              style="color:#38c7c4;padding-right:10px"
            ></span>
          </div>
        </div>
        <!--服务房间-->
        <div class="weui-cell border-rightbottom item-style site">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务房间</label>
          </div>
          <div
            class="weui-cell__bd ipt-content"
            @click="toSelectSite"
            v-if="isExist"
          >
            <input
              class="weui-input ipt"
              type="text"
              readonly
              v-model="localtionPlaceName"
              placeholder="请选择服务房间"
            />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>
        <!-- 送水签收人 -->
        <div class="weui-cell item-style matter-style border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">送水签收人</label>
          </div>
          <input
            class="weui-input ipt ellipsis"
            type="text"
            maxlength="20"
            v-model="receiptName"
            placeholder="请输入送水签收人"
          />
        </div>
        <!-- 数量 -->
        <div class="weui-cell item-style matter-style border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">数量</label>
          </div>
          <div class="weui-cell__bd ipt-content matter-ipt">
            <div class="ipt-pos">
              <input-number
                ref="inpNum"
                @numVariation="variation"
                @numberChange="countChange"
                :num="reviseNum"
              ></input-number>
            </div>
          </div>
        </div>
      </div>

      <!--申报描述-->
      <div class="weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea"
              placeholder="请输入您要申报的内容描述、语音，字数在120字以内，语音限制60秒"
              maxlength="120"
              rows="5"
              ref="textarea"
              v-model="describe"
              @keydown="keydown($event)"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
        <upload-voice
          ref="voices"
          @getvVoiceParams="getVoice"
          @voiceFlag="deleteVoice"
        ></upload-voice>
      </div>
      <!--图片上传-->
      <upload-image
        class="bottom"
        style="position:relative"
        ref="imgs"
        @getImgParams="getImages"
        @deleteImg="deleteImg"
        v-bind:num="this.amount"
      ></upload-image>
      <div :class="[rotate ? 'down-content' : 'up-content']">
        <!--紧急程度-->
        <urgent-level ref="urgent" :urgentArr="urgentArr"></urgent-level>
        <!--申报属性-->
        <declare-attribute
          ref="attribute"
          :declareAttributeArr="declareAttributeArr"
        ></declare-attribute>
        <!--是否返修-->
        <is-rework ref="isRework"></is-rework>
      </div>
      <!--收起和完善信息-->
      <div class="adjustable">
        <div @click="switchBtn" style="color:#38c7c4">
          <span class="hint">信息越详细</span>
          <img
            src="~images/newForm/btn-fold.gif"
            :class="[!rotate ? 'down' : 'up']"
          />
          <span class="hint">报修更快速</span>
        </div>
      </div>
    </div>
    <div class="submit-btn">
      <button class="weui-btn weui-btn_primary" @click="submitAfterVrification">
        提交
      </button>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import ServiceHours from "./components/ServiceHours";
import UploadImage from "@/common/uploadImg/uploadImg";
import UploadVoice from "@/common/uploadVoice/UploadVoice";
import UrgentLevel from "./components/UrgentLevel";
import DeclareAttribute from "./components/DeclareAttribute";
import IsRework from "./components/IsRework";
import inputNumber from "./components/inputNumber";
export default {
  name: "customWater",
  data() {
    return {
      reviseNum: 0,
      serviceHoursText: [{ dictLabel: "预约" }, { dictLabel: "立刻" }], // 头顶配置
      reservation: false,
      appointmentType: 0,
      voiceParams: "",
      serviceSiteResId: "", //服务区域ID
      allSiteDataInfo: [], //请求返回所有的相关地点的信息
      imagesParams: [],
      requireCode: 2, //判断是否为自动派单
      localtionName: "", //服务区域
      localtion: "",
      localtionPlaceName: "", //服务房间
      localtionPlaceCode: "",
      matterlists: "", //服务事项(展示)
      matterCodes: "",
      describe: "", //描述
      rotate: false, //完善申报信息时候旋转
      urgentArr: [], //紧急程度字典项
      isSubmit: true, //节流标示
      accessToken: "",
      declareAttributeArr: [],
      isExist: true,
      hint: "", //工单配置提醒
      workCode: "", //桶装水工单
      receiptName: "", //送水签收人
      requiredCode: "", //提交工单必填项
      amount: {
        //上传图片限制
        quantity: 3,
        capital: "三"
      }
    };
  },
  computed: {
    ...mapState([
      "loginInfo",
      "staffInfo",
      "changeLocation",
      "hospitalInfo",
      "openId",
      "serviceAreaTreeData"
    ]),
    reviseServiceArea() {
      return this.$store.state.serviceArea;
    },
    changeLocation() {
      return this.$store.state.changeLocation;
    },
    reviseServiceMatters() {
      return this.$store.state.serviceMatters;
    },
    workTypeCode() {
      return this.$route.query.workTypeCode;
    },
    isItem() {
      return this.$route.query.isItem == "Y";
    }
  },
  watch: {
    receiptName() {
      this.receiptName = this.receiptName.replace(
        /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,
        ""
      );
    },
    reviseServiceMatters: {
      deep: true,
      handler(value) {
        if (value.itemServiceCode) {
          this.matterlists =
            value.itemTypeName +
            "-" +
            value.itemDetailName +
            "-" +
            value.itemServiceName;
          this.matterCodes =
            value.itemTypeCode +
            "," +
            value.itemDetailCode +
            "," +
            value.itemServiceCode;
        } else if (value.itemDetailCode) {
          this.matterlists = value.itemTypeName + "-" + value.itemDetailName;
          this.matterCodes = value.itemTypeCode + "," + value.itemDetailCode;
        } else {
          this.matterlists = value.itemTypeName;
          this.matterCodes = value.itemTypeCode;
        }
      }
    },
    reviseServiceArea(value) {
      let arr = [];
      value.code.forEach(item => {
        if (item) {
          arr.push(item);
        }
      });
      if (arr.length < 3) {
        this.isExist = false;
      } else {
        this.isExist = true;
      }
      this.localtion = arr.join(",");
      this.serviceSiteResId = value.code[2];
      if (Array.isArray(value.name)) {
        this.localtionName = value.name.join("");
      } else {
        //          最近服务区域返回的信息
        this.localtionName = value.name;
      }
    },
    changeLocation(value) {
      this.localtionPlaceName = value.localtionPlaceName;
      this.localtionPlaceCode = value.localtionPlaceCode;
    },
    describe(value, oldValue) {
      if (oldValue && !value) {
        this.$refs.textarea.click();
      }
    }
  },
  created() {
    this.getHospitalDispatchingConfig(
      this.loginInfo.userOffice[0].hospitalCode,
      this.loginInfo.userOffice[0].unitCode
    );
    this.getPersonnelDictionaryFun(1);
    this.getPersonnelDictionaryFun(2);
    this.getPersonnelDictionaryFun(4);
    this.getConfigParams();
    this.getTaskWorkConfiguration(
      this.loginInfo.userOffice[0].hospitalCode,
      this.loginInfo.userOffice[0].unitCode,
      this.$route.query.workTypeCode
    );
    this.receiptName = JSON.parse(localStorage.getItem("loginInfo")).name;
  },
  methods: {
    /**
     * 工单配置提醒接口
     */
    getTaskWorkConfiguration(hospitalCode, unitCode, workTypeCode) {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?getTaskWorkConfiguration",
          this.$qs.stringify({
            hospitalCode,
            unitCode,
            workTypeCode
          }),
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.hint = res.hint;
          this.workCode = res.workTypeCode;
        });
    },
    /**
     * 获取医院自定义的字典项接口
     */
    getPersonnelDictionaryFun(type) {
      let params = {
        type: type
      };
      if (type == 2) {
        params.states = 2;
      }
      this.$api.getPersonnelDictionary(params).then(res => {
        switch (type) {
          case 1:
            this.urgentArr = res.reverse();
            break;
          case 2:
            this.declareAttributeArr = res;
            break;
          case 4:
            this.serviceHoursText = res;
            this.appointmentType = res[0].dictValue;
            if (this.appointmentType == 1) {
              this.reservation = true;
            }
            break;
        }
      });
    },
    variation(value) {
      this.reviseNum = value;
    },
    countChange(type) {
      if (type == "add") {
        this.reviseNum++;
      } else {
        if (this.reviseNum > 0) {
          this.reviseNum--;
        }
      }
    },
    //切换头部区域
    handleClickTimeText(idx) {
      this.appointmentType = idx;
      if (idx == 0) {
        this.reservation = false;
      } else {
        this.reservation = true;
      }
    },
    //区分自动非自动派工
    getHospitalDispatchingConfig(hospitalCode, unitCode) {
      this.$api
        .getHospitalDispatchingConfig({
          hospitalCode: hospitalCode,
          unitCode: unitCode
        })
        .then(res => {
          //否需要必填字段
          this.requireCode = res;
        });
    },
    //扫码成功之后的处理
    scanRepairCallBack(scanData) {
      let result = scanData;
      if (result[0] == "ihsp") {
        this.axios
          .post(
            process.env.API_BASE +
              "/hospitalController/getHospitalGridInfoById",
            this.$qs.stringify({
              unitCode: this.loginInfo.userOffice[0].unitCode,
              hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
              gridCode: result[result.length - 1]
            })
          )
          .then(data => {
            let res = data.data;
            if (res.code == 200) {
              let staffGrid = res.data;
              if (staffGrid.allParentId) {
                let placeNameArr = staffGrid.allParentName.split(">");
                let placeCodeArr = staffGrid.allParentId.split(",");
                //增加未完工工单列表
                let lastAreaCode = placeCodeArr[placeCodeArr.length - 1];
                this.$api
                  .getUnfinishedMineTask({
                    userId: this.loginInfo.id,
                    // staffType: 2,
                    staffType: this.loginInfo.type,
                    type: "1,2,3,4",
                    curPage: 1,
                    pageSize: 999,
                    startTime: "",
                    endTime: "",
                    // areaCode: 'c9ec1fdd94a34373aaad00305e2a4b49',
                    areaCode: lastAreaCode,
                    // staffId:'89733778adba4940b272f54ab1a1d5aa'
                    staffId: this.staffInfo.staffId
                  })
                  .then(resP => {
                    // console.log(resP)
                    if (this.loginInfo.type == 2) {
                      if (resP.details.length > 0) {
                        $.toast(
                          "当前区域中有未完成工单，待完工后再进行扫码申报！",
                          "text"
                        );
                        this.$router.push("/notCompleteOrderList");
                      }
                    } else if (this.loginInfo.type == 1) {
                      if (resP.length > 0) {
                        $.toast(
                          "当前区域中有未完成工单，待完工后再进行扫码申报！",
                          "text"
                        );
                        this.$route.push("/notCompleteOrderList");
                      }
                    }
                  });

                this.serviceSiteResId = staffGrid.id; //服务区域id
                this.localtion = staffGrid.allParentId; //服务区域id
                if (placeCodeArr.length == 4) {
                  this.localtionPlaceName = placeNameArr.pop();
                  this.localtionPlaceCode = placeCodeArr.pop();
                } else {
                  this.localtionPlaceName = "";
                  this.localtionPlaceCode = "";
                }
                this.localtionName = placeNameArr.join(""); //服务区域名称,展示的
                // this.localtionName = staffGrid.allParentName.replaceAll(
                //   ">",
                //   "",
                //   false
                // ); //服务区域名称
              } else {
                $.toast("未找到相关位置", "text");
              }
            }
          });
      } else {
        $.toast("请检查二维码是否正确", "text");
      }
    },
    //扫描空间码获取服务房间，区域
    scanRepair() {
      // this.scanRepairCallBack(
      //   "ihsp,ZXYSZGDW,ZKZXYSYY,ZKZXYSYY0100101002".split(",")
      // );
      window.wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ["qrCode", "barCode"],
        success: res => {
          let result = res.resultStr.split(","); // 当needResult 为 1 时，扫码返回的结果
          this.scanRepairCallBack(result);
        }
      });
    },
    //选择服务区域用
    toSelectArea() {
      //级联弹窗数据还原
      this.localtionPlaceName = "";
      this.localtionPlaceCode = "";
      this.$router.push({
        path: "/selectArea",
        query: {
          source: "revise",
          workTypeCode: this.workTypeCode
        }
      });
    },
    //选择服务房间用
    toSelectSite() {
      let arr = this.serviceSiteResId.split(",");
      if (this.serviceSiteResId == "") {
        $.toast("请先选择服务区域", "text");
      } else {
        this.showVal(this.serviceAreaTreeData, arr[arr.length - 1]);
        return;
        if (this.loginInfo && !location.href.includes("hospitalCode")) {
          //注册并已登录用户,而且不是通过扫码进行报修的
          this.$api
            .cascadingQueryHospitalGridInfo({
              gridId: arr[arr.length - 1]
            })
            .then(this.showVal);
        } else {
          //临时用户
          this.axios
            .post(
              process.env.API_BASE +
                "/hospitalController/cascadingQueryHospitalGridInfo",
              this.$qs.stringify({
                unitCode: this.unitCode
                  ? this.unitCode
                  : sessionStorage.unitCode,
                hospitalCode: this.hospitalCode
                  ? this.hospitalCode
                  : sessionStorage.hospitalCode,
                gridId: arr[arr.length - 1]
              })
            )
            .then(this.showVal);
        }
      }
    },
    //仅供服务房间展示，调起弹窗并设置相关参数
    showVal(res, gridId) {
      // if (res.data && res.data.code == 200) {
      //   this.allSiteDataInfo = res.data.data;
      // } else {
      //   this.allSiteDataInfo = res;
      // }
      this.allSiteDataInfo = res.filter(item => {
        return item.parentId == gridId;
      });
      //将所有地点取出放到数组中
      let siteArr = [];
      for (let i = 0; i < this.allSiteDataInfo.length; i++) {
        siteArr.push(this.allSiteDataInfo[i]);
      }
      //跳到搜索页面
      this.$router.push({
        name: "Site",
        params: {
          list: siteArr
        }
      });
    },
    /**
     * 跳转选择服务事项页面
     */
    goToSelectMatterlists() {
      this.$router.push({
        path: "/matterlists",
        query: {
          workTypeCode: this.workTypeCode,
          source: "custom",
          projectName: "/task"
        }
      });
    },
    //空格校验
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    },
    handleTextareaBlurEvent() {
      let scrollHeight =
        document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 获取录音路径
     * @param params
     */
    getVoice(params) {
      if (this.isOnlyVoice) {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode
                ? this.hospitalCode
                : sessionStorage.hospitalCode
                ? sessionStorage.getItem("hospitalCode")
                : "BJSJTYY",
              unitCode: this.unitCode
                ? this.unitCode
                : sessionStorage.unitCode
                ? sessionStorage.getItem("unitCode")
                : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params,
              type: "1"
            })
          )
          .then(res => {
            this.voiceParams = res.data.data.ossUrl;
            this.isMissingItem();
          });
      }
    },
    /**
     * 删除录音
     */
    deleteVoice() {
      this.voiceParams = "";
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode
              ? this.hospitalCode
              : sessionStorage.hospitalCode
              ? sessionStorage.getItem("hospitalCode")
              : "BJSJTYY",
            unitCode: this.unitCode
              ? this.unitCode
              : sessionStorage.unitCode
              ? sessionStorage.getItem("unitCode")
              : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: this.voiceParams
          })
        )
        .then(res => {});
    },
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode
              ? this.hospitalCode
              : sessionStorage.hospitalCode
              ? sessionStorage.getItem("hospitalCode")
              : "BJSJTYY",
            unitCode: this.unitCode
              ? this.unitCode
              : sessionStorage.unitCode
              ? sessionStorage.getItem("unitCode")
              : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.hospitalCode
                ? this.hospitalCode
                : sessionStorage.hospitalCode
                ? sessionStorage.getItem("hospitalCode")
                : "BJSJTYY",
              unitCode: this.unitCode
                ? this.unitCode
                : sessionStorage.unitCode
                ? sessionStorage.getItem("unitCode")
                : "BJSYGJ",
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
            // this.imagesParams.push(res.data.data.ossUrl);
            // this.imageConfig.push(res.data.data);
          });
      });
    },
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    },
    /**
     * 获取签名
     */
    getConfigParams() {
      // 进行签名的时候  Android 不用使用之前的链接， ios 需要
      let signLink = /(Android)/i.test(navigator.userAgent)
        ? location.href.split("#")[0]
        : window.entryUrl;
      let paramValue = {
        localUrl: signLink,
        appId: process.env.WxAppid
      };
      this.axios
        .get(process.env.API_HOST + "/accessToken/getSignature", {
          params: paramValue
        })
        .then(this.setAllConfig);
    },
    /**
     * 设置微信config参数
     * @param res
     */
    setAllConfig(res) {
      this.$refs.voices.setConfig(res);
      this.$refs.voices.initRecord();
      this.$refs.imgs.setConfig(res);
      this.accessToken = res.data.data.accessToken;
    },
    //切换更多选项
    switchBtn() {
      this.rotate = !this.rotate;
    },
    //工单提交
    submitAfterVrification() {
      if (
        this.$refs.imgs.imgServerId.length != 0 &&
        this.$refs.voices.hasVoice != "recorded"
      ) {
        //上传图片、没有录音
        let imgLen = () => {
          if (this.imagesParams.length != 0) {
            this.isMissingItem();
          } else {
            setTimeout(() => {
              imgLen();
            }, 500);
          }
        };
        imgLen();
      } else if (
        this.$refs.imgs.imgServerId.length != 0 &&
        this.$refs.voices.hasVoice == "recorded"
      ) {
        //上传图片、录音
        let imgLen = () => {
          if (this.imagesParams.length != 0) {
            this.isOnlyVoice = true;
            this.$refs.voices.saveVoice();
          } else {
            setTimeout(() => {
              imgLen();
            }, 500);
          }
        };
        imgLen();
      } else if (
        this.$refs.imgs.imgServerId.length == 0 &&
        this.$refs.voices.hasVoice == "recorded"
      ) {
        //不需上传图片、但有录音
        this.isOnlyVoice = true;
        this.$refs.voices.saveVoice();
      } else if (
        this.$refs.imgs.imgServerId.length == 0 &&
        this.$refs.voices.hasVoice == "unvoice"
      ) {
        //不需上传图片、没有录音
        this.isMissingItem();
      }
    },
    isMissingItem() {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement/getIsRequired",
          this.$qs.stringify({
            hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
            unitCode: this.loginInfo.userOffice[0].unitCode,
            workTypeCode: this.workTypeCode
          }),
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.requiredCode = res.requiredCode;
          this.requiredParameter(res.requiredCode);
        });
    },
    requiredParameter(value) {
      if (this.requireCode != 2) {
        if (value.indexOf("localtion", 0) != -1 && !this.localtion) {
          $.toast("请选择服务区域", "text");
          return false;
        }
        if (!this.reviseNum) {
          //数量一直必填
          $.toast("请选择数量", "text");
          return false;
        }
        if (value.indexOf("realName", 0) != -1 && !this.receiptName) {
          $.toast("请填写送水签收人", "text");
          return false;
        }
        if (
          value.indexOf("itemTypeCode", 0) != -1 &&
          !this.reviseServiceMatters.itemTypeCode
        ) {
          if (this.isItem) {
            $.toast("请选择服务事项", "text");
            return false;
          }
        }
        if (this.appointmentType == 1 && !this.$refs.serviceHours.reservation) {
          $.toast("请填写预约时间", "text");
          return false;
        }
        if (!this.localtion) {
          if (!this.describe && !this.voiceParams) {
            $.toast("请填写申报描述或录入语音", "text");
            return false;
          }
        }
      } else {
        if (!this.describe && !this.voiceParams) {
          $.toast("请填写申报描述或录入语音", "text");
          return false;
        }
        if (this.appointmentType == 1 && !this.$refs.serviceHours.reservation) {
          $.toast("请填写预约时间", "text");
          return false;
        }
        if (value.indexOf("localtion", 0) != -1 && !this.localtion) {
          $.toast("请选择服务区域", "text");
          return false;
        }
        if (!this.reviseNum) {
          //数量一直必填
          $.toast("请选择数量", "text");
          return false;
        }
        if (value.indexOf("realName", 0) != -1 && !this.receiptName) {
          $.toast("请填写送水签收人", "text");
          return false;
        }
      }
      this.getHttp();
      return true;
    },
    getHttp() {
      if (!this.isSubmit) {
        $.toast("该工单已经提交,请勿重复提交", "text");
        return;
      }
      if (this.$refs.imgs.imgLocalIds.length != this.imagesParams.length) {
        $.toast("请等待图片上传完毕", "text");
        return;
      }
      this.isSubmit = false;
      $.showLoading("提交中…");
      let sourcesFloorName, sourcesFloor;
      // if (!this.staffInfo || this.staffInfo.gridList.length == 0) {
      //   sourcesFloorName = "";
      //   sourcesFloor = "";
      // } else {
      //   let params = this.staffInfo.gridList[0];
      //   sourcesFloorName =
      //     params.parentGridName.replace(">", "") +
      //     this.staffInfo.gridList[0].gridName;
      //   sourcesFloor = this.staffInfo.gridList[0].allParentId;
      // }
      let params = {
        hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
        unitCode: this.loginInfo.userOffice[0].unitCode,
        deptCode: "",
        userId: this.loginInfo.id,
        phoneNumber: this.loginInfo.phone,
        realName: this.loginInfo.staffName,
        workTypeCode: this.workTypeCode,
        deptName: this.staffInfo.officeName,
        questionDescription: this.describe,
        urgencyDegreeCode: this.$refs.urgent.urgent,
        jobNumber: this.staffInfo.staffNumber,
        accessToken: this.accessToken,
        type: this.loginInfo ? this.loginInfo.type : 1,
        job: this.staffInfo.administrativePost,
        appointmentType: this.loginInfo ? (this.reservation ? "1" : "0") : 0,
        appointmentDate: this.loginInfo
          ? this.reservation
            ? this.$refs.serviceHours.reservation
            : ""
          : "",
        localtionName: this.localtionName,
        localtion: this.localtion,
        itemDetailCode: this.reviseServiceMatters.itemDetailCode || "",
        itemDetailName: this.reviseServiceMatters.itemDetailName || "",
        itemServiceCode: this.reviseServiceMatters.itemServiceCode || "",
        itemServiceName: this.reviseServiceMatters.itemServiceName || "",
        itemTypeCode: this.reviseServiceMatters.itemTypeCode || "",
        itemTypeName: this.reviseServiceMatters.itemTypeName || "",
        sourcesFloorName: sourcesFloorName,
        sourcesFloor: sourcesFloor,
        repairWork: this.$refs.isRework.isRework,
        staffId: this.staffInfo.staffId,
        transportNum: this.reviseNum,
        typeSources: this.$refs.attribute.attr,
        workSources: "1", //巡检为3,其余为1
        dispatchingConfig: this.requireCode, //	移动申报自动派工标识
        realName: this.receiptName //送水签收人
      };
      if (this.localtionPlaceCode) {
        params.localtionName = this.localtionName + this.localtionPlaceName;
        if (params.localtion.split(",").length == 3) {
          params.localtion = params.localtion + "," + this.localtionPlaceCode;
        }
      }

      params.callerTape = this.voiceParams;
      params.attachment = JSON.stringify(this.imagesParams);
      // console.log(params);
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat",
          this.$qs.stringify(params),
          {
            headers: {
              Authorization:
                "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          if (res.data.code == "200") {
            $.toast("工单提交成功", "success", () => {
              sessionStorage.removeItem("hospitalCode");
              sessionStorage.removeItem("unitCode");
              sessionStorage.removeItem("url");
            });
            window.location.href = process.env.WX + "/personalCenter";
          } else {
            $.toast(res.data.message, "text");
            if (res.data.code == 40001) {
              this.voiceParams = "";
              this.$refs.voices.handleDelVoiceClick();
            }
          }
          $.hideLoading();
        });
    }
  },
  components: {
    ServiceHours,
    UploadImage,
    UploadVoice,
    UrgentLevel,
    DeclareAttribute,
    IsRework,
    inputNumber
  },
  activated() {
    document.title = this.$route.query.workTypeName;
    //这里要做一个数据清理的工作
  }
};
</script>
<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper {
  height: 100%;
  background-color: $bgColor;

  .content {
    box-sizing: border-box;
    min-height: 100%;
    padding-bottom: 76px !important;
    background-color: $bgColor;

    .reservation-btns {
      marginBottom20();
      text-align: center;

      span {
        display: inline-block;
        width: 1.24rem;
        margin: 0 0.36rem;
      }

      .active {
        color: $color;
        border-bottom: 2px solid $color;
      }
    }

    .other-location-name {
      marginBottom20();

      .ipt-content {
        justify-content: flex-end;
        line-height: 1.2;
        text-align: left;
      }
    }

    .adjustable {
      padding: 0 0.32rem;

      > div {
        text-align: center;
      }

      img {
        width: 0.72rem;
      }

      .down {
        transition: all 0.3s;
      }

      .up {
        transform: rotateZ(180deg);
        transition: all 0.3s;
      }
    }

    .up-content {
      transform: rotateX(90deg);
      transform-origin: center top;
      height: 0;
      transition: height 0.3s;
      transition: all 0.3s;
    }

    .down-content {
      transition: all 0.3s;
      transform-origin: center top;
      margin-bottom: 10px;
    }

    .er-level {
      display: flex;
      align-items: center;
      itemBaseStyle();

      .weui-cells_checkbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .redio-content {
          display: flex;
          color: $contentColor;

          .declare-attr {
            padding: 0;
          }
        }
      }
    }

    .hosp {
      marginBottom20();
    }

    .item-style {
      itemBaseStyle();

      .login-tips {
        line-height: 1;
        white-space: nowrap;
        font-size: 0.28rem;
        font-style: italic;
        color: $color;
      }
    }

    .matter-style {
      min-height: 0.98rem;
      line-height: initial;
    }

    .desc-form {
      margin-top: 0;
      background-color: #fff;

      .desc {
        padding: 0.32rem 0.32rem 0;
        font-size: 0.28rem;
        line-height: 1.5em;
        margin-bottom: 0.2rem;
        color: $contentColor;

        textarea {
          display: inline-block;
          text-indent: 2em;
          font-size: 0.3rem;
        }
      }
    }

    .tips {
      color: $contentColor;
      text-align: center;
      margin: 0.6rem 0;

      .key-tips {
        color: $color;
      }
    }
  }
}

.btn {
  padding: 0 1.12rem 0.3rem;
  position: relative;
  user-select: none;

  .delete-voice {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.5rem;
  }

  .play-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
    }
  }
}

.recording {
  position: fixed;
  z-index: 2;
  top: 2rem;
  left: 50%;
  margin-left: -1.2rem;

  img {
    width: 2.4rem;
    opacity: 0.7;
  }
}

.title-content {
  itemBaseStyle();
  margin-top: $marginb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ipt-content {
  text-align: right;
  display: flex;
  align-items: center;
  color: #999;
}

.matter-ipt {
  display: flex;
  justify-content: flex-end;

  .ipt-pos {
    position: relative;
    width: 100%;

    .matterInput {
      display: none;
    }

    span {
      font-family: serif;
      text-align: left;
      display: flex;
      font-size: 15px;
    }
  }
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;

  .img-wrapper {
    flex: 1;
    width: 0;
    margin: $marginb;
    position: relative;

    img {
      width: 100%;
    }

    .icon-img {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
}

.popups-content {
  width: 80%;
  margin: 0 auto;
  position: fixed;
  max-width: 300px;
  border-radius: 3px;
  overflow: hidden;

  & > div {
    margin: 0 auto;
    background: #fff;
    padding-bottom: 15px;
    border-bottom: 1px solid #efefef;
    height: 1.18rem;
    align-items: flex-end;
    box-sizing: border-box;
  }

  .cfm-phone-num-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 48px;
    font-size: 18px;
    padding: 0;
    color: #38C7C4;
  }
}

.play-void-img {
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
}

.title {
  font-size: 0.32rem;
}

.er-level-redio {
  display: flex;
  background-color: #fff;
  font-size: 0.3rem;
  color: $contentColor;
}

.redio-content {
  position: relative;

  .select-time {
    position: absolute;
    left: 1rem;
    bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 5em);

    .text {
      width: 50px;
      line-height: initial;
    }

    .ipt {
      flex: 1;
      text-align: right;
    }

  }

  .textColor {
    color: #999;
  }

  .disPointer {
    pointer-events: none;
  }
}

.hint {
  display: inline-block;
  margin: 0 20px;
}

.reminder{
  height:0.68rem;
  background:#FAECE9;
  padding-left:.32rem;
  line-height:.68rem;
  .reminder-img{
    width:.28rem;
  }
  .reminder-text{
    margin-left:.24rem;
    color:#FF7859;
    font-size:.26rem;
  }

}

.ellipsis{
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
