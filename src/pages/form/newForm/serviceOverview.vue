<template>
  <div class="inner">
    <Header title="服务总览" @backFun="goBack"></Header>
    <div class="typeBer">
      <div v-for="(item, index) in btnOption" :key="index" class="typeBtn"
        :class="btnActive == index ? 'activeBtn' : ''" @click="changeBtnType(item)">
        {{ item.name }}
      </div>
      <van-calendar v-model="dateShow" type="range" @confirm="onConfirm" :min-date="new Date(1990, 0, 1)" />
    </div>
    <div class="content">
      <div v-show="echartData.length > 0" class="executeCharts" ref="executeCharts"></div>
      <div v-show="echartData.length <1 " class="executeCharts notCharts">
        <span class="nullText">暂无数据</span>
      </div>
      <div class="workOrderContent">
        <div class="dayWorkOrder">
          <div class="itemList" v-for="(item,index) in  workOrderInfoList" :key="index" @click="goList(item)">
            <p class="itemTop">
              <span class="count">{{item.value}}</span><span class="unit">{{item.unit}}</span>
            </p>
            <p class="itemBottom">
              <span class="label">{{item.label}}</span>
            </p>
          </div>
        </div>
        <div class="timeWorkOrder">
          <div class="itemList" v-for="(item,index) in  workOrderTimeList" :key="index">
            <p
              :class="['itemTop ', item.average == 'up' ? 'averageInner2' :item.average == 'down'? 'averageInner1':'']">
              <span class="count">{{item.value}}</span><span class="unit">{{item.unit}}</span>
            </p>
            <p class="itemBottom">
              <span class="label">{{item.label}}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
export default {
  components: {},
  data() {
    return {
      moment,
      btnActive: 0,
      dateType: "day",
      dateShow: false,//日期组件
      btnOption: [
        {
          id: 0,
          type: "day",
          name: "今日",
        },
        {
          id: 1,
          type: "week",
          name: "本周",
        },
        {
          id: 2,
          type: "month",
          name: "本月",
        },
        {
          id: 3,
          type: "year",
          name: "本年",
        },
        {
          id: 4,
          type: "custom",
          name: "自定义",
        },
      ],
      workOrderInfoList: [
        {
          label: '工单数量',
          value: 0,
          unit: "单",
        },
        {
          label: '未完工',
          value: 0,
          unit: "单",
        },
        {
          label: '已完工',
          value: 0,
          unit: "单",
        },
        {
          label: '完工满意度',
          value: 0,
          unit: "%",
        },
        {
          label: '未完工率',
          value: 0,
          unit: "%",
        },
        {
          label: '完工率',
          value: 0,
          unit: "%",
        },
      ],
      workOrderTimeList: [
        {
          label: '工单平均响应时长',
          value: '0秒',
        },
        {
          label: '同比平均响应时长',
          value: '0.00',
          unit: "%",
          average: 'down',
        },
        {
          label: '工单平均接单时长',
          value: '0秒',
        },
        {
          label: '同比平均接单时长',
          value: '0.00',
          unit: "%",
          average: 'down',
        },
        {
          label: '工单平均完工时长',
          value: '0秒',
        },
        {
          label: '同比平均完工时长',
          value: '0.00',
          unit: "%",
          average: 'down',
        },
      ],
      echartData: [],
      startTime: '',
      endTime: '',

    };
  },
  created() {
    this.startTime = this.moment().format('YYYY-MM-DD')
    this.endTime = this.moment().format('YYYY-MM-DD')
    this.getWorkOrderData();
    this.getChartData()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    changeBtnType(val) {
      this.btnActive = val.id;
      this.dateType = val.type;
      if (this.dateType === 'day') {
        this.startTime = this.moment().format('YYYY-MM-DD')
        this.endTime = this.moment().format('YYYY-MM-DD')
        this.getChartData()
        this.getWorkOrderData()
      } else if (this.dateType === 'week') {
        this.startTime = this.moment().weekday(1).format("YYYY-MM-DD")
        this.endTime = this.moment().weekday(7).format("YYYY-MM-DD")
        this.getChartData()
        this.getWorkOrderData()
      } else if (this.dateType === 'month') {
        this.startTime = this.moment().startOf("month").format("YYYY-MM-DD")
        this.endTime = this.moment().endOf('month').format("YYYY-MM-DD")
        this.getChartData()
        this.getWorkOrderData()
      } else if (this.dateType === 'year') {
        this.startTime = this.moment().startOf('year').format('YYYY-MM-DD')
        this.endTime = this.moment().endOf('year').format('YYYY-MM-DD')
        this.getChartData()
        this.getWorkOrderData()
      } else {
        this.dateShow = true;
        this.echartData = [];
        this.workOrderInfoList = [
          {
            label: '工单数量',
            value: 0,
            unit: "单",
          },
          {
            label: '未完工',
            value: 0,
            unit: "单",
          },
          {
            label: '已完工',
            value: 0,
            unit: "单",
          },
          {
            label: '完工满意度',
            value: 0,
            unit: "%",
          },
          {
            label: '未完工率',
            value: 0,
            unit: "%",
          },
          {
            label: '完工率',
            value: 0,
            unit: "%",
          },
        ]
        this.workOrderTimeList = [
          {
            label: '工单平均响应时长',
            value: '0秒',
          },
          {
            label: '同比平均响应时长',
            value: '0.00',
            unit: "%",
            average: 'down',
          },
          {
            label: '工单平均接单时长',
            value: '0秒',
          },
          {
            label: '同比平均接单时长',
            value: '0.00',
            unit: "%",
            average: 'down',
          },
          {
            label: '工单平均完工时长',
            value: '0秒',
          },
          {
            label: '同比平均完工时长',
            value: '0.00',
            unit: "%",
            average: 'down',
          },
        ]
      }
    },
    // 日期确认
    onConfirm(date) {
      this.startTime = this.moment(date[0]).format('YYYY-MM-DD')
      this.endTime = this.moment(date[1]).format('YYYY-MM-DD')
      this.getChartData()
      this.getWorkOrderData()
      this.dateShow = false;
    },
    getChartData() {
      let params = {
        startTime: `${this.startTime} 00:00:00`,
        endTime: `${this.endTime} 23:59:59`,
      };
      this.$toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0,
      });
      this.$api.getServiceStatusData(params).then((res) => {
        this.$toast.clear();
        if (res.length) {
          this.echartData = res;
          if (this.echartData && this.echartData.length) {
            this.$nextTick(() => {
              this.getPieData(this.echartData)
            });
          }
        } else {
          this.echartData = []
          // const getchart = this.$echarts.init(this.$refs.executeCharts);
          // getchart.clear();
        }
      });
    },
    getPieData(arr) {
      const getchart = this.$echarts.init(this.$refs.executeCharts);
      const nameList = Array.from(arr, item => item.label);
      const valueList = Array.from(arr, item =>
        item.value ? item.value.toFixed(2) : 0
      );
      const percentageList = Array.from(arr, item => item.percentage);
      const data = [];
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i],
          percentage: percentageList[i]
        });
      }
      getchart.clear();
      getchart.setOption({
        backgroundColor: "#fff",
        color: ['#3562DB', '#985EE1', '#FF6461', '#A0B8F6', '#08CB83', '#FF9435'],
        legend: {
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemWidth: 10,
          itemHeight: 10,
          top: "15%",
          bottom: '10%',
          right: "4%",
          formatter: function (name) {
            const res = data.filter(n => { return n.name === name })
            if (!res.length) return
            return `${name}  ${res[0].value} 单  ${res[0].percentage}`
          },
          textStyle: {
            fontSize: 13,
            color: '#4E5969',
          },
        },
        tooltip: {
          trigger: 'item',
          // 关闭tooltip指示线
          axisPointer: {
            type: 'none'
          }
        },
        title: {
          text: "工单状态统计",
          left: "3%",
          top: '3%',
          textStyle: {
            fontSize: 14,
            color: "#1D2129",
          },
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2,
            normal: {
              label: {
                show: false   //隐藏文字
              },
              labelLine: {
                show: false   //隐藏指示线
              }
            },
          },
          type: "pie",
          radius: "60%",
          center: ["22%", "58%"],
          data: data,
          hoverAnimation: false,
          labelLine: {
            show: false,
            normal: {
              lineStyle: {
                color: "#4E5969" // 设置标示线的颜色
              }
            }
          },
          label: {
            normal: {
              textStyle: {
                color: "#4E5969"
              }
            }
          }
        }
      });
    },
    goBack() {
      if (this.$route.query.from == "workOrder") {
        this.$router.go(-1);
      } else {
        // api.closeFrame();
        this.$YBS.apiCloudCloseFrame();
      }
    },
    goList(item) {
      if (this.dateType === 'day' || this.dateType === 'month') {
        if (item.label === '工单数量') {
          this.$router.push({
            path: "/workOrderList",
            query: {
              type: this.dateType === 'day' ? 'day' : this.dateType === 'month' ? 'monts' : '',
              from: "qualityAnalysis"
            }
          });
        }
      }
    },
    getWorkOrderData() {
      let params = {
        startTime: `${this.startTime} 00:00:00`,
        endTime: `${this.endTime} 23:59:59`,
      };
      this.$api.getServiceInfoData(params).then(res => {
        this.workOrderInfoList.find(i => i.label == '工单数量').value = res.allCount;
        this.workOrderInfoList.find(i => i.label == '未完工').value = res.unfinishedCount;
        this.workOrderInfoList.find(i => i.label == '已完工').value = res.finishedCount;
        this.workOrderInfoList.find(i => i.label == '完工满意度').value = res.evaluatePercentage;
        this.workOrderInfoList.find(i => i.label == '未完工率').value = res.unfinishedPercentage;
        this.workOrderInfoList.find(i => i.label == '完工率').value = res.finishedPercentage;
        this.workOrderTimeList.find(i => i.label == '工单平均响应时长').value = res.responseTime;
        this.workOrderTimeList.find(i => i.label == '同比平均响应时长').value = res.responseTimeRate;
        this.workOrderTimeList.find(i => i.label == '工单平均完工时长').value = res.finishTime;
        this.workOrderTimeList.find(i => i.label == '同比平均完工时长').value = res.finishTimeRate;

        this.workOrderTimeList.find(i => i.label == '工单平均接单时长').value = res.avgOrderReceivingDate;
        this.workOrderTimeList.find(i => i.label == '同比平均接单时长').value = res.orderReceivingRate;

        if (res.responseTimeRate.indexOf('-') === -1 && res.responseTimeRate !== '0.00') {
          this.workOrderTimeList.find(i => i.label == '同比平均响应时长').average = 'up'
        } else {
          this.workOrderTimeList.find(i => i.label == '同比平均响应时长').average = 'down'
        }
        if (res.finishTimeRate.indexOf('-') === -1 && res.finishTimeRate !== '0.00') {
          this.workOrderTimeList.find(i => i.label == '同比平均完工时长').average = 'up'
        } else {
          this.workOrderTimeList.find(i => i.label == '同比平均完工时长').average = 'down'
        }
        if (res.orderReceivingRate.indexOf('-') === -1 && res.orderReceivingRate !== '0.00') {
          this.workOrderTimeList.find(i => i.label == '同比平均接单时长').average = 'up'
        } else {
          this.workOrderTimeList.find(i => i.label == '同比平均接单时长').average = 'down'
        }
      });
    },
  }
};
</script>
<style lang="scss" scoped>
.inner {
  min-height: 100vh;
  background-color: #f2f4f9;
  .typeBer {
    padding: 0.1rem 0.12rem 0 0.12rem;
    height: 0.72rem;
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .typeBtn {
      width: calc(100% / 5);
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4e5969;
    }
    .activeBtn {
      background-color: #e6effc;
      color: #3562db;
    }
  }
  .content {
    padding: 10px;
    .executeCharts {
      height: 28vh;
      text-align: center;
      position: relative;
      .nullText {
        position: absolute;
        bottom: 50%;
        left: calc(50% - 0.4rem);
      }
    }
    .workOrderContent {
      margin-top: 10px;
      border-radius: 8px;
      background-color: #fff;
      .itemTop {
        .count {
          font-size: 18px;
          color: #1d2129;
          font-weight: bold;
        }
        .unit {
          color: #86909c;
          font-size: 13px;
          margin-left: 4px;
        }
      }
      .itemBottom {
        margin-top: 10px;
        .label {
          color: #4e5969;
          font-size: 14px;
        }
      }
      .dayWorkOrder {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .itemList {
          width: calc(100% / 3);
          text-align: center;
          padding: 24px 0;
        }
        border-bottom: 1px solid #e5e6eb;
      }
      .timeWorkOrder {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
        .itemList {
          width: calc(100% / 2);
          text-align: center;
          padding: 24px 0;
        }
      }
    }
  }
}
/deep/ .averageInner2 {
  .count {
    color: #f53f3f !important;
  }
  .unit {
    color: #f53f3f !important;
  }
}
/deep/.averageInner1 {
  .count {
    color: #00b42a !important;
  }
  .unit {
    color: #00b42a !important;
  }
}
</style>
