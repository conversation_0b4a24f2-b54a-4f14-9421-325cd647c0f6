<template>
  <div class="scanning-repair-wrapper">
    <!--服务区域-->
    <div class="content">
      <div class="weui-cell border-rightbottom item-style">
        <div class="weui-cell__hd"><label class="weui-label title">服务区域</label></div>
        <div class="weui-cell__bd ipt-content">
          <input
              class="weui-input ipt"
              type="text"
              readonly
              placeholder="请选择服务区域"
          >
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <!--服务地点-->
      <div class="weui-cell border-rightbottom item-style">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务地点</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input
              class="weui-input ipt"
              type="text"
              readonly
              placeholder="请选择服务地点"
          >
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <!--服务事项-->
      <div class="weui-cell border-rightbottom item-style matter-style" @click="goToSelectMatterlists">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt">
          <div class="ipt-pos">
            <input
                class="weui-input ipt"
                type="text"
                v-model="matterlistsState"
                :class="{matterInput:matterlistsState}"
                readonly
                placeholder="请选择服务事项"
            >
            <span v-text="matterlists"></span>
          </div>
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
    </div>
    <div class="submit-btn">
      <button class="weui-btn weui-btn_primary" @click="submit">提交
      </button>
    </div>
    <div class="tips">
      <p class="title">当前报修方式属于简便报修，一下功能不能使用：</p>
      <p>1、语音上传</p>
      <p>2、图片上传</p>
      <p>3、预约报修等功能</p>
    </div>
    <div class="more-services">
      <p>如需使用更多服务，请关注下方公众号</p>
      <img src="~images/ybs.png" alt="">
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import wx from "weixin-js-sdk"
  export default {
    name: "ScanningRepair",
    data () {
      return {
        matterlistsObj:{},//选择的服务事项
        matterlists:'',//选择的服务事项内容拼接，用于展示
        matterlistsState:'',//服务事项展示（ipt)
      }
    },
    methods: {
      /**
       * 跳转选择服务事项页面
       */
      goToSelectMatterlists () {
        this.$router.push({
          name: 'ServiceMatters',
          params: {
            workTypeCode: '1'
          }
        })
      },
      submit () {
        $.modal({
          title: "报修成功！",
          text: "是否关注公众号，使用更多服务！",
          buttons: [
            { text: "否", className: "default", onClick: function(){
              wx.closeWindow()
            } },
            { text: "是", onClick: function(){
              location.href="https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz=MzU0NTg2MDYxMw==&scene=126&bizpsid=0#wechat_redirect"
            } },
          ]
        });
      }
    },
    activated() {
      let getMatterlistsO = this.$route.query.matterlistsObj
      if (getMatterlistsO) {
        this.matterlistsObj = getMatterlistsO
        if (getMatterlistsO.itemTypeName) {
          this.matterlists = getMatterlistsO.itemTypeName + '/' + getMatterlistsO.itemDetailName + '/' + getMatterlistsO.itemServiceName
        }
        this.matterlistsState = ' '
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
.scanning-repair-wrapper
  height: 100%
  background-color: $bgColor
  .content
    box-sizing: border-box
    .ipt-content
      text-align: right
      display: flex
      align-items: center
      color: #999
    .er-level
      display: flex
      align-items: center
      margin-bottom: $marginb
      itemBaseStyle()
      .weui-cells_checkbox
        display: flex
        align-items: center
        justify-content: center
        justify-content: space-between
        width: 100%
        .redio-content
          display: flex
          color: $contentColor
          .declare-attr
            padding: 0
    .matter-ipt
      display: flex
      justify-content: flex-end
      .ipt-pos
       position: relative
       .matterInput
         display: none
       span
         font-family: serif
         text-align: left
         display: flex
    .item-style
      itemBaseStyle()
      .ipt
        text-align: right
        color: $contentColor
    .matter-style
      min-height: 0.98rem
      line-height: initial
  .submit-btn
    padding: 0.42rem 0.32rem 0 .32rem
    margin-bottom: 1.6rem
    width: 100%
    box-sizing: border-box
    height: 66px
  .tips
    color: $contentColor
    line-height: 1.5em
    padding-left: .33rem
    .title
      line-height: 3em
  .more-services
    text-align: center
    color: $contentColor
    position: fixed
    bottom: .9rem
    width: 100%
    img
      width: 1.85rem
      margin-top: .35rem
</style>
