<template>
  <div class="inner">
    <Header title="我的审批" @backFun="goBack"></Header>
    <van-tabs @click="tabClick" style="height: 40px" :ellipsis="false">
      <van-tab v-for="item in tabList" :name="item.id" :title="item.name" :key="item.id" />
    </van-tabs>
    <van-search v-model="queryInfo" background="#fff" clearable placeholder="搜索工单号,发起人" @input="searchList"> </van-search>
    <div v-if="approvalList.length > 0" class="approvalList">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width: 100%">
            <div v-for="item in approvalList" :key="item.id" class="listStyle">
              <div @click="goDetails(item)">
                <div class="name">
                  <div>挂单申请</div>
                  <div class="state">
                    <span :class="['approvalStatus', `approvalStatus${item.approvalStatus}`]" v-if="approvalTabStatus == 2"> {{ approvalType(item.approvalStatus) }}</span>
                    <span><van-icon name="arrow" /></span>
                  </div>
                </div>
                <div class="txt">
                  <div class="txt_l">申请编号</div>
                  <div class="txt_r">{{ item.applicationNumber }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">发起人</div>
                  <div class="txt_r">{{ item.creatorName }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">发起时间</div>
                  <div class="txt_r">{{ item.startTime }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">工单号</div>
                  <div class="txt_r">{{ item.businessId }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">挂单说明</div>
                  <div class="txt_r">{{ item.disEntryOrdersReason }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">解决说明</div>
                  <div class="txt_r">{{ item.disEntryOrdersSolution }}</div>
                </div>
                <div class="txt">
                  <div class="txt_l">预计解决时间</div>
                  <div class="txt_r">{{ item.disPlanSolutionTime }}</div>
                </div>
              </div>
              <div class="btn" v-if="approvalTabStatus == 1">
                <van-button style="width: 40%" color="#ffece7" size="small" @click="approve(2, item)"
                  ><span style="color: #e94e48"><van-icon name="cross" />&nbsp;驳回</span></van-button
                >
                <van-button style="width: 40%" color="#e6effc" size="small" @click="approve(1, item)"
                  ><span style="color: #3d67c1"><van-icon name="success" />&nbsp;通过</span></van-button
                >
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      queryInfo: "",
      approvalTabStatus: 1,
      approvalList: [],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      pageSize: 4,
      total: 0,
      tabList: [
        {
          id: 1,
          name: "待办"
        },
        {
          id: 2,
          name: "已办"
        }
      ]
    };
  },
  mounted() {
    this.getList();
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    approve(type, row) {
      this.$router.push({
        path: "/BillApproval",
        query: {
          type: type,
          id: row.approvalControlId
        }
      });
    },
    searchList() {
      this.finished = false;
      this.loading = true;
      this.approvalList = [];
      this.current = 1;
      this.getList();
    },
    getList() {
      let params = {
        pageSize: this.pageSize,
        currentPage: this.current,
        queryInfo: this.queryInfo,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        type: this.approvalTabStatus
      };
      this.$api.queryApprovalList(params).then(res => {
        console.log(res, "===========");
        this.loading = false;
        res.responseList.forEach(item => {
          this.approvalList.push(item);
        });
        if (this.approvalList.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    goDetails(row) {
      this.$router.push({
        path: "/approvalDetails",
        query: {
          id: row.approvalControlId
        }
      });
    },
    approvalType(approvalState) {
      const approvalStatusMap = {
        0: "审批中",
        1: "已通过",
        2: "已驳回",
        3: "已取消"
      };
      return approvalStatusMap[approvalState];
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.approvalList = [];

      this.getList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.current++;

      this.getList();
    },
    tabClick(val) {
      this.approvalTabStatus = val;
      this.approvalList = [];
      this.getList();
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style  scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
  font-size: 16px;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
    }
    .state {
      .approvalStatus {
        padding: 2px 8px;
        border-radius: 4px;
      }
      .approvalStatus0 {
        background: #e6effc;
        color: #3562db;
      }
      .approvalStatus1 {
        background: #e8ffea;
        color: #00b42a;
      }

      .approvalStatus2 {
        background: #ffece8;
        color: #f53f3f;
      }
      .approvalStatus3 {
        background: #f2f3f5;
        color: #4e5969;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .txt_l {
        margin: auto 0;
        width: 100px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
    }
  }
}

.approvalList {
  height: calc(100% - 120px);
  overflow: auto;
}
.notList {
  position: relative;
  height: calc(100% - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
/deep/ .van-tabs__line {
  background-color: #3562db;
}
.btn {
  border-top: 1px solid #eee;
  padding: 5px 0;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
}
</style>
