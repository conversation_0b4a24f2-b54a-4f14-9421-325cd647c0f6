<template>
  <div class="content">
    <Header title="通过" @backFun="goBack"></Header>
    <star-page ref="stars" style="margin-top:0.15rem"></star-page>
    <div v-if="isShowIdeas" class="form-box">
      <van-field class="star" readonly clickable label="选择意见" :value="evaluationAdvice" placeholder="选择意见" @click="showPicker = true" />
      <van-field
        v-model="evaluationExplain"
        rows="2"
        autosize
        label="评价描述"
        type="textarea"
        maxlength="50"
        placeholder=""
        show-word-limit
      />
    </div>
    <van-popup v-model="showPicker" round position="bottom">
      <van-picker show-toolbar :columns="columns" @cancel="showPicker = false" @confirm="onConfirm" />
    </van-popup>
    <signature-page :key="componentKey" @saveImg="handleSign" :actualArr="actualArr" :workNum="workNum" :sourcesDeptName="sourcesDeptName" :operationSignatureOrderInfo="operationSignatureOrderInfo"></signature-page>
  </div>
</template>
<script>
import StarPage from "@/pages/form/newForm/components/Star";
import SignaturePage from "@/pages/form/formDetails/components/Signature";
import { mapState } from "vuex";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  mixins: [fontSizeMixin],
  components: {
    StarPage,
    SignaturePage
  },
  data() {
    return {
      signImg: "",
      starNum: "",
      disDegreeText: "",
      configParams: {},
      isShowIdeas: "",
      evaluationAdvice: "",
      evaluationExplain: "",
      showPicker: false,
      columns: [],
      componentKey: 0,
      actualArr: [],
      workNum: '',
      sourcesDeptName: '',
      operationSignatureOrderInfo: ''
    };
  },
  watch: {
    evaluationAdvice: function(newVal, oldVal) {
      if (newVal == "其他") {
        this.reloadComponent();
      }
    }
  },
  created() {
    // 滚动到页面顶部
    window.scrollTo(0, 0);
  },
  mounted() {
    this.getConfig();
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getTaskDetail();
    this.$watch(
      () => this.$refs.stars.starIdx,
      (newVal, oldVal) => {
        // 在这里处理 starIdx 的变化
        console.log("starIdx 变化了：", newVal);
        if (this.configParams.satisfactionConfigScore * newVal <= this.configParams.satisfactionConfigLimit) {
          this.isShowIdeas = true;
          this.reloadComponent();
        } else {
          this.isShowIdeas = true;
        }
      }
    );
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  methods: {
    getTaskDetail() {
      this.$api.getTaskDetail({taskId: this.$route.query.id}).then(res => {
        this.workNum = res[0].workNum
        this.sourcesDeptName = res[0].sourcesDeptName
        let actual
        res.forEach(item => {
          if (item.operationCode == '6') {
            actual = item.actual
          }
        })
        console.log('actual', actual);
        let actualArr = []
        actual.forEach(item => {
          actualArr.push(item.depThreeTypeName + '*' + item.num)
        })
        this.actualArr = actualArr
        this.reloadComponent()
      });
    },
    reloadComponent() {
      this.componentKey++;
    },
    onConfirm(value) {
      this.evaluationAdvice = value.text;
      this.showPicker = false;
    },
    getConfig() {
      this.$api.getNewSysConfigParam({}).then(res => {
        this.configParams = res.olgWorkPushNew;
        this.operationSignatureOrderInfo = JSON.parse(res.configBody).operationSignatureOrderInfo;
        this.columns = JSON.parse(res.olgWorkPushNew.adviceDict)
          .filter(item => item.isChecked)
          .map(item => {
            return {
              text: item.typeName,
              value: item.typeCode
            };
          });
        // 给this.columns最后面加{text:'其他',value:'other'}
        this.columns.push({ text: "其他", value: "other" });
        if (
          this.configParams.satisfactionConfigScore * this.$refs.stars.starIdx != 0 &&
          this.configParams.satisfactionConfigScore * this.$refs.stars.starIdx <= this.configParams.satisfactionConfigLimit
        ) {
          this.isShowIdeas = true;
          this.reloadComponent();
        } else {
          this.isShowIdeas = false;
        }
      });
    },
    handleSign(img) {
      let starCount = this.$refs.stars.starIdx;
      if (starCount == "") {
        this.starNum = "";
      } else {
        this.starNum = +starCount;
      }
      this.signImg = img;
      switch (this.starNum) {
        case 1:
          this.disDegreeText = "非常差";
          break;
        case 2:
          this.disDegreeText = "差";
          break;
        case 3:
          this.disDegreeText = "一般";
          break;
        case 4:
          this.disDegreeText = "满意";
          break;
        case 5:
          this.disDegreeText = "非常满意";
          break;
      }
      this.handleSubmit();
    },
    goBack() {
      this.$router.go(-1);
    },
    handleSubmit() {
       if (this.starNum==0) {
        $.toast("请进行满意度评价", "text");
        return;
      }
      if (this.isShowIdeas && !this.evaluationAdvice) {
        $.toast("请选择意见", "text");
        return;
      }
      this.$api
        .checkAndAccept({
          id: this.$route.query.id,
          userId: this.loginInfo.staffId,
          acceptedScore: this.starNum,
          imgBase: this.signImg,
          evaluationAdvice: this.evaluationAdvice,
          evaluationExplain: this.evaluationExplain
        })
        .then(res => {
          $.toast("验收成功!", "success");
          setTimeout(() => {
            this.$router.go(-1);
          }, 1000);
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
}
.star {
  position: relative;
}
.star::before {
  content: "*";
  color: red;
  position: absolute;
  top: 12px;
  left: 8px;
}
/deep/ .van-field__label > span {
  font-size: 16px;
  color: #353535;
}
// 设置placeholder的字体大小
/deep/ .van-field__control::placeholder {
  font-size: 16px;
}
/deep/ .van-field__control {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .van-field__control::placeholder {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .van-field__value {
  padding-left: 35px;
}
.van-cell::after {
  display: none;
}
/deep/ .form-box .van-field .van-field__label span {
  font-size: calc(16px * var(--font-scale))!important;
  min-width: 100px;
}
/deep/ .form-box .van-field__label {
  min-width: 100px;
}
/deep/ .van-picker .van-picker__toolbar > button  {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .van-picker .van-ellipsis {
  font-size: calc(16px * var(--font-scale))!important;
}
</style>
