<template>
  <div class="wrapper">
    <Header title="工单管理" @backFun="goback"></Header>
    <div class="content">
      <div class="card-item" v-for="item in filterMenuList" :key="item.label" @click="goPage(item.url)">
        <van-badge :content="item.count" max="99">
          <img :src="item.icon" />
        </van-badge>
        <span>{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "orderIndex",
  data() {
    return {
      timer: "",
      menuList: [
        {
          icon: require("@/assets/images/orderIndex_3.png"),
          label: "我的服务",
          url: "myorder?leaguerType=1&type=2",
          count: "",
          countForKey: "allOrder",
          pathUrl: "/ihcrsYBSApp/orderIndexMyorder",
        },
        {
          icon: require("@/assets/images/orderIndex_2.png"),
          label: "工单处理",
          url: "workbench?type=3&workTypeCode=&leaguerType=1",
          count: "",
          countForKey: "allTeamPersonOrder",
          pathUrl: "/ihcrsYBSApp/orderIndexWorkbench",
        },
        {
          icon: require("@/assets/images/orderIndex_1.png"),
          label: "工单验收",
          url: "orderAcceptance",
          count: "",
          countForKey: "acceptanceNum",
          pathUrl: "/ihcrsYBSApp/orderIndexOrderAcceptance",
        },
        // 新增工单新建
        {
          icon: require("@/assets/images/orderIndex_4.png"),
          label: "工单新建",
          url: "newOrder",
          count: "",
          pathUrl: "/ihcrsYBSApp/orderIndexNewOrder",
        },
          // 新增工单新建
        {
          icon: require("@/assets/images/orderIndex_5.png"),
          label: "我的审批",
          url: "myBillApproval",
          count: "",
          pathUrl: "/ihcrsYBSApp/orderIndexMyBillApproval",
        },
        // {
        //   icon: require("@/assets/images/orderIndex_4.png"),
        //   label: "服务总览",
        //   url: "serviceOverview?from=workOrder",
        //   count: "",
        //   pathUrl: "/ihcrsYBSApp/orderIndex",
        // }
      ],
      filterMenuList: []
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"]),
  },
  methods: {
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
    goPage(url) {
      if (!url) return;
      this.$router.push({ path: url });
    },
    // 获取菜单权限
    getMenuAuth() {
      const params = {
        state: "0",
        userId: this.loginInfo.id,
      };
      this.$api.getMenuAuth(params).then((res) => {
        const authMenu = this.$YBS.treeToList(res || []);
          // .find((i) => i.menuId == 1000)
          // .children.find((k) => k.menuId == 1004)
          // .children.find((j) => j.menuId == 280).children;
        this.filterMenuList = this.menuList.filter((item) => {
          return authMenu.some((i) => i.pathUrl == item.pathUrl);
        });
        this.timer = setInterval(() => {
          if (this.staffInfo.staffId) {
            this.getCount();
            clearInterval(this.timer);
          }
        }, 5);
      });
    },
    getCount() {
      this.$api
        .getOrderCountByUserInfo({
          staffId: this.staffInfo.staffId,
          teamPersonId: this.staffInfo.teamPersonId,
        })
        .then((res) => {
          const { map } = res;
          this.filterMenuList.forEach((item) => {
            if (item.countForKey) {
              item.count = map[item.countForKey];
            }
          });
          // this.menuList[0].count = map.allOrder;
          // this.menuList[1].count = map.allTeamPersonOrder;
          // this.menuList[2].count = map.acceptanceNum;
        });
    },
  },
  mounted() {
    this.getMenuAuth();
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  background-color: #f2f4f9;
  height: 100vh;
}
.content {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  padding: 6px;
}
.card-item {
  width: 29vw;
  height: 32vw;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: calc((100% - 29vw * 3) / 3 / 2);
  // margin-bottom: 16px;
}
.card-item img {
  width: 40px;
}
.card-item span {
  transform: translateY(12px);
}
</style>
