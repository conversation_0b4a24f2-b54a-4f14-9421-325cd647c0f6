<template>
  <div class="wrapper">
    <div class="content">
      <!--紧急程度-->
      <div class="er-level">
        <div class="weui-cells_checkbox">
          <div class="er-text title">紧急程度</div>
          <div class="redio-content">
            <label class="weui-cell weui-check__label" for="s11">
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  class="weui-check"
                  name="checkbox1"
                  id="s11"
                  checked="checked"
                  value="1"
                  v-model="urgent"
                />
                <i class="weui-icon-checked"></i>
              </div>
              <div class="weui-cell__bd">
                一般
              </div>
            </label>
            <label class="weui-cell weui-check__label" for="s12">
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  name="checkbox1"
                  class="weui-check"
                  id="s12"
                  value="0"
                  v-model="urgent"
                />
                <i class="weui-icon-checked"></i>
              </div>
              <div class="weui-cell__bd">
                紧急
              </div>
            </label>
          </div>
        </div>
      </div>
      <!--其他项目只展示，不能选择-->
      <div v-if="otherProject == 'ipms'" class="other-location-name">
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务地点</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            {{ localtionName }}
          </div>
        </div>
      </div>
      <!--服务区域-->
      <div v-if="otherProject == ''">
        <div class="weui-cell border-rightbottom item-style">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务区域</label>
          </div>
          <div class="weui-cell__bd ipt-content" @click="toSelectArea">
            <input
              class="weui-input ipt"
              type="text"
              readonly
              v-model="areaVal"
              placeholder="请选择服务区域"
            />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>
        <div class="weui-cell border-rightbottom item-style site">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务地点</label>
          </div>
          <div
            class="weui-cell__bd ipt-content"
            @click="toSelectSite"
            v-if="isExist"
          >
            <input
              class="weui-input ipt"
              type="text"
              readonly
              v-model="localtionPlaceName"
              placeholder="请选择服务地点"
            />
            <span class="iconfont arrow">&#xe646;</span>
          </div>
        </div>
      </div>
      <!--服务事项-->
      <div
        class="weui-cell border-rightbottom item-style matter-style"
        @click="goToSelectMatterlists"
      >
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt">
          <div class="ipt-pos">
            <input
              class="weui-input ipt ellipsis"
              type="text"
              v-model="matterlistsState"
              :class="{ matterInput: matterlistsState }"
              readonly
              placeholder="请选择服务事项"
            />
            <span v-text="matterlists"></span>
          </div>
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <!--服务时间-->
      <div class="weui-cells_form service-time border-rightbottom">
        <div class="item-style border-bottom">
          <div class="er-text title">服务时间</div>
        </div>
        <div class="weui-cells_checkbox">
          <div class="redio-content">
            <label class="weui-cell weui-check__label item-style" for="s13">
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  class="weui-check"
                  name="checkbox2"
                  id="s13"
                  checked="checked"
                  value="0"
                  v-model="time"
                />
                <i class="weui-icon-checked"></i>
              </div>
              <div class="weui-cell__bd" :class="{ textColor: !timeType }">
                随时
              </div>
            </label>
            <label class="weui-cell weui-check__label item-style" for="s14">
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  name="checkbox2"
                  class="weui-check"
                  id="s14"
                  value="1"
                  v-model="time"
                />
                <i class="weui-icon-checked"></i>
              </div>
            </label>
            <div class="weui-cell__bd select-time">
              <div class="text" :class="{ textColor: timeType }">预约</div>
              <input
                class="weui-input ipt"
                type="text"
                ref="datetime"
                v-model="reservation"
                readonly
                :class="{ textColor: timeType, disPointer: first }"
              />
            </div>
          </div>
        </div>
      </div>
      <!--申报属性-->
      <div class="er-level border-rightbottom">
        <div class="weui-cells_checkbox">
          <div class="er-text title">申报属性</div>
          <div class="redio-content">
            <label class="weui-cell weui-check__label declare-attr" for="yw">
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  class="weui-check"
                  name="checkboxsx"
                  id="yw"
                  checked="checked"
                  value="1"
                  v-model="attr"
                />
                <i class="weui-icon-checked"></i>
              </div>
              <div class="weui-cell__bd">医务报修</div>
            </label>
            <label class="weui-cell weui-check__label declare-attr" for="ww">
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  name="checkboxsx"
                  class="weui-check"
                  id="ww"
                  value="2"
                  v-model="attr"
                />
                <i class="weui-icon-checked"></i>
              </div>
              <div class="weui-cell__bd">外委巡查</div>
            </label>
          </div>
        </div>
      </div>
      <!--需联系电话-->
      <div class="weui-cell border-rightbottom item-style mb-css">
        <div class="weui-cell__hd">
          <label class="weui-label title">需联系电话</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input
            class="weui-input ipt"
            type="tel"
            v-model="phone"
            oninput="if(value.length>13)value=value.slice(0,13)"
            placeholder="请输入需联系人电话"
          />
        </div>
      </div>
      <!--是否返修-->
      <div class="er-level border-rightbottom">
        <div class="weui-cells_checkbox">
          <div class="er-text title">返修工单</div>
          <div class="redio-content">
            <label class="weui-cell weui-check__label" for="s15">
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  class="weui-check"
                  name="checkbox3"
                  id="s15"
                  checked="checked"
                  value="1"
                  v-model="isRework"
                />
                <i class="weui-icon-checked"></i>
              </div>
              <div class="weui-cell__bd">
                否
              </div>
            </label>
            <label class="weui-cell weui-check__label" for="s16">
              <div class="weui-cell__hd">
                <input
                  type="radio"
                  name="checkbox3"
                  class="weui-check"
                  id="s16"
                  value="2"
                  v-model="isRework"
                />
                <i class="weui-icon-checked"></i>
              </div>
              <div class="weui-cell__bd">
                是
              </div>
            </label>
          </div>
        </div>
      </div>
      <!--申报描述-->
      <div class="weui-cells weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea"
              placeholder="请输入您要报修的故障内容描述，字数在500字以内，或直接语音发布，限时60秒"
              maxlength="500"
              rows="5"
              v-model="value"
            ></textarea>
          </div>
        </div>
        <upload-voice ref="voices" @getvVoiceParams="getVoice"></upload-voice>
      </div>

      <upload-image
        v-bind:num="this.amount"
        style="position:relative"
        class="bottom"
        ref="imgs"
        @getImgParams="getImages"
      ></upload-image>
    </div>
    <div class="submit-btn" :class="{ active: canShow }">
      <button
        class="weui-btn weui-btn_primary"
        @click="submitAfterVrification"
        :disabled="disSubmit"
      >
        提交
      </button>
    </div>
    <linkage-select
      @getAreaVal="handleGetAreaVal"
      ref="linkageSelect"
    ></linkage-select>
  </div>
</template>

<script>
const wx = require("weixin-js-sdk");
import { mapState } from "vuex";
import { preView } from "@/common/lib/util.js";
import UploadImage from "@/common/uploadImg/uploadImg";
import UploadVoice from "@/common/uploadVoice/UploadVoice";
import LinkageSelect from "@/common/customize/LinkageSelect";
export default {
  name: "RepairWorkOrder",
  components: {
    UploadImage,
    UploadVoice,
    LinkageSelect
  },
  data() {
    return {
      canShow: false,
      value: "",
      phone: "",
      isExist: true,
      voiceParams: "",
      imagesParams: [],
      urgent: "1",
      attr: "1",
      isRework: "1",
      time: "0",
      reservation: "", //预约时间
      voiceFlag: false,
      imgFlag: false,
      accessToken: "",
      isOnlyVoice: false,
      timeType: true,
      text: "",
      areaVal: "",
      localtionName: "",
      localtion: "",
      localtionPlaceName: "",
      localtionPlaceCode: "",
      serviceSiteResId: "",
      allSiteDataInfo: [], //请求返回所有的相关地点的信息
      popData: {},
      first: true,
      matterlistsObj: {}, //服务事项
      matterlistsState: "", //服务事项(展示ipt)
      matterlists: "", //服务事项(展示)
      workId: "", //用于巡检
      otherProject: "", //是否为其他项目（仅调用该页面）
      disSubmit: false,
      amount: {
        //上传图片限制
        quantity: 3,
        capital: "三"
      }
    };
  },
  computed: {
    ...mapState([
      "loginInfo",
      "staffInfo",
      "changeLocation",
      "serviceAreaTreeData"
    ])
  },
  methods: {
    /**
     * 区域选择
     */
    toSelectArea() {
      //级联弹窗数据还原
      this.localtionPlaceName = "";
      this.$refs.linkageSelect.buildingData = [];
      this.$refs.linkageSelect.floorData = [];
      this.$refs.linkageSelect.resCount = 0;
      this.$refs.linkageSelect.isClick1 = true;
      this.$refs.linkageSelect.isClick2 = true;
      this.$refs.linkageSelect.curArea = "region";

      this.$refs.linkageSelect.show = true;
      this.$refs.linkageSelect.getLinkageRes("");
    },
    /**
     * 处理已选择的区域参数
     */
    handleGetAreaVal(params) {
      this.serviceSiteResId = params.code[2];
      this.areaVal = params.name.join("");
      this.localtionName = params.name.join("");
      this.localtion = params.code.join(",");
    },
    /**
     * 服务地点请求函数
     */
    toSelectSite() {
      if (this.serviceSiteResId == "") {
        $.toast("请先选择服务区域", "text");
      } else {
        var arr = this.serviceSiteResId.split(",");
        this.showVal(this.serviceAreaTreeData, arr[arr.length - 1]);
        // this.axios.get(process.env.API_BASE + '/hospitalController/cascadingQueryHospitalGridInfo', {
        //       params: {
        //         unitCode: this.loginInfo.userOffice[0].unitCode,
        //         hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
        //         gridId: arr[arr.length-1],
        //       }
        //     })
        //     .then(this.showVal)
      }
    },
    /**
     * 仅供服务地点展示，调起弹窗并设置相关参数
     */
    showVal(res, gridId) {
      // if (res.data.code == 200) {
      //   this.allSiteDataInfo = res.data.data
      // } else {
      //   $.toast(res.data.message, 'text')
      // }
      this.allSiteDataInfo = res.filter(item => {
        return item.parentId == gridId;
      });
      //将所有地点取出放到数组中
      let siteArr = [];
      for (let i = 0; i < this.allSiteDataInfo.length; i++) {
        siteArr.push(this.allSiteDataInfo[i]);
      }
      //跳到搜索页面
      this.$router.push({
        name: "Site",
        params: {
          list: siteArr
        }
      });
    },
    /**
     * 获取录音路径
     * @param params
     */
    getVoice(params) {
      this.voiceParams = params;
      if (this.isOnlyVoice) {
        this.getHttp();
      }
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    getImages(params) {
      this.imagesParams = params;
      //        this.getHttp()
    },
    /**
     * 处理提交按钮事件
     */
    handleSubmitClick() {
      if (this.detector.browser.name != "micromessenger") {
        this.getHttp();
      } else {
        if (
          this.$refs.imgs.imgServerId.length != 0 &&
          this.$refs.voices.hasVoice != "recorded"
        ) {
          //需上传图片、没有录音
          let imgLen = () => {
            if (this.imagesParams.length != 0) {
              this.getHttp();
            } else {
              setTimeout(() => {
                imgLen();
              }, 500);
            }
          };
          imgLen();
        } else if (
          this.$refs.imgs.imgServerId.length != 0 &&
          this.$refs.voices.hasVoice == "recorded"
        ) {
          //需上传图片、录音
          let imgLen = () => {
            if (this.imagesParams.length != 0) {
              this.isOnlyVoice = true;
              this.$refs.voices.saveVoice();
            } else {
              setTimeout(() => {
                imgLen();
              }, 500);
            }
          };
          imgLen();
        } else if (
          this.$refs.imgs.imgServerId.length == 0 &&
          this.$refs.voices.hasVoice == "recorded"
        ) {
          //不需上传图片、但有录音
          this.isOnlyVoice = true;
          this.$refs.voices.saveVoice();
        } else if (
          this.$refs.imgs.imgServerId.length == 0 &&
          this.$refs.voices.hasVoice == "unvoice"
        ) {
          //不需上传图片、没有录音
          this.getHttp();
        }
      }
    },
    /**
     * 获取签名
     */
    getConfigParams() {
      this.axios
        .get(process.env.API_HOST + "/accessToken/getSignature", {
          params: {
            localUrl: window.location.href.split("#")[0],
            appId: this.staffInfo.appList[0].appId
          }
        })
        .then(this.setAllConfig);
    },
    /**
     * 设置微信config参数
     * @param res
     */
    setAllConfig(res) {
      this.$refs.voices.setConfig(res);
      this.$refs.imgs.setConfig(res);
      this.accessToken = res.data.data.accessToken;
    },
    /**
     * 跳转选择服务事项页面
     */
    goToSelectMatterlists() {
      this.$router.push({
        name: "ServiceMatters",
        params: {
          workTypeCode: "1"
        }
      });
    },
    /**
     * 工单提交请求函数
     */
    getHttp() {
      if (this.isMissingItem()) {
        $.showLoading("提交中…");
        let sourcesFloorName, sourcesFloor;
        // if(this.staffInfo.gridList.length == 0){
        //   sourcesFloorName = ''
        //   sourcesFloor = ''
        // }else{
        //   sourcesFloorName = this.staffInfo.gridList[0].gridName
        //   sourcesFloor = this.staffInfo.gridList[0].parentGridId
        // }
        if (this.otherProject == "") {
          //如果值为ipms巡检，不需要设置这两个值，直接带过来的
          if (this.changeLocation.localtionPlaceCode) {
            this.localtion =
              this.localtion + "," + this.changeLocation.localtionPlaceCode;
            this.localtionName = this.localtionName + this.localtionPlaceName;
          } else {
            let localtionData = this.localtion;
            this.localtion = localtionData.substr(0, localtionData.length - 1);
          }
        }
        let param = null;
        let params = {
          hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
          deptCode: this.loginInfo.userOffice[0].deptCode,
          unitCode: this.loginInfo.userOffice[0].unitCode,
          userId: this.loginInfo.id,
          phoneNumber: this.loginInfo.phone,
          realName: this.loginInfo.staffName,
          workTypeCode: "1",
          questionDescription: this.value,
          urgencyDegreeCode: this.urgent,
          deptName: this.staffInfo.officeName,
          jobNumber: this.staffInfo.staffNumber,
          accessToken: this.accessToken,
          // type: this.loginInfo.type,
          type: this.staffInfo.teamId ? 2 : 1,
          job: this.staffInfo.administrativePost,
          appointmentType: this.time,
          appointmentDate: this.reservation,
          localtionName: this.localtionName,
          localtion: this.localtion,
          contactNumber: this.phone,
          itemDetailCode: this.matterlistsObj.itemDetailCode,
          itemDetailName: this.matterlistsObj.itemDetailName,
          itemServiceCode: this.matterlistsObj.itemServiceCode,
          itemServiceName: this.matterlistsObj.itemServiceName,
          itemTypeCode: this.matterlistsObj.itemTypeCode,
          itemTypeName: this.matterlistsObj.itemTypeName,
          sourcesFloorName: sourcesFloorName,
          sourcesFloor: sourcesFloor,
          repairWork: this.isRework,
          staffId: this.staffInfo.staffId,
          workSources: this.otherProject == "ipms" ? "3" : "1", //巡检为3,其余为1
          callerCompanyCode: this.loginInfo.userOffice[0].companyCode, //用于巡检
          callerCompanyName: this.loginInfo.userOffice[0].companyName, //用于巡检
          typeSources: this.attr
        };
        if (this.detector.browser.name != "micromessenger") {
          let imgArr = this.$refs.imgs.imgLocalIds;
          let blobArr = preView(imgArr);
          params.attachmentBlob1 = blobArr[0] || "";
          params.attachmentBlob2 = blobArr[1] || "";
          params.attachmentBlob3 = blobArr[2] || "";
          //params.callerTape1 = this.$refs.voices.base64Path.filedata[0] || "";
          params.workSources = 1;
          params.type = 1;
          params.repairWork = 1;
          params.callerTape = null;
          params.attachment = null;
          let formdata = new FormData();
          for (var item in params) {
            formdata.append(item, params[item] || "");
          }
          param = formdata;
          this.axios
            .post(__PATH.ONESTOP + "/appOlgTaskManagement.do?saveTask", param)
            .then(this.handleRequestSucc);
        } else {
          (params.callerTape = this.voiceParams),
            (params.attachment = JSON.stringify(this.imagesParams)),
            (param = this.$qs.stringify(params));
          this.axios
            .post(
              __PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat",
              param,
              {
                headers: {
                  Authorization:
                    "Bearer " + localStorage.getItem("token")
                }
              }
            )
            .then(this.handleRequestSucc);
        }
      }
    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      $.hideLoading();
      if (res.data.code == "200") {
        $.toast("工单提交成功", "success", () => {
          if (this.detector.browser.name != "micromessenger") {
            api.closeWin();
          } else {
            if (localStorage.prevLinkUrl) {
              //判断进入来源页面是公众号还是其他项目
              if (this.otherProject == "ipms") {
                //其他项目登录
                window.location.href =
                  localStorage.prevLinkUrl +
                  "?workNum=" +
                  res.data.data.workNum +
                  "&workId=" +
                  this.workId;
              } else {
                window.location.href = process.env.WX + "/mainHome";
                //                  wx.closeWindow();
              }
            }
          }
        });
      } else {
        $.toast(res.data.message, "text");
        if (res.data.code == 40001) {
          this.voiceParams = "";
          this.$refs.voices.handleDelVoiceClick();
        }
      }
    },
    /**
     * 校验必填选项
     */
    isMissingItem() {
      if (this.areaVal == "") {
        if (this.otherProject == "ipms") return true;
        else $.toast("请选择服务区域", "text");
        this.disSubmit = false;
      } else if (!this.matterlistsObj.itemTypeCode) {
        $.toast("请选择服务事项", "text");
        this.disSubmit = false;
      } else if (this.value == "") {
        $.toast("请填写申报描述", "text");
        this.disSubmit = false;
      } else {
        return true;
      }
    },
    /**
     * 时间比较（所选时间不能大于当前时间）
     * @param startTime  当前时间
     * @param endTime   所选时间
     */
    //时间比较（yyyy-MM-dd HH:mm:ss）
    compareTime(startTime, endTime) {
      let startTimeStyle = startTime;
      let startTimes = startTime.substring(0, 10).split("-");
      let endTimes = endTime.substring(0, 10).split("-");
      startTime =
        startTimes[1] +
        "-" +
        startTimes[2] +
        "-" +
        startTimes[0] +
        " " +
        startTime.substring(10, 19);
      endTime =
        endTimes[1] +
        "-" +
        endTimes[2] +
        "-" +
        endTimes[0] +
        " " +
        endTime.substring(10, 19);
      let thisResult =
        (Date.parse(endTime.replace(/-/g, "/")) -
          Date.parse(startTime.replace(/-/g, "/"))) /
        3600 /
        1000;
      if (thisResult < -0.02) {
        $.toast("应大于当前时间", "text");
        this.reservation = startTimeStyle;
      }
    },
    add0(m) {
      return m < 10 ? "0" + m : m;
    },
    format(shijianchuo) {
      var time = new Date(shijianchuo);
      var y = time.getFullYear();
      var m = time.getMonth() + 1;
      var d = time.getDate();
      var h = time.getHours();
      var mm = time.getMinutes();
      var s = time.getSeconds();
      return (
        y +
        "-" +
        this.add0(m) +
        "-" +
        this.add0(d) +
        " " +
        this.add0(h) +
        ":" +
        this.add0(mm) +
        ":" +
        this.add0(s)
      );
    },
    /**
     * 校验需联系电话的格式
     */
    submitAfterVrification() {
      this.disSubmit = true;
      // var reg= /^(((1[345789]\d{9})|0\d{2,3}-\d{7,8})|(\d{7,8}))$/;
      // if(!reg.test(this.phone) && this.phone != ''){
      if (this.phone.length < 11 && this.phone != "") {
        $.toast("请输入正确的电话号", "text");
        this.disSubmit = false;
      } else {
        this.handleSubmitClick();
      }
    }
  },
  mounted() {
    let _this = this;
    const nTime = new Date(); //限制时间大于当前（日)
    $(this.$refs.datetime).datetimePicker({
      title: "请选择预约时间",
      onChange: function(result) {
        let bir = [];
        result.value.forEach((i, index) => {
          bir.push(i);
        });
        let reservation =
          bir[0] + "-" + bir[1] + "-" + bir[2] + " " + bir[3] + ":" + bir[4];
        _this.reservation = reservation;
        _this.compareTime(_this.format(nTime), reservation);
      }
    });
  },
  watch: {
    time(curTimeType) {
      this.timeType = !this.timeType;
      if (curTimeType == 1) {
        this.$refs.datetime.click();
        this.first = false;
      }
    }
  },
  activated() {
    if (this.localtionPlaceName != this.changeLocation.localtionPlaceName) {
      this.localtionPlaceName = this.changeLocation.localtionPlaceName;
    }
    if (this.$route.params.matterlistsObj) {
      this.matterlistsObj = this.$route.params.matterlistsObj;
      if (this.$route.params.matterlistsObj.itemTypeName) {
        this.matterlists =
          this.$route.params.matterlistsObj.itemTypeName +
          "/" +
          this.$route.params.matterlistsObj.itemDetailName +
          "/" +
          this.$route.params.matterlistsObj.itemServiceName;
      }
      this.matterlistsState = " ";
    }
  },
  beforeRouteEnter(to, from, next) {
    //进入后先判断是否是登录状态
    if (to.matched.some(m => m.meta.auth)) {
      next(vm => {
        //判断是否从其他项目进入，并传参
        if (location.search.indexOf("ipms") != -1) {
          vm.otherProject = "ipms";
          let str = location.search;
          let strs = str.substr(1).split("&"); //[]
          let theRequest = {};
          for (let i = 0; i < strs.length; i++) {
            theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
          }

          if (theRequest.workId) {
            vm.workId = theRequest.workId;
            vm.localtionName = decodeURI(theRequest.localtionName);
            vm.localtion = theRequest.localtion;
          }
        }
        vm.getConfigParams();
      });
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .wrapper
    height: 100%
    background-color: $bgColor
    .content
      box-sizing: border-box
      min-height: 100%
      padding-bottom: 76px
      background-color: $bgColor
      .other-location-name
        margin-bottom: .2rem
        .ipt-content
          justify-content: flex-end
          line-height: 1.2
          text-align: left
      .er-level
        display: flex
        align-items: center
        margin-bottom: .2rem
        itemBaseStyle()
        .weui-cells_checkbox
          display: flex
          align-items: center
          justify-content: center
          justify-content: space-between
          width: 100%
          .redio-content
            display: flex
            color: $contentColor
            .declare-attr
              padding: 0
      .site
        margin-bottom: $marginb
      .item-style
        itemBaseStyle()
        .ipt
          text-align: right
          color: $contentColor
      .matter-style
        min-height: 0.98rem
        line-height: initial
      .mb-css
        margin-bottom: $marginb
      .desc-form
        margin-top: $marginb
        .desc
          padding: .32rem .32rem 0
          font-size: .28rem

  .btn
    padding: 0 1.12rem .3rem
    position: relative
    user-select: none
    .delete-voice
      position: absolute
      right: 10px
      top: 10px
      width: .5rem
    .play-btn
      display: flex
      justify-content: center
      align-items: center
      img
        width: .3rem
        position: absolute
        left: .24rem

  .recording
    position: fixed
    z-index: 2
    top: 2rem
    left: 50%
    margin-left: -1.2rem
    img
      width: 2.4rem
      opacity: .7

  .title-content
    itemBaseStyle()
    margin-top: .2rem
    display: flex
    align-items: center
    justify-content: space-between

  .ipt-content
    text-align: right
    display: flex
    align-items: center
    color: #999
  .matter-ipt
    display: flex
    justify-content: flex-end
    .ipt-pos
      position: relative
      .matterInput
        display: none
      span
        font-family: serif
        text-align: left
        display: flex

  .img-content
    background: #fff
    padding: 0 .3rem
    display: flex
    justify-content: space-around
    .img-wrapper
      flex: 1
      width: 0
      margin: .2rem
      position: relative
      img
        width: 100%
      .icon-img
        position: absolute
        top: 0
        right: 0

  .submit-btn
    padding: 0.22rem 0.32rem
    background-color: #fff
    width: 100%
    box-sizing: border-box
    height: 66px
    margin-top: -66px

  .play-void-img
    position: fixed
    width: 35%
    top: 50%
    left: 50%
    margin-left: -70px
    z-index: 99999

  .title
    font-size: .32rem

  .redio-content
    position: relative
    .select-time
      position: absolute
      left: 1rem
      bottom: .3rem
      display: flex
      justify-content: space-between
      width: calc(100% - 5em)
      .text
        width: 50px
        line-height: initial
      .ipt
        flex: 1
        text-align: right
    .textColor
      color: #999
    .disPointer
      pointer-events: none
.ellipsis{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
