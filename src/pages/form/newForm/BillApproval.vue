<template>
  <div class="inner">
    <Header :title="title" @backFun="goBack"></Header>
    <van-field v-model="approvalComment" rows="10" autosize label="审批意见" type="textarea" maxlength="500" placeholder="请输入审批意见" show-word-limit />
    <div class="btn">
      <van-button style="width: 90%" color="#3562db" @click="submit(2)" v-if="$route.query.type == 2">驳回</van-button
      ><van-button style="width: 90%" color="#3562db" @click="submit(1)" v-else>通过</van-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      title: "",
      approvalComment: ""
    };
  },

  created() {
    this.title = this.$route.query.type == 2 ? "驳回" : "通过";
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    submit(type) {
      let params = {
        isApproved: type,
        approvalControlId: this.$route.query.id,
        approvalComment: this.approvalComment,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        userName: JSON.parse(localStorage.getItem("loginInfo")).staffName
      };
      this.$api.approveOrReject(params).then(res => {
        this.$toast.success("审批成功");
        this.$router.go(-1);
      });
    },

    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style   scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
</style>
