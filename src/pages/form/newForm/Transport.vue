<template>
  <div class="wrapper">
    <Header title="运送服务" @backFun="goback"></Header>
    <!-- 工单提醒 -->
    <div class="reminder" v-if="hint">
      <img class="reminder-img" src="@/assets/images/reminder.png" alt="" />
      <span class="reminder-text">{{ hint }}</span>
    </div>
    <div class="content">
      <div class="reservation-btns item-style">
        <span
          :class="{
            active: idx == appointmentType ? !reservation : reservation
          }"
          @click="handleClickTimeText(idx)"
          v-for="(item, idx) of serviceHoursText"
          :key="idx"
          >{{ item.dictLabel }}</span
        >
      </div>
      <!--服务起点-->
      <div>
        <div>
          <div class="item-style border-bottom">
            <div class="er-text title">服务起点</div>
          </div>
          <div class="weui-cell item-style" style="padding-right: 0">
            <div class="weui-cell__hd">
              <label class="weui-label title">服务区域</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input class="weui-input ipt ellipsis" type="text" @click="showAreaPicker" readonly v-model="areaStartVal" placeholder="请选择服务起点" />
              <van-icon style="margin: 0 8px" name="arrow" />
              <van-icon style="margin-right: 10px" name="search" @click="toSelectArea(3)" />
              <span class="iconfont icon-saomiao" @click="scanRepair(0)" style="color: #3562db; padding-right: 10px"></span>
            </div>
          </div>
          <div class="weui-cell item-style site">
            <div class="weui-cell__hd">
              <label class="weui-label title">服务房间</label>
            </div>
            <div class="weui-cell__bd ipt-content" @click="toSelectSite(3)" v-if="isExistStartSite">
              <input class="weui-input ipt ellipsis" type="text" readonly v-model="startPlaceName" placeholder="请选择服务房间" />
              <span class="iconfont arrow">&#xe646;</span>
            </div>
          </div>
          <div class="weui-cell border-rightbottom item-style">
            <div class="weui-cell__hd">
              <label class="weui-label title">所属科室</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input class="weui-input ipt ellipsis right-aligned-input" type="text" readonly @click="showDeptPicker" v-model="sourcesDeptName" placeholder="请选择所属科室" />
              <van-icon style="margin: 0 8px" name="arrow" />
              <van-icon style="margin-right: 10px" name="search" @click="toSelectDept" />
            </div>
          </div>
          <van-popup v-model="showDeptPickerFlag" position="bottom">
            <div class="search-box">
              <van-search v-model="deptSearchValue" placeholder="请输入搜索关键词" />
            </div>
            <van-picker show-toolbar :columns="filteredDeptList" value-key="name" @confirm="onDeptConfirm" @cancel="showDeptPickerFlag = false" title="选择所属科室" />
          </van-popup>
        </div>
        <!--服务终点-->
        <div>
          <div class="item-style border-bottom">
            <div class="er-text title">服务终点</div>
          </div>
          <div class="weui-cell item-style" style="padding-right: 0">
            <div class="weui-cell__hd">
              <label class="weui-label title">服务区域</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input @click="showEndAreaPicker" class="weui-input ipt ellipsis" type="text" readonly v-model="areaEndVal" placeholder="请选择服务终点" />
              <van-icon style="margin: 0 8px" name="arrow" />
              <van-icon style="margin-right: 10px" name="search" @click="toSelectArea(4)" />
              <span class="iconfont icon-saomiao" @click="scanRepair(1)" style="color: #3562db; padding-right: 10px"></span>
            </div>
          </div>
          <div class="weui-cell item-style site border-rightbottom">
            <div class="weui-cell__hd">
              <label class="weui-label title">服务房间</label>
            </div>
            <div class="weui-cell__bd ipt-content" @click="toSelectSite(4)" v-if="isExistEndSite">
              <input class="weui-input ipt ellipsis" type="text" readonly v-model="endPlaceName" placeholder="请选择服务房间" />
              <span class="iconfont arrow">&#xe646;</span>
            </div>
          </div>
          <van-popup v-model="showAreaPickerFlag" position="bottom">
            <div class="search-box">
              <van-search v-model="areaSearchValue" placeholder="请输入搜索关键词" />
            </div>
            <van-picker show-toolbar :columns="filteredAreaList" value-key="showname" @confirm="onAreaConfirm" @cancel="showAreaPickerFlag = false" title="选择服务区域" />
          </van-popup>
          <van-popup v-model="showEndAreaPickerFlag" position="bottom">
            <div class="search-box">
              <van-search v-model="areaSearchValue" placeholder="请输入搜索关键词" />
            </div>
            <van-picker show-toolbar :columns="filteredEndAreaList" value-key="showname" @confirm="onEndAreaConfirm" @cancel="showEndAreaPickerFlag = false" title="选择服务区域" />
          </van-popup>
          <div class="weui-cell border-rightbottom item-style">
            <div class="weui-cell__hd">
              <label class="weui-label title">所属科室</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input
                class="weui-input ipt ellipsis right-aligned-input"
                type="text"
                readonly
                @click="showRecipientDeptPicker"
                v-model="recipientDeptName"
                placeholder="请选择所属科室"
              />
              <van-icon style="margin: 0 8px" name="arrow" />
              <van-icon style="margin-right: 10px" name="search" @click="toSelectRecipientDept" />
            </div>
          </div>
          <van-popup v-model="showRecipientDeptPickerFlag" position="bottom">
            <div class="search-box">
              <van-search v-model="recipientDeptSearchValue" placeholder="请输入搜索关键词" />
            </div>
            <van-picker
              show-toolbar
              :columns="filteredRecipientDeptList"
              value-key="name"
              @confirm="onRecipientDeptConfirm"
              @cancel="showRecipientDeptPickerFlag = false"
              title="选择所属科室"
            />
          </van-popup>
          <div class="weui-cell border-rightbottom item-style">
            <div class="weui-cell__hd">
              <label class="weui-label title star">服务部门</label>
            </div>
            <div class="weui-cell__bd ipt-content">
              <input class="weui-input ipt" readonly type="text" @click="handleDeptClick" v-model="designateDeptName" placeholder="请选择部门" />
            </div>
          </div>
          <van-field
            readonly
            clickable
            name="datetimePicker"
            :value="requireAccomplishDate"
            label="要求完工时间"
            right-icon="arrow"
            input-align="left"
            placeholder="请选择时间"
            @click="closingTimeFn"
          />
          <!-- 选择要求完工时间弹框 -->
          <van-popup v-model="timeShow" position="bottom" :style="{ height: '40%' }">
            <van-datetime-picker v-model="currentDate" type="datetime" title="选择要求完工时间" @confirm="dateComfirm" @cancel="dateCancel" />
          </van-popup>
        </div>
      </div>
      <!--预约时间-->
      <service-hours ref="serviceHours" v-if="reservation"></service-hours>
      <!--申报描述-->
      <div class="weui-cells weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">申报描述</div>
        </div>
        <div class="weui-cell desc content-css">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea"
              :placeholder="placeholder"
              maxlength="500"
              rows="5"
              ref="textarea"
              v-model="value"
              @keydown="keydown($event)"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
        <sounds-recording @getRecord="getRecord" @getRecordFile="getRecordFile" ref="voices" />
      </div>
      <upload-image style="position: relative" class="bottom" ref="imgs" @getImg="getImg" @delImg="delImg"></upload-image>
      <div :class="[rotate ? 'down-content' : 'up-content']">
        <!--服务起点-->
        <!-- <div v-if="requireCode == 2">
          <div>
            <div class="item-style border-bottom item-box" @click="showItemsPicker = true">
              <div>服务事项</div>
              <div v-if="pickItems && pickItems.length > 0">
                <span v-for="item in pickItems" :key="item">{{ item }} </span>
              </div>
              <div v-else class="ipt">请选择服务事项</div>
            </div>
          </div>
          <div>
            <div class="item-style border-bottom">
              <div class="er-text title">服务起点</div>
            </div>
            <div class="weui-cell item-style" style="padding-right: 0">
              <div class="weui-cell__hd">
                <label class="weui-label title">服务区域</label>
              </div>
              <div class="weui-cell__bd ipt-content">
                <input @click="toSelectArea(3)" class="weui-input ipt ellipsis" type="text" readonly v-model="areaStartVal" placeholder="请选择服务起点" />
                <span class="iconfont icon-saomiao" @click="scanRepair(0)" style="color: #3562db; padding-right: 10px"></span>
              </div>
            </div>
            <div class="weui-cell item-style site">
              <div class="weui-cell__hd">
                <label class="weui-label title">服务房间</label>
              </div>
              <div class="weui-cell__bd ipt-content" @click="toSelectSite(3)" v-if="isExistStartSite">
                <input class="weui-input ipt ellipsis" type="text" readonly v-model="startPlaceName" placeholder="请选择服务房间" />
                <span class="iconfont arrow">&#xe646;</span>
              </div>
            </div>
          </div>
          <div>
            <div class="item-style border-bottom">
              <div class="er-text title">服务终点</div>
            </div>
            <div class="weui-cell item-style" style="padding-right: 0px">
              <div class="weui-cell__hd">
                <label class="weui-label title">服务区域</label>
              </div>
              <div class="weui-cell__bd ipt-content">
                <input @click="toSelectArea(4)" class="weui-input ipt ellipsis" type="text" readonly v-model="areaEndVal" placeholder="请选择服务终点" />
                <span class="iconfont icon-saomiao" @click="scanRepair(1)" style="color: #3562db; padding-right: 10px"></span>
              </div>
            </div>
            <div class="weui-cell item-style site border-rightbottom">
              <div class="weui-cell__hd">
                <label class="weui-label title">服务房间</label>
              </div>
              <div class="weui-cell__bd ipt-content" @click="toSelectSite(4)" v-if="isExistEndSite">
                <input class="weui-input ipt ellipsis" type="text" readonly v-model="endPlaceName" placeholder="请选择服务房间" />
                <span class="iconfont arrow">&#xe646;</span>
              </div>
            </div>
          </div>
        </div> -->
        <!--紧急程度-->
        <urgent-level ref="urgent" :urgentArr="urgentArr"></urgent-level>
        <!--申报属性-->
        <declare-attribute ref="attribute" :declareAttributeArr="declareAttributeArr"></declare-attribute>
        <!--联系电话-->
        <!-- <div class="weui-cell border-rightbottom item-style mb-css">
          <div class="weui-cell__hd">
            <label class="weui-label title">联系电话</label>
          </div>
          <div class="weui-cell__bd ipt-content">
            <input
                class="weui-input ipt"
                type="tel"
                v-model="phone"
                oninput="if(value.length>13)value=value.slice(0,13)"
                @blur="handleTextareaBlurEvent"
                placeholder="请输入联系人电话"
            >
          </div>
        </div>-->
        <!--是否返修-->
        <is-rework ref="isRework"></is-rework>
      </div>
      <!--收起和完善信息-->
      <div class="adjustable">
        <div @click="switchBtn" style="color: #3562db" class="hide-box" :style="`width: ${!rotate ? '60%' : '82px'}`">
          <span class="hint">{{ !rotate ? "信息越详细报修更快捷" : "收起" }}</span>
          <img src="~images/more_down.png" :class="[!rotate ? 'down' : 'up']" />
        </div>
      </div>
    </div>
    <div class="submit-btn" :class="{ active: canShow }">
      <button class="weui-btn weui-btn_primary" @click="submitAfterVrification">提交</button>
    </div>
    <linkage-select @getAreaVal="handleGetAreaVal" ref="linkageSelect"></linkage-select>
    <van-popup v-model="showItemsPicker" position="bottom" :style="{ height: '30%' }">
      <van-checkbox-group v-model="pickItems">
        <van-cell-group>
          <van-cell v-for="(item, index) in columns" clickable :key="item" :title="item" @click="toggle(index)">
            <template #right-icon>
              <van-checkbox :name="item" ref="checkboxes" checked-color="#3562db" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
    </van-popup>
    <!-- <van-popup v-model="showPicker" round position="bottom">
      <van-picker show-toolbar :columns="teamList" @cancel="showPicker = false" @confirm="onConfirm" value-key="team_name" />
    </van-popup> -->
    <PublicServiceDepartment :showPicker="showPicker" :dataObj="dataObj" @cancel="showPicker = false" @confirm="onConfirm"></PublicServiceDepartment>
  </div>
</template>

<script>
import wx from "weixin-js-sdk";
import { mapState } from "vuex";
import { preView } from "@/common/lib/util.js";
import UploadImage from "@/common/uploadImg/uploadImg";
import UploadVoice from "@/common/uploadVoice/UploadVoice";
import LinkageSelect from "@/common/customize/LinkageSelect";
import UrgentLevel from "./components/UrgentLevel";
import ServiceHours from "./components/ServiceHours";
import DeclareAttribute from "./components/DeclareAttribute";
import IsRework from "./components/IsRework";
import YBS from "@/assets/utils/utils.js";
import moment from "moment";
import PublicServiceDepartment from "./components/publicServiceDepartment.vue";
export default {
  name: "TransportWorkOrder",
  components: {
    UploadImage,
    UploadVoice,
    LinkageSelect,
    UrgentLevel,
    ServiceHours,
    DeclareAttribute,
    IsRework,
    PublicServiceDepartment
  },
  data() {
    return {
      timeShow: false,
      currentDate: new Date(),
      requireAccomplishDate: "", //要求完工时间
      recordingInfo: "",
      recordingInfoFile: "",
      canShow: false,
      value: "",
      phone: "",
      isExist: true,
      isExistStartSite: true,
      isExistEndSite: true,
      voiceParams: "",
      imagesParams: [],
      imageConfig: [], //处理图片的mediaId和ossUrl
      voiceFlag: false,
      imgFlag: false,
      accessToken: "",
      isOnlyVoice: false,
      timeType: true,
      text: "",
      areaStartVal: "",
      areaEndVal: "",
      transportStartLocalName: "", //起点
      transportStartLocal: "",
      startPlaceName: "",
      startPlaceCode: "",
      transportEndLocalName: "", //终点
      transportEndLocal: "",
      serviceStartSiteResId: "",
      serviceEndSiteResId: "",
      endPlaceName: "",
      endPlaceCode: "",
      allSiteDataInfo: [], //请求返回所有的相关地点的信息
      popData: {},
      locationFlag: "",
      siteFlag: "",
      rotate: false, //完善申报信息时候旋转
      reservation: false, // 点击预约
      urgentArr: [], //紧急程度字典项
      declareAttributeArr: [], //申报属性字典项
      serviceHoursText: [], //服务时间（预约、立刻）字典项
      appointmentType: "", //服务时间（预约、立刻）字典项0 或1
      requireCode: 2,
      isSubmit: true,
      placeholder: "请输入您要申报的内容描述、语音，字数在500字以内，语音限制60秒",
      requiredCode: "", //提交工单必选项
      hint: "", //工单配置提醒
      amount: {
        //上传图片限制
        quantity: 3,
        capital: "三"
      },
      type: {}, //路由数据
      showItemsPicker: false,
      columns: [],
      pickItems: [],
      itemsList: [],
      showPicker: false,
      designateDeptName: "",
      designateDeptCode: "",
      teamList: [],
      dataObj: {
        workTypeCode: "3"
      },
      showDeptPickerFlag: false,
      deptSearchValue: "",
      sourcesDeptName: "",
      sourcesDept: "",
      areaSearchValue: "",
      areaList: [],
      deptList: [],
      olgWorkPushNew: {}, // 工单配置
      showAreaPickerFlag: false, // 服务区域选择器
      showRecipientDeptPickerFlag: false, // 服务部门选择器
      recipientDeptSearchValue: "",
      recipientDeptList: [],
      recipientDeptName: "",
      recipientDept: "",
      showEndAreaPickerFlag: false,
      endAreaList: [],
      endAreaSearchValue: ""
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "transLocationStart", "transLocationEnd", "serviceAreaTreeData"]),
    // 过滤后的区域列表
    filteredAreaList() {
      if (!this.areaSearchValue) {
        return this.areaList;
      }
      return this.areaList.filter(item => item.name && item.name.toLowerCase().includes(this.areaSearchValue.toLowerCase()));
    },
    filteredEndAreaList() {
      if (!this.endAreaSearchValue) {
        return this.endAreaList;
      }
      return this.endAreaList.filter(item => item.name && item.name.toLowerCase().includes(this.endAreaSearchValue.toLowerCase()));
    },
    // 过滤后的科室列表
    filteredDeptList() {
      if (!this.deptSearchValue) {
        return this.deptList;
      }
      return this.deptList.filter(item => item.name && item.name.toLowerCase().includes(this.deptSearchValue.toLowerCase()));
    },
    // 过滤后的服务部门列表
    filteredRecipientDeptList() {
      if (!this.recipientDeptSearchValue) {
        return this.recipientDeptList;
      }
      return this.recipientDeptList.filter(item => item.name && item.name.toLowerCase().includes(this.recipientDeptSearchValue.toLowerCase()));
    }
  },
  watch: {
    value(value, oldValue) {
      if (oldValue && !value) {
        this.$refs.textarea.click();
      }
    }
  },
  methods: {
    showEndAreaPicker() {
      this.endAreaSearchValue = ""; // 清空搜索值
      this.showEndAreaPickerFlag = true;
    },
    getRecipientDeptListByLocation(configType) {
      this.$api
        .getDeptListByLocation({
          localtionId: this.endPlaceCode ? this.transportEndLocal.replace(/,/g, "_") + "_" + this.endPlaceCode : this.transportEndLocal.replace(/,/g, "_")
        })
        .then(res => {
          this.recipientDeptList = res;
          if (configType == "1" || (configType == "2" && !this.recipientDeptName && res && res.length > 0)) {
            this.recipientDeptName = res[0].name;
            this.recipientDept = res[0].id;
          }
        });
    },
    onRecipientDeptConfirm(value) {
      this.recipientDeptName = value.name;
      this.recipientDept = value.id;
      this.showRecipientDeptPickerFlag = false;
    },
    toSelectRecipientDept() {
      this.$router.push({
        path: "/deptSelectedPage",
        query: {
          fromPath: "transport",
          isRecipient: true
        }
      });
    },
    showRecipientDeptPicker() {
      this.recipientDeptSearchValue = ""; // 清空搜索值
      this.showRecipientDeptPickerFlag = true;
    },
    getEndLocationListByDept(configType) {
      this.$api
        .getLocationListByDept({
          deptId: this.recipientDept
        })
        .then(res => {
          console.log("根据科室获取地点列表", res);
          // 处理数据，拼接名称和ID
          this.endAreaList = res.spaceInfoByDeptIdList.map(item => {
            // 处理名称：拼接allParentName和gridName，并去掉所有的>符号
            const name = (item.allParentName + item.gridName).replace(/>/g, "");
            const showname = item.allParentName + ">" + item.gridName;
            // 直接修改item的allParentId属性
            if (item.allParentId && item.allParentId.startsWith("#,")) {
              item.allParentId = item.allParentId.substring(2); // 去掉#和逗号
            } else if (item.allParentId && item.allParentId.startsWith("#")) {
              item.allParentId = item.allParentId.substring(1); // 只去掉#
            }

            return {
              ...item,
              name,
              showname
            };
          });
          if (configType == "0" || (configType == "2" && !this.areaEndVal)) {
            this.areaEndVal = this.endAreaList[0].allParentName.replace(/>/g, "");
            this.endPlaceName = this.endAreaList[0].gridName;
            this.transLocationEnd.endPlaceName = this.endPlaceName;
            this.transLocationEnd.endPlaceCode = this.endPlaceCode;
            this.transportEndLocalName = this.areaEndVal;
            this.transportEndLocal = this.endAreaList[0].allParentId;
            this.serviceEndSiteResId = this.endAreaList[0].parentGridId;
          }
        });
    },
    getLocationListByDept(configType) {
      this.$api
        .getLocationListByDept({
          deptId: this.sourcesDept
        })
        .then(res => {
          console.log("根据科室获取地点列表", res);
          // 处理数据，拼接名称和ID
          this.areaList = res.spaceInfoByDeptIdList.map(item => {
            // 处理名称：拼接allParentName和gridName，并去掉所有的>符号
            const name = (item.allParentName + item.gridName).replace(/>/g, "");
            const showname = item.allParentName + ">" + item.gridName;
            // 直接修改item的allParentId属性
            if (item.allParentId && item.allParentId.startsWith("#,")) {
              item.allParentId = item.allParentId.substring(2); // 去掉#和逗号
            } else if (item.allParentId && item.allParentId.startsWith("#")) {
              item.allParentId = item.allParentId.substring(1); // 只去掉#
            }

            return {
              ...item,
              name,
              showname
            };
          });
          if (configType == "0" || (configType == "2" && !this.areaStartVal)) {
            this.areaStartVal = this.areaList[0].allParentName.replace(/>/g, "");
            this.startPlaceName = this.areaList[0].gridName;
            this.startPlaceCode = this.areaList[0].id;
            this.transLocationStart.startPlaceName = this.startPlaceName;
            this.transLocationStart.startPlaceCode = this.startPlaceCode;
            this.transportStartLocalName = this.areaStartVal;
            this.transportStartLocal = this.areaList[0].allParentId;
            this.serviceStartSiteResId = this.areaList[0].parentGridId;
          }
        });
    },
    onAreaConfirm(value) {
      this.areaStartVal = value.allParentName.replace(/>/g, "");
      this.startPlaceName = value.gridName;
      this.startPlaceCode = value.id;
      this.transLocationStart.startPlaceName = this.startPlaceName;
      this.transLocationStart.startPlaceCode = this.startPlaceCode;
      this.transportStartLocalName = this.areaStartVal;
      this.transportStartLocal = value.allParentId;
      this.serviceStartSiteResId = value.parentGridId;
      this.showAreaPickerFlag = false;
    },
    onEndAreaConfirm(value) {
      this.areaEndVal = value.allParentName.replace(/>/g, "");
      this.endPlaceName = value.gridName;
      this.endPlaceCode = value.id;
      this.transLocationEnd.endPlaceName = this.endPlaceName;
      this.transLocationEnd.endPlaceCode = this.endPlaceCode;
      this.transportEndLocalName = this.areaEndVal;
      this.transportEndLocal = value.allParentId;
      this.serviceEndSiteResId = value.parentGridId;
      this.showEndAreaPickerFlag = false;
    },
    showAreaPicker() {
      this.areaSearchValue = ""; // 清空搜索值
      this.showAreaPickerFlag = true;
    },
    getDeptListByLocation(configType) {
      this.$api
        .getDeptListByLocation({
          localtionId: this.startPlaceCode ? this.transportStartLocal.replace(/,/g, "_") + "_" + this.startPlaceCode : this.transportStartLocal.replace(/,/g, "_")
        })
        .then(res => {
          console.log("根据地点获取科室列表", res);
          this.deptList = res;
          if (configType == "1" || (configType == "2" && !this.sourcesDeptName && res && res.length > 0)) {
            this.sourcesDeptName = res[0].name;
            this.sourcesDept = res[0].id;
          }
        });
    },
    onDeptConfirm(value) {
      this.sourcesDeptName = value.name; // 使用 name 字段作为显示值
      this.sourcesDept = value.id; // 使用 id 字段作为实际值
      this.showDeptPickerFlag = false;
    },
    showDeptPicker() {
      this.deptSearchValue = ""; // 清空搜索值
      this.showDeptPickerFlag = true;
    },
    toSelectDept() {
      this.$router.push({
        path: "/deptSelectedPage",
        query: {
          fromPath: "transport"
        }
      });
    },
    getDialogShow() {
      let params = {
        userId: this.loginInfo.staffId,
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getNotCommentWarn(params).then(res => {
        if (res.commentCount > 0 && res.commentStatus == "1") {
          this.$dialog
            .confirm({
              message: "您当前有未评价的工单！",
              confirmButtonText: "去评价",
              cancelButtonText: "忽略",
              confirmButtonColor: "#3562db"
            })
            .then(() => {
              this.$router.push({
                path: "/orderAcceptance"
              });
            })
            .catch(() => {});
        }
      });
    },
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    //时间弹窗

    dateComfirm(e) {
      this.requireAccomplishDate = this.formatDate(e);
      this.dateCancel();
    },
    dateCancel() {
      this.timeShow = false;
    },
    closingTimeFn() {
      this.timeShow = true;
    },
    getOCTeamInfo() {
      const params = {
        localtionId: "",
        workTypeCode: "3",
        // itemTypeCode: this.selectRowId,
        matterId: ""
      };
      this.$api.getTeamsByTask(params).then(res => {
        console.log("getOCTeamInfo", res);
        this.teamList = res.list;
      });
    },
    handleDeptClick() {
      let localtionId = this.startPlaceCode ? this.startPlaceCode : this.serviceStartSiteResId || "";
      this.dataObj = {
        ...this.dataObj,
        localtionId, // 服务地点 / 服务房间
        matterId: "" // 服务事项
      };
      this.showPicker = true;
    },
    onConfirm(val) {
      this.designateDeptName = val.team_name;
      this.designateDeptCode = val.id;
      this.showPicker = false;
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle();
      console.log("index", index);
    },
    getItems() {
      this.$api.getOlgTransportItem({}).then(res => {
        this.itemsList = res.list;
        this.columns = res.list.map(item => {
          return item.typeName;
        });
      });
    },
    pickerClosed() {
      console.log("pickerClosed", this.showItemsPicker);
    },
    getImg(img) {
      this.imagesParams.push(img);
    },
    delImg(url) {
      this.imagesParams = this.imagesParams.filter(item => {
        return item != url;
      });
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
    getRecord(info) {
      this.recordingInfo = info;
      this.voiceParams = info;
      console.log("recordingInfo", this.recordingInfo);
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
      console.log("recordingInfoFile", this.recordingInfoFile);
    },
    /**
     * 工单配置提醒接口
     */
    getTaskWorkConfiguration(hospitalCode, unitCode) {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?getTaskWorkConfiguration",
          this.$qs.stringify({
            hospitalCode,
            unitCode,
            workTypeCode: this.type.workTypeCode
          }),
          {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.hint = res.hint;
          this.workCode = res.workTypeCode;
        });
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 点击随时
     */
    handleClickTimeText(index) {
      if (index == 0) {
        this.reservation = false;
      } else {
        this.reservation = true;
      }
    },
    /**
     * 是否展示除地点以外的信息
     */
    switchBtn() {
      this.rotate = !this.rotate;
    },
    /**
     * 区域选择
     */
    toSelectArea(num) {
      //级联弹窗数据还原
      this.localtionPlaceName = "";
      this.$router.push({
        path: "/selectArea",
        query: {
          routerPath: this.$route.path,
          style: num
        }
      });

      if (num == 3) {
        this.startPlaceName = "";
        this.startPlaceCode = "";
        this.transLocationStart.startPlaceName = "";
        this.transLocationStart.startPlaceCode = "";
      }
      if (num == 4) {
        this.endPlaceName = "";
        this.endPlaceCode = "";
        this.transLocationEnd.endPlaceName = "";
        this.transLocationEnd.endPlaceCode = "";
      }
    },
    /**
     * 处理已选择的区域参数
     */
    handleGetAreaVal(params) {
      if (this.locationFlag == "4") {
        //终点
        this.serviceEndSiteResId = params.code[2];
        this.areaEndVal = params.name.join("");
        this.transportEndLocalName = params.name.join("");
        this.transportEndLocal = params.code.join(",");

        if (this.isExist) {
          this.isExistEndSite = true;
        } else {
          this.isExistEndSite = false;
        }
      } else {
        //          起点
        this.serviceStartSiteResId = params.code[2];
        this.areaStartVal = params.name.join("");
        this.transportStartLocalName = params.name.join("");
        this.transportStartLocal = params.code.join(",");

        if (this.isExist) {
          this.isExistStartSite = true;
        } else {
          this.isExistStartSite = false;
        }
      }
    },
    /**
     * 服务房间请求函数
     */
    toSelectSite(num) {
      let siteArr = [];
      const treeData = this.serviceAreaTreeData;
      if (num == 3) {
        if (this.serviceStartSiteResId == "") {
          $.toast("请先选择起始服务区域", "text");
        } else {
          this.allSiteDataInfo = treeData.filter(item => {
            return item.parentId == this.serviceStartSiteResId;
          });
          //将所有地点取出放到数组中
          for (let i = 0; i < this.allSiteDataInfo.length; i++) {
            siteArr.push(this.allSiteDataInfo[i]);
          }
          // this.$api
          //   .cascadingQueryHospitalGridInfo({
          //     gridId: this.serviceStartSiteResId
          //   })
          //   .then(res => {
          //     this.allSiteDataInfo = res;
          //     //将所有地点取出放到数组中
          //     for (let i = 0; i < this.allSiteDataInfo.length; i++) {
          //       siteArr.push(this.allSiteDataInfo[i]);
          //     }
          //   });
        }
      } else {
        if (this.serviceEndSiteResId == "") {
          $.toast("请先选择终点服务区域", "text");
        } else {
          this.allSiteDataInfo = treeData.filter(item => {
            return item.parentId == this.serviceEndSiteResId;
          });
          //将所有地点取出放到数组中
          for (let i = 0; i < this.allSiteDataInfo.length; i++) {
            siteArr.push(this.allSiteDataInfo[i]);
          }
          // this.$api
          //   .cascadingQueryHospitalGridInfo({
          //     gridId: this.serviceEndSiteResId
          //   })
          //   .then(res => {
          //     this.allSiteDataInfo = res;
          //     //将所有地点取出放到数组中
          //     for (let i = 0; i < this.allSiteDataInfo.length; i++) {
          //       siteArr.push(this.allSiteDataInfo[i]);
          //     }
          //   });
        }
      }
      //跳到搜索页面
      this.$router.push({
        name: "Site",
        params: {
          list: siteArr,
          style: num
        }
      });
    },
    /**
     * 仅供服务地点展示
     */
    showVal(res) {},
    /**
     * 删除录音
     */
    deleteVoice() {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "BJSJTYY",
            unitCode: this.unitCode ? this.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "BJSYGJ",
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: this.voiceParams
          })
        )
        .then(res => {
          this.voiceParams = "";
        });
    },
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempIdx = "";
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
            unitCode: this.loginInfo.userOffice[0].unitCode,
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
              unitCode: this.loginInfo.userOffice[0].unitCode,
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
          });
      });
    },
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    },
    /**
     * 处理提交按钮事件
     */
    handleSubmitClick() {
      this.isMissingItem();
      // if (
      //   this.imagesParams.length != 0 &&
      //   this.$refs.voices.UploadVoice.hasVoice != "recorded"
      // ) {
      //   //需上传图片、没有录音
      //   let imgLen = () => {
      //     if (this.imagesParams.length != 0) {
      //       this.isMissingItem();
      //     } else {
      //       setTimeout(() => {
      //         imgLen();
      //       }, 500);
      //     }
      //   };
      //   imgLen();
      // } else if (
      //   this.imagesParams.length != 0 &&
      //   this.$refs.voices.UploadVoice.hasVoice == "recorded"
      // ) {
      //   //需上传图片、录音
      //   let imgLen = () => {
      //     if (this.imagesParams.length != 0) {
      //       this.isOnlyVoice = true;
      //       // this.$refs.voices.saveVoice();
      //     } else {
      //       setTimeout(() => {
      //         imgLen();
      //       }, 500);
      //     }
      //   };
      //   imgLen();
      // } else if (
      //   this.imagesParams.length == 0 &&
      //   this.$refs.voices.UploadVoice.hasVoice == "recorded"
      // ) {
      //   //不需上传图片、但有录音
      //   this.isOnlyVoice = true;
      //   // this.$refs.voices.saveVoice();
      // } else if (
      //   this.imagesParams.length == 0 &&
      //   this.$refs.voices.UploadVoice.hasVoice == "unvoice"
      // ) {
      //   //不需上传图片、没有录音
      //   this.isMissingItem();
      // }
    },
    /**
     * 设置微信config参数
     * @param res
     */
    setAllConfig(res) {
      this.$refs.voices.setConfig(res);
      this.$refs.imgs.setConfig(res);
      this.accessToken = res.data.data.accessToken;
    },
    /**
     * 工单提交请求函数
     */
    getHttp() {
      // this.isMissingItem()
      if (!this.isSubmit) return;
      this.isSubmit = false;
      $.showLoading("提交中…");
      let sourcesFloorName, sourcesFloor;
      // if (this.staffInfo.gridList.length == 0) {
      //   sourcesFloorName = "";
      //   sourcesFloor = "";
      // } else {
      //   sourcesFloorName = this.staffInfo.gridList[0].gridName;
      //   sourcesFloor = this.staffInfo.gridList[0].parentGridId;
      // }
      let pickItemsCodes = [];
      this.itemsList.forEach(item => {
        this.pickItems.forEach(pick => {
          if (item.typeName == pick) {
            pickItemsCodes.push(item.typeValue);
          }
        });
      });
      let param = null;
      let params = {
        hospitalCode: this.loginInfo.hospitalCode,
        deptCode: "",
        unitCode: this.loginInfo.unitCode,
        userId: this.loginInfo.id,
        phoneNumber: this.loginInfo.phone,
        realName: this.loginInfo.staffName,
        workTypeCode: this.type.workTypeCode,
        deptName: "",
        questionDescription: this.value,
        callerTape: this.voiceParams,
        attachment: JSON.stringify(this.imagesParams),
        urgencyDegreeCode: this.$refs.urgent.urgent,
        jobNumber: this.staffInfo.staffNumber,
        accessToken: this.accessToken,
        type: this.staffInfo.teamId ? 2 : 1,
        job: this.staffInfo.administrativePost,
        appointmentType: this.reservation ? "1" : "0",
        appointmentDate: this.reservation ? this.$refs.serviceHours.reservation : "",
        transportStartLocalName: this.transportStartLocalName,
        transportStartLocal: this.transportStartLocal,
        startPlaceName: this.transLocationStart.startPlaceName,
        startPlaceCode: this.transLocationStart.startPlaceCode,
        transportEndLocalName: this.transportEndLocalName,
        transportEndLocal: this.transportEndLocal,
        endPlaceName: this.transLocationEnd.endPlaceName,
        endPlaceCode: this.transLocationEnd.endPlaceCode,
        contactNumber: this.phone,
        sourcesFloorName: sourcesFloorName,
        sourcesFloor: sourcesFloor,
        repairWork: this.$refs.isRework.isRework,
        staffId: this.staffInfo.staffId,
        workSources: "1",
        typeSources: this.$refs.attribute.attr,
        dispatchingConfig: this.requireCode, //	移动申报自动派工标识
        transportTypeCode: pickItemsCodes.join(","),
        transportName: this.pickItems.join(","),
        designateDeptName: this.designateDeptName,
        designateDeptCode: this.designateDeptCode,
        requireAccomplishDate: this.requireAccomplishDate, //要求完工时间
        sourcesDeptName: this.sourcesDeptName,
        sourcesDept: this.sourcesDept,
        recipientDeptCode: this.recipientDept,
        recipientDeptName: this.recipientDeptName
      };
      param = this.$qs.stringify(params);
      this.axios
        .post(__PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat", param, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(this.handleRequestSucc);
      // if (this.detector.browser.name != "micromessenger") {
      //   let imgArr = this.$refs.imgs.imgLocalIds;
      //   let blobArr = preView(imgArr);
      //   params.attachmentBlob1 = blobArr[0] || "";
      //   params.attachmentBlob2 = blobArr[1] || "";
      //   params.attachmentBlob3 = blobArr[2] || "";
      //   //params.callerTape1 = this.$refs.voices.base64Path.filedata[0] || "";
      //   params.workSources = 1;
      //   params.type = 1;
      //   params.repairWork = 1;
      //   params.callerTape = null;
      //   params.attachment = null;
      //   let formdata = new FormData();
      //   for (var item in params) {
      //     formdata.append(item, params[item] || "");
      //   }
      //   param = formdata;
      //   this.axios
      //     .post(__PATH.ONESTOP + "/appOlgTaskManagement.do?saveTask", param)
      //     .then(this.handleRequestSucc);
      // } else {
      //   param = this.$qs.stringify(params);
      //   this.axios
      //     .post(
      //       __PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat",
      //       param
      //     )
      //     .then(this.handleRequestSucc);
      // }
    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      $.hideLoading();
      if (res.data.code == "200") {
        $.toast("工单提交成功", "success", () => {
          sessionStorage.removeItem("hospitalCode");
          sessionStorage.removeItem("unitCode");
          sessionStorage.removeItem("url");
        });
        setTimeout(() => {
          // api.closeFrame();
          this.$YBS.apiCloudCloseFrame();
        }, 1000);
      } else {
        $.toast(res.data.message, "text");
        if (res.data.code == 40001) {
          this.voiceParams = "";
          this.$refs.voices.handleDelVoiceClick();
        }
      }
      // $.hideLoading("提交中…");
    },
    /**
     * 校验必填选项
     */
    isMissingItem() {
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement/getIsRequired",
          this.$qs.stringify({
            hospitalCode: this.loginInfo.hospitalCode,
            unitCode: this.loginInfo.unitCode,
            workTypeCode: this.type.workTypeCode
          }),
          {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          }
        )
        .then(res => {
          res = res.data.data;
          this.requiredCode = res.requiredCode;
          this.requiredParameter(res.requiredCode);
        });
    },
    requiredParameter(value) {
      if (this.value == "" && !this.voiceParams) {
        $.toast("请填写申报描述或录入语音", "text");
      } else if (this.reservation == 1 && !this.$refs.serviceHours.reservation) {
        $.toast("请填写预约时间", "text");
      } else if (value && value.indexOf("transportStartLocal", 0) != -1 && this.areaStartVal == "") {
        $.toast("请选择服务起点区域", "text");
      } else if (value && value.indexOf("transportEndLocal", 0) != -1 && this.areaEndVal == "") {
        $.toast("请选择服务终点区域", "text");
      } else if (this.value == "" && !this.voiceParams) {
        $.toast("请填写申报描述或录入语音", "text");
      } else if (this.reservation == 1 && !this.$refs.serviceHours.reservation) {
        $.toast("请填写预约时间", "text");
      } else {
        this.getHttp();
      }
    },
    /**
     * 校验联系电话的格式
     */
    submitAfterVrification() {
      // var reg = /^(((1[345789]\d{9})|0\d{2,3}-\d{7,8})|(\d{7,8}))$/;
      // if (!reg.test(this.phone) && this.phone != "") {
      if (this.phone.length < 11 && this.phone != "") {
        $.toast("请输入正确的电话号", "text");
      } else {
        this.handleSubmitClick();
      }
    },
    /**
     * 获取医院自定义的字典项接口
     */
    getPersonnelDictionaryFun(type) {
      let params = {
        type: type
      };
      if (type == 2) {
        params.states = 2;
      }
      this.$api.getPersonnelDictionary(params).then(res => {
        switch (type) {
          case 1:
            this.urgentArr = res.reverse();
            break;
          case 2:
            this.declareAttributeArr = res;
            break;
          case 4:
            this.serviceHoursText = res;
            this.appointmentType = res[0].dictValue;
            if (this.appointmentType == 1) {
              this.reservation = true;
            }
            break;
        }
      });
    },
    getHospitalDispatchingConfig(hospitalCode, unitCode) {
      this.$api
        .getHospitalDispatchingConfig({
          hospitalCode: hospitalCode,
          unitCode: unitCode
        })
        .then(res => {
          //复制是否需要必填字段
          this.requireCode = res;
        });
    },
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    },
    scanRepairCallback(scanData, option) {
      let result = scanData;
      let unitCode = this.unitCode ? this.unitCode : sessionStorage.unitCode;
      let hospitalCode = this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode;
      if (result[1]) {
        this.axios
          .post(
            __PATH.BASE_API + "/space/api/getHospitalSpaceInfoById",
            this.$qs.stringify({
              unitCode: unitCode || this.loginInfo.unitCode,
              hospitalCode: hospitalCode || this.loginInfo.hospitalCode,
              id: result[1]
            }),
            {
              headers: {
                Authorization: "Bearer " + localStorage.getItem("token")
              }
            }
          )
          .then(res => {
            if (res.data.data.gridLevel == "3" || res.data.data.gridLevel == "2") {
              //4为楼层,2为区
              //二维码中ID为楼宇，不显示服务房间
              this.isExist = false;
            }
            let staffGrid = res.data.data;
            if (staffGrid.allParentId) {
              this.serviceSiteResId = staffGrid.id; //服务区域id
              let placeNameArr = staffGrid.allParentName.split(">");
              let placeCodeArr = staffGrid.allParentId.split(",");
              //增加未完工工单列表
              let lastAreaCode = placeCodeArr[placeCodeArr.length - 1];
              this.$api
                .getUnfinishedMineTask({
                  userId: this.loginInfo.id,
                  // staffType: 2,
                  staffType: this.loginInfo.type,
                  type: "1,2,3,4",
                  curPage: 1,
                  pageSize: 999,
                  startTime: "",
                  endTime: "",
                  // areaCode: 'c9ec1fdd94a34373aaad00305e2a4b49',
                  areaCode: lastAreaCode,
                  // staffId:'89733778adba4940b272f54ab1a1d5aa'
                  staffId: this.staffInfo.staffId
                })
                .then(resP => {
                  // console.log(resP)
                  if (this.loginInfo.type == 2) {
                    if (resP.details.length > 0) {
                      $.toast("当前区域中有未完成工单，待完工后再进行扫码申报！", "text");
                      this.$router.push("/notCompleteOrderList");
                    }
                  } else if (this.loginInfo.type == 1) {
                    if (resP.length > 0) {
                      $.toast("当前区域中有未完成工单，待完工后再进行扫码申报！", "text");
                      this.$route.push("/notCompleteOrderList");
                    }
                  }
                });

              if (option) {
                //终点
                if (placeCodeArr.length == 4) {
                  this.endPlaceName = placeNameArr.pop();
                  this.endPlaceCode = placeCodeArr.pop();
                } else {
                  this.endPlaceName = "";
                  this.endPlaceCode = "";
                }
                this.transLocationEnd.endPlaceName = this.endPlaceName;
                this.transLocationEnd.endPlaceCode = this.endPlaceCode;
                this.transportEndLocalName = placeNameArr.join("");
                this.areaEndVal = placeNameArr.join("");
                this.transportEndLocal = placeCodeArr.join(","); //服务区域id
              } else {
                //起点
                if (placeCodeArr.length == 4) {
                  this.startPlaceName = placeNameArr.pop();
                  this.startPlaceCode = placeCodeArr.pop();
                } else {
                  this.startPlaceName = "";
                  this.startPlaceCode = "";
                }
                this.transLocationStart.startPlaceName = this.startPlaceName;
                this.transLocationStart.startPlaceCode = this.startPlaceCode;
                this.transportStartLocalName = placeNameArr.join("");
                this.transportStartLocal = placeCodeArr.join(",");
                this.areaStartVal = placeNameArr.join("");
              }
            } else {
              $.toast("未找到相关位置", "text");
            }
          });
      } else {
        $.toast("请检查二维码是否正确", "text");
      }
    },
    scanRepair(option) {
      // window.wx.scanQRCode({
      //   needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
      //   scanType: ["qrCode", "barCode"],
      //   success: res => {
      //     let result = res.resultStr.split(","); // 当needResult 为 1 时，扫码返回的结果
      //     this.scanRepairCallback(result, option);
      //   }
      // });
      if (!YBS.hasPermission("storage")) {
        YBS.reqPermission(["storage"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
            } else {
            }
          }
        });
        return;
      }
      if (!YBS.hasPermission("camera")) {
        YBS.reqPermission(["camera"], function (ret) {
          console.log(ret);
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
          }
        });
        return;
      }

      try {
        YBS.scanCodeNew().then(
          item => {
            console.log(item, "ittt");
            this.scanRepairCallback(item, option);
          },
          () => {
            this.$toast.fail("无效二维码!");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    }
  },
  mounted() {
    this.$api.getNewSysConfigParam({}).then(res => {
      console.log("获取pc配置", res);
      this.olgWorkPushNew = res.olgWorkPushNew;
    });
    this.getDialogShow();

    this.getOCTeamInfo();
    this.getItems();
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if (systemType === "ios") {
      document.querySelector(".content").style.minHeight = "88%";
    }
    console.log(this.$route.query);
    this.type = this.$route.query;
    /*获取字典项*/
    this.getPersonnelDictionaryFun(1);
    this.getPersonnelDictionaryFun(2);
    this.getPersonnelDictionaryFun(4);
    this.getHospitalDispatchingConfig(this.loginInfo.hospitalCode, this.loginInfo.unitCode);
    String.prototype.replaceAll = function (reallyDo, replaceWith, ignoreCase) {
      if (!RegExp.prototype.isPrototypeOf(reallyDo)) {
        return this.replace(new RegExp(reallyDo, ignoreCase ? "gi" : "g"), replaceWith);
      } else {
        return this.replace(reallyDo, replaceWith);
      }
    };
  },
  activated() {
    setTimeout(() => {
      this.$YBS.apiCloudEventKeyBack(this.goback);
    }, 100);
    this.getTaskWorkConfiguration(this.loginInfo.hospitalCode, this.loginInfo.unitCode);
    if (this.transLocationStart.style == 3) {
      //起点
      this.designateDeptName = "";
      this.designateDeptCode = "";
      this.startPlaceName = this.transLocationStart.startPlaceName;
      this.startPlaceCode = this.transLocationStart.startPlaceCode;
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "1" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getDeptListByLocation(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    }
    if (this.transLocationEnd.style == 4) {
      //终点
      this.endPlaceName = this.transLocationEnd.endPlaceName;
      this.endPlaceCode = this.transLocationEnd.endPlaceCode;
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "1" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getRecipientDeptListByLocation(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    }
    if (this.$route.query.selectedOffice && !this.$route.query.isRecipient) {
      this.sourcesDeptName = this.$route.query.selectedOffice.officeName;
      this.sourcesDept = this.$route.query.selectedOffice.id;
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "0" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getLocationListByDept(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    }
    if (this.$route.query.selectedOffice && this.$route.query.isRecipient) {
      this.recipientDeptName = this.$route.query.selectedOffice.officeName;
      this.recipientDept = this.$route.query.selectedOffice.id;
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "0" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getEndLocationListByDept(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    }
    if (JSON.stringify(this.$route.query).includes("isExist")) {
      //从选择服务区域页面跳转回来携带参数
      let params = this.$route.query;
      if (params.style == 3) {
        //        判断服务区域是数组还是字符串
        if (Array.isArray(params.name)) {
          this.areaStartVal = params.name.join("");
          this.transportStartLocalName = params.name.join("");
          this.serviceStartSiteResId = params.code[params.code.length - 1];
          this.transportStartLocal = params.code.join(",");
        } else {
          //          最近服务区域返回的信息
          this.areaStartVal = params.name;
          this.transportStartLocalName = params.name;
          this.serviceStartSiteResId = params.code[params.code.length - 1];
          this.transportStartLocal = params.code.join(",");
        }
        if (this.$route.query.isExist == "false") {
          this.isExistStartSite = false;
        } else if (this.$route.query.isExist == "true") {
          this.isExistStartSite = true;
        } else {
          this.isExistStartSite = this.$route.query.isExist;
        }

        if (!this.serviceStartSiteResId) {
          //没选楼层
          let localtionData = this.transportStartLocal;
          this.transportStartLocal = localtionData.substr(0, localtionData.length - 1);
        }
        if (this.olgWorkPushNew.departmentServiceLocationCascade == "1" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
          this.getDeptListByLocation(this.olgWorkPushNew.departmentServiceLocationCascade);
        }
      } else {
        //        判断服务区域是数组还是字符串
        if (Array.isArray(params.name)) {
          this.areaEndVal = params.name.join("");
          this.transportEndLocalName = params.name.join("");
          this.serviceEndSiteResId = params.code[params.code.length - 1];
          this.transportEndLocal = params.code.join(",");
        } else {
          //          最近服务区域返回的信息
          this.areaEndVal = params.name;
          this.transportEndLocalName = params.name;
          this.serviceEndSiteResId = params.code[params.code.length - 1];
          this.transportEndLocal = params.code.join(",");
        }
        if (this.$route.query.isExist == "false") {
          this.isExistEndSite = false;
        } else if (this.$route.query.isExist == "true") {
          this.isExistEndSite = true;
        } else {
          this.isExistEndSite = this.$route.query.isExist;
        }

        if (!this.serviceEndSiteResId) {
          //没选楼层
          let localtionData = this.transportEndLocal;
          this.transportEndLocal = localtionData.substr(0, localtionData.length - 1);
        }
        if (this.olgWorkPushNew.departmentServiceLocationCascade == "1" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
          this.getRecipientDeptListByLocation(this.olgWorkPushNew.departmentServiceLocationCascade);
        }
      }
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper {
  height: 100%;
  background-color: $bgColor;

  .content {
    box-sizing: border-box;
    min-height: 92%;
    padding-bottom: 76px;
    background-color: $bgColor;

    .reservation-btns {
      marginBottom20();
      text-align: center;

      span {
        display: inline-block;
        width: 1.24rem;
        margin: 0 0.36rem;
      }

      .active {
        color: $color;
        border-bottom: 2px solid $color;
      }
    }

    .hosp {
      marginBottom20();
    }

    .adjustable {
      padding: 0 0.32rem;

      > div {
        text-align: center;

        img {
          // width: 0.48rem;
          width: 14px;
        }

        .down {
          transition: all 0.3s;
        }

        .up {
          transform: rotateZ(180deg);
          transition: all 0.3s;
        }
      }
    }

    .up-content {
      transform: rotateX(90deg);
      transform-origin: center top;
      height: 0;
      transition: height 0.3s;
      transition: all 0.3s;
    }

    .down-content {
      transition: all 0.3s;
      transform-origin: center top;
      margin-bottom: 10px;
    }

    .er-level {
      display: flex;
      align-items: center;
      itemBaseStyle();

      .weui-cells_checkbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .redio-content {
          display: flex;
          color: $contentColor;

          .declare-attr {
            padding: 0;
          }
        }
      }
    }

    .item-style {
      itemBaseStyle();
    }

    .desc-form {
      margin-top: $marginb;

      .desc {
        padding: 0.32rem 0.32rem 0;

        textarea {
          display: inline-block;
          text-indent: 2em;
        }
      }
    }
  }
}

.btn {
  padding: 0 1.12rem 0.3rem;
  position: relative;
  user-select: none;

  .delete-voice {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.5rem;
  }

  .play-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
    }
  }
}

.recording {
  position: fixed;
  z-index: 2;
  top: 2rem;
  left: 50%;
  margin-left: -1.2rem;

  img {
    width: 2.4rem;
    opacity: 0.7;
  }
}

.title-content {
  itemBaseStyle();
  margin-top: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ipt-content {
  text-align: right;
  display: flex;
  align-items: center;
  color: #999;
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;

  .img-wrapper {
    flex: 1;
    width: 0;
    margin: 0.2rem;
    position: relative;

    img {
      width: 100%;
    }

    .icon-img {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
  position: fixed;
  bottom: 0;
  z-index: 999;
}

.play-void-img {
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
}

.title {
  font-size: 0.32rem;
}

.er-level-redio {
  display: flex;
  background-color: #fff;
  font-size: 0.3rem;
  color: $contentColor;
}

.redio-content {
  position: relative;

  .select-time {
    position: absolute;
    left: 1rem;
    bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 5em);

    .text {
      width: 50px;
      line-height: initial;
    }
  }

  .textColor {
    color: #999;
  }

  .disPointer {
    pointer-events: none;
  }
}

.hint {
  display: inline-block;
  margin: 0 3px;
}

.reminder{
  height:0.68rem;
  background:#FAECE9;
  padding-left:.32rem;
  line-height:.68rem;
  .reminder-img{
    width:.28rem;
  }
  .reminder-text{
    margin-left:.24rem;
    color:#FF7859;
    font-size:.26rem;
  }

}

.ellipsis{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.item-box {
  display: flex;
}
.item-box>div:first-child {
  margin-right: 40px;
}
.hide-box {
  width: 82px;
  height: 36px;
  background-color: #fff;
  border-radius: 100px 100px 100px 100px;
  border: 1px solid #E5E6EB;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
>>> .van-field__label{
  font-size:0.31rem;
  color:#353535
}
>>> .van-field__control{
  font-size:0.30rem;
}
</style>
