<template>
  <!--服务事项选择联动-->
  <div class="options">
    <div class="navtop">
      <div :class="{active:open==='options1'}" @click="switchoverone()">{{navArr[0]}}</div>
      <div :class="{active:open==='options2'}" @click="switchovertwo" v-if="navif2">{{navArr[1]}}</div>
      <div :class="{active:open==='options3'}" v-if="navif3">{{navArr[2]}}</div>
    </div>
    <div class="info">
      <!--列表事项-->

      <ul class="info-list">
        <li
          v-show="Level==1"
          v-for="(item, i) of allServiceMatters"
          :key="i"
          @click="handle(item.itemTypeCode, item.nodelevel,item)"
          :class="{on:i%2==0,off:i%2!=0}"
        >{{item.itemTypeName}}</li>
        <li
          v-show="Level==2"
          v-for="(i,k) of allServiceMatters"
          :key="'e'+k"
          @click="handle(i.itemDetailCode,i.nodelevel,i)"
          :class="{on:k%2==0,off:k%2!=0}"
        >{{i.itemDetailName}}</li>
        <li
          v-show="Level==3"
          v-for="(i,k) of allServiceMatters "
          :key="'s'+k"
          @click="shang(i)"
          :class="{on:k%2==0,off:k%2!=0}"
        >{{i.itemServiceName}}</li>
      </ul>
    </div>
  </div>
</template>

<script type=text/ecmascript-6 >
import global from "@/utils/Global";
import qs from "qs";
export default {
  props: {
    request: {
      type: Function
    },
    navArr: {
      type: Array,
      default: () => {
        return ["一级", "二级", "三级"];
      }
    }
  },
  name: "Levellinkage",
  data() {
    return {
      allServiceMatters: {}, //服务事项数据
      open: "options1", //服务事项选中,默认第一级
      Level: 1, //选择级别,默认第一级
      item2: {}, //返回参数
      selectedData: "",
      typecode: "", //返回二级数据的参数
      navif2: false,
      navif3: false
    };
  },
  methods: {
    /**
     * 向父组件传递数据
     */
    toParent() {
      this.$emit("itemval", this.item2, this.selectedData);
    },
    /**
     * 高亮切换到一级
     */
    switchoverone() {
      //
      this.open = "options1";
      this.Level = 1;
      this.navif2 = false;
      this.navif3 = false;
      this.requestLists(1);
    },
    switchovertwo() {
      if (this.Level == 3) {
        this.handle(this.typecode, 1);
      }
    },
    /**
     * 点击切换到下一级
     * @param {e} Code
     * @param {level} 请求级别
     * @qaram {open}  高亮显示
     *
     */

    handle(e, level, item) {
      this.allServiceMatters = this.allitemname;
      this.selectedData = item;
      this.open = "options2";
      if (level == 1) {
        if (e) {
          this.requestLists(2, e);
          this.typecode = e;
          this.Level = 2;
          this.$emit("typecode", e);
          this.navif2 = true;
          this.navif3 = false;
        } else {
          item.itemServiceCode = "";
          item.itemServiceName = "";
          item.itemDetailName = "";
          this.shang(item);
        }
      } else if (level == 2) {
        if (e) {
          this.navif3 = true;
          this.open = "options3";
          this.requestLists(3, e);
          this.Level = 3;
          this.$emit("DetailCode", e);
        } else {
          item.itemServiceCode = "";
          item.itemServiceName = "";
          item.itemDetailName = "";
          this.shang(item);
        }
      }
    },
    /**
     * 点击最后的选项向父组件传递数据
     */
    shang(i) {
      this.item2 = i;
      this.toParent();
    },

    requestLists(params, e) {
      params = params || 1;
      e = e || "";
      $.showLoading();
      this.request(params, e).then(res => {
        this.allServiceMatters = res;
      });
    }
  },
  watch: {},
  mounted() {
    this.requestLists();
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.on {
  background-color: #FCFCFC;
}

.off {
  background-color: #fff;
}

.options {
  background-color: #fff;
  height: 100%;

  // overflow:auto;
  .navtop {
    color: #999;
    font-size: calc(16px * var(--font-scale))!important;
    padding-left: 0.33rem;
    // padding-top: 0.2rem;
    border-bottom: 1px solid #E5E5E5;

    > div {
      // width: 1.27rem;
      line-height: 1rem;
      margin-right: 0.8rem;
      display: inline-block;
      text-align: center;
      font-weight: 500;
    }
  }

  .info {
    background-color: #fff;
    height: 68%;
    color: #1D2129;
    font-size: 0.3rem;
    overflow: auto;

    .info-list {
      font-size: 0.3rem;
      padding-bottom: 50px;
    }

    .info-list>li {
      padding: 0rem 0.3rem;
      font-size: calc(16px * var(--font-scale))!important;
      // line-height: 1rem;
      line-height :.35rem;
      padding: 0.32rem 0.3rem;
      width: 93%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

.active {
  color: #3562DB;
  border-bottom: 2px solid #3562DB;
}
</style>
