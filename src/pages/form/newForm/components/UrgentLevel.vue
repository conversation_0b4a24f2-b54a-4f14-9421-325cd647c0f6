<template>
  <div>
    <div class="border-bottom">
      <div class="item-style">
        <div class="er-text title">紧急程度</div>
      </div>
      <div class="weui-cells_checkbox">
        <div class="redio-content er-level-redio border-bottom">
          <label class="weui-cell weui-check__label" v-for="(item,idx) of urgentArr" :key="idx">
            <div class="weui-cell__hd">
              <input
                  type="radio"
                  class="weui-check"
                  name="checkbox1"
                  :value="item.dictValue"
                  v-model="urgent"
              >
              <i class="weui-icon-checked"></i>
            </div>
            <div class="weui-cell__bd">{{ item.dictLabel }}</div>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
//属性重复
  export default {
    name: "UrgentLevel",
    props:["urgentArr"],
    data () {
      return {
        urgent: '',
      }
    },
    methods:{
      changeUrgent (val) {
        this.urgent = val
        console.log('urgent',this.urgent)
      }
    },
    watch:{
      urgentArr (val) {
        this.urgent = this.urgentArr[0].dictValue
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import '~styles/varibles.styl';
  @import '~styles/mixins.styl';
  .item-style
    itemBaseStyle();
  .title
    font-size: .32rem
  .er-level-redio
    display flex
    background-color: #fff
    font-size: .3rem
    color: $contentColor
</style>
