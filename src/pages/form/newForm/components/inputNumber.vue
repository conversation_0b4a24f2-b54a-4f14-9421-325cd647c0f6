<template>
  <div class="coustom-number">
    <div
      class="number-sub"
      @click="sub"
    >
      <img
        src="@/assets/images/icon/ic-sub.png"
        alt
        srcset
      />
    </div>
    <div class="number">
      <input   
      class="weui-input ipt"
       type="number"
       @change="variation"
       oninput="if(value.length > 4)value = value.slice(0, 4)" 
       @input="oninput"
       v-model="num" placeholder="" />
    </div>
    <div
      class="number-add"
      @click="add"
    >
      <img
        src="@/assets/images/icon/ic-add.png"
        alt
        srcset
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      transportNum: 0
    };
  },
  props: {
    num: {
      type: Number,
      default: 0
    }
  },
  created() {
    this.$nextTick(() => {
      this.transportNum = this.num || 0;
    });
  },
  methods: {
    oninput(e) {
      e.target.value = e.target.value.replace(/[^\d]/g,'');
      this.num = e.target.value;
    },
    variation(){
      this.$emit('numVariation',this.num);
    },
    add() {
      // this.num++;
      this.$emit('numberChange','add');
    },
    sub() {
      // if (this.num > 0) {
      //   this.num--;
      // }
      this.$emit('numberChange','sub');
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
.coustom-number {
  display: flex;
  justify-content: flex-end;
  width: 55%;
  float: right;

  .number-add, .number-sub {
    margin: 0;
    text-align: center;

    img {
      width: 55%;
      margin: 0 5px;
      margin-top: 2px;
    }
  }

  .number {
    width: 110px;
    font-size: 15px;
    color: #353535;
    background: #efeff4;

    input {
      text-align: center;
    }
  }
}
</style>
