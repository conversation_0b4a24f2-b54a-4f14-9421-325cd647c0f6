<template>
  <div class="matters-wrapper">
    <Header title="服务事项" @backFun="goback"></Header>
    <div
      class="ipt-header"
      v-show="initPage"
    >
      <div class="search-ipt">
        <span class="iconfont">&#xe60a;</span>
        <input
          v-model="searchVal"
          id="search"
          type="text"
          maxlength="50"
          placeholder="请输入关键字"
        />
        <span class="line"></span>
        <span
          class="cancle"
          @click="back()"
        >取消</span>
      </div>
    </div>

    <!-- 查询内容列表 -->
    <ul
      v-show="searchList && searchString.length > 0"
      class="search-list"
    >
      <li
        v-for="(item,index) of searchString "
        :key="index"
        @click="goup(item)"
      >{{item.itemServiceName}}</li>
    </ul>

    <div class="header">
      <div>
        <div id="gosearch">
          <div
            class="ipt-area"
            @click="watchSearchBoxHidden"
          >
            <span class="value">
              <span class="iconfont">&#xe60a;</span>
              <span>搜索</span>
            </span>
            <span
              class="value1"
              ref="value1"
            >{{ value }}</span>
          </div>
        </div>
      </div>
    </div>
    <div id="letter"></div>
    <div class="sort_box-two"></div>

    <div
      class="hot-search"
      v-if="relance"
      v-show="$route.query.source !== 'custom'"
    >
      <p class="search-title">
        <span class="text title-style">服务事项Top5</span>
      </p>
      <ul class="event-lists">
        <li
          class="item"
          v-for="(item,index) of hotLists"
          :key="index"
          :class="{active:index==n}"
          @click="changeList(index),goup(item)"
        >{{ item.itemServiceName }}</li>
      </ul>
    </div>
    <div
      class="matters-content"
      :class="{'matters-content2' : $route.query.source === 'custom'}"
      id="content"
      v-if="relance"
    >
      <div class="matters-title">
        <span class="title-style">服务事项</span>
      </div>
      <LevelLinkage
        :request="requestNew"
        :navArr="navlist"
        ref="LevelLinkage"
        @itemval="change($event)"
      />

    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import global from "@/utils/Global";
import { mapState } from "vuex";
import LevelLinkage from "./LevelLinkage";
import fontSizeMixin from "@/mixins/fontSizeMixin";

export default {
  name: "ServiceMatters",
  components: {
    LevelLinkage
  },
  mixins: [fontSizeMixin],
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  data() {
    return {
      isOriginHei: true,
      screenHeight: document.documentElement.clientHeight,
      originHeight: document.documentElement.clientHeight,
      canShow: false,
      hotLists: [],
      n: -1,
      workTypeCode: "",
      routeSearch: "",
      allServiceMatters: [],
      checkedValue: "",
      value: "",
      searchBox: true,
      relance: true,
      resultObj: {},
      initPage: false,
      unitCode: "", // 目的存储扫码进来时携带的医院信息
      hospitalCode: "", // 目的存储扫码进来时携带的医院信息
      bottomDistance: false, //ios加下边距
      changStyle: false, //右侧字母列表是否使用固定定位

      itemlist: {}, //子组件传递过来的数据
      navlist: [
        //列表导航
        "类别",
        "事项",
        "明细"
      ],
      stores: "",
      searchVal: "", //搜索输入内容
      searchString: [],
      searchList: false //查询出的列表
    };
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    /**
     * 接收子组件的参数
     */
    change(data,selected) {
      this.level = 3;
      this.itemlist = data;
      this.goup(this.itemlist);
    },
    /**
     * 切换top5时样式
     * @param index 当前项的index
     */
    changeList(index) {
      if (this.n != index) {
        this.n = index;
        this.checkedValue = "";
        this.searchBox = true;
        this.value = "";
      } else {
        this.n = -1;
      }
    },
    /**
     * 点击最后的选项返回上个页面
     */
    goup(e) {
      let path = this.$route.query.projectName;
      if (this.$route.query.source == "revise" || this.$route.query.source == "custom") {
        if(e.itemServiceCode == ""){
          e.itemServiceName = ""
        }
        //这里处理修改的逻辑
        this.$store.commit("reviseServiceMatters", e);
        this.$router.go(-1);
      } else {
        this.$router.replace({
          path: path,
          query: {
            item: e,
            source: "service",
            cache: true
          }
        });
        this.$router.go(-1);
      }
    },
    /**
     * 服务事项列表请求函数
     */
    // requestLists () {
    //   $.showLoading()
    //   if(this.loginInfo && !this.unitCode){ //this.unitCode如果存在说明是扫码携带了unitCode信息，不需要登录信息中的unitCode
    //     this.$api.getItemTypeList({
    //       workTypeCode: this.workTypeCode
    //     }).then(this.requestListsSuccess)
    //   }else{
    //     this.axios.get(__PATH.ONESTOP + "/appOlgTaskManagement.do?getItemTypeListNew",{
    //       params:{
    //         hospitalCode: this.hospitalCode,
    //         unitCode: this.unitCode,
    //         workTypeCode: this.workTypeCode
    //       }
    //     }).then(this.requestListsSuccess)
    //   }
    // },
    requestNew(level, parentCode, fuzzySearch) {
      return new Promise((resolve, reject) => {
        this.$api
          .getItemTypeList({
            workTypeCode: this.$route.query.workTypeCode, //测试数据
            level: level,
            parentCode: parentCode,
            fuzzySearch: fuzzySearch
          })
          .then(res => {
            //
            if (level != 1 && this.$route.query.source == "custom") {
              if(!res.itemList[0]){
                  res.itemList[0] = this.$refs.LevelLinkage.selectedData;
              }
              let defaultStr = { ...res.itemList[0] }; //res.itemList[0];
              if (level == 2) {
                defaultStr.itemDetailCode = "";
                defaultStr.itemDetailName = "暂不选择";
              } else {
                defaultStr.itemServiceCode = "";
                defaultStr.itemServiceName = "暂不选择";
              }
              res.itemList.unshift(defaultStr);
            }
            resolve(res.itemList);
            this.requestListsSuccess(res);
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    //模糊查询请求函数
    searchRequest(fuzzySearch) {
      this.$api
        .getItemTypeList({
          workTypeCode: this.$route.query.workTypeCode, //测试数据
          level: 3,
          fuzzySearch: fuzzySearch
        })
        .then(res => {
          this.searchString = res.itemList;
        });
    },
    back() {
      global.back();
      this.relance = !this.relance;
      this.searchList = false;
      this.initPage = false;
      this.searchVal = "";
    },
    /**
     * 服务事项列表请求成功
     * @param res
     */
    requestListsSuccess(res) {
      $.hideLoading();
      // if(res.data && res.data.code == 200){
      //   res = res.data.data
      // }
      this.allServiceMatters = res.itemList;
      this.hotLists = res.itemTop5;
      global.pyList(
        this.watchSearchBoxHidden,
        this.setSearchValue,
        this.clearRadio
      );
    },
    /**
     * 监控搜索栏隐藏
     */
    watchSearchBoxHidden() {
      this.searchBox = false;
      this.relance = false;
      this.initPage = true;
    },
    /**
     * 回显到搜索栏
     */
    setSearchValue(res) {
      this.value = res;
      this.relance = true;
      this.checkedValue = res;
    },
    /**
     * 单项选择清空
     */
    clearRadio() {
      this.checkedValue = "";
      this.n = -1;
    },
    /**
     * 点击确定
     * @param res
     */
    submitVal() {
      //
      this.goup(this.stores);

      // if(this.n == -1){
      //   if(this.value == ''){
      //     //点击选择列表中的选项
      //     this.filterResulter(this.checkedValue)
      //   }else{
      //     //通过搜索中进行选择的
      //     this.filterResulter(this.value)
      //   }
      // }else{
      //   //选择热搜中的内容
      //   this.resultObj = this.hotLists[this.n]
      // }
      // this.$router.replace({
      //   // path:this.routeSearch,
      //   path:"/repair",
      //   query:{
      //     // matterlistsObj:this.resultObj
      //   }
      // })
    },
    /**
     * 根据所选项的名称筛选出其他相关信息
     */
    filterResulter(resultName) {
      for (let i = 0; i < this.allServiceMatters.length; i++) {
        if (this.allServiceMatters[i].itemServiceName == $.trim(resultName)) {
          this.resultObj = this.allServiceMatters[i];
        }
      }
    },
    /**
     * 判断手机终端（安卓、ios）
     */
    downApp() {
      if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
        //Ios
        this.bottomDistance = true;
      } else if (/(Android)/i.test(navigator.userAgent)) {
        //Android终端
      }
    },
    handleScroll() {
      //改变元素#searchBar的top值
      var scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop; //页面滚动的距离
      // var offsetTop = document.querySelector('#content').offsetTop ;//元素距离顶部的距离
      // if(scrollTop > offsetTop){
      //   this.changStyle = true
      // }else{
      //   this.changStyle = false
      // }
    },
    beforeDestroy() {
      window.removeEventListener("scroll", this.handleScroll);
    }
  },
  watch: {
    // 监控搜索框输入内容
    searchVal() {
      if (!this.searchVal) return (this.searchList = false);
      this.searchRequest(this.searchVal);
      this.searchList = true;
    },
    checkedValue(val, lastVal) {
      if (lastVal == "") {
        //没有被选择的
        this.searchBox = true;
        this.value = "";
        this.n = -1;
      } else {
        //已经有选择项了
        if (this.searchBox) {
          this.searchBox = true;
        }
      }
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    let self = this;
    window.onresize = function() {
      return (function() {
        self.screenHeight = document.documentElement.clientHeight;
      })();
    };
    this.workTypeCode = this.$route.query.workTypeCode;
    this.unitCode = this.$route.query.unitCode;
    this.hospitalCode = this.$route.query.hospitalCode;
    // this.requestLists()
    this.requestNew(1);
    this.downApp();
    window.addEventListener("scroll", this.handleScroll);
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.routeSearch = from.fullPath;
    });
  },
  beforeRouteLeave(to, from, next) {
    this.beforeDestroy();
    next();
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.matters-wrapper >>> .sort_letter {
  padding-left: 5px;
}

.matters-wrapper >>> .sort_box-two {
  .sort_list-two {
    padding: 10px 0.4rem;
    background-color: #fff;
  }
}

.sort_list {
  padding-right: 45px;
}

.matters-wrapper {
  height: 100vh;
  overflow-y: hidden;
  background-color: #F2F4F9;

  .ipt-header {
    width: 100%;
    height: 0.78rem;
    line-height: 0.78rem;
    padding: 0.2rem 0rem;
    background-color: #fff;
    position: fixed;

    .search-ipt {
      position: relative;

      .iconfont {
        position: absolute;
        left: 0.4rem;
        top: 0rem;
        color: $textColor;
      }

      #search {
        background-color: $bgColor;
        padding-left: 0.8rem;
        padding-right: 1.1rem;
        box-sizing: border-box;
        height: 0.78rem;
        display: inline-block;
        width: 95%;
        margin-left: 2.5%;
        border-radius: 5px;
      }

      .line {
        width: 1px;
        height: 0.3rem;
        background-color: #D3D3D5;
        display: inline-block;
        position: absolute;
        right: 1.1rem;
        top: 0.24rem;
      }

      .cancle {
        position: absolute;
        right: 7px;
        top: 0;
        width: 1rem;
        text-align: center;
      }
    }
  }

  .header {
    #gosearch {
      height: 0.7rem;
      background-color: #fff;
      padding: 0.2rem;

      .ipt-area {
        height: 0.78rem;
        background-color: #F2F3F5;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        color:$contentColor;

        .iconfont {
          padding-right: 7px;
        }
      }
    }
  }

  .hot-search {
    background-color: #fff;
    margin-bottom: 0.2rem;

    .search-title {
      padding: 0.2rem 0.32rem;

      .iconfont {
        color: #d6d6d8;
        font-size: 0.32rem;
        vertical-align: middle;
      }

      .text {
        color: $textColor;
        font-size: calc(16px * var(--font-scale))!important;
        vertical-align: middle;
      }
    }

    .event-lists {
      overflow: hidden;
      background-color: #fff;
      padding: 0.1rem 0.16rem 0.4rem;

      .item {
        padding: 0.2rem 0.45rem;
        margin: 0.1rem 0.16rem;
        float: left;
        background-color: #f7f8fa;
        color: #4E5969;
        border-radius: 5px;
        font-size: calc(16px * var(--font-scale))!important;
      }

      .active {
        background-color: #ebf9f9;
        color: #1BAD97;
      }
    }
  }
 
  .matters-content {
    height: calc(100% - 4.6rem);
    overflow: hidden;

    // position: relative
    .initials {
      // position: absolute
      right: 0;
      top: 0.1rem;
      z-index: 10;
      width: 25px;
      height: auto !important;

      ul {
        text-align: center;
      }
    }

    .changStyle {
      // position: fixed
      top: 0.5rem;
    }

    .matters-title {
      color: $textColor;
      padding-top: 0.1rem;
      padding-bottom: 0.1rem;
      padding-left: 0.32rem;
      background-color: #FFF;

      >img {
        width: 0.3rem;
      }

      >span {
        position: relative;
        top: 0.09rem;
        left: 0;
      }
    }

    .matters-lists {
      margin-top: 0;

      .list {
        font-size: 0.32rem;
        line-height: 0.88rem;
        padding-left: 0.2rem;
        background: #fff;
        color: $darkTextColor;
      }
    }
  }
  .matters-content2{
    height: calc(100% - 0.6rem) ;
  }

  .button-wrapper {
    width: 100%;
    height: 1.3rem;
    padding: 0.22rem 0.32rem;
    box-sizing: border-box;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    border-top: 1px solid $bgColor;
    z-index: 20;

    .btn {
      background-color: $btnColor;
      font-size: 0.36rem;
      color: #fff;
      border-radius: 3px;
      text-align: center;
      height: 0.88rem;
      line-height: 0.88rem;
    }
  }
}

.search-list {
  width: 100%;
  max-height: calc(100% - 1.19rem);
  background-color: #fff;
  font-size: 0.3rem;
  overflow: auto;
  padding-top: 1.19rem;

  >li {
    line-height: 1rem;
    color: $contentColor;
    padding-left: 0.6rem;
  }
}
.title-style {
  font-size: calc(16px * var(--font-scale))!important;
  color: #1D2129!important;
}
</style>
