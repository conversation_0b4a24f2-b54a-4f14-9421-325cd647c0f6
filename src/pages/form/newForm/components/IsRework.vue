<template>
  <div>
    <div class="er-level border-rightbottom">
      <div class="weui-cells_checkbox">
        <div class="er-text title">返修工单</div>
        <div class="redio-content">
          <label class="weui-cell weui-check__label" for="s15">
            <div class="weui-cell__hd">
              <input
                  type="radio"
                  class="weui-check"
                  name="checkbox3"
                  id="s15"
                  checked="checked"
                  value="1"
                  v-model="isRework"
              >
              <i class="weui-icon-checked"></i>
            </div>
            <div class="weui-cell__bd">否</div>
          </label>
          <label class="weui-cell weui-check__label" for="s16">
            <div class="weui-cell__hd">
              <input
                  type="radio"
                  name="checkbox3"
                  class="weui-check"
                  id="s16"
                  value="2"
                  v-model="isRework"
              >
              <i class="weui-icon-checked"></i>
            </div>
            <div class="weui-cell__bd">是</div>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "IsRework",
    data () {
      return {
        isRework: '1',
      }
    },
    methods:{
      changeRework (val) {
        this.isRework = val
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import '~styles/varibles.styl';
  @import '~styles/mixins.styl';
  .er-level
    display: flex
    align-items: center
    itemBaseStyle()
    .weui-cells_checkbox
      display: flex
      align-items: center
      justify-content: center
      justify-content: space-between
      width: 100%
      .redio-content
        display: flex
        color: $contentColor
        font-size: .3rem
</style>
