<template>
  <div class="popup_content">
    <van-popup v-model="isShowPicker" round position="bottom" @close="$emit('cancel')">
      <div class="box">
        <!-- tab -->
        <van-tabs v-model="active" color="#098ae8" title-active-color="#098ae8" @click="handleTabClick">
          <van-tab title="默认" name="0"></van-tab>
          <van-tab title="全部" name="1"></van-tab>
        </van-tabs>
        <!-- 搜索 -->
        <van-search v-model="teamName" show-action placeholder="请输入搜索关键词" @clear="onClear" @search="onSearch">
          <template #action>
            <div @click="onSearch">搜索</div>
          </template>
        </van-search>
        <!-- 列表 -->
        <van-list v-model="loading" :immediate-check="false" :finished="finished" finished-text="没有更多了" @load="onLoad" error-text="请求失败，点击重新加载" :error.sync="error">
          <div v-if="isLoading" style="height: 100%; display: flex; justify-content: center; align-items: center">
            <van-loading type="spinner" color="#098ae8" />
          </div>
          <template v-else>
            <van-cell v-for="item in teamList" :key="item.id" :title="item.team_name" @click="handleDoubleClick(item)" class="van-clearfix"> </van-cell>
          </template>
          <template #finished v-if="!isLoading && !error">
            <div v-show="teamList.length === 0 && active === '0'">
              <div>暂无默认数据，请切换全部列表查看</div>
              <div style="color: #81d3f8" @click="loadViewAll">查看全部</div>
            </div>
            <div v-show="teamList.length !== 0">没有更多了</div>
          </template>
        </van-list>
      </div>
      <!-- <van-picker show-toolbar :columns="teamList" @cancel="$emit('cancel')" @confirm="val => $emit('confirm', val)" value-key="team_name" /> -->
    </van-popup>
  </div>
</template>

<script>
export default {
  name: "publicServiceDepartment",
  components: {},
  mixins: [],
  props: {
    showPicker: {
      type: Boolean,
      default: false
    },
    dataObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isShowPicker: false,
      active: "0",
      teamName: "",
      teamList: [],
      error: false,
      loading: false,
      finished: false,
      isLoading: false
    };
  },
  created() {},
  mounted() {},
  methods: {
    onLoad() {
      this.isLoading = true;
      this.active === "0" ? this.getTeamList() : this.getAllTeamList();
    },
    // 全部接口调用
    getAllTeamList() {
      const { workTypeCode } = this.dataObj;
      const params = {
        // localtionId: "", // 服务地点
        // matterId: "", // 服务事项
        workTypeCode,
        teamName: this.teamName
      };
      this.$api
        .getTeamsByTaskAll(params)
        .then(res => {
          this.teamList = res.list || [];
          if (this.teamList.length >= res.total || this.teamList.length == 0) {
            this.finished = true; // 数据全部加载完成
          } else {
            this.finished = false; // 数据未加载完成
          }
        })
        .catch(() => {
          this.error = true; // 数据加载失败
          this.finished = true; // 数据全部加载完成
        })
        .finally(() => {
          this.isLoading = false;
          this.loading = false;
        });
    },
    // 默认接口
    getTeamList() {
      this.teamList = []
      const { workTypeCode, localtionId, matterId } = this.dataObj;
      const params = {
        localtionId, // 服务地点 / 服务房间
        matterId, // 服务事项
        workTypeCode,
        teamName: this.teamName
      };
      this.$api
        .getTeamsByTask(params)
        .then(res => {
          this.teamList = res.list || [];
          this.teamList.unshift({
            id:'ddzx123456789',
            team_name:"调度中心"
          })
          if (this.teamList.length >= res.total || this.teamList.length == 0) {
            this.finished = true; // 数据全部加载完成
          } else {
            this.finished = false; // 数据未加载完成
          }
        })
        .catch(() => {
          this.error = true; // 数据加载失败
          this.finished = true; // 数据全部加载完成
        })
        .finally(() => {
          this.isLoading = false;
          this.loading = false;
        });
    },
    // 查看全部
    loadViewAll() {
      this.active = "1";
      // 调用接口
      this.getAllTeamList();
    },
    handleTabClick(val) {
      this.teamName = "";
      val === "0" ? this.getTeamList() : this.getAllTeamList();
    },
    // 单机某一项
    handleDoubleClick(item) {
      this.$emit("confirm", item);
    },
    // 搜索
    onSearch() {
      this.active === "0" ? this.getTeamList() : this.getAllTeamList();
    },
    // 清除
    onClear() {
      this.active === "0" ? this.getTeamList() : this.getAllTeamList();
    }
  },
  computed: {},
  watch: {
    showPicker: {
      handler(val) {
        if (val) {
          this.teamName = "";
          this.active = "0";
          this.onLoad();
        }
        this.isShowPicker = val;
      },
      immediate: true
    }
  }
};
</script>
<style lang="scss" scoped>
.popup_content {
  .van-popup {
    height: 75%;
    .box {
      height: 100%;
      .van-list {
        height: calc(100% - 98px);
        overflow-y: auto;
      }
    }
  }
}
.van-cell {
  font-size: calc(16px * var(--font-scale))!important;
}
</style>