<!--
 * @Author: <PERSON>
 * @Date: 2021-03-12 14:54:27
 * @LastEditors: <PERSON>
 * @LastEditTime: 2021-05-17 19:25:29
 * @Description: file content
 * @FilePath: \wechat\src\pages\form\newForm\components\DeclareAttribute.vue
-->
<template>
  <div>
    <div class="border-bottom">
      <div class="item-style">
        <div class="er-text title star">申报属性</div>
      </div>
      <div class="weui-cells_checkbox">
        <div class="redio-content er-level-redio border-bottom">
          <label
            class="weui-cell weui-check__label"
            style="white-space: nowrap;padding: 10px 7.5px;display: inline-flex;"
            :for="item.dictValue + 5"
            :key="idx"
            v-for="(item,idx) of declareAttributeArr"
             v-if="item.dictValue != 5"
          >
            <div class="weui-cell__hd">
              <input
                type="radio"
                class="weui-check"
                name="checkboxsx"
                :id="item.dictValue + 5"
                :value="item.dictValue"
                checked="checked"
                v-model="attr"
              />
              <i class="weui-icon-checked"></i>
            </div>
            <div>{{ item.dictLabel }}</div>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
export default {
  name: "DeclareAttribute",
  props: ["declareAttributeArr"],
  data() {
    return {
      attr: ""
    };
  },
  methods:{
    changeAttr(val){
      this.attr = val
    }
  },
  watch: {
    declareAttributeArr(val) {
      // this.attr = this.declareAttributeArr[0].dictValue;
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.item-style {
  itemBaseStyle();
}

.title {
  font-size: 0.32rem;
}

.er-level-redio {
  display: flex;
  background-color: #fff;
  font-size: 0.3rem;
  color: $contentColor;
}

.er-level {
  display: flex;
  align-items: center;
  itemBaseStyle();

  .weui-cells_checkbox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .redio-content {
      display: flex;
      color: $contentColor;
      flex: 1;
      font-size: 0.3rem;

      .declare-attr {
        padding: 0;
        width: 50%;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
.star {
  position: relative;
  &::before {
    content: '*';
    color: red;
    position: absolute;
    left: -0.4em;  // 调整为相对父元素的位置
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
