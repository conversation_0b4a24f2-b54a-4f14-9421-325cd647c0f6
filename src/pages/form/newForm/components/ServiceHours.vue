<template>
  <div>
    <div class="weui-cell border-rightbottom item-style">
      <div class="weui-cell__hd"><label class="weui-label title">预约时间</label></div>
      <div class="weui-cell__bd ipt-content" @click="toTime" >
        <input
            class="weui-input ipt"
            type="text"
            readonly
            ref="datetime"
            v-model="reservation"
            placeholder="请选择预约服务时间"
        >
        <van-icon style="margin: 0 24px" color="#999" name="arrow" />
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import moment from "moment"
  export default {
    name: "ServiceHours",
    data () {
      return {
        reservation: '',  //预约时间
//        timeType: true,
//        first: true
      }
    },
    methods: {
      /**
       * 时间比较（所选时间不能大于当前时间）
       * @param startTime 当前时间
       * @param endTime  所选时间
       */
      //时间比较（yyyy-MM-dd HH:mm:ss）
      compareTime (startTime, endTime) {
        let startTimeStyle = startTime
        let startTimes = startTime.substring(0, 10).split('-')
        let endTimes = endTime.substring(0, 10).split('-')
        startTime = startTimes[1] + '-' + startTimes[2] + '-' + startTimes[0] + ' ' + startTime.substring(10, 19)
        endTime = endTimes[1] + '-' + endTimes[2] + '-' + endTimes[0] + ' ' + endTime.substring(10, 19)
        let thisResult = (Date.parse(endTime.replace(/-/g, '/')) - Date.parse(startTime.replace(/-/g, '/'))) / 3600 / 1000
        if (thisResult < -0.02) {
          $.toast("应大于当前时间", "text")
          this.reservation = startTimeStyle
        }
      },
      add0(m) {
        return m < 10 ? "0" + m : m;
      },
      format (shijianchuo) {
        var time = new Date(shijianchuo);
        var y = time.getFullYear();
        var m = time.getMonth()+1;
        var d = time.getDate();
        var h = time.getHours();
        var mm = time.getMinutes();
        var s = time.getSeconds();
        return y+'-'+this.add0(m)+'-'+this.add0(d)+' '+this.add0(h)+':'+this.add0(mm)+':'+this.add0(s);
      },
      /**
       * 点击请选择预约
       */
      toTime () {
        this.$refs.datetime.click()
      }
    },
    mounted () {
      this.reservation = moment(new Date()).format("YYYY-MM-DD HH:mm");
      let _this = this
      const nTime = new Date(); //限制时间大于当前（日)
      $(this.$refs.datetime).datetimePicker({
        title: "请选择预约时间",
        onChange: function (result) {
          let bir = []
          result.value.forEach((i, index) => {
            bir.push(i)
          })
          let reservation = bir[0] + "-" + bir[1] + "-" + bir[2] + " " + bir[3] + ":" + bir[4]
          _this.reservation = reservation
          _this.compareTime(_this.format(nTime),reservation)
        }
      })
    },
    /*watch: {
      time(curTimeType) {
        this.timeType = !this.timeType
        if (curTimeType == 1) {
          this.$refs.datetime.click()
          this.first = false
        }
      }
    }*/
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .item-style
    padding-right: 0!important
    itemBaseStyle()
    .ipt-content
      display flex
      align-items center
      .ipt
        flex: 1
        text-align: left!important

</style>