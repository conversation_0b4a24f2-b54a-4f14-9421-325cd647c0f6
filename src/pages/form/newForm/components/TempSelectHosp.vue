<template>
  <div>
    <div class="weui-cell border-rightbottom item-style hosp">
      <div class="weui-cell__hd"><label class="weui-label title">
        <span class="iconfont">&#xe63d;</span>
        <span>{{ hospitalInfo.province || province || '医院' }}</span>

      </label></div>
      <div class="weui-cell__bd ipt-content">
        <input
            class="weui-input ipt"
            type="text"
            readonly
            v-model="hospitalInfo.hospitalName || locationHosp.hospitalName"
            placeholder="请选择医院"
        >
        <em class="italic" @click="toSelectHosp">医院不对?</em>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import { mapMutations } from "vuex"
  export default {
    name: "TempSelectHosp",
    props:['hospitalInfo','isFirst'],
    data () {
      return {
        faultLocation:'',
        longitude: '',
        latitude: '',
        locationHosp:{},//经纬度离定位最近的那个医院
        province: '北京市',
        city: '北京市',
        district: '朝阳区'
      }
    },
    methods: {
      /**
       * 定位成功
       */
      showPosition(position) {
        this.province = position.province
        this.city = position.city
        this.district = position.district
        this.faultLocation = this.province + this.city + this.district + position.addr
        this.longitude = position.lng
        this.latitude = position.lat
        this.getHospitalInfo()
      },
      /**
       * 定位失败
       */
      showErr() {
         console.log('定位失败');
      },
      /**
       * 获取医院信息
       */
      getHospitalInfo () {
        $.showLoading()
        this.$api.getHospitalListAndLogoByUnitCode({
          province: this.province,//this.province
          city: this.province == this.city ? "" : this.city,//this.city
//          district: this.district//this.district
        }).then(this.handleGetHospInfoSucc)
      },
      /**
       * 获取医院信息成功函数
       * @param result  结果
       */
      handleGetHospInfoSucc (res) {
        let allHops = []
        res.forEach((item, index) => {
          //将state == 2、3、4 的医院不进行显示,只有0,1显示（开通状态（0已上线、1开通中、2暂停使用、3演示版、4公测版））
          if(item.state != 2 && item.state != 4 && item.state != 3 && (item.appList.length > 0 && (item.appList[0].appId == process.env.WxAppid))){
            allHops.push(item)
          }
        })

        $.hideLoading()
        let distanceArr = []
        let hasPointHosp = []
        for (let i = 0; i < allHops.length; i++) {
          if (allHops[i].xpoint && allHops[i].ypoint) {
            let xDifference2 = Math.pow(Math.abs(allHops[i].xpoint - this.latitude), 2)
            let yDifference2 = Math.pow(Math.abs(allHops[i].ypoint - this.longitude), 2)
            distanceArr.push(Math.sqrt(Math.abs(xDifference2 - yDifference2)))
            hasPointHosp.push(allHops[i])
          }
        }
        let distanceArrNew = distanceArr.slice(0)
        let sortArr = distanceArrNew.sort((a, b) => a - b)
        let sortIdx = distanceArr.findIndex(item => {
          return item == sortArr[0]
        })
        this.locationHosp = hasPointHosp[sortIdx]
        // this.changeHosp(this.locationHosp)
        sessionStorage.setItem("unitCode",this.locationHosp.unitCode)
        sessionStorage.setItem("hospitalCode",this.locationHosp.hospitalCode)
        this.$parent.getConfigParams()
        this.$emit("locationHopsInfo",this.locationHosp)
      },
      // ...mapMutations(['changeHosp']),
      /**
       * 点击‘医院不对？’跳转医院选择页面
       */
      toSelectHosp () {
        this.$router.push({
          path:'hospListNew',
          query:{
            pathName:this.$route.fullPath,
            province:this.province,
            city:this.city,
//            district:this.district
          }
        })
      }
    },
    created () {
      if(this.isFirst){
        var geolocation = new qq.maps.Geolocation();
        var options = {timeout: 9000};
        let that = this
        setTimeout( () => {
          if (!that.faultLocation){
            geolocation.getLocation(that.showPosition, that.showErr, options);
          }
        },200)
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .hosp
    marginBottom20()
  .iconfont
    color: $contentColor
    vertical-align middle
    &:nth-child(2)
      display: inline-block
      width: 60px
      white-space: nowrap
      text-overflow: ellipsis
      overflow: hidden
      vertical-align: middle
  .item-style
    itemBaseStyle()
    .ipt-content
      justify-content: flex-end
      line-height: 1.2
      text-align: left
      display flex
      align-items center
      .italic
        font-style italic
        white-space: nowrap
        color: $color
        font-size: .28rem
</style>
