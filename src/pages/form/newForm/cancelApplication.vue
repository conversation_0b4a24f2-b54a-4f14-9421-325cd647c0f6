<template>
  <div class="inner">
    <Header title="取消挂单申请" @backFun="goBack"></Header>
    <van-field v-model="approvalComment" rows="10" autosize label="取消原因" type="textarea" maxlength="500" placeholder="请输入取消原因" show-word-limit />
    <div class="btn">
      <van-button style="width: 45%" color="#3562db" @click="$router.go(-1)">取消</van-button><van-button style="width: 45%" color="#3562db" @click="submit">确定</van-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      approvalComment: ""
    };
  },

  created() {},
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    submit() {
      let params = {
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        userName: JSON.parse(localStorage.getItem("loginInfo")).staffName,
        businessId: this.$route.query.workNum,
        revokeType:'1',
        approvalComment:this.approvalComment,
      };
      this.$api.revokeApproval(params).then(res => {
        this.$toast.success("取消成功");
        this.$router.go(-1);
      });
    },

    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style   scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
</style>
