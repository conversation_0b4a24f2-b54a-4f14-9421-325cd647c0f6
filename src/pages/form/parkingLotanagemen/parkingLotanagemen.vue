<template>
  <div class="content">
    <Header title="停车场运营管理" @backFun="goback()"></Header>
    <div class="title">运营总览</div>
    <div class="btn_tags">
      <div v-for="(item, index) in tabArr" :key="index" class="btn_item" :class="dateType == item.id ? 'btn_item_active' : ''" @click="changeType(item.id)">
        {{ item.title }}
      </div>
    </div>
    <div class="cont">
      <div>
        <div>
          <div>
            <span class="caption">{{ parkingLot.parkingInTime || 0 }}</span
            >&nbsp;<span class="explain">辆</span>
          </div>
          <div class="explain2">入场车次</div>
        </div>
        <div>
          <div>
            <span class="caption">{{ parkingLot.parkingOutTime || 0 }}</span
            >&nbsp;<span class="explain">辆</span>
          </div>
          <div class="explain2">出场车次</div>
        </div>
      </div>
      <div>
        <div>
          <div>
            <span class="caption">{{ parkingLot.chargeTotal || 0 }}</span
            >&nbsp;<span class="explain">元</span>
          </div>
          <div class="explain2">实时收费总额</div>
        </div>
        <div>
          <div>
            <span class="caption">{{ parkingLot.chargeTime || 0 }}</span
            >&nbsp;<span class="explain">笔</span>
          </div>
          <div class="explain2">收费笔数</div>
        </div>
      </div>
    </div>
    <div class="statistics">
      <div class="title">车场监管统计</div>

      <div class="conts">
        <div>
          <div>
            <div>
              <span class="caption">{{ carYardStatisticsList.parkingSpaceTotal }}</span>
            </div>
            <div class="explain2">总车位数</div>
          </div>
          <div>
            <div>
              <span class="caption">{{ carYardStatisticsList.remainingSpace }}</span>
            </div>
            <div class="explain2">可用车位</div>
          </div>
          <div>
            <div>
              <span class="caption">{{ carYardStatisticsList.inUseSpace }}</span>
            </div>
            <div class="explain2">已用车位</div>
          </div>
        </div>
      </div>
      <div v-show="carYardStatisticsList != ''" id="carYardStatistics" class="echarts"></div>
      <div v-show="carYardStatisticsList == ''" class="executeCharts">
        <img height="100%" src="../../../assets/images/equipmentManagement/缺省-图表@2x.png" alt="" />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
    <div class="sty"></div>
    <div class="collectFees">
      <div class="title">出口收费统计</div>
      <div id="chargeStatistics" v-show="chargeStatisticsList != ''" class="echarts2"></div>
      <div v-show="chargeStatisticsList == ''" class="executeCharts">
        <img height="100%" src="../../../assets/images/equipmentManagement/缺省-图表@2x.png" alt="" />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      parkingLot: {},
      tabArr: [
        {
          id: "1",
          title: "今日"
        },
        {
          id: "2",
          title: "本周"
        },
        {
          id: "3",
          title: "本月"
        }
      ],
      dateType: "1",
      carYardStatisticsList: "",
      chargeStatisticsList: ""
    };
  },
  created() {
    this.changeType("1");
    this.getCarYardStatistics();
    this.getChargeStatistics();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    // 车场监管统计
    getCarYardStatistics() {
      this.$api.getParkingSuperviseInfo({}).then(res => {
        this.carYardStatisticsList = res;
        this.$nextTick(() => {
          this.electricityPieEchart(res.chartData, "carYardStatistics");
        });
      });
    },
    electricityPieEchart(arr, id) {
      const getchart = this.$echarts.init(document.getElementById(id));
      getchart.setOption({
        backgroundColor: "#fff",
        color: ["#00bc6d", "#3562db", "#ff9435", "#ffbe00"],
        legend: {
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemWidth: 10,
          itemHeight: 10,
          bottom: "10%",
          left: "center",
          formatter: name => {
            let sum = 0;
            arr.forEach(item => {
              sum = sum + Number(item.value);
            });
            const count = arr.find(i => i.name == name);
            return name + "  " + +count.value + " " + "占比：" + count.rate;
          }
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2
          },
          type: "pie",
          radius: "50%",
          center: ["50%", "35%"],
          data: arr,
          hoverAnimation: false,
          labelLine: {
            //指示线样式设置
            normal: {
              lineStyle: {
                color: "#4E5969" // 设置标示线的颜色
              }
            }
          },
          label: {
            normal: {
              textStyle: {
                color: "#4E5969"
              }
            }
          }
        }
      });
    },
    //出口收费统计
    getChargeStatistics() {
      this.$api.getExistChargeDetail({}).then(res => {
        this.chargeStatisticsList = res;
        this.$nextTick(() => {
          this.initChargeStatistics(this.chargeStatisticsList);
        });
      });
    },
    initChargeStatistics(data) {
      console.log(data, "datrata");
      const getchart = this.$echarts.init(document.getElementById("chargeStatistics"));
      let colors = ["#3562DB", "#FF9435"];
      getchart.setOption({
        color: colors,
        tooltip: {
          trigger: "axis",
          // axisPointer: {
          //   type: "cross"
          // },
          textStyle: {
            // 文字提示样式
            color: "#000000",
            fontSize: "13"
          },
          backgroundColor: "#fff"
        },
        grid: {
          right: "15%",
          left: "12%"
        },
        legend: {
          data: [
            {
              name: "实收金额/元",
              icon: "circle"
            },
            {
              name: "支付笔数/笔"
            }
          ],
          textStyle: {
            color: "#4E5969" // 图例文字颜色
          }
        },
        xAxis: [
          {
            type: "category",
            axisLine: {
              lineStyle: {
                color: "#86909C"
              }
            },

            axisTick: {
              show: false,

              alignWithLabel: true
            },
            data: data.existDetail
          }
        ],
        yAxis: [
          {
            axisLine: {
              show: false
            }
          },
          {
            type: "value",
            name: "",
            min: 0,
            max: Math.max.apply(null, data.actualPrice),
            position: "left",
            axisLine: {
              show: false,
              lineStyle: {
                color: "#414653"
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: "{value}",
              color: "#86909C"
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#e3e7ec"
              }
            }
          },
          {
            type: "value",
            name: "",
            min: 0,
            max: Math.max.apply(null, data.paymentFrequency),
            offset: 22,
            position: "right",
            axisLine: {
              show: false
            },
            axisLabel: {
              formatter: "{value}",
              color: "#86909C"
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: "实收金额/元",
            type: "bar",
            yAxisIndex: 1,
            barWidth: 10,
            data: data.actualPrice,
            itemStyle: {
              borderRadius: 2
            }
          },
          {
            name: "支付笔数/笔",
            type: "line",
            yAxisIndex: 2,
            data: data.paymentFrequency,
            symbol: "circle",
            itemStyle: {
              borderType: "solid",
              borderColor: "#fff",
              borderWidth: 1
            }
          }
        ]
      });
    },
    changeType(val) {
      this.dateType = val;
      this.$api.getOperateTotalDetail({ querySection: this.dateType }).then(res => {
        this.parkingLot = res;
      });
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="stylus" scoped>
.content{
  height:100%;
  background-color: #F2F4F9;
}
  .btn_tags{
    display: flex;
    justify-content: space-between;
    padding:.3125rem;
    background-color #fff
    .btn_item{
      width: 1.6rem;
      height: .7rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #F7F8FA;
      p{
        font-size: 14px;
        color: #4E5969;
      }
    }
    .btn_item_active{
      background: #E6EFFC;
      color: #3562DB;
    }
}
.cont {
  width: 100%;
  height: 180px;
  background-color #fff
  > div {
    height: 50%;
    width: 100%;
    display: flex;
    align-items: center;
    > div {
      flex: 1;
      text-align: center;
      line-height:25px
    }
  }
}
.title{
  background-color #fff;
  color:#1D2129;
  font-size: 15px
  // line-height:25px;
    padding:.3125rem 0 0 .3125rem;

}
  .caption{
    font-size: 20px; color: #333333; font-weight: 600
  }
.explain{
font-size: 14px; color: #7f848c
}
.explain2{
  font-size: 15px; color: #4E5969
}
.statistics{
  width:100%;
  // height:500px;
  background-color: #fff;
  margin-top:10px
.conts {
  width: 100%;
  // height: 180px;
  margin-top:20px;
  background-color #fff
  > div {
    height: 50%;
    width: 100%;
    display: flex;
    align-items: center;
    > div {
      flex: 1;
      text-align: center;
      line-height:25px
    }
    >div:nth-child(1)
    {
      border-right: 1px solid #e4e7ed;

    }
  }
}
 .echarts{
width: 100%;height: 350px
    }
}
.collectFees{
    width:100%;
    height:300px;
    background-color: #fff;
    margin-top :10px
    .echarts2{
      margin-top :10px;
width: 100%;height: 300px
    }
}
.executeCharts {
  height: 40vh;
  text-align: center;
  position: relative;
  .nullText {
    position: absolute;
    bottom: 1rem;
    left: 43%
  }
}
.sty{
  background-color: #F2F4F9;
  height:10px
}
</style>
