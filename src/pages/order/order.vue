<template>
  <div class="content">
    <div class="title">
      <div class="hospital-info">
        <img src="@/assets/images/hospital-logo.png" />
        <span>中国医学科学院肿瘤医院深圳医院-线上订餐</span>
      </div>
      <span class="bed-info">{{ bedName }}</span>
    </div>
    <div class="tool-box">
      <div class="btn" @click="sortTypeChanged('1')">
        <span :class="{ 'text-active': sortType == '1' }">默认排序</span>
      </div>
      <div class="btn" @click="sortTypeChanged('2')">
        <span :class="{ 'text-active': sortType == '2' }">评价</span>
        <span :class="['arrow-down', { 'arrow-down-active': sortType == '2' }]"></span>
      </div>
      <div class="btn" @click="sortTypeChanged('3')">
        <span :class="{ 'text-active': sortType == '3' }">订单量</span>
        <span :class="['arrow-down', { 'arrow-down-active': sortType == '3' }]"></span>
      </div>
    </div>
    <div class="card-box" v-if="orderList.length > 0">
      <div class="card" v-for="item in orderList" :key="item.id">
        <div class="canteen-name-box">
          <div class="canteen-name-title">{{ item.merchantName }}</div>
          <div class="canteen-name-btn" v-if="item.qrCodeOrUrlType" @click="showImgUrl(item)">订单查询</div>
        </div>
        <div class="main-content">
          <img v-if="item.merchantPicture" :src="item.merchantPicture" @click="previewImg(item.merchantPicture)" />
          <img v-else src="@/assets/images/noDataDefault/no-img.png" />
          <div class="time-info">
            <div>早餐 {{ item.breakfastExplain }}</div>
            <div>午餐 {{ item.lunchExplain }}</div>
            <div>晚餐 {{ item.dinnerExplain }}</div>
          </div>
        </div>
        <van-button type="primary" block color="#3562DB" @click="goOrder(item.bedOrderCode)">立即订餐</van-button>
      </div>
    </div>
    <div class="empty-box" v-else>
      <van-empty description="暂无数据" />
    </div>
  </div>
</template>

<script>
import { ImagePreview } from "vant";
export default {
  name: "orderPage",
  data() {
    return {
      bedName: "暂无床位信息",
      orderList: [],
      bedId: "",
      sortType: "1"
    };
  },
  mounted() {
    this.bedId = this.$route.query.id;
    this.getOrderList();
  },
  methods: {
    showImgUrl(item) {
      if(item.qrCodeOrUrlType == 1){
        ImagePreview([item.qrCodeOrUrl]);
      }
      if(item.qrCodeOrUrlType == 2){
        this.goOrder(item.qrCodeOrUrl)
      }
      
    },
    getOrderList() {
      let params = {
        bedId: this.bedId,
        pageNum: 1,
        pageSize: 20
      };
      this.$api.pageListByBedId(params).then(res => {
        console.log("返回res", res);
        this.orderList = res.list;
        this.bedName = res.bedName;
      });
    },
    goOrder(url) {
      window.location.href = url;
    },
    previewImg(url) {
      ImagePreview([url]);
    },
    sortTypeChanged(val) {
      this.sortType = val;
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  background-color: #f2f4f9;
  min-height: 100vh;
}
.title {
  background-color: #fff;
}
.title img {
  width: 30px;
  margin-right: 5px;
}
.hospital-info {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
}
.hospital-info span {
  color: #4e5969;
  font-size: 14px;
  font-weight: bold;
}
.bed-info {
  display: inline-block;
  font-size: 17px;
  color: #1d2129;
  font-weight: bold;
  width: 100%;
  text-align: center;
  height: 38px;
  line-height: 38px;
  margin-bottom: 10px;
}
.btns {
  padding: 0 10px;
  margin: 8px 0;
}
.card-box {
  padding: 0 10px;
}
.card {
  background-color: #fff;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;
}
.card .main-content {
  display: flex;
  justify-content: space-between;
  margin: 16px 0;
}
.card .main-content img {
  width: 32vw;
  height: 28vw;
  border-radius: 5px;
}
.time-info {
  width: 50vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.time-info span {
  color: #1d2129;
  font-size: 16px;
}
.time-info div {
  margin-bottom: 14px;
}
.canteen-name-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .canteen-name-title {
    width: calc(100% - 90px);
    flex-shrink: 0;
    font-size: 17px;
    color: #1d2129;
    font-weight: bold;
  }
  .canteen-name-btn {
    color: #3a62d8;
    font-size: 16px;
    margin-left: 10px;
    flex-shrink: 0;
    padding: 0px 5px;
    line-height: 1.2;
  }
}

.empty-box {
  margin: 0 10px;
  background-color: #fff;
  height: 50vh;
  border-radius: 5px;
}
.tool-box {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  height: 40px;
  align-items: center;
  padding: 0 8vw;
  margin-bottom: 10px;
  span {
    color: #1d2129;
    margin-right: 8px;
  }
  .btn {
    display: flex;
    align-items: center;
  }
}
.arrow-down {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #cacdd4; /* 修改此处的颜色来改变箭头颜色 */
}
.arrow-down-active {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #3a62d8;
}
.text-active {
  color: #3a62d8 !important;
}
</style>
