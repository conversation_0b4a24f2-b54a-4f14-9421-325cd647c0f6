<template>
  <div class="inner">
    <Header  title="任务详情" @backFun="goBack()"></Header>
    <div class="aeecssDetail">
      <div class="aeecssDetailBox">
        <div style="display: flex;">
          <div class="imgGS"><img src="../../../assets/images/Frame <EMAIL>" alt=""></div>
          <div class="imgGStitle">{{ learnTaskData.name }}</div>
        </div>
        <div class="aeecssDetail_conter">
          <div class="aeecssDetail_Nav">
            <div class="aeecssDetail_lable">所属部门：</div>
            <div class="aeecssDetail_item">{{ learnTaskData.deptName }}</div>
          </div>
          <div class="aeecssDetail_Nav">
            <div class="aeecssDetail_lable">任务时间：</div>
            <div class="aeecssDetail_item">{{ learnTaskData.startTime }} - {{ learnTaskData.endTime }}</div>
          </div>
          <div class="aeecssDetail_Nav">
            <div class="aeecssDetail_lable">所属科目：</div>
            <div class="aeecssDetail_item">{{ learnTaskData.subjectName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="aeecssDetail_tab">
      <div class="switbox" v-for="(x, y) in switchTab" :key="y" :class="{ active: switchIndex == y }"
        @click="openSwitch(y)">{{ x.name }} <div class="linesy" v-if="switchIndex == y"></div>
      </div>
    </div>
    <div class="aeecssDeList">
      <div v-if="switchIndex == 0">
        <div class="aeecssDeListKC" v-for="(item, index) in courseList" :key="index">
          <div class="accessBox">
            <div class="accessImg"><img :src="item.coverUrl" alt=""></div>
            <div>
              <div class="courseName">{{ item.courseName }}</div>
              <div class="courseName_mak">{{ item.comments }}</div>
            </div>
          </div>
          <div style="width: 100%; border: .01rem solid #F2F3F5; "></div>
          <div class="accessBox">
            <div class="access_lable">课程类型：</div>
            <div class="access_item">{{ item.subjectName }}</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">课时数：</div>
            <div class="access_item">{{ item.periodCount }}</div>
          </div>
          <div class="courseBtn" >
            <van-button v-if="item.studyState == 0" type="info" style="width: 1.76rem; height:.64rem; background-color:#3562DB;" @click="openCourse(item)">学习中</van-button>
            <van-button v-if="item.studyState == 2" type="info" style="width: 1.76rem; height:.64rem; background-color:#3562DB;" @click="openCourse(item)">立即学习</van-button>
            <van-button v-if="item.studyState == 3" type="info" style="width: 1.76rem; height:.64rem; background-color: #C9CDD4; border: #C9CDD4;" @click="openCourse(item)">已学完</van-button>
          </div>
          
        </div>
      </div>
      <div v-if="switchIndex == 1">
        <div class="aeecssDeListPX" v-for="(item, index) in trainList" :key="index">
          <span style="color: #1D2129; font-size: .32rem; font-weight: bold;">{{ item.courseName }}</span>
          <div
            style="color: #86909C; font-size: .26rem; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; margin-top: .2rem;">
            {{ item.ramik }}</div>
          <div class="accessBox">
            <div class="access_lable">所属科目：</div>
            <div class="access_item">{{ item.subjectName }}</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">培训老师：</div>
            <div class="access_item">{{ item.teacherName }}</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">培训资料：</div>
            <div class="access_item">{{ item.materialNum }} 个</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">培训时间：</div>
            <div class="access_item">{{ item.startTime }} - {{ item.endTime }}</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">培训地点：</div>
            <div class="access_item">{{ item.address }}</div>
          </div>
          <div class="courseBtn" v-if="item.signStatus == 0">
            <van-button type="info" style="width: 1.76rem; height:.64rem; background-color:#3562DB;" @click="openTrainDeatil(item)">培训签到</van-button>
          </div>
        </div>
      </div>
      <div v-if="switchIndex == 2">
        <div class="aeecssDeListks" v-for="(item, index) in examinList" :key="index">
          <span style="color: #1D2129; font-size: .32rem; font-weight: bold;">{{ item.courseName }}</span>
          <div
            style="color: #86909C; font-size: .26rem; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; margin-top: .2rem;">
            {{ item.ramik }}</div>
          <div class="accessBox">
            <div class="access_lable">所属科目：</div>
            <div class="access_item">{{ item.subjectName }}</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">考试时限：</div>
            <div class="access_item">{{ item.startTime }} - {{ item.endTime }}</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">考试时长：</div>
            <div class="access_item">{{ item.duration || '0'}} 分钟</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">考题数量：</div>
            <div class="access_item">{{ item.questionNum || '0'}} 题</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">考试总分：</div>
            <div class="access_item">{{ item.score || '0'}} 分</div>
          </div>
          <div class="accessBox">
            <div class="access_lable">所属部门：</div>
            <div class="access_item">{{ item.deptName }}</div>
          </div>
          <div class="courseBtn" v-if="item.examStatusApp == 0">
            <van-button type="info" style="width: 1.76rem; height:.64rem; background-color:#3562DB;" @click="openExamin">立即考试</van-button>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import axios from "axios";
import ImageCompressor from "image-compressor.js";
export default {
  components: {},
  data() {
    return {
      loginData: '',
      courseId: '', // 课程id
      courseDatail: [], // 课程详情
      tableList: [], // 课时列表
      total: '',
      switchTab: [
        {
          name: '课程内容',
          id: '0',
        },
        {
          name: '培训内容',
          id: '1',
        },
        {
          name: '考试内容',
          id: '2',
        }
      ],
      switchIndex: '0',
      courseList: [],
      trainList: [],
      examinList: [],
      learnTaskData: '', // 我的任务详情
    };
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    this.loginData = JSON.parse(localStorage.getItem("loginData"));
  },
  mounted() {
    this.courseId = this.$route.query.id
    if(this.courseId) {
      this.getlearnTaskDetail();
    }
    
    this.$utils.backBtn(this.goBack)
  },
  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 选择名称筛选
    openSwitch(e) {
      this.switchIndex = e
    },
    // 获取我的任务详情
    getlearnTaskDetail() {
      this.$api.learnTaskDetail({ id: this.courseId}).then( res => {
        console.log(res,'XIANGQ 789');
        this.learnTaskData = res;
        this.courseList = res.courseList;
        this.examinList = res.examList;
        this.trainList = res.trainList;
        console.log(this.learnTaskData);
      })
    },
    // 点击学习跳转课程详情
    openCourse(item) {
      this.$router.push({
        path: '/courseDetail',
        query: {
          courseId: item.id,
        }
      })
    },
     // 培训查看详情
     openTrainDeatil(item) {
      this.$router.push({
        path: '/trainIndex',
        // query: {
        //   id: item.trainId,
        // }
      })
    },
    // 考试跳转
    openExamin() {
      this.$router.push({
        path: '/examinationIndex',
        // query: {
        //   id: item.taskId,
        // }
      })
    }

  }
};
</script>

<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
}

.inner {
  height: 100%;
  background-color: #f2f4f9;
  position: relative;

  .contener {
    height: calc(100% - 1.6rem);
    overflow: auto;
  }
}

.imgGS img {
  width: .36rem;
  height: .36rem;
  margin: .2rem .16rem .2rem 0rem;

}

.imgGStitle {
  color: #1D2129;
  font-size: .32rem;
  font-weight: bold;
  margin: .2rem 0rem;

}

.accessImg img {
  width: 2.08rem;
  height: 1.16rem;
}

.aeecssDetail {
  // width: 100%;
  background-color: #fff;
  padding: .2rem;

  .aeecssDetailBox {
    padding: 0rem .32rem;
    // width: 100%;
    height: 3.06rem;
    border-radius: .16rem;
    background-image: url('../../../assets/images/背景@2x.png');
    background-repeat: no-repeat;
    background-size: 100% 3.06rem;
  }
}

.courseName {
  font-size: .32rem;
  color: #1D2129;
  font-weight: bold;
  margin: .2rem 0rem .3rem .2rem;
}

.courseName_mak {
  width: 4rem;
  font-size: .26rem;
  color: #86909C;
  margin: 0rem 0rem 0rem .2rem;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.aeecssDetail_conter {
  padding: .12rem .2rem .2rem .2rem;
  box-sizing: border-box;
  width: 100%;
  height: 1.92rem;
  border-radius: .08rem;
  background-color: rgba(255, 255, 255, .6);

}

.aeecssDetail_Nav {
  display: flex;
  margin: .2rem 0rem;

  .aeecssDetail_lable {
    width: 25%;
    font-size: .28rem;
    color: #86909C;
  }

  .aeecssDetail_item {
    width: 70%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.aeecssDetail_tab {
  // width: 100%;
  height: .84rem;
  background-color: #fff;
  margin-top: .04rem;
  display: flex;
  line-height: .84rem;
}

.switbox {
  width: 100%;
  padding: 0rem .4rem;
  line-height: .84rem;
  font-size: .32rem;
  color: #86909C;
  font-weight: bold;
  text-align: center;

  &.active {
    color: #1D2129;

  }
}

.linesy {
  width: .48rem;
  height: .06rem;
  border-radius: .04rem;
  background-color: #3562DB;
  margin-left: .55rem;
  margin-top: -0.16rem;
}

.aeecssDeList {
  // width: 100%;
  height: 7.2rem;
  overflow-y: auto;
  padding: .2rem;
}

.aeecssDeListks {
  // width: 100%;
  height: 4.6rem;
  background-color: #fff;
  border-radius: .16rem;
  padding: .3rem;
  margin: .2rem 0rem;
  position: relative;
}

.aeecssDeListKC {
  // width: 100%;
  height: 3.5rem;
  background-color: #fff;
  border-radius: .16rem;
  padding: .1rem .3rem;
  // padding: .3rem;
  margin: .2rem 0rem;
  position: relative;
}

.aeecssDeListPX {
  // width: 100%;
  height: 4.4rem;
  background-color: #fff;
  border-radius: .16rem;
  padding: .3rem .3rem;
  // padding: .3rem;
  margin: .2rem 0rem;
  position: relative;
}

.accessBox {
  display: flex;
  margin: .26rem 0rem;

  .access_lable {
    width: 25%;
    font-size: .28rem;
    color: #86909C;
  }

  .access_item {
    width: 70%;
    font-size: .28rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.detail_footer {
  width: 100%;
  height: .8rem;
  position: fixed;
  padding: 0rem .4rem;
  bottom: .1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  border-radius: .04rem .04rem .04rem .04rem;
}

.courseBtn {
  position: absolute;
  bottom: .1rem;
  right: .1rem;
  // position: relative;
  // bottom: .3rem;
  // left: 78%;
}
</style>
