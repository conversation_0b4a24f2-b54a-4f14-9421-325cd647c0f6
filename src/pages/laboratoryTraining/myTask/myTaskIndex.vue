<template>
  <div class="inner" v-if="loginData.isFalg == 0">
    <Header  title="我的任务" @backFun="goBack()"></Header>
    <div class="courseTitle">
      <van-search v-model="finList.name" @search="onSearch" :clearable="true" @clear="handleClear"
        placeholder="请输入搜索关键词" />
    </div>
    <div class="headline">
      <div class="headlineLable" v-for="(k, m) in coursesList" :key="m" :class="{ active: courseIndex == m }"
        @click="openCourses(m)">{{ k.lable }}</div>
    </div>
    <div style="padding: 0rem .2rem;" class="conter">
      <van-pull-refresh v-if="accessData.length > 0" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="headlineDetail" v-for="(item, index) in accessData" :key="index">
            <div class="headlineDetail_box" @click="openDetail(item)">
              <div style="display: flex;">
                <div class="access"><img src="../../../assets/images/Frame <EMAIL>" alt=""></div>
                <div id="posthard">{{ item.name }}</div>
              </div>
              <div class="headlineDetail_list">
                <div class="accessBox">
                  <div class="access_lable">实验室类型：</div>
                  <div class="access_item">{{ item.subjectName }}</div>
                </div>
                <div class="accessBox">
                  <div class="access_lable">任务时间：</div>
                  <div class="access_item">{{ item.startTime }} - {{ item.endTime }}</div>
                </div>
                <div class="accessBox">
                  <div class="access_lable">所属组织：</div>
                  <div class="access_item">{{ item.deptName }}</div>
                </div>
                
              </div>
            </div>
            <div class="acceSum">
              <div>
                <div class="acceSum_lable">课程数：</div>
                <div class="acceSum_item">{{ item.courseList.length || '0'}}个</div>
              </div>
              <div>
                <div class="acceSum_lable">考试数:</div>
                <div class="acceSum_item">{{item.examList.length || '0' }}个</div>
              </div>
              <div>
                <div class="acceSum_lable">培训数：</div>
                <div class="acceSum_item">{{ item.trainList.length || '0' }}个</div>
              </div>
            </div>
            <div class="acceSumBtn">
              <div class="openBtn">
                <van-button v-if="item.taskStatusApp == 0" style="width: 1.76rem; height: .7rem; background-color: #3562DB;" type="info"  @click="openDetail(item)">进入学习</van-button>
              </div>
              <div class="openBtn">
                <van-button v-if="item.taskStatusApp == 1" style="width: 1.76rem; height: .7rem; background-color: #3562DB;" type="info"  @click="openDetail(item)">学习中</van-button>
              </div>
              <div class="openBtn">
                <van-button v-if="item.taskStatusApp == 2" disabled="item.taskStatusApp == 2" style="width: 1.76rem; height: .7rem; background-color: #3562DB;" type="info">已学完</van-button>
              </div>
              <div class="openBtn">
                <van-button v-if="item.taskStatusApp == 3" disabled="item.taskStatusApp == 3" style="width: 1.76rem; height: .7rem; background-color: #3562DB;" type="info">已超时</van-button>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <div>
      <permissionPrompt></permissionPrompt>
    </div>
  </div>
</template>

<script>
// import moreSelect from "@/components/lx-cascade-select/more-select.vue"
import permissionPrompt from "../openCurser/components/permissionPrompt.vue"
export default {
  components: { permissionPrompt },
  data() {
    return {
      show: false,
      loginData: '',
      value: '',
      isLoading: false,
      loading: false,
      finished: false,
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      coursesList: [
        {
          lable: '全部任务',
          value: '-1',
        },
        {
          lable: '未开始',
          value: '0',
        },
        {
          lable: '学习中',
          value: '1',
        },
        {
          lable: '已学完',
          value: '2',
        },
        {
          lable: '超时',
          value: '3',
        }
      ],
      courseIndex: 0,
      accessData: [], // 我的任务列表
      finList: {
        name: '',
        taskStatusApp: '',
      },
      listName: [],
      cityListName: "",
    };
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    this.loginData = JSON.parse(localStorage.getItem("loginData"));
    if(this.loginData.isFalg == 1) {
      return
    }
    this.getlearnTaskList()
    // this.getTreeAllList()
  },
  mounted() {
    // wx.onHistoryBack(() => {
    //   this.$router.push('/homePage')
    //   return false
    // });
  },
  methods: {
    // 监听返回
    goBack() {
      this.$router.push('/laboratoryTraining')
    },
    // 选择分类
    openCourses(el) {
      this.courseIndex = el
      if(this.courseIndex == 0) {
        this.finList.taskStatusApp = ''
        this.getlearnTaskList()
      } else if(this.courseIndex == 1) {
        this.finList.taskStatusApp = 0
        this.getlearnTaskList()
      } else if(this.courseIndex == 2) {
        this.finList.taskStatusApp = 1
        this.getlearnTaskList()
      } else if(this.courseIndex == 3) {
        this.finList.taskStatusApp = 2
        this.getlearnTaskList()
      }else if(this.courseIndex == 4) {
        this.finList.taskStatusApp = 3
        this.getlearnTaskList()
      }
      // console.log(this.courseIndex,'this.courseIndex');
      // this.finList.taskStatusApp = this.courseIndex
      // this.getlearnTaskList()
    },
    rest() {
      this.finList.name = ''
      this.$refs.moreselects.result = []
      this.getlearnTaskList()
    },
    // 下拉刷新
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.accessData = []
      this.getlearnTaskList()
    },
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = false
      this.loading = true
      this.getlearnTaskList()
    },
    // 搜索筛选
    onSearch(val) {
      // this.$toast(val)
      this.getlearnTaskList()
    },
    //搜索筛选
    handleClear() {
      this.finList.nameame = ''
      this.getlearnTaskList()
    },
    // 获取我的任务列表
    getlearnTaskList() {
      let params = {
        current: this.pageParmes.pageNo,
        size: this.pageParmes.pageSize,
        equipmentFlag: 2,
        ...this.finList,
      }
      this.$api.learnTaskList(params).then((res) => {
        this.accessData = res.list
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
      })
    },
    // 跳转课程详情
    openDetail(item) {
      this.$router.push({
        path: '/myTaskDetail',
        query: {
          id: item.id
        }
      })
    },
  }
};
</script>

<style scoped lang="scss">
.inner {
  height: 100%;
  padding-top: .1rem;
  background-color: #f2f4f9;
  position: relative;

  .contener {
    height: calc(100% - 1.6rem);
    overflow: auto;
  }

  .courseTitle {
    // width: 100%;
    height: 1.36rem;
    padding: .16rem .1rem 0rem .1rem;
    box-sizing: border-box;
    background: #FFFFFF;
  }
}

.conter {
  // width: 100%;
  height: 78%;
  overflow-y: auto;

}

// .headline {
//   width: 100%;
//   overflow-x: scroll;
//   white-space: nowrap;
//   height: .6rem;
//   line-height: .6rem;
//   margin: .2rem 0rem;
//   color: #1D2129;
//   font-size: .32rem;
//   padding: 0rem .2rem;
//   box-sizing: border-box;

//   span {
//     color: #86909C;
//   }
// }

// .headline_name {
//   width: 100%;
//   overflow-x: scroll;
//   font-size: .28rem;
//   color: #1D2129;
// }

.headlineDetail {
  // width: 100%;
  height: 5.8rem;
  background-color: #FFFFFF;
  border-radius: .16rem .16rem .16rem .16rem;
  // padding: .2rem;
  box-sizing: border-box;
  margin: .2rem 0rem;
  border: .01rem solid #E6EFFC;

}

.access img {
  width: .36rem;
  height: .36rem;
  margin: .04rem .1rem .2rem 0rem;
}

.headlineDetail_box {
  // width: 100%;
  height: 3.4rem;
  padding: .2rem;
  background-image: url('../../../assets/images/背景@2x.png');
  background-repeat: no-repeat;
  background-size: 100% 3.4rem;
}

.headlineDetail_list {
  padding: .2rem;
  // width: 100%;
  height: 2.08rem;
  border-radius: .16rem;
  background-color: rgba(255, 255, 255, .6);

  .accessBox {
    display: flex;
    margin: .2rem 0rem;

    .access_lable {
      width: 30%;
      font-size: .28rem;
      color: #86909C;
    }

    .access_item {
      width: 70%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.acceSum {
  display: flex;
  padding: 0rem .32rem;
  // width: 100%;
  justify-content: space-between;

  .acceSum_lable {
    font-size: .28rem;
    color: #86909C;
  }

  .acceSum_item {
    width: .9rem;
    height: .44rem;
    padding: .04rem;
    background-color: #FFF7E8;
    border-radius: .04rem;
    color: #D25F00;
    text-align: center;
    line-height: .36rem;
    margin: .1rem 0rem 0rem 0rem;
    // margin-left: .2rem;
    // margin-top: .2rem;
  }
}

.headImg {
  width: 2rem;
  height: 1.16rem;
  margin-right: .4rem;
}

.headImg img {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.head_top {
  display: flex;
}

.introduction {
  width: 65%;

  .head_item {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #86909C;
    font-size: .26rem;
  }
}

#posthard {
  font-size: .36rem;
  color: #1D2129;
  font-weight: 500;
  // margin: .1rem 0rem .3rem 0rem;
}

.line {
  width: 100%;
  margin: .3rem 0rem;
  border: .01rem solid #F2F3F5;
}

.head_conter {
  font-size: .28rem;
  color: #86909C;
}

.head_conter>div {
  margin: .2rem 0rem;
}

.head_lable {
  color: #4E5969;

}

.head_footer {
  display: flex;
  justify-content: space-between;
}

.study {

  font-size: .32rem;
  color: #FF7D00;
}

.btn {
  color: #3562DB;
  // width: 2rem;
  // height: .64rem;
}

.notList {
  position: relative;
  height: calc(100% - 1rem);

  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url('../../../assets/images/noData.png') 0 40% no-repeat;
    background-size: 100% auto;

    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}

.acceSumBtn {
  width: 100%;
  display: flex;
  position: relative;

  .openBtn {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

/deep/ .van-search__content {
  background-color: #F2F3F5;
}
.headline {
  // width: 100%;
  padding: 0px 12px;
  height: 40px;
  line-height: 30px;
  // margin-top: 6px;
  display: flex;
  justify-content: space-between;
  background-color: #FFFFFF;

  .headlineLable {
    width: 18%;
    height: 30px;
    background-color: #F7F8FA;
    text-align: center;
    &.active {
      color: #3562DB;
      background-color: #E6EFFC;
    }
  }
}
</style>
