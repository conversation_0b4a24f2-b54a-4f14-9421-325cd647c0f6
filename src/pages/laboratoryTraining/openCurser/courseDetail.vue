<template>
  <div class="inner">
    <Header  title="课程详情" @backFun="goBack()"></Header>
    <div>
      <div class="datailImg">
        <img v-if="courseDatail.data.coverUrl" :src="courseDatail.data.coverUrl">
        <img v-else src="../../../assets/images/icon_moren.png" alt="">
      </div>
      <div style="padding: 0rem .2rem;">
        <div class="detailName">{{ courseDatail.data.courseName }}</div>
        <div class="description">{{ courseDatail.data.comments || '暂无课程介绍' }}</div>
        <div class="detailStudy">
          <div><van-icon name="manager-o" />讲课老师：<span>{{ courseDatail.data.createName }}</span></div>
          <div> {{ courseDatail.data.viewCount || '0' }} 人学过</div>
        </div>
      </div>
      <div class="detail_conter">
        <div class="detail_title">课程目录<span>({{ total }})</span></div>
        <div class="courseDetailist">
          <div class="detaiList" v-for="(item, index) in tableList" :key="index">
            <div style="display: flex;">
              <div class="courseImg">
                <img v-if="item.type == 1" src="../../../assets/images/mp4.png" alt="">
                <img v-if="item.type == 2" src="../../../assets/images/doc.png" alt="">
                <img v-if="item.type == 3" src="../../../assets/images/text.png" alt="">
              </div>
              <div>
                <div class="courlable">{{ item.periodName }}</div>
                <div v-if="item.type == 1" class="courItem">时长：{{ item.courseDuration || '0' }}</div>
                <div v-if="item.type == 2 || item.type == 3" class="courItem">共：{{ '-' }}</div>
              </div>
              <div style="margin-left: auto;">
                <div v-if="item.periodSpace != 99 && item.periodSpace != 100"><van-button type="info"
                    style="width: 1.2rem; height: .6rem; border-radius: .04rem; background-color:#3562DB; border: #3562DB;"
                    @click="openVideo(item)">去学习</van-button></div>
                <div v-if="item.periodSpace == '100' || item.periodSpace == '99'"><van-button type="info"
                    style="width: 1.2rem; height: .6rem; border-radius: .04rem; background-color:#C9CDD4; border: #C9CDD4;"
                    @click="openVideo(item)">已学完</van-button></div>
              </div>
            </div>
            <div v-if="item.periodSpace == 100 || item.periodSpace == 99">
              <div class="courseLine" v-if="courseDatail.data.studyState != 1 && item.questionCount != 0"></div>
              <div style="display: flex; justify-content: space-between;"
                v-if="courseDatail.data.studyState != 1 && item.questionCount != 0">
                <div class="questions">课后试题</div>
                <div style="display: flex;">
                 
                  <div class="questionSum">共 : {{ item.questionCount || '0' }} 道</div>
                  <div>
                    <van-button v-if="item.coursePeriodQuestionList.length != 0" type="info"
                      style="width: 1.2rem; height: .6rem; border-radius: .04rem; background-color:#C9CDD4; border: #C9CDD4;"
                      @click.stop="openQuestions(item)">已答完</van-button>
                    <van-button v-else type="info"
                      style="width: 1.2rem; height: .6rem; border-radius: .04rem; background-color:#3562DB; border: #3562DB;" @click.stop="openQuestions(item)">未答题</van-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="detail_footer" v-if="courseDatail.data.studyState == 1">
          <van-button @click="collSc" type="info" style="width: 100%; height: 100%; background-color:#3562DB;"><van-icon
              name="star-o" style="margin-right: .2rem;" />收藏学习</van-button>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
import moment from "moment";
import axios from "axios";
import ImageCompressor from "image-compressor.js";
export default {
  components: {},
  data() {
    return {
      loginData: '',
      courseId: '', // 课程id
      courseDatail: [], // 课程详情
      tableList: [], // 课时列表
      total: '',
      courseDuration: '',  // 视频时长
    };
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
      this.loginData = JSON.parse(localStorage.getItem("loginData"));
  },
  mounted() {
    this.courseId = this.$route.query.courseId
    setTimeout(() => {
      this.getCourseDetail();
    }, 200)
    // this.$utils.backBtn(this.goBack)
  },
  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    getCourseDetail() {
      axios.get(__PATH.LAB_COURSE + '/mine/get', {
        params: {
          courseId: this.courseId,
          userId: this.loginData.userId
        }
      }).then((res) => {
        this.courseDatail = res.data;
        
        this.total = res.data.data.coursePeriodDTOList.length;
        res.data.data.coursePeriodDTOList.forEach(el => {
          if (el.url) {
            JSON.parse(el.url).forEach(e => {
              console.log(e.duration, 'e.duration');
              let hour = Math.floor(e.duration / 60 / 60 % 24);	// 时
              let minutes = Math.floor(e.duration / 60 % 60);
              let seconds = Math.floor(e.duration % 60);
              hour = hour < 10 ? "0" + hour : hour;
              minutes = minutes < 10 ? "0" + minutes : minutes;
              seconds = seconds < 10 ? "0" + seconds : seconds;
              el.courseDuration = hour + ":" + minutes + ":" + seconds;
            })
          }
        })
        this.tableList = res.data.data.coursePeriodDTOList;
      }).catch(err => {
        console.log(err);
      })
    },
    // 打开视频播放
    openVideo(item) {
      console.log(item, 'ITEM');
      if (this.courseDatail.data.studyState == 1) {
        alert('该课程暂未收藏，请先收藏课程')
        return
      }
      this.$router.push({
        path: '/courseVideo',
        query: {
          url: item
        }
      })
    },
    // 打开试题
    openQuestions(item) {
      if (!item.questionCount) {
        return this.$toast('暂无课后习题')
      }
      this.$router.push({
        path: '/examination',
        query: {
          id: item.id,
          courseId: item.courseId,
          url: item,
        }
      })
    },
    // 详情收藏课程
    collSc() {
      let params = {
        courseId: this.courseId,
        userId: this.loginData.userId
      }
      this.$api.getCollect(params).then((res) => {
        this.$toast.success('收藏成功！')
        this.getCourseDetail()
      })
    },
  }
};
</script>
<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
}

.inner {
  height: 100%;
  background-color: #fff;
  position: relative;

  .contener {
    height: calc(100% - 1.6rem);
    overflow: auto;
  }
}

.datailImg {
  width: 100%;
  height: 4rem;
}

.datailImg img {
  width: 100%;
  height: 100%;

}

.courseDetailist {
  width: 100%;
  height: 4.6rem;
  overflow-y: auto;
}

.detailName {
  color: #1D2129;
  font-size: .36rem;
  font-weight: bold;
  margin: .3rem 0rem;
  overflow: hidden;
  width: 100%;
  text-overflow: ellipsis;
  height: .38rem;
}

.description {
  color: #4E5969;
  font-size: .28rem;
  line-height: .4rem;
  height: 1rem;
  width: 100%;
  overflow: auto;
}

.detailStudy {
  display: flex;
  justify-content: space-between;
  font-size: .28rem;
  color: #86909C;
  margin: .3rem 0rem;

}

.detail_conter {
  // width: 100%;
  height: 4rem;
  padding: 0rem .2rem;

  // overflow-y: auto;
  .detaiList {
    // width: 100%;
    // height: 70px;
    border-radius: .16rem;
    background-color: #F7F8FA;
    display: flex;
    flex-direction: column;
    padding: .3rem;
    margin: .2rem 0rem;

  }
}

.detail_title {
  color: #1D2129;
  font-size: .32rem;
  font-weight: 500;
  font-weight: bold;
}

.courseImg {
  width: .64rem;
  height: .64rem;
  margin-right: .4rem;
  margin-top: .1rem;
}

.courseImg img {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.courlable {
  font-size: .32rem;
  font-weight: 500;
  color: #1D2129;
}

.courItem {
  color: #4E5969;
  margin-top: .2rem;
}

.detail_footer {
  width: 90%;
  height: 1rem;
  position: fixed;
  padding: 0rem .4rem;
  bottom: .2rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  border-radius: .04rem .04rem .04rem .04rem;
}

.courseLine {
  width: 100%;
  border: .01rem solid #EAEAEA;
  margin: .4rem 0rem .3rem 0rem;
}

.questions {
  font-size: .32rem;
  color: #1D2129;
  margin: .2rem 0rem 0rem 1rem;
}

.questionSum {
  font-size: .28rem;
  color: #4E5969;
  margin: .2rem .4rem 0rem 0rem;
}
</style>
