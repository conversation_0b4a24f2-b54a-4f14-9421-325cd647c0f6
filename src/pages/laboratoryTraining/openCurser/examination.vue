<template>
  <div class="box">
    <Header  title="在线考试" @backFun="goBack()"></Header>
    <div class="course_node_video">
      <div class="validity" v-for="(item, index) in questionsList" :key="index" v-if="questionsIndex === index">
        <div class="validity_select" v-if="item.type == 1">单选题</div>
        <div class="validity_select" v-if="item.type == 2">解题多选</div>
        <div class="validity_select" v-if="item.type == 3">判断题</div>
        <div class="validity_title">{{ item.topic }} <span>({{ item.score || '0' }}分)</span> </div>
        <!-- 单选 -->
        <div class="validity_radio">
          <van-radio-group v-model="item.userAnswer" v-if="item.type == 1">
            <van-radio :disabled="urlOption.viewState && urlOption.periodSpace == '100'" style="margin: .3rem 0rem;"
              v-for="(k, index) in item.options" :key="index" :name="k.id">
              <div class="selectRadiosy">{{ k.id }}. {{ k.label }}</div>
            </van-radio>
          </van-radio-group>
        </div>
        <!-- 多选 -->
        <div class="validity_check" v-if="item.type == 2">
          <van-checkbox-group v-model="item.userAnswer">
            <van-checkbox :disabled="urlOption.viewState && urlOption.periodSpace == '100'" shape="square"
              style="margin: .3rem 0rem;" v-for="(k, index) in item.options" :key="index" :name="k.id">
              <div class="selectRadiosy">{{ k.id }}. {{ k.label }}</div>
            </van-checkbox>
          </van-checkbox-group>
        </div>
        <!-- 判断 -->
        <div class="validity_radio">
          <van-radio-group v-model="item.userAnswer" v-if="item.type == 3">
            <van-radio :disabled="urlOption.viewState && urlOption.periodSpace == '100'" style="margin: .3rem 0rem;"
              :name="'1'">正确</van-radio>
            <van-radio :disabled="urlOption.viewState && urlOption.periodSpace == '100'" style="margin: .3rem 0rem;"
              :name="'2'">错误</van-radio>
          </van-radio-group>
        </div>
        <div v-if="urlOption.viewState && urlOption.periodSpace == '100'">
          <div>答案：{{ item.answer == 1 ? '正确' : '错误' }}</div>
          <div style="display: flex; width: 100%; margin: .2rem 0rem;" v-if="item.analysis != ''">
            <div class="analysis">解析：</div>
            <div class="analysisItem">{{ item.analysis }}</div>
          </div>
        </div>
      </div>
      <div v-if="datoi">
        <div class="questionSty" @click="showPopup">
          <div><img src="../../../assets/images/icon-wrapper.png" alt=""></div>
          <div><span style="margin-left: .1rem; font-size: .3rem; color:#3562DB ;">{{ questionsIndex + 1 }}</span> /
            <span style=" font-size: .3rem; color:#4E5969 ;">{{ this.questionsList.length }}</span>
          </div>
        </div>

      </div>
      <div class="questionBtn">
        <van-button
          style="width: 2rem; height: .9rem; margin: 0rem .3rem; background-color: #E6EFFC; border: #E6EFFC; color: #3562DB; font-size: .3rem;"
          :disabled="preDisabled" type="primary" @click="upSelect">上一题</van-button>
        <van-button
          style="width: 2rem; height: .9rem;  margin: 0rem .3rem; background-color: #3562DB; font-size: .3rem;"
          type="info" :disabled="nextDisabled" @click="nextSelect">下一题</van-button>
        <div v-if="urlOption.viewState && urlOption.periodSpace != '100'"><van-button
            style="width: 2rem; height: .9rem;  margin: 0rem .3rem; background-color: #3562DB; font-size: .3rem;"
            type="info" @click="goSubmit">提交</van-button></div>
      </div>

    </div>
    <van-popup v-model="show" position="bottom" :style="{ height: '60%' }">
      <div class="content_right">
        <p>
          全部题目<span>（{{ questionsIndex + 1 }}/{{ this.questionsList.length }}）</span>
        </p>
        <div class="answerNum">
          <div v-for="(item, index) in questionsList" :key="index" :class="[
      'item',
      questionsIndex == index ? 'isActive' : '',isfinished(item) ? 'isfinished' : '',]" @click="goOnQuestions(index)">
            {{ index + 1 }}
            <img v-if="isfinished(item)" src="../../../assets/images/考试图片.png" alt="" />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import axios from "axios";
export default {
  data() {
    return {
      datoi: true,
      show: false,
      preDisabled: true, //上禁用按钮
      nextDisabled: false, //下禁用按钮
      questionsList: [], // 试题列表
      loginData: '',
      periodIds: '', // 课时id
      courseId: '', // 课程id
      webRoleCode: '',
      itemInfo: [],
      questionsIndex: 0,
      noLength: 0, // 未作试题长度
      urlOption: {},
    }
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
      this.loginData = JSON.parse(localStorage.getItem("loginData"));
    this.periodIds = this.$route.query.id
    this.getQuestionsList()
  },
  computed: {
  },
  watch: {
    gk: function () {
    }
  },
  mounted() {
    this.urlOption = this.$route.query.url
    console.log(this.urlOption, 'this.urlOption');
    this.periodIds = this.$route.query.id
    this.courseId = this.$route.query.courseId
    this.$utils.backBtn(this.goBack)
  },
  watch: {
    questionsIndex(now, old) {
      if (now == this.questionsList.length - 1) {
        this.nextDisabled = true;
      } else {
        this.nextDisabled = false;
      }
      if (now < 1) {
        this.preDisabled = true;
      }
    }
  },
  methods: {
    showPopup() {
      this.show = true;
    },
    // 是否完成
    isfinished(item) {
      console.log(item, 'item');
      return item.type != '2' ? Boolean(item.userAnswer) : Boolean(item.userAnswer.length)
    },
    // 返回按钮
    goBack() {
      this.$router.go(-1)
    },
    // 试题列表
    getQuestionsList() {
      axios.get(__PATH.LAB_COURSE + '/period/question/list', {
        params: {
          periodId: this.periodIds,
          userId: this.loginData.userId
        }
      }).then((res) => {
        if (res.data.code == 200) {
          res.data.data.forEach(el => {
            el.options = JSON.parse(el.options)
            if (el.type == 2) {
              el.userAnswer = el.userAnswer || []
            } else {
              el.userAnswer = el.userAnswer || ''
            }
          })
        }
        this.questionsList = res.data.data;
      })
    },
    // 上一题
    upSelect() {
      if (this.questionsIndex === 0) {
        this.questionsIndex = 0
      } else {
        this.questionsIndex -= 1
      }
    },
    // 下一题
    nextSelect() {
      this.preDisabled = false;
      if (this.questionsIndex < this.questionsList.length - 1) {
        this.questionsIndex += 1
      }
    },
    goOnQuestions(index) {
      this.questionsIndex = index
      this.itemInfo = this.questionsList[index]
    },
    // 提交
    goSubmit() {
      this.noLength = 0
      this.questionsList.forEach((item) => {
        if (!this.isfinished(item)) {
          this.noLength += 1
        }
      })
      console.log(this.noLength, 'this.noLength');
      if (this.noLength) {
        this.$toast(`还有 ${this.noLength} 道题没有答题，请先答题`);
      } else {
        this.questionsList.forEach(i => {
          if (!i.userAnswer) {
            i.isTrue = false
          }
        })
        this.isOk('userAnswer')
      }
    },
    isOk(str) {
      this.questionsList.forEach((x) => {
        x.options = JSON.stringify(x.options)
        if (x.type == 2) {
          x.userAnswer = x.userAnswer.join(',')
        }
        x.isTrue = x.userAnswer && x.userAnswer == x.answer
      })
      let params = {
        courseId: this.courseId,
        periodId: this.periodIds,
        userCoursePeriodQuestions: this.questionsList,
        userId: this.loginData.userId
      }
      this.$api[str](params).then(res => {
        this.$router.go(-1)
      })
    },
  },

}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  background: #ffffff;
}

.examination_heard {
  height: .9rem;
  width: 100%;
  background: #ffffff;
  margin-top: .02rem;
  line-height: .9rem;
}

.examination_time {
  text-align: right;

  .lable {
    color: #3562DB;
    font-size: .28rem;
  }
}

.validity {
  width: 100%;
  // height: 450px;
  padding: .2rem .4rem 0rem .4rem;
}

.validity_select {
  width: 1.28rem;
  height: .54rem;
  background: #F2F3F5;
  color: #4E5969;
  font-size: .28rem;
  text-align: center;
  line-height: .54rem;
  margin-bottom: .3rem;
}

.validity_title {
  color: #1D2129;
  font-size: .34rem;
  font-weight: bolder;
  line-height: .5rem;
  margin-bottom: .4rem;
  height: 1.6rem;
  overflow: auto;
}

.questionBtn {
  width: 100%;
  display: flex;
  justify-content: center;
  position: fixed;
  // top: .2rem;
  bottom: .6rem;
  text-align: center;
}

.questionSty {
  width: 2.12rem;
  height: .76rem;
  border-radius: 19.98rem;
  border: .02rem solid #C9CDD4;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  right: .4rem;
  bottom: 15%;
  z-index: 9999;
}

.content_right {
  width: 100%;
  height: 100%;
  padding: 20px;

  p {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    margin-bottom: 24px;

    span {
      font-size: 16px;
      font-weight: normal;
      color: #7f848c;
    }
  }

  .answerNum {

    width: 100%;
    height: calc(100% - 60px);
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start; // 替代原先的space-between布局方式

    .item {
      width: 52px;
      height: 52px;
      border: 1px solid #E4E7ED;
      background-color: #fff;
      line-height: 52px;
      text-align: center;
      margin: 0 16px 16px 0; // 间隙为5px
      font-size: 16px;
      cursor: pointer;

      &:nth-child(5n) {
        // 去除第3n个的margin-right
        margin-right: 0;
      }
    }

    .isActive {
      background: #e6effc;
      border: 1px solid #3562db;
    }

    .isfinished {
      position: relative;

      img {
        width: 20px;
        height: 16px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
}

.selectRadiosy {
  width: 100%;
  height: .4rem;
  overflow-y: auto;
}

.analysis {
  width: 20%;
}

.analysisItem {
  width: 100%;
  height: 1rem;
  overflow-y: auto;
}
</style>
