<template>
  <div class="inner">
    <Header  @backFun="goBack()"></Header>
    <div class="jurisdictionBox">
      <div class="jurisdiction"><img src="../../../../assets/images/qst.png" alt=""></div>
      <div class="jurisdiction_lable">暂无实验室权限！</div>
    </div>

  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      
    };
  },
  created() {
    
  },
  mounted() {
    
  },
  methods: {
    // 返回
    goBack() {
      this.$router.go(-1)
    },
 
  }
};
</script>
<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
}

.inner {
  height: 100%;
  background-color: #fff;
  position: relative;

  .contener {
    height: calc(100% - 1.6rem);
    overflow: auto;
  }
}
.jurisdiction img{
  height: 290px;
}
.jurisdiction_lable {
  width: 100%;
  text-align: center;
  font-size: .29rem;
  margin-top: .1rem;
  color: #CCCED3;
}
.jurisdictionBox {
  position: fixed;
  top: 40%;
  left: 51%;
  transform: translate(-50%,-40%);
}
</style>
