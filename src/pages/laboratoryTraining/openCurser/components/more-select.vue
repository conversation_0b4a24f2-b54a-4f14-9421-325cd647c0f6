<template>
	<div class="city-box">
		<div class="choosed-box">
			<div class="province-single" v-for="(item, index) in cityData" :key="index"
				:class="{ active: oneIndex == index }" @click="switchOne(item, index)">
				<div class="choosed-box-name">{{ item.name }}</div>
			</div>
		</div>
		<div class="province-data" v-if="cityData[oneIndex].childList.length != 0">
			<div class="two-single" v-for="(zItem, zIndex) in cityData[oneIndex].childList" :key="zIndex"
				@click="chooseTwo(zItem, zIndex)" :class="{ active: twoIndex == zIndex }">
				<div class="city-name">{{ zItem.name }}</div>
			</div>
		</div>
		<div class="three-data" v-if="cityData[oneIndex].childList.length != 0 && cityData[oneIndex].childList[twoIndex].childList.length != 0">
			<div  v-if="cityData[oneIndex].childList[twoIndex].childList && cityData[oneIndex].childList[twoIndex].childList.length != 0">
				<van-checkbox-group style="margin-top: 5px;" v-model="result"
					v-for="(zItem, zIndex) in cityData[oneIndex].childList[twoIndex].childList" :key="zIndex"
					@change="chooseThree">
					<van-checkbox :name="zItem">{{ zItem.name }}</van-checkbox>
				</van-checkbox-group>
			</div>
		</div>

	</div>
</template>

<script>
export default {

	data() {
		return {
			// divHeight: 0,
			oneIndex: 0,
			twoIndex: 0,
			result: [],
			threeIndex: null,
			params: [],
		};
	},
	props: {
		chooseCityList: {
			type: Array,
			default: () => []
		},
		cityData: {
			type: Array,
			default: () => []
		},
	},
	mounted() {
		// this.getDescBox()
	},
	methods: {
		switchOne(item, index) {
			let oneSwitParams = []
			this.params = []
			oneSwitParams.push(item)
			this.oneIndex = index;
			this.$emit('chooseCity', oneSwitParams)
		},
		chooseTwo(e, zIndex) {
			let paramsIds = []
			this.twoIndex = zIndex
			paramsIds.push(e)
			this.$emit('chooseCity', paramsIds)
		},
		chooseThree(e) {

			this.params = []
			console.log(e,'eeeeeeeeeeeeeeeeeeee');
			this.params = e
			// this.threeIndex = index
			this.$emit('chooseCity', this.params)
		},
	}
}
</script>

<style scoped lang="scss">
.city-box {
	width: 100%;
	height: 450px;
	display: flex;
	padding-top: 10px;
	z-index: 999;
	// justify-content: space-between;
}

.choosed-box {
	max-width: 35%;
	height: 100%;
	background-color: #F7F8FA;
	white-space: nowrap;
	/* 不换行 */
	overflow: hidden;
	/* 超出部分隐藏 */
	text-overflow: ellipsis;
	/* 使用省略号表示被隐藏部分 */
}

.province-data {
	max-width: 35%;
	height: 100%;
	background-color: #FFFFFF;
	white-space: nowrap;
	/* 不换行 */
	overflow: hidden;
	/* 超出部分隐藏 */
	text-overflow: ellipsis;
	/* 使用省略号表示被隐藏部分 */
}

.three-data {
	max-width: 35%;
	height: 100%;
	background-color: #FFFFFF;
	white-space: nowrap;
	/* 不换行 */
	overflow: hidden;
	/* 超出部分隐藏 */
	text-overflow: ellipsis;
	/* 使用省略号表示被隐藏部分 */
}

.choosed-box-name {
	width: 80%;
	white-space: nowrap;
	/* 不换行 */
	overflow: hidden;
	/* 超出部分隐藏 */
	text-overflow: ellipsis;
	/* 使用省略号表示被隐藏部分 */
	padding: 0px 5px;
	line-height: 40px;
}

/deep/ .van-checkbox__icon {
	font-size: 13px !important;
	margin-left: 2px !important;
}

/deep/ .van-checkbox__label {
	width: 100%;
	white-space: nowrap;
	/* 不换行 */
	overflow: hidden;
	/* 超出部分隐藏 */
	text-overflow: ellipsis;
	/* 使用省略号表示被隐藏部分 */
}

.province-single {
	width: 100%;
	white-space: nowrap !important;
	/* 不换行 */
	overflow: hidden !important;
	/* 超出部分隐藏 */
	text-overflow: ellipsis !important;

	/* 使用省略号表示被隐藏部分 */
	&.active {
		width: 100%;
		color: #3562DB;
		font-weight: bold;
		background: #ffffff !important;
	}
}

.two-single {
	width: 100%;
	color: #4E5969;
	font-size: 14px;
	line-height: 30px;
	padding: 0px 5px;

	&.active {
		color: #3562DB;
		font-weight: bold;
	}
}

.city-name {
	width: 100%;
	white-space: nowrap !important;
	/* 不换行 */
	overflow: hidden !important;
	/* 超出部分隐藏 */
	text-overflow: ellipsis !important;
	/* 使用省略号表示被隐藏部分 */
}

/deep/ .van-checkbox__icon--checked .van-icon {
	background-color: #3562DB;
	border-color: #3562DB;
}
</style>
