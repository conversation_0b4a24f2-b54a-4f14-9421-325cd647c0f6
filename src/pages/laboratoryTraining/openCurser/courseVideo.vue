<template>
  <div class="box">
    <Header title="在线学习" @backFun="goBack()"></Header>
    <div class="courseVideo" v-if="videoUrl.type == 2 || videoUrl.type == 3">
      <div><img src="../../../assets/images/kejian1.png" alt="" /></div>
      <div class="courseVideo_lable">
        
        课件学习时间：<span style="color: #333333">{{ countDown }}</span>
      </div>
    </div>
    <!-- 视频 -->
    <div v-if="videoUrl.type == 1" class="course_node_video">
      <video-player
        id="myVideo2"
        class="video-player vjs-custom-skin"
        style=""
        ref="videoPlayer"
        :playsinline="true"
        :options="playerOptions"
        @play="onPlayerPlay($event)"
        @pause="onPlayerPause($event)"
        @timeupdate="onPlayerTimeupdate($event)"
        @ready="playerReadied"
        @ended="onPlayerEnded($event)"
      >
      </video-player>
    </div>

    <!-- 文档图片 -->
    <div v-if="lastFour == 'png' || lastFour == 'jpg'" class="videoImgs">
      <img style="width: 100%; height: 300px" :src="wordPng" alt="" />
    </div>
    <!-- 文档预览 -->
    <div v-if="lastFour == 'docx'" class="docxClass">
      <iframe id="iframe1" frameborder="no" border="0" marginwidth="0" marginheight="0" @load="loadFrame" :src="currentProtocol + fileUrl"></iframe>
    </div>
    <!-- 文本 -->
    <div v-if="videoUrl.type == 3" class="videoText">
      <div style="font-size: 0.3rem; padding: 0px 0.4rem; line-height: 0.5rem" v-html="videoUrl.document"></div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
export default {
  // 返回上一级保存学习进度
  beforeRouteLeave(to, from, next) {
    if (this.videoUrl.type == 1) {
      if (this.initialtime > 0 && this.videoUrl.periodSpace != 100 && this.videoUrl.periodSpace != 99) {
        this.putLearningObj();
      }
    } else if (this.videoUrl.type == 2 || this.videoUrl.type == 3) {
      if (this.videoUrl.periodSpace != 100 && this.videoUrl.periodSpace != 99) {
        this.putLearningObj();
      }
    }
    next();
  },
  data() {
    return {
      newVideoUrl: "",
      currentProtocol: __PATH.PREVIEW_URL,
      countDown: "", // 倒计时时间
      show: false,
      loginData: "",
      fileUrl: "",
      learningDuration: {
        courseId: "", // 课程id
        duration: "", // 课时全部时长
        periodId: "", // 课时id
        userId: "", // 用户id
        textDuration: "", // 文本剩余阅读时长
        videoDuration: "", // 视频阅读时长
      },
      videoUrl: [],
      wordPng: "", // 文本url
      // 视频播放器配置
      playerOptions: {
        // 自定义设置播放速度
        // playbackRates: [0.7, 1.0, 1.5, 2.0],
        // 如果为true,浏览器准备好时开始回放。
        autoplay: false,
        // 默认情况下将会消除任何音频。
        muted: false,
        // 是否视频一结束就重新开始。
        loop: false,
        // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        preload: "auto",
        // zh-CN  需要main.js全局引入才可以生效
        language: "zh-CN",
        // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        aspectRatio: "16:9",
        // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        fluid: true,
        sources: [
          {
            // 视频格式
            type: "video/mp4",
            // 视频地址
            src: "",
          },
        ],
        // 视频封面地址
        poster: "",
        width: document.documentElement.clientWidth,
        // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
        notSupportedMessage: "此视频暂无法播放，请稍后再试",
        controlBar: {
          //  分割线/显示隐藏
          timeDivider: true,
          //  显示总时间
          durationDisplay: true,
          //  是否显示剩余时间功能
          remainingTimeDisplay: false,
          //  是否显示全屏按钮
          fullscreenToggle: true,
          //  播放暂停按钮
          playToggle: true,
          //  音量控制
          volumeMenuButton: true,
          //  当前播放时间
          currentTimeDisplay: true,
          //  进度条显示隐藏
          progressControl: true,
          //  直播流时，显示LIVE
          liveDisplay: true,
          //  播放速率，当前只有html5模式下才支持设置播放速率   如果false右下角 1X 播放速率将会隐藏
          playbackRateMenuButton: false,
        },
      },
      //视频观看起点
      //  playtimes:"",                                        有请求  接口方式设置视频播放起点  步骤:111111111
      // 秒数设置 单位默认s  进度条位置设置   视频观看起点设置     没有    请求接口方式设置视频播放起点  步骤:111111111
      currentTime: 0,
      lastFour: "",
      playtimes: "",
      mediaPath: "",
      initialtime: 0, //记录视频观看时长
      durations: "", // 视频总时长
      isCanQuick: false, // 视频是否可快进
      residueTime: "", //剩余时长
      currentTime: 0,
      maxTime: 0,
    };
  },

  created() {
    console.log(this.initialtime, "this.initialtimethis.initialtime");
    (this.queryInfo = localStorage.getItem("taskInfo") ? JSON.parse(localStorage.getItem("taskInfo")) : ""), (this.loginData = JSON.parse(localStorage.getItem("loginData")));
    this.initialtime = this.$route.query.initialtime; //视频记录时间
  },
  mounted() {
    console.log(this.$route.query,'this.$route.query-------------------------------');
    
    this.videoUrl = this.$route.query.url;
    console.log(this.videoUrl,'this.videoUrl------------------------');
    
    this.durations = this.videoUrl.duration;
    this.residueTime = this.videoUrl.userDuration || this.videoUrl.duration;
    this.startCountDown(); // 阅读时长倒计时
    let obj = JSON.parse(this.videoUrl.url)[0].url;
    this.newVideoUrl = obj;
    console.log(obj, "obj");
    let newArr = obj.split(".");
    this.lastFour = newArr[newArr.length - 1];
    console.log(this.lastFour, "this.lastFour************************************************");
    this.wordPng = obj;
    if (this.lastFour == "docx") {
      this.fileUrl = btoa(this.wordPng);
    }
    this.playerOptions.sources[0].src = this.wordPng;
    // this.$utils.backBtn(this.goBack);
  },
  // destroyed() {
  //   clearInterval(this.interval);
  // },

  methods: {
    // 返回
    goBack() {
      this.$router.go(-1);
    },
    // 阅读时长倒计时
    startCountDown() {
      this.interval = setInterval(() => {
        this.residueTime--;
        let hour = Math.floor((this.residueTime / 60 / 60) % 24); // 时
        let minutes = Math.floor(this.residueTime / 60);
        let seconds = Math.floor(this.residueTime % 60);
        minutes = minutes < 10 ? "0" + minutes : minutes;
        seconds = seconds < 10 ? "0" + seconds : seconds;
        this.countDown = hour + ":" + minutes + ":" + seconds;
        console.log(this.countDown,'this.countDownthis.countDownthis.countDown');
        if(this.residueTime === 0){
          clearInterval(this.interval);
          this.putLearningObj()
        }
      }, 1000);
    },
    // 保存学习进度
    putLearningObj() {
      let params = {
        courseId: this.videoUrl.courseId,
        duration: this.durations,
        periodId: this.videoUrl.id,
        userId: this.loginData.userId,
        textDuration: this.residueTime, // 文本剩余阅读时长
        videoDuration: "",
      };
      if (this.videoUrl.type == 1) {
        params.videoDuration = this.initialtime;
      }
      console.log(params, "params");
      this.$api.videoTime(params).then((res) => {
        this.$toast.success("保存学习进度成功");
      });

      // if (this.videoUrl.type == 2 || this.videoUrl.type == 3) {
      //   params.textDuration = this.residueTime
      // } else if (this.videoUrl.type == 1) {
      //   params.textDuration = '',
      //   params.videoDuration = this.initialtime
      // }
    },
    // loadFrame函数是iframe加载完成后回调函数
    loadFrame() {
      // 获取iframe节点
      const iframeBox = document.getElementById("iframe1");
      // 获取iframe html文件
      const doc = iframeBox.contentWindow.document;
      const elementInsideIframe = doc.querySelector(".my-photo");
      elementInsideIframe.style.width = "100%";
    },
    showPopup() {
      this.show = !this.show;
    },
    gerList() {
      // this.videoUrl.src = this.mediaPath
    },
    playVideo(item) {
      console.log(item, "ITEM");
    },
    onPlayerPlay(player) {
      console.log(player, "player");
      // 播放回调
    },
    // 暂停
    onPlayerPause(player) {
      console.log(player, "player");
      // 暂停回调
    },
    // 视频播放结束
    onPlayerEnded($event) {
      if (this.videoUrl.periodSpace != 100 && this.videoUrl.periodSpace != 99) {
        this.putLearningObj();
      }
      //播放完成回调
    },
    // 获取视频播放进度
    onPlayerTimeupdate(player) {
      this.durations = parseInt(player.cache_.duration); //播放的总时长
      this.initialtime = parseInt(player.cache_.currentTime); // 实时播放进度 秒数
      if (this.videoUrl.periodSpace != 100 && this.videoUrl.periodSpace != 99) {
        this.$refs.videoPlayer.$el.firstChild.getElementsByClassName("vjs-control-bar")[0].getElementsByClassName("vjs-progress-control")[0].style.pointerEvents = "none";
      }
    },
    playerReadied(player) {
      let time = this.videoUrl.userDuration;
      if (time && this.videoUrl.periodSpace != 100 && this.videoUrl.periodSpace != 99) {
        player.currentTime(time);
      } else {
        player.currentTime();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  background: #ffffff;
}

video {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

video:focus {
  outline: none;
}

.docxClass {
  width: 100%;
  height: 100%;

  img {
    width: 100% !important;
  }

  #iframe1 {
    width: 100%;
    height: 100%;
  }
}

/deep/ .vjs-custom-skin > .video-js .vjs-control-bar {
  font-size: 0.24rem;
}
.vjs-custom-skin .video-js .vjs-tech {
  width: 100vw; /* 宽度设置为视口宽度 */
  height: 56.25vw; /* 高度设置为宽度的56.25%，保持宽高比 */
}
.course_node_video {
  width: 100%;
  height: 100%;
  text-align: center;
}
// .course_node_video_copy {
//   position: fixed;
//   left: 0;
//   top: 0;
//   width: 100vmax;
//   height: 100vmin;
//   transform-origin: top left;
//   transform: rotate(90deg) translateY(-100vmin);
//   z-index: 100000;
// }
/deep/ .video-js {
  height: 100%;
}

// .video-js .vjs-vjs-tech {
//   position: absolute;
//   top: .2rem;
//   object-fit: fill !important;
// }

// /deep/ .vjs-poster {
//   background-size: cover;
// }

// /deep/ .vjs-progress-control {
//   pointer-events: none !important;
// }

.windowing {
  width: 2rem;
  height: 1rem;
  border-radius: 0.1rem;
  margin: 0.2rem 0.4rem;
  text-align: center;
  line-height: 1rem;
  color: #000000;
  font-size: 0.28rem;
  background-color: #3562db;
}

.coursrName {
  width: 100%;
  height: 1.08rem;
  line-height: 1.08rem;
  font-size: 0.32rem;
  color: #1d2129;
  border-bottom: 0.02rem solid #e5e6eb;
  text-align: center;
}

.coursrVideo {
  margin-top: 0.2rem;
}

/deep/ .vjs-custom-skin > .video-js .vjs-big-play-button {
  border-radius: 50%;
  width: 1.48rem !important;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.5);
  top: 50%;
  left: 52%;
  // transform: translate(-50%,-50%);
}

.courseVideo {
  // width: 100%;
  height: 0.9rem;
  background-color: #e6effc;
  display: flex;
  margin: 0rem 0rem 0.4rem 0rem;
  line-height: 0.9rem;
  padding: 0rem 0.4rem;
  justify-content: flex-end;
}

.courseVideo_lable {
  font-size: 0.3rem;
  color: #3562db;
  margin-top: 0.04rem;
  margin-left: 0.16rem;
}

.videoText {
  width: 100%;
  height: 80%;
  overflow-y: auto;
}

.videoImgs {
  width: 100%;
  text-align: center;
}
</style>
