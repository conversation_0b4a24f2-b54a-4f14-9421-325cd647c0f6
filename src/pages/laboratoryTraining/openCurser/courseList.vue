<template>
  <div class="inner" v-if="loginData.isFalg == 0">
    <Header title="公开课程" @backFun="goBack()"></Header>
    <div class="courseTitle">
      <van-search v-model="finList.courseName" @search="onSearch" :clearable="true" @clear="handleClear"
        placeholder="请输入搜索关键词" />
    </div>
    <div class="headline" @click="openCourse">
      <div v-if="cityListName == ''">全部课程 <span>></span></div>
      <div class="headline_name" v-else>{{ cityListName }}</div>
    </div>
    <div style="padding: 0px 10px;" class="conter">
      <van-pull-refresh v-if="courseData.length > 0" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="headlineDetail" v-for="(item, index) in courseData" :key="index" @click="openDetail(item)">
            <div class="head_top">
              <div class="headImg">
                <img :src="item.coverUrl" alt="">
              </div>
              <div class="introduction">
                <div class="head_item" id="posthard">{{ item.courseName }}</div>
                <div class="head_item">{{ item.comments }}</div>
              </div>
            </div>
            <div class="line"></div>
            <div class="head_conter">
              <div style="width: 100%; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">课程类型：<span
                  class="head_lable">{{ item.subjectName }}</span></div>
              <div>课时数：<span class="head_lable"><span>{{ item.tunoppoluad }}</span>课时</span></div>
            </div>
            <div class="head_footer">
              <div style="margin-top: 10px;"><span class="study">{{ item.viewCount || '0' }}</span>人学过</div>
              <div class="btn">
                <van-button v-if="item.studyState == 0" style="width: 115px; height: 35px; background-color: #3562DB;"
                  type="info"><van-icon style="margin-right: 10px;" name="star-o" />{{ item.studyStateName
                  }}</van-button>
                <van-button v-if="item.studyState == 1"
                  style="width: 115px; height: 35px; background-color: #3562DB; border: #3562DB;"
                  @click.stop="collect(item)" type="info">{{ item.studyStateName }}</van-button>
                <van-button v-if="item.studyState == 2" disabled
                  style="width: 115px; height: 35px; background-color: #3562DB; border: #3562DB;" type="info">{{
                    item.studyStateName }}</van-button>
                <van-button v-if="item.studyState == 3" disabled
                  style="width: 115px; height: 35px; background-color: #C9CDD4; border: #C9CDD4;" type="info">{{
                    item.studyStateName }}</van-button>
                <van-button v-if="item.studyState == 4" disabled
                  style="width: 115px; height: 35px; background-color: #C9CDD4; border: #C9CDD4;" type="info">{{
                    item.studyStateName }}</van-button>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
    <van-popup v-model="show" position="bottom" :style="{ height: '80%' }">
      <div>
        <more-select ref="moreselects" :cityData="cityData" @chooseCity="chooseCity" @switchCity="switchCity"
          @closeCity="closeCity" @queryCity="queryCity"></more-select>
      </div>
      <div class="btnsy">
        <van-button
          style="width: 3.6rem; height: .88rem; background-color: #E6EFFC; color: #3562DB; border: #E6EFFC; margin-right: .1rem;"
          type="info" @click="rest">重置</van-button>
        <van-button style="width: 3.6rem; height: .88rem; background-color: #3562DB;" type="info"
          @click="submit">确定</van-button>
      </div>
    </van-popup>
  </div>
  <div v-else>
    <div>
      <permissionPrompt></permissionPrompt>
    </div>
  </div>
</template>
<script>
import moreSelect from "./components/more-select.vue"
import permissionPrompt from "./components/permissionPrompt.vue"
import moment from "moment";
import axios from "axios";
import ImageCompressor from "image-compressor.js";
export default {
  components: { moreSelect, permissionPrompt },
  data() {
    return {
      show: false,
      loginData: '',
      value: '',
      isLoading: false,
      loading: false,
      finished: false,
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      courseData: [], // 课程列表
      finList: {
        courseName: '',
      },
      cityData: [],
      listName: [],
      oneName: '',
      twoName: '',
      cityListName: "",
      subtotal: '',
    };
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    this.loginData = JSON.parse(localStorage.getItem("loginData"));
    if(this.loginData.isFalg == 1) {
      return
    }
    this.getCourseList()
    this.getTreeAllList()
  },
  mounted() {
    // wx.onHistoryBack(() => {
    //   this.$router.push('/homePage')
    //   return false
    // });
  },
  methods: {
    // 监听返回
    goBack() {
      this.$router.push('/laboratoryTraining')
    },
    getTreeAllList() {
      let params = {
        pageNo: this.pageParmes.pageNo,
        pageSize: this.pageParmes.pageSize,
      }
      this.$api.treeAllList(params).then((res) => {
        console.log(res, 'RES');
        this.cityData = res.records
      })
    },
    // 选中的分类筛选数据
    chooseCity(parms) {
      let cityData = this.cityData
      console.log(cityData, 'cityData');
      console.log(parms, 'parms123');
      this.listName = []
      this.ids = []
      for (let item of parms) {
        this.listName.push(item.name)
        if (item.level != 1) {
          this.subtotal = item.free3
        }
        this.ids.push(item.id)
      }
    },
    submit() {
      this.show = false
      if (this.subtotal) {
        this.cityListName = this.subtotal + '>' + this.listName.join('>')
      } else {
        this.cityListName = this.listName.join('>')
      }
      this.getCourseList()
    },
    rest() {
      this.cityListName = ''
      this.ids = []
      this.show = false
      this.$refs.moreselects.result = []
      this.getCourseList()
    },
    queryCity() { },

    switchCity(data) {
      // this.oneName = ''
      // for (let item of data) {
      //   this.oneName = item.name
      //   console.log(this.oneName, 'this.oneName');
      // }
      // console.log(data, 'DAYA');
    },
    closeCity(datas) {
      // this.twoName = ''
      // for (let item of datas) {
      //   this.twoName = item.name
      //   console.log(this.twoName, 'this.oneName');
      // }
      // console.log(datas, 'DAYA');
    },
    // 下拉刷新
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.courseData = []
      this.getCourseList()
    },
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = false
      this.loading = true
      this.getCourseList()
    },
    // 搜索筛选
    onSearch(val) {
      // this.$toast(val)
      this.getCourseList()
    },
    //搜索筛选
    handleClear() {
      this.finList.courseName = ''
      this.getCourseList()
    },
    // 获取公开课程列表
    getCourseList() {
      let params = {
        pageNo: this.pageParmes.pageNo,
        pageSize: this.pageParmes.pageSize,
        ...this.finList,
      }
      if (this.cityListName.length != 0) {
        params.subjectId = this.ids.join(',')
      }
      this.$api.openCourseList(params).then((res) => {
        this.courseData = res.list
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
      })
    },
    // 打开课程分类弹窗
    openCourse() {
      this.show = !this.show
    },
    // 跳转课程详情
    openDetail(item) {
      this.$router.push({
        path: '/courseDetail',
        query: {
          courseId: item.id
        }
      })
    },
    // 收藏公开课
    collect(item) {
      let params = {
        courseId: item.id,
        userId: this.loginData.userId
      }
      this.$api.getCollect(params).then((res) => {
        this.$toast.success('收藏成功！')
        this.getCourseList()
      })
    },
  }
};
</script>
<style scoped lang="scss">
.inner {
  height: 100%;
  background-color: #f2f4f9;
  position: relative;

  .contener {
    height: calc(100% - 1.6rem);
    overflow: auto;
  }

  .courseTitle {
    margin-top: .1rem;
    width: 100%;
    height: 1.36rem;
    padding: .16rem .1rem 0rem .1rem;
    box-sizing: border-box;
    background: #FFFFFF;
  }
}

.conter {
  height: 72%;
  overflow-y: auto;

}

.headline {
  width: 100%;
  overflow-x: scroll;
  white-space: nowrap;
  height: .6rem;
  line-height: .6rem;
  margin: .2rem 0rem;
  color: #1D2129;
  font-size: .32rem;
  padding: 0rem .2rem;
  box-sizing: border-box;

  span {
    color: #86909C;
  }
}

.headline_name {
  width: 100%;
  overflow-x: scroll;
  font-size: .28rem;
  color: #1D2129;

  &::-webkit-scrollbar {
    display: none;
    /* 对于Webkit浏览器，如Chrome，Edge等 */
  }
}

.headlineDetail {
  width: 100%;
  height: 3.84rem;
  background-color: #FFFFFF;
  border-radius: .16rem .16rem .16rem .16rem;
  padding: .2rem;
  box-sizing: border-box;
  margin: .2rem 0rem;
}

.headImg {
  width: 2rem;
  height: 1.16rem;
  margin-right: .4rem;
}

.headImg img {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.head_top {
  display: flex;
}

.introduction {
  width: 65%;

  .head_item {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #86909C;
    font-size: .26rem;
  }
}

#posthard {
  font-size: .36rem;
  color: #1D2129;
  font-weight: 500;
  margin: .1rem 0rem .3rem 0rem;
}

.line {
  width: 100%;
  margin: .3rem 0rem;
  border: .01rem solid #F2F3F5;
}

.head_conter {
  font-size: .28rem;
  color: #86909C;
}

.head_conter>div {
  margin: .2rem 0rem;
}

.head_lable {
  color: #4E5969;

}

.head_footer {
  display: flex;
  justify-content: space-between;
}

.study {

  font-size: .32rem;
  color: #FF7D00;
}

.btn {
  color: #3562DB;
  // width: 2rem;
  // height: .64rem;
}

.notList {
  position: relative;
  height: calc(100% - 1rem);

  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url('../../../assets/images/noData.png') 0 40% no-repeat;
    background-size: 100% auto;

    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}

.btnsy {
  display: flex;
  justify-content: space-between;
  padding: 0rem .1rem;
  box-sizing: border-box;
  position: absolute;
  left: 0;
  bottom: .4rem;
}

.treeList {
  width: 100%;
  height: 8rem;
  display: flex;
  justify-content: space-between;
  border: .02rem solid #000;
}

.stair {
  width: 30%;
  height: 100%;
  border: .02rem solid #000;
}

.second {
  width: 30%;
  height: 100%;
  border: .02rem solid #000;
}

.threeLevel {
  width: 30%;
  height: 100%;
  border: .02rem solid #000;
}
/deep/ .van-search__content {
  background-color: #F2F3F5;
}
</style>
