<template>
  <div class="inner" v-if="loginData.isFalg == 0">
    <Header  title="我的课程" @backFun="goBack()"></Header>
    <div class="switchover">
      <div style="display: flex; margin-top: 0.1rem;">
        <div class="switbox" v-for="(x, y) in switchTab" :key="y" :class="{ active: switchIndex == y }"
          @click="openSwitch(y)">{{ x.name }} <div class="linesy" v-if="switchIndex == y"></div>
        </div>
      </div>
      <div><van-search v-model="finList.courseName" @search="onSearch" :clearable="true" @clear="handleClear"
          placeholder="请输入搜索关键词" /></div>
      <div class="headline">
        <div class="headlineLable" v-for="(item, index) in taskCourseList" :key="index"
          :class="{ active: courseIndex == index }" @click="openTaskCourses(index)">{{ item.label }}</div>
      </div>
    </div>
    <div style="padding: 0rem .2rem;" class="conter" v-if="switchIndex == 0">
      <van-pull-refresh v-if="taskCourseDataList.length > 0" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="headlineDetail" v-for="(item, index) in taskCourseDataList" :key="index"
            @click="openDetail(item)">
            <div class="head_top">
              <div class="headImg"><img :src="item.coverUrl" alt=""></div>
              <div class="introduction">
                <div class="head_item" id="posthard">{{ item.courseName }}</div>
                <div class="head_item">{{ item.comments }}</div>
              </div>
            </div>
            <div class="line"></div>
            <div class="head_conter">
              <div style="width: 100%; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">课程类型：<span
                  class="head_lable">{{ item.subjectName }}</span></div>
              <div>所属任务：<span class="head_lable"><span>{{ item.learnName || '--' }}</span></span></div>
              <div>课时数：<span class="head_lable"><span>{{ item.tunoppoluad }}</span>课时</span></div>
            </div>
            <div class="head_footer">
              <div style="margin-top: .2rem;">已学 <span class="study">{{ item.studyCount || '0' }} </span>课时</div>
              <div class="btn">
                <van-button v-if="item.studyState == 0"
                  style="width: 2.3rem; height: .7rem; background-color: #3562DB; font-size: .28rem;"
                  type="info">学习中</van-button>
                <van-button v-if="item.studyState == 2"
                  style="width: 2.3rem; height: .7rem; background-color: #3562DB; font-size: .28rem;"
                  type="info">未学习</van-button>
                <van-button v-if="item.studyState == 3" disabled
                  style="width: 2.3rem; height: .7rem; background-color: #C9CDD4; border: #C9CDD4; font-size: .28rem;"
                  type="info">已学完</van-button>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
    <div style="padding: 0rem .2rem;" class="conter" v-if="switchIndex == 1">
      <van-pull-refresh v-if="courseData.length > 0" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="headlineDetail" v-for="(item, index) in courseData" :key="index" @click="openDetail(item)">
            <div class="head_top">
              <div class="headImg"><img :src="item.coverUrl" alt=""></div>
              <div class="introduction">
                <div class="head_item" id="posthard">{{ item.courseName }}</div>
                <div class="head_item">{{ item.comments }}</div>
              </div>
            </div>
            <div class="line"></div>
            <div class="head_conter">
              <div style="width: 100%; overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">课程类型：<span
                  class="head_lable">{{ item.subjectName }}</span></div>
              <div>课时数：<span class="head_lable"><span>{{ item.tunoppoluad }}</span>课时</span></div>
            </div>
            <div class="head_footer">
              <div style="margin-top: .2rem;">已学 <span class="study">{{ item.studyCount || '0' }} </span>课时</div>
              <div class="btn">
                <van-button v-if="item.studyState == 0"
                  style="width: 2.3rem; height: .7rem; background-color: #3562DB; font-size: .28rem;"
                  type="info">学习中</van-button>
                <van-button v-if="item.studyState == 2"
                  style="width: 2.3rem; height: .7rem; background-color: #3562DB; font-size: .28rem;"
                  type="info">未学习</van-button>
                <van-button v-if="item.studyState == 3" disabled
                  style="width: 2.3rem; height: .7rem; background-color: #C9CDD4; border: #C9CDD4; font-size: .28rem;"
                  type="info">已学完</van-button>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <div>
      <permissionPrompt></permissionPrompt>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import axios from "axios";
import ImageCompressor from "image-compressor.js";
import permissionPrompt from "../openCurser/components/permissionPrompt.vue"
export default {
  components: {permissionPrompt},
  data() {
    return {
      loginData: '',
      isLoading: false,
      loading: false,
      finished: false,
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      finList: {
        courseName: '',
        studyState: '',
      },
      switchIndex: 0,
      courseIndex: '',
      taskCourseList: [ // 任务课程
        {
          value: '',
          label: '全部课程',
        },
        {
          value: '2',
          label: '未学习'
        },
        {
          value: '0',
          label: '学习中'
        },
        {
          value: '3',
          label: '已学完'
        }
      ],
      taskCourseDataList: [],
      coursesList: [ // 收藏课程
        {
          lable: '全部课程',
          value: '',
        },
        {
          lable: '学习中',
          value: '0',
        },
        {
          lable: '收藏学习',
          value: '1',
        },
        {
          lable: '已收藏',
          value: '2',
        },
        {
          lable: '已学习',
          value: '3',
        }
      ],
      courseData: [],
      switchTab: [
        {
          name: '任务课程',
          id: '0',
        },
        {
          name: '收藏课程',
          id: '1',
        }
      ],
    };
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    this.loginData = JSON.parse(localStorage.getItem("loginData"));
    if(this.loginData.isFalg == 1) {
      return
    }
    this.getMyCourse()
  },
  mounted() {
    // wx.onHistoryBack(() => {
    //   this.$router.push('/homePage')
    //   return false
    // });
  },
  methods: {
    // 下拉刷新
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.courseData = []
      this.getMyCourse()
    },
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = false
      this.loading = true
      this.getMyCourse()
    },
    // 返回按钮
    goBack() {
      this.$router.push('/laboratoryTraining')
    },
    // 搜索筛选
    onSearch(val) {
      // this.$toast(val)
      this.getMyCourse()
    },
    //搜索筛选
    handleClear() {
      this.finList.courseName = ''
      this.getMyCourse()
    },
    // 获取我的课程列表
    getMyCourse() {
      let params = {
        pageNo: this.pageParmes.pageNo,
        pageSize: this.pageParmes.pageSize,
        ...this.finList,
      }
      if (this.switchIndex == 0) {
        params.type = 1
      } else {
        params.type = 0
      }
      this.$api.myCourseNewList(params).then((res) => {
        this.courseData = res.list;
        if (this.switchIndex == 0) {
          this.taskCourseDataList = res.list;
        }
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
      })
    },
    // 打开课程分类弹窗
    openCourse() {
      this.$router.push({
        path: '/searchOpty',
      })
    },
    // 跳转课程详情
    openDetail(item) {
      this.$router.push({
        path: '/courseDetail',
        query: {
          courseId: item.courseId
        }
      })
    },
    // 收藏公开课
    // collect(item) {
    //   let params = {
    //     courseId: item.courseId,
    //     userId: this.loginData.userId
    //   }
    //   this.$api.getCollect(params).then((res) => {
    //     alert('收藏成功');
    //     this.getMyCourse()
    //   })
    // },
    openTaskCourses(el) {
      this.courseIndex = el
      this.finList.studyState = this.courseIndex
      if (this.courseIndex == 0) {
        this.finList.studyState = ''
      } else if (this.courseIndex == 1) {
        this.finList.studyState = 2
      } else if (this.courseIndex == 2) {
        this.finList.studyState = 0
      } else if (this.courseIndex == 3) {
        this.finList.studyState = 3
      }
      this.getMyCourse()
    },
    // 选择名称筛选
    openSwitch(e) {
      this.switchIndex = e
      this.getMyCourse()
      console.log(this.switchIndex, 'this.switchIndex');
    },
  }
};
</script>
<style scoped lang="scss">
.inner {
  height: 100%;
  // padding-top: .1rem;
  background-color: #f2f4f9;
  position: relative;

  .contener {
    height: calc(100% - 1.6rem);
    overflow: auto;
  }

  .courseTitle {
    width: 100%;
    height: 1rem;
    //  padding: .16rem .1rem 0rem .1rem;
    margin-top: .04rem;
    box-sizing: border-box;
    background: #FFFFFF;
  }
}

.switchover {
  width: 100%;
  height: 2.88rem;
  background-color: #fff;
}

.switbox {
  // position: relative;
  width: 100%;
  padding: 0rem .4rem;
  height: .6rem;
  line-height: .6rem;
  font-size: .32rem;
  color: #86909C;
  font-weight: bold;
  margin-top: .2rem;
  text-align: center;

  &.active {
    color: #1D2129;

  }
}

.linesy {
  width: .48rem;
  height: .06rem;
  border-radius: .04rem;
  background-color: #3562DB;
  margin-left: 1.2rem;
}

.headline {
  // width: 100%;
  padding: 0rem .24rem;
  height: .6rem;
  line-height: .6rem;
  margin-top: .12rem;
  display: flex;
  justify-content: space-between;

  .headlineLable {
    width: 23%;
    background-color: #F7F8FA;
    text-align: center;

    // margin-left: .24rem;
    // margin-right: .2rem;
    &.active {
      color: #3562DB;
      background-color: #E6EFFC;
    }
  }
}

.conter {
  // width: 100%;
  height: 65%;
  overflow-y: auto;

}

.conter::-webkit-scrollbar {
  width: 0rem;
  height: 0rem;
}

.headlineDetail {
  // width: 100%;
  height: 4.1rem;
  background-color: #FFFFFF;
  border-radius: .16rem .16rem .16rem .16rem;
  padding: .2rem;
  box-sizing: border-box;
  margin: .2rem 0rem;
}

.headImg {
  width: 2rem;
  height: 1.16rem;
  margin-right: .4rem;
}

.headImg img {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.head_top {
  display: flex;
}

.introduction {
  width: 65%;

  .head_item {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #86909C;
    font-size: .26rem;
  }
}

#posthard {
  font-size: .36rem;
  color: #1D2129;
  font-weight: 500;
  margin: .1rem 0rem .3rem 0rem;
}

.line {
  width: 100%;
  margin: .3rem 0rem;
  border: .01rem solid #F2F3F5;
}

.head_conter {
  font-size: .28rem;
  color: #86909C;
}

.head_conter>div {
  margin: .2rem 0rem;
}

.head_lable {
  color: #4E5969;
}

.head_footer {
  margin-top: -0.2rem;
  display: flex;
  justify-content: space-between;
}

.study {

  font-size: .32rem;
  color: #FF7D00;
}

.notList {
  position: relative;
  height: calc(100% - 1rem);

  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url('../../../assets/images/noData.png') 0 40% no-repeat;
    background-size: 100% auto;

    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}

.btn {
  color: #3562DB;

  // width: 2rem;
  // height: .64rem;
}
/deep/ .van-search__content {
  background-color: #F2F3F5;
}
</style>
