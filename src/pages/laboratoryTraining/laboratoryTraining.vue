<template>
  <div class="wrapper">
    <Header title="教育培训" @backFun="goback"></Header>
    <div class="content">
      <div class="card-item" v-for="item in filterMenuList" :key="item.value" @click="goPage(item)">
        <van-badge :content="item.count" max="99">
          <img :src="item.img" />
        </van-badge>
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { mapState } from "vuex";
import { Toast } from "vant";
Vue.use(Toast);
export default {
  data() {
    return {
      filterMenuList: [
      {
          value: "1",
          name: "公共课程",
          img: require("../../assets/images/kecheng.png"),
          count: "",
          path: "/courseList"
        },
        {
          value: "2",
          name: "我的任务",
          img: require("../../assets/images/myRw.png"),
          count: "",
          path: '/myTaskIndex'
        },
        {
          value: "3",
          name: "我的课程",
          img: require("../../assets/images/mykc.png"),
          count: "",
          path: '/courseIndex'
        },
        {
          value: "4",
          name: "培训任务",
          img: require("../../assets/images/peixun.png"),
          count: "",
          path: '/trainIndex'
        },
        {
          value: "5",
          name: "考试任务",
          img: require("../../assets/images/ksrw.png"),
          count: "",
          path: '/examinationIndex'
        }
      ],
      // menuList: [],
      timer: null
    };
  },
  created() {
    this.getMenuAuth()
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"]),
  },
  mounted() {
   
    this.getSafeTySum()
    // this.$YBS.apiCloudEventKeyBack(this.goback);
    
    // this.getQuestionnaireCount();
  },
  methods: {
    // 获取菜单权限
    getMenuAuth() {
      console.log(this.loginInfo.phone,'89');
      let params = {
        mobile: this.loginInfo.userName,
        platform: 2
      }
      this.$api.loginLaboratory(params).then((res) => {
        localStorage.setItem("loginData", JSON.stringify({...res,isFalg: '0'}));
      }).catch((err)=>{
        localStorage.setItem("loginData", JSON.stringify({"systemCode":"course",isFalg: '1'}))
        
      })
    },
     // 获取安全教育徽章数
     getSafeTySum() {
      this.$api.safeTatistics({}).then( res => {
        this.filterMenuList.forEach((item) => {
          console.log(item);
          if(item.value == 4) {
            item.count = res.myTrainCount
          } else if(item.value == 2) {
            item.count = res.myTaskCunt
          } else if(item.value == 5) {
            item.count = res.examCount
          }
        })
      })
    },
    goPage(item) {
      console.log(item,'BALLLL');
        this.$router.push({
          path: item.path
        });
     
    },
    getQuestionnaireCount() {
      let params = {
        questionStatus: "publish",
        unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
        hospitalCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        userName: JSON.parse(localStorage.getItem("loginInfo")).staffName
      };
      this.$api.selfQuestionCount(params).then(res => {
        if (res > 0) {
          this.filterMenuList[0].count = res;
        } else {
          this.filterMenuList[0].count = "";
        }
      });
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  background-color: #f2f4f9;
  height: 100vh;
}
.content {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  padding: 6px;
}
.card-item {
  width: 29vw;
  height: 32vw;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: calc((100% - 29vw * 3) / 3 / 2);
  // margin-bottom: 16px;
}
.card-item img {
  width: 40px;
}
.card-item span {
  transform: translateY(12px);
}
</style>
