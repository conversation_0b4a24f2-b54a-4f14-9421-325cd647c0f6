<template>
  <div class="box" v-if="loginData.isFalg == 0">
    <Header  title="考试任务" @backFun="goBack()"></Header>
    <div class="seach">
      <div>
        <van-search v-model="finList.name" @search="onSearch" :clearable="true" @clear="handleClear"
          placeholder="请输入搜索关键词" />
      </div>
      <div class="headline">
        <div class="headlineLable" v-for="(k, m) in coursesList" :key="m" :class="{ active: courseIndex == m }"
          @click="openCourses(m)">{{ k.lable }}</div>
      </div>
    </div>
    <div class="examinationTitle" style="padding: 0rem .2rem;">
      <van-pull-refresh v-if="examinationList.length > 0" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="examinationData" v-for="(item, index) in examinationList" :key="index"
            @click.stop="openExmainDetail(item)">
            <div class="examination_bt">
              <!-- <div class="examinationImg"><img src="../../assets/images/icon分类图标@2x.png" alt=""></div> -->
              <div class="examination_mc">
                <div class="examination_mcLable">{{ item.name }}</div>
                <div class="examination_mcItem">{{ item.disc }}</div>
              </div>
            </div>
            <div class="examinLine"></div>
            <div style="width: 100%;">
              <div class="itemLable">
                <div class="conterlable">所属科目：</div>
                <div class="conterItem">{{ item.subjectName }}</div>
              </div>
              <div class="itemLable">
                <div class="conterlable">考试时限：</div>
                <div class="conterItem">{{ item.startTime || '-' }} 至 {{ item.endTime || '-' }}</div>
              </div>
              <div class="itemLable">
                <div class="conterlable">考试时长：</div>
                <div class="conterItem">{{ item.duration }} 分钟</div>
              </div>
              <div class="itemLable">
                <div class="conterlable">考题数量：</div>
                <div class="conterItem">{{ item.questionNum || '0' }}</div>
              </div>
              <div class="itemLable">
                <div class="conterlable">考试总分：</div>
                <div class="conterItem">{{ item.score }}</div>
              </div>
              <div class="itemLable">
                <div class="conterlable">所属单位：</div>
                <div class="conterItem">{{ item.deptName }}</div>
              </div>
            </div>
            <div class="examinBtn">
              <!-- <div class="notPass"><van-icon name="warning" style="margin-right: .1rem;"/>未通过</div> -->
              <div v-if="item.pass == 1" class="notPass"><van-icon name="warning-o"
                style="margin-right: .1rem;" />未通过
              </div>
              <div v-if="item.pass == 0" class="pass"><van-icon name="checked"
                  style="margin-right: .1rem;" />通过
              </div>
              <div class="passItem" v-if="item.taskStatusApp == '3' && item.respond == 1"><van-button
                  style="height: .64rem; background-color:#3562DB; border-radius: .04rem;" type="info"
                  @click.stop="openQuestion(item)">立即考试</van-button></div>
              <div class="passItem" v-if="item.taskStatusApp == '4' && item.respond == 1"><van-button
                  style="width: 1.76rem; height: .64rem; background-color:#3562DB; border-radius: .04rem;" type="info"
                  @click.stop="openQuestion(item)">进行中</van-button></div>
              <div class="passItem" v-if="item.taskStatusApp == '2'"><van-button
                  style="width: 1.76rem; height: .64rem; background-color:#C9CDD4; border: #C9CDD4; border-radius: .04rem;"
                  type="info" @click.stop="openExmainDetail(item)">已考试</van-button></div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <div>
      <permissionPrompt></permissionPrompt>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import moment from 'moment';
import permissionPrompt from "../openCurser/components/permissionPrompt.vue"
export default {
  components: { permissionPrompt },
  data() {
    return {
      isLoading: false,
      loading: false,
      finished: false,
      finList: {
        name: '',
        taskStatusApp: '',
      },
      examinationList: [],
      coursesList: [
        {
          lable: '全部',
          value: '',
        },
        {
          lable: '未开始',
          value: '3',
        },
        {
          lable: '考试中',
          value: '4',
        },
        {
          lable: '已考试',
          value: '2',
        },
        {
          lable: '超时',
          value: '1',
        }
      ],
      courseIndex: 0,
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      loginData: "", //路由传过来的值
    }
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    this.loginData = JSON.parse(localStorage.getItem("loginData"));
    if(this.loginData.isFalg == 1) {
      return
    }
    this.getquestionsList()

  },

  mounted() {
    // wx.onHistoryBack(() => {
    //   this.$router.push('/homePage')
    //   return false
    // });
  },
  watch: {

  },
  methods: {
    // 下拉刷新
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.examinationList = []
      this.getquestionsList()
    },
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = false
      this.loading = true
      this.getquestionsList()
    },
    // 监听返回
    goBack() {
      this.$router.push('/laboratoryTraining')
    },
    formatTimestamp(timestamp) {
      return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
    },
    // 搜索筛选
    onSearch(val) {
      // this.$toast(val)
      this.getquestionsList()
    },
    //搜索筛选
    handleClear() {
      this.finList.name = ''
      this.getquestionsList()
    },
    // 选择分类
    openCourses(el) {
      console.log(el, 'EL');
      this.courseIndex = el
      if (this.courseIndex == 0) {
        this.finList.taskStatusApp = ''
      } else if (this.courseIndex == 1) {
        this.finList.taskStatusApp = 3
      } else if (this.courseIndex == 2) {
        this.finList.taskStatusApp = 4
      } else if (this.courseIndex == 3) {
        this.finList.taskStatusApp = 2
      } else {
        this.finList.taskStatusApp = 1
      }
      // this.finList.taskStatusApp = this.courseIndex
      this.getquestionsList()
    },
    // 试题列表
    getquestionsList() {
      let params = {
        current: this.pageParmes.pageNo,
        size: this.pageParmes.pageSize,
        listType: 1,
        appListSign: 0,
        ...this.finList,
      }
      this.$api.questionsList(params).then((res) => {
        this.examinationList = res.list
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
      })
    },
    // 点击跳转详情
    openExmainDetail(item) {
      this.$router.push({
        path: '/examinationInDetail',
        query: {
          examinationId: item.id
        }
      })
    },
    // 打开试题列表
    openQuestion(item) {
      const today = new Date().toISOString().split('T')[0];
      const startDate = new Date(item.startTime).toISOString().split('T')[0];
      const endDate = new Date(item.endTime).toISOString().split('T')[0];
      if (today >= startDate && today <= endDate) {
        this.$router.push({
          path: '/questionsList',
          query: {
            examinationId: item.id
          }
        })
      } else {
        this.$toast('考试暂未开始');
      }

    }
  },

}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  background: #f2f4f9;
}

.seach {
  height: 2.2rem;
  background: #ffffff;
}

.headline {
  // width: 100%;
  padding: 0rem .24rem;
  height: .6rem;
  line-height: .6rem;
  margin-top: .12rem;
  display: flex;
  justify-content: space-between;

  .headlineLable {
    width: 18%;
    background-color: #F7F8FA;
    text-align: center;

    // margin-left: .24rem;
    // margin-right: .2rem;
    &.active {
      color: #3562DB;
      background-color: #E6EFFC;
    }
  }
}

.examinationTitle {
  // width: 100%;
  height: 73%;
  overflow-y: auto;
}

.examinationData {
  // width: 100%;
  height: 5.6rem;
  background-color: #ffffff;
  border-radius: .16rem;
  padding: 0rem .3rem;
  margin: .2rem 0rem;
}

.examinationImg {
  width: 20%;
  // text-align: center;
  height: .8rem;
  margin-top: .2rem;
}

.examinationImg img {
  width: .8rem;
  height: .8rem;
  overflow: hidden;
}

.examination_bt {
  display: flex;
  height: 1rem;
  width: 100%;
  margin-bottom: .2rem;
}

.examination_mc {
  width: 80%;
  // overflow: hidden;
  // text-overflow: ellipsis;
  // white-space: nowrap;

}

.examination_mcItem {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: .26rem;
  color: #86909C;
}

.examination_mcLable {
  font-size: .32rem;
  color: #1D2129;
  margin: .18rem 0rem;
}

.conterlable {
  font-size: .28rem;
  color: #86909C;
  margin-top: .28rem;
  width: 25%;
}

.conterItem {
  width: 75%;
  font-size: .28rem;
  color: #1D2129;
  margin-top: .28rem;
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号表示被隐藏部分 */
}

.itemLable {
  width: 100%;
  display: flex;
  align-items: center;
}

.examinBtn {
  display: flex;
  // justify-content: space-between;
  height: .8rem;
  width: 100%;
  line-height: .8rem;
  margin-top: .04rem;
  position: relative;
}

.examinLine {
  width: 100%;
  border: .01rem solid #F2F3F5;
}

.notPass {
  margin-top: .2rem;
  width: 1.5rem;
  height: .54rem;
  background-color: #FFECE8;
  border-radius: .04rem;
  line-height: .54rem;
  text-align: center;
  color: #F53F3F;
  font-size: .28rem;
}

.pass {
  margin-top: .2rem;
  width: 1.5rem;
  height: .54rem;
  background-color: #E8FFEA;
  border-radius: .04rem;
  line-height: .54rem;
  text-align: center;
  color: #00B42A;
  font-size: .28rem;
}

.passItem {
  position: absolute;
  right: 0;
}
.notList {
  position: relative;
  height: 100%;

  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url('../../../assets/images/noData.png') 0 40% no-repeat;
    background-size: 100% auto;

    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
