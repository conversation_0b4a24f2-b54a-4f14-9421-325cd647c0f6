<template>
  <div class="box">
    <Header title="在线考试" @backFun="goBack()"></Header>
    <div class="examinTime">
      <div class="examinTimeDown">
        <div>剩余时间：</div>
        <div><van-count-down :time="examinDuration * 60 * 1000" format="HH:mm:ss" @finish="onCountDownFinish" /></div>
      </div>
    </div>
    <div class="questions">
      <div v-for="(item, index) in questionsList" :key="index" v-if="questionsIndex === index">
        <div class="validity_select" v-if="item.type == 1">单选题</div>
        <div class="validity_select" v-if="item.type == 2">解题多选</div>
        <div class="validity_select" v-if="item.type == 3">判断题</div>
        <div class="validity_title">{{ item.topic }} <span>({{ item.score || '0' }}分)</span> </div>
        <!-- 单选 -->
        <div class="validity_radio">
          <van-radio-group v-model="item.userAnswer" v-if="item.type == 1">
            <van-radio class="selectRadio" style="margin: .3rem 0rem;" v-for="(k, index) in item.options" :key="index"
              :name="k.id">
              <div class="selectRadiosy">{{ k.id }}. {{ k.label }}</div>
            </van-radio>
          </van-radio-group>
        </div>
        <!-- 多选 -->
        <div class="validity_check" v-if="item.type == 2">
          <van-checkbox-group v-model="item.userAnswer">
            <van-checkbox class="selectRadio" shape="square" style="margin: .3rem 0rem;"
              v-for="(k, index) in item.options" :key="index" :name="k.id">
              <div class="selectRadiosy">{{ k.id }}. {{ k.label }}</div>
            </van-checkbox>
          </van-checkbox-group>
        </div>
        <!-- 判断 -->
        <div class="validity_radio">
          <van-radio-group v-model="item.userAnswer" v-if="item.type == 3">
            <van-radio class="selectRadio" style="margin: .3rem 0rem;" v-for="(k, index) in item.options" :key="index"
              :name="k.id">
              <div class="selectRadiosy"> {{ k.id == 'A' ? '正确' : '错误' }}</div>
            </van-radio>
          </van-radio-group>
        </div>
      </div>
      <div v-if="datoi">
        <div class="questionSty" @click="showPopup">
          <div><img src="../../../assets/images/icon-wrapper.png" alt=""></div>
          <div><span style="margin-left: .1rem; font-size: .3rem; color:#3562DB ;">{{ questionsIndex + 1 }}</span> /
            <span style=" font-size: .3rem; color:#4E5969 ;">{{ this.questionsList.length }}</span>
          </div>
        </div>

      </div>
      <div class="questionBtn">
        <van-button
          style="width: 2rem; height: .9rem; margin: 0rem .3rem; background-color: #E6EFFC; border: #E6EFFC; color: #3562DB; font-size: .3rem;"
          type="primary" @click="upSelect">上一题</van-button>
        <van-button
          style="width: 2rem; height: .9rem;  margin: 0rem .3rem; background-color: #3562DB; font-size: .3rem;"
          type="info" @click="nextSelect">下一题</van-button>
        <div><van-button
            style="width: 2rem; height: .9rem;  margin: 0rem .3rem; background-color: #3562DB; font-size: .3rem;"
            type="info" @click="goSubmit">提交</van-button></div>
      </div>
      <van-popup v-model="show" position="bottom" :style="{ height: '60%' }">
        <div class="content_right">
          <p>
            全部题目<span>（{{ questionsIndex + 1 }}/{{ this.questionsList.length }}）</span>
          </p>
          <div class="answerNum">
            <div v-for="(item, index) in questionsList" :key="index" :class="[
              'item',
              questionsIndex == index ? 'isActive' : '', isfinished(item) ? 'isfinished' : '',]" @click="goOnQuestions(index)">
              {{ index + 1 }}
              <img v-if="isfinished(item)" src="../../../assets/images/考试图片.png" alt="" />
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { Dialog } from 'vant';
import moment from 'moment';
export default {
  data() {
    return {
      datoi: true,
      loginData: '', // 登陆人用户信息
      examitId: '',
      countDown: '',
      questionsList: [],
      questionsIndex: 0,
      examinDuration: 0, // 考试总时长
      residueTime: '', // 考试用时时长
      timer: null,
      show: false,
      type: '', // 培训跳转状态
      noLength: 0, // 未作试题长度
    }
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
      this.loginData = JSON.parse(localStorage.getItem("loginData"));
    this.examitId = this.$route.query.examinationId;
    this.type = this.$route.query.type;
    this.startTimer();
  },
  beforeDestroy() {
    this.clearTimer();
  },
  mounted() {
    if (this.type == 'train') {
      this.getExaminPlanDetail()
    } else {
      this.getRecordInfo(); // 详情
    }
    wx.onHistoryBack(() => {
      this.$router.push('/homePage')
      return false
    });
  },
  computed: {
  },
  methods: {
    // 监听返回
    goBack() {
      this.$router.go(-1);
    },
    formatTimestamp(timestamp) {
      return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
    },
    startTimer() {
      this.timer = setInterval(() => {
        this.residueTime--;
        let hour = Math.floor(this.residueTime / 60 / 60 % 24);	// 时
        let minutes = Math.floor(this.residueTime / 60);
        let seconds = Math.floor(this.residueTime % 60);
        minutes = minutes < 10 ? "0" + minutes : minutes;
        seconds = seconds < 10 ? "0" + seconds : seconds;
        this.countDown = hour + ":" + minutes + ":" + seconds;
      }, 1000);
    },
    showPopup() {
      this.show = true;
    },
    // 是否完成
    isfinished(item) {
      console.log(item, 'item');
      return item.type != '2' ? Boolean(item.userAnswer) : Boolean(item.userAnswer.length)
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    // 倒计时结束
    onCountDownFinish() {
      if (this.examinDuration != 0) {
        this.noLength = 0
        let answer = []
        let correct = ''
        this.questionsList.forEach((el) => {
          if (!this.isfinished(el)) {
            this.noLength += 1
          }
          if (el.type == 2) {
            el.userAnswer = el.userAnswer.join(',')
          }
          if (el.actualAnswer == el.answer) {
            correct = 0
          } else {
            correct = 1
          }
          let question = {
            planQuestionId: el.id,
            actualAnswer: el.userAnswer,
            recordId: this.examinInfo.id,
            correct: correct,
          }
          answer.push(question)
        })
        let params = {
          id: this.examitId,
          recordId: 0,
          answer: answer,
          actualDuration: parseInt(this.examinDuration) - parseInt(this.residueTime),
        }
        if (this.noLength) {
          this.$toast(`倒计时结束，自动提交考试,您还有 ${this.noLength} 道题没有答题`);
        }
        this.$api.submitExam(params).then((res) => {
          if (res.code == 200) {
            this.$toast.success(res.msg)
            this.$router.go(-1)
          }
        })

      }
    },
    // 筛选分类
    openSwitch(e) {
      this.switchIndex = e
    },
    // 搜索筛选
    onSearch(val) {
      // this.$toast(val)
      this.getCourseList()
    },
    //搜索筛选
    handleClear() {
      this.finList.courseName = ''
      this.getCourseList()
    },
    // 试题详情
    getRecordInfo() {
      this.$api.examitDetail({ id: this.examitId }).then((res) => {
        this.examinInfo = res
        this.examinDuration = res.duration,
          console.log(this.examinDuration, 'this.examinDuration');
        this.residueTime = this.examinDuration;
        console.log(this.examinDuration, 'this.examinDuration');
        res.questions.forEach((el) => {
          el.options = JSON.parse(el.options)
          if (el.type == 2) {
            el.userAnswer = el.userAnswer || []
          } else {
            el.userAnswer = el.userAnswer || ''
          }
        })
        this.questionsList = res.questions
      })
    },
    // 获取考试计划下的试题
    getExaminPlanDetail() {
      this.$api.examitPlanDetail({ id: this.examitId }).then((res) => {
        this.examinInfo = res
        this.examinDuration = res.duration,
          console.log(this.examinDuration, 'this.examinDuration');
        res.questions.forEach((el) => {
          el.options = JSON.parse(el.options)
          if (el.type == 2) {
            el.userAnswer = el.userAnswer || []
          } else {
            el.userAnswer = el.userAnswer || ''
          }
        })
        this.questionsList = res.questions
      })
    },
    // 上一题
    upSelect() {
      if (this.questionsIndex === 0) {
        this.questionsIndex = 0
      } else {
        this.questionsIndex -= 1
      }
    },
    // 下一题
    nextSelect() {
      this.preDisabled = false;
      if (this.questionsIndex < this.questionsList.length - 1) {
        this.questionsIndex += 1
      }
    },
    goOnQuestions(index) {
      this.questionsIndex = index
      this.itemInfo = this.questionsList[index]
    },
    // 提交
    goSubmit() {
      this.noLength = 0
      let answer = []
      let correct = ''
      this.questionsList.forEach((el) => {
        if (!this.isfinished(el)) {
          this.noLength += 1
        }
        if (el.type == 2) {
          el.userAnswer = el.userAnswer.join(',')
        }
        if (el.actualAnswer == el.answer) {
          correct = 0
        } else {
          correct = 1
        }
        let question = {
          planQuestionId: el.id,
          actualAnswer: el.userAnswer,
          recordId: this.examinInfo.id,
          correct: correct,
        }
        answer.push(question)
      })
      let params = {
        id: this.examitId,
        recordId: 0,
        answer: answer,
        actualDuration: parseInt(this.examinDuration) - parseInt(this.residueTime),
      }
      console.log(this.noLength);
      if (this.noLength) {
        Dialog.confirm({
          message: `还有 ${this.noLength} 道题目没有作答,确认要进行提交吗？提交后将按错题处理`,
        }).then(() => {
          this.$api.submitExam(params).then((res) => {
            this.$toast.success('成功')
            this.$router.go(-1)
          })
        })
      } else {
        this.$api.submitExam(params).then((res) => {
          this.$toast.success('成功')
          this.$router.go(-1)
        })
      }
    },
  },

}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  background: #F2F4F9;
}

.questions {
  // width: 100%;
  height: 100%;
  // margin-top: 15px;
  background: #fff;
  padding: 10px 15px 0px 15px;
}

.validity_select {
  width: 1.28rem;
  height: .54rem;
  background: #F2F3F5;
  color: #4E5969;
  font-size: .28rem;
  text-align: center;
  line-height: .54rem;
  margin-bottom: .3rem;
}

.questionBtn {
  width: 100%;
  display: flex;
  position: fixed;
  // top: .2rem;
  bottom: .6rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.validity_title {
  color: #1D2129;
  font-size: 17px;
  font-weight: bolder;
  line-height: 25px;
  margin-bottom: 20px;
  height: 80px;
  overflow: auto;
}

.answer {
  width: 100%;
  height: 1.6rem;
  border: .02rem solid #000;
}

.answerLable {
  width: 100%;
  margin: .2rem 0rem;
  display: flex;
}

.selectRadio {
  width: 100%;
  // height: 20px;
}

.selectRadiosy {
  // width: 100%;
  height: .8rem;
  overflow-y: auto;
}

/deep/ .van-radio__label {
  line-height: .8rem;
}

/deep/ .van-checkbox__label {
  line-height: .8rem;
}

.examinTime {
  // width: 100%;
  height: 40px;
  margin: 2px 0px;
  background-color: #fff;
  // line-height: 40px;
  padding-right: 5px;
}

.examinTimeDown {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;

}

.questionSty {
  width: 2.12rem;
  height: .76rem;
  border-radius: 19.98rem;
  border: .02rem solid #C9CDD4;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  right: .4rem;
  bottom: 15%;
  z-index: 9999;
}

.content_right {
  width: 100%;
  height: 100%;
  padding: 20px;

  p {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    margin-bottom: 24px;

    span {
      font-size: 16px;
      font-weight: normal;
      color: #7f848c;
    }
  }

  .answerNum {

    width: 100%;
    height: calc(100% - 60px);
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start; // 替代原先的space-between布局方式

    .item {
      width: 52px;
      height: 52px;
      border: 1px solid #E4E7ED;
      background-color: #fff;
      line-height: 52px;
      text-align: center;
      margin: 0 16px 16px 0; // 间隙为5px
      font-size: 16px;
      cursor: pointer;

      &:nth-child(5n) {
        // 去除第3n个的margin-right
        margin-right: 0;
      }
    }

    .isActive {
      background: #e6effc;
      border: 1px solid #3562db;
    }

    .isfinished {
      position: relative;

      img {
        width: 20px;
        height: 16px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
}

// /deep/ .van-radio__icon .van-icon {
//   margin-top: -0.2rem;
// }

// /deep/ .van-checkbox__icon .van-icon {
//   margin-top: -10px;
// }</style>
