<template>
  <div class="box">
    <Header  title="考试详情" @backFun="goBack()"></Header>
    <div class="examinType">
      <div class="switbox" v-for="(x, y) in switchTab" :key="y" :class="{ active: switchIndex == y }"
        @click="openSwitch(y)">{{ x.name }} <div class="linesy" v-if="switchIndex == y"></div>
      </div>
    </div>
    <div class="examinBasics" v-if="switchIndex == 0">
      <div class="examinNav">
        <div class="examinBox">
          <div class="examinLable">联系电话：</div>
          <div class="examinItem">{{ examinInfo.mobilePhone || '-' }}</div>
        </div>
        <div class="examinBox">
          <div class="examinLable">姓名：</div>
          <div class="examinItem">{{ examinInfo.respondentName }}</div>
        </div>
        <div class="examinBox">
          <div class="examinLable">试卷名称：</div>
          <div class="examinItem">{{ examinInfo.name }}</div>
        </div>
        <div class="examinBox">
          <div class="examinLable">所属科目：</div>
          <div class="examinItem">{{ examinInfo.subjectName }}</div>
        </div>
        <div class="examinBox">
          <div class="examinLable">考试得分：</div>
          <div class="examinItem">{{ examinInfo.actualScore || '0' }} 分</div>
        </div>
        <div class="examinBox">
          <div class="examinLable">考试总分：</div>
          <div class="examinItem">{{ examinInfo.score }} 分</div>
        </div>
        <div class="examinBox">
          <div class="examinLable">答题用时：</div>
          <div class="examinItem">{{ examinInfo.actualDuration }} 分钟</div>
        </div>
        <div class="examinBox">
          <div class="examinLable">提交时间：</div>
          <div class="examinItem">{{ examinInfo.submissionTime || '-' }}</div>
        </div>
      </div>
    </div>
    <div class="questions" v-if="switchIndex == 1">
      <div v-if="examinInfo.testedStatus == 2">
        <div v-for="(item, index) in questionsList" :key="index" v-if="questionsIndex === index">
          <div class="validity_select" v-if="item.type == 1">单选题</div>
          <div class="validity_select" v-if="item.type == 2">解题多选</div>
          <div class="validity_select" v-if="item.type == 3">判断题</div>
          <div class="validity_title">{{ item.topic }} <span>({{ item.score || '0' }}分)</span> </div>
          <!-- 单选 -->
          <div class="validity_radio">
            <van-radio-group v-model="item.actualAnswer" v-if="item.type == 1">
              <van-radio class="selectRadio" style="margin: .3rem 0rem;" v-for="(k, index) in item.options" :key="index"
                :name="k.id" :disabled="item.actualAnswer != '' || item.actualAnswer != null">
                <div class="selectRadiosy">{{ k.id }}. {{ k.label }}</div>
              </van-radio>
            </van-radio-group>
          </div>
          <!-- 多选 -->
          <div class="validity_check" v-if="item.type == 2">
            <van-checkbox-group v-model="item.actualAnswer">
              <van-checkbox class="selectRadio" shape="square" style="margin: .3rem 0rem;"
                v-for="(k, index) in item.options" :key="index" :name="k.id" :disabled="item.actualAnswer != '' || item.actualAnswer != null">
                <div class="selectRadiosy">{{ k.id }}. {{ k.label }}</div>
              </van-checkbox>
            </van-checkbox-group>
          </div>
          <!-- 判断 -->
          <div class="validity_radio">
            <van-radio-group v-model="item.actualAnswer" v-if="item.type == 3">
              <van-radio style="margin: .3rem 0rem;" :name="'1'" :disabled="item.actualAnswer != '' || item.actualAnswer != null">正确</van-radio>
              <van-radio style="margin: .3rem 0rem;" :name="'2'" :disabled="item.actualAnswer != '' || item.actualAnswer != null">错误</van-radio>
            </van-radio-group>
          </div>
          <div class="answer">
            <div class="answerLable" v-if="item.actualAnswer == '1'">答案：<div>{{ item.answer == 1 ? '正确' : '错误' }}</div>
            </div>
            <div v-if="item.analysis != ''">解析：<div class="analysisItem">{{ item.analysis }}</div>
            </div>
          </div>
        </div>
        <div class="questionBtn">
          <van-button
            style="width: 2rem; height: .9rem; margin: 0rem .3rem; background-color: #E6EFFC; border: #E6EFFC; color: #3562DB; font-size: .3rem;"
            type="primary" @click="upSelect">上一题</van-button>
          <van-button
            style="width: 2rem; height: .9rem;  margin: 0rem .3rem; background-color: #3562DB; font-size: .3rem;"
            type="info" @click="nextSelect">下一题</van-button>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import axios from "axios";
import moment from 'moment';
export default {
  data() {
    return {
      examitId: '',
      switchTab: [
        {
          name: '基础信息',
          id: '0',
        },
        {
          name: '答题详情',
          id: '1',
        }
      ],
      examinInfo: '',
      switchIndex: 0,
      questionsList: [],
      questionsIndex: 0,
    }
  },
  created() {
    // this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    // this.loginData = JSON.parse(localStorage.getItem("loginData"));
    this.examitId = this.$route.query.examinationId
  },

  mounted() {
    this.getRecordInfo() // 详情
    wx.onHistoryBack(() => {
      this.$router.push('/homePage')
      return false
    });
  },
  methods: {
    // 监听返回
    goBack() {
      this.$router.go(-1);
    },
    formatTimestamp(timestamp) {
      return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
    },
    // 筛选分类
    openSwitch(e) {
      this.switchIndex = e
      console.log(this.switchIndex, 'this.switchIndex');
      console.log(this.examinInfo, 'examinInfo');
      if (this.switchIndex == 1 && this.examinInfo.testedStatus != 2) {
        this.$toast('您暂未考试，请先进行考试');
        return false
      }
    },
    // 搜索筛选
    onSearch(val) {
      // this.$toast(val)
      this.getCourseList()
    },
    //搜索筛选
    handleClear() {
      this.finList.courseName = ''
      this.getCourseList()
    },
    // 试题详情
    getRecordInfo() {
      this.$api.examitDetail({ id: this.examitId }).then((res) => {
        this.examinInfo = res
        res.questions.forEach((el) => {
          el.options = JSON.parse(el.options)
          if (el.type == 2) {
            el.answer = el.answer || []
          } else {
            el.answer = el.answer || ''
          }
        })
        this.questionsList = res.questions
      })
    },
    // 上一题
    upSelect() {
      if (this.questionsIndex === 0) {
        this.questionsIndex = 0
      } else {
        this.questionsIndex -= 1
      }
    },
    // 下一题
    nextSelect() {
      this.preDisabled = false;
      if (this.questionsIndex < this.questionsList.length - 1) {
        this.questionsIndex += 1
      }
    },
  },

}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  background: #F2F4F9;
}

.examinType {
  width: 100%;
  height: 1rem;
  background-color: #fff;
  display: flex;
  line-height: 1rem;
}

.switbox {
  width: 100%;
  padding: 0rem .4rem;
  font-size: .32rem;
  color: #86909C;
  font-weight: bold;
  text-align: center;

  &.active {
    color: #1D2129;

  }
}

.linesy {
  width: .48rem;
  height: .06rem;
  border-radius: .04rem;
  background-color: #3562DB;
  margin-left: 1.2rem;
  margin-top: -0.2rem;
}

.examinBasics {
  // width: 100%;
  height: 80%;
  margin-top: .3rem;
  background: #fff;
}

.examinBox {
  display: flex;
  margin: .3rem;
}

.examinNav {
  // width: 100%;
  // height: 100%;
  padding: .3rem .3rem;
}

.examinLable {
  width: 25%;
  text-align: left;
  color: #86909C;
  font-size: .3rem;
}

.examinItem {
  width: 75%;
  text-align: left;
  color: #1D2129;
  font-size: .3rem;
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号表示被隐藏部分 */
}

.questions {
  // width: 100%;
  height: 78%;
  margin-top: .3rem;
  background: #fff;
  padding: .2rem .3rem 0rem .3rem;
}

.validity_select {
  width: 1.28rem;
  height: .54rem;
  background: #F2F3F5;
  color: #4E5969;
  font-size: .28rem;
  text-align: center;
  line-height: .54rem;
  margin-bottom: .3rem;
}

.questionBtn {
  width: 100%;
  position: fixed;
  // top: 10px;
  bottom: 30px;
  text-align: center;
}

.validity_title {
  color: #1D2129;
  font-size: .34rem;
  font-weight: bolder;
  line-height: .5rem;
  margin-bottom: .4rem;
  height: 1.6rem;
  overflow: auto;
}

.answer {
  width: 100%;
  height: 1.6rem;
}

.answerLable {
  width: 100%;
  margin: .2rem 0rem;
  display: flex;
}

.selectRadio {
  width: 100%;
  // height: .4rem;
}

.selectRadiosy {
  width: 100%;
  height: .4rem;
  overflow-y: auto;
}

.analysisItem {
  width: 100%;
  height: 1rem;
  margin-top: .1rem;
  overflow-y: auto;
}
</style>
