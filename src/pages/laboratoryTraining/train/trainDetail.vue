<template>
  <div class="inner">
    <Header title="培训详情" @backFun="goBack()"></Header>
    <div>
      <div class="trainDetailTitle">
        <div class="detailbox">
          <div><img src="../../../assets/images/icon-头像@2x.png" alt=""></div>
          <div class="detailbox_lable">{{ trainDeatil.name }}</div>
        </div>
        <div class="detailbox">
          <div class="detail_lable">所属科目：</div>
          <div class="detailItem">{{ trainDeatil.subjectName }}</div>
        </div>
        <div class="detailbox">
          <div class="detail_lable">培训内容：</div>
          <div class="detailItem">{{ trainDeatil.name }}</div>
        </div>
        <div class="detailbox">
          <div class="detail_lable">课时数：</div>
          <div class="detailItem">{{ trainDeatil.coursePeriod }}课时</div>
        </div>
        <div class="detailbox">
          <div class="detail_lable">培训时间：</div>
          <div class="detailItem">{{ trainDeatil.startTime }} - {{ trainDeatil.endTime }}</div>
        </div>
        <div class="detailbox">
          <div class="detail_lable">培训地址：</div>
          <div class="detailItem" style="line-height: 20px;">{{ trainDeatil.address }}</div>
        </div>
        <div class="detailbox"
          style="display: flex; justify-content: space-between;color: #86909C; font-size: 15px; margin-top: 15px;">
          <div style="width: 75%; display: flex; ">
            <div class="trainOption"><van-icon name="manager" color="#86909C" /> 培训老师：</div>
            <div class="trainTeach">{{ trainDeatil.teacherName }}</div>
          </div>
          <div style="width: 25%;">{{ trainDeatil.signNum || '0' }}人已签到</div>
        </div>
      </div>
      <div class="detail_conter">
        <div class="detail_title">培训课件<span>({{ tableList.length || '0' }})</span></div>
        <div class="courseDetailist" v-for="(item, index) in tableList" :key="index">
          <div class="detaiList">
            <div>
              <div class="courseImg"><img src="../../../assets/images/doc.png" alt=""></div>
            </div>
            <div>
              <div class="courlable">{{ index + 1 }}.{{ item.originalFilename }}</div>
            </div>
            <div class="download">
              <div class="download_lable" @click="openVideo(item)"><span>查看</span></div>
              <!-- <div class="download_lable"><span>下载</span></div> -->
            </div>
          </div>
        </div>
        <div class="examDetail" v-if="examinDataInfo">
          <div v-for=" item in examinDataInfo">
            <div class="examDetaiName">
              <div><img src="../../../assets/images/KS.png" alt=""></div>
              <div class="examDetaiLable">{{ item.name }}</div>
            </div>
            <div class="examDetailMak">{{ item.disc }}</div>
            <div class="examDetailNav">
              <div>考试时长：</div>
              <div>{{ item.duration || '0' }} 分钟</div>
            </div>
            <div class="examDetailNav">
              <div>考题数量：</div>
              <div>{{ item.questionNum || '0' }} 题</div>
            </div>
            <div class="examDetailNav">
              <div>考试总分：</div>
              <div>{{ item.score || '0' }} 分</div>
            </div>
            <div class="examDetailNav">
              <div>所属单位：</div>
              <div>{{ item.deptName || '-' }}</div>
            </div>
            <div style="text-align: right; margin-top: -5px;" v-if="examinData[0].respond != 0">
              <van-button
                style="width: 88px; height: 32px; background-color:#3562DB; border-radius: 2px; margin-bottom: 10px; margin-top: 5px;"
                type="info" @click.stop="openSigin()">立即考试</van-button>
            </div>
          </div>
        </div>
        <div class="detail_footer" v-if="trainDeatil.taskStatus != 5">
          <van-button v-if="trainDeatil.type == 0" type="info"
            style="width: 100%; height: 100%; background-color:#3562DB;" @click="getSgin">培训签到</van-button>
          <van-button v-if="trainDeatil.type == 1" type="info"
            style="width: 100%; height: 100%; background-color:#3562DB;" @click="openQrcode"><van-icon name="scan"
              style="margin-right: .2rem;" />扫码签到</van-button>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
import YBS from "@/assets/utils/utils.js";
export default {
  components: {},
  data() {
    return {
      loginData: '',
      trainDeatil: {},
      tableList: [], // 培训课件
      examinData: [], // 培训关联试卷信息
      trainId: '',
      dataCode: '', // 扫码获取字符码
      courseSum: '',
      examinDataInfo: [],
      examinId: '',
    };
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
      this.loginData = JSON.parse(localStorage.getItem("loginData"));
    console.log(this.loginData, 'this.loginData');
    this.trainId = this.$route.query.id;
    this.getConfigParams();
  },
  mounted() {
    this.getTrainDetail();
    this.getPermission()
    // this.$utils.backBtn(this.goBack)
  },
  methods: {
    // 返回按钮
    goBack() {
      this.$router.go(-1)
    },
    openVideo(item) {
      console.log(item);
      this.$router.push({
        path: '/seeFile',
        query: {
          tradinUrl: item.viewAddress
        }
      })
    },
    // 获取扫码相关权限
    getPermission() {
      if (!this.YBS.hasPermission("storage")) {
        this.YBS.reqPermission(["storage"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            if (!this.YBS.hasPermission("camera")) {
              this.YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                  const btn = document.getElementById('btn')
                  btn.click()
                } else {
                  this.$toast('请打开相机权限否则将无法拍照')
                }
              });
              return;
            }
          }
        });
        return;
      } else {
        if (!this.YBS.hasPermission("camera")) {
          this.YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            } else {
              this.$toast('请打开相机权限否则将无法拍照')
            }
          });
          return;
        }
      }
    },
    // 线下扫码签到
    // openQrcode() {
    // parent.wx.scanQRCode({
    //   needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
    //   scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
    //   success: (res) => {
    //     //扫码返回的结果
    //     this.dataCode = res.resultStr
    //     this.$router.push({
    //       path: '/signIndex',
    //       query: {
    //         id: this.dataCode
    //       }
    //     })
    //     // this.getSgin()
    //   },
    //   cancel: () => {
    //     this.$dialog.alert({
    //       message: res
    //     });
    //   },
    // });
    // },
    // 扫码逻辑
    openQrcode() {
      var FNScanner = api.require('FNScanner')
      return new Promise((resolve, reject) => {
        FNScanner.openScanner({
          autorotation: false,
          isAlbum: true
        }, (ret, err) => {
          if (ret.eventType == 'success') {
            console.log('ret.content', ret.content);
            this.dataCode = ret.content
            this.$router.push({
              path: '/signIndex',
              query: {
                id: this.dataCode
              }
            })
          } else if (ret.eventType == 'cameraError') {
            this.loding = false
            this.$toast.fail({
              message: '请开启访问摄像头权限',
              duration: 2500
            })
            setTimeout(() => {
              FNScanner.closeView()
            }, 2500)
          } else if (ret.eventType == 'fail') {
            this.loding = false
            this.$toast.fail({
              message: '扫码失败,请重新扫码',
              duration: 2500
            })
            setTimeout(() => {
              FNScanner.closeView()
            }, 2500)
          } else if (ret.eventType == 'cancel') {
            FNScanner.closeView()
          }
        })
      })
    },
    // 获取签名
    getConfigParams() {
      // 进行签名的时候  Android 不用使用之前的链接， ios 需要
      // let signLink = /(Android)/i.test(navigator.userAgent) ? location.href.split("#")[0] : window.entryUrl;
      // let signLink = location.href.split("#")[0];
      // let params = {
      //   url: signLink,
      //   appKey: __PATH.APPKEY
      // }
      // this.$api.getSignature(params).then(res => {
      //   let obj = {
      //     appId: res.corpId,
      //     nonceStr: res.nonceStr,
      //     signature: res.signature,
      //     timestamp: res.timestamp
      //   }
      //   this.setConfig(obj)
      // });
    },
    // 获取详情
    getTrainDetail() {
      this.$api.trainPlanDetail({ id: this.trainId }).then(res => {
        this.trainDeatil = res;
        this.tableList = res.files;
        this.examinDataInfo = res.examInfos;
        console.log(this.examinDataInfo, 'this.examinDataInfo');
        if (res.examInfos[0].examRecord) {
          this.examinData = res.examInfos[0].examRecord;
          this.examinId = this.examinData[0].id
        }
        console.log(this.examinData, 'this.examinData');
        this.courseSum = res.files.length
      })
    },
    // 点击培训立即考试
    openSigin() {
      console.log(this.examinData, 'this.examinData');
      if (this.trainDeatil.taskStatus != 5) {
        this.$toast('请先签到，签到后支持考试')
      } else {
        this.$router.push({
          path: '/questionsList',
          query: {
            examinationId: this.examinId,
          }
        })
      }
    },
    // 扫码签到
    getSgin() {
      let parmas = {
        taskId: this.trainId
      }
      this.$api.trainSgin(parmas).then(res => {
        this.$toast.success('签到成功')
        this.$router.go(-1)
      }).catch(err => {
        // console.log(err.data.msg,'err');
        this.$toast(err.data.msg)
      })
    },
    // setConfig(data) {
    //   wx.config({
    //     debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    //     appId: data.appId, // 必填，公众号的唯一标识
    //     timestamp: data.timestamp, // 必填，生成签名的时间戳
    //     nonceStr: data.nonceStr, // 必填，生成签名的随机串
    //     signature: data.signature, // 必填，签名,
    //     jsApiList: [
    //       "scanQRCode",
    //       "startRecord",
    //       "stopRecord",
    //       "onVoiceRecordEnd",
    //       "playVoice",
    //       "downloadVoice",
    //       "stopVoice",
    //       "onVoicePlayEnd",
    //       "uploadVoice",
    //       "chooseImage",
    //       "uploadImage",
    //       "downloadImage"
    //     ] // 必填，需要使用的JS接口列表
    //   });
    //   this.configWeiXinVoice();

    // },
    // configWeiXinVoice() {
    //   wx.ready(() => {
    //     wx.checkJsApi({
    //       jsApiList: [
    //         "scanQRCode",
    //         "startRecord",
    //         "downloadVoice",
    //         "stopRecord",
    //         "onVoiceRecordEnd",
    //         "playVoice",
    //         "stopVoice",
    //         "onVoicePlayEnd",
    //         "uploadVoice",
    //         "chooseImage",
    //         "uploadImage",
    //         "downloadImage"
    //       ],
    //       success: res => {
    //         wx.stopRecord({});
    //         if (!localStorage.WeiXinconf) {
    //           wx.startRecord({
    //             success: function () {
    //               localStorage.WeiXinconf = true
    //               setTimeout(() => {
    //                 wx.stopRecord({});
    //               }, 500)
    //             },
    //             fail: function () {
    //               console.log("失败回调");
    //             }
    //           });
    //         }
    //       }
    //     });

    //   });
    //   wx.error(res => {
    //     console.log(
    //       "你的微信版本太低，不支持微信JS接口，请升级到最新的微信版本！"
    //     );
    //   });
    // }
  }
};
</script>
<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
}

.examDetailNav {
  display: flex;
  margin: .2rem 0rem;
  font-size: .28rem;
  color: #86909C;
}

.examDetailMak {
  width: 100%;
  margin: .2rem 0rem;
  font-size: .28rem;
  color: #86909C;
}

.examDetaiName {
  display: flex;

}

.examDetaiName img {
  width: .4rem;
  height: .4rem;
  overflow: hidden;
  margin-right: .1rem;
}

.examDetaiLable {
  color: #1D2129;
  font-size: .3rem;
  margin-top: .06rem;
}

.examDetail {
  padding: 0rem .2rem;
}

.inner {
  width: 100%;
  height: 100%;
  background: #fff;
}

.trainDetailTitle {
  // width: 100%;
  height: 4.6rem;
  background-color: #ffffff;
  padding: .2rem .3rem;
}

.detailbox {
  display: flex;
  margin-bottom: 10px;

}

.detailbox img {
  width: 24px;
  height: 24px;
}

.detailbox_lable {
  font-size: 16px;
  color: #1D2129;
  font-weight: bold;
  margin: 5px 0px 0px 8px;
}

.detail_lable {
  font-size: .3rem;
  color: #86909C;
  margin-top: .2rem;
  width: 26%;
}

.detailItem {
  font-size: 15px;
  color: #1D2129;
  margin-top: .2rem;
  width: 74%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail_conter {
  // width: 100%;
  height: 6.5rem;
  padding: 0rem .2rem;
  margin-top: .4rem;
  overflow-y: auto;

  .detaiList {
    // width: 100%;
    //height: 1.4rem;
    border-radius: .16rem;
    background-color: #F7F8FA;
    display: flex;
    justify-content: space-between;
    padding: .3rem;
    margin: .2rem 0rem;

  }
}

.detail_footer {
  width: 100%;
  height: 1rem;
  // position: fixed;
  // padding: 0rem .4rem;
  // bottom: .2rem;
  // left: 50%;
  // transform: translateX(-50%);
  background-color: #fff;
  border-radius: .04rem .04rem .04rem .04rem;
}

.detail_title {
  color: #1D2129;
  font-size: .32rem;
  font-weight: 500;
  font-weight: bold;
  margin: .4rem 0rem;
}

.courseImg {
  width: .64rem;
  height: .64rem;
  margin-right: .24rem;
  margin-top: .1rem;
}

.courseImg img {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.courlable {
  font-size: .32rem;
  width: 4rem;
  font-weight: 500;
  color: #1D2129;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: .26rem;
  // margin-left: -50%;
}

.courItem {
  color: #4E5969;
  margin-top: .2rem;
}

.download {
  display: flex;
  margin-top: .1rem;
  font-size: .24rem;
  color: #3562DB;
  align-items: flex-start;
}

.download_lable {
  width: .72rem;
  height: .48rem;
  background-color: #E6EFFC;
  text-align: center;
  line-height: .48rem;
  margin: 0rem .1rem;
}

.trainTeach {
  width: 100%;
  margin-left: -30%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.trainOption {
  width: 100%;
}
</style>
