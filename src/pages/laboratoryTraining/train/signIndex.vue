<template>
  <div class="inner">
    <Header  title="培训签到" @backFun="goBack()"></Header>
    <div class="signDetail">
      <div class="signDetail_top">
        <div style="display: flex; margin: .3rem 0rem;">
          <div class="signDetailImg"><img src="../../../assets/images/peixun.png" alt=""></div>
          <div class="signDetai_title">{{ siginData.name }}</div>
        </div>
        <div class="signDetail_top_detail">
          <div class="signDetailLable">培训老师：<span>{{ siginData.teacherName }}</span></div>
          <div class="signDetailLable">培训内容：<span>{{ siginData.tmpName || siginData.name }}</span></div>
        </div>
      </div>
      <div class="signDetail_bottom">
        <div class="signDetail_bottom_title">
          <div class="signDetail_bottom_lable">姓名：<span>{{ loginData.name }}</span></div>
          <div class="signDetail_bottom_lable">电话：<span>{{ loginData.phone }}</span></div>
          <div class="signDetail_bottom_lable">部门：<span>{{ loginData.officeName }}</span></div>
        </div>
      </div>
    </div>
    <div style="display: flex;justify-content: center;">
      <div class="signImg">
        <div class="qdImg" @click="openhoto"><img src="../../../assets/images/Frame <EMAIL>" alt=""></div>
        <!-- <img  :src="avatarUrl" alt=""> -->
      </div>

    </div>
    <div style="width: 100%; text-align: center; margin-top: .1rem; color: #4E5969;">请使用前置镜头和培训场景自拍</div>
  </div>
</template>
<script>
import axios from "axios";
export default {
  components: {},
  data() {
    return {
      loginData: '',
      tableList: [],
      siginData: {}, // 培训信息
      courseId: '',
      pic: '',
      noPic: true,
      serverId: '',
      avatarUrl: '',
    };
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    this.loginData = JSON.parse(localStorage.getItem("loginData"));
    // this.getConfigParams();
    this.courseId = this.$route.query.id
    // this.siginData = JSON.parse(this.$route.query.item)
    this.getTrainDetail()
    // setTimeout(() => {
    //   if (this.serverId) {
    //     this.submit()
    //   }
    // }, 3000)
  },
  mounted() {
    // this.$utils.backBtn(this.goBack)
  },
  methods: {
    // 返回按钮
    goBack() {
      this.$router.go(-1)
    },
    getTrainDetail() {
      this.$api.trainPlanDetail({ id: this.courseId }).then(res => {
        this.siginData = res;
      })
    },
    // 获取签名
    // getConfigParams() {
    //   let signLink = location.href.split("#")[0];
    //   let params = {
    //     url: signLink,
    //     appKey: __PATH.APPKEY
    //   }
    //   this.$api.getSignature(params).then(res => {
    //     let obj = {
    //       appId: res.corpId,
    //       nonceStr: res.nonceStr,
    //       signature: res.signature,
    //       timestamp: res.timestamp
    //     }
    //     this.setConfig(obj)
    //   });
    // },
    //拍照
    // openhoto() {
    //   let that = this;
    //   wx.checkJsApi({
    //     jsApiList: ['chooseImage', 'getLocalImgData'],
    //     success: function (res) {
    //       if (res.checkResult.chooseImage) {
    //         wx.chooseImage({
    //           count: 1,
    //           sizeType: ['compressed'],
    //           sourceType: ['album', 'camera'],
    //           success: function (req) {
    //             wx.getLocalImgData({
    //               localId: req.localIds[0].toString(),
    //               success: function (res) {
    //                 const localData = res.localData;
    //                 let imageBase64 = '';
    //                 if (localData.indexOf('data:image') == 0) {
    //                   //苹果的直接赋值，默认生成'data:image/jpeg;base64,'的头部拼接
    //                   imageBase64 = localData;
    //                 } else {
    //                   //此处是安卓中的唯一得坑！在拼接前需要对localData进行换行符的全局替换
    //                   //此时一个正常的base64图片路径就完美生成赋值到img的src中了
    //                   imageBase64 = 'data:image/jpeg;base64,' + localData.replace(/\n/g, '');
    //                 }
    //                 that.avatarUrl = imageBase64;
    //                 that.handleAvatar(that.dataURLtoBlob(imageBase64));
    //               }
    //             });
    //           },
    //           fail() {
    //             that.$toast.show({
    //               type: 'text',
    //               text: '选择头像失败！',
    //             });
    //           }
    //         });
    //       } else {
    //         that.$toast.show({
    //           type: 'text',
    //           text: '暂不支持修改头像！',
    //         });
    //       }
    //     },
    //     fail: function () {
    //       that.$toast.show({
    //         type: 'text',
    //         text: '暂不支持修改头像！',
    //       });
    //     },
    //   });
    // },
    openhoto(imageData) {
      let parmas = {
        taskId: this.courseId
      }
      this.$api.trainSgin(parmas).then(res => {
        this.$toast.success('签到成功')
        this.$router.push({
          path: '/trainDetail',
          query: {
            id: this.courseId,
          }
        })
      }).catch((err) => {
        this.$toast.fail(err.data.msg)
      })
    },
    dataURLtoBlob(dataurl) {
      let arr = dataurl.split(',');
      let mime = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },
    /**
* jssdk 参数配置
* @param res
*/
    setConfig(data) {
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: data.appId, // 必填，公众号的唯一标识
        timestamp: data.timestamp, // 必填，生成签名的时间戳
        nonceStr: data.nonceStr, // 必填，生成签名的随机串
        signature: data.signature, // 必填，签名,
        jsApiList: [
          "scanQRCode",
          "startRecord",
          "stopRecord",
          "onVoiceRecordEnd",
          "playVoice",
          "downloadVoice",
          "stopVoice",
          "onVoicePlayEnd",
          "uploadVoice",
          "chooseImage",
          "uploadImage",
          "downloadImage"
        ] // 必填，需要使用的JS接口列表
      });
      this.configWeiXinVoice();

    },
    /**
     * 微信js ready方法
     */
    configWeiXinVoice() {
      wx.ready(() => {
        wx.checkJsApi({
          jsApiList: [
            "scanQRCode",
            "startRecord",
            "downloadVoice",
            "stopRecord",
            "onVoiceRecordEnd",
            "playVoice",
            "stopVoice",
            "onVoicePlayEnd",
            "uploadVoice",
            "chooseImage",
            "uploadImage",
            "downloadImage"
          ],
          success: res => {
            wx.stopRecord({});
            if (!localStorage.WeiXinconf) {
              //消除第一次录音是弹出的录音授权弹窗对用户体验问题
              wx.startRecord({
                success: function () {
                  localStorage.WeiXinconf = true
                  setTimeout(() => {
                    wx.stopRecord({});
                  }, 500)
                },
                fail: function () {
                  console.log("失败回调");
                }
              });
            }
          }
        });

      });
      wx.error(res => {
        console.log(
          "你的微信版本太低，不支持微信JS接口，请升级到最新的微信版本！"
        );
      });
    }


  }
};
</script>
<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
}

.inner {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
}

.signDetail {
  // width: 100%;
  height: 6rem;
  padding: .2rem .2rem;
}

.signDetail_top {
  // width: 100%;
  height: 3rem;
  background-image: url('../../../assets/images/背景@2x.png');
  background-size: 100% 3rem;
  background-repeat: no-repeat;
  border-radius: .16rem .16rem 0rem 0rem;
  padding: .2rem;
}

.signDetail_top_detail {
  // width: 100%;
  // height: 1.36rem;
  background-color: #fff;
  z-index: 999999;
  border-radius: .08rem;
  padding: .2rem;

  .signDetailLable {
    // width: 100%;
    font-size: .28rem;
    color: #86909C;
    margin-bottom: .3rem;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
    /* 使用省略号表示被隐藏部分 */

    span {
      color: #1D2129;
    }
  }
}

.signDetailImg img {
  width: .36rem;
  height: .36rem;
  margin-right: .1rem;
}

.signDetai_title {
  font-size: .32rem;
  color: #1D2129;
}

.signDetail_bottom {
  width: 100%;
  height: 2rem;
  margin-top: .3rem;
  display: flex;
  justify-content: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  .signDetail_bottom_title {
    width: 3.3rem;
  }
}

.signDetail_bottom_lable {
  font-size: .3rem;
  color: #86909C;
  margin: .3rem .1rem;

  span {
    font-size: .32rem;
    color: #1D2129;
    font-weight: bold;
  }
}

.signImg {
  width: 5.48rem;
  height: 4.64rem;
  // border: 1px dashed #86909C;
  text-align: center;
  line-height: 4.64rem;

  .qdImg img {
    width: 5.1rem;
    height: 4.1rem;
  }
}
</style>
