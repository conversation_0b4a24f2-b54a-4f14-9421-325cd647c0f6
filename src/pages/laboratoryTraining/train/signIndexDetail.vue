<template>
  <div class="inner">
    <Header v-if="!$utils.isIos()" title="" @backFun="goBack()"></Header>
    <div class="faceBox">
      <div class="singinImg">
        <div  @click="takePhoto">拍照</div>
      </div>
      <div class="singinBtn">
        <div> 
          <van-button style="width: 165px; height: 44px; background-color: #E6EFFC; color: #3562DB; border: #E6EFFC; margin-right: 5px;" type="info">重置</van-button>
        </div>
        <div>
          <van-button style="width: 165px; height: 44px; background-color: #3562DB;" type="info" @click="submit">确定</van-button>
        </div>
      </div>
    </div>
    <!-- <div class="faceBox">
      <div class="facePhotoBox" v-if="noPic">
        <div class="facePhoto">
          <div class="photoBg">
            <div class="photoIcon" @click="takePhoto"></div>
          </div>
          <div class="photoPeople"></div>
        </div>
      </div>

      <div class="facePicture" v-else>
        <image style="width: 285px" :src="pic" :fit="contain" @click="takePhoto"></image>
      </div>
    </div> -->
  </div>
</template>
<script>

export default {
  components: {},
  data() {
    return {
      loginData: '',
      config: {
        appId: '',
        timestamp: '',
        nonceStr: '',
        signature: '',
      },
      pic: '',
      noPic: true,
      serverId: '',

    };
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    this.loginData = JSON.parse(localStorage.getItem("loginData"));
    this.getConfigParams();
    this.takePhoto()
  },
  mounted() {
    this.$utils.backBtn(this.goBack);
    this.getWxConfig();
  },
  methods: {
    // 返回按钮
    goBack() {
      this.$router.go(-1)
    },
    // 获取签名
    getConfigParams() {
      // console.log('缓存');
      // 进行签名的时候  Android 不用使用之前的链接， ios 需要
      // let signLink = /(Android)/i.test(navigator.userAgent) ? location.href.split("#")[0] : window.entryUrl;
      let signLink = location.href.split("#")[0];
      let params ={
        url:signLink,
        appKey:__PATH.APPKEY
      }
      this.$api.getSignature(params).then(res=>{
        let obj ={
          appId: res.corpId,
		      nonceStr: res.nonceStr,
		      signature: res.signature,
		      timestamp: res.timestamp
        }
        this.setConfig(obj)
      });
    },
    //扫码
    // takePhoto() {
    //   let that = this;
    //   wx.chooseImage({
    //     count: 1, // 默认9
    //     sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
    //     sourceType: ['camera'], // 可以指定来源是相册还是相机，默认二者都有'album', 'camera'
    //     defaultCameraMode: 'front', //表示进入拍照界面的默认模式，目前有normal与batch两种选择，normal表示普通单拍模式，batch表示连拍模式，不传该参数则为normal模式。（注：用户进入拍照界面仍然可自由切换两种模式）
    //     isSaveToAlbum: 0, //整型值，0表示拍照时不保存到系统相册，1表示自动保存，默认值是1
    //     success: function (res) {
    //       var androidId = res.localIds[res.localIds.length - 1];
    //       var iosId;
    //       var localId = res.localIds[res.localIds.length - 1];
    //       if (window.__wxjs_is_wkwebview) {
    //         //判断ios是不是用的 wkwebview 内核
    //         wx.getLocalImgData({
    //           //循环调用 getLocalImgData
    //           localId: localId,
    //           // 图片的localID
    //           success: function (res) {
    //             var localData = res.localData; // localData是图片的base64数据，可以用img标签显示
    //             localData = localData.replace('jgp', 'jpeg'); //iOS 系统里面得到的数据，类型为 image/jgp,因此需要替换一下
    //             iosId = localData;
    //             that.pic = iosId;
    //             that.noPic = false;
    //           },
    //           fail: function (res) {
    //             alert('res');
    //           },
    //         });
    //       } else {
    //         //如果不是用的wkwebview内核或者是用的安卓系统
    //         that.pic = androidId;
    //         that.noPic = false;
    //       }
    //       //上传图片到微信服务器
    //       wx.uploadImage({
    //         localId: localId, // 需要上传的图片的本地ID，由chooseImage接口获得
    //         isShowProgressTips: 1, // 默认为1，显示进度提示
    //         success: function (res) {
    //           //因这里只保存一张所以直接赋值，多张请自行处理为字符串
    //           that.serverId = res.serverId; // 返回图片的服务器端ID,即素材的media_id
    //         },
    //       });
    //     },
    //     error: function (res) {
    //       if (res.errMsg.indexOf('function_not_exist') > 0) {
    //         this.$message.error('版本过低请升级', '1', 'error.gif');
    //         return false;
    //       }
    //     },
    //   });
    // },
    //提交
    submit() {
      if (this.serverId == '') {
        this.$message.error('请拍完照再提交');
      }
      if (this.serverId) {
        const params = {
          server_id: this.serverId,
        };
        faceUpload(params).then((res) => {
          if (res.code == 1) {
            WeixinJSBridge.call('closeWindow');
          }
        });
      }
    },
         /**
     * jssdk 参数配置
     * @param res
     */
     setConfig(data) {
        wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: data.appId, // 必填，公众号的唯一标识
            timestamp: data.timestamp, // 必填，生成签名的时间戳
            nonceStr: data.nonceStr, // 必填，生成签名的随机串
            signature: data.signature, // 必填，签名,
            jsApiList: [
              "scanQRCode",
              "startRecord",
              "stopRecord",
              "onVoiceRecordEnd",
              "playVoice",
              "downloadVoice",
              "stopVoice",
              "onVoicePlayEnd",
              "uploadVoice",
              "chooseImage",
              "uploadImage",
              "downloadImage"
            ] // 必填，需要使用的JS接口列表
          });
          this.configWeiXinVoice();

    },
    /**
     * 微信js ready方法
     */
    configWeiXinVoice() {
      wx.ready(() => {
        wx.checkJsApi({
          jsApiList: [
            "scanQRCode",
            "startRecord",
            "downloadVoice",
            "stopRecord",
            "onVoiceRecordEnd",
            "playVoice",
            "stopVoice",
            "onVoicePlayEnd",
            "uploadVoice",
            "chooseImage",
            "uploadImage",
            "downloadImage"
          ],
          success: res => {
            wx.stopRecord({});
            if(!localStorage.WeiXinconf){
              //消除第一次录音是弹出的录音授权弹窗对用户体验问题
              wx.startRecord({
                success: function() {
                  localStorage.WeiXinconf = true
                  setTimeout(()=>{
                    wx.stopRecord({});
                  },500)
                },
                fail: function() {
                  console.log("失败回调");
                }
              });
            }
          }
        });

      });
      wx.error(res => {
        console.log(
          "你的微信版本太低，不支持微信JS接口，请升级到最新的微信版本！"
        );
      });
    }
  }
};
</script>
<style scoped lang="scss">
* {
  margin: 0;
  padding: 0;
}

.inner {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
.singinBtn {
  width: 100%;
  height: 44px;
  display: flex;
  position: fixed;
  bottom: 20px;
  text-align: center;
  justify-content: center;
}
.singinImg {
  width: 100%;
  height: 510px;
  background: #4B536A;
  border: 1px solid #000;
}
</style>
