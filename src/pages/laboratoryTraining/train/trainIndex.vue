<template>
  <div class="box" v-if="loginData.isFalg == 0">
    <Header title="培训任务" @backFun="goBack()"></Header>
    <div class="seach">
      <div style="margin-top: 0.1rem;">
        <van-search v-model="finList.name" @search="onSearch" :clearable="true" @clear="handleClear"
          placeholder="请输入搜索关键词" />
      </div>
      <div class="headline">
        <div class="headlineLable" v-for="(k, m) in coursesList" :key="m" :class="{ active: courseIndex == m }"
          @click="openCourses(m)">{{ k.lable }}</div>
      </div>
    </div>
    <div class="trainTitle">
      <van-pull-refresh v-if="trainList.length > 0" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="trainData" v-for="(item, index) in trainList" :key="index" @click.stop="openTrainDeatil(item)">
            <div class="train_bt">
              <div class="train_mc">
                <div class="train_mcLable">{{ item.name }}</div>
              </div>
            </div>
            <div class="examinLine"></div>
            <div>
              <div class="conterlable">所属科目：<span class="conterItem">{{ item.subjectName }}</span></div>
              <div class="conterlable">培训老师：<span class="conterItem">{{ item.teacherName }}</span></div>
              <div class="conterlable">培训内容：<span class="conterItem">{{ item.name || '-' }}</span></div>
              <div class="conterlable">课时数：<span class="conterItem">{{ item.coursePeriodNum || '0' }}</span></div>
              <div class="conterlable">培训时间：<span class="conterItem">{{ item.startTime }} 至 {{ item.endTime
              }}</span>
              </div>
              <div class="conterlable">培训地址：<span class="conterItem">{{ item.address }}</span></div>
            </div>
            <div class="examinBtn">
              <div v-if="item.examInfos != null" class="peixun" @click.stop="openTap(index)">培训后包含考试 <span class="arrow"
                  :style="{ transform: isOpen ? transform : '', transition: transition }"></span></div>
              <div v-if="item.taskStatus == 4 && item.type == 0" class="singinQd">
                <van-button
                  style="width: 88px; height: 32px; background-color:#3562DB; border-radius: 2px; margin-bottom: 10px; margin-top: 5px;"
                  type="info" @click.stop="openSigin(item)">培训签到</van-button>
              </div>
              <div v-if="item.taskStatus == 5" class="singinQd">
                <van-button
                  style="width: 88px; height: 32px; background-color:#C9CDD4; border: #C9CDD4; border-radius: 2px; margin-bottom: 10px;"
                  type="info">已签到</van-button>
              </div>
            </div>
            <div v-if="index == indexTrain">
              <div class="exmainList" v-if="isOpen">
                <div class="exmainBox" v-for="(x, y) in item.examInfos" :key="y">
                  <div style="display: flex; margin-bottom: .2rem;">
                    <div><img src="../../../assets/images/xz.png" alt=""></div>
                    <div class="trainLable">{{ x.examPlanName }}</div>
                  </div>
                  <div class="conterTrain">{{ x.examPlanDesc }}</div>
                  <div class="trainLine"></div>
                  <div style="display: flex; justify-content: space-between;">
                    <div style="font-size: .32rem; color: #1D2129;">查看详情</div>
                    <div style="color: #C9CDD4; font-size: .4rem;">></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <div>
      <permissionPrompt></permissionPrompt>
    </div>
  </div>
  
</template>
 
<script>
import axios from "axios";
import moment from 'moment';
import permissionPrompt from "../openCurser/components/permissionPrompt.vue"
export default {
  components: { permissionPrompt },
  data() {
    return {
      isLoading: false,
      loading: false,
      finished: false,
      finList: {
        name: '',
        taskStatusApp: '',
      },
      loginData: '',
      trainList: [],
      coursesList: [
      {
          lable: '全部',
          value: '',
        },
        {
          lable: '未开始',
          value: '1',
        },
        {
          lable: '培训中',
          value: '2',
        },
        {
          lable: '已签到',
          value: '3',
        },
        {
          lable: '超时',
          value: '4',
        }
      ],
      courseIndex: 0,
      isOpen: false,
      indexTrain: 0,
      transform: "rotate(180deg)",
      transition: "all .3s",
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
    }
  },
  created() {
    this.queryInfo = localStorage.getItem('taskInfo') ? JSON.parse(localStorage.getItem('taskInfo')) : '',
    this.loginData = JSON.parse(localStorage.getItem("loginData"));
    if(this.loginData.isFalg == 1) {
      return
    }
    this.getTrainPlanList()
  },

  mounted() {
    // wx.onHistoryBack(() => {
    //   this.$router.push('/homePage')
    //   return false
    // });
  },
  activated() {
    this.getTrainPlanList()
  },
  methods: {
    // 下拉刷新
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.trainList = []
      this.getTrainPlanList()
    },
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = true
      this.loading = true
      this.getTrainPlanList()
    },
    // 返回按钮
    goBack() {
      this.$router.push('/laboratoryTraining')
    },
    openTap(index) {
      this.indexTrain = index
      this.isOpen = !this.isOpen
    },

    // 搜索筛选
    onSearch(val) {
      // this.$toast(val)
      this.getTrainPlanList()
    },
    //搜索筛选
    handleClear() {
      this.finList.name = ''
      this.getTrainPlanList()
    },
    formatTimestamp(timestamp) {
      return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
    },
    // 培训列表
    getTrainPlanList() {
      let params = {
        current: this.pageParmes.pageNo,
        size: this.pageParmes.pageSize,
        listType: 1,
        ...this.finList,
      }
      this.$api.trainPlanList(params).then((res) => {
        this.trainList = res.list
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
      })
    },
    // 选择分类
    openCourses(el) {
      this.courseIndex = el
      console.log(this.courseIndex,'this.courseIndexthis.courseIndex');
      if(this.courseIndex == 0) {
        this.finList.taskStatusApp =  ''
        console.log(this.finList.taskStatusApp,'this.finList.taskStatusApp');
      } else if(this.courseIndex == 1) {
        this.finList.taskStatusApp = 1
      } else if(this.courseIndex == 2) {
        this.finList.taskStatusApp = 2
      } else if(this.courseIndex == 3) {
        this.finList.taskStatusApp = 3
      } else {
        this.finList.taskStatusApp = 4
      }
      this.getTrainPlanList()
    },
    // 查看详情
    openTrainDeatil(item) {
      this.$router.push({
        path: '/trainDetail',
        query: {
          id: item.taskId,
        }
      })
    },
    // 扫码签到跳转
    openSigin(item) {
      let parmas = {
        taskId: item.taskId
      }
      this.$api.trainSgin(parmas).then(res => {
        this.$toast.success('签到成功')
        this.getTrainPlanList()
      }).catch(err => {
        // console.log(err.data.msg,'err');
        this.$toast(err.data.msg)
      })
    },
  },

}
</script>
 
<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
  background: #f2f4f9;
}

.seach {
  height: 2.2rem;
  background: #ffffff;
}

.headline {
  // width: 100%;
  padding: 0rem .24rem;
  height: .6rem;
  line-height: .6rem;
  margin-top: .12rem;
  display: flex;
  justify-content: space-between;

  .headlineLable {
    width: 18%;
    background-color: #F7F8FA;
    text-align: center;

    &.active {
      color: #3562DB;
      background-color: #E6EFFC;
    }
  }
}

.trainTitle {
  // width: 100%;
  height: 71%;
  overflow-y: auto;
  padding: 0rem .3rem;
  margin-top: .2rem;
}

.trainData {
  // width: 100%;
  margin-bottom: .2rem;
  // height: 5.6rem;
  background-color: #ffffff;
  border-radius: .16rem;
  padding: 15px 15px;
}

.trainImg {
  width: 20%;
  height: .8rem;
  margin-top: .2rem;
}

.trainImg img {
  width: .8rem;
  height: .8rem;
  overflow: hidden;
}

.train_bt {
  display: flex;
  height: .6rem;
  width: 100%;
  margin-bottom: .2rem;
}

.train_mc {
  width: 80%;
}

// .train_mcItem {
//   width: 100%;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   font-size: .26rem;
//   color: #86909C;
// }

.train_mcLable {
  width: 100%;
  font-size: .32rem;
  color: #1D2129;
  margin: .18rem 0rem;
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
}

.conterlable {
  font-size: 14px;
  color: #86909C;
  margin-top: 14px;
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号表示被隐藏部分 */
}

.conterItem {
  font-size: 14px;
  color: #1D2129;
}

.examinBtn {
  display: flex;
  // justify-content: space-between;
  height: .8rem;
  width: 100%;
  line-height: .8rem;
  margin-top: .04rem;
  position: relative;

  .peixun {
    justify-self: flex-start;
  }

  .singinQd {
    position: absolute;
    right: 0;
  }

}

.examinLine {
  width: 100%;
  border: .01rem solid #F2F3F5;
}

.exmainList {
  width: 100%;
  // height: 3.16rem;
}

.exmainBox {
  width: 100%;
  // height: 2.9rem;
  background-color: #F7F8FA;
  border-radius: .08rem;
  padding: .3rem .2rem;
}

.exmainBox img {
  width: .36rem;
  height: .36rem;
  overflow: hidden;
}

.trainLable {
  font-size: .3rem;
  color: #1D2129;
  font-weight: bold;
  margin: .02rem 0rem 0rem .2rem;
}

.trainLine {
  width: 100%;
  border: .02rem solid #E5E6EB;
  margin: .26rem 0rem;
}

.conterTrain {
  line-height: .4rem;
  font-size: .28rem;
  color: #86909C;
}
.arrow {
  padding-top: 0rem !important;
  width: .36rem;
  height: .28rem;
  display: inline-block;
  background-image: url('../../../assets/images/箭头.png');
  background-size: 18px 14px;
  background-repeat: no-repeat;
}
.notList {
  position: relative;
  height: calc(100% - 1rem);

  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url('../../../assets/images/noData.png') 0 40% no-repeat;
    background-size: 100% auto;

    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
