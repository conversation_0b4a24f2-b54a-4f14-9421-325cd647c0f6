<template>
  <div class="options">
    <Header title="故障原因" @backFun="goBack"></Header>
    <div class="ipt-header header-two">
      <div class="search-ipt">
        <span class="iconfont">&#xe60a;</span>
        <div v-show="concealed" class="inputdiv" @click="serve"></div>
        <input ref="searchInput" v-model="query" id="search" type="text" maxlength="50" placeholder="请输入关键字搜索" />
        <span v-if="concealedList" class="countermand" @click="off">取消</span>
      </div>
    </div>
    <div class="servelistClass" >
      <van-radio-group v-model="selectedValue">
        <van-radio @click="servetwo(item.id,item.name)" :name="item.id" v-for="(item,index) of servelist" :key="index"
          style="margin-bottom:0.3rem">{{item.name}}</van-radio>
      </van-radio-group>
    </div>
    
    <div v-if="concealed" style="margin-top:1.6rem">
      <div>
        <div class="selectedValueClass" >
          <van-radio-group v-model="selectedValue">
            <van-radio @click="two(item.id,item.name)" :name="item.id" v-for="(item,index) of troubleList" :key="index"
              style="margin-bottom:0.3rem">{{item.name}}</van-radio>
          </van-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>


<script type=text/ecmascript-6>
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  mixins: [fontSizeMixin],
  name:"OptionsTrouble",
  data(){
    return{
      selectedValue:"",
      active:0,
      isshow:1,   //当前显示级别
      newshow:false,  //新建故障原因
      newshowMaintain:false,  //新建维修方法
      concealed:true,     //点击搜索的显隐
      concealedList:false,     //点击搜索的显隐
      concealedListVal:false,
      troubleList:[],
      maintainList:[],
      servelist:[],
      servelistCheck:[],
      chooseAfterValue:[], //选中的数组
      troubleVal:"",     //新增故障原因
      maintainVal:"",    //新增维修方法
      userId:"",    //用户id
      query:"",     //模糊查询数据
      oneobj:{},    //一级
      choose:[],
      i:-1,
    }
  },
   mounted(){
    this.request(1);
  },
   watch:{
    query(){
      if(!this.query) return (this.concealedListVal=false)
        this.serveQuery(1,this.query)
        this.concealedListVal=true
    }
  },
  methods:{
   
    goBack() {
      this.$router.go(-1);
    },
    // 获取维修方法数据
    two(id,name){
     this.$router.push({
        path:'/renewal',
        query:{
          faultId:id,
          faultName:name,
          itemServiceCode:this.$route.query.itemServiceCode
        }
      })
    
    },
   
    servetwo(id,name){
      this.off();
    this.$router.push({
        path:'/renewal',
        query:{
          faultId:id,
          faultName:name,
           itemServiceCode:this.$route.query.itemServiceCode
        }
      })
    },
  
    /**
     * 点击搜索
     * @param  false||true
     */
    serve(){
       // 手动让 input 获取焦点
      this.$refs.searchInput.focus();
      this.concealed=false;
      this.concealedList=true;
    },
    off(){
      this.concealed=true;
      this.concealedList=false;
      this.query="";
      this.servelist=[];
      this.servelistCheck=[];
    },
   
   
    /**
     * 获取故障原因
     */
    request(type,parentId){
      
      this.$api.getMalfunctionReasonMethod({
          itemServiceCode:this.$route.query.itemServiceCode,
          type,
          parentId,
          // fuzzySearch
        })
        .then( res=>{
          if(type==1){
             this.troubleList=res;
          } else {
             this.maintainList=res;
          }
         
         
        } )
    },
  
    /**
     * 模糊查询请求函数
     * @param {fuzzySearch} 查询字段
     */
    serveQuery(type,fuzzySearch,parentId){
      this.$api.getMalfunctionReasonMethod({
          itemServiceCode:this.$route.query.itemServiceCode,
          type,
          fuzzySearch,
          parentId,
        })
        .then( res=>{
          if(type==1) {
            this.servelist=res
          } else {
            this.servelistCheck=res
          }
          
        } )
    },
  },
}
</script>
<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';
.options
  min-height: 100%
  overflow-y: hidden
  background-color: $bgColor
.ipt-header
    width: 92%;
    height: .78rem
    position: fixed;
    line-height: .78rem
    padding: .2rem .32rem
    background-color: #efeff4
    z-index: 2;
    border-bottom:1px solid #efeff4
.search-ipt
  position: relative
  
  .inputdiv
    position: absolute
    width :100%
    height:.8rem
    top:0
    left : 0
    // background:#ddd
  .countermand
    height :.28rem
    line-height :.28rem
    position :absolute
    top:.25rem
    right:.25rem
    padding-left:.28rem
    border-left:1px solid #D3D3D5
    color:#353535
    font-size: calc(16px * var(--font-scale))!important;

  .iconfont
    position: absolute
    left: .28rem
    top: .05rem
    color: $textColor
  #search
    background-color: #fff
    padding-left: .8rem
    padding-right: 1.22rem
    box-sizing: border-box
    height: .78rem
    display: inline-block
    width: 100%
    border-radius: 10px
.options-list
  width : 100%
  font-size: 0.28rem
  overflow :auto
  padding-top:1.05rem
  padding-bottom:1.3rem
  .content-text
    display :block
    width :95%
    overflow hidden
    text-overflow :ellipsis
    white-space:nowrap
    color:#353535
    font-size : .3rem
    margin-left: 0.32rem
  .select-img
    position :absolute
    top: .28rem
    right: .25rem
    width : .44rem
.list-content
  height .98rem
  line-height :  .98rem
  position : relative
  font-size: .32rem
  border-top:1px solid #EFEFF4
  background-color: #fff
  padding-right: .67rem
.options-footer
  width : 100%
  height : 1.32rem
  position:fixed
  display :flex
  justify-content :space-around
  bottom :0
  left:0
  border-top:1px solid #EFEFF4
  background-color : #ffffff
  .btn-back
    width :3.33rem
    color: #3562db
    background : #0000
    font-size :0.36rem
  .btn-go
    width: 3.33rem
    font-size :0.32rem
  .btn-backone
    width 1.53rem
    font-size: 0.36rem
    color: #3562db
    background : #0000
  .btn-submit
    width :5.22rem
    font-size: 0.36rem
  .btn-new
    width : 1rem
    font-size:0.32rem
    line-height: 66px
    font-size: 50px
    font-weight: 900
.btn
  background-color: $btnColor
  color: #fff
  border-radius: 5px
  text-align: center
  height: .88rem
  margin-top:0.23rem
.shade
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background: rgba(0,0,0,0.2);
.options-new
    width: 100%;
    height :4.56rem
    position: fixed;
    bottom: 0;
    left:0;
    background: #fff;
    border-top-left-radius:  10px;
    border-top-right-radius: 10px;
    z-index: 200;
    .btn-confirm
      width :6.86rem
      margin-left:0.32rem
      font-size : .36rem
    .inputval
      box-sizing : border-box
      width: 6.84rem;
      height: 1.98rem;
      margin-left: .32rem;
      margin-bottom:0.25rem;
      border:1px solid #E5E5E5;
      padding:0.31rem ;
      color: #353535
      font-size: .3rem
      resize:none
    .new-title
      line-height : .97rem
      margin-bottom: .03rem
      text-align :center
      font-size:.28rem
      .title-text
        color : #999
      .title-cancel>img
        position :absolute
        top:.23rem
        right: .31rem
        width :.45rem
.active
  background-color : #F2F2F2
  border-top: 1px solid #fff
.selectedValueClass
  margin-left: 0.6rem
  font-size: calc(16px * var(--font-scale))!important;
.servelistClass
  margin-top: 1.6rem
  margin-left: 0.6rem
</style>
<style rel="stylesheet/stylus" lang="stylus">
.van-tabs__line
  background-color: #098ae8
</style>
<style lang="scss" scoped>
/deep/ .van-radio__label {
  font-size: calc(16px * var(--font-scale))!important;
  line-height: 1.2;
}
#search {
  font-size: calc(16px * var(--font-scale))!important;
}
</style>