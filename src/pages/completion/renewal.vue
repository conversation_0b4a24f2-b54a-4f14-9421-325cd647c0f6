<!--  -->
<!-- 模板 -->
<template>
  <!-- 页面的主体结构 -->
  <div class='options'>
    <Header title="更新" @backFun="goBack"></Header>
    <div class="content">
      <div class="weui-cell border-rightbottom item-style">
        <div class="weui-cell__hd">
          <label :class="['weui-label', 'title', 'star' ]" style="font-size:calc(16px * var(--font-scale));padding-left:0px">故障原因</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input class="weui-input ipt" type="text" v-model="faultCause" maxlength="20" placeholder="请输入/选择故障原因"
            @input="faultCauseInput" />
          <van-icon @click="clickIcon" name="search" />
        </div>
      </div>
      <div class="weui-cell border-rightbottom item-style">
        <div class="weui-cell__hd">
          <label class="weui-label title" style="font-size:calc(16px * var(--font-scale))">维修方法</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input class="weui-input ipt" type="text" v-model="faultMethod" maxlength="20" placeholder="请输入维修方法"
            @input="faultMethodInput" />
        </div>
      </div>
    </div>
    <div class="options-footer">
      <!-- <button class="btn btn-backone" @click="one">返回</button> -->
      <button class="btn btn-submit" @click="gosunmit">确定</button>
      <!-- <button class="btn btn-new" @click="newMaintain"> <span>+</span></button> -->

    </div>
  </div>

</template>

<script>
import fontSizeMixin from "@/mixins/fontSizeMixin";
  export default {
    mixins: [fontSizeMixin],
     name: "Renewal",
    // 组件注册
    components: {},
    data() {
      // 数据定义
      return {
        faultCause: '',
        faultMethod: '',
        userId: ""

      };
    },
    // 计算属性定义
    computed: {},
    // 监视器，用于监听数据的变化
    watch: {},
    // 方法定义，包括事件处理函数
    methods: {
      goBack() {
        this.$router.replace({
          path: '/OrderHandle',
          query: {
            itemServiceCode: this.$route.query.itemServiceCode
          }
        })
        this.$router.go(-1);
      },
      //点击确定
      gosunmit() {
        if (!this.faultCause) {
          $.toast("请输入/选择故障原因", "text");
          return
        } else {
          this.$api.saveMalfunctionReasonMethod({
            type:1,
              userId: this.userId,
              parentName: this.faultCause,
              parentId: this.$route.query.faultId,
              itemServiceCode: this.$route.query.itemServiceCode,
              name: this.faultMethod
            })
            .then(res => {
              this.$router.replace({
                path: '/optionsTrouble',
                query: {
                  itemServiceCode: this.$route.query.itemServiceCode,
                  i:-1
                }
              })
              this.$router.go(-1);
            })
        }

      },
      faultCauseInput() {},
      faultMethodInput() {},
      //点击搜素按钮
      clickIcon() {
        this.$router.push({
          path: '/faultCause',
          query: {
            itemServiceCode: this.$route.query.itemServiceCode
          }
        })

      }

    },
    // 生命周期钩子：创建后
    created() {

    },
    // 生命周期钩子：载入后
    mounted() {
      let id = JSON.parse(window.localStorage.loginInfo).id;
      this.userId = id;
      this.faultCause = this.$route.query.faultName
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    },

    beforeCreate() {}, // 生命周期：创建前
    beforeMount() {}, //生命周期 ： 载入前
    beforeUpdate() {}, //生命周期：更新前
    updated() {}, //生命周期：更新后
    beforeDestroy() {}, //生命周期：销毁前
    destroyed() {}, //生命周期：销毁后
    activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
  }

</script>
<style lang='scss' scoped>
  .options {
    //   min-height: 100%;
    overflow-y: hidden;
    background-color: #fff
  }

  .content {
    background-color: #f5f5f9;
    min-height: 20rem;
    padding-top: .8rem;
    padding-left: .5rem;
  }

  .options-footer {
    width: 100%;
    height: 1.32rem;
    position: fixed;
    display: flex;
    justify-content: space-around;
    bottom: 0;
    left: 0;
    // border-top: 1px solid #EFEFF4;
    background-color: #f5f5f9;
  }

  .btn-submit {
    width: 6.22rem;
    font-size: calc(16px * var(--font-scale))!important;
  }

  .btn {
    background-color: #3562db;
    color: #fff;
    border-radius: 5px;
    text-align: center;
    height: .88rem;
    margin-top: 0.23rem;
  }

  .ipt-content {
    text-align: right;
    display: flex;
    align-items: center;
    color: #999;
  }

  .van-icon {
    font-size: 20px;
  }

  .star::before {
    position: absolute;
    left: 0px;
    top: 14px;
    content: '*';
    color: red;
  }
  .weui-cell__bd {
    font-size: calc(14px * var(--font-scale))!important;
  }

</style>
