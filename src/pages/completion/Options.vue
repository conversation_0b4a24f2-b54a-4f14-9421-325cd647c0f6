<template>
  <div class="options">
    <div class="ipt-header header-two" >
      <div class="search-ipt">
        <span class="iconfont">&#xe60a;</span>
        <input id="search" type="text" placeholder="请输入关键字"/>
        <span class="line"></span>
        <span class="cancle">取消</span>
      </div>
    </div>  
    <div> 
      <ul class="options-list" v-if="isshow==1">
        <li class="list-content" v-for="(item,index) of infolist" :key="index" @click="two"> 
           <span class="content-text">{{item.name}}</span>
           <span class="iconfont arrow gotwo">&#xe646;</span>
        </li>
      </ul>
      
      <ul class="options-list" v-if="isshow==2">
        <li class="list-content" v-for="(item,index) of twolist" :key="index"> 
           <span class="content-text">{{item.name}}</span>
        </li>
      </ul>
      
    </div>
    <div class="options-footer"  v-if="isshow==1">
      <button class="btn">新增</button>
    </div>
    <div class="options-footer" v-if="isshow==2">
      <button class="btn" @click="one">返回</button>
    </div>
  </div>
</template>


<script type=text/ecmascript-6 >
export default {
  name:"Options",
  data(){
    return{
      isshow:1,
      infolist:[
        {
          name:"测试1",
          two:[
            {name:"11111"},
            {name:"22222"},
            {name:"33333"},
            {name:"44444"},
            {name:"55555"},
            {name:"66666"},
          ]
        },
        {
          name:"测试2",
          two:[
            {name:"2-1111"},
            {name:"2-2222"},
            {name:"2-3333"},
            {name:"2-4444"},
            {name:"2-5555"},
            {name:"2-6666"},
          ]
        },
        {
          name:"测试3",
          two:[
            {name:"3-1111"},
            {name:"3-2222"},
            {name:"3-3333"},
            {name:"3-4444"},
            {name:"3-5555"},
            {name:"3-6666"},
          ]
        },
        {
          name:"测试4",
          two:[
            {name:"4-1111"},
            {name:"4-2222"},
            {name:"4-3333"},
            {name:"4-4444"},
            {name:"4-5555"},
            {name:"4-6666"},
          ]
        },
      ],
      twolist:[
        {name:"1-1"},
        {name:"1-2"},
      ]
    }
  },
  methods:{
    two(){
      this.isshow=2
    },
    one(){
      this.isshow=1
    },
  },
}
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';
.options
  min-height: 100%
  overflow-y: hidden
  background-color: $bgColor
.ipt-header
    height: .78rem
    line-height: .78rem
    padding: .2rem .32rem
    background-color: #fff
.search-ipt
  position: relative
  .iconfont
    position: absolute
    left: .28rem
    top: .05rem
    color: $textColor
  #search
    background-color: $bgColor
    padding-left: .8rem
    box-sizing: border-box
    height: .78rem
    display: inline-block
    width: 100%
    border-radius: 25px
  .line
    width: 1px
    height: .3rem
    background-color: #D3D3D5
    display: inline-block
    position: absolute
    right: 1.1rem
    top: .24rem
  .cancle
    position: absolute
    right: 5px
    top: 0
    width: 1rem
    text-align: center
.options-list
  width : 100%
  font-size: 0.28rem
  .list-content
    height 1rem
    border-top:1px solid #ddd
    font-size: .32rem
    background-color: #fff
    position : relative
    line-height : 1rem
    .content-text
      color:#888888
      margin-left: 0.2rem
    .gotwo
      position:absolute
      top: 0rem
      right: .2rem
.options-footer
  position:fixed
  bottom :0
  left:0
  width : 100%
  height : 1.3rem
  background-color : #ffffff
  .btn
    position absolute
    right:.3rem
    top: 25%
    background-color: $btnColor
    width: 104px
    height: 35px
    font-size: 18px
    color: #fff
    border-radius: 3px
    text-align: center
</style>
