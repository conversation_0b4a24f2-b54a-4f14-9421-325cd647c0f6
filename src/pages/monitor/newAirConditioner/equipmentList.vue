<!--
 * @Author: hedd
 * @Date: 2023-08-16 16:11:02
 * @LastEditTime: 2023-08-17 10:07:11
 * @FilePath: \ybs_h5\src\pages\monitor\boiler\steamSupply.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header :title="$route.query.title" @backFun="goback"></Header>
    <div class="eq_table">
      <van-sticky :offset-top="'10vh'">
        <div class="eq_tr">
          <div class="eq_th">设备名称</div>
          <div class="eq_th">运行</div>
          <div class="eq_th">在线</div>
          <div class="eq_th">报警</div>
        </div>
      </van-sticky>
      <div v-if="airRunRate.length" style="background-color: white;">
        <div class="eq_tr" v-for="(item,index) in airRunRate" :key="index + 'tableData'">
          <div class="eq_td">{{item.surveyName}}</div>
          <div class="eq_td">
            <span class="td_btn" :class="item.run == '运行' ? 'td_success' : 'td_info'">{{item.run}}</span>
          </div>
          <div class="eq_td">
            <span class="td_btn" :class="item.line == '在线' ? 'td_success' : 'td_info'">{{item.line}}</span>
          </div>
          <div class="eq_td">
            <span class="td_btn" :class="item.warn == '正常' ? 'td_success' : 'td_danger'">{{item.warn}}</span>
          </div>
        </div>
      </div>
      <div class="no_data" v-else>
        <span>暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      airRunRate: []
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback)
    this.getDetails()
  },
  methods: {
    getDetails() {
      // this.airRunRate =  [
      //   {
      //     "surveyName": "风冷热泵1#",
      //     "run": "运行",
      //     "line": "在线",
      //     "warn": "故障"
      //   },
      //   {
      //     "surveyName": "风冷热泵机1#",
      //     "run": "停止",
      //     "line": "离线",
      //     "warn": "正常"
      //   },
      //   {
      //     "surveyName": "风冷热泵机组2#",
      //     "run": "运行",
      //     "line": "在线",
      //     "warn": "正常"
      //   }
      // ]
      // return
      let query = this.$route.query
      this.$api.querySurveyListByMenuCode(query).then(res => {
        this.airRunRate = res
      });
    },
    goback() {
      this.$router.go(-1)
    },
  }
}
</script>

<style scoped lang="stylus">
.no_data{
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center
}
.inner{
  height: 100%;
  background-color: #F2F4F9;
  .eq_table{
    width: 100%;
    .eq_tr{
      width: 100%;
      display: flex
      .eq_th{
        flex: 1
        padding: 16px;
        font-size: 15px;
        font-weight: 400;
        color: #1D2129;
        background: #E6EFFC;
      }
      .eq_td{
        flex: 1
        padding: 16px;
        font-size: 15px;
        font-weight: 400;
        color: #4E5969;
        border-bottom: 1px solid #E5E6EB;
        .td_btn{
          padding: 5px;
          border-radius: 2px;
          font-size: 12px;
          font-weight: 500;
        }
        .td_success{
          background: #E8FFEA;
          color: #00B42A;
        }
        .td_info{
          background: #F2F3F5;
          color: #4E5969;
        }
        .td_danger{
          background: #FFECE8;
          color: #F53F3F;
        }
      }
    }
  }
}
</style>
