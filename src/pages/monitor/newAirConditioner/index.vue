<template>
  <Overview :projectCode="projectCode" title="空调末端" />
</template>

<script>
import { monitorTypeList } from "./../components/dict.js";
import Overview from './../components/overview/index.vue'
export default {
  data() {
    return {
      projectCode: monitorTypeList.find(
        item => item.projectName === '空调监测'
      ).projectCode,
    }
  },  
  components: {
    Overview
  }
}
</script>

