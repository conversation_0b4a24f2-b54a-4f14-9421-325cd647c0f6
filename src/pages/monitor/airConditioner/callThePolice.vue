<!--
 * @Author: hedd
 * @Date: 2023-05-22 17:02:37
 * @LastEditTime: 2024-03-21 17:45:02
 * @FilePath: \ybs_h5\src\pages\monitor\airConditioner\callThePolice.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header :title="title" @backFun="goback"></Header>
    <van-tabs v-model="active" sticky offset-top="10vh" v-if="title=='告警统计'">
      <van-tab v-for="item in alarmStatusArr" :title="item.name" :key="item.alarmStatus" :name="item.alarmStatus">
        <ListPolice
          :requestApi="$api.selectAlarmRecordAll"
          :queryParams="{
            'alarmStatus':item.alarmStatus,
            'projectCode':item.projectCode
          }"
          :isTabs="true"
        >
          <template #default="slotItem">
            <ListItem :item="slotItem.item"></ListItem>
          </template>
        </ListPolice>
      </van-tab>
    </van-tabs>

    <ListPolice
      v-else
      :requestApi="$api.selectAlarmRecordAll"
      :queryParams="{
        'projectCode':$route.query.projectCode,
        menuCode: active
      }"
    >
      <template #default="slotItem">
        <ListItem :item="slotItem.item"></ListItem>
      </template>
    </ListPolice>

  </div>
</template>

<script>
import ListPolice from "@/pages/monitor/components/listPolice/index.vue";
import ListItem from "@/pages/monitor/components/listItem/index.vue";
export default {
  components: {
    ListPolice,
    ListItem
  },
  data(){
    return{
      title:'报警清单',
      alarmStatusArr:[
        {
          name:'全部',
          alarmStatus:'',
          projectCode:''
        },
        {
          name:'未处理',
          alarmStatus:'0',
          projectCode:''
        },
        {
          name:'处理中',
          alarmStatus:'1',
          projectCode:''
        }
      ],
      active: '',
    }
  },
  created(){
    console.log(this.$route.query);
    if(this.$route.query.title){
      this.title = this.$route.query.title
    }
    if(this.$route.query.entityMenuCode){
      this.active = this.$route.query.entityMenuCode
    }
  },
  mounted(){
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
  }
}
</script>

<style scoped lang="stylus">
.inner{
  height: 100%;
  background-color: #F2F4F9;
}
</style>
