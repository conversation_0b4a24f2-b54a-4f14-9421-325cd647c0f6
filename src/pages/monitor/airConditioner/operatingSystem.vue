<template>
  <div class="inner">
    <Header title="空调监测" @backFun="goback"></Header>
    <p class="describe">查看运行系统图</p>
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <van-cell style="padding: 16px;" v-for="item in list" :key="item.id">
        <template slot="default">
          <span style="font-size: 16px;">{{ item.name }}</span>
        </template>
      </van-cell>
    </van-list>
  </div>
</template>

<script>
export default {
  data(){
    return {
      list:[]
    }
  },
  methods:{
    onLoad(){
      setTimeout(() => {
        this.list = [
          {name:'1F空调机组',id:1},
          {name:'2F空调机组',id:2},
          {name:'3F空调机组',id:3},
          {name:'4F空调机组',id:5},
          {name:'6F空调机组',id:6},
          {name:'7F空调机组',id:7},
          {name:'8F空调机组',id:8},
          {name:'9F空调机组',id:9},
        ]
        this.loading = false;
        this.finished = true;
      }, 1000);
    },
    goback() {
      YBS.apiCloudBack(this)
    },
  }
}
</script>

<style scoped lang="stylus">
.ope_content{
  background: white;
  padding: 0 16px;
}
.describe{
  font-size: 14px;
  font-weight: 400;
  color: #86909C;
  padding: 8px 16px;
}

.inner{
  height: 100%;
  background-color: #F2F4F9;
}
</style>