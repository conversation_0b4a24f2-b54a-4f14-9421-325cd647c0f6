<template>
  <div class="inner">
    <Header title="空调监测" @backFun="goback"></Header>
    <van-row  gutter="10" style="padding:16px 10px;background: white;">
      <van-col span="8" v-for="(item, index) in totalList" :key="index + 'totalList'" @click="jumpVariousTypesPage(item)">
        <div class="air_title" :style="{ 'background': item.bagColor }">
          <div class="air_title_l">
            <img :src="item.img" alt="">
            <p :style="{ 'color': item.textColor }">{{item.name}}</p>
          </div>
          <span :style="{ 'color': item.textColor }">{{item.num}}</span>
        </div>
      </van-col>
    </van-row>

    <div class="equipment">
      <div class="equipment_item" v-for="(item,index) in tabList" :key="index + 'equipmentArr'">
        <div class="item_title">
          <h3>{{item.menuName}}</h3>
          <span @click="jupmGroupListPage(item.menuCode)">图形查看</span>
        </div>
        <div class="total_num">总数<span>{{item.count||0}}</span></div>
          <div v-if="item.eqTypeArr&&item.eqTypeArr.length">
            <div class="chart" :style="{'grid-template-columns': getLineWidth(item.eqTypeArr,item.count,97,1)}">
              <div class="chart_line" v-for="(v,i) in item.eqTypeArr" :key="i + 'eqTypeArr'">
                <div class="line" :style="{'background':v.color}"></div>
                <!-- <div class="line_text">
                  <p :style="{'color':v.color}">{{ v.name }}</p>
                  <span>{{ v.num }}</span>
                </div> -->
              </div>
            </div>
            <div class="chart" :style="{'grid-template-columns': getLineWidth(item.eqTypeArr,item.count,70,10)}">
              <div class="chart_line" v-for="(v,i) in item.eqTypeArr" :key="i + 'eqTypeArr'">
                <div class="line_text">
                  <p :style="{'color':v.color}">{{ v.name }}</p>
                  <span>{{ v.num }}</span>
                </div>
              </div>
            </div>
          </div>
          
      </div>
    </div>

    <div class="today_function">
      <div class="today_fun_title">
        <p>今日运行时长统计（h）</p>
      </div>
      <div id="airConditionerCake" style="width: 100%;height: 6rem;"></div>
      <div class="today_fun_title">
        <p>累计运行时长统计（h）</p>
      </div>
      <van-row v-if="eqCumulativeArr.length" @click="toRunInventory({projectCode})">
        <van-col span="8" v-for="(item, index) in eqCumulativeArr" :key="index + 'eqCumulativeArr'">
          <div class="eq_cumulative">
            <p>{{ item.runTime }}</p>
            <span>{{ item.menuName }}</span>
          </div>
        </van-col>
      </van-row>
      <div class="no_data" style="height: 2rem" v-else >
        <span>暂无数据</span>
      </div>
    </div>

    <div class="today_function">
      <div class="today_fun_title">
        <p>今日运行率占比排行</p>
      </div>
      <div id="airConditionerColumn" style="width: 100%;height: 4rem;"></div>
      <div class="eq_table">
        <div class="eq_tr">
          <div class="eq_th">监测项类型</div>
          <div class="eq_th">监测项数量</div>
          <div class="eq_th">平均运行率</div>
        </div>
        <div v-if="airRunRate.length">
          <div class="eq_tr" v-for="(item,index) in airRunRate" :key="index + 'tableData'">
            <div class="eq_td">{{item.menuName}}</div>
            <div class="eq_td">{{item.monitorCount}}</div>
            <div class="eq_td">{{item.runRate}}</div>
          </div>
        </div>
        <div class="no_data" v-else>
          <span>暂无数据</span>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import global from "@/utils/Global";
import moment from "moment";
import { monitorTypeList } from "./../components/dict.js";
export default {
  data() {
    const projectName = "空调监测";
    return {
      projectName,
      projectCode: monitorTypeList.find(
        item => item.projectName === projectName
      ).projectCode,
      totalList:[
        {
          img: require('@/assets/images/monitor/icon2.png'),
          name:'故障',
          num: 0,
          bagColor: '#FFECE8',
          textColor: '#F53F3F',
          type:4,
          path: '/abnormalList',
          abnormalType: 'fault'
        },
        {
          img: require('@/assets/images/monitor/icon3.png'),
          name:'报警',
          num: 0,
          bagColor: '#FFECE8',
          textColor: '#F53F3F',
          path: '/callThePolice',
          abnormalType: 'fault'
        },
        {
          img: require('@/assets/images/monitor/icon1.png'),
          name:'离线',
          num: 0,
          bagColor: '#F2F3F5',
          textColor: '#86909C',
          type:5,
          path: '/abnormalList',
          abnormalType: 'offLine'
        }
      ],
      menuList:[],
      tabList:[],
      // equipmentArr:[
      //   {
      //     eqName:'空调机组',
      //     eqNum: 33,
      //     eqTypeArr: [
      //       { name:'运行',num:8000,color:'#00B42A' },
      //       { name:'停止',num:3,color:'#4E5969' },
      //       { name:'故障',num:6,color:'#F53F3F' },
      //       { name:'离线',num:2,color:'#FF7D00' },
      //     ]
      //   },
      //   {
      //     eqName:'风机盘管',
      //     eqNum: 33,
      //     eqTypeArr: [
      //       { name:'运行',num:12,color:'#00B42A' },
      //       { name:'停止',num:18,color:'#4E5969' },
      //       { name:'故障',num:16,color:'#F53F3F' },
      //       { name:'离线',num:21,color:'#FF7D00' },
      //     ]
      //   }
      // ],
      eqCumulativeArr:[],
      airRunRate:[]
    }
  },
  methods:{
    getRunData(v,item) {
      if (v.name == '运行') {
        console.log(v,item);
        let data = {
          entityMenuCode: item.code,
          projectId: item.projectId,
        }
        this.toRunInventory(data)
      }
    },
    toRunInventory(data) {
      this.$router.push({
        path: '/monitorRunList',
        query: data
      })
    },
    // 获取设备分组列表
    getEntityMenuList() {
      const params = {
        projectId: this.projectCode
      };
      this.$api.getEntityMenuList(params).then(res => {
        this.menuList = res;
        // this.tabList = res.filter(item => item.parentId === "#")
      });
      this.$api.airGetEntityMenuList({
        projectCode: this.projectCode
      }).then(res => {
        this.tabList = res.map(ele => {
          return {
            ...ele,
            eqTypeArr:[
              { name:'运行',num:ele.normalCount||0,color:'#00B42A' },
              { name:'停止',num:ele.stopCount||0,color:'#4E5969' },
              { name:'故障',num:ele.faultCount||0,color:'#F53F3F' },
              { name:'离线',num:ele.offlineCount||0,color:'#FF7D00' },
            ]
          }
        })
      });
    },
    getAirViewDetail(){
      // this.totalList.forEach(ele=>{
      //   if(ele.type){
      //     this.$api.airViewDetail({type:ele.type,projectCode:this.projectCode}).then(res=>{
      //       if(ele.type == 4){
      //         ele.num = res.airConditionBreakList.count
      //       }else{
      //         ele.num = res.airConditionOffList.count
      //       }
      //     })
      //   }
      // })
      this.$api.airViewDetailApp({projectCode:this.projectCode}).then(res=>{
        this.totalList[2].num = res.offLineCount
        this.totalList[0].num = res.faultCount
      })
      this.$api.airRunPolice({projectCode:this.projectCode}).then(res=>{
        this.totalList[1].num = res.total
      })
      
    },
    // 跳转分组列表页
    jupmGroupListPage(entityMenuCode) {
      console.log('this.menuList=======', this.menuList);
      let path = "";
      // 如果有二级分组，跳转到分组列表页；如果没有，跳转到设备列表页
      if (this.menuList.some(e => e.parentId == entityMenuCode)) {
        path = "/monitorGroup";
      } else {
        path = "/monitorEntityPage";
      }
      this.$router.push({
        path: path,
        query: {
          projectCode: this.projectCode,
          entityMenuCode: entityMenuCode
        }
      });
    },
    // 跳转报警清单 故障离线高低液位页
    jumpVariousTypesPage({path, abnormalType}) {
      console.log(path, abnormalType);
      let queryData = {
        projectCode: this.projectCode,
        abnormalType,
      };
      this.$router.push({
        path,
        query: queryData
      });
    },
    goback() {
      this.$YBS.apiCloudCloseFrame()
    },
    getAirRunRate(){
      // let data = {
      //   endTime: "2023-12-31",
      //   projectCode: this.projectCode,
      //   startTime: "2023-01-01",
      // }
      let data = {
        endTime: moment().format('YYYY-MM-DD'),
        projectCode: this.projectCode,
        startTime: moment().format('YYYY-MM-DD'),
      }
      this.$api.airRunRate(data).then(res=>{
        this.airRunRate = res.airRunRate
        this.getAirConditionerColumn(res.airRunRate)
      })
    },
    getAirConditionerColumn(data = []){
      console.log(data);
      let myChart2 = this.$echarts.init(document.getElementById("airConditionerColumn"));
      global.extension(myChart2,'xAxis',4)
      let textColor = '#86909C'
      let option = {}
      if(data.length){
        option = {
          color:['#3562DB'],
          xAxis: {
            type: 'category',
            data: data.map(x => x.menuName),
            triggerEvent: true,
            axisLine: {
              lineStyle:{
                color:'#E5E6EB'
              }
            },
            axisLabel: {
              color: textColor,
              fontSize: 12,
              formatter: function (params) {
                var val="";
                if(params.length > 4){
                    val = params.substr(0,4)+'...';
                    return val;
                }else{
                    return params;  
                }
              }
            },
          },
          yAxis: {
            type: 'value',
            name: '单位：%',
            nameTextStyle:{
              color: textColor,
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: textColor,
              fontSize: 13
            },
            // y轴轴线颜色
            splitLine: {
              show: true,
              lineStyle: {
                color: "#E5E6EB"
              }
            }
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              bottom: 5,
              start: 0,
              end: 15,
              height: 6,
              borderRadius: 0,
              borderColor: '#D7DEE8',
              fillerColor: '#C4C4C4', // 滑动块的颜色
              // backgroundColor: "#33384b", //两边未选中的滑动条区域的颜色
              // 是否显示detail，即拖拽时候显示详细数值信息
              showDetail: false,
              zoomLock: false,
              brushSelect: false,
              // 控制手柄的尺寸
              handleSize: 8,
              // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
              showDataShadow: false,
              // filterMode: 'filter',
              handleIcon: 'M0,0 v11h3 v-11h-3 Z',
              handleStyle: {
                color: '#FFF',
                shadowOffsetX: 0, // 阴影偏移x轴多少
                shadowOffsetY: 0 // 阴影偏移y轴多少
                // borderCap: 'square',
                // borderColor: '#D8DFE9',
                // borderType: [15, 20],
              }
            },
            {
              type: 'inside',
              // show: false,
              xAxisIndex: [0],
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ],
          series: [
            {
              data: data.map(x => x.monitorCount),
              type: 'bar',
              barWidth:10,
            }
          ],
          grid: { // 让图表占满容器
            top: '30px',
            left: '16px',
            right: '16px',
            bottom: '20px',
            containLabel: true
          }
        };
      }else{
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      
      myChart2.setOption(option)
    },
    getAirRunTime(){
      // let data = {
      //   endTime: "2023-12-31",
      //   projectCode: this.projectCode,
      //   startTime: "2023-01-01",
      // }
      // 今日运行时长
      let data = {
        endTime: moment().format('YYYY-MM-DD'),
        projectCode: this.projectCode,
        startTime: moment().format('YYYY-MM-DD'),
      }
      this.$api.airRunTime(data).then(res=>{
        this.getAirConditionerCake(this.dataHandle(res.airRunTime))
      })
      // 累计运行时长
      let cumulativeData = {
        projectCode: this.projectCode
      }
      this.$api.airRunTime(cumulativeData).then(res=>{
        this.eqCumulativeArr = res.airRunTime
      })
    },
    dataHandle(airRunTime) {
      // var data = [
      //   {
      //     name: '今日运行时长统计今日运计',
      //     value: 123,
      //     proportion: '12%',
      //   },
      //   {
      //     name: '今日运行时长统计今日运行时长统计今日运行时长统计1',
      //     value: 123,
      //     proportion: '12%',
      //   },
      //   {
      //     name: '今日运行时长统计今日运行时长统计2',
      //     value: 123,
      //     proportion: '12%',
      //   } 
      // ]
      // return data
      let dataNew = airRunTime?airRunTime.map(ele=>{ 
        return {
          name:ele.menuName,
          value:ele.runTime,
          proportion:ele.runTimeRate,
        } 
      }):[]
      return dataNew
    },
    getAirConditionerCake(data = []){
      let myChart1 = this.$echarts.init(document.getElementById("airConditionerCake"));
      // let total = data.reduce((a,b)=>a+b.value,0)
      if(data.length){
        myChart1.setOption({
          backgroundColor: '#fff',
          color: ['#A0B8F6', '#7BE188', '#FBAF1B'],
          title: [  ],
          legend: {
            type: 'scroll',  // 开启滚动功能
            scrollDataIndex: 0,  // 设置需要滚动的系列的数据索引
            height: '30%',
            bottom: 20,
            left: 'center',
            orient: 'vertical',
            itemHeight: 8,
            itemWidth: 8,
            formatter: name => {
              let obj
              for (let i = 0; i < data.length; i++) {
                if (data[i].name == name) {
                  obj = data[i]
                }
              }
              var strs = name.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每五个字符增加换行符
              strs.forEach((item, index) => {
                if (index % 8 === 0 && index !== 0) {
                  str += '\n'
                }
                str += item
              })
              let arr = `{name|${str} : }{value|${obj.value}h}{proportion|占比 : ${obj.proportion}}`
              return arr
            },
            textStyle: {
              rich: {
                name: {
                  width:110,
                  fontSize: 14,
                  color: '#4E5969',
                  padding: [0, 20, 0, 0]
                },
                value: {
                  width:50,
                  fontSize: 14,
                  color: '#4E5969'
                },
                proportion: {
                  fontSize: 14,
                  color: '#4E5969',
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: '45%',
              center: ['50%', '30%'],
              data: data,
              labelLine: {
                show: false
              },
              label: {
                show: false,
                position: 'center'
              },
            }
          ]
        })
      }else{
        myChart1.setOption({
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        })
      }
      
    },
    getLineWidth(eqTypeArr,total,cMax,cMin){
      // let total = eqTypeArr.reduce((a,b)=>a+b.num,0)
      let str = ''
      eqTypeArr.forEach(ele => {
        let num = ((ele.num / total) * 100)
        // 最大值不得超过cMax   为其他元素留位置   最小值不得小于cMin
        let max = Math.max(num,cMin)
        let min = Math.min(max,cMax)
        str += `${min.toFixed(2)}% `
      });
      return str
    }
  },
  mounted(){
    this.$YBS.apiCloudEventKeyBack(this.goback)
    this.getAirRunTime()
    this.getAirRunRate()
    this.getAirViewDetail()
    this.getEntityMenuList()
  }
}
</script>

<style scoped lang="stylus">
.no_data{
  width: 100%
  height: 3rem
  display: flex
  justify-content: center
  align-items: center
  color: #999
}
.today_function{
  width: 100%;
  background: #FFFFFF
  .today_fun_title{
    padding: 16px;
    p{
      font-size: 16px;
      font-weight: 400;
      color: #1D2129;
    }
  }
  .eq_cumulative{
    height: 1.6rem
    display: flex
    flex-direction: column
    align-items: center
    justify-content: center
    p{
      font-size: 18px;
      font-weight: bold;
      color: #1D2129;
      margin-bottom: 8px;
    }
    span{
      font-size: 14px;
      font-weight: 400;
      color: #86909C;
    }
  }
  .eq_table{
    width: 100%;
    .eq_tr{
      width: 100%;
      display: flex
      .eq_th{
        flex: 1
        padding: 16px;
        font-size: 15px;
        font-weight: 400;
        color: #1D2129;
        background: #E6EFFC;
      }
      .eq_td{
        flex: 1
        padding: 16px;
        font-size: 15px;
        font-weight: 400;
        color: #4E5969;
        border-bottom: 1px solid #E5E6EB;
      }
    }
  }
}
.equipment{
  width: calc(100vw - 20px);
  height: 8rem;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px 10px 0 10px
  &_item{
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px
    margin-bottom: 10px
    .item_title{
      display: flex
      justify-content: space-between
      h3{
        font-size: 16px;
        font-weight: 400;
        color: #1D2129;
      }
      span{
        font-size: 14px;
        font-weight: 400;
        color: #3562DB;
      }
    }
    .total_num{
      font-size: 14px;
      font-weight: 400;
      color: #4E5969;
      margin-top: 16px;
      span{
        font-size: 18px;
        font-weight: bold;
        color: #1D2129;
        margin-left: 8px
      }
      span::after{
        content: '台'
        font-size: 13px;
        font-weight: 400;
        color: #86909C;
        margin-left: 4px
      }
    }
    .chart{
      display: grid;
      grid-gap:1px;
      grid-template-columns: auto auto auto auto
      .chart_line{
        width: 100%;
        .line{
          width: 100%;
          height: 10px;
          margin: 10px 0;
          // background: #00B42A;
        }
        .line_text{
          // min-width:100px;
          p{
            font-size: 14px;
            line-height: 25px
          }
          span{
            font-size: 14px;
            font-weight: bold;
            color: #1D2129;
          }
        }

      }
    }

  }
}
.air_title{
  height: 1.2rem;
  display: flex
  justify-content: space-between
  align-items: center
  border-radius: 8px 8px 8px 8px;
  padding: 0 16px;
  &_l{
    display: flex
    align-items: center
    p{
      font-size: 14px;
      font-weight: 400;
      margin-left: 5px;
    }
  }
  span{
    font-size: 18px;
    font-weight: bold;
  }
}
.inner{
  height: 100%;
  background-color: #F2F4F9;
}
</style>
