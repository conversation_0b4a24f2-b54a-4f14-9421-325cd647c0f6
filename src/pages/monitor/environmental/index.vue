<template>
  <div class="inner">
    <Header title="环境监测" @backFun="goback"></Header>
    <div class="content">
      <div v-for="gather in listData" :key="gather.name">
        <p class="title">{{gather.name}}（平均）</p>
        <van-row v-if="gather.name == '室外'" gutter="10" style="width: 100%;">
          <van-col :span="12" v-for="(item, index) in gather.list" :key="index+'outdoor'" style="margin-bottom: 10px;">
            <div class="outdoor">
              <div class="outdoor_item">
                <h3>{{ item.parameterName }}</h3>
                <div class="item_data">
                  <div class="data_num">
                    <p>{{ item.average }}</p>
                    <span>{{ item.unit }}</span>
                  </div>
                  <p class="tags danger" :style="{'background': item.colour}">{{ item.averageConfigName }}</p>
                </div>
              </div>
            </div>
          </van-col>
        </van-row>
        <div v-else class="outdoor indoor" v-for="(item, index) in gather.list" :key="index + 'indoor'">
          <div class="outdoor_item" style="width: 40%;" @click="goToDetails(1, item)">
            <h3>{{ item.parameterName }}</h3>
            <div class="item_data">
              <div class="data_num">
                <p>{{ item.average }}</p>
                <span>{{ item.unit }}</span>
              </div>
              <p class="tags danger" :style="{'background': item.colour}">{{ item.averageConfigName }}</p>
            </div>
          </div>
          <div v-if="item.exceptionInfoEntityList.length" class="btn_box" @click="goToDetails(0,item)">
            <div v-for="(alarm, i) in item.exceptionInfoEntityList" :key="i + 'environmentArr'" >
              <p class="g_btn" :style="{ 'color': alarm.colour }">{{ alarm.exceptionName }} {{ alarm.exceptionCount }}</p>
            </div>
          </div>
          <div v-else class="btn_box">
            <p class="g_btn" :style="{ 'color': '#00B42A' }">全部正常</p>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import { monitorTypeList } from "./../components/dict.js";
export default {
  data() {
    return {
      projectCode: monitorTypeList.find(item => item.projectName === '环境监测').projectCode,
      listData: []
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getSettingOverview()
  },
  methods: {
    goToDetails(type, item) {
      console.log(item)
      this.$router.push({
        path: '/environmentalDetails',
        query: {
          exception: type,
          paramId: item.parameterId,
          average: item.average,
          unit: item.unit
        }
      })
    },
    // 获取总览
    getSettingOverview() {
      let params = {
        projectCode: this.projectCode
      }
      this.$api.GetSettingOverview(params).then(res => {
        this.listData = res
      })
    },
    goback() {
      this.$YBS.apiCloudCloseFrame()
    }
  }
}
</script>

<style scoped lang="stylus">
.primary{
  background: #00B42A;
}
.warning{
  background: #FF7D00;
}
.danger{
  background: #F53F3F;
}
.tags{
  font-size: 12px;
  padding: 4px 6px;
  color: white;
  border-radius: 2px;
}
.btn_box{
  padding: 10px 0;
  margin-left: .4rem;
  display: flex;
  flex-direction: column;
  gap: 8px;
  .g_btn{
    display: inline-block;
    font-size: 12px;
    padding: 6px 12px;
    background: #F2F4F9;
    border-radius: 100px;
    // margin-bottom: 10px;
  }
}

.inner{
  height: 100%;
  background-color: #F2F4F9;
  display: flex;
  flex-direction: column;
}
.indoor{
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.outdoor{
  background: white;
  border-radius: 8px;
  .outdoor_item{
  padding: 16px;
  h3{
    font-size: 14px;
    color: #1D2129;
    margin-bottom: 15px;
  }
  .item_data{
    display: flex;
    justify-content: space-between;
    .data_num{
      display: flex;
      align-items: flex-end;
      p{
        font-size: 18px;
        font-weight: bold;
        color: #1D2129;
      }
      span{
        font-size: 13px;
        font-weight: 400;
        color: #86909C;
        margin-left: 4px;
      }
    }

  }
}
}

.content{
  padding: 10px;
  flex: 1;
  overflow: auto
  .title{
    font-size: 14px;
    color: #86909C;
    margin-bottom: 10px;
  }
}
</style>
