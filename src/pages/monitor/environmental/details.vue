<template>
  <div class="inner">
    <Header title="环境监测" @backFun="goback"></Header>
    <div class="content">
      <van-collapse v-model="activeCollapse" accordion>
        <van-collapse-item :title="item.configName" :name="item.configName" v-for="(item, index) in listData" :key="item.configName + index">
          <div class="collapse-content">
            <van-empty description="暂无数据" v-if="!item.dataList.length" />
            <template v-else>
              <div v-for="(v, i) in item.dataList" :key="i + 'item'" class="collapse-content-item">
                <p style="width: 30%">{{ v.paramTime }}</p>
                <p style="width: 40%">{{ v.paramName }}</p>
                <p style="width: 30%">{{ v.paramValue }}{{ unit }}</p>
              </div>
            </template>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
  </div>
</template>

<script>
import { monitorTypeList } from "./../components/dict.js";
export default {
  data() {
    return {
      projectCode: monitorTypeList.find((v) => v.projectName === "环境监测").projectCode,
      activeCollapse: [], // 已展开name集合
      listData: [],
      unit: "",
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.unit = this.$route.query.unit;
    this.getSettingDetails();
  },
  methods: {
    // 获取总览
    getSettingDetails() {
      let params = {
        paramId: this.$route.query.paramId,
        average: this.$route.query.average,
        exception: this.$route.query.exception,
        projectCode: this.projectCode,
      };
      this.$api.GetSettingDetails(params).then((res) => {
        this.listData = res;
      });
    },
    goback() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped lang="stylus">
.inner {
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .content {
    flex: 1;
    overflow: auto;

    /deep/ .van-collapse {
      .van-collapse-item {
        .van-cell__title {
          font-size: 16px;
          color: #212121;
          position: relative;
          padding-left: 12px;
        }

        .van-cell__title::before {
          content: '';
          position: absolute;
          height: 16px;
          width: 4px;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          background-color: #3562DB;
        }

        .van-collapse-item__content {
          background: #F2F4F9;
          padding: 8px 16px;
        }
      }
    }

    .collapse-content {
      .collapse-content-item {
        padding: 5px 0px;
        display: flex;

        p {
          font-size: 16px;
          font-weight: 400;
          color: #1D2129;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
