<template>
  <div class="UPSmonitor-view">
    <Header title="UPS运行监测" @backFun="goback"></Header>
    <div class="UPSmonitor-main">
      <div class="main-heade">
        <p class="heade-title">UPS主机状态</p>
        <div class="heade-list">
          <div class="chart" :style="{'grid-template-columns': getLineWidth(stateList,98,1)}">
            <div class="chart_line" v-for="(v,i) in stateList" :key="i + 'eqTypeArrA'">
              <div class="line" :style="{'background':v.color}"></div>
            </div>
          </div>
          <div class="chart" :style="{'grid-template-columns': getLineWidth(stateList,80,10, 'text')}">
            <div class="chart_line" v-for="(v,i) in stateList" :key="i + 'eqTypeArrB'">
              <div class="line_text">
                <p :style="{'color':v.color}">{{ v.name }}</p>
                <span>{{ v.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="main-list">
        <van-collapse v-if="listData.length" v-model="activeCollapse">
          <van-collapse-item
            :title="item.surveyEntityName"
            :name="item.surveyEntityCode"
            v-for="item in listData"
            :key="item.surveyEntityCode"
          >
            <template #value>
              <span
                class="collapse-value"
                :style="{ color: listStatusColor[item.status].color, background: listStatusColor[item.status].bagColor }"
              >
                {{ listStatusColor[item.status].name }}
              </span>
            </template>
            <div class="collapse-content">
              <div
                class="content-item"
                v-for="v in item.parameterList"
                v-show="v.parameterName != '充氧状态'"
                :key="v.scalaId"
              >
                <p class="item-label">{{ v.parameterName }}</p>
                <p
                  class="item-value"
                  :style="{ color: v.msg ? '#F53F3F' : '#1D2129' }"
                >
                  {{ v.parameterValue || "" }}{{ v.parameterUnit || "" }}
                  <span class="item-msg" v-if="v.msg">{{ v.msg }}</span>
                </p>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
        <div class="noData" v-else>
          <img src="../../../assets/images/noData_img.png">
          <p>数据为空</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { monitorTypeList } from "./../components/dict.js";
export default {
  name: "",
  data() {
    // const projectName = "医用气体";// 开发暂用
    const projectName = "UPS监测";
    return {
      projectName,
      projectCode: monitorTypeList.find(
        item => item.projectName === projectName
      ).projectCode,
      activeCollapse: [], // 已展开name集合
      listData: [],
      stateList: [
        { value: 0, name: "正常", color: "#00B42A" },
        { value: 0, name: "离线", color: "#FF7D00" },
        { value: 0, name: "异常", color: "#F53F3F" }
      ],
      listStatusColor: {
        0:{
          name:'正常',
          color:'#00B42A',
          bagColor: '#E8FFEA'
        },
        10:{
          name:'报警',
          color:'#F53F3F',
          bagColor: '#FFECE8'
        },
        6:{
          name:'离线',
          color:'#4E5969',
          bagColor: '#F2F3F5'
        }
      }
    };
  },
  computed: {},
  created() {

    this.getRealMonitor()
    this.getSurveyStatus()
  },
  methods: {
    getSurveyStatus(){
      this.$api.getSurveyStatusByImhCode({projectCode:this.projectCode}).then(res=>{
        this.stateList[0].value = res.normalCount
        this.stateList[1].value = res.offLineCount
        this.stateList[2].value = res.faultCount
      })
    },
    getRealMonitor() {
      let data = {
        entityMenuCode: null,
        isHistory: 0,
        page: 1,
        pageSize: 1000,
        projectCode: this.projectCode
      }
      $.showLoading();
      // this.$api.getEntityMenuList({projectId: this.projectCode}).then(res=>{
        // data.entityMenuCode = res[0].code
        this.$api.getRealMonitoring(data).then(res=>{
          $.hideLoading();
          this.listData = res.list || []
        })
      // })
    },
    goback() {
      this.$YBS.apiCloudCloseFrame()
    },
    getLineWidth(eqTypeArr,cMax,cMin, type){
      let total = eqTypeArr.reduce((a,b)=>{
        if (b.value === 0 && type) {
          return a+1
        }
        return a+b.value
      },0)
      let str = ''
      eqTypeArr.forEach(ele => {
        let num = ((ele.value / total) * 100)
        // 最大值不得超过cMax   为其他元素留位置   最小值不得小于cMin
        let max = Math.max(num,cMin)
        let min = Math.min(max,cMax)
        str += `${min.toFixed(2)}% `
      });
      return str
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  }
};
</script>

<style lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';
.UPSmonitor-view{
  height: 100%;
  background-color: $bgColor;
  display: flex;
  flex-direction: column;
  .UPSmonitor-main{
    flex: 1;
    display: flex;
    flex-direction: column;
    .main-heade{
      height: 2.56rem;
      background: #FFFFFF;
      border-radius: .16rem;
      margin: $marginb;
      padding: .32rem;
      box-sizing: border-box;
      .heade-title{
        font-size: $titleFontSize;
        color: #1D2129;
        margin-bottom: .2rem;
        line-height: .44rem;
      }
      .heade-list{
        width: 100%;
        .chart{
          display: grid;
          grid-gap:1px;
          grid-template-columns: auto auto auto auto
          .chart_line{
            width: 100%;
            .line{
              width: 100%;
              height: 10px;
              margin: 10px 0;
              // background: #00B42A;
            }
            .line_text{
              // min-width:100px;
              p{
                font-size: .28rem;
                line-height: 25px
              }
              span{
                font-size: .28rem;
                color: #1D2129;
              }
            }

          }
        }
        // .list-item{
        //   height: .2rem;
        //   margin-right: 1px;
        //   position: relative;
        // }
        // .item-name{
        //   font-size: .28rem;
        //   position: absolute;
        //   line-height: .4rem;
        //   top: .4rem;
        // }
        // .item-value{
        //   font-size: .28rem;
        //   position: absolute;
        //   line-height: .4rem;
        //   top: .88rem;
        // }
      }
    }
    .main-list{
      flex: 1;
      background: #fff;
      .collapse-value{
        font-size: 12px;
        margin-right: 10px;
        font-size: 12px;
        padding: 3px 6px;
        border-radius: 2px;
      }
      .collapse-content{
        .content-item{
          display: flex;
          padding-top: .2rem;
          align-items: center;
          position: relative;
          .item-label{
            width: 1.8rem;
            font-size: .3rem;
            font-weight: 500;
            color: #4E5969;
          }
          .item-value{
            padding-left: .2rem;
            font-size: .28rem;
            color: #1D2129;
          }
          .item-msg{
            margin-left: .08rem;
            font-size: .26rem;
          }
          .item-checkDetails{
            width: 50px;
            text-align: right;
            position: absolute;
            right: 0;
            font-size: 14px;
            font-weight: 500;
            color: #888888;
          }
        }
      }
      .noData{
        padding: 100px 0px 0px 0px ;
        text-align: center;
        color: #999999;
        img {
          width: 180px;
          height: 130px;
        }
        p{
          font-size: 15px;
          font-weight: 300;
          color: #4E5969;
          line-height: 21px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
  .van-collapse{
    .van-collapse-item{
      .van-cell--clickable{
        align-items: center;
      }
      .van-cell__title{
        font-size: .32rem;
        color: #212121;
        position: relative;
        padding-left: .24rem;
      }
      .van-cell__title::before {
        content: '';
        position: absolute;
        height: .32rem;
        width: .08rem;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        background-color: #3562DB;
      }
      .van-collapse-item__content{
        background: #F2F4F9;
        padding: .16rem .4rem .36rem .72rem;
      }
    }
  }
</style>
