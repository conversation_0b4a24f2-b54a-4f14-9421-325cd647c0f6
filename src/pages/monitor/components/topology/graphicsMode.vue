<template>
  <div ref="topology" class="scada-preview">
    <topology preview="true" :data="data" @mousewheel.prevent width="300" height="700" />
    <div v-if="showTools" class="tools">
      <!-- <div class="screen-img over-turn"></div> -->
      <div class="screen-img scale-cut" @click="scaleTo(1)"></div>
      <van-slider
        v-model="sliderVal"
        :min="1"
        :max="50"
        @change="sliderChange"
      />
      <div class="screen-img scale-add" @click="scaleTo(50)"></div>
      <!-- <el-button type="primary" @click="onSizeWindow"> 适合窗口 </el-button> -->
    </div>
    <!-- <div class="tools" @click="onSizeWindow">适合窗口</div> -->
    <!-- <div class="tools" @click="changeScreen">切换视角</div> -->
    <!-- <el-empty :image-size="200"></el-empty> -->
  </div>
</template>

<script>
import Vue from "vue";
// import "@/assets/styles/topology-vue.scss";
// import Topology from "topology-vue";
import { Toast } from "vant";
// Vue.use(Topology);
export default {
  name: "scadaShow",
  beforeRouteLeave(to, from, next) {
    // 离开当前页面关闭webstocket连接
    window.topology.closeSocket();
  },
  props: {
    projectId: {
      type: String,
      default: ""
    },
    entityMenuCode: {
      type: String,
      default: ""
    },
    surveyEntityCode: {
      type: String,
      default: ""
    },
    scadaType: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      data: {},
      showTools: true,
      sliderVal: 1
    };
  },
  created() {},
  mounted() {
    // this.changeScreen()
    console.log("mounted");
    // 图纸类型 剖面图还是普通图纸
    // if (this.scadaType == 'profile') {
    //   this.getSurveyImgByCode()
    // } else {
    // 暂时无剖面图数据
    this.getScadaList();
    // }
    this.webstocket();
  },
  beforeDestroy() {
    // console.log('销毁')
    window.topology.closeSocket();
  },
  methods: {
    // 实时监测--scada
    getScadaList() {
      this.$api
        .getScadaList({
          projectId: this.projectId,
          menuCode: this.entityMenuCode
        })
        .then(res => {
          if (res.length > 0) {
            // this.showTools = true
            const scadaJsonData = res[0].isiJsonStr || "";
            this.handleScadaChange(scadaJsonData);
          } else {
            $.toast("暂无数据", "text");
            //   this.$message.warning('暂无数据')
          }
        });
    },
    // 剖面图数据
    getSurveyImgByCode() {
      this.$api
        .getSurveyImgByCode({
          projectCode: this.projectId,
          surveyCode: this.surveyEntityCode
        })
        .then(res => {
          if (res.code == 200) {
            this.showTools = true;
            const scadaJsonData = res.data.isiJsonStr;
            this.handleScadaChange(scadaJsonData);
          } else {
            // Toast.warning("暂无数据");
            $.toast("暂无数据", "text");
          }
        });
    },
    // 选择scada
    handleScadaChange(val) {
      const updataList = JSON.parse(val);
      // console.log('updataList==========',updataList);
      // let canvas = document.getElementsByTagName('canvas')[0];
      // let h = canvas.height
      // let w = canvas.width
      // updataList.pens.forEach(ele=>{
      //   ele.rotate = 90
      //   ele.rect.x = ele.evs.y
      //   ele.rect.y = ele.evs.x
        // let ox = ele.rect.x / w
        // let nx = Math.round(canvas.height * ox)
        // let oy = ele.rect.y / h
        // let ny = Math.round(canvas.width * oy)
        // ele.rect.x = w - ny
        // ele.rect.y = nx

        // let x = ele.rect.x
        // ele.rect.x = ele.rect.y + ele.rect.height * 2
        // ele.rect.y = x - ele.rect.height + 20
      // })
      this.data = updataList;
      setTimeout(() => {
        this.onSizeWindow();
      }, 200);
      // console.log(window.topology.canvas);
      // window.topology.fitView(16);
      // console.log(this.data);
    },
    changeScreen() {
      // console.log(window.topology.data.scale);
      // window.topology.scaleTo(2)
      console.log(window.topology.data.scale);
      // window.TopologyMenu.onScale(1000);
      // setTimeout(() => {
      //   this.onSizeWindow();
      // }, 200);
      // window.topology.changeScreen();
      // const body = document.getElementsByClassName('scada-preview')[0];
      // const le5le = document.getElementsByClassName('le5le-topology')[0];
      // const width = body.clientWidth;
      // const height = body.clientHeight;
      // le5le.style.width = height + "px";
      // le5le.style.height = width + "px";
      // le5le.style.transformOrigin = '0 0';
      // le5le.style.transform = 'rotateZ(90deg) translateY(-100%)';
      // window.topology.overflow()
      // const canvas = window.topology.canvas;
      // var ctx = canvas.canvas.getContext('2d')
      // console.log(ctx);
      // canvas宽高度互换
      // const width = canvas.width;
      // const height = canvas.height;
      // canvas.width = height;
      // canvas.height = width;
      // ctx.scale(-1, 1);
      // ctx.translate(canvas.width / 2, canvas.height / 2);
      // ctx.rotate(90 * Math.PI / 180)
      // ctx.translate(canvas.width / 2, canvas.height / 2);
      // ctx.rotate(90 * Math.PI / 180);
      // ctx.translate(-canvas.height / 2, -canvas.width / 2);
      // console.log(canvas.width, canvas.height);
    },
    // 放大缩小视角
    sliderChange() {
      window.topology.scaleTo(this.sliderVal / 10);
    },
    webstocket() {
      window.topology.openSocket();
    },
    onSizeWindow() {
      window.topology.fitView(16);
      this.sliderVal = window.topology.data.scale * 10;
    },
    scaleTo(val) {
      this.sliderVal = val;
      this.sliderChange();
    }
  }
};
</script>
<style lang="scss" scoped>
.scada-preview {
  position: relative;
  height: calc(100% - 0px);
  width: 100%;
  // transform-origin: 0 0;
  // transform: rotateZ(90deg) translateY(-100%);
  .tools {
    position: absolute;
    bottom: 0;
    height: 80px;
    width: 100%;
    background: #fff;
    padding: 8px 25px 28px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .screen-img {
      width: 20px;
      height: 20px;
    }
    /deep/ .van-slider {
      width: calc(100% - 35px * 3);
    }
    .over-turn {
      background: url("~@/assets/images/monitor/over-turn.png") no-repeat;
      background-size: 100% 100%;
    }
    .scale-cut {
      background: url("~@/assets/images/monitor/scale-cut.png") no-repeat;
      background-size: 100% 100%;
    }
    .scale-add {
      background: url("~@/assets/images/monitor/scale-add.png") no-repeat;
      background-size: 100% 100%;
    }
  }
  .menus-list {
    position: absolute;
    top: 28px;
    left: 20px;
  }
}
</style>
