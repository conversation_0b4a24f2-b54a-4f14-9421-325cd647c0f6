<template>
  <div class="pol_item" v-if="item">
    <div class="pol_title">
      <span>{{item.alarmObjectName}}</span>
      <div class="pol_title_icon">
        <p 
          :style="{'background':alarmStatusList[item.alarmStatus].background,'color':alarmStatusList[item.alarmStatus].color}"
        >{{alarmStatusList[item.alarmStatus].name}}</p>
        <van-icon name="arrow" color="#C9CDD4" />
      </div>
    </div>
    <div class="pol_row">
      <p>报警时间</p>
      <span>{{formatDateMMDDHHmm(item.alarmStartTime)}}</span>
    </div>
    <div class="pol_row">
      <p>报警地点</p>
      <span>{{item.alarmSpaceName}}</span>
    </div>
    <div class="pol_row">
      <p>报警类型</p>
      <span>{{item.alarmDetails}}</span>
    </div>
    <div class="pol_row">
      <p>警情级别</p>
      <div class="pol_tab" :style="{'background':polLevelList[item.alarmLevel].color}">{{polLevelList[item.alarmLevel].name}}</div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  data(){
    return{
      // 报警处置状态(0.未处理 1.处理中 2.已关闭)
      alarmStatusList:{
        0:{name:'未处理',color:'#F53F3F',background:'#FFECE8'},
        1:{name:'处理中',color:'#FF7D00',background:'#FFF7E8'},
        2:{name:'已关闭',color:'#4E5969',background:'#F2F3F5'},
      },
      // 	报警级别(0:通知 1:一般 2:紧急 3:重要)
      polLevelList:{
        3:{name:'重要',color:'#F53F3F'},
        2:{name:'紧急',color:'#F7BA1E'},
        1:{name:'一般',color:'#3562DB'},
        0:{name:'通知',color:'#3562DB'},
      }
    }
  },
  props:{
    item: {
      type:Object,
      default:()=>{
        return {}
      }
    }
  },
  methods:{
    formatDateMMDDHHmm(date){
      return moment(date).format("MM-DD HH:mm");
    }
  },
}
</script>

<style scoped lang="stylus">
.pol_item{
  padding: 20px 16px;
  background: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 10px;
  .pol_title{
    display: flex;
    justify-content: space-between;
    align-items: center
    span{
      font-size: 16px;
      font-weight: 400;
      color: #1D2129;
    }
    &_icon{
      display: flex
      align-items: center
      p{
        width: 48px;
        height: 24px;
        
        border-radius: 2px;
        font-size: 12px;
        font-weight: 500;
        
        display: flex;
        justify-content: center
        align-items: center
        margin-right: 10px;
      }
      .handle{
        background: #F2F3F5;
        color: #4E5969;
      }
      .no_handle{
        background: #FFECE8;
        color: #F53F3F;
      }
    }
  }
  .pol_row{
    display: flex
    align-items: flex-start;
    margin-top: 10px;
    p{
      font-size: 16px;
      font-weight: 400;
      color: #4E5969;
      margin-right: 25px;
      line-height: 22px;
    }
    span{
      flex: 1
      font-size: 16px;
      line-height: 22px;
      font-weight: 400;
      color: #1D2129;
    }
    .pol_tab{
      width: 36px;
      height: 24px;
      // background: #F53F3F;
      border-radius: 2px;
      font-size: 12px;
      font-weight: 500;
      color: #FFFFFF;
      display: flex
      justify-content: center
      align-items: center
    }
  }
}
</style>