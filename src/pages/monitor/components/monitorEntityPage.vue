<!--
 * @Author: hedd
 * @Date: 2023-05-16 11:24:35
 * @LastEditTime: 2023-05-22 18:55:43
 * @FilePath: \ybs_h5\src\pages\monitor\components\monitorEntityPage.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header title="运行监测" @backFun="goback"></Header>
    <van-tabs
      v-model="active"
      sticky
      offset-top="10vh"
      title-inactive-color="#86909C"
      title-active-color="#1D2129"
      color="#3562DB"
      animated
      line-height="3px"
    >
      <van-tab
        title="scada"
        name="scada"
        :title-style="{ fontSize: '0.32rem' }"
      >
        <graphics-mode
          v-if="active == 'scada'"
          ref="scadaShow"
          :entityMenuCode="queryData.entityMenuCode"
          :projectId="queryData.projectCode"
        />
      </van-tab>
      <van-tab title="列表" name="list" :title-style="{ fontSize: '0.32rem' }">
        <monitor-entity-list v-if="active == 'list'" :queryData="queryData"/>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import graphicsMode from "./topology/graphicsMode";
import monitorEntityList from './monitorEnity/monitorEntityList.vue'
export default {
  components: {
    graphicsMode,
    monitorEntityList
  },
  data() {
    return {
      active: "scada",
      queryData: {},
    };
  },
  mounted() {
    this.queryData = this.$route.query;
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    goback() {
      this.$router.go(-1);
    }
  }
};
</script>
<style scoped lang="scss">
.inner {
  height: 100%;
  background-color: #f2f4f9;
  /deep/ .van-tabs {
    height: calc(100% - 10vh);
    .van-tabs__content {
      height: calc(100% - 44px);
      .van-tab__pane {
        height: 100%;
        overflow-y: auto;
        // border-top: 0.2rem solid #f2f4f9;
        // padding: 0 10px;
      }
    }
  }
}
</style>
