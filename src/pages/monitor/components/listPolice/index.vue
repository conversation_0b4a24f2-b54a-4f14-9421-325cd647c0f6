<template>
  <van-pull-refresh
    v-model="refreshing"
    @refresh="onRefresh"
    success-text="刷新成功"
    :class="isTabs ? 'pull-refresh' : 'pull-refresh-notabs'"
  >
    <van-list
      v-if="list.length"
      style="padding: 10px;"
      v-model="listLoading"
      :error.sync="error"
      error-text="请求失败，点击重新加载"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div v-for="item in list" :key="item.id+'pol'">
        <slot :item="item"></slot>
      </div>
    </van-list>
    <ybsEmpty v-else imageType="searchEmpty" style="height: 100%" />
    <!-- <div v-else class="centered">
      <van-empty
        class="custom-image"
        :image="dataNull"
        description="数据为空"
      />
    </div> -->

  </van-pull-refresh>
</template>

<script>
import dataNull from '@/assets/images/equipmentManagement/缺省-图表@2x.png'
export default {
  data(){
    return{
      dataNull,
      pageNo:1,
      // 下拉状态
      refreshing:false,
      // 是否处于加载状态，加载过程中不触发load事件
      listLoading:false,
      // 是否已加载完成，加载完成后不再触发load事件
      finished:false,
      error:false,
      list:[]
    }
  },
  props:{
    // 传入请求接口
    requestApi: {
      type: Function,
      require: true,
      default: false
    },
    // 分页数量
    pageSize: {
      type: Number,
      default: 15
    },
    // 请求参数
    queryParams: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    isTabs: {
      type: Boolean,
      default: false
    }
  },
  mounted(){
    this.getList()
  },
  methods:{
    // 到达底部
    onLoad(){
      this.getList()
    },
    // 下拉刷新
    onRefresh(){
      this.pageNo = 1
      this.getList('refresh')
    },
    // 请求数据
    getList(type){
      let data = {
        ...this.queryParams,
        pageSize:this.pageSize,
        pageNo:this.pageNo
      }
      this.requestApi(data).then(res=>{
        if(res.records){
          //  下拉刷新
          if(type == 'refresh'){
            this.list = []
            this.finished = false
            this.refreshing = false
          }
          this.list = [...this.list,...res.records]
          if(this.pageNo >= res.pages){
            this.finished = true
          }else{
            this.pageNo++
            this.listLoading = false
          }
        }else{
          this.listLoading = false
          this.error = true
        }
      },err=>{
        this.listLoading = false
        this.refreshing = false
        this.error = true
      })
    }
  }
}
</script>

<style lang="stylus" scoped>
.pull-refresh-notabs {
  height: 90vh;
  overflow-y: auto;
}
.pull-refresh {
  height: calc(90vh - 44px);
  overflow-y: auto;
}
.centered{
  width: 100%;
  height: 90vh;
  display: flex;
  justify-content: center;
  align-content: center;
}
</style>
