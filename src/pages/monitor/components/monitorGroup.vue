<!--
 * @Author: hedd
 * @Date: 2023-05-12 16:38:07
 * @LastEditTime: 2023-05-18 16:39:55
 * @FilePath: \ybs_h5\src\pages\monitor\components\monitorGroup.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header :title="monitorTypeList.find(item => item.projectCode === projectCode).projectName" @backFun="goback"></Header>
    <p class="describe">查看运行系统图</p>
    <van-cell style="padding: 16px;" v-for="item in list" :key="item.code" @click="jumpMointorEntityListPage(item.code)">
      <template slot="default">
        <span style="font-size: 16px;">{{ item.name }}</span>
      </template>
    </van-cell>
  </div>
</template>

<script>
import { monitorTypeList } from "./../components/dict.js";
export default {
  data() {
    return {
      monitorTypeList,
      list: [],
      projectCode: ''
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getGroupList();
  },
  methods: {
    // 通过一级分组查询二级分组
    getGroupList() {
      console.log(this.$route.query);
      const queryData = this.$route.query;
      this.projectCode = queryData.projectCode
      const params = {
        projectCode: queryData.projectCode,
        entityMenuCode: queryData.entityMenuCode
      }
      this.$api.getChildMenuCodesByProjectCode(params).then(res => {
        console.log(res)
        this.list = res.map(e => {
          return {
            code: e.iemCode,
            id: e.iemId,
            name: e.iemName
          }
        })
      })
    },
    // 跳转至分组对应监测实体列表页
    jumpMointorEntityListPage(code) {
      this.$router.push({
        path: '/monitorEntityPage',
        query: {
          projectCode: this.projectCode,
          entityMenuCode: code
        }
      })
    },
    goback() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped lang="stylus">
.ope_content{
  background: white;
  padding: 0 16px;
}
.describe{
  font-size: 14px;
  font-weight: 400;
  color: #86909C;
  padding: 8px 16px;
}

.inner{
  height: 100%;
  background-color: #F2F4F9;
}
</style>
