<template>
  <div class="inner">
    <Header :title="dataObj.name" @backFun="goback"></Header>
    <div class="content_page">
      <van-grid :column-num="2" :gutter="10" :border="false" :center="false" style="margin:10px 0;">
        <van-grid-item v-for="item in dataObj.list" :key="item.name">
          <template slot="default">
            <div class="grid_box">
              <h3>{{ item.name }}</h3>
              <div class="box_content">
                <img :src="item.icon" alt="">
                <div class="box_content_r">
                  <p>{{ item.count }}</p>
                  <span>{{ item.company }}</span>
                </div>
              </div>
              <div v-if="type == 2" style="margin-top: 20px;">
                <van-progress :show-pivot='false' :track-color="'transparent'" :stroke-width="8" :percentage="item.progress" :color="item.color" />
              </div>
            </div>
          </template>
        </van-grid-item>
      </van-grid>
      <van-tabs v-model="active" color="#3562DB" class="my_vant_tabs_cls">
        <van-tab
          v-for="item in dataObj.tabList"
          :key="item.id"
          :title="item.name"
          :name="item.id">
          <div class="tabs_content">

            <!-- <div class="function_chart" v-if="item.id">
              <div class="chart_name">
                <img src="@/assets/images/monitor/imageIcon.png" alt="">
                <p>运行系统图</p>
              </div>
              <span @click="jupmGroupListPage(item.entityMenuCode)">查看</span>
            </div> -->

            <div class="grid_err">
              <div class="err_item bag-grey" v-if="type!=1">
                <img src="@/assets/images/monitor/icon1.png" alt="">
                <p class="color-grey">离线</p>
                <span class="color-grey">{{ item.offLine }}</span>
              </div>
              <div class="err_item bag-orange">
                <img src="@/assets/images/monitor/icon2.png" alt="">
                <p class="color-orange">故障</p>
                <span class="color-orange">{{ item.fault }}</span>
              </div>
              <div class="err_item bag-orange">
                <img src="@/assets/images/monitor/icon3.png" alt="">
                <p class="color-orange">报警</p>
                <span class="color-orange">{{ item.callThePolice }}</span>
              </div>
            </div>

            <div class="grid_err" v-if="type!=1">
              <div class="err_item bag-blue">
                <img src="@/assets/images/monitor/icon4.png" alt="">
                <p>低液位</p>
                <span>{{ item.lowLiquidLevel }}</span>
              </div>
              <div class="err_item bag-blue">
                <img src="@/assets/images/monitor/icon5.png" alt="">
                <p>高液位</p>
                <span>{{ item.highLiquidLevel }}</span>
              </div>
            </div>

            <div class="equipment_list">
              <div class="equipment_item" v-for="ele in item.arr" :key="ele.equipmentName">
                <p>{{ ele.equipmentName }}</p>
                <div class="item_bot">
                  <div class="count">
                    <span style="color:#30B75E;">{{ ele.normal }}</span>
                    <span>/{{ ele.all }}</span>
                  </div>
                  <div class="repair">
                    <img src="@/assets/images/monitor/icon6.png" alt="">
                    <span>{{ ele.repair }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataObj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    projectCode: {
      type: String,
      default: ''
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      active: '',
    }
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame()
    },
    // 分组列表页
    jupmGroupListPage(entityMenuCode) {
      this.$router.push({
        path: '/monitorGroup',
        query: {
          projectCode: this.projectCode,
          entityMenuCode: entityMenuCode
        }
      })
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  }
}
</script>

<style scoped lang="stylus">
// $color-grey = #86909C
// $color-orange = #F53F3F
// $bag-grey = #F2F3F5
// $bag-orange = #FFECE8
// $bag-blue = #E6EFFC
.color-grey{
  color: #86909C;
}
.color-orange{
  color: #F53F3F;
}
.bag-grey{
  background-color: #F2F3F5;
}
.bag-orange{
  background-color: #FFECE8;
}
.bag-blue{
  background-color: #E6EFFC;
}
.inner{
  height: 100%;
  background-color: #F2F4F9;
}
.grid_box{
  padding: 0 8px;
  h3{
    font-weight: 400;
    margin-bottom: .3rem
  }
  .box_content{
    display: flex;
    justify-content: space-between;
    align-items: center;
    img{
      width: 40px;
      height: 40px;
    }
  }
  .box_content_r{
    display: flex;
    align-items: center;
    p{
      font-size: 18px;
      font-weight: bold;
    }
    span{
      font-size: 12px;
      color: #86909C;
      margin-left: 5px;
    }
  }
}
.content_page{
  height: 90vh;
  display: flex
  flex-direction: column
}
.my_vant_tabs_cls{
  flex: 1;
  display: flex
  flex-direction: column
  /deep/ .van-tabs__content{
    flex: 1
    .van-tab__pane{
      height: 100%

    }
  }
}
.tabs_content{
  background: #FFFFFF;
  padding: 15px;
  height: calc(100% - 30px);
  .function_chart{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    .chart_name{
      display: flex;
      align-items: center
      img{
        width: 17px;
        height: 17px;
        margin-right: 5px
      }
      p{
        font-size: 15px;
      }
    }
    span{
      font-size: 15px;
      color: #3562DB;
    }
  }
  .grid_err{
    // margin: 0px 0 10px 0;
    margin-bottom: 10px;
    display: flex;
    grid-gap: 16px;
    .err_item{
      height: 1.2rem;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      p{
        font-size: 14px;
        // color: #F53F3F;
        margin-left: 5px;
        margin-right: 15px;
      }
      span{
        font-size: 18px;
        font-weight: bold;
        // color: #F53F3F;
      }
    }
  }
  .equipment_list{
    width: 100%;
    display: flex;
    grid-gap: 16px;
    flex-wrap: wrap;
    .equipment_item{
      flex: 1;
      min-width: ceil(50% - 16px)
      padding: 16px;
      background: #FFFFFF;
      border-radius: 8px;
      border: 1px solid #E5E6EB;
      p{
        font-size: 14px;
      }
      .item_bot{
        margin-top: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between
        .count{
          display: flex;
          font-size: 18px;
          font-weight: bold;
        }
        .repair{
          display: flex;
          align-items: center
          padding: 2px 5px;
          border-radius: 100px;
          border: 1px solid #F53F3F;
          color: #F53F3F;
          font-size: 12px;
          font-weight: 500;
          img{
            margin-right: 5px;
          }
        }
      }
    }

  }
}
</style>
