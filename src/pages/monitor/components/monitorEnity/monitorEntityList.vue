<!--
 * @Author: hedd
 * @Date: 2023-05-16 11:24:35
 * @LastEditTime: 2024-01-02 15:51:57
 * @FilePath: \ybs_h5\src\pages\monitor\components\monitorEnity\monitorEntityList.vue
 * @Description:
-->
<template>
  <div class="content">
    <van-pull-refresh
      v-model="isLoading"
      class="listItem"
      @refresh="onRefresh"
    >
      <van-list
        v-if="entitylist.length > 0"
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="(item, i) in entitylist"
          :key="i"
          class="entity-list"
          @click="jumpMonitorEntityDetail(i)"
        >
          <div class="title">
            <span class="task-name">{{ item.surveyEntityName }}</span>
          </div>
          <div class="point">
            <div
              v-for="(parameter, j) in item.parameterList"
              :key="j"
              class="pointItem"
            >
              <!-- :class="{ w50: !parameter.slFlag, w100: parameter.slFlag }" -->
              <span class="pointTitle" v-trunced:slFlag="parameter">{{
                parameter.parameterName
              }}</span>
              <span class="count">{{
                parameter.parameterUnit
                  ? (parameter.parameterValue || "") + parameter.parameterUnit
                  : parameter.parameterValue
              }}</span>
            </div>
          </div>
        </div>
      </van-list>
      <ybsEmpty v-else imageType="searchEmpty" height="100%" />
    </van-pull-refresh>
  </div>
</template>
<script>
export default {
  props: {
    queryData: {
      type: Object,
      default: () => {}
    },
    // 默认根据分组 穿runList根据运行实体类型
    listType: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      isLoading: false,
      entitylist: [],
      loading: false,
      finished: false,
      pageParmes: {
        page: 1,
        pageSize: 10
      }
    };
  },
  mounted() {
    this.pageParmes.page = 1;
    this.entitylist = [];
    this.getListData();
  },
  methods: {
    jumpMonitorEntityDetail() {},
    // 下拉刷新
    onRefresh() {
      this.pageParmes.page = 1;
      this.finished = false;
      this.loading = true;
      this.entitylist = [];
      this.getListData();
    },
    // 加载更多
    onLoad() {
      this.pageParmes.page++;
      this.finished = false;
      this.loading = true;
      this.getListData();
    },
    // 获取列表数据
    getListData() {
      const params = {
        page: this.pageParmes.page,
        pageSize: this.pageParmes.pageSize,
        entityMenuCode: this.queryData.entityMenuCode,
        projectCode: this.queryData.projectCode
      };
      let apiName = "getRealMonitoringList";
      if (this.listType == 'runList') {
        Object.assign(params, {entityTypeId: this.queryData.entityTypeId});
        apiName = "getRealEnvMonitoringListApp";
      }
      this.$api[apiName](params).then(res => {
        this.entitylist = this.entitylist.concat(res.list || []);
        this.isLoading = false;
        this.finished =
          this.pageParmes.pageSize * this.pageParmes.page >= res.totalCount;
        this.loading = false;
      });
    }
  }
};
</script>
<style scoped lang="scss">
.content {
  height: 100%;
  .listItem {
    height: 100%;
    overflow: auto;
    /deep/ .van-list {
      // box-sizing: border-box;
      padding-top: 10px;
    }
  }
  .entity-list {
    width: calc(100% - 20px);
    padding: 0.4rem 0.32rem 0 0.32rem;
    border-radius: 8px;
    position: relative;
    font-size: 0.32rem;
    background-color: #fff;
    // margin-bottom: 10px;
    margin: 0 10px 10px 10px;
    box-sizing: border-box;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .task-name {
        color: #1d2129;
        font-weight: 600;
      }
    }
    .point {
      display: flex;
      flex-wrap: wrap;
      margin-top: 0.32rem;
      .pointItem {
        min-width: 50%;
        max-width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 0.32rem;
        .pointTitle {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #4e5969;
          margin-right: 0.48rem;
          padding: 0;
          border: 0;
        }
        .count {
          color: #1d2129;
        }
      }
      .w50 {
        width: 50%;
      }
      .w100 {
        width: 100%;
      }
    }
  }
}
</style>
