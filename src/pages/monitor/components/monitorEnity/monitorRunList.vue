<!--
 * @Author: hedd
 * @Date: 2023-05-12 16:38:07
 * @LastEditTime: 2023-05-18 19:31:23
 * @FilePath: \ybs_h5\src\pages\monitor\components\monitorEnity\monitorRunList.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header title="运行清单" @backFun="goback"></Header>
    <div class="box-content">
      <monitor-entity-list :queryData="$route.query" listType="runList"/>
    </div>
  </div>
</template>

<script>
import monitorEntityList from './monitorEntityList.vue'
export default {
  components: {
    monitorEntityList
  },
  data() {
    return {
      queryData: {}
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.queryData = this.$route.query;
  },
  methods: {
    goback() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped lang="stylus">
.inner{
  height: 100%;
  background-color: #F2F4F9;
  .box-content {
    height: calc(100% - 10vh);
  }
}
</style>
