<template>
  <div class="step_box">
    <div class="step_list">
      <div
        v-show="stepList.length"
        class="step_item"
        v-for="(item, index) in stepList"
        :key="index + 'id'"
      >
        <div class="icon_left">
          <img
            v-if="index == 0"
            src="@/assets/images/monitor/icon_start.png"
            alt=""
          />
          <img
            v-else-if="index == stepList.length - 1"
            src="@/assets/images/monitor/icon_end.png"
            alt=""
          />
          <img v-else src="@/assets/images/monitor/icon_content.png" alt="" />
          <p class="vertical" v-if="index != stepList.length - 1"></p>
        </div>
        <div class="step_right">
          <div class="step_time">{{ item.time }}</div>
          <p>{{ item.imsName }}</p>
          <div class="step_gprs">
            <img src="@/assets/images/monitor/gprs.png" alt="" />
            <span>{{ item.regionName }}</span>
          </div>
        </div>
      </div>
      <ybsEmpty v-show="!stepList.length" imageType="searchEmpty" height="100%" />
      <!-- <div v-show="!stepList.length" class="centered">
        <van-empty
          class="custom-image"
          :image="dataNull"
          description="数据为空"
        />
      </div> -->
    </div>
  </div>
</template>

<script>
import dataNull from "@/assets/images/equipmentManagement/缺省-图表@2x.png";
export default {
  data() {
    return {
      dataNull
    };
  },
  props: {
    stepList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  }
};
</script>

<style scoped lang="stylus">
.centered{
  width: 100%;
  height: 90vh;
  display: flex;
  justify-content: center;
  align-content: center;
}
.step_item{
  display: flex;
  .icon_left{
    display: flex;
    flex-direction: column
    align-items: center;
    p{
      width: 1px;
      flex: 1
      background: #E5E6EB;
    }
  }
  .step_right{
    margin-left: 12px;
    padding-bottom: 20px
    .step_time{
      font-size: 14px;
      font-weight: 400;
      color: #3562DB;
      margin-top: 4px;
    }
    p{
      font-size: 16px;
      font-weight: bold;
      color: #1D2129;
      margin: 10px 0 5px
    }
    .step_gprs{
      display: flex
      align-items: center
      img{
        margin-right: 5px;
      }
      span{
        font-size: 15px;
        font-weight: 400;
        color: #4E5969;
      }
    }
  }
}
.step_box{
  padding: 10px;
  background: white;
  box-sizing: border-box;
  height: 100%;
  .step_list{
    padding: 16px;
    box-sizing: border-box;
    height: 100%;
  }
}
</style>
