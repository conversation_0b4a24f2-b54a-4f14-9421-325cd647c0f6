<!--
 * @Author: hedd
 * @Date: 2023-05-19 13:50:45
 * @LastEditTime: 2023-05-23 15:40:20
 * @FilePath: \ybs_h5\src\pages\monitor\components\abnormalList\abnormalList.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header :title="pageData.title" @backFun="goback"></Header>
    <div class="my_step">
      <StepDetailed :stepList="list" />
    </div>
  </div>
</template>

<script>
import StepDetailed from "../stepDetailed/index.vue";
export default {
  components: {
    StepDetailed,
  },
  data(){
    return {
      pageData: {},
      queryData: {},
      list:[],
      abnormalDict: {
        offLine: {
          title: '离线清单',
          field: 'status',
          status: 6
        },
        fault: {
          title: '故障清单',
          field: 'status',
          status: 2
        },
        highLevel: {
          title: '高液位清单',
          field: 'paramStatus',
          paramStatus: 1
        },
        lowLevel: {
          title: '低液位清单',
          field: 'paramStatus',
          paramStatus: 2
        }
      }
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.queryData = this.$route.query;
    this.pageData = this.abnormalDict[this.queryData.abnormalType]
    this.getListData()
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    // 获取列表数据
    getListData() {
      const params = {
        projectCode: this.queryData.projectCode,
        entityMenuCode: this.queryData.entityMenuCode,
        [this.pageData.field]: this.pageData[this.pageData.field]
      }
      this.$api.getSelectStatusList(params).then(res => {
        this.list = res
      })
    }
  }
}
</script>

<style scoped lang="stylus">
.my_step{
  height: 90vh
  background: white;
}
.inner{
  height: 100%;
  background-color: #F2F4F9;
}
</style>
