<template>
  <div class="inner">
    <Header :title="title" @backFun="goback"></Header>
    <van-row gutter="10" style="padding: 16px 10px; background: white">
      <p class="alarm_statistics">报警统计</p>
      <van-col span="8" v-for="(item, index) in totalList" :key="index + 'totalList'" style="background: #f7f8fa">
        <div class="air_title">
          <div class="air_title_content" :style="index == 0 ? 'border-right: 1px solid #E5E6EB;' : ''">
            <span :style="{ color: item.textColor }">{{ item.num }}</span>
            <p>{{ item.name }}</p>
          </div>
        </div>
      </van-col>
    </van-row>

    <div class="equipment">
      <div class="equipment_item" v-for="(item, index) in tabList" :key="index + 'equipmentArr'" @click="jupmGroupListPage(item.menuCode)">
        <div class="item_title">
          <h3>{{ item.menuName }}</h3>
          <van-icon name="arrow" style="color: #c9cdd4" />
        </div>
        <div class="total_num">
          总数<span>{{ item.count || 0 }}</span>
        </div>
        <div v-if="item.eqTypeArr && item.eqTypeArr.length">
          <div class="chart" :style="{ 'grid-template-columns': getLineWidth(item.eqTypeArr, item.count, 97, 1) }">
            <div class="chart_line" v-for="(v, i) in item.eqTypeArr" :key="i + 'eqTypeArr'">
              <div class="line" :style="{ background: v.color }"></div>
            </div>
          </div>
          <div class="chart" :style="{ 'grid-template-columns': getLineWidth(item.eqTypeArr, item.count, 70, 10) }">
            <div class="chart_line" v-for="(v, i) in item.eqTypeArr" :key="i + 'eqTypeArr'">
              <div class="line_text">
                <p :style="{ color: v.color }">{{ v.name }}</p>
                <span>{{ v.num }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import global from "@/utils/Global";
// import moment from "moment";
export default {
  props: {
    projectCode: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      totalList: [
        {
          name: "报警总数",
          num: 0,
          textColor: "#86909C"
        },
        {
          name: "未处理",
          num: 0,
          textColor: "#F53F3F"
        },
        {
          name: "处理中",
          num: 0,
          textColor: "#86909C"
        }
      ],
      menuList: [],
      tabList: [],
      // equipmentArr:[
      //   {
      //     eqName:'空调机组',
      //     eqNum: 33,
      //     eqTypeArr: [
      //       { name:'运行',num:8000,color:'#00B42A' },
      //       { name:'停止',num:3,color:'#4E5969' },
      //       { name:'故障',num:6,color:'#F53F3F' },
      //       { name:'离线',num:2,color:'#FF7D00' },
      //     ]
      //   },
      //   {
      //     eqName:'风机盘管',
      //     eqNum: 33,
      //     eqTypeArr: [
      //       { name:'运行',num:12,color:'#00B42A' },
      //       { name:'停止',num:18,color:'#4E5969' },
      //       { name:'故障',num:16,color:'#F53F3F' },
      //       { name:'离线',num:21,color:'#FF7D00' },
      //     ]
      //   }
      // ],
      eqCumulativeArr: [],
      airRunRate: []
    };
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    // 获取设备分组列表
    getEntityMenuList() {
      // this.$api.getEntityMenuList(params).then(res => {
      //   this.menuList = res;
      // this.tabList = res.filter(item => item.parentId === "#")
      // });
      $.showLoading();
      this.$api
        .airGetEntityMenuList({
          projectCode: this.projectCode
        })
        .then(res => {
          this.tabList = res
            .map(ele => {
              return {
                ...ele,
                eqTypeArr: [
                  { name: "运行", sort: 1, num: ele.normalCount || 0, color: "#00B42A" },
                  { name: "停止", sort: 2, num: ele.stopCount || 0, color: "#4E5969" },
                  { name: "报警", sort: 3, num: ele.policeCount || 0, color: "#F53F3F" },
                  { name: "离线", sort: 4, num: ele.offlineCount || 0, color: "#FF7D00" }
                ]
              };
            })
          $.hideLoading();
        });
    },
    getAirViewDetail() {
      this.$api.airRunPolice({ projectCode: this.projectCode }).then(res => {
        this.totalList[0].num = res.total;
        this.totalList[1].num = res.noDealCount;
        this.totalList[2].num = res.isDealCount;
      });
    },
    // 跳转分组列表页
    jupmGroupListPage(menuCode) {
      this.$router.push({
        path: "/cEquipmentList",
        query: {
          projectCode: this.projectCode,
          menueCode: menuCode,
          title: this.title
        }
      });
    },
    getLineWidth(eqTypeArr, total, cMax, cMin) {
      let newEqTypeArr = JSON.parse(JSON.stringify(eqTypeArr)).sort((a, b) => a.num - b.num);
      total = eqTypeArr.reduce((a, b) => a + b.num, 0);
      let lessThanlessNum = 0;
      newEqTypeArr.forEach(ele => {
        const numPercent = (ele.num / total) * 100;
        if (numPercent > cMin) {
          lessThanlessNum++;
        }
      });
      let deductNum = 0;
      newEqTypeArr.map((ele) => {
        let num = (ele.num / total) * 100;
        // 最大值不得超过cMax   为其他元素留位置   最小值不得小于cMin
        if (num < cMin) {
          deductNum += cMin - num;
          num = cMin;
        } else {
          if (num - deductNum > cMin) {
            num = Math.min(cMax, (num - deductNum / lessThanlessNum));
          } else {
            lessThanlessNum = (lessThanlessNum - 1) || 1;
          }
        }
        ele.occupancy = `${num.toFixed(2)}% `;
      });
      const orthodox = newEqTypeArr.sort((a, b) => a.sort - b.sort);
      return orthodox.map(ele => `${ele.occupancy} `).join(" ");
    }
  },
  activated() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    // this.getAirRunTime()
    // this.getAirRunRate()
    this.getAirViewDetail();
    this.getEntityMenuList();
  }
};
</script>

<style scoped lang="stylus">
.no_data{
  width: 100%
  height: 3rem
  display: flex
  justify-content: center
  align-items: center
  color: #999
}
.today_function{
  width: 100%;
  background: #FFFFFF
  .today_fun_title{
    padding: 16px;
    p{
      font-size: 16px;
      font-weight: 400;
      color: #1D2129;
    }
  }
  .eq_cumulative{
    height: 1.6rem
    display: flex
    flex-direction: column
    align-items: center
    justify-content: center
    p{
      font-size: 18px;
      font-weight: bold;
      color: #1D2129;
      margin-bottom: 8px;
    }
    span{
      font-size: 14px;
      font-weight: 400;
      color: #86909C;
    }
  }
  .eq_table{
    width: 100%;
    .eq_tr{
      width: 100%;
      display: flex
      .eq_th{
        flex: 1
        padding: 16px;
        font-size: 15px;
        font-weight: 400;
        color: #1D2129;
        background: #E6EFFC;
      }
      .eq_td{
        flex: 1
        padding: 16px;
        font-size: 15px;
        font-weight: 400;
        color: #4E5969;
        border-bottom: 1px solid #E5E6EB;
      }
    }
  }
}
.equipment{
  width: calc(100vw - 20px);
  // height: 8rem;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px 10px 0 10px
  &_item{
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px
    margin-bottom: 10px
    .item_title{
      display: flex
      justify-content: space-between
      h3{
        font-size: 16px;
        font-weight: 400;
        color: #1D2129;
      }
      span{
        font-size: 14px;
        font-weight: 400;
        color: #3562DB;
      }
    }
    .total_num{
      font-size: 14px;
      font-weight: 400;
      color: #4E5969;
      margin-top: 16px;
      span{
        font-size: 18px;
        font-weight: bold;
        color: #1D2129;
        margin-left: 8px
      }
      span::after{
        content: '台'
        font-size: 13px;
        font-weight: 400;
        color: #86909C;
        margin-left: 4px
      }
    }
    .chart{
      display: grid;
      grid-gap:1px;
      grid-template-columns: auto auto auto auto
      .chart_line{
        width: 100%;
        .line{
          width: 100%;
          height: 10px;
          margin: 10px 0;
          // background: #00B42A;
        }
        .line_text{
          // min-width:100px;
          p{
            font-size: 14px;
            line-height: 25px
          }
          span{
            font-size: 14px;
            font-weight: bold;
            color: #1D2129;
          }
        }

      }
    }

  }
}
.alarm_statistics{
  font-size: 16px;
  font-family: PingFang HK-Regular, PingFang HK;
  font-weight: 400;
  color: #1D2129;
  margin-bottom: 10px
}
.air_title{
  // height: 1rem;
  padding: 16px 0;
  flex: 1;
  display: flex
  flex-direction: column;
  justify-content: center
  align-items: center
  .air_title_content{
    width: 100%
    display: flex
    flex-direction: column;
    justify-content: center
    align-items: center
    p{
      font-size: 14px;
      font-weight: 400;
      color: #4E5969;
    }
    span{
      margin-bottom: 5px;
      font-size: 18px;
      font-weight: bold;
      color: #1D2129;
    }
  }
}
.inner{
  height: 100%;
  background-color: #F2F4F9;
}
</style>
