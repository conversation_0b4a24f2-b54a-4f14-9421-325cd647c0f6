<template>
  <div class="powerDistribution-view">
    <Header title="配电监测" @backFun="goback"></Header>
    <div class="powerDistribution-header">
      <van-tabs v-model="tabsActive" color="#3562DB" @click="tabClick">
        <van-tab
          v-for="item in firstLevelMenuList"
          :key="item.code"
          :title="item.name"
          :name="item.code"
        ></van-tab>
      </van-tabs>
    </div>
    <div class="powerDistribution-main">
      <div class="main-module envirMonitor">
        <p class="module-title">环境监测</p>
        <div class="module-content">
          <div v-for="item in envirData" :key="item.paramName" class="list-item">
            <div class="item-value">
              <span
                class="item-num"
                :style="{ color: item.name ? item.colour : '#121F3E' }"
                >{{ item.number || "-" }}</span
              >
              <span class="item-unit">{{ item.paramUnit || "" }}</span>
            </div>
            <p class="item-title">{{ item.paramName }}</p>
            <p
              v-if="item.name"
              class="item-info"
              :style="{ color: item.colour }"
            >
              <van-icon name="warn-o" size="13" :color="item.colour" />
              {{ item.name }}
            </p>
          </div>
        </div>
      </div>
      <div class="main-module deviceOperationMonitor">
        <p class="module-title">设备运行统计</p>
        <div class="module-content">
          <div
            v-for="item in operationList"
            :key="item.entityTypeName"
            class="list-item"
            @click="goToDeviceOperating(1, item.entityTypeId)"
          >
            <div class="item-value">
              <span class="item-num">{{ item.normal || 0 }}</span
              ><span class="item-unit">/{{ item.total || 0 }}</span>
            </div>
            <p class="item-title">{{ item.entityTypeName }}</p>
          </div>
        </div>
      </div>
      <div class="main-module deviceOperationMonitor">
        <p class="module-title">重点设备统计</p>
        <div class="module-content" >
          <div class="list-item" @click="goToDeviceOperating()">
            <div class="item-value">
              <span class="item-num">{{ keyDeviceCount.count || 0 }}</span
              ><span class="item-unit" style="font-size: 16px;">台</span>
            </div>
            <p class="item-title">设备总数</p>
          </div>
          <div class="list-item" @click="goToDeviceOperating(6)">
            <div class="item-value">
              <span class="item-num">{{ keyDeviceCount.offlineCount || 0 }}</span
              ><span class="item-unit" style="font-size: 16px;">台</span>
            </div>
            <p class="item-title">离线数量</p>
          </div>
          <div class="list-item" @click="goToDeviceOperating(10)">
            <div class="item-value">
              <span class="item-num">{{ keyDeviceCount.policeCount || 0 }}</span
              ><span class="item-unit" style="font-size: 16px;">台</span>
            </div>
            <p class="item-title">报警数量</p>
          </div>
        </div>
      </div>
      <div class="main-module transformerMonitor">
        <p class="module-title">变压器监测</p>
        <div class="module-content">
          <div class="content-select">
            <p
              class="select-item"
              v-for="item in monitorEntityList"
              :key="item.surveyEntityCode"
              :class="{
                'select-active':
                  item.surveyEntityCode == selectMonitorEntity.surveyEntityCode
              }"
              @click="monitorEntitySelect(item.surveyEntityCode)"
            >
              {{ item.surveyEntityName }}
            </p>
          </div>
          <div class="content-schedule">
            <van-circle
              v-model="selectMonitorEntity.schedule"
              color="#3562DB"
              layer-color="#F2F3F5"
              :clockwise="false"
              :stroke-width="90"
            >
              <template #default>
                <div class="circle-default">
                  <p class="circle-default-num">
                    {{ selectMonitorEntity.schedule }}%
                  </p>
                  <p class="circle-default-text">负载率</p>
                </div>
              </template>
            </van-circle>
          </div>
          <div class="content-monitor">
            <div
              v-for="item in selectMonitorEntity.parameterList"
              :key="item.parameterId"
              class="monitor-item"
            >
              <div class="item-value">
                <span class="item-num">{{ item.parameterValue || "-" }}</span>
                <span class="item-unit">{{ item.parameterUnit || "" }}</span>
              </div>
              <p class="item-title">{{ item.parameterName }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { monitorTypeList } from "./../components/dict.js";
export default {
  name: "powerDistribution",
  data() {
    const projectName = "配电监测";
    return {
      projectName,
      projectCode: monitorTypeList.find(
        item => item.projectName === projectName
      ).projectCode,
      tabsActive: "",
      firstLevelMenuList: [], // 一级菜单列表
      envirData: [], // 环境监测列表
      operationList: [], // 设备运行统计列表
      monitorEntityList: [], // 监测实体列表
      selectMonitorEntity: {}, // 当前选择监测实体
      keyDeviceCount: [], // 重点设备统计
    };
  },
  computed: {},
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {
    this.getParentMenuList();
  },
  methods: {
    goToDeviceOperating(type, id = '') {
      if(type === 1) {
        this.$router.push({
          path: '/deviceOperating',
          query: {
            entityMenuCode: this.tabsActive,
            entityTypeId: id,
            projectCode: this.projectCode
          }
        })
      } else {
        let query = {
          entityMenuCode: this.tabsActive,
          projectCode: this.projectCode,
          deviceStatus: type
        }
        // if(type === 2) {
        //   query.iphDisposeResult = 0
        // }
        // 这里需要区分是点击哪一个item进配电监测
        this.$router.push({
          path: '/deviceOperating',
          query: query
        })
      }
    },
    tabClick(name, title) {
      this.initRequest({
        projectCode: this.projectCode,
        entityMenuCode: name
      });
    },
    initRequest(params) {
      this.getEnvironmentMonitor(params);
      this.getKeyDeviceCount(params);
      this.getEquipmentOperationMonitor(params);
      this.getRealMonitoringListSecurity(params);
    },
    // 监测实体选择
    monitorEntitySelect(code) {
      if (!code) {
        this.selectMonitorEntity = { parameterList: [], schedule: 0 }
        return
      }
      this.selectMonitorEntity = this.monitorEntityList.find(
        item => item.surveyEntityCode == code
      ) || { parameterList: [] };
      let schedule = this.selectMonitorEntity.parameterList.find(
          item => item.parameterName.indexOf("负载") != -1
        )
      this.selectMonitorEntity.schedule = schedule ? schedule.parameterValue : 0;
    },
    // 获取监测实体
    getRealMonitoringListSecurity(params) {
      this.$api
        .GetRealMonitoringListSecurity({ ...params, page: 1, pageSize: 99 })
        .then(res => {
          this.monitorEntityList = res.list;
          this.monitorEntitySelect(res.list.length ? res.list[0].surveyEntityCode : '');
        });
    },
    // 重点设备统计
    getKeyDeviceCount(params) {
      this.$api.GetKeyDeviceCount({projectCode: params.projectCode, menuCode: params.entityMenuCode, keyDevice: 0}).then(res => {
        this.keyDeviceCount = res;
      });
    },
    // 设备运行统计
    getEquipmentOperationMonitor(params) {
      this.$api.GetEquipmentOperationMonitor(params).then(res => {
        this.operationList = res;
      });
    },
    // 环境监测
    getEnvironmentMonitor(params) {
      this.$api.GetEnvironmentMonitor({menuCode: params.entityMenuCode, projectCode: params.projectCode, easyOrInfo: 0}).then(res => {
        this.envirData = res;
      });
    },
    // 获取一级菜单
    getParentMenuList() {
      this.$api.GetParentMenuList({ projectId: this.projectCode }).then(res => {
        this.firstLevelMenuList = res;
        this.tabsActive = res[0].code;
        this.initRequest({
          projectCode: this.projectCode,
          entityMenuCode: this.tabsActive
        });
      });
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="scss" scoped>
.powerDistribution-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  .powerDistribution-header {
  }
  .powerDistribution-main {
    flex: 1;
    background: #f2f4f9;
    padding: 0px 10px;
    overflow: auto;
    .main-module {
      margin-top: 10px;
      background: #ffffff;
      border-radius: 8px;
      padding: 16px 0;
      .module-title {
        font-size: 16px;
        font-weight: 400;
        color: #1d2129;
        line-height: 22px;
        padding: 0px 16px;
      }
    }
    .envirMonitor {
      .module-content {
        padding-top: 10px;
        display: flex;
        flex-wrap: wrap;
      }
      .list-item {
        width: calc(100% / 3);
        text-align: center;
        .item-value {
          margin-top: 14px;
        }
        .item-num {
          font-size: 18px;
          font-family: Arial-Bold, Arial;
          font-weight: bold;
          color: #1d2129;
          line-height: 25px;
        }
        .item-unit {
          font-size: 13px;
          font-weight: 400;
          color: #86909c;
          line-height: 18px;
        }
        .item-title {
          margin-top: 8px;
          font-size: 14px;
          font-weight: 400;
          color: #86909c;
          line-height: 20px;
        }
        .item-info {
          font-size: 12px;
          font-weight: 400;
          line-height: 17px;
          margin-top: 3px;
        }
      }
    }
    .deviceOperationMonitor {
      .module-content {
        padding-top: 10px;
        display: flex;
        flex-wrap: wrap;
      }
      .list-item {
        padding: 14px 0px;
        width: calc(100% / 3);
        text-align: center;
        .item-value {
          font-size: 18px;
          font-weight: bold;
        }
        .item-num {
          color: #3562db;
        }
        .item-unit {
          color: #1d2129;
        }
        .item-title {
          margin-top: 8px;
          font-size: 14px;
          font-weight: 400;
          color: #86909c;
          line-height: 20px;
        }
      }
    }
    .transformerMonitor {
      .module-content {
        .content-select {
          display: flex;
          flex-wrap: wrap;
          padding: 0px 10px 10px 0px;
          .select-item {
            padding: 8px;
            background: #f7f8fa;
            font-size: 14px;
            font-weight: 300;
            color: #4e5969;
            line-height: 20px;
            margin: 10px 0px 0px 10px;
          }
          .select-active {
            background: #e6effc;
            color: #3562db;
          }
        }
        .content-schedule {
          text-align: center;
          padding: 8px 0px;
          .van-circle {
            .van-circle__hover {
              stroke-linecap: inherit;
            }
          }
          .circle-default {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          }
          .circle-default-num {
            color: #3562db;
            font-size: 18px;
            font-weight: bold;
          }
          .circle-default-text {
            color: #1d2129;
            margin-top: 6px;
            font-size: 14;
          }
        }
        .content-monitor {
          display: flex;
          flex-wrap: wrap;
          .monitor-item {
            padding: 14px 8px;
            width: calc(100% / 3);
            text-align: center;
            box-sizing: border-box;
          }
          .item-value {
            margin-top: 10px;
          }
          .item-num {
            font-size: 18px;
            font-weight: bold;
            color: #1d2129;
            line-height: 25px;
          }
          .item-unit {
            font-size: 13px;
            font-weight: 400;
            color: #86909c;
            line-height: 18px;
          }
          .item-title {
            margin-top: 8px;
            font-size: 14px;
            font-weight: 400;
            color: #86909c;
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>
