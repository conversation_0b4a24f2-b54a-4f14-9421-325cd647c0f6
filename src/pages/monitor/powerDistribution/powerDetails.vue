<template>
  <div class="inner">
    <Header title="配电监测" @backFun="goback"></Header>
    <div class="data_analy_list" v-if="listData">
      <h3>{{listData.surveyName}}</h3>
      <div class="item_data_name" v-for="(item, index) in listData.paramObj" :key="index">
        <p>{{ item.paramName }}</p>
        <div v-if="item.paramId == '30265'" class="item_btn" :style="item.paramValue ? '' : {backgroundColor: '#F53F3F',color: '#fff'}">{{item.paramValue ? '启动' : '停止'}}</div>
        <div class="item_data_val" v-else>
          <span>{{ item.paramValue }} {{ item.unit }}</span>
          <div class="item_data_status" v-if="item.warnId">
            <img src="@/assets/images/error.png" alt="">
            <b :style="{ color: item.warnColor }">{{ item.warnName }}</b>
          </div>
        </div>
      </div>
    </div>
    <ybsEmpty v-else imageType="searchEmpty" style="height: 100%" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      listData: null
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback)
    this.getDetails()
  },
  methods: {
    getDetails() {
      let query = this.$route.query
      let data = {
        ...query,
      }
      this.$api.GetBoilerMonitorDetail(data).then(res => {
        this.listData = res
      });
    },
    goback() {
      this.$router.go(-1);
    },
  }
}
</script>

<style scoped lang="stylus">
.noData{
  padding: 100px 0px 0px 0px ;
  text-align: center;
  color: #999999;
  img {
    width: 180px;
    height: 130px;
  }
  p{
    font-size: 15px;
    font-weight: 300;
    color: #4E5969;
    line-height: 21px;
  }
}
.inner{
  height: 100%;
  background-color: #F2F4F9;
  .data_analy_list{
    background: white;
    padding: 16px;
    h3{
      font-size: 16px;
      color: #1D2129;
      margin-bottom: 7px;
    }
    .item_data_name{
      margin: 10px 0
      display: flex;
      align-items: center
      p{
        width: 120px
        font-size: 16px;
        color: #4E5969;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .item_data_val{
        display: flex
        span{
          font-size: 16px;
          color: #1D2129;
          margin-right: 10px
        }
        .item_data_status{
          display: flex
          align-items: center;
          img{
            width: 12px;
            height: 12px;
          }
          b{
            font-size: 12px;
            color: #F53F3F;
          }
        }
      }
      .item_btn{
        width: 36px;
        height: 24px;
        display: flex;
        justify-content: center
        align-items: center
        background: #E8FFEA;
        border-radius: 2px 2px 2px 2px;
        font-size: 12px;
        font-weight: 500;
        color: #00B42A;
      }
    }
  }
}
</style>
