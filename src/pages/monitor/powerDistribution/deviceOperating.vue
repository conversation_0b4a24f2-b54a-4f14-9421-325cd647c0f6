<template>
  <div class="deviceOperating-view">
    <Header title="配电监测" @backFun="goback"></Header>
    <el-table :data="deviceList" style="width: 100%" @row-click="tableRowClick">
      <el-table-column prop="surveyName" label="设备名称"></el-table-column>
      <el-table-column prop="status" label="在线状态">
        <template slot-scope="scope">
          <span class="table-tag" :style="{background: scope.row.lineBackground, color: lineColor }">
            {{ scope.row.lineStatus }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="policeStats" label="报警状态">
        <template slot-scope="scope">
          <span class="table-tag" :style="{background: scope.row.warnBackground, color: scope.row.warnColor }">
            {{ scope.row.warnType }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "deviceOperating",
  data() {
    return {
      hasEntityType: false,// 是否有entityTypeId 对应两种不同的页面
      deviceList: [
        {surveyName: '1', status: '', policeStats: ''}
      ]
    };
  },
  computed: {},
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {
    this.hasEntityType = Boolean(this.$route.query.entityTypeId)
    if (this.hasEntityType) {
      this.getDeviceOperating()
    } else {
      this.querySurveyListByMenuCode()
    }
  },
  methods: {
    tableRowClick(row) {
      if(!this.$route.query.entityTypeId) {
        this.$router.push({
          path: '/powerDetails',
          query: {
            surveyCode: row.surveyCode,
            projectCode: this.$route.query.projectCode
          }
        })
      }
    },
    querySurveyListByMenuCode() {
      let params = {
        // iphDisposeResult: this.$route.query.iphDisposeResult * 1,
        deviceStatus: this.$route.query.deviceStatus,
        keyDevice: '0',
        menueCode: this.$route.query.entityMenuCode,
        projectCode: this.$route.query.projectCode,
      }
      this.$api.querySurveyListByMenuCode(params).then(res => {
        this.deviceList = res.length && res.map(e => {
          return {
            ...e,
            lineStatus: e.line,
            lineBackground: e.line == "在线" ? '#E8FFEA' : '#F2F3F5',
            lineColor: e.line == "在线" ? '#00B42A' : '#4E5969',
            warnType: e.warn,
            warnBackground: e.warn == '报警' ? '#FFECE8' : '#E8FFEA',
            warnColor: e.warn == '报警' ? '#F53F3F' : '#00B42A'
        }
        });
      });
    },
    getDeviceOperating() {
      this.$api.GetDeviceOperating(this.$route.query).then(res => {
        this.deviceList = res.length && res.map(e => {
          return {
            ...e,
            lineStatus: e.status == 0 ? '运行' : (e.status == 1 ? '停止' : '离线'),
            lineBackground: e.status == 0 ? '#E8FFEA' : '#F2F3F5',
            lineColor: e.status == 0 ? '#00B42A' : '#4E5969',
            warnType: e.policeStats == 0 ? '故障' : '正常',
            warnBackground: e.policeStats == 0 ? '#FFECE8' : '#E8FFEA',
            warnColor: e.policeStats == 0 ? '#F53F3F' : '#00B42A'
        }
        });
        console.log(this.deviceList);
      });
    },
    goback() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.deviceOperating-view {
  height: 100%;
  // display: flex;
  // flex-direction: column;
  background: #f2f4f9;
  /deep/ .el-table {
    .el-table__header-wrapper{
      th{
        border: none;
        background: #E6EFFC;
        .cell{
          padding-left: 8px;
          padding-right: 8px;
          color: #1D2129;
        }
      }
    }
    .el-table__body-wrapper{
      td{
        .cell{
          padding-left: 8px;
          padding-right: 8px;
          color: #4E5969
        }
      }
    }
  }
  .table-tag{
    display: inline-block;
    padding: 4px 6px;
    font-size: 12px;
    font-weight: 500;
    line-height: 17px;
    border-radius: 2px;
  }
}
</style>
