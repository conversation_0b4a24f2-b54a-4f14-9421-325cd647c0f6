<template>
  <div class="inner">
    <Header title="照明监控" @backFun="goback"></Header>
    <!-- <van-tabs v-model="tabsSwitch" color="#3562DB" @change="clickTabs" style="margin-bottom: 10px;">
      <van-tab title="自动｜晴天模式" name="0"></van-tab>
      <van-tab name="1">
        <template slot="title">
          手动
          <span v-if="tabsSwitch==1">({{ timerNum }}s)</span>
        </template>

      </van-tab>
    </van-tabs> -->
    <div class="lighting_content">
      <div class="btn_tags">
        <div class="btn_item" :class="functionType==0 ? 'btn_item_active' : ''" style="margin-right: 10px;" @click="clickFun(0)">
          <p>运行总览</p>
        </div>
        <div class="btn_item" :class="functionType==1 ? 'btn_item_active' : ''" @click="clickFun(1)">
          <p>运行监控</p>
        </div>
      </div>
      <div class="flex_content" style="padding: 0 16px;" v-show="functionType==0">
        <div id="lightingEcharts" style="width: 100%;height: 50%;"></div>
        <div class="lighting_data">
          <div class="data_item">
            <p>{{overviewData.outPutTotal}}</p>
            <span>回路总数</span>
          </div>
          <div class="data_item">
            <p>{{overviewData.actuatorTotal}}</p>
            <span>模块总数</span>
          </div>
        </div>
        <div class="bag_data">
          <div class="bag_item bag_img4" >
            <p style="color: #606266;">{{overviewData.offlineTotal}}</p>
            <span>控制模块离线数</span>
          </div>
          <div class="bag_item bag_img5">
            <p class="color_white">{{overviewData.onlineRate}}</p>
            <span class="color_white">在线率</span>
          </div>
          <div class="bag_item bag_img1">
            <p class="color_red">{{ overviewData.moduleFailureTotal }}</p>
            <span>模块故障数</span>
          </div>
        </div>

        <div class="bag_data">
          <div class="bag_item bag_img3" >
            <p class="color_blue">{{overviewData.openTotal}}</p>
            <span>开启数</span>
          </div>
          <div class="bag_item bag_img2">
            <p class="color_white">{{overviewData.openRate}}</p>
            <span class="color_white">开启率</span>
          </div>
          <div class="bag_item bag_img1">
            <p class="color_red">{{ overviewData.controlReturnTotal }}</p>
            <span>回路控返不一致</span>
          </div>
        </div>

      </div>

      <ListData v-show="functionType==1" />

    </div>

  </div>
</template>

<script>
import ListData from './components/listData.vue'
export default {
  name:'lighting',
  data() {
    return {
      tabsSwitch: 0,
      timerNum:120,
      timer:null,
      functionType:0,
      overviewData:{},
    }
  },
  components:{ ListData },
  mounted() {
    this.getCountOverview()
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    goback(){
      this.$YBS.apiCloudCloseFrame()
    },
    getCountOverview(){
      this.$api.countOverview().then(res=>{
        this.overviewData = res
        let data = [
          {
            name: '开启',
            value: res.openTotal =='-' ? 0 : res.openTotal
          },
          {
            name: '关闭',
            value: res.closeTotal =='-' ? 0 : res.closeTotal
          },
          {
            name: '其他',
            value: res.unknownTotal=='-' ? 0 : res.unknownTotal
          }
        ]
        this.getLightingEcharts(data)
      })
    },
    getLightingEcharts(data){
      let myChart1 = this.$echarts.init(document.getElementById("lightingEcharts"));
      let total = data.reduce((a,b) => {
        return a + b.value
      }, 0)
      myChart1.setOption({
        backgroundColor: '#fff',
        color: ['#3562DB', '#FBAF1B', '#86909C'],
        title: [  ],
        legend: {
          left: 'center',
          top: 'bottom',
          orient: 'vertical',
          selectedMode: false,   // 设置是否打开默认点击事件
          itemHeight: 8,
          itemWidth: 8,
          formatter: name => {
            let value
            for (let i = 0; i < data.length; i++) {
              if (data[i].name == name) {
                value = data[i].value
              }
            }
            let proportion = Math.round((value / total) * 100)
            let arr = `{name|${name}}:{value|${value}}{name|百分比}:{value|${proportion}%}`
            return arr
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 16,
                color: '#4E5969',
                fontWeight: 350,
                padding: [0, 5, 0, 5]
              },
              value: {
                width: 40,
                fontSize: 14,
                color: '#4E5969',
                fontWeight: 350,
                padding: [0, 0, 0, 5]
              },
            }
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['35%', '60%'],
            center: ['50%', '40%'],
            data: data,
            labelLine: {
              show: false
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                formatter:'{d}%',
                color:'#353535',
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            // label: {
            //   normal: {
            //     formatter: "{d}%",
            //     textStyle: {
            //       fontWeight: "normal",
            //       fontSize: 14,
            //     },
            //     show: true,
            //     position: "center",
            //   },
            // }
          }
        ]
      })
    },
    clickFun(type){
      this.functionType = type
      if (type == 0) {
        this.getCountOverview()
      }
    },
    clickTabs(type){
      this.tabsSwitch = type
      if(type==1){
        clearInterval(this.timer)
        this.timerNum = 120
        this.timer = setInterval(() => {
          if(this.timerNum <= 0){
            this.clickTabs(1)
          }
          this.timerNum--
        }, 1000);
      }else{
        clearInterval(this.timer)
      }
    }
  }
}
</script>

<style scoped lang="stylus">
.inner{
  height: 100%;
  background-color: #F2F4F9;
  display: flex;
  flex-direction: column;
}
.lighting_content{
  background: white;
  flex: 1;
  display: flex;
  flex-direction: column;
  // padding: 10px 0px 0 0px;
  padding-top: 10px;
  .flex_content{
    width: calc(100% - 32px);
    flex: 1;
    .lighting_data{
      display: grid;
      grid-template-columns:auto auto;
      grid-gap: 10px;
      .data_item{
        height: 1.6rem;
        display: flex
        flex-direction: column
        justify-content: center
        align-items: center;
        box-shadow: 0px 1px 5px 0px rgba(190,196,204,0.5);
        border-radius: 4px;
        p{
          font-size: 18px;
          color: #3562DB;
          font-weight: bold;
          &::after{
            content: '条'
            font-size: 13px;
            font-weight: 500;
            color: #999999;
            margin-left: 5px;
          }
        }
        span{
          font-size: 14px;
          font-weight: 400;
          margin-top: 6px;
        }
      }
    }
    .bag_data{
      height: 2rem
      display: flex
      margin-top: 10px;
      .bag_item{
        height: 100%;
        display: flex
        flex-direction: column
        justify-content: center
        align-items: center
        .color_white{
          color:#FFFFFF;
        }
        .color_red{
          color: #F53F3F
        }
        .color_blue{
          color: #3562DB;
        }

        p{
          font-size: 18px;
          color: #606266;
        }
        span{
          font-size: 14px;
          font-weight: 400;
          margin-top: 8px;
        }
      }
      .bag_img1{
        width: 30%;
        background-image: url('./../../../assets/images/monitor/bag_img1.png');
        background-size: cover;
      }
      .bag_img2{
        width: 30%;
        background-image: url('./../../../assets/images/monitor/bag_img2.png');
        background-size: cover;
      }
      .bag_img3{
        width: 40%;
        background-image: url('./../../../assets/images/monitor/bag_img3.png');
        background-size: cover;
      }
      .bag_img4{
        width: 40%;
        background-image: url('./../../../assets/images/monitor/bag_img4.png');
        background-size: cover;
      }
      .bag_img5{
        width: 30%;
        background-image: url('./../../../assets/images/monitor/bag_img5.png');
        background-size: cover;
      }
    }
  }

}
.btn_tags{
  width: calc(100% - 32px);
  display: flex;
  justify-content: center;
  padding: 0 16px;
  .btn_item{
    flex: 1
    // width: 2rem;
    height: .7rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #F7F8FA;
    p{
      font-size: 14px;
      color: #4E5969;
    }
  }
  .btn_item_active{
    background: #E6EFFC;
    color: #3562DB;
  }
}
/deep/ .van-tab .van-tab__text--ellipsis{
  font-size: 16px !important;
  color: #86909C !important;
}
/deep/ .van-tab--active .van-tab__text--ellipsis{
  font-size: 16px !important;
  font-weight: bold;
  color: #1D2129 !important;
}
.tabs_switch{
  display: flex;
  margin-top: 10px;
  .tabs_item{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    img{
      margin-right: 5px;
    }
  }
  .tabs_item_A{
    background: #FFFFFF;
    border-radius: 0px 28px 0px 0px;
    color: #3562DB;
    p{
      font-size: 15px;
      font-weight: 600;
    }
  }
  .tabs_item_B{
    background: #FFFFFF;
    border-radius: 28px 0px 0px 0px;
    color: #3562DB;
    font-size: 15px;
    font-weight: 600;
    p{
      font-size: 15px;
      font-weight: 600;
    }
  }
}
</style>
