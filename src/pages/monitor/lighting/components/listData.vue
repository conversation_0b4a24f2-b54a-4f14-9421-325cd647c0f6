<template>
  <div style="width: 100%;">
    <div class="tabs_type">
      <div class="tabs_btn" v-for="item in tabsTypeArr" :key="item.type"
        :class="tabsType == item.type ? 'tabs_btn_active' : ''" @click="onTabsType(item)">
        <span>{{ item.label }}</span>
      </div>
    </div>
    <ModularList 
      v-if="tabsType==1"
      :buildingOption="buildingOption"
      :floorOption="floorOption"
    ></ModularList>
    <GroupingList 
      v-if="tabsType==2"
    ></GroupingList>
    <LoopList 
      v-if="tabsType==3"
      :buildingOption="buildingOption"
      :floorOption="floorOption"
    ></LoopList>
    <SpaceList 
      v-if="tabsType==4"
      :areaOption="areaOption"
    ></SpaceList>
    
    <!-- <div class="footer"></div>
    <div class="footer footer_btn">
      <p>一键全关</p>
      <p style="background: #3562DB;color:#FFFFFF;margin-left: 20px;">一键全开</p>
    </div> -->
  </div>
</template>

<script>
import ModularList from './searchList/modularList.vue'
import GroupingList from './searchList/groupingList.vue'
import LoopList from './searchList/loopList.vue'
import SpaceList from './searchList/spaceList.vue'
export default {
  name: 'listData',
  components: {
    ModularList,  // 模板
    GroupingList,   //分组
    LoopList,   //回路
    SpaceList,   //空间
  },
  data() {
    return {
      tabsTypeArr: [
        {
          type: 1,
          label: '按模块',
        },
        {
          type: 2,
          label: '按分组',
        },
        {
          type: 3,
          label: '按回路',
        },
        {
          type: 4,
          label: '按空间',
        }
      ],
      
      tabsType: 1,
      // 服务建筑
      buildingOption:[],
      // 服务楼层
      floorOption:[],
      // 控件类型
      areaOption:[],
    }
  },
  mounted() {
    this.getStructureTree()
    this.getSelectByList()
  },
  methods: {
    getSelectByList(){
      this.$api.selectByList({ typeValue: 'SP' }).then(res=>{
        if(res.length){
          this.areaOption = res.map(e => {
            return {
              ...e,
              text: e.dictName,
              value: e.id
            }
          })
        }
      })
    },  
    getStructureTree(){
      this.$api.structureTree().then(res=>{
        if(res.length){
          this.buildingOption = res.filter((e) => {
            if(e.ssmType == 3){
              e.text = e.ssmName,
              e.value = e.id
              return true
            }
            return false
          })
          this.floorOption = res.filter((e) => {
            if(e.ssmType == 4){
              e.text = e.ssmName,
              e.value = e.id
              return true
            }
            return false
          })
        }
      })
    },
    onTabsType(item) {
      this.tabsType = item.type
    }
  }
}
</script>

<style scoped lang="stylus">
.tabs_type{
  display: grid;
  grid-template-columns:repeat(4, auto)
  grid-gap:10px;
  margin-top: 25px
  padding: 0 16px;
  .tabs_btn{
    height: 24px;
    border-radius: 100px 100px 100px 100px;
    border: 1px solid #A0B8F6;
    text-align: center;
    span{
      font-size: 12px;
      font-weight: 500;
      color: #3562DB;
      line-height: 24px;
    }
  }
  .tabs_btn_active{
    border: 1px solid #3562DB;
    background: #3562DB;
    span{
      color: white;
    }
  }
}



.footer{
  width: 100%
  height: 1.2rem
}
.footer_btn{
  position: fixed
  bottom: 0
  left: 0
  display: flex
  justify-content: center
  align-items: center
  // box-shadow: 0px -1px 0px 0px #E5E5E5;
  background: white
  p{
    width: 161px;
    height: 44px;
    border-radius: 2px;
    border: 1px solid #3562DB;
    display: flex
    justify-content: center
    align-items: center
    font-size: 15px;
    background: #E6EFFC;
    color: #3562DB
  }
}
</style>