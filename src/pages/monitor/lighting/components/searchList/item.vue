<template>
  <div class="list_item">
    <div class="item_l">
      <img v-if="item.outputStatus == 0" style="width:20px" src="@/assets/images/monitor/lamp.png" alt="">
      <img v-else style="width:20px" src="@/assets/images/monitor/lamp_active.png" alt="">
      <span>{{item.loopName}}</span>
      <p class="item_spot" :class="item.outputStatus == 1 ? 'item_spot_on' : 'item_spot_off'"></p>
    </div>
    <div class="item_r">
      <span>{{ item.outputStatus == 1 ? '开' : '关' }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props:{
    item:{
      type: Object,
      default: ()=>{
        return {}
      }
    }
  }
}
</script>

<style scoped lang="stylus">
.list_item{
  width: 100%
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: 15px;
  .item_l{
    display: flex
    align-items: center
    span{
      font-size: 16px;
      margin: 0 10px
      font-weight: bold;
      color: black;
    }
    .item_spot{
      width: 6px;
      height: 6px;
      border-radius: 50%;
    }
    .item_spot_on{
      background: #00B42A;
    }
    .item_spot_off{
      background: #F53F3F;
    }
  }
  .item_r{
    display: flex
    align-items: center
    span{
      font-size: 14px;
      color: #888888;
    }
  }
}
</style>