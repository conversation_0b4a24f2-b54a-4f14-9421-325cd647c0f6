<template>
  <div class="search_list">
    <div class="search_bor_bom">
      <van-search
        v-if="searchShow"
        v-model="queryParams.surveryName"
        show-action
        placeholder="请输入关键词搜索"
        @search="keyWordGetList"
        @cancel="onCancel"
      />
      <van-dropdown-menu v-if="!searchShow" active-color="#3562DB" class="my_dropdown_menu">
        <van-dropdown-item ref="vanDropdownMenu1" v-model="queryParams.constructionId" :options="buildingOption" @change="stateGetList">
          <template slot="title">
            <span :style="{'color':queryParams.constructionId ? '#3562DB' : ''}">{{ getName(buildingOption, queryParams.constructionId, '建筑')}}</span>
          </template>
          <template slot="default">
            <div class="btn_box">
              <div class="btn" @click="reset('vanDropdownMenu1', 'constructionId')">重置</div>
            </div>
          </template>
        </van-dropdown-item>
        <van-dropdown-item ref="vanDropdownMenu2" v-model="queryParams.floorId" :options="floorOption" @change="stateGetList">
          <template slot="title">
            <span :style="{'color':queryParams.floorId ? '#3562DB' : ''}">{{ getName(floorOption, queryParams.floorId, '楼层')}}</span>
          </template>
          <template slot="default">
            <div class="btn_box">
              <div class="btn" @click="reset('vanDropdownMenu2', 'floorId')">重置</div>
            </div>
          </template>
        </van-dropdown-item>
      </van-dropdown-menu>
      <div v-if="!searchShow" class="list_search" @click.stop="searchShow=true">
        <div class="search_img">
          <img src="@/assets/images/monitor/search.png" alt="" />
        </div>
      </div>
    </div>
    <div class="list_data">
      <div class="item_box" v-show="list.length" v-for="item in list" :key="item.id">
        <div style="padding: 0 16px;">
          <div class="father_name">
            <div class="father_title">
              <p>{{ item.localName }}</p>
              <span>以下数据不可作为参考值</span>
            </div>
            <div class="father_type" :style="{'background':item.state == 0?'#E8FFEA':'#F2F3F5'}">
              <img v-if="item.state == 0" src="@/assets/images/monitor/onLine.png" alt="">
              <img v-else src="@/assets/images/monitor/offLine.png" alt="">
              <span :style="{'color':item.state == 0?'#00B42A':'#4E5969'}">在线</span>
            </div>
          </div>
          <Item v-for="v in item.child" :key="v.outPutId + item.actuatorId" :item="v" />
        </div>
      </div>
      <ybsEmpty v-show="!list.length" imageType="searchEmpty" style="height: 100%" />
      <!-- <van-empty
        v-show="!list.length"
        class="custom-image"
        :image="dataNull"
        description="数据为空"
      /> -->
    </div>
  </div>
  
</template>

<script>
import Item from './item.vue'
import dataNull from './../../../../../assets/images/equipmentManagement/缺省-图表@2x.png'
export default {
  components: {
    Item
  },
  data(){
    return {
      dataNull,
      queryParams:{
        switchStatus: null,
        constructionId: null,
        floorId: null,
        surveryName: null,
        groupName: null,
        spaceName: null,
        spaceTypeId: null,
        type: 1,
        page: 1,
        pageSize: 15
      },
      list:[],
      searchShow:false,
    }
  },
  props:{
    buildingOption:{
      type:Array,
      default:[]
    },
    floorOption:{
      type:Array,
      default:[]
    },
  },
  mounted() {
    this.getList()
  },
  methods:{
    getName(arr, id, defaultText) {
      let obj = arr.find(ele => ele.value == id)
      return obj ? obj.text : defaultText
    },
    reset(refName, keyName) {
      this.$refs[refName].toggle()
      this.queryParams[keyName] = null
      this.getList()
    },
    stateGetList(){
      this.queryParams.surveryName = null
      this.getList()
    },
    keyWordGetList(){
      this.queryParams.constructionId = null
      this.queryParams.floorId = null
      this.getList()
    },
    onCancel(){
      this.searchShow = false
      this.stateGetList()
    },
    getList() {
      this.$api.groupOperationMonitoring(this.queryParams).then(res => {
        this.list = res
      })
    },
  }
}
</script>

<style scoped lang="stylus">
.btn_box{
  width: 100%;
  background: white
  position: sticky;
  bottom: 0;
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
  .btn{
    width: 60px;
    height: 32px;
    background: #E6EFFC;
    border-radius: 2px;
    border: 1px solid #E6EFFC;
    font-size: 14px;
    font-weight: 300;
    color: #3562DB;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
    
  }
}

/deep/ .van-collapse-item{
  border-top: 1px solid #E5E6EB;
}
/deep/ .van-dropdown-menu__item{
  width: 33% !important;
  flex: none !important;
  justify-content: flex-start !important;
}
/deep/ .van-search__content{
  background: #F2F3F5;
}
/deep/ .van-search__action{
  color: #3562DB;
}
.search_bor_bom .van-search{
  width: 100%;
}
.item_box{
  padding: 15px 0;
  border-top: 1px solid #E5E6EB;
  
  .father_name{
    display: flex
    align-items: center
    justify-content: space-between;
    margin-bottom: 20px;
    .father_title{
      display: flex
      flex-direction: column
      p{
        font-size: 16px;
        font-weight: bold;
        color: #1D2129;
      }
      span{
        font-size: 14px;
        font-weight: 400;
        color: #86909C;
        margin-top: 10px;
      }
    }
    .father_type{
      width: 50px;
      height: 24px;
      display: flex
      align-items: center
      justify-content: center
      span{
        font-size: 12px;
        font-weight: 500;
        color: #00B42A;
        margin-left: 5px
      }
    }
  }
}
.search_bor_bom{
  // border-bottom: 1px solid #E5E6EB;
  display: flex
  align-items: center
  padding-top:10px;
  .my_dropdown_menu{
    width: 80vw
    padding: 0 16px;
  }
  .list_search{
    flex: 1
    padding: 16px 0
    .search_img{
      width: 100%;
      border-left: 1px solid #E5E6EB;
      display: flex
      justify-content: center
    }
    
  }
}
.list_data{
  height: calc(100vh - 10vh - 55px - 60px - 50px);
  overflow-y: auto
}
</style>