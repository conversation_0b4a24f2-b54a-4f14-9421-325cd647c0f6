<template>
  <div class="search_list">
    <div class="search_bor_bom">
      <van-search
        v-if="searchShow"
        v-model="queryParams.surveryName"
        show-action
        placeholder="请输入关键词搜索"
        @search="keyWordGetList"
        @cancel="onCancel"
      />
      <van-dropdown-menu v-if="!searchShow" active-color="#3562DB" class="my_dropdown_menu">
        <van-dropdown-item ref="vanDropdownMenu1" v-model="queryParams.constructionId" :options="buildingOption" @change="stateGetList">
          <template slot="title">
            <span :style="{'color':queryParams.constructionId ? '#3562DB' : ''}">{{ getName(buildingOption, queryParams.constructionId, '建筑')}}</span>
          </template>
          <template slot="default">
            <div class="btn_box">
              <div class="btn" @click="reset('vanDropdownMenu1', 'constructionId')">重置</div>
            </div>
          </template>
        </van-dropdown-item>
        <van-dropdown-item ref="vanDropdownMenu2" v-model="queryParams.floorId" :options="floorOption" @change="stateGetList">
          <template slot="title">
            <span :style="{'color':queryParams.floorId ? '#3562DB' : ''}">{{ getName(floorOption, queryParams.floorId, '楼层')}}</span>
          </template>
          <template slot="default">
            <div class="btn_box">
              <div class="btn" @click="reset('vanDropdownMenu2', 'floorId')">重置</div>
            </div>
          </template>
        </van-dropdown-item>
      </van-dropdown-menu>
      <div v-if="!searchShow" class="list_search" @click.stop="searchShow=true">
        <div class="search_img">
          <img src="@/assets/images/monitor/search.png" alt="" />
        </div>
      </div>
    </div>
    <div class="list_data">
      <van-list
        v-model="listLoading"
        :error.sync="error"
        error-text="请求失败，点击重新加载"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div style="padding: 10px 16px;">
          <Item v-for="item in list" :key="item.groupId" :item="item" />
        </div>
      </van-list>
      <ybsEmpty v-show="!list.length" imageType="searchEmpty" style="height: 100%" />
      <!-- <van-empty
        v-show="!list.length"
        class="custom-image"
        :image="dataNull"
        description="数据为空"
      /> -->
    </div>
  </div>
  
</template>

<script>
import Item from './item.vue'
import dataNull from './../../../../../assets/images/equipmentManagement/缺省-图表@2x.png'
export default {
  components:{
    Item
  },  
  data(){
    return {
      // 是否处于加载状态，加载过程中不触发load事件
      listLoading:false,
      // 是否已加载完成，加载完成后不再触发load事件
      finished:false,
      error:false,
      openArr:[],
      dataNull,
      switchArr:[
        {text:'开启',value:1},
        {text:'关闭',value:0},
      ],
      queryParams:{
        switchStatus: null,
        constructionId: null,
        floorId: null,
        surveryName: null,
        groupName: null,
        spaceName: null,
        spaceTypeId: null,
        type: 3,
        page: 1,
        pageSize: 15
      },
      list:[],
      searchShow:false,
    }
  },
  props:{
    buildingOption:{
      type:Array,
      default:[]
    },
    floorOption:{
      type:Array,
      default:[]
    },
  },
  mounted() {
    
  },
  methods:{
    getName(arr, id, defaultText) {
      let obj = arr.find(ele => ele.value == id)
      return obj ? obj.text : defaultText
    },
    reset(refName, keyName) {
      this.$refs[refName].toggle()
      this.queryParams[keyName] = null
      this.getList()
    },
    // 到达底部
    onLoad(){
      this.getList()
    },
    stateGetList(){
      this.list = []
      this.openArr = []
      this.queryParams.surveryName = null
      this.queryParams.page = 1
      this.getList()
    },
    keyWordGetList(){
      this.list = []
      this.openArr = []
      this.queryParams.constructionId = null
      this.queryParams.floorId = null
      this.queryParams.page = 1
      this.getList()
    },
    onCancel(){
      this.searchShow = false
      this.stateGetList()
    },
    getList() {
      this.$api.groupOperationMonitoring(this.queryParams).then(res => {
        if(res.list){
          this.list = [...this.list,...(res.list.map(ele=>{return {...ele,loopName:ele.loopsName}}))]
          if(!res.list.length){
            this.finished = true
          }else{
            this.queryParams.page++
            this.listLoading = false
          }
        }else{
          this.listLoading = false
          this.error = true
        }
      },err=>{
        this.listLoading = false
        this.error = true
      })
    },
  }
}
</script>

<style scoped lang="stylus">
.btn_box{
  width: 100%;
  background: white
  position: sticky;
  bottom: 0;
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
  .btn{
    width: 60px;
    height: 32px;
    background: #E6EFFC;
    border-radius: 2px;
    border: 1px solid #E6EFFC;
    font-size: 14px;
    font-weight: 300;
    color: #3562DB;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
    
  }
}
/deep/ .van-collapse-item{
  border-top: 1px solid #E5E6EB;
}
/deep/ .van-dropdown-menu__item{
  width: 33% !important;
  flex: none !important;
  justify-content: flex-start !important;
}
/deep/ .van-search__content{
  background: #F2F3F5;
}
/deep/ .van-search__action{
  color: #3562DB;
}
.search_bor_bom .van-search{
  width: 100%;
}
.item_box{
  padding: 15px 0;
  border-top: 1px solid #E5E6EB;
  
  .father_name{
    display: flex
    align-items: center
    justify-content: space-between;
    margin-bottom: 20px;
    .father_title{
      display: flex
      flex-direction: column
      p{
        font-size: 16px;
        font-weight: bold;
        color: #1D2129;
      }
      span{
        font-size: 14px;
        font-weight: 400;
        color: #86909C;
        margin-top: 10px;
      }
    }
    .father_type{
      width: 50px;
      height: 24px;
      display: flex
      align-items: center
      justify-content: center
      span{
        font-size: 12px;
        font-weight: 500;
        color: #00B42A;
        margin-left: 5px
      }
    }
  }
}
.search_bor_bom{
  // border-bottom: 1px solid #E5E6EB;
  display: flex
  align-items: center
  padding-top:10px;
  .my_dropdown_menu{
    width: 80vw
    padding: 0 16px;
    
  }
  .list_search{
    flex: 1
    
    padding: 16px 0
    .search_img{
      width: 100%;
      border-left: 1px solid #E5E6EB;
      display: flex
      justify-content: center
    }
    
  }
}
.list_data{
  height: calc(100vh - 10vh - 55px - 60px - 50px);
  overflow-y: auto
}
</style>