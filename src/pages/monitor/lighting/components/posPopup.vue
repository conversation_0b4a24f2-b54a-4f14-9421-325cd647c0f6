<template>
  <van-popup v-model="popupShow" position="bottom" :style="{ height: '90vh' }">
    <div class="pop_box">
      <van-search
        v-model="posSearch"
        show-action
        shape="square"
        placeholder="请输入关键词搜索"
        @search="onSearch"
        @cancel="onCancel"
      />
      <div class="pop_content">
        <div class="pop_content_title">
          <p>空间功能类型1</p>
        </div>
        <div class="pop_content_list" v-for="item in 50" :key="item">
          <p>选项B</p>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
export default {
  data(){
    return {
      posSearch:null,
      // 搜索弹出层
      popupShow:false,
    }
  },
  methods:{
    onSearch(){

    },
    onCancel(){
      this.popupShow = false
    }
  }
}
</script>

<style scoped lang="stylus">
.pop_box{
  height: 100%;
  background: #F2F3F5;
  .pop_content{
    .pop_content_title{
      padding: 16px;
      p{
        font-size: 16px;
        font-weight: 400;
        color: #1D2129;
      }
    }
    .pop_content_list{
      padding: 16px;
      background: white;
      p{
        font-size: 16px;
        font-weight: 400;
        color: #1D2129;
      }
    }
  }
}
/deep/ .van-search__content{
  background: #F2F3F5;
}
/deep/ .van-search__action{
  color: #3562DB;
}

</style>