<!--
 * @Author: hedd
 * @Date: 2023-10-19 18:20:45
 * @LastEditTime: 2023-11-20 18:23:16
 * @FilePath: \ybs_h5\src\pages\monitor\securityMonitor\components\securityContainer.vue
 * @Description:
-->
<template>
  <div class="monitor">
    <Header :title="monitorData.projectName" @backFun="goback"></Header>
    <div class="monitor-content">
      <div class="monitor-top">
        <div v-if="$slots.overview" class="top-overview"><slot name="overview"></slot></div>
        <div v-else class="top-overview">
          <div class="top-overview-flex">
            <div class="overview-box">
              <span class="box-num">{{totalList[0]}}</span>
              <span class="box-name">故障数</span>
            </div>
            <div class="overview-box">
              <span class="box-num">{{totalList[1]}}</span>
              <span class="box-name">报警数</span>
            </div>
            <div class="overview-box">
              <span class="box-num">{{totalList[2]}}</span>
              <span class="box-name">离线数</span>
            </div>
          </div>
        </div>
        <div class="tab-content">
          <div v-for="(item, index) in tabsList" @click="judgeIsSame(index)" :class="{ textStyle: curArea == index }">
            <span class="title">{{ item }}</span>
            <span :class="{ tabItemActive: curArea == index }"></span>
          </div>
        </div>
      </div>
      <div class="monitor-body">
        <div v-if="curArea == 0" class="body-device-monitor">
          <slot name="body"></slot>
        </div>
        <div v-else class="body-warn-analysis">
          <div class="box-title">报警趋势</div>
          <div class="search-btns">
            <div class="type-btn" v-for="(item, index) in searchBtns" @click="changeActiveBtn(item.type)" :class="{ 'active-style': activeBtn == item.type }">
              <span class="title">{{ item.label }}</span>
            </div>
          </div>
          <div class="warnAnalysisBox">
            <div v-show="lineYearData.length > 0" id="warnAnalysisEchart"></div>
            <div v-show="!lineYearData.length" class="executeCharts">
              <img height="100%" src="../../../../assets/images/equipmentManagement/缺省-图表@2x.png" alt="" />
              <span class="nullText">暂无数据</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  name: "securityContainer",
  props: {
    tabsList: {
      type: Array,
      default: () => ["设备监测", "报警趋势"]
    },
    monitorData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      curArea: 0,
      activeBtn: "day",
      searchBtns: [
        {
          label: "今天",
          type: "day",
          dataRange: [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD")],
          dateFlag: 'HOUR'
        },
        {
          label: "近7天",
          type: "week",
          dataRange: [
            moment()
              .subtract(7, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD")
          ],
          dateFlag: 'DATE'
        },
        {
          label: "近30天",
          type: "month",
          dataRange: [
            moment()
              .subtract(30, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD")
          ],
          dateFlag: 'DATE'
        }
      ],
      lineYearData: [1],
      totalList: [0, 0, 0]
    };
  },
  created() {},
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getCountWarnByTime();
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    // 获取报警统计总览
    getCountWarnByTime() {
      const params = {
        projectCode: this.monitorData.projectCode
      };
      this.$api.airViewDetailApp(params).then(res => {
        this.totalList[2] = res.offLineCount
        this.totalList[0] = res.faultCount
      });
      this.$api.airRunPolice(params).then(res => {
        this.totalList[1] = res ? res.total : 0
      });
    },
    // 获取报警趋势分析折线图数据
    getWarnAnalysisData() {
      const selectData = this.searchBtns.find(e => e.type === this.activeBtn);
      const params = {
        startTime: selectData.dataRange[0],
        endTime: selectData.dataRange[1],
        dateFlag: selectData.dateFlag,
        projectCode: this.monitorData.projectCode
      };
      this.$api.queryAlarmTrendFromTime(params).then(res => {
        console.log(res, "queryAlarmTrendFromTime");
        let xAxisData = [];
        let seriesData = [];
        let legendData = [];
        if (res.length) {
          res.forEach(item => {
            xAxisData.push(item.time);
            item.param.forEach((param, index) => {
              if (seriesData[index]) {
                seriesData[index].data.push(param.paramValue);
              } else {
                legendData.push(param.paramName);
                seriesData[index] = {
                  name: param.paramName,
                  type: "line",
                  data: [param.paramValue],
                  symbol: "circle", //将小圆点改成实心 不写symbol默认空心
                  symbolSize: 6, //小圆点的大小
                }
              }
            })
          })
          this.$nextTick(() => {
            this.setWarnAnalysisEcharts(xAxisData, seriesData, legendData);
          });
        }
      });
    },
    // 报警趋势分析折线图
    setWarnAnalysisEcharts(xAxisData, seriesData, legendData) {
      const getchart = this.$echarts.init(document.getElementById("warnAnalysisEchart"));
      getchart.setOption({
        tooltip: {
          trigger: "axis",
          show: true,
          backgroundColor: "#fff",
          borderColor: "rgba(0, 0, 0,0.12)",
          borderWidth: 1,
          textStyle: {
            // 文字提示样式
            color: "#000000",
            fontSize: "13"
          },
          axisPointer: {
            // 坐标轴虚线
            type: "cross",
            label: {
              backgroundColor: "#6a7985"
            }
          }
        },
        legend: {
          type: 'scroll',
          icon: "circle",
          left: 'center',
          orient: 'horizontal',
          itemWidth: 10,
          itemHeight: 10,
          data: legendData
        },
        grid: {
          left: "1%",
          right: "4%",
          top: "20%",
          bottom: "4%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          nameLocation: "start",
          data: xAxisData,
          axisLabel: {
            color: "#86909C", //刻度线标签颜色
            interval: 0, //X轴内容过多造成内容缺失时加上这个内容就会显示出来
            formatter(val) {
              if (val.length > 5) {
                return val.slice(0, 4) + "...";
              } else {
                return val;
              }
            }
          },
          axisLine: {
            show: true, //是否显示轴线
            lineStyle: {
              color: "#86909C" //刻度线的颜色
            }
          },
          axisTick: {
            alignWithLabel: true // 刻度与文案居中对齐
          }
        },
        yAxis: {
          type: "value",
          name: "单位: 件",
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: false // 不显示坐标轴线
          },
          axisLabel: {
            color: "#86909C", //刻度线标签颜色
            margin: 2
            // formatter: function(value, index) {
            //   if (value >= 10000 && value < 10000000) {
            //     value = value / 10000 + "万";
            //   } else if (value >= 10000000) {
            //     value = value / 10000000 + "千万";
            //   }
            //   return value;
            // }
          },
          grid: {
            left: 35
          }
        },
        dataZoom: [
          {
            type: "slider",
            show: xAxisData.length > 6, // 是否显示滑动条
            realtime: true,
            startValue: 0,
            endValue: 4,
            height: 6,
            top: "96%",
            borderRadius: 0,
            borderColor: "#D7DEE8",
            fillerColor: "#C4C4C4", // 滑动块的颜色
            // backgroundColor: "#33384b", //两边未选中的滑动条区域的颜色
            // 是否显示detail，即拖拽时候显示详细数值信息
            showDetail: false,
            zoomLock: false,
            brushSelect: false,
            // 控制手柄的尺寸
            handleSize: 8,
            // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
            showDataShadow: false,
            // filterMode: 'filter',
            handleIcon: "M0,0 v11h3 v-11h-3 Z",
            handleStyle: {
              color: "#FFF",
              shadowOffsetX: 0, // 阴影偏移x轴多少
              shadowOffsetY: 0 // 阴影偏移y轴多少
              // borderCap: 'square',
              // borderColor: '#D8DFE9',
              // borderType: [15, 20],
            }
          },
          {
            type: "inside", // 支持内部鼠标滚动平移
            start: 0,
            end: 40,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        series: seriesData
        // series: [
        //   {
        //     name: "温度报警",
        //     data: valueList,
        //     type: "line",
        //     symbol: "circle", //将小圆点改成实心 不写symbol默认空心
        //     symbolSize: 6, //小圆点的大小
        //     color: "#FF9F42"
        //   },
        //   {
        //     name: "漏电报警",
        //     data: value2List,
        //     type: "line",
        //     symbol: "circle", //将小圆点改成实心 不写symbol默认空心
        //     symbolSize: 6, //小圆点的大小
        //     color: "#F53F3F"
        //   }
        // ]
      });
    },
    // tabs切换选中
    judgeIsSame(index) {
      this.curArea = index;
      this.$emit("tabChange", this.curArea);
      if (index == 1) {
        this.getWarnAnalysisData();
      }
    },
    // 报警趋势日期切换选中
    changeActiveBtn(type) {
      this.activeBtn = type;
      this.getWarnAnalysisData();
    }
  }
};
</script>

<style lang="scss" scope>
$bgColor: #efeff4;
$color: #3562db;
$btnColor: #3562db;
.monitor {
  height: 100%;
  .monitor-content {
    height: calc(100% - 10vh);
    display: flex;
    flex-direction: column;
  }
  .monitor-top {
    width: 100%;
    background: #fff;
    padding: 10px 10px 0 10px;
    box-sizing: border-box;
    .top-overview {
      margin-top: 10px;
      background: #f7f8fa;
      border-radius: 8px;
      padding: 20px 0;
      .top-overview-flex {
        display: flex;
        justify-content: space-around;
      }
      .overview-box {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        text-align: center;
        .box-num {
          font-size: 18px;
          font-family: Arial-Bold, Arial;
          font-weight: bold;
          color: #1d2129;
          margin-bottom: 8px;
        }
        .box-name {
          font-size: 14px;
          font-family: PingFang HK-Regular, PingFang HK;
          font-weight: 400;
          color: #4e5969;
        }
      }
    }
    .tab-content {
      width: 100%;
      height: 0.88rem;
      display: flex;
      font-size: 0.3rem;
      background-color: #fff;
      color: #86909c;
      font-family: PingFang SC-Regular, PingFang SC;
      position: relative;
      .textStyle .title {
        color: #1d2129;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: 600 !important;
      }
      div {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        line-height: 100%;
        .title {
          padding: 0.17rem 0 0.23rem 0;
          font-size: 16px;
          font-family: PingFang HK-Light, PingFang HK;
        }
        .num {
          font-size: 0.4rem;
        }
      }
      .tabItemActive {
        position: absolute;
        width: 24px;
        height: 3px;
        border-radius: 2px;
        background: $btnColor;
        bottom: calc(3px);
      }
    }
  }
  .monitor-body {
    flex: 1;
    overflow: hidden;
    .body-device-monitor {
      height: 100%;
    }
    .body-warn-analysis {
      margin-top: 10px;
      height: calc(100% - 10px);
      box-sizing: border-box;
      background: #fff;
      padding: 0 16px;
      .box-title {
        padding: 16px 0;
        font-size: 16px;
        font-family: PingFang HK-Regular, PingFang HK;
        font-weight: 400;
        color: #1d2129;
      }
      .search-btns {
        display: flex;
        justify-content: space-between;
        padding-bottom: 15px;
        .type-btn {
          width: 30%;
          height: 36px;
          line-height: 36px;
          text-align: center;
          background: #f7f8fa;
          font-size: 14px;
          font-family: PingFang HK-Light, PingFang HK;
          font-weight: 300;
          color: #4e5969;
        }
        .active-style {
          background: #e6effc;
          color: #3562db;
        }
      }
      .warnAnalysisBox {
        width: 100%;
        height: auto;
        aspect-ratio: 12/9;
        & > div {
          width: 100%;
          height: 100%;
          text-align: center;
          position: relative;
          .nullText {
            position: absolute;
            bottom: 1rem;
            left: 43%;
          }
        }
      }
    }
  }
}
</style>
