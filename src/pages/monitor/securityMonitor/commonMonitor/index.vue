<!--
 * @Author: hedd
 * @Date: 2023-10-19 18:20:45
 * @LastEditTime: 2023-11-08 15:24:35
 * @FilePath: \ybs_h5\src\pages\monitor\securityMonitor\commonMonitor\index.vue
 * @Description:
-->
<template>
  <div class="inner">
    <securityContainer v-if="JSON.stringify(monitorData) !== '{}'" :tabsList="tabsList" :monitorData="monitorData" @tabChange="tabChange">
      <van-pull-refresh slot="body" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-if="entitylist.length" v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div v-for="(item, i) in entitylist" :key="i" :class="{
            'entity-list': true,
            'entity-warn': item.alarm === '0'
          }">
            <div class="title">
              <span class="task-name">{{ item.surveyName }}</span>
            </div>
            <div class="point">
              <div v-for="(parameter, j) in item.paramList" :key="j" class="pointItem">
                <span class="pointTitle" v-trunced:slFlag="parameter">{{ parameter.paramName }}</span>
                <span class="count">{{ parameter.paramUnit ? (parameter.paramValue || "") + parameter.paramUnit : parameter.paramValue }}</span>
              </div>
            </div>
          </div>
        </van-list>
        <ybsEmpty v-else imageType="searchEmpty" height="100%" />
      </van-pull-refresh>
    </securityContainer>
  </div>
</template>

<script>
import { monitorTypeList } from "../../components/dict.js";
import securityContainer from "../components/securityContainer.vue";
export default {
  components: {
    securityContainer
  },
  data() {
    return {
      monitorData: {},
      tabsList: ["设备监测", "报警趋势"],
      activeTab: 0,
      isLoading: true,
      entitylist: [],
      loading: true,
      finished: false,
      pageParmes: {
        page: 1,
        pageSize: 10
      }
    };
  },
  created() {
    this.monitorData = monitorTypeList.find(item => item.monitorRouter === this.$route.name);
  },
  mounted() {
    this.pageParmes.page = 1;
    this.entitylist = [];
    this.getListData();
  },
  methods: {
    // 子组件tabs切换事件
    tabChange(val) {
      this.activeTab = val;
    },
    // 下拉刷新
    onRefresh() {
      this.pageParmes.page = 1;
      this.finished = false;
      this.loading = true;
      this.entitylist = [];
      this.getListData();
    },
    // 加载更多
    onLoad() {
      this.pageParmes.page++;
      this.finished = false;
      this.loading = true;
      this.getListData();
    },
    // 获取列表数据
    getListData() {
      const params = {
        currentPage: this.pageParmes.page,
        pageSize: this.pageParmes.pageSize,
        projectCode: this.monitorData.projectCode
      };
      // this.$YBS.auiToast.loading()
      this.$api['querySurveyListPageByMenuCode'](params).then(res => {
        // this.$YBS.auiToast.hide()
        this.entitylist = this.entitylist.concat(res.list);
        this.isLoading = false;
        this.finished = this.pageParmes.pageSize * this.pageParmes.page >= res.count;
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="scss" scope>
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
  .listItem {
    height: 100%;
    padding-top: 10px;
    overflow-y: auto;
    box-sizing: border-box;
    background-color: #f2f4f9;
    /deep/ .van-list {
      padding-top: 10px;
      // box-sizing: border-box;
    }
    .entity-list {
      width: calc(100% - 20px);
      padding: 0.4rem 0.32rem 0 0.32rem;
      border-radius: 8px;
      position: relative;
      font-size: 0.32rem;
      background-color: #fff;
      // margin-bottom: 10px;
      margin: 0 10px 10px 10px;
      border: 1px solid #fff;
      box-sizing: border-box;
      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .task-name {
          color: #1d2129;
          font-weight: 600;
        }
      }
      .point {
        display: flex;
        flex-wrap: wrap;
        margin-top: 0.32rem;
        .pointItem {
          min-width: 50%;
          max-width: 100%;
          display: flex;
          align-items: center;
          margin-bottom: 0.32rem;
          .pointTitle {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #4e5969;
            margin-right: 0.48rem;
            padding: 0;
            border: 0;
          }
          .count {
            color: #1d2129;
          }
        }
        .w50 {
          width: 50%;
        }
        .w100 {
          width: 100%;
        }
      }
    }
    .entity-warn {
      background: #FFECE8;
      border: 1px solid #F53F3F;
      box-sizing: border-box;
    }
  }
}
</style>
