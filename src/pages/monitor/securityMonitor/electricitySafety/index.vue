<!--
 * @Author: hedd
 * @Date: 2023-10-19 18:20:45
 * @LastEditTime: 2023-12-06 10:29:01
 * @FilePath: \ybs_h5\src\pages\monitor\securityMonitor\electricitySafety\index.vue
 * @Description:
-->
<template>
  <div class="inner">
    <securityContainer v-if="JSON.stringify(monitorData) !== '{}'" :tabsList="tabsList" :monitorData="monitorData" @tabChange="tabChange">
      <div v-if="activeTab == 0" slot="overview" class="top-overview-flex" style="flex-direction: column">
        <div class="top-overview-data">
          <div class="overview-box">
            <span class="box-num">{{ statisticsList.monitoringPointNum || 0 }}</span>
            <span class="box-name">监控点总数</span>
          </div>
          <div class="overview-box">
            <span class="box-num">{{ statisticsList.normalNodeNum || 0 }}</span>
            <span class="box-name">正常点数</span>
          </div>
          <div class="overview-box">
            <span class="box-num">{{ statisticsList.abnormalNodeNum || 0 }}</span>
            <span class="box-name">异常点数</span>
          </div>
          <div class="overview-box">
            <span class="box-num">{{ statisticsList.offlineNodeNum || 0 }}</span>
            <span class="box-name">离线点数</span>
          </div>
        </div>
        <div class="group-overview">监控区数<span>{{ statisticsList.monitoringAreaNum || 0 }}</span></div>
      </div>
      <div v-if="activeTab == 1" slot="overview" class="top-overview-flex">
        <div class="overview-box">
          <span class="box-num">{{warnStatistics.todayWarnNum}}</span>
          <span class="box-name">今日温度报警数</span>
        </div>
        <div class="overview-box">
          <span class="box-num">{{warnStatistics.todayLeakageWarnNum}}</span>
          <span class="box-name">今日漏电报警数</span>
        </div>
        <div class="overview-box">
          <span class="box-num">{{warnStatistics.notHandleWarnNum}}</span>
          <span class="box-name">未处理报警数</span>
        </div>
      </div>
      <div slot="body" class="content">
        <div class="search" @click="show = true">
          <span>{{menuConfirm.menuName}}</span>
          <van-icon name="arrow-down" />
        </div>
        <div class="top-overview-data">
          <div class="overview-box">
            <span class="box-num">{{ menuData.monitoringPointNum || 0 }}</span>
            <span class="box-name">监控点总数</span>
          </div>
          <div class="overview-box">
            <span class="box-num">{{ menuData.normalNodeNum || 0 }}</span>
            <span class="box-name">正常点数</span>
          </div>
          <div class="overview-box">
            <span class="box-num">{{ menuData.abnormalNodeNum || 0 }}</span>
            <span class="box-name">异常点数</span>
          </div>
          <div class="overview-box">
            <span class="box-num">{{ menuData.offlineNodeNum || 0 }}</span>
            <span class="box-name">离线点数</span>
          </div>
        </div>
        <div class="frame_list">
          <van-list
            v-if="demoList.length"
            v-model="listLoading"
            :error.sync="error"
            error-text="请求失败，点击重新加载"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <div
              v-for="(item, index) in demoList"
              :key="index"
              :class="{ frame_item: true, normalList: item.onlineOrOffline != '离线' && item.alarm != '1', errorList: item.alarm == '0', downList: item.onlineOrOffline == '离线'}"
              @click="toDetails(item)"
            >
              <div class="frame_item_header">
                <p class="headerTitle">{{ item.surveyName }}</p>
                <div class="entity_type">
                  <img v-if="item.alarm == '0'" src="@/assets/images/monitor/abnormal.png" alt="">
                  <img v-else-if="item.onlineOrOffline == '离线'" src="@/assets/images/monitor/offLine.png" alt="" />
                  <img v-else src="@/assets/images/monitor/onLine.png" alt="">
                  <p>{{ item.alarm == '0' ? '异常' : (item.onlineOrOffline || '在线') }}</p>
                </div>
              </div>
              <div class="frame_item_content">
                <div class="content_parameter" v-for="(child, key) in item.paramList" :key="key">
                  <p class="childLabel">{{ child.paramName }}</p>
                  <div class="childValue_div">
                    <span class="childValue" :class="child.warn == '0' ? 'childValueRed' : ''">{{ (isNaN(child.paramValue) ? child.paramValue : Number(child.paramValue).toFixed(2)) || '-' }}{{ child.paramUnit }}</span>
                    <img v-if="child.warn == '0'" src="@/assets/images/error.png" alt="">
                  </div>
                </div>
              </div>
            </div>
          </van-list>
        </div>
        <van-popup v-model="show" round position="bottom">
            <van-picker
              title="选择监控区"
              show-toolbar
              value-key="menuName"
              :columns="groupList"
              @confirm="onConfirm"
              @cancel="show = false"
            >
              <template #option="option">
                <span>{{ option.menuName }}</span>
                <span v-if="option.alarm == '0'" class="alarm_dig">报警</span>
              </template>
            </van-picker>
        </van-popup>

      </div>
    </securityContainer>
  </div>
</template>

<script>
import { monitorTypeList } from "../../components/dict.js";
import securityContainer from "../components/securityContainer.vue";
import moment from 'moment'
export default {
  name: "electricitySafetyMonitor",
  components: {
    securityContainer
  },
  data() {
    return {
      queryParams:{
        page: 1,
        pageSize: 10
      },
      // 是否处于加载状态，加载过程中不触发load事件
      listLoading:false,
      // 是否已加载完成，加载完成后不再触发load事件
      finished:false,
      error:false,
      statisticsList: {},
      monitorData: {},
      warnStatistics: {},
      tabsList: ["监控区", "报警趋势"],
      activeTab: 0,
      groupList: [],
      menuConfirm: {},
      menuData:{},
      show: false,
      demoList: []
    };
  },
  created() {
    this.monitorData = monitorTypeList.find(item => item.projectName === "用电安全");
  },
  mounted() {
    this.getQueryMenuGroup()
    this.getQuerySurveyCount()
    this.getList()
    this.countWarnByTime()
  },
  methods: {
    toDetails(item) {
      this.$router.push({
        path: '/monitorDetails',
        query:{
          surveyCode:item.surveyCode,
          projectCode: this.monitorData.projectCode
        }
      })
    },
    // 到达底部
    onLoad(){
      this.queryParams.page++
      this.finished = false;
      this.listLoading = true;
      this.getList()
    },
    countWarnByTime() {
      const params = {
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      this.$api.countWarnByTime(params).then(res => {
        this.warnStatistics = res
      })
    },
    getMenuData() {
      let data = {
        projectCode: this.monitorData.projectCode,
        menuCode: this.menuConfirm.menuCode
      }
      this.$api.countMonitorPointByMenuCode(data).then(res => {
        this.menuData = res
      })
    },
    getList() {
      let data = {
        projectCode: this.monitorData.projectCode,
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD'),
        currentPage: this.queryParams.page,
        pageSize: this.queryParams.pageSize,
        menuCode: this.menuConfirm.menuCode
      }
      this.$api.queryMenuByPage(data).then(res => {
        if(res){
          this.listLoading = false
          this.demoList = [...this.demoList,...res.list]
          this.finished = this.queryParams.pageSize * this.queryParams.page >= res.count;
          // if(!res.list.length){
          //   this.finished = true
          // }
        }else{
          this.error = true
        }
      },err=>{
        this.listLoading = false
        this.error = true
      })
    },
    onConfirm(value) {
      console.log(value)
      this.menuConfirm = value
      this.show = false
      this.finished = false
      this.queryParams.page = 1
      this.demoList = []
      this.getMenuData()
      this.getList()
    },
    getQueryMenuGroup() {
      this.$api.queryMenuGroup({projectCode: this.monitorData.projectCode}).then(res => {
        this.groupList = [
          {
            alarm: '',
            menuCode: '',
            menuName: '全部监控区'
          },
          ...res
        ]
        this.menuConfirm = this.groupList[0]
        this.getMenuData()
      })
    },
    getQuerySurveyCount() {
      let data = {
        projectCode: this.monitorData.projectCode,
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      this.$api.querySurveyCount(data).then(res => {
        this.statisticsList = res
      })
    },
    // 子组件tabs切换事件
    tabChange(val) {
      this.activeTab = val;
    }
  }
};
</script>

<style lang="scss" scope>
.alarm_dig{
  background: #FFECE8;
  border-radius: 2px 2px 2px 2px;
  font-size: 12px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #F53F3F;
  line-height: 17px;
  padding: 2px 5px;
  margin-left: 5px;
}
.search_header {
  background: #ffffff;
}

.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;

  .top-overview-flex {
    .top-overview-data {
      display: flex;
      justify-content: space-around;

      .overview-box:first-child {
        border-right: 1px solid #e5e6eb;
      }

      .overview-box:nth-child(2) .box-num {
        color: #3562db;
      }

      .overview-box:nth-child(3) .box-num {
        color: #f53f3f;
      }
    }
  }

  .group-overview {
    font-size: 14px;
    font-family: PingFang HK-Regular, PingFang HK;
    font-weight: 400;
    color: #4e5969;
    text-align: center;
    margin-top: 20px;

    span {
      font-size: 18px;
      font-family: Arial-Bold, Arial;
      font-weight: bold;
      color: #1d2129;
      margin-left: 20px;
    }
  }

  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    .search {
      padding: 12px;
      background: #fff;
      border-bottom: 1px solid #e5e6eb;
      .van-icon {
        font-size: 12px;
      }
    }

    .top-overview-data {
      display: flex;
      justify-content: space-around;
      background: #fff;
      padding: 10px 0px 14px;
      .overview-box {
        display: flex;
        flex-direction: column;
        text-align: center;
        .box-num {
          font-size: 18px;
          font-family: Arial-Bold, Arial;
          font-weight: bold;
          color: #1d2129;
          margin-bottom: 8px;
        }
        .box-name {
          font-size: 14px;
          font-family: PingFang HK-Regular, PingFang HK;
          font-weight: 400;
          color: #4e5969;
        }
      }
      .overview-box:first-child {
        padding-right: 10px;
        border-right: 1px solid #e5e6eb;
      }

      .overview-box:nth-child(2) .box-num {
        color: #3562db;
      }

      .overview-box:nth-child(3) .box-num {
        color: #f53f3f;
      }
    }

    .top-overview-flex {
      background: #fff;
    }

    .frame_list {
      flex: 1;
      overflow: auto;
      padding: 10px;
      .frame_item {
        background: #fff;
        border-radius: 8px;
        padding: 15px 20px;
        margin-bottom: 10px;

        .frame_item_header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .headerTitle {
            font-weight: 600;
          }
          .entity_type {
            display: flex;
            font-size: 12px;
            padding: 6px 8px;
            border-radius: 2px;

            img {
              margin-right: 4px;
            }
          }
        }

        .frame_item_content {
          min-height: 90px;
          .content_parameter {
            display: inline-flex;
            width: 45%;
            justify-content: space-between;
            margin-right: 10%;
            height: 26px;
            line-height: 26px;

            &:nth-of-type(2n) {
              margin-right: 0px;
            }

            .childLabel {
              font-size: 16px;
              font-family: PingFang HK-Light, PingFang HK;
              font-weight: 300;
              color: #4E5969;
            }
            .childValue_div{
              display: flex;
              align-items: center;
              img{
                width: 16px;
                height: 16px;
                margin-left: 5px;
              }
            }
            .childValue {
              font-size: 16px;
              font-family: PingFang HK-Regular, PingFang HK;
              font-weight: 400;
              color: #1D2129;
            }
            .childValueRed{
              color: #F53F3F;
            }
          }
        }
      }

      .errorList {
        border: 1px solid#F53F3F;
        background: #ffece8;

        .frame_item_header {
          color: #f53f3f;
        }

        .entity_type {
          background: transparent !important;
          p{
            color: #F53F3F;
          }
        }
      }

      .downList {
        opacity: .8;
        .entity_type {
          background: #f2f3f5;
        }

        .childLabel {
          color: #4e5969;
        }
      }

      .normalList {
        .entity_type {
          background: #e8ffea;
          color: #00b42a;
        }

        .childLabel {
          color: #f2f3f5;
        }
      }
    }
  }
}
</style>
