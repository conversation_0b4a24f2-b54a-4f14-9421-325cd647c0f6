<template>
    <div class="monitorDetailBox">
        <Header title="监测设备详情" @backFun="goBack"></Header>
        <div class="content">
            <div class="topBox">
                <img src="@/assets/images/monitor/camera.png" alt="">
                <div class="topRight">
                    <div class="eachRow">
                        <span class="name">当前监控区域</span>
                        <span class="value">{{equipmentData.surveyName}}</span>
                    </div>
                    <div class="eachRow">
                        <span class="name">当前监控位置</span>
                        <span class="value">{{equipmentData.locate}}</span>
                    </div>
                    <div class="eachRow">
                        <span class="name">监测设备类型</span>
                        <span class="value">{{equipmentData.deviceType}}</span>
                    </div>
                </div>
            </div>
            <div class="middleBox">
                <el-row>
                    <el-col :span="8" class="parameter_item" v-for="(item, index) in equipmentData.paramList" :key="index">
                        <div class="parameter">
                            <span class="parameter_num" :style="false ? {'color': '#F53F3F'} : ''">{{ item.paramValue }}</span>
                            <span class="parameter_unit" :style="false ? {'color': '#F53F3F'} : ''">{{ item.paramUnit }}</span>
                            <!-- <img src="@/assets/images/error.png" alt=""> -->
                        </div>
                        <p>{{ item.paramName }}</p>
                    </el-col>
                </el-row>
            </div>
            <div class="bottomBox">
                <el-row>
                    <el-col :span="8" class="parameter_item">
                        <div class="parameter">
                            <span class="parameter_num">{{equipmentData.warnStatus == '1' ? '正常' : '异常'}}</span>
                        </div>
                        <p>报警状态</p>
                    </el-col>
                    <el-col :span="8" class="parameter_item">
                        <div class="parameter">
                            <span class="parameter_num">{{equipmentData.isOffline || '在线'}}</span>
                        </div>
                        <p>在线状态</p>
                    </el-col>
                    <el-col :span="8" class="parameter_item">
                        <div class="parameter">
                            <span class="parameter_num">{{equipmentData.deviceStatus == '1' ? '正常' : '异常'}}</span>
                        </div>
                        <p>设备状态</p>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            equipmentData: {}
        };
    },
    mounted() {
        this.getData()
    },
    methods: {
        getData() {
            let data = {
                surveyCode: this.$route.query.surveyCode,
                projectCode: this.$route.query.projectCode
            }
            this.$api.querySurveyDetail(data).then(res => {
                let obj = res.paramList.find(ele => ele.parameterId == '30361')
                res.isOffline = obj ? obj.paramValue : ''
                this.equipmentData = res
            })
        },
        goBack() {
            this.$router.go(-1);
        }
    }
};
</script>

<style lang="scss" scope>

.monitorDetailBox {
    background: #F2F4F9;
    height: 100%;
    .content {
        padding: 10px;
        .middleBox,.bottomBox{
            background: #FFFFFF;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            .parameter_item{
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 16px 0;
                .parameter{
                    display: flex;
                    align-items: center;
                    .parameter_num{
                        font-size: 18px;
                        font-family: Arial-Bold, Arial;
                        font-weight: bold;
                        color: #1D2129;
                    }
                    .parameter_unit{
                        font-size: 13px;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #86909C;
                        margin: 0 5px;
                    }
                    img{
                        height: 16px;
                        width: 16px;
                    }
                }
                p{
                    font-size: 14px;
                    font-family: PingFang HK-Regular, PingFang HK;
                    font-weight: 400;
                    color: #86909C;
                    margin-top: 10px;
                }
            }
        }
        // .bottomBox{
        //     background: #FFFFFF;
        //     border-radius: 8px;
        //     padding: 16px;
        // }
        .topBox {
            display: flex;
            align-items: center;
            background: #FFFFFF;
            border-radius: 8px;
            padding: 16px;
            img {
                width: 42px;
                height: 42px;
            }  
            .topRight {
                margin-left: 16px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                .eachRow {
                    margin-top: 8px;
                    display: flex;
                    .name {
                        display: inline-block;
                        color: #4E5969;
                        margin-right: 8px;
                        width: 100px;
                    }
                    .value {
                        color: #1D2129;  
                        flex: 1;
                        font-size: 15px;
                        font-weight: 400;
                    }
                }
            }
        }
    }
}
</style>