<template>
  <Overview :projectCode="projectCode" title="风冷热泵监测" />
</template>

<script>
import { monitorTypeList } from "./../components/dict.js";
import Overview from './../components/overview/index.vue'
export default {
  data() {
    return {
      projectCode: monitorTypeList.find(
        item => item.projectName === '风冷热泵监测'
      ).projectCode,
    }
  },  
  components: {
    Overview
  }
}
</script>

