<!-- 冷热源监测 -->
<template>
  <div class="inner">
    <Header :title="projectName" @backFun="goback"></Header>
    <div class="content_page">
      <van-grid
        :column-num="2"
        :gutter="10"
        :border="false"
        :center="false"
        style="margin:10px 0;"
      >
        <van-grid-item v-for="item in waterTotalData" :key="item.name">
          <template slot="default">
            <div class="grid_box">
              <h3>{{ item.name }}</h3>
              <div class="box_content">
                <img :src="item.icon" alt="" />
                <div class="box_content_r">
                  <p>{{ item.count }}</p>
                  <span>{{ item.company }}</span>
                </div>
              </div>
            </div>
          </template>
        </van-grid-item>
      </van-grid>
      <van-tabs
        v-model="active"
        sticky
        swipeable
        offset-top="10vh"
        title-inactive-color="#86909C"
        title-active-color="#1D2129"
        color="#3562DB"
        animated
        line-height="3px"
        class="my_vant_tabs_cls"
        @change="avtiveTabChange"
      >
        <van-tab
          v-for="item in tabList"
          :key="item.code"
          :title="item.name"
          :name="item.code"
          :title-style="{ fontSize: '0.32rem' }"
        >
          <div class="tabs_content">
            <!-- <div class="function_chart" v-if="item.code != '#'">
              <div class="chart_name">
                <img src="@/assets/images/monitor/imageIcon.png" alt="" />
                <p>运行系统图</p>
              </div>
              <span @click="jupmGroupListPage(item.code)">查看</span>
            </div> -->

            <div class="grid_err">
              <div
                class="err_item bag-grey"
                @click="jumpVariousTypesPage('/abnormalList', {abnormalType: 'offLine'})"
              >
                <img src="@/assets/images/monitor/icon1.png" alt="" />
                <p class="color-grey">离线</p>
                <span class="color-grey">{{ tabMenuData.offLineCount }}</span>
              </div>
              <div
                class="err_item bag-orange"
                @click="jumpVariousTypesPage('/abnormalList', {abnormalType: 'fault'})"
              >
                <img src="@/assets/images/monitor/icon2.png" alt="" />
                <p class="color-orange">故障</p>
                <span class="color-orange">{{ tabMenuData.faultCount }}</span>
              </div>
              <div
                class="err_item bag-orange"
                @click="jumpVariousTypesPage('/callThePolice', {entityMenuCode: item.child || ''})"
              >
                <img src="@/assets/images/monitor/icon3.png" alt="" />
                <p class="color-orange">报警</p>
                <span class="color-orange">{{ policeCount }}</span>
              </div>
            </div>

            <div class="equipment_list">
              <div
                class="equipment_item"
                v-for="(ele, k) in tabMenuData.data"
                :key="k"
                @click="jupmMonitorRunPage(ele)"
              >
                <p>{{ ele.entityTypeName }}</p>
                <div class="item_bot">
                  <div class="count">
                    <span style="color:#30B75E;">{{ ele.workCount }}</span>
                    <span>/{{ ele.totalCount }}</span>
                  </div>
                  <div class="repair">
                    <img src="@/assets/images/monitor/icon6.png" alt="" />
                    <span>{{ ele.faultCount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { monitorTypeList } from "./../components/dict.js";
export default {
  data() {
    const projectName = "冷热源监测";
    return {
      projectName,
      projectCode: monitorTypeList.find(
        item => item.projectName === projectName
      ).projectCode,
      active: "#",
      tabList: [],
      menuList: [],
      policeCount: 0,
      tabMenuData: { airView: [] },
      waterTotalData: [
        {
          name: '总供冷量',
          icon: require('@/assets/images/monitor/coldAndHot0.png'),
          count: 0,
          company: 'W'
        },
        {
          name: '总供热量',
          icon: require('@/assets/images/monitor/coldAndHot1.png'),
          count: 0,
          company: 'W'
        },
        {
          name: '总耗电量',
          icon: require('@/assets/images/monitor/coldAndHot2.png'),
          count: 0,
          company: 'kw'
        },
        {
          name: '总耗水量',
          icon: require('@/assets/images/monitor/coldAndHot3.png'),
          count: 0,
          company: 't'
        },
      ]
    };
  },
  mounted() {
    this.getColdOverview();
    this.getEntityMenuList();
    this.getPoliceCount();
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    // tab切换
    avtiveTabChange() {
      const active = this.active == '#' ? '' : this.active
      this.getColdHostStatistics(active);
      this.getPoliceCount(active)
    },
    // 跳转分组列表页
    jupmGroupListPage(entityMenuCode) {
      console.log('this.menuList=======', this.menuList);
      let path = "";
      // 如果有二级分组，跳转到分组列表页；如果没有，跳转到设备列表页
      if (this.menuList.some(e => e.parentId == entityMenuCode)) {
        path = "/monitorGroup";
      } else {
        path = "/monitorEntityPage";
      }
      this.$router.push({
        path: path,
        query: {
          projectCode: this.projectCode,
          entityMenuCode: entityMenuCode
        }
      });
    },
    // 跳转运行设备列表页
    jupmMonitorRunPage(item) {
      this.$router.push({
        path: '/monitorRunList',
        query: {
          projectCode: this.projectCode,
          entityMenuCode: this.active === "#" ? "" : this.active,
          entityTypeId: item.entityTypeId
        }
      });
    },
    // 跳转报警清单 故障离线高低液位页
    jumpVariousTypesPage(path, query = {}) {
      let queryData = {
        projectCode: this.projectCode,
        entityMenuCode: this.active === "#" ? "" : this.active
      };
      this.$router.push({
        path: path,
        query: {...queryData, ...query}
      });
    },
    // 获取冷热源统计
    getColdOverview() {
      const params = {
        projectCode: this.projectCode,
        startTime: moment().format("YYYY-MM-DD"),
        endTime: moment().format("YYYY-MM-DD"),
        timeType: 0
      };
      this.$api.GetColdOverview(params).then(res => {
        this.waterTotalData[0].count = res.coldCount;
        this.waterTotalData[1].count = res.hotCount;
        this.waterTotalData[2].count = res.powerCount;
        this.waterTotalData[3].count = res.waterCount;
      });
    },
    // 获取设备分组列表
    getEntityMenuList() {
      const params = {
        projectId: this.projectCode
      };
      this.$api.getEntityMenuList(params).then(res => {
        this.menuList = res;
        const menuList = [
          {
            id: "",
            name: "全部",
            code: "#"
          }
        ].concat(res.filter(item => item.parentId === "#"));
        this.tabList = menuList;
        this.getColdHostStatistics();
      });
    },
    // 获取给排水统计
    getColdHostStatistics(entityMenuCode = "") {
      const params = {
        projectCode: this.projectCode,
        menuCode: entityMenuCode,
        allData: 0
      };
      this.$api.getWaterOverview(params).then(res => {
        this.tabMenuData = res;
      });
    },
    // 获取报警数量
    getPoliceCount(entityMenuCode = "") {
      const params = {
        projectCode: this.projectCode,
        entityMenuCode: entityMenuCode
      }
      this.$api.getPoliceCount(params).then(res => {
        this.policeCount = res
      })
    }
  }
};
</script>

<style scoped lang="stylus">
.color-grey {
  color: #86909c;
}
.color-orange {
  color: #f53f3f;
}
.bag-grey {
  background-color: #f2f3f5;
}
.bag-orange {
  background-color: #ffece8;
}
.bag-blue {
  background-color: #e6effc;
}
.inner {
  height: 100%;
  background-color: #f2f4f9;
}
.grid_box {
  padding: 0 8px;
  h3 {
    font-weight: 400;
    margin-bottom: 0.3rem;
  }
  .box_content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    img {
      width: 40px;
      height: 40px;
    }
  }
  .box_content_r {
    display: flex;
    align-items: center;
    p {
      font-size: 18px;
      font-weight: bold;
    }
    span {
      font-size: 12px;
      color: #86909c;
      margin-left: 5px;
    }
  }
}
.content_page {
  height: 90vh;
  display: flex;
  flex-direction: column;
  /deep/ .van-grid-item__content {
    border-radius: 8px;
  }
}
.my_vant_tabs_cls {
  flex: 1;
  display: flex;
  flex-direction: column;
  /deep/ .van-tabs__content {
    flex: 1;
    .van-tab__pane {
      height: 100%;
    }
  }
}
.tabs_content {
  background: #ffffff;
  padding: 15px;
  height: calc(100% - 30px);
  .function_chart {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 27px 0;
    .chart_name {
      display: flex;
      align-items: center;
      img {
        width: 17px;
        height: 17px;
        margin-right: 5px;
      }
      p {
        font-size: 15px;
      }
    }
    span {
      font-size: 15px;
      color: #3562db;
    }
  }
  .grid_err {
    // margin: 0px 0 10px 0;
    margin-bottom: 10px;
    display: flex;
    grid-gap: 16px;
    .err_item {
      height: 1.2rem;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      p {
        font-size: 14px;
        // color: #F53F3F;
        margin-left: 5px;
        margin-right: 15px;
      }
      span {
        font-size: 18px;
        font-weight: bold;
        // color: #F53F3F;
      }
    }
  }
  .equipment_list {
    width: 100%;
    display: flex;
    grid-gap: 16px;
    flex-wrap: wrap;
    .equipment_item {
      flex: 1;
      min-width: ceil(50% - 16px);
      padding: 16px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #e5e6eb;
      p {
        font-size: 14px;
      }
      .item_bot {
        margin-top: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .count {
          display: flex;
          font-size: 18px;
          font-weight: bold;
        }
        .repair {
          display: flex;
          align-items: center;
          padding: 2px 5px;
          border-radius: 100px;
          border: 1px solid #f53f3f;
          color: #f53f3f;
          font-size: 12px;
          font-weight: 500;
          img {
            margin-right: 5px;
          }
        }
      }
    }
  }
}
</style>

