<!--
 * @Author: hedd
 * @Date: 2023-08-16 16:11:02
 * @LastEditTime: 2023-08-17 10:07:11
 * @FilePath: \ybs_h5\src\pages\monitor\boiler\steamSupply.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header title="供汽监测" @backFun="goback"></Header>
    <div class="eq_table">
      <van-sticky :offset-top="'10vh'">
        <div class="eq_tr">
          <div class="eq_th">设备</div>
          <div class="eq_th">全部</div>
          <div class="eq_th">本年</div>
          <div class="eq_th">本月</div>
        </div>
      </van-sticky>
      <div v-if="airRunRate.length" style="background-color: white;">
        <div class="eq_tr" v-for="(item,index) in airRunRate" :key="index + 'tableData'">
          <div class="eq_td">{{item.surveyName}}</div>
          <div class="eq_td">{{item.allYear}}</div>
          <div class="eq_td">{{item.currentYear}}</div>
          <div class="eq_td">{{item.currentMonth}}</div>
        </div>
      </div>
      <div class="no_data" v-else>
        <span>暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      airRunRate: []
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback)
    this.getDetails()
  },
  methods: {
    getDetails() {
      let query = this.$route.query
      this.$api.energyStatisticsDetail(query).then(res => {
        this.airRunRate = res
      });
    },
    goback() {
      this.$router.go(-1)
    },
  }
}
</script>

<style scoped lang="stylus">
.inner{
  height: 100%;
  background-color: #F2F4F9;
  .eq_table{
    width: 100%;
    .eq_tr{
      width: 100%;
      display: flex
      .eq_th{
        flex: 1
        padding: 16px;
        font-size: 15px;
        font-weight: 400;
        color: #1D2129;
        background: #E6EFFC;
      }
      .eq_td{
        flex: 1
        padding: 16px;
        font-size: 15px;
        font-weight: 400;
        color: #4E5969;
        border-bottom: 1px solid #E5E6EB;
      }
    }
  }
}
</style>
