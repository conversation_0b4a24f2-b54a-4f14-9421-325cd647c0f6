<!--
 * @Author: hedd
 * @Date: 2023-08-16 16:11:02
 * @LastEditTime: 2023-08-17 10:03:38
 * @FilePath: \ybs_h5\src\pages\monitor\boiler\components\boilerMonitoring.vue
 * @Description:
-->
<template>
  <div class="inner">
    <div class="data_analy_list" v-for="(item, index) in listData" :key="item.surveyCode + index" @click="boilerDetails(item)">
      <h3>{{ item.surveyName }}</h3>
      <div class="item_data">
        <div class="item_data_name" v-for="(v, i) in item.paramObj" :key="index + '-' + i">
          <p>{{ v.paramName }}</p>
          <div v-if="v.paramId == '30265'" :style="v.paramValue ? '' : {backgroundColor: '#F53F3F',color: '#fff'}" class="item_btn">{{v.paramValue ? '启动' : '停止'}}</div>
          <div v-else class="item_data_val">
            <span>{{ (v.paramValue || '-') + v.unit }}</span>
            <div class="item_data_status" v-if="v.warnName">
              <van-icon name="warn-o" size="13" :color="v.warnColor" />
              <b :style="{color: v.warnColor}">{{ v.warnName }}</b>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { monitorTypeList } from "../../components/dict.js";
import moment from "moment";
export default {
  data() {
    return {
      projectCode: monitorTypeList.find(
        item => item.projectName === '锅炉监测'
      ).projectCode,
      listData: []
    }
  },
  created() {
    this.getBoilerMonitorList()
  },
  methods: {
    boilerDetails(item) {
      this.$router.push({
        path: '/boilerDetails',
        query: {
          projectCode: this.projectCode,
          surveyCode: item.surveyCode,
          surveyName: item.surveyName
        }
      })
    },
    getBoilerMonitorList() {
      this.$api.GetBoilerMonitorList({currentDate: moment().format("YYYY-MM-DD"), projectCode: this.projectCode}).then(res => {
        this.listData = res
      });
    }
  }
}
</script>

<style scoped lang="stylus">
.inner{
  height: 100%;
  background-color: #F2F4F9;
  .data_analy_list{
    border-radius: 8px;
    background: white;
    padding: 16px;
    margin-top: 10px;
    h3{
      font-size: 16px;
      color: #1D2129;
      margin-bottom: 7px;
    }
    .item_data{
      display: grid;
      grid-gap:5px;
      grid-template-columns: 50% 50%
    }
    .item_data_name{
      margin: 5px 0
      display: flex;
      align-items: center
      p{
        width: 80px
        font-size: 16px;
        color: #4E5969;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .item_data_val{
        span{
          font-size: 16px;
          color: #1D2129;
        }
        .item_data_status{
          display: flex
          align-items: center;
          img{
            width: 12px;
            height: 12px;
          }
          b{
            margin-left: 2px;
            font-size: 12px;
          }
        }
      }
      .item_btn{
        width: 36px;
        height: 24px;
        display: flex;
        justify-content: center
        align-items: center
        background: #E8FFEA;
        border-radius: 2px 2px 2px 2px;
        font-size: 12px;
        font-weight: 500;
        color: #00B42A;
      }
    }
  }
}
</style>
