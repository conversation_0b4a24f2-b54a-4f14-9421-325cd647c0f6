<template>
  <div class="inner">
    <div class="data_analy_list">
      <h3>燃气泄漏</h3>
      <div class="gas_leakage" v-for="(item, index) in gasList" :key="index">
        <div class="gas_leakage_l">
          <van-icon name="location-o" />
          <p>{{ item.monitorName }}：</p>
        </div>
        <div class="gas_leakage_r">
          <span>{{ item.monitorValue }}ppm</span>
          <div class="item_btn" :class="{'item_btn_error': item.isNormal}">{{ item.isNormal ? '报警' : '正常'}}</div>
        </div>
      </div>
    </div>
    <div class="data_analy_list">
      <h3>锅炉统计</h3>
      <div class="grid_statistics">
        <div class="item_grid_statistics" v-for="item in countList" :key="item.text">
          <p>{{ item.value }}</p>
          <span>{{ item.text }}</span>
        </div>
      </div>
    </div>
    <div class="data_analy_list" style="height: 45vh;">
      <h3>报警类型统计</h3>
      <div id="alarmTypeStatistics" style="width: 100%;height: 95%;"></div>
    </div>
  </div>
</template>

<script>
import { monitorTypeList } from "../../components/dict.js";
import moment from "moment";
export default {
  data() {
    return {
      projectCode: monitorTypeList.find(
        item => item.projectName === '锅炉监测'
      ).projectCode,
      gasList: [],
      countList: [
        {text: '总数', value: 0},
        {text: '在线', value: 0},
        {text: '报警', value: 0},
        {text: '报警总数', value: 0},
        {text: '未处理', value: 0},
        {text: '处理中', value: 0},
      ]
    }
  },
  mounted() {
    this.getGasMonitoring()
    this.getBoilerCount()
    this.getAirRunPoliceDetail()
  },
  methods: {
    getAirRunPoliceDetail() {
      let params = {
        page: 1,
        pageSize: 15,
        startTime: '',
        endTime: moment().format("YYYY-MM-DD"),
        projectCode: this.projectCode
      }
      let arr = []
      this.$api.GetAirRunPoliceDetail(params).then(res => {
        this.countList[3].value = res.airRunPolice.total
        this.countList[4].value = res.airRunPolice.noDealCount
        this.countList[5].value = res.airRunPolice.isDealCount
        // res.airRunPolice.policeList.forEach(val => {
        //   arr.push({
        //     name: val.menuName,
        //     value: val.policeCount
        //   })
        // })
        // console.log(arr)
        // this.getAlarmTypeStatistics(arr)
      });
      this.$api.getBoilerByProjectCode(params).then(res => {
        console.log('res===========', res)
        let arr = res.policeList.map(ele => {
          return {
            name: ele.entityTypeName,
            value: ele.count
          }
        })
        this.$nextTick(() => {
          this.getAlarmTypeStatistics(arr)
        })
      })
    },
    // 查询锅炉总数
    getBoilerCount() {
      this.$api.GetBoilerCount({currentDate: moment().format("YYYY-MM-DD"), projectCode: this.projectCode}).then(res => {
        this.countList[0].value = res.total
        this.countList[1].value = res.startTotal
        this.countList[2].value = res.warnTotal
      });
    },
    // 获取燃气
    getGasMonitoring() {
      this.$api.GetGasMonitoring({currentDate: moment().format("YYYY-MM-DD"), projectCode: this.projectCode}).then(res => {
        this.gasList = res
        console.log(this.gasList)
      });
    },
    getAlarmTypeStatistics(data){
      let myChart1 = this.$echarts.init(document.getElementById("alarmTypeStatistics"));
      let total = data.reduce((a,b)=>{
        return a + b.value
      },0)
      console.log(total);
      myChart1.setOption({
        backgroundColor: '#fff',
        color: ['#3562DB', '#FBAF1B', '#86909C'],
        title: [  ],
        legend: {
          left: 'center',
          top: 'bottom',
          orient: 'vertical',
          selectedMode: false,   // 设置是否打开默认点击事件
          itemHeight: 8,
          itemWidth: 8,
          formatter: name => {
            let value
            for (let i = 0; i < data.length; i++) {
              if (data[i].name == name) {
                value = data[i].value
              }
            }
            let proportion = Math.round((value / total) * 100)
            let arr = `{name|${name}}:{value|${value}}{name|百分比}:{value|${proportion}%}`
            return arr
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 16,
                color: '#4E5969',
                fontWeight: 350,
                padding: [0, 5, 0, 5]
              },
              value: {
                width: 40,
                fontSize: 14,
                color: '#4E5969',
                fontWeight: 350,
                padding: [0, 0, 0, 5]
              },
            }
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['35%', '60%'],
            center: ['50%', '40%'],
            data: data,
            labelLine: {
              show: false
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                formatter:'{d}%',
                color:'#353535',
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            // label: {
            //   normal: {
            //     formatter: "{d}%",
            //     textStyle: {
            //       fontWeight: "normal",
            //       fontSize: 14,
            //     },
            //     show: true,
            //     position: "center",
            //   },
            // }
          }
        ]
      })
    },
  }
}
</script>

<style scoped lang="stylus">
.inner{
  height: 100%;
  background-color: #F2F4F9;

  .data_analy_list{
    border-radius: 8px;
    background: white;
    padding: 16px;
    margin-top: 10px;
    h3{
      font-size: 16px;
      color: #1D2129;
      margin-bottom: 7px;
    }
  }
  .grid_statistics{
    display: grid;
    grid-gap:16px;
    grid-template-columns: 33.33% 33.33% 33.33%;
    margin-top: 15px
    .item_grid_statistics{
      display: flex
      flex-direction: column
      justify-content: center
      align-items: center
      padding: 14px 0
      p{
        font-size: 18px;
        font-weight: bold;
        color: #1D2129;
        margin-bottom: 10px
      }
      span{
        font-size: 14px;
        color: #86909C;
      }
    }
  }
  .gas_leakage{
    display: flex
    justify-content: space-between
    align-items: center
    margin-top: 20px
    .gas_leakage_l{
      display: flex
      align-items: center
      p{
        margin-left: 10px
        font-size: 16px;
        color: #4E5969;
      }
    }
    .gas_leakage_r{
      display: flex
      align-items: center
      span{
        margin-right: 10px
        font-size: 14px;
        font-weight: bold;
        color: #1D2129;
      }
      .item_btn{
        width: 36px;
        height: 24px;
        display: flex;
        justify-content: center
        align-items: center
        background: #E8FFEA;
        border-radius: 2px 2px 2px 2px;
        font-size: 12px;
        font-weight: 500;
        color: #00B42A;
      }
      .item_btn_error{
        background: #FFECE8;
        color: #F53F3F;
      }
    }
  }
}
</style>
