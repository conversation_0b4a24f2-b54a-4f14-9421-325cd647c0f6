<template>
  <div class="inner">
    <div class="data_analy_list" v-for="(item, index) in listData" :key="index" @click="toDetails(item)">
      <div class="analy_type">
        <p>{{ item.paramName }}</p>
        <b>{{ item.total }}</b>
        <span>{{ item.unit }}</span>
      </div>
      <div class="data_time">
        <div class="time_type">
          <div class="time_date">本月</div>
          <div class="data_num">
            <b>{{ item.currentMonthValue }}</b>
            <span>{{ item.unit }}</span>
          </div>
          <div class="date_proportion">
            <span>同比</span>
            <!-- up #F53F3F  down #00B42A -->
            <p :style="{color: item.yoyStatus == 0 ? '#00B42A' : '#F53F3F'}">{{ item.yoy }}</p>
            <img v-if="item.yoyStatus == 0" src="@/assets/images/direction-down-circle-fill.png" alt="">
            <img v-else src="@/assets/images/direction-up-circle-fill.png" alt="">
          </div>
          <div class="date_proportion">
            <span>环比</span>
            <p :style="{color: item.qoqStatus == 0 ? '#00B42A' : '#F53F3F'}">{{ item.qoq }}</p>
            <img v-if="item.qoqStatus == 0" src="@/assets/images/direction-down-circle-fill.png" alt="">
            <img v-else src="@/assets/images/direction-up-circle-fill.png" alt="">
          </div>
        </div>
        <div class="time_type">
          <div class="time_date">本年</div>
          <div class="data_num">
            <b>{{ item.currentYearValue }}</b>
            <span>{{ item.unit }}</span>
          </div>
          <div class="date_proportion">
            <span>同比</span>
            <p :style="{color: item.currentYearYoyStatus == 0 ? '#00B42A' : '#F53F3F'}">{{ item.currentYearYoy }}</p>
            <img v-if="item.currentYearYoyStatus == 0" src="@/assets/images/direction-down-circle-fill.png" alt="">
            <img v-else src="@/assets/images/direction-up-circle-fill.png" alt="">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { monitorTypeList } from "../../components/dict.js";
import moment from "moment";
export default {
  data() {
    return {
      projectCode: monitorTypeList.find(
        item => item.projectName === '锅炉监测'
      ).projectCode,
      listData: []
    }
  },
  created() {
    this.getEnergyStatistics()
  },
  methods: {
    toDetails(item) {
      this.$router.push({
        path: '/steamSupply',
        query: item.query
      })
    },
    async getEnergyStatistics() {
      let params = {
        projectCode: this.projectCode,
        flag: 1,
        startTIme: moment().format("YYYY-MM-DD"),
      }
      let arr = [
        {...params, surveyFlag: 3, paramType: 4},
        {...params, surveyFlag: 1, paramType: 1},
        {...params, surveyFlag: 1, paramType: 2},
        {...params, surveyFlag: 1, paramType: 3}
      ]
      let aa = await this.$api.GetEnergyStatistics(arr[0]).catch(_=> new Object())
      let bb = await this.$api.GetEnergyStatistics(arr[1]).catch(_=> new Object())
      let cc = await this.$api.GetEnergyStatistics(arr[2]).catch(_=> new Object())
      let dd = await this.$api.GetEnergyStatistics(arr[3]).catch(_=> new Object())
      this.listData = [
        {...aa, query: arr[0]},
        {...bb, query: arr[1]},
        {...cc, query: arr[2]},
        {...dd, query: arr[3]},
      ]
    }
  }
}
</script>

<style scoped lang="stylus">
.inner{
  height: 100%;
  background-color: #F2F4F9;
  .data_analy_list{
    border-radius: 8px;
    background: white;
    padding: 16px;
    margin-top: 10px;
    .analy_type{
      background: #E6EFFC;
      border-radius: 8px;
      height: 48px;
      display: flex;
      justify-content: center
      align-items: center
      p{
        font-size: 15px;
        color: #1D2129;
      }
      b{
        font-size: 18px;
        color: #1D2129;
        margin: 0 8px
      }
      span{
        font-size: 13px;
        color: #86909C;
      }
    }
    .data_time{
      display: flex
      .time_type{
        flex: 1
        .time_date{
          font-size: 14px;
          color: #86909C;
          margin: 16px 0 8px 0
        }
        .data_num{
          display: flex
          b{
            font-size: 18px;
            color: #1D2129;
            margin-right: 5px
          }
          span{
            font-size: 13px;
            color: #86909C;
          }
        }
        .date_proportion{
          display: flex;
          margin: 16px 0 8px 0
          span{
            font-size: 14px;
            color: #4E5969;
          }
          p{
            font-size: 14px;
            margin: 0 5px 0 10px;
          }
          img{
            width: 14px
          }
        }
      }
    }
  }
}
</style>
