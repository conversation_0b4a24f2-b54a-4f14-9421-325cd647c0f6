<!--
 * @Author: hedd
 * @Date: 2023-08-16 16:11:02
 * @LastEditTime: 2023-08-17 10:05:24
 * @FilePath: \ybs_h5\src\pages\monitor\boiler\index.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header title="锅炉监测" @backFun="goback"></Header>
    <van-sticky :offset-top="offsetValue">
      <van-tabs v-model="tabsSwitch" color="#3562DB" @change="clickTabs" style="margin-bottom: 20px;">
        <van-tab title="数据分析" name="0">
          <!-- <DataAnalysis /> -->
        </van-tab>
        <van-tab title="锅炉监测" name="1">
          <!-- <BoilerMonitoring /> -->
        </van-tab>
        <van-tab title="运行总览" name="2">
          <!-- <OperationOverview /> -->
        </van-tab>
      </van-tabs>
    </van-sticky>
    <div class="boiler_content">
      <DataAnalysis v-if="tabsSwitch == 0" />
      <BoilerMonitoring v-if="tabsSwitch == 1"  />
      <OperationOverview v-if="tabsSwitch == 2"  />
    </div>
  </div>
</template>

<script>
import DataAnalysis from '@/pages/monitor/boiler/components/dataAnalysis'  // 数据分析
import BoilerMonitoring from '@/pages/monitor/boiler/components/boilerMonitoring'  // 锅炉监测
import OperationOverview from '@/pages/monitor/boiler/components/operationOverview'   // 运行总览
export default {
  components: {
    DataAnalysis,   // 数据分析
    BoilerMonitoring,    // 锅炉监测
    OperationOverview    // 运行总览
  },
  data() {
    return {
      tabsSwitch: '2',
      offsetValue: 0
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback)
    let ele = document.querySelector('.content-box');
    let height = ele.clientHeight;
    this.offsetValue = height;
  },
  methods: {
    clickTabs() {

    },
    goback() {
      this.$YBS.apiCloudCloseFrame()
    },
  }
}
</script>

<style scoped lang="stylus">
.boiler_content{
  padding: 0 10px
}
.inner{
  height: 100%;
  background-color: #F2F4F9;
}
</style>
