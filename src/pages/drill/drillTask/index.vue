<template>
  <div class="inner">
    <Header title="演练任务" @backFun="goBack"></Header>
    <div v-if="taskList.length > 0" class="taskList">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width:100%">
            <div v-for="item in taskList" :key="item.id" class="listStyle" @click="toDeail(item)">
              <div class="name">
                <div> {{ item.taskName }}</div>
                <div class="state">
                  <span :class="['taskState', `taskState${item.taskState}`]">
                    {{ item.taskState===0?'未完成':'已完成' }}</span>
                  <span><van-icon name="arrow" /></span>
                </div>
              </div>
              <div class="txt">
                <div class="txt_l">演练类型</div>
                <div class="txt_r">{{ item.drillTypeName||"" }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">部门责任人</div>
                <div class="txt_r">{{ item.headNames||"" }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">参与人数</div>
                <div class="txt_r">{{ item.deptPersonCount||0 }}</div>
              </div>
              <div class="txtOpertaion">
                <div class="txt_l">执行时间</div>
                <div class="txt_r">{{  moment(item.taskStartTime).format("YYYY-MM-DD")||"" }}</div>
                <div class="btn" v-if="item.appointState==='1'" @click.stop="evaluate(item)">
                  <span>去评价</span>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  data() {
    return {
      moment,
      taskList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      userId: "",
    };
  },
  mounted() {
    this.userId = JSON.parse(localStorage.getItem("loginInfo")).staffId
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getTaskData();
  },
  methods: {
    getTaskData() {
      let data = {
        page: {
          current: this.page,
          size: this.pageSize
        },
        userId: this.userId
      };
      this.$api
        .getQueryTaskData(data)
        .then(res => {
          this.loading = false;
          res.list.forEach(item => {
            this.taskList.push(item);
          });
          if (this.taskList.length >= res.totalCount) {
            this.finished = true;
            this.loading = false;
            return;
          }
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    onRefresh() {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.taskList = [];
      this.getTaskData();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.page++;
      this.getTaskData();
    },
    // 评定
    evaluate(item) {
      this.$router.push({
        path: '/drillTaskAssess',
        query: {
          taskId: item.id
        }
      })
    },
    toDeail(item) {
      // 详情
      this.$router.push({
        path: '/drillTaskDetail',
        query: {
          taskId: item.id
        }
      })
    },
    goBack() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  height: 100vh;
  overflow: hidden;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
    }
    .state {
      .taskState {
        padding: 2px 8px;
        border-radius: 4px;
      }
      .taskState0 {
        background: #ffece8;
        color: #f53f3f;
      }
      .taskState1 {
        background: #e8ffea;
        color: #00b42a;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .txt_l {
        margin: auto 0;
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
}
.taskList {
  height: calc(100% - 80px);
  overflow: auto;
}
.notList {
  position: relative;
  height: calc(100% - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
