<template>
  <div class="inner">
    <Header title="任务详情" @backFun="goBack"></Header>
    <div class="content">
      <!-- 基本信息 -->
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left">
              <span class="lineTitle"></span><span>基本信息</span>
            </div>
            <div class="state">
              <span :class="['taskState', `taskState${detailInfo.taskState}`]">
                {{ detailInfo.taskState===0?'未完成':'已完成' }}</span>
            </div>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练名称:</span>
            <span class="list-itemContent">{{ detailInfo.taskName||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练类型:</span>
            <span class="list-itemContent">{{ detailInfo.drillTypeName||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">部门责任人:</span>
            <span class="list-itemContent">{{ detailInfo.headNames||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">部门组织人:</span>
            <span class="list-itemContent">{{ detailInfo.organizerName||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">参与人数:</span>
            <span
              class="list-itemContent">{{ detailInfo.deptPersonList?  detailInfo.deptPersonList.join(','):''}}({{ detailInfo.deptPersonList?detailInfo.deptPersonList.length:0 }})</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">执行时间:</span>
            <span class="list-itemContent">{{ moment(detailInfo.taskStartTime).format('YYYY-MM-DD')||""}}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">周期类型:</span>
            <span class="list-itemContent">{{detailInfo.cycleTypeName }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练地点:</span>
            <span class="list-itemContent">{{ detailInfo.drillPlace||""}}</span>
          </div>
        </div>
      </div>
      <!-- 演练任务 -->
      <div class="content-taskInfo">
        <div class="list-wrap">
          <div class="list-title">演练任务</div>
          <div class="list-item tsakInfo">
            {{detailInfo.drillDesc||""}}
          </div>
        </div>
      </div>
      <!-- 预案法规文档 -->
      <div class="content-documentInfo">
        <div class="list-wrap">
          <span class="list-title">预案法规文档:</span>
        </div>
        <div class="attachmentsLists" v-if="regulationsDoc.length">
          <span style="width:80%">{{regulationsDoc.length?regulationsDoc[0].name:""}}</span>
          <span class="upload"><van-icon name="down" @click="openFile1(regulationsDoc)" />下载</span>
        </div>
        <div class="list-wrap  list-flex">
          <span class="list-itemTitle">法规文案:</span>
          <span class="list-itemContent" @click="toCopywriting">详情<van-icon name="arrow" /></span>
        </div>
        <div class="copywriting" v-if="regulationsText" v-html="regulationsText"></div>
      </div>
      <div v-if="detailInfo.taskState===1">
        <!-- 演练效果评定 -->
        <div class="content-effectInfo">
          <div class="list-wrap">
            <div class="list-title">演练效果评定</div>
            <div class="effectInfo" v-if="taskEffect.dutyRadio">
              <span
                class="list-state state-color1">{{taskEffect.dutyRadio?divideRadioText[taskEffect.dutyRadio].text:''}}</span><span
                class="list-state state-color2">{{taskEffect.dutyRadio?dutyRadioText[taskEffect.dutyRadio].text : ''}}</span><span
                class="list-state state-color3">{{taskEffect.dutyRadio?effectRadioText[taskEffect.effectRadio].text : ''}}</span>
            </div>
          </div>
        </div>
        <!-- 演练附件 -->
        <div class="content-effectInfo">
          <div class="list-wrap">
            <div class="list-title">演练附件({{taskFilelist.length||0}})</div>
            <div class="effectInfo">
              <div v-for="(item,index) in taskFilelist" :key="index" class="attachmentsLists" @click="openFile(item)">
                <span style="width:80%">{{item.name}}</span>
                <span class="upload"><van-icon name="down" />下载</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 演练效果评定 -->
        <div class="content-effectInfo">
          <div class="list-wrap">
            <div class="list-title">演练评价</div>
            <div class="list-item">
              <span class="list-itemTitle">{{taskRemark.remark||'--'}}</span>
            </div>
            <div class="list-item" v-if="taskRemark.remarkCheckList">
              <span class="list-itemTitle"
                v-if="taskRemark.remarkCheckList&&taskRemark.remarkCheckList.length&&taskRemark.remarkCheckList[0]==='0'">有领导参加</span>
              <span class="list-itemTitle"
                v-if="taskRemark.remarkCheckList&&taskRemark.remarkCheckList.length&&taskRemark.remarkCheckList[0]==='1'">有安全事故</span>
              <span class="list-itemTitle"
                v-if="taskRemark.remarkCheckList&&taskRemark.remarkCheckList.length&&taskRemark.remarkCheckList.length===2">有领导参加,有安全事故</span>
              <!-- <van-checkbox-group v-model="taskRemark.remarkCheckList" direction="horizontal" dis>
              <van-checkbox :name="'0'" disabled>有领导参加</van-checkbox>
              <van-checkbox :name="'1'" disabled></van-checkbox>
            </van-checkbox-group> -->
            </div>
          </div>
        </div>
      </div>
      <!-- 法规文案弹窗 -->
      <van-popup v-model="copywritingPopupShow" position="bottom " closeable :style="{ height: '60%' }">
        <div class="pop-box">
          <div class="pop-title">
            法规文案详情
          </div>
          <div class="pop-content" v-html="regulationsText"></div>
        </div>
      </van-popup>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { Dialog } from "vant";
export default {
  components: {

  },
  data() {
    return {
      moment,
      copywritingPopupShow: false,//法规文案弹窗
      detailInfo: {},
      taskEffect: {
        dutyRadio: '',
        divideRadio: '',
        effectRadio: ''
      },
      taskRemark: {
        remark: '',
        remarkCheckList: ''
      },
      divideRadioText: {
        '0': {
          text: '分工明确'
        },
        '1': {
          text: '分工不明确'
        }
      },
      dutyRadioText: {
        '0': {
          text: '职责清晰'
        },
        '1': {
          text: '职责不清晰'
        }
      },
      effectRadioText: {
        '0': {
          text: '达到预期效果'
        },
        '1': {
          text: '基本达到预期效果'
        },
        '2': {
          text: '未达到预期效果'
        }
      },
      taskFilelist: [],
      regulationsDoc: [],
      regulationsText: '',
      cycleTypeList: [
        {
          cycleType: 0,
          label: '单次'
        },
        {
          cycleType: 1,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 4,
          label: '半年'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
    };
  },
  methods: {
    getDetail() {
      let taskId = this.$route.query.taskId
      this.$api
        .getTaskDataById({ id: taskId })
        .then(res => {
          this.detailInfo = res
          this.taskEffect = res.taskEffect ? JSON.parse(res.taskEffect) : {}
          this.taskRemark = res.taskRemark ? JSON.parse(res.taskRemark) : {}
          this.taskFilelist = res.taskUrl ? JSON.parse(res.taskUrl) : []
          this.detailInfo.cycleTypeName = this.cycleTypeFn(res.cycleType) || ''
          this.regulationsDoc = res.regulationsDoc ? JSON.parse(res.regulationsDoc) : []
          this.regulationsText = res.regulationsText || ''
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    // 周期类型
    cycleTypeFn(e) {
      const item = this.cycleTypeList.filter((i) => i.cycleType === e)
      if (item)
        return item[0].label || ''
    },
    goBack() {
      this.$router.go(-1);
    },
    openFile1(list) {
      api.download(
        {
          url: this.$YBS.imgUrlTranslation(list[0].url),
          savePath: "fs://" + list[0].name,
          report: true,
          cache: true,
          allowResume: true
        },
        function (ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", ret);
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {
            });
            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },
    openFile(item) {
      api.download(
        {
          url: item.url,
          savePath: "fs://" + item.name,
          report: true,
          cache: true,
          allowResume: true
        },
        function (ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", ret);
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {
            });
            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },
    // 详情查看
    toCopywriting() {
      this.copywritingPopupShow = true;
    },
    // 关闭法规文案弹窗
    closePop() {
      this.copywritingPopupShow = false;
    },
  },
  created() {
    this.getDetail();
  },
  mounted() {
    try {
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    } catch (e) {
      console.log(e);
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #f2f4f9;
  font-size: 14px;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 97px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-taskInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-documentInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      font-size: 14px;
      color: #353535;
      .list-wrap {
        .list-item {
          font-size: 16px;
          padding: 7px 0;
          display: flex;
          .list-itemTitle {
            min-width: 75px;
            color: #353535;
          }
          .list-itemContent {
            color: #888;
            overflow: scroll;
            white-space: nowrap;
          }
        }
      }
      .list-flex {
        display: flex;
        justify-content: space-between;
        .list-itemContent {
          color: #86909c;
          font-size: 16px;
        }
      }
      .copywriting {
        margin-top: 10px;
        width: 100%;
        word-break: break-all;
        text-overflow: ellipsis;
      }
    }
    .attachmentsLists {
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      .upload {
        color: #3562db;
      }
    }
    .content-effectInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .list-state {
        padding: 2px 4px;
        display: inline-block;
        border-radius: 2px;
        margin: 7px 10px 7px 0;
      }
      .state-color1 {
        color: #f53f3f;
        background: #ffece8;
      }
      .state-color2 {
        color: #00b42a;
        background: #e8ffea;
      }
      .state-color3 {
        color: #ff7d00;
        background: #fff7e8;
      }
    }
    .list-wrap {
      .list-title {
        font-size: 16px;
        color: #1d2129;
        display: flex;
        justify-content: space-between;
        height: 0.58rem;
        .title-left {
          margin: auto 0;
          .lineTitle {
            height: 0.35rem;
            width: 0.1rem;
            background-color: #3562db;
            display: inline-block;
            margin-right: 6px;
            vertical-align: bottom;
          }
        }
      }
      .state {
        .taskState {
          padding: 6px 8px;
          border-radius: 4px;
          font-size: 14px !important;
          display: inline-block;
        }
        .taskState0 {
          background: #ffece8;
          color: #f53f3f;
        }
        .taskState1 {
          background: #e8ffea;
          color: #00b42a;
        }
      }
      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 92px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
}
.pop-box {
  height: calc(100% - 40px);
  margin: 20px 10px 10px 10px;
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  font-size: 16px;
  color: #1d2129;
  .pop-title {
    text-align: center;
    padding: 10px 0px;
  }
  .pop-content {
    width: 100%;
    height: calc(100% - 30px);
    overflow: scroll;
    word-break: break-all;
  }
}
.tsakInfo {
  width: 100%;
  overflow-y: scroll;
  word-break: break-all;
  cursor: pointer;
  text-overflow: ellipsis;
}
</style>
