<template>
  <div class="inner">
    <Header title="任务详情" @backFun="goBack"></Header>
    <div class="content">
      <!-- 基本信息 -->
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left">
              <span class="lineTitle"></span><span>基本信息</span>
            </div>
            <div class="state">
              <span :class="['taskState', `taskState${detailInfo.taskState}`]">
                {{ detailInfo.taskState===0?'未完成':'已完成' }}</span>
            </div>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练名称</span>
            <span class="list-itemContent">{{ detailInfo.taskName||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练类型</span>
            <span class="list-itemContent">{{ detailInfo.drillTypeName||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">部门责任人</span>
            <span class="list-itemContent">{{ detailInfo.headNames||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">部门组织人</span>
            <span class="list-itemContent">{{ detailInfo.organizerName||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">参与人数</span>
            <span
              class="list-itemContent">{{ detailInfo.deptPersonList?  detailInfo.deptPersonList.join(','):''}}({{ detailInfo.deptPersonList?detailInfo.deptPersonList.length:0 }})</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">执行时间</span>
            <span class="list-itemContent">{{ moment(detailInfo.taskStartTime).format('YYYY-MM-DD')||""}}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">周期类型</span>
            <span class="list-itemContent">{{ detailInfo.cycleTypeName }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练地点</span>
            <span class="list-itemContent">{{ detailInfo.drillPlace||""}}</span>
          </div>
        </div>
      </div>
      <!-- 演练任务 -->
      <div class="content-taskInfo">
        <div class="list-wrap">
          <div class="list-title">演练任务</div>
          <div class="list-item tsakInfo">
            {{detailInfo.drillDesc||""}}
          </div>
        </div>
      </div>
      <!-- 预案法规文档 -->
      <div class="content-documentInfo">
        <div class="list-wrap">
          <span class="list-title">预案法规文档:</span>
        </div>
        <div class="attachmentsLists" v-if="regulationsDoc.length">
          <span style="width:80%">{{regulationsDoc.length?regulationsDoc[0].name:""}}</span>
          <span class="upload"><van-icon name="down" @click="openFile1(regulationsDoc)" />下载</span>
        </div>
        <div class="list-wrap  list-flex">
          <span class="list-itemTitle">法规文案:</span>
          <span class="list-itemContent" @click="toCopywriting">详情<van-icon name="arrow" /></span>
        </div>
        <div class="copywriting" v-if="regulationsText" v-html="regulationsText"></div>
      </div>
      <!-- 演练效果评定 -->
      <div class="content-effectInfo">
        <div class="list-wrap">
          <div class="list-title">演练效果评定</div>
          <div class="effectInfo">
            <div class="radioListItem">
              <div class="radioListItem-title">演习分工</div>
              <div><van-radio-group v-model="divideRadio" direction="horizontal">
                  <van-radio :name="'0'">分工明确</van-radio>
                  <van-radio :name="'1'">分工不明确</van-radio>
                </van-radio-group></div>
            </div>
            <div class="radioListItem">
              <div class="radioListItem-title">演练职责</div>
              <div><van-radio-group v-model="dutyRadio" direction="horizontal">
                  <van-radio :name="'0'">职责清晰</van-radio>
                  <van-radio :name="'1'">职责不清晰</van-radio>
                </van-radio-group></div>
            </div>
            <div class="radioListItem radioListItem">
              <div class="radioListItem-title">演练效果</div>
              <div><van-radio-group v-model="effectRadio" direction="horizontal">
                  <van-radio :name="'0'">达到预期效果</van-radio>
                  <van-radio :name="'1'">基本达到预期效果</van-radio>
                  <van-radio :name="'2'" style="margin-top:8px">未达到预期效果</van-radio>
                </van-radio-group></div>
            </div>
          </div>
        </div>
      </div>
      <!-- 演练附件 -->
      <div class="content-effectInfo">
        <div class="list-wrap">
          <div class="list-title">演练附件({{fileList.length||0}})</div>
          <div class="effectInfo">
            <van-uploader style="width: 100%" preview-size="100" :after-read="afterReadSQ" v-model="fileList" multiple
              :max-count="12" :max-size="maxSize * 1024 * 1024" :before-delete="beforedelete" accept="">
              <el-button size="small" type="primary">上传附件</el-button>
            </van-uploader>
          </div>
        </div>
      </div>
      <!-- 演练效果评定 -->
      <div class="content-evaluateInfo">
        <div class="list-wrap">
          <div class="evaluateTitle">演习评价</div>
        </div>
        <div class="list-wrap">
          <van-field v-model="remark" maxlength="200" show-word-limit autosize type="textarea" @focus="clickField"
            placeholder="请输入演练评价，最多输入200字符" />
        </div>
        <div style="padding:20px 0">
          <van-checkbox-group v-model="remarkCheckList" direction="horizontal">
            <van-checkbox :name="'0'">有领导参加</van-checkbox>
            <van-checkbox :name="'1'">有安全事故</van-checkbox>
          </van-checkbox-group>
        </div>
      </div>
      <!-- 法规文案弹窗 -->
      <van-popup v-model="copywritingPopupShow" position="bottom " closeable :style="{ height: '60%' }">
        <div class="pop-box">
          <div class="pop-title">
            法规文案详情
          </div>
          <div class="pop-content" v-html="regulationsText"></div>
        </div>
      </van-popup>
    </div>
    <!--底部按钮-->
    <div class=" operationBtns">
      <div class="cancelBtn" @click="goToCancelPage">取消</div>
      <div class="sureBtn" @click="evaluate"> 提交</div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import axios from 'axios'
import { Toast, Dialog } from "vant";
import YBS from "@/assets/utils/utils.js";
export default {
  components: {

  },
  data() {
    return {
      taskFilelist: [],
      copywritingPopupShow: false,//法规文案弹窗
      fileList: [],
      moment,
      detailInfo: {},
      maxSize: 20,
      cycleTypeList: [
        {
          cycleType: 0,
          label: '单次'
        },
        {
          cycleType: 1,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 4,
          label: '半年'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      dutyRadio: '0',
      divideRadio: '0',
      effectRadio: '0',
      remarkCheckList: ['0'],
      remark: '',
      divideRadioText: {
        '0': {
          text: '分工明确'
        },
        '1': {
          text: '分工不明确'
        }
      },
      dutyRadioText: {
        '0': {
          text: '职责清晰'
        },
        '1': {
          text: '职责不清晰'
        }
      },
      effectRadioText: {
        '0': {
          text: '达到预期效果'
        },
        '1': {
          text: '基本达到预期效果'
        },
        '2': {
          text: '未达到预期效果'
        }
      },
      regulationsDoc: [], // 法规文档
      regulationsText: ''
    };
  },
  methods: {
    // 判断手机 - ios/andriod
    isIOS() {
      const u = navigator.userAgent;
      return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    },
    clickField(e) {
      if (this.isIOS()) {
        window.addEventListener('focusin', function () {
          if (
            document.activeElement.tagName === 'INPUT' ||
            document.activeElement.tagName === 'TEXTAREA'
          ) {
            setTimeout(function () {
              document.documentElement.scrollTop = document.body.scrollHeight;
            }, 0);
          }
        });
      } else {
        document.activeElement.scrollIntoView()
      }
    },
    getDetail() {
      let taskId = this.$route.query.taskId
      this.$api
        .getTaskDataById({ id: taskId })
        .then(res => {
          this.detailInfo = res
          this.detailInfo.cycleTypeName = this.cycleTypeFn(res.cycleType) || ''
          this.regulationsDoc = res.regulationsDoc ? JSON.parse(res.regulationsDoc) : []
          this.regulationsText = res.regulationsText || ''
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    power() {
      // 类似于storage和camera相同权限存在重复校验建议增加hasPermission包裹判断
      if (!YBS.hasPermission("storage")) {
        YBS.monitorDevicePermissions("storage", {}, function () {
          YBS.monitorDevicePermissions("camera", {})
        })
      } else {
        YBS.monitorDevicePermissions("camera", {})
      }
    },
    // 周期类型
    cycleTypeFn(e) {
      const item = this.cycleTypeList.filter((i) => i.cycleType === e)
      if (item)
        return item[0].label || ''
    },
    goBack() {
      this.$router.go(-1);
    },
    /**
     * 跳转取消
     */
    goToCancelPage() {
      this.$router.go(-1);
    },
    // 上传
    afterReadSQ(file) {
      const isLt20M = file.file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$toast.fail("上传文件大小不能超过 20MB!");
        return isLt20M;
      }
      file.status = "uploading";
      file.message = "上传中...";
      // 需要使用 FormData 进行文件上传
      let form = new FormData();
      form.append("file", file.file)
      axios({
        method: 'post',
        url: __PATH.PLAN_API + '/preplanDrillTask/upload',
        data: form,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          'Authorization': localStorage.getItem("token")
        }
      }).then(res => {
        file.status = "done";
        file.message = "上传成功";
        if (res.data.code == 200) {
          this.taskFilelist.push({
            url: res.data.data.picUrl,
            name: res.data.data.name,
          })
        }
      }).catch((err) => {
        file.status = "failed";
        file.message = "上传失败";
        console.log(err);
      });
    },
    beforedelete(file, index) {
      this.fileList.splice(index.index, 1)
      this.taskFilelist.splice(index.index, 1)
    },
    // 详情查看
    toCopywriting() {
      this.copywritingPopupShow = true;
    },
    // 关闭法规文案弹窗
    closePop() {
      this.copywritingPopupShow = false;
    },
    //评价
    evaluate() {
      // // 演练文件校验
      if (this.taskFilelist.length < 1) {
        Toast({
          type: 'fail',
          message: '请上传演练文件',
          duration: 1500
        })
        return false
      }
      const taskId = this.$route.query.taskId
      let data = {
        updateName: JSON.parse(localStorage.getItem("loginInfo")).staffName,
        updateId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        taskStartTime: this.detailInfo.taskStartTime,
        taskEffect: '',
        taskRemark: '',
        id: taskId,
        taskUrl: JSON.stringify(this.taskFilelist)
      }
      let taskEffectObj = {
        dutyRadio: this.dutyRadio,
        divideRadio: this.divideRadio,
        effectRadio: this.effectRadio,
        result: this.getEffectText(this.dutyRadio, this.divideRadio, this.effectRadio)
      }
      let taskRemarkObj = {
        remark: this.remark,
        remarkCheckList: this.remarkCheckList,
        result: this.getRemarkText(this.remarkCheckList)
      }
      const taskEffectStr = JSON.stringify(taskEffectObj)
      const taskRemarkStr = JSON.stringify(taskRemarkObj)
      data.taskEffect = taskEffectStr
      data.taskRemark = taskRemarkStr
      this.$api
        .updateTaskData(data)
        .then(res => {
          Toast({
            type: 'success',
            message: '保存成功',
            duration: 1500
          })
          this.$router.go('-1')
        })
        .catch(res => {
          if (res.data.code == "500") {
            // this.$toast.fail(res.data.msg);
          }
        });
    },
    // 获取效果评定文字
    getEffectText(dutyRadio, divideRadio, effectRadio) {
      return this.divideRadioText[dutyRadio].text + ',' + this.dutyRadioText[divideRadio].text + ',' + this.effectRadioText[effectRadio].text
    },
    // 获取备注文字
    getRemarkText(remarkCheckList) {
      if (remarkCheckList.length) {
        if (remarkCheckList[0] === '0') {
          return '有领导参加'
        } else if (remarkCheckList[0] === '1') {
          return '有安全事故'
        } else {
          return '有领导参加,有安全事故'
        }
      } else {
        return '-'
      }
    },
    openFile1(list) {
      api.download(
        {
          url: this.$YBS.imgUrlTranslation(list[0].url),
          savePath: "fs://" + list[0].name,
          report: true,
          cache: true,
          allowResume: true
        },
        function (ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", ret);
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {
            });
            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },
  },
  created() {
    setTimeout(() => {
      this.power();
    }, 1000);
    this.getDetail();
  },

  mounted() {
    window.addEventListener('resize', () => {
      if (document.activeElement.tagName == 'INPUT') {
        //延迟出现是因为有些 android 手机键盘出现的比较慢
        window.setTimeout(() => {
          document.activeElement.scrollIntoViewIfNeeded();
        }, 100);
      }
    });

    try {
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    } catch (e) {
      console.log(e);
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  position: relative;
  overflow: hidden;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 160px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-taskInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-documentInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      font-size: 14px;
      color: #353535;
      .list-wrap {
        margin-bottom: 10px;
        .list-item {
          font-size: 16px;
          padding: 7px 0;
          display: flex;
          .list-itemTitle {
            min-width: 102px;
            color: #353535;
          }
          .list-itemContent {
            color: #888;
            overflow: scroll;
            white-space: nowrap;
          }
        }
      }
      .list-flex {
        display: flex;
        justify-content: space-between;
        .list-itemContent {
          color: #86909c;
          font-size: 16px;
        }
      }
      .copywriting {
        margin-top: 10px;
        width: 100%;
        word-break: break-all;
        text-overflow: ellipsis;
      }
    }
    .content-effectInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .radioListItem {
        padding: 0px 0px 12px 0;
        border-bottom: 1px solid #e5e6eb;
        .radioListItem-title {
          margin: 10px 0;
          color: #1d2129;
        }
      }
    }
    .content-evaluateInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      .list-wrap {
        display: flex;
        justify-content: flex-start;
      }
      .evaluateTitle {
        font-size: 16px;
      }
    }
    .attachmentsLists {
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      .upload {
        color: #3562db;
      }
    }
    .list-wrap {
      .list-title {
        font-size: 16px;
        color: #1d2129;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        height: 0.58rem;
        .title-left {
          margin: auto 0;
          .lineTitle {
            height: 0.35rem;
            width: 0.1rem;
            background-color: #3562db;
            display: inline-block;
            margin-right: 6px;
            vertical-align: bottom;
          }
        }
      }
      .state {
        .taskState {
          padding: 6px 8px;
          border-radius: 4px;
          font-size: 14px !important;
          display: inline-block;
        }
        .taskState0 {
          background: #ffece8;
          color: #f53f3f;
        }
        .taskState1 {
          background: #e8ffea;
          color: #00b42a;
        }
      }
      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 92px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
  .operationBtns {
    height: 1.32rem;
    padding: 0.26rem;
    margin-top: 0.2rem;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    position: fixed;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #eceef8;
    z-index: 2;
    .cancelBtn,
    .sureBtn {
      width: 44%;
      height: 0.9rem;
      margin: auto;
      text-align: center;
      line-height: 0.9rem;
    }
    .cancelBtn {
      background-color: #e6effc;
      color: #3562db;
    }
    .sureBtn {
      background-color: #3562db;
      color: #ffffff;
      margin-left: 10px;
    }
  }
}
.pop-box {
  height: calc(100% - 40px);
  margin: 20px 10px 10px 10px;
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  font-size: 16px;
  color: #1d2129;
  .pop-title {
    text-align: center;
    padding: 10px 0px;
  }
  .pop-content {
    width: 100%;
    height: calc(100% - 30px);
    overflow: scroll;
    word-break: break-all;
  }
}
.tsakInfo {
  width: 100%;
  overflow-y: scroll;
  word-break: break-all;
  cursor: pointer;
  text-overflow: ellipsis;
}
</style>
