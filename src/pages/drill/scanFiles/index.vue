<template>
  <div class="inner">
    <Header title="扫描上传文件" @backFun="goBack"></Header>
    <div class="content" v-if="isSubmit">
      <div class="uploadContent">
        <van-uploader style="width: 100%" preview-size="100" :after-read="afterRead" v-model="fileList" multiple
          @oversize="onOversize" :max-count="10" :max-size="maxSize * 1024 * 1024" :before-delete="beforedelete"
          accept="" class="custom-uploader">
          <!-- <el-button size="small" type="primary">
            点击上传
          </el-button> -->
          <div class="upload-btn">
            <img src="./../../../assets/images/drill/upload-add.png" alt="">
            <p> 点击上传</p>
          </div>
        </van-uploader>
      </div>
      <div class="tips">
        单个文件最大上传20M，支持文件格式：jpg、png、pdf、doc、txt、xls
      </div>
    </div>
    <div class="content" v-if="isContinue">
      <div class="tipContent">
        <img src="./../../../assets/images/drill/upload-success.png" alt="">
        <p class="info">上传成功</p>
      </div>
    </div>
    <div class="content" v-if="isRetry">
      <div class="tipContent">
        <img src="./../../../assets/images/drill/upload-error.png" alt="">
        <p class="info">上传失败</p>
        <p class="hint">请检查网络</p>
      </div>
    </div>
    <div class="content" v-if="isScan">
      <div class="tipContent">
        <img src="./../../../assets/images/drill/upload-loseEfficacy.png" alt="">
        <p class="info">链接失效</p>
        <p class="hint">链接已失效，请重新扫码</p>
      </div>
    </div>
    <div class="operationBtns">
      <div class="sureBtn" v-if="isSubmit" @click="sure"> 提交</div>
      <div class="sureBtn" v-if="isContinue" @click="continueUpload"> <van-icon name="plus" /> 继续上传</div>
      <div class="sureBtn" v-if="isRetry" @click="retry"> 重试</div>
    </div>
  </div>
</template>
<script>
import { Toast } from "vant";
import axios from 'axios'
export default {
  data() {
    return {
      fileList: [],
      maxSize: 20,
      scanFileList: [],
      isSubmit: true,
      isContinue: false,
      isRetry: false,
      isScan: false
    };
  },
  methods: {
    goBack() {
      this.$YBS.apiCloudCloseFrame();
    },
    // 确认
    sure() {
      if (this.scanFileList.length < 1) {
        Toast({
          type: 'fail',
          message: '请上传文件',
          duration: 1500
        })
        return false
      }
      const { alramId } = this.$route.query
      let data = {
        alarmId: alramId,
        list: this.scanFileList,
        userId: localStorage.getItem("loginInfo") ? JSON.parse(localStorage.getItem("loginInfo")).staffId : "",
      }
      this.$api
        .submitScanFile(data)
        .then(res => {
          if (res) {
            Toast({
              type: 'success',
              message: '提交成功',
              duration: 1500
            })
            this.isSubmit = false
            this.isContinue = true
            this.isRetry = false
            this.isScan = false
            this.fileList = []
            this.scanFileList = []
          }
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.isSubmit = false
            this.isContinue = false
            this.isRetry = true
            this.isScan = false
            // this.$toast.fail(res.data.msg);
          }
        });
    },
    // 继续上传
    continueUpload() {
      this.isSubmit = true
      this.isContinue = false
      this.isRetry = false
      this.isScan = false
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    // 上传
    // 图片上传成功回调
    afterRead(file) {
      this.fileList.forEach(i => {
        return (i.status = "uploading");
      });
      // 使用formdata上传
      const formData = new FormData();
      if (file.length && file.length > 0) {
        file.forEach(item => {
          formData.append("file", item.file);
          this.subImg(formData);
        })
      } else {
        formData.append("file", file.file);
        this.subImg(formData);
      }
    },
    subImg(params) {
      axios({
        method: 'post',
        url: __PATH.PLAN_API + '/preplanDrillTask/upload',
        data: params,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        }
      }).then(res => {
        if ((res.data.code = "200")) {
          this.fileList.forEach(i => {
            return (i.status = "done");
          });
          this.scanFileList.push({
            operationUrl: res.data.data.picUrl,
            operationName: res.data.data.name
          })
        }
      });
    },
    beforedelete(file, index) {
      this.fileList.splice(index.index, 1)
      this.scanFileList.splice(index.index, 1)
    },
    //检查是否超时
    getAlarmState() {
      const { alramId } = this.$route.query
      this.$api
        .checkIsTimeout({ alarmId: alramId })
        .then(res => {
          if (res) {
            this.isSubmit = true
            this.isContinue = false
            this.isRetry = false
            this.isScan = false
          } else {
            this.isSubmit = false
            this.isContinue = false
            this.isRetry = false
            this.isScan = true
          }
        })
    },
    // 重试
    retry() {
      this.isSubmit = true
      this.isContinue = false
      this.isRetry = false
      this.isScan = false
      this.fileList = []
      this.scanFileList = []
    }
  },
  mounted() {
    this.getAlarmState()
    try {
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    } catch (e) {
      console.log(e);
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  height: 100vh;
  font-size: 14px;
  position: relative;
  overflow: hidden;
  color: #1d2129;
  background: url("./../../../assets/images/drill/upload-bg.png") no-repeat;
  .content {
    margin: 16px 5px 10px 5px;
    height: calc(100vh - 89px);
    .uploadContent {
      height: calc(100% - 100px);
      overflow-y: scroll;
    }
    .tips {
      padding: 0 16px;
      color: #a6afbf;
    }
    .info {
      font-weight: 500;
      margin: 16px 0;
      font-size: 18px;
      color: #ffffff;
    }
    .hint {
      font-size: 14px;
      color: #a6afbf;
    }
  }
  .tipContent {
    margin-top: 60px !important;
    width: 100%;
    text-align: center;
  }
  .operationBtns {
    height: 1.32rem;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 2;
    .sureBtn {
      width: 100%;
      height: 0.9rem;
      margin: auto;
      text-align: center;
      line-height: 0.9rem;
      img {
        vertical-align: text-bottom;
      }
    }
    .sureBtn {
      background: url("./../../../assets/images/drill/upload-btn.png") no-repeat;
      background-size: 100% auto;
      font-size: 15px;
      color: #a4afc1;
    }
  }
}
.custom-uploader .upload-btn {
  height: 98px !important;
  width: 98px !important;
  border: 1px dashed #b0e3fa;
  border-radius: 4px;
  color: #b0e3fa;
  display: table-cell;
  vertical-align: middle;
  text-align: center; /* 水平居中 */
  img {
    padding: 8px;
  }
}
</style>
<style lang="scss" >
.van-uploader__file {
  background-color: #06102c !important;
}
.van-uploader__file-name {
  color: #ffffff !important;
  white-space: pre-wrap !important;
}
.van-icon-description {
  color: #ffffff !important;
}
.van-uploader__preview,
.van-uploader__input-wrapper {
  margin-left: 10px !important;
}
</style>
