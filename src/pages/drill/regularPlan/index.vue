<template>
  <div class="regularPlan-view">
    <Header title="常规预案" @backFun="goback"></Header>
    <van-search
      v-model="planName"
      background="#fff"
      clearable
      placeholder="搜索预案名称"
      @search="getPlanList"
      @clear="searchCancel"
    >
    </van-search>
    <div class="view-list">
      <van-pull-refresh v-model="pullLoading" success-text="刷新成功" @refresh="getPlanList('init')">
        <van-list v-model="listLoading" :finished="listFinished" finished-text="没有更多了" @load="getPlanList">
          <div class="list-item" v-for="item in dataList" :key="item.code" @click="viewItem(item)">
            <div class="item-title">
              <p class="title-code">{{ item.planName || '-' }}</p>
              <van-icon name="arrow" color="#C9CDD4" :size="14" />
            </div>
            <div class="item-info">
              <img :src="$YBS.imgUrlTranslation(JSON.parse(item.regulationsFlow)[0].url)">
              <div style="flex: 1;overflow: hidden;">
                <div class="info-item">
                  <p class="info-label">报警类型</p>
                  <p class="info-value">{{ item.alarmTypeName || '-' }}</p>
                </div>
                <div class="info-item">
                  <p class="info-label">空间类型</p>
                  <p class="info-value">{{ item.spaceTypeName || '-' }}</p>
                </div>
                <div class="info-item">
                  <p class="info-label">版本号</p>
                  <p class="info-value">{{ item.versionNo || '-' }}</p>
                </div>
              </div>
            </div>
            <p class="item-text">{{ item.regulationsDesc || '-' }}</p>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
export default {
  name: 'regularPlan',
  data() {
    return {
      planName: '',
      pullLoading: false, // 下拉刷新
      listLoading: false, // 列表加载
      listFinished: false, // 列表加载完成
      statusInfoList: {
        0: {name: '待提交', color: '#FF7D00', bgdColor: '#FFF7E8'},
        1: {name: '待审核', color: '#3562DB', bgdColor: '#E6EFFC'},
        2: {name: '已审核', color: '#00B42A', bgdColor: '#E8FFEA'},
        3: {name: '未通过', color: '#F53F3F', bgdColor: '#FFECE8'},
        4: {name: '已取消', color: '#4E5969', bgdColor: '#F2F3F5'}
      },
      dataList: [],
      pageParams: {
        currentPage: 0,
        pageSize: 10
      }
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback)
  },
  created() {

  },
  methods: {
    // 取消搜索
    searchCancel(){
      this.planName = ''
      this.getPlanList()
    },
    // 预案列表
    getPlanList(type) {
      if (type == 'init') {
        this.dataList = []
        this.pageParams.currentPage = 1
      } else {
        this.pageParams.currentPage += 1
      }
      let params = {
        planCategory: '1',
        planName: this.planName,
        spaceApplyStatus: this.statusCode,
        page: this.pageParams.currentPage,
        pageSize: this.pageParams.pageSize
      }
      this.$api.GetPlanList(params).then(res => {
        this.dataList = this.dataList.concat(res.records)
        this.pullLoading = false
        this.listLoading = false
        if (this.pageParams.currentPage >= res.pages) {
          this.listFinished = true
        } else {
          this.listFinished = false
        }
      });
    },
    viewItem(item) {
      this.$router.push({
        path: '/planDetails',
        query: {id: item.id}
      })
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
}

</script>

<style lang="scss" scoped>
.regularPlan-view{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .topSelest{
    display: flex;
    align-items: center;
    p{
      color: #1D2129;
      font-size: 16px;
    }
    .el-icon-caret-bottom{
      color: #C9CDD4;
      margin-left: 4px;
    }
  }
  .view-list {
    flex: 1;
    background: #F2F4F9;
    padding: 0px 10px 0px 10px;
    overflow: auto;
    .list-item{
      padding: 16px;
      margin-top: 10px;
      border-radius: 8px;
      background: #fff;
      .item-title{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        .title-code{
          font-size: 16px;
          font-weight: 500;
          color: #1D2129;
        }
      }
      .item-info{
        display: flex;
        align-items: center;
        img {
          width: 108px;
          height: 81px;
          border-radius: 4px;
          margin-right: 10px;
        }
        .info-item {
          padding: 5px 0px;
          display: flex;
          font-size: 16px;
          line-height: 22px;
        }
        .info-label {
          padding-right: 16px;
          font-weight: 400;
          color: #4E5969;
        }
        .info-value {
          font-size: 16px;
          flex: 1;
          font-weight: 400;
          color: #1D2129;
          overflow:hidden;
          text-overflow:ellipsis;
          white-space:nowrap;
        }
      }
      .item-text {
        margin-top: 10px;
        font-weight: 400;
        font-size: 16px;
        color: #1D2129;
        line-height: 22px;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
      }
    }
    .van-pull-refresh{
      height: calc(100% - 10px);
      overflow: initial;
    }

  }
}
</style>
