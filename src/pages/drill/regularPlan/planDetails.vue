<template>
  <div class="planDetails-view">
    <Header title="预案详情" @backFun="goback"></Header>
    <van-tabs v-model="activeTab" :line-width="24" color="#3562DB" title-active-color="#1D2129" title-inactive-color="#86909C">
      <van-tab title="基本信息">
        <div class="view-info">
          <p class="info-item">
            <span class="info-item-label">预案模版名称</span>
            <span class="info-item-value">{{ planData.planName || '-' }}</span>
          </p>
          <p class="info-item">
            <span class="info-item-label">报警类型</span>
            <span class="info-item-value">{{ planData.alarmTypeName || '-' }}</span>
          </p>
          <p class="info-item">
            <span class="info-item-label">空间类型</span>
            <span class="info-item-value">{{ planData.spaceTypeName || '-' }}</span>
          </p>
          <p class="info-item">
            <span class="info-item-label">预案说明</span>
            <span class="info-item-value">{{ planData.regulationsDesc || '-' }}</span>
          </p>
          <p class="info-item">
            <span class="info-item-label">版本号</span>
            <span class="info-item-value">{{ planData.versionNo || '-'  }}</span>
          </p>
        </div>
        <div class="view-docs">
          <p class="docs-title">法规文档</p>
          <div class="docs-file">
            <p class="file-name">{{ planData.regulationsDoc ? planData.regulationsDoc[0].name : '-' }}</p>
            <p class="file-download" @click="dload"><i class="el-icon-download"></i> 下载</p>
          </div>
          <p class="docs-title">法规流程图</p>
          <img class="docs-img" :src="$YBS.imgUrlTranslation(planData.regulationsFlow[0].url)">
          <p class="docs-title">
            <span>法规流程图</span>
            <span class="title-right" @click="isDocs = true">详情 <i class="el-icon-arrow-right"></i></span>
          </p>
          <div class="docs-text" v-html="planData.regulationsText"></div>
        </div>
      </van-tab>
      <!-- <van-tab title="往期版本">内容 2</van-tab> -->
    </van-tabs>
    <van-action-sheet v-model="isDocs" title="法规文案详情">
      <div class="docsContent" v-html="planData.regulationsText"></div>
    </van-action-sheet>
  </div>
</template>

<script>
import { Dialog } from "vant";
export default {
  name: '',
  data() {
    return {
      activeTab: 0,
      planData: {},
      isDocs: false
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {
    this.getPlanDetail()
  },
  methods: {
    dload() {
      if (!this.planData.regulationsDoc.length) return
      api.download(
        {
          url: this.$YBS.imgUrlTranslation(this.planData.regulationsDoc[0].url),
          savePath: "fs://" + this.planData.regulationsDoc[0].name,
          report: true,
          cache: true,
          allowResume: true
        },
        function(ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", JSON.stringify(ret));
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {
            });
            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },
    // 预案详情
    getPlanDetail() {
      this.$api.GetPlanDetail({id: this.$route.query.id}).then(res => {
        res.regulationsFlow = JSON.parse(res.regulationsFlow)
        res.regulationsDoc = JSON.parse(res.regulationsDoc)
        this.planData = res
      });
    },
    goback() {
      this.$router.go(-1);
    }
  }
}

</script>

<style lang="scss" scoped>
.planDetails-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  /deep/ .van-tabs {
    flex: 1;
    overflow: hidden;
    .van-tabs__wrap {
      .van-tabs__nav {
        // padding: 0px;
      }
    }
    .van-tabs__content {
      height: calc(100% - 44px);
      .van-tab__pane {
        height: 100%;
        background: #F2F4F9;
        overflow: auto;
      }
    }
  }
  .view-info {
    background: #fff;
    padding: 16px 0px;
    .info-item {
      display: flex;
      padding: 5px 16px;
      span {
        display: inline-block;
      }
      .info-item-label {
        font-weight: 300;
        font-size: 16px;
        color: #4E5969;
        line-height: 22px;
        min-width: 100px;
        margin-right: 16px;
      }
      .info-item-value {
        flex: 1;
        font-weight: 400;
        font-size: 16px;
        color: #1D2129;
        line-height: 22px;
        word-wrap: break-word;
        word-break: break-all;
      }
    }
  }
  .view-docs {
    margin: 10px 0px;
    background: #fff;
    padding: 0px 16px 16px 16px;
    .docs-title {
      font-weight: 400;
      font-size: 16px;
      color: #1D2129;
      line-height: 22px;
      padding: 16px 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-right {
        font-weight: 400;
        font-size: 15px;
        color: #86909C;
        line-height: 21px;
      }
    }
    .docs-file {
      margin: 10px 0px;
      padding: 16px;
      background: #F7F8FA;
      border-radius: 8px;
      display: flex;
      align-items: center;
      .file-name {
        flex: 1;
        font-weight: 400;
        font-size: 16px;
        color: #1D2129;
        line-height: 22px;
      }
      .file-download {
        font-weight: 500;
        font-size: 15px;
        color: #3562DB;
        line-height: 21px;
        padding-left: 16px;
      }
    }
    .docs-img {
      width: 108px;
      height: 81px;
      border-radius: 4px;
      margin-bottom: 16px;
    }
    .docs-text {
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
  .docsContent {
    height: 70vh;
    padding: 0px 16px 16px 16px;
    overflow: auto;
  }
}
</style>
