<template>
  <div class="inner">
    <Header :title="pageTitle" @backFun="goBack"></Header>
    <div :class="['content ', $route.query.dirllType==='affirm' ? 'contentHeight' : 'contentHeight1']">
      <!-- 培训信息 -->
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-item">
            <span class="list-itemTitle">演练名称</span>
            <span class="list-itemContent">{{ detailInfo.taskName||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练类型</span>
            <span class="list-itemContent">{{ detailInfo.drillTypeName||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练部门</span>
            <span class="list-itemContent">{{ detailInfo.deptNames||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练负责人</span>
            <span class="list-itemContent">{{ detailInfo.headNames||"--" }}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练时间</span>
            <span class="list-itemContent">{{ moment(detailInfo.taskStartTime).format('YYYY-MM-DD')||""}} -
              {{ moment(detailInfo.taskEndTime).format('YYYY-MM-DD')||""}}</span>
          </div>
          <div class="list-item">
            <span class="list-itemTitle">演练地点</span>
            <span class="list-itemContent">{{ detailInfo.drillPlace||""}}</span>
          </div>
        </div>
      </div>
      <!-- 演练任务 -->
      <div class="content-taskInfo">
        <div class="list-wrap">
          <div class="list-title">演练任务</div>
          <div class="list-item tsakInfo">
            {{detailInfo.drillDesc||""}}
          </div>
        </div>
      </div>
      <!-- 预案法规文档 -->
      <div class="content-documentInfo">
        <div class="list-wrap">
          <span class="list-itemTitle">预案法规文档:</span>
          <span class="list-itemContent"
            @click="openFile1(regulationsDoc)">{{regulationsDoc.length?regulationsDoc[0].name:""}}</span>
        </div>
        <div class="attachmentsLists" v-if="regulationsDoc.length">
          <span style="width:80%">{{regulationsDoc.length?regulationsDoc[0].name:""}}</span>
          <span class="upload"><van-icon name="down" @click="openFile1(regulationsDoc)" />下载</span>
        </div>
        <div class="list-wrap  list-flex">
          <span class="list-itemTitle">法规文案:</span>
          <span class="list-itemContent" @click="toCopywriting">详情<van-icon name="arrow" /></span>
        </div>
        <div class="copywriting" v-html="regulationsText"></div>
      </div>
      <!-- 法规文案弹窗 -->
      <van-popup v-model="copywritingPopupShow" position="bottom " closeable :style="{ height: '60%' }">
        <div class="pop-box">
          <div class="pop-title">
            法规文案详情
          </div>
          <div class="pop-content" v-html="regulationsText"></div>
        </div>
      </van-popup>
    </div>
    <!--底部按钮-->
    <div class="operationBtns" v-if="$route.query.dirllType==='affirm'">
      <div :class="['cancelBtn', detailInfo.isConfirm === 1 ? 'btnState1' : '']" @click="alter" v-if="!delFlagBtn">
        更改演练通知</div>
      <div class="sureBtn" @click="sure" v-if="!delFlagBtn && detailInfo.isConfirm === 0"> 确认演练
      </div>
      <div class="sureBtn " v-if="detailInfo.isConfirm === 1 "> 已确认</div>
      <div class="btnState1  deleteBtn" v-if="delFlagBtn"> 当前任务已经被删除</div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { Toast, Dialog } from "vant";
export default {
  components: {

  },
  data() {
    return {
      copywritingPopupShow: false,//法规文案弹窗
      moment,
      planState: 1,
      detailInfo: {},
      regulationsDoc: [], // 法规文档
      regulationsText: '',
      delFlagBtn: false,
    };
  },
  methods: {
    getDetail() {
      let taskId = this.$route.query.taskId
      this.$api
        .getTaskDataById({ id: taskId })
        .then(res => {
          this.detailInfo = res
          this.regulationsDoc = res.regulationsDoc ? JSON.parse(res.regulationsDoc) : []
          this.regulationsText = res.regulationsText || ''
          if (res.delFlag === 1) {
            this.delFlagBtn = true
          }
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    goBack() {
      this.$YBS.apiCloudCloseFrame();
    },
    /**
     * 更改
     */
    alter() {
      let taskId = this.$route.query.taskId
      this.$api
        .getTaskDataById({ id: taskId })
        .then(res => {
          if (res.delFlag === 0) {
            this.$router.push({
              path: '/changeDrillInform',
              query: {
                taskId: taskId
              }
            })
          } else {
            this.delFlagBtn = true
          }
        })
    },
    // 详情查看
    toCopywriting() {
      this.copywritingPopupShow = true;
    },
    // 关闭法规文案弹窗
    closePop() {
      this.copywritingPopupShow = false;
    },
    // 确认
    sure() {
      const taskId = this.$route.query.taskId
      let data = {
        id: taskId,
      }
      this.$api
        .sureTaskNoticeData(data)
        .then(res => {
          Toast({
            type: 'success',
            message: '成功',
            duration: 1500
          })
          this.goBack()
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });

    },
    openFile1(list) {
      api.download(
        {
          url: this.$YBS.imgUrlTranslation(list[0].url),
          savePath: "fs://" + list[0].name,
          report: true,
          cache: true,
          allowResume: true
        },
        function (ret, err) {
          console.log("下载res", ret);
          if (ret.state == 1) {
            console.log("下载成功", ret);
            Dialog.alert({
              message: "下载成功，文件存储路径" + ret.savePath
            }).then(() => {
            });
            //下载成功
          } else {
            console.log("下载失败", ret);
          }
        }
      );
    },
  },
  created() {
    this.pageTitle = this.$route.query.dirllType === 'affirm' ? '演练确认通知' : '演练通知'
    this.getDetail();
  },
  mounted() {
    try {
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    } catch (e) {
      console.log(e);
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  position: relative;
  overflow: hidden;
  color: #1d2129;
  .content {
    margin: 5px 5px 10px 5px;
    overflow-y: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-taskInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
    .content-documentInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      font-size: 14px;
      .list-wrap {
        margin-bottom: 10px;
        .list-item {
          font-size: 16px;
          padding: 7px 0;
          display: flex;
          .list-itemTitle {
            min-width: 92px;
            color: #353535;
          }
          .list-itemContent {
            color: #888;
            overflow: scroll;
            white-space: nowrap;
          }
        }
      }
      .list-flex {
        display: flex;
        justify-content: space-between;
        .list-itemContent {
          color: #86909c;
          font-size: 16px;
        }
      }
      .copywriting {
        margin-top: 10px;
        width: 100%;
        word-break: break-all;
        text-overflow: ellipsis;
      }
    }
    .content-effectInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .radioListItem {
        display: flex;
        margin-top: 12px;
      }
    }
    .content-evaluateInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .list-wrap {
        display: flex;
        justify-content: flex-start;
      }
      .evaluateTitle {
        font-size: 16px;
      }
    }
    .list-wrap {
      .list-title {
        font-size: 16px;
        color: #333333;
        display: flex;
        justify-content: space-between;
        margin-bottom: 7px;
      }
      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 102px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
  .contentHeight {
    height: calc(100vh - 150px);
  }
  .contentHeight1 {
    height: calc(100vh - 89px) !important;
  }
  .attachmentsLists {
    margin: 15px 0;
    display: flex;
    justify-content: space-between;
    .upload {
      color: #3562db;
    }
  }
  .operationBtns {
    height: 1.32rem;
    padding: 0.26rem;
    margin-top: 0.2rem;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    position: fixed;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #eceef8;
    z-index: 2;
    .cancelBtn,
    .sureBtn {
      width: 44%;
      height: 0.9rem;
      margin: auto;
      text-align: center;
      line-height: 0.9rem;
    }
    .deleteBtn {
      width: 80%;
      height: 0.9rem;
      margin: auto;
      text-align: center;
      line-height: 0.9rem;
      background-color: #3562db;
      color: #ffffff;
    }
    .cancelBtn {
      background-color: #e6effc;
      color: #3562db;
    }
    .sureBtn {
      background-color: #3562db;
      color: #ffffff;
      margin-left: 10px;
    }
  }
}
.tsakInfo {
  width: 100%;
  overflow-y: scroll;
  word-break: break-all;
  cursor: pointer;
  text-overflow: ellipsis;
}
.pop-box {
  height: calc(100% - 40px);
  margin: 20px 10px 10px 10px;
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  font-size: 16px;
  color: #1d2129;
  .pop-title {
    text-align: center;
    padding: 10px 0px;
  }
  .pop-content {
    width: 100%;
    height: calc(100% - 30px);
    overflow: scroll;
    word-break: break-all;
  }
}
.btnState1 {
  color: #ccced3 !important;
  cursor: not-allowed;
  pointer-events: none;
}
</style>
