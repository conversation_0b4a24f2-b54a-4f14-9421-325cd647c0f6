<template>
  <div class="page">
    <van-popup v-model="popupShow" position="bottom">
      <!-- 头部 -->
      <Header title="选择参演组织" @backFun="backFn"></Header>
      <div class="content">
        <div class="popupContent">
          <van-search placeholder="搜索部门名称" v-model="keyword" show-action>
            <template #action>
              <div class="search-btn" @click="onSearch">搜索</div>
            </template>
          </van-search>
          <van-checkbox-group v-model="result" ref="checkboxGroup" :max="10" @change="thechangCheck">
            <div v-for="(item, index) in this.deptList" :key="index" class="checkBox">
              <van-checkbox :name="item.id">
                {{item.label}}
              </van-checkbox>
            </div>
          </van-checkbox-group>
        </div>
      </div>
      <div class="foot">
        <div class="foot-left">
          <van-checkbox v-model="checkAllFlag" @change="theAllCheck">全选
            <span @click.stop="unfold"> 已选择：已选择{{selected.length||""}}部门 <van-icon name="arrow-up"
                v-if="choiceShow" /><van-icon v-if="!choiceShow" name="arrow-down" /></span>
          </van-checkbox>
        </div>
        <div class="foot-right">
          <van-button class="btn" type="info" @click="saveDepts">确认({{selected.length||0}})</van-button>
        </div>
      </div>
      <van-popup v-model="choiceShow" position="bottom" :style="{ height: '60%' }">
        <div class="choiceBody">
          <div class="choiceHeader">
            <div class="choice-title">
              已选择：已选择部门
            </div>
            <div class="choice-btn">
              <div class="btns" @click="sureSelected">确认</div>
            </div>
          </div>
          <div class="choiceContent">
            <div v-for="(el,index) in  selected" :key="index" class="item-list">
              <div class="label">{{el.label}}</div>
              <div class="operation" @click="deleteHandle(index)">
                <van-icon name="delete-o" size="16" />
              </div>
            </div>
          </div>
        </div>
      </van-popup>
    </van-popup>
  </div>
</template>
<script>
import { Toast } from "vant";
export default {
  props: {
    popupShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      choiceShow: false,
      keyword: "",
      selected: [], //已选
      selectedId: [],//已选Id
      // 全选
      result: [],
      checkAllFlag: false,
      deptList: [],
      allDeptData: [],//所有数据
    };
  },
  mounted() {
    this.getAllOffice()
  },
  methods: {
    getAllOffice() {
      this.$api.getAllOffice({}).then((res) => {
        this.deptList = res.body.result;
        this.allDeptData = res.body.result;
      });
    },
    thechangCheck(list) {
      let arr = Array.from(new Set([... this.selectedId, ...list]));
      let arr1 = []
      arr.forEach(item => {
        this.allDeptData.forEach(el => {
          if (item === el.id) {
            arr1.push(el)
          }
        })
      })
      this.selectedId = arr
      this.selected = arr1
    },
    onSearch() {
      if (this.keyword) {
        this.deptList = this.fuzzyQuery(this.deptList, this.keyword)
      } else {
        this.getAllOffice()
      }
    },
    fuzzyQuery(list, keyWord) {
      var arr = [];
      for (var i = 0; i < list.length; i++) {
        if (list[i].label.indexOf(keyWord) >= 0) {
          arr.push(list[i]);
        }
      }
      return arr;
    },
    unfold() {
      this.choiceShow = !this.choiceShow
    },
    // 全选
    theAllCheck(val) {
      if (val) {
        // map遍历展示选中最大选中数
        this.result = [];
        this.result = this.deptList.map((item) => item);
        this.selected = this.result
      } else {
        // 防止有一条数据取消选中后所有数据全部取消选中
        if (this.result.length === this.deptList.length) {
          this.checkAllFlag = false;
          this.result = [];
          this.selected = []
        }
      }
    },
    // 移除
    deleteHandle(index) {
      this.selected.splice(index, 1);
      this.selectedId.splice(index, 1);
      this.result = this.selectedId
    },
    // 确认
    sureSelected() {
      this.choiceShow = false
    },
    saveDepts() {
      if (!this.selected.length) {
        Toast.fail('请选择部门！');
        return
      }
      this.$emit("closePopup", this.selected);
      this.selected = [];
      this.result = []
    },
    backFn() {
      this.$emit("closePopup");
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .van-overlay {
  z-index: 9999999 !important;
}
/deep/ .van-popup {
  z-index: 99999999 !important;
  width: 100vw;
  height: 100vh;
}
.content {
  height: calc(100vh - 121px);
  overflow-y: scroll;
  .checkBox {
    padding: 10px;
  }
}
.foot {
  height: 1.22rem;
  padding: 0.12rem;
  margin-top: 0.2rem;
  box-sizing: border-box;
  background-color: #fff;
  margin: auto;
  display: flex;
  justify-content: space-around;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #eceef8;
  z-index: 2;

  .foot-left {
    margin: auto 0;
    width: 60%;
  }
  .foot-right {
    text-align: right;
    flex: 1;
    .btn {
      padding: 0 20px;
    }
  }
}
.choiceBody {
  padding: 10px 20px;
  .choiceHeader {
    display: flex;
    margin: auto;
    box-sizing: border-box;
    justify-content: space-between;
    .choice-title {
      margin: auto 0;
      width: 60%;
    }
    .choice-btn {
      .btns {
        padding: 10px 20px;
        background: #1989fa;
        color: #ffffff;
      }
    }
  }
  .choiceContent {
    height: calc(100vh / 2);
    overflow-y: scroll;
    .item-list {
      padding: 10px 0;
      display: flex;
      box-sizing: border-box;
      justify-content: space-between;
    }
  }
}
</style>
