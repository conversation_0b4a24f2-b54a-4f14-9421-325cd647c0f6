<template>
  <div class="inner">
    <!-- 返回title -->
    <!-- <titleRoom /> -->
    <Header title="更改演练通知" @backFun="goback"></Header>
    <van-form @submit="onSubmit">
      <div class="content">
        <van-field v-model="detailInfo.taskName" label="演练名称" readonly />
        <van-field v-model="detailInfo.drillTypeName" label="演练类型" readonly />
        <van-field readonly clickable v-model="formItem.deptNames" label="参演部门" placeholder="选择部门"
          :rules="[{ required: true, message: '请选择部门' }]" @click="deptPopupShow = true" />
        <van-field v-model="time" label="演练时间" placeholder="演练时间" readonly clickable @click="timeShow = true"
          :rules="[{ required: true, message: '请选择演练时间' }]" />
        <van-field readonly clickable v-model="formItem.headNames" label="演练负责人" placeholder="演练负责人"
          @click="headPersonHandle" :rules="[{ required: true, message: '请选择演练负责人' }]" />
        <van-field readonly clickable v-model="formItem.organizerName" label="演练组织人" placeholder="演练组织人"
          @click="organizerHandle" :rules="[{ required: true, message: '请选择演练组织人' }]" />
        <van-field v-model="formItem.drillPlace" rows="2" autosize label="演练地点" type="textarea" maxlength="200"
          placeholder="请输入培训地点" />
        <van-calendar v-model="timeShow" type="range" @confirm="onTimeConfirm" />
        <!-- 选择参演组织弹窗 -->
        <selectDeptPicker v-if="deptPopupShow" :popupShow="deptPopupShow" @closePopup="closeDeptPopup"
          :personType="'duty'">
        </selectDeptPicker>
        <!-- 选择负责人/组织人 -->
        <selectPersonPicker v-if="personPopupShow" :popupShow="personPopupShow" @closePopup="closePersonPopup"
          :personType="personType">
        </selectPersonPicker>
      </div>
      <!--底部按钮-->
      <div class="operationBtns">
        <div class="btns">
          <van-button class="btn" type="default" @click="goToCancelPage">取消</van-button>
          <van-button class="btn" type="info" native-type="submit">演练通知更改</van-button>
        </div>
      </div>
    </van-form>
  </div>
</template>

<script>
import moment from "moment"
import { Toast } from "vant";
import selectDeptPicker from "./components/selectDeptPicker.vue";
import selectPersonPicker from "./components/selectPersonPicker.vue";
export default {
  name: "changeDrillInform",
  components: {
    selectDeptPicker,
    selectPersonPicker
  },
  data() {
    return {
      detailInfo: {
        taskName: '',//演练名称
        drillTypeName: '',//类型名称
      },
      formItem: {
        drillPlace: '',//演练地点
        deptNames: '',//参演部门
        organizerName: '',//组织人
        organizerId: '',//组织人id
        headIds: '', // 演练责任人ids
        headNames: "",//演练责任人names
        deptIds: '',//参演部门ids
        deptNames: '',//参演部门names
      },
      time: '',
      taskStartTime: '',
      taskEndTime: '',
      timeShow: false,// 日期选择
      showOrganizerPicker: false,//人员选择
      deptPopupShow: false, //科室
      personPopupShow: false,//人员
      personType: '',// duty:责任人 tissue:组织人
    };
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      let taskId = this.$route.query.taskId
      this.$api
        .getTaskDataById({ id: taskId })
        .then(res => {
          this.detailInfo = res
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
    //科室选择
    closeDeptPopup(list) {
      this.deptPopupShow = false
      if (list && list.length) {
        this.formItem.deptIds = Array.from(list).map((item) => item.id).join(',')
        this.formItem.deptNames = Array.from(list).map((item) => item.label).join(',')
        console.log(this.formItem.deptNames, 'dasdas')
      }
    },
    // 人员选择
    closePersonPopup(list) {
      this.personPopupShow = false
      if (list && list.length) {
        if (this.personType === 'tissue') {
          this.formItem.organizerId = list[0].id
          this.formItem.organizerName = list[0].staffName
        }
        if (this.personType === 'duty') {
          this.formItem.headIds = Array.from(list).map((item) => item.id).join(',')
          this.formItem.headNames = Array.from(list).map((item) => item.staffName).join(',')
        }
      }
    },
    // 责任人选择
    headPersonHandle() {
      this.personType = 'duty'
      this.personPopupShow = true
    },
    // 组织人选择
    organizerHandle() {
      this.personType = 'tissue'
      this.personPopupShow = true
    },
    goback() {
      this.goToCancelPage()
    },
    goToCancelPage() {
      this.$router.go(-1)
    },
    onTimeConfirm(date) {
      const [start, end] = date;
      this.timeShow = false;
      this.taskStartTime = moment(start).format("YYYY-MM-DD")
      this.taskEndTime = moment(end).format("YYYY-MM-DD")
      this.time = `${this.taskStartTime} - ${this.taskEndTime}`;
    },
    // 提交
    onSubmit() {
      const taskId = this.$route.query.taskId
      let data = {
        updateName: JSON.parse(localStorage.getItem("loginInfo")).staffName,
        updateId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        taskStartTime: this.taskStartTime,
        taskEndTime: this.taskEndTime,
        ...this.formItem,
        id: taskId,
      }
      this.$api
        .updateTaskNoticeData(data)
        .then(res => {
          Toast({
            type: 'success',
            message: '保存成功',
            duration: 1500
          })
          this.$router.go('-1')
        })
        .catch(res => {
          if (res.data.code == "500") {
            this.$toast.fail(res.data.msg);
          }
        });
    },
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  font-size: 14px;
  position: relative;
  color: #1d2129;
  background-color: #f2f4f9;
  .content {
    margin: 5px 5px;
  }
  .operationBtns {
    height: 1.32rem;
    padding: 0.16rem;
    margin-top: 0.2rem;
    box-sizing: border-box;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    width: 100%;
    margin: auto;
    border-top: 1px solid #eceef8;
    z-index: 2;
    .btns {
      display: flex;
      justify-content: space-around;
      .btn {
        width: 42%;
        padding: 0.16rem;
        font-size: 0.26rem;
        line-height: inherit;
      }
      .btn:nth-child(1) {
        background-color: #e6effc;
        color: #3562db;
      }
      .btn:nth-child(2) {
        background-color: #3562db;
      }
    }
  }
}
</style>
