<template>
  <div class="inner">
    <Header title="空间位置" @backFun="goBack">
      <div style="color: #3562db" @click="goSubmitRecord">提交记录</div>
    </Header>
    <div class="content">
      <div class="breadcrumb">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index"
            ><i @click="breadcrumbClick(index)" :style="index == breadcrumbList.length - 1 ? 'color:#3562DB' : ''">{{ item }}</i></el-breadcrumb-item
          >
        </el-breadcrumb>
      </div>
      <div style="height: 500px">
        <div class="hospital" v-if="breadcrumbList.length == 1">
          <div v-for="item in hospitalList" :key="item.id" @click="hospitalDown(item)">
            <div class="list">
              <span><img src="../../assets/images/hospitalIcon.png" alt="" />&nbsp;&nbsp; {{ item.ssmName }}</span>
              <span><van-icon name="arrow" /></span>
            </div>
          </div>
        </div>
        <div class="hospital" v-if="breadcrumbList.length > 1">
          <div v-if="deptList.length">
            <div v-for="item in deptList" :key="item.id" @click="deptDown(item)">
              <div class="list">
                <span
                  ><img src="../../assets/images/hospitalAreaIcon.png" alt="" v-if="item.ssmType != 4" /><img src="../../assets/images/storeyIcon.png" alt="" v-else />&nbsp;&nbsp;
                  {{ item.ssmName }}</span
                >
                <span v-if="item.ssmType != 4"><van-icon name="arrow" /></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import YBS from "@/assets/utils/utils.js";
import Vue from "vue";
import { Breadcrumb, BreadcrumbItem } from "element-ui";
Vue.use(Breadcrumb);
Vue.use(BreadcrumbItem);
export default {
  props: ["affiliatedPerson"],
  data() {
    return {
      treeList: [],
      spaceTreeData: [],
      current: 1,
      pageSize: 10,
      hospitalList: [],
      breadcrumbList: ["院区"],
      deptList: [],
      breadcrumLocationList: [] //存储面包屑节点的内容，方便回显
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getTree();
  },
  methods: {
    //提交记录跳转
    goSubmitRecord() {
      this.$router.push({
        path: "/submitRecord"
      });
    },
    goBack() {
      this.$YBS.apiCloudCloseFrame();
    },
    getTree() {
      this.$api.structureTree().then(res => {
        this.spaceTreeData = res;
        let spaceData = res.filter(item => item.ssmType !== 1);
        this.treeList = spaceData;
        this.hospitalList = YBS.transData(spaceData, "id", "pid", "children");
      });
    },
    ListTree(list, rootid) {
      var arr = [];
      list.forEach(item => {
        if (item.id === rootid) {
          arr.push(item.id);
          const newArr = this.ListTree(list, item.pid);
          if (newArr) {
            arr.unshift(...newArr);
          }
        }
      });
      return arr;
    },
    deptDown(item) {
      if (item.children) {
        this.deptList = item.children;
        this.breadcrumLocationList.push(item.children);
        this.breadcrumbList.push(item.ssmName);
      } else {
        if (item.ssmType != "4") {
          return this.$toast("暂无下级");
        } else {
          let treeIdArr = this.ListTree(this.spaceTreeData, item.pid);
          treeIdArr.push(item.id);
          this.$router.push({
            path: "/belongSpace",
            query: {
              simCode: treeIdArr.toString()
            }
          });
        }
      }
    },
    hospitalDown(row) {
      this.breadcrumbList.push(row.ssmName);
      this.breadcrumLocationList.push(row.children);
      this.deptList = row.children;
    },

    breadcrumbClick(index) {
      if (index == 0) {
        this.sliceArr(index);
      } else {
        this.sliceArr(index);
        this.deptList = this.breadcrumLocationList[index - 1];
      }
    },
    sliceArr(index) {
      let arr = this.breadcrumbList;
      this.breadcrumbList = arr.slice(0, index + 1);
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    height: calc(100vh - 200px);
    background-color: #fff;

    .breadcrumb {
      padding: 10px;
    }
    .hospital {
      padding: 5px 10px;
      > div {
        .list {
          font-size: 15px;
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid #e5e6eb;
          line-height: 35px;
          padding: 10px 0;
        }
      }
    }
  }
}
.notList {
  .emptyImg {
    position: absolute;
    height: 70%;
    width: 50%;
    left: 25%;
    // background: url("../../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
.el-breadcrumb__item {
  max-width: 80px; /* 或你需要的宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
