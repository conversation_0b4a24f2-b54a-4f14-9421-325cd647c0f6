<template>
  <div class="inner">
    <Header :title="equItemDetail.primaryFacilityTypeName" @backFun="goBack"></Header>
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left"><span class="lineTitle"></span><span>设备信息</span></div>
          </div>
          <div v-for="(item, index) in equItemDetail.secondDevices" :key="index" class="list-item">
            <img :src="$YBS.imgUrlTranslation(item.deviceImage)" alt="" />
            <div class="list-itemTitle">{{ item.secondaryFacilityTypeName }}</div>
            <van-stepper v-model="item.deviceQuantity" min="0" default-value="1" integer input-width="30px" button-size="20px" />
          </div>
        </div>
      </div>
    </div>

    <div class="buttom">
      <van-button color="#3562db" @click="submit">保存</van-button>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      equItemDetail: {
        secondDevices: []
      },
      otherEquList: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getDeviceList();
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },

    getDeviceList() {
      let arr = sessionStorage.getItem("otherEquList") ? JSON.parse(sessionStorage.getItem("otherEquList")) : [];
      arr.forEach(el => {
        let TypeId = el.primaryFacilityTypeId.split("/");
        if (TypeId[TypeId.length - 1] == this.$route.query.typeId) {
          this.equItemDetail = el;
        }
      });
    },
    submit() {
      let arr = JSON.parse(sessionStorage.getItem("otherEquList"));
      let obj = {
        primaryFacilityTypeName: this.equItemDetail.primaryFacilityTypeName,
        primaryFacilityTypeId: this.equItemDetail.primaryFacilityTypeId,
        deviceQuantity: this.equItemDetail.secondDevices.length,
        secondDevices: this.equItemDetail.secondDevices,
        status: 1
      };

      let noIndex = arr.findIndex(item => item.primaryFacilityTypeId === obj.primaryFacilityTypeId);
      if (noIndex !== -1) {
        arr[noIndex].secondDevices = obj.secondDevices;
        arr[noIndex].deviceQuantity = arr[noIndex].secondDevices.length;
      } else {
        arr.push(obj);
      }
      sessionStorage.setItem("otherEquList", JSON.stringify(arr));
      this.$router.push({
        name: "equDetail",
        query: {
          id: this.$route.query.id,
          addType: this.$route.query.addType,
          dataType: this.$route.query.dataType
        }
      });
    },
    addEqu() {
      let primaryFacilityTypeId = this.equItemDetail.primaryFacilityTypeId.split("/");
      let sessionStorageList = JSON.parse(sessionStorage.getItem("otherEquList"));
      const deviceQuantityMap = {};
      this.equItemDetail.secondDevices.forEach(e => {
        deviceQuantityMap[e.secondaryFacilityTypeId] = e.deviceQuantity;
      });
      sessionStorageList.forEach(item => {
        item.secondDevices.forEach(j => {
          if (deviceQuantityMap.hasOwnProperty(j.secondaryFacilityTypeId)) {
            j.deviceQuantity = deviceQuantityMap[j.secondaryFacilityTypeId];
          }
        });
      });
      sessionStorage.setItem("otherEquList", JSON.stringify(sessionStorageList));
      this.$router.push({
        name: "selectTypes",
        query: {
          id: this.$route.query.id,
          typeId: this.$route.query.typeId,
          primaryFacilityTypeId: primaryFacilityTypeId[0]
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  // height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 140px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .list-wrap {
        .list-title {
          font-size: 16px;
          color: #1d2129;
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          height: 0.58rem;

          .title-left {
            margin: auto 0;
            .lineTitle {
              height: 0.35rem;
              width: 0.1rem;
              background-color: #3562db;
              display: inline-block;
              margin-right: 6px;
              vertical-align: bottom;
            }
          }
        }

        .list-item {
          font-size: 16px;
          padding: 10px 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid #ccc;
          &:last-child {
            border-bottom: none;
          }
          img {
            width: 88px;
            height: 88px;
            margin-right: 10px;
          }
          .list-itemTitle {
            width: 150px;
            color: #4e5969;
          }
          .list-itemContent {
            color: #1d2129;
            word-wrap: break-word;
          }
        }
      }
    }
  }
  .buttom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 60px;
    line-height: 40px;
    padding: 10px;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    .van-button {
      // width: calc(50% - 4px);
      width: calc(100% - 8px);
      height: 40px;
    }
  }
}
</style>
