<template>
  <div class="inner">
    <Header title="设施登记" @backFun="goBack"></Header>
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left"><span class="lineTitle"></span><span>设备信息</span></div>
          </div>
          <van-cell :title="`${item.primaryFacilityTypeName}(${item.deviceQuantity})`" v-for="(item, index) in deviceList" :key="index" is-link @click="goItemDetail(item)">
            <span :style="`color:${item.status == 0 ? '#F53F3F' : '#00B42A'}`">{{ item.status == 0 ? "未查看" : "已查看" }}</span>
          </van-cell>
        </div>
      </div>
    </div>

    <div class="buttom">
      <div class="bottomTips">
        <van-icon name="info" />
        <span>需查看所有待提交的设备信息后才可以提交登记单</span>
      </div>
      <div class="btnGroup">
        <!-- 底部 -->
        <van-button color="#3562db" plain @click="equOtherType">新增设施</van-button>
        <van-button color="#3562db" @click="submit">提交</van-button>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      deviceList: []
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  mounted() {
    this.getDeviceTypeList();
  },
  methods: {
    goBack() {
      sessionStorage.removeItem("otherEquList");
      this.$router.replace({
        name: "spaceDeviceReg",
        query: {
          roomCode: this.$route.query.id,
          from: "H5"
        }
      });
    },

    // 获取设备列表
    getDeviceTypeList() {
      let arr = sessionStorage.getItem("otherEquList") ? JSON.parse(sessionStorage.getItem("otherEquList")) : [];
      if (arr.length) {
        this.deviceList = this.deviceList.concat(arr);
      } else {
        this.$api.spaceDeviceList({ id: this.$route.query.id }).then(res => {
          this.deviceList = res.devices || [];
          if (this.deviceList.length) {
            sessionStorage.setItem("otherEquList", JSON.stringify(this.deviceList));
          }
        });
      }
    },
    goItemDetail(item) {
      if (item.primaryFacilityTypeId == -1) return;
      let TypeIds = item.primaryFacilityTypeId.split("/");
      this.$router.push({
        name: "equItemDetail",
        query: {
          id: this.$route.query.id,
          typeId: TypeIds[TypeIds.length - 1],
          dataType: item.dataType
        }
      });
    },
    equOtherType() {
      this.$router.push({
        name: "selectType",
        query: {
          id: this.$route.query.id
        }
      });
    },
    submit() {
      let noView = this.deviceList.findIndex(item => item.status != 1);
      if (noView !== -1) {
        this.$toast.fail("全部查看之后才可提交");
        return;
      }
      if (this.deviceList.length == 0) {
        this.$toast.fail("请添加设备");
        return;
      }

      this.$api.lookUpById({ id: this.$route.query.id }).then(result => {
        this.$api
          .saveRegistration({
            devices: this.deviceList,
            spaceId: this.$route.query.id,
            fullSpaceId: result.simCode,
            initiatorDepartment: this.loginInfo.deptId,
            initiator: this.loginInfo.staffId
          })
          .then(res => {
            sessionStorage.removeItem("otherEquList");
            sessionStorage.removeItem("equipmentIds");
            this.$router.push({ name: "equRegResult", query: { id: this.$route.query.id } });
            this.$toast.success(res.msg);
          });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  // height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 140px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }

    .list-wrap {
      .list-title {
        font-size: 16px;
        color: #1d2129;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        height: 0.58rem;

        .title-left {
          margin: auto 0;
          .lineTitle {
            height: 0.35rem;
            width: 0.1rem;
            background-color: #3562db;
            display: inline-block;
            margin-right: 6px;
            vertical-align: bottom;
          }
        }
      }

      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 92px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
  .buttom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    // height: 90px;
    background-color: #fff;
    box-sizing: border-box;
    .bottomTips {
      height: 40px;
      box-sizing: border-box;
      background: #fff7e8;
      color: #ff7d00;
      padding: 10px 5px;
      line-height: 20px;
      text-align: center;
    }
    .btnGroup {
      display: flex;
      height: 40px;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;

      .van-button {
        width: calc(50% - 8px);
        height: 40px;
      }
    }
  }
}
</style>
