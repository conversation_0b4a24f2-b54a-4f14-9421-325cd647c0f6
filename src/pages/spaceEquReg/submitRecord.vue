<template>
  <div class="inner" style="height: 100%'">
    <Header title="提交记录" @backFun="goBack"></Header>
    <div v-if="list.length > 0">
      <van-steps direction="vertical" :active="list.length" active-color="#3562db">
        <div v-for="item in list" :key="item.id" @click="goDetail(item)">
          <van-step>
            <p style="font-weight: bold" class="heightRow">{{ item.initiationTime }}</p>
            <div style="display: flex; justify-content: space-between">
              <div class="heightRow" style="color: #4e5969">
                {{ item.initiator }}&nbsp;&nbsp;<span class="deptName">{{ item.initiatorDepartment }}</span>
              </div>
              <div class="state">
                <span :class="['auditStatus', `auditStatus${item.status}`]"> {{ item.statusName }}</span>
              </div>
            </div>
            <div class="txt">
              <div class="txt_l">房间名称</div>
              <div>{{ item.ssmName }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">房间号</div>
              <div>{{ item.roomCode }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">所属位置</div>
              <div>{{ item.simName }}</div>
            </div>
          </van-step>
        </div>
      </van-steps>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getList();
  },
  methods: {
    getList() {
      this.$api
        .submitRecord({
          spaceIds: [],
          pageSize: 10,
          currentPage: 1
        })
        .then(res => {
          this.list = res.records;
        });
    },
    goDetail(item) {
      this.$router.push({
        name: "submitRecordDetail",
        query: {
          registrationId: item.registrationId
        }
      });
      console.log(item);
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #fff;
  font-size: 14px;
  overflow: auto;
  color: #353535;
  margin: 5px 0 0 0;
  .heightRow {
    line-height: 30px;
  }
}
/deep/ .van-step__line {
  background-color: #e5e6eb !important;
}
.notList {
  position: relative;
  height: calc(100% - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
/deep/ .el-table__header-wrapper th {
  background-color: #e6effc; /* 自定义表头背景颜色 */
  color: #1d2129;
}
.txt {
  display: flex;
  padding: 0 15px 0 0;
  justify-content: space-between;
  > div {
    line-height: 30px;
  }
  .txt_l {
    color: #4e5969;
  }
}
.state {
  .auditStatus {
    padding: 5px;
    border-radius: 4px;
  }
  .auditStatus0 {
    background: #ffece8;
    color: #f53f3f;
  }
  .auditStatus1 {
    background: #e8ffea;
    color: #00b42a;
  }
  .auditStatus2 {
    background: #ffece8;
    color: #f53f3f;
  }
}
.deptName {
  display: inline-block;
  background: #f2f3f5;
  padding: 0 5px;
  line-height: 22px;
  border-radius: 4px;
}
</style>
