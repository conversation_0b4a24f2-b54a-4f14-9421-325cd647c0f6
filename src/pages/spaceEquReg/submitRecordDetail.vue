<template>
  <div class="inner">
    <Header title="设施登记" @backFun="goBack"></Header>
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left"><span class="lineTitle"></span><span>设备信息</span></div>
          </div>
          <van-collapse v-model="activeNames" accordion>
            <van-collapse-item :title="`${item.primaryFacilityTypeName}(${item.deviceQuantity})`" v-for="(item, index) in deviceList" :key="index" :name="index">
              <span slot="value" :style="`color:${item.status == 0 ? '#F53F3F' : '#00B42A'}`">{{ item.status == 0 ? "未查看" : "已查看" }}</span>
              <van-cell v-for="(el, i) in item.secondDevices" :title="el.secondaryFacilityTypeName" :key="i" :value="el.deviceQuantity"></van-cell>
            </van-collapse-item>
          </van-collapse>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      deviceList: [],
      activeNames: "0",
      childActive: []
    };
  },

  mounted() {
    this.getDeviceTypeList();
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },

    // 获取设备列表
    getDeviceTypeList() {
      this.$api.submitRecordDetail({ id: this.$route.query.registrationId }).then(res => {
        this.deviceList = res.devices || [];
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  // height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 140px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }

    .list-wrap {
      .list-title {
        font-size: 16px;
        color: #1d2129;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        height: 0.58rem;

        .title-left {
          margin: auto 0;
          .lineTitle {
            height: 0.35rem;
            width: 0.1rem;
            background-color: #3562db;
            display: inline-block;
            margin-right: 6px;
            vertical-align: bottom;
          }
        }
      }

      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 92px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
}
</style>
