<template>
  <div class="inner">
    <Header :title="headerTitle" @backFun="goBack"></Header>
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <van-cell
            style="margin-bottom: 2px"
            v-for="(item, index) in facilitiesList"
            is-link
            clickable
            :key="index"
            :title="title(item)"
            :value="statusType(item.status)"
            @click="toggle(item)"
            :class="[`backgroundStatus${item.status}`]"
            :value-class="[`colorStatus${item.status}`]"
          >
          </van-cell>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      headerTitle: "",
      facilitiesList: []
    };
  },
  activated() {},
  created() {
    this.facilitiesList = JSON.parse(this.$route.query.facilitiesList);
    this.headerTitle = this.$route.query.title;
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    toggle(item) {
      this.$router.push({
        name: "selectEqu",
        query: {
          id: item.secondaryFacilityTypeId,
          type: this.$route.query.type || ""
        }
      });
    },
    title(item) {
      if (this.$route.query.type != "details" && item.status != 2) {
        if (item.status == 1) {
          return item.secondaryFacilityTypeName + " " + "(" + item.deviceQuantity + ")";
        } else {
          return item.secondaryFacilityTypeName;
        }
      } else {
        return item.secondaryFacilityTypeName + " " + "(" + item.deviceQuantity + ")";
      }
    },
    statusType(status) {
      const statusMap = {
        0: "未清查",
        1: "已清查",
        2: ""
      };
      return statusMap[status];
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  height: 100%;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    // height: 100vh;
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
  }
  .buttom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    // height: 90px;
    background-color: #fff;
    box-sizing: border-box;
    .bottomTips {
      height: 40px;
      box-sizing: border-box;
      background: #e6effc;
      color: #2749bf;
      padding: 10px 5px;
      line-height: 20px;
      text-align: center;
    }
    .btnGroup {
      display: flex;
      height: 40px;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;

      .van-button {
        width: 100%;
        height: 40px;
      }
    }
  }
}
.backgroundStatus0 {
  background-color: #ffece8;
}
.backgroundStatus1 {
  background-color: #e8ffea;
}
.colorStatus0 {
  color: #f53f3f;
}
.colorStatus1 {
  color: #00b42a;
}
</style>
