<template>
  <div class="inner" style="height: 100%'">
    <Header title="变动记录" @backFun="goBack"></Header>
    <div v-if="list.length > 0">
      <van-steps direction="vertical" :active="list.length" active-color="#3562db">
        <van-step v-for="item in list" :key="item.id">
          <p style="font-weight: bold" class="heightRow">{{ changeTime(item.changeTime) }}</p>
          <p class="heightRow">{{ item.operator }}&nbsp;&nbsp;{{ item.operatorDepartment }}</p>
          <el-table :data="item.records" stripe>
            <el-table-column label="设施类型" prop="facilityTypeName"></el-table-column>
            <el-table-column label="变更前" prop="quantityBefore"></el-table-column>
            <el-table-column label="变更后" prop="quantityAfter"></el-table-column>
          </el-table>
        </van-step>
      </van-steps>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  data() {
    return {
      list: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getList();
  },
  methods: {
    changeTime(val) {
      return moment(val).format("YYYY-MM-DD HH:mm:ss");
    },
    goBack() {
      this.$router.go(-1);
    },
    getList() {
      this.$api
        .changeRecord({
          id: this.$route.query.id,
          page: {
            current: 1,
            size: 10
          }
        })
        .then(res => {
          this.list = res.list;
        });
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  background-color: #fff;
  font-size: 14px;
  overflow: auto;
  color: #353535;
  margin: 5px 0 0 0;
  .heightRow {
    line-height: 25px;
  }
}
/deep/ .van-step__line {
  background-color: #e5e6eb !important;
}
.notList {
  position: relative;
  height: 50px;
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
/deep/ .el-table__header-wrapper th {
  background-color: #e6effc; /* 自定义表头背景颜色 */
  color: #1d2129;
}
</style>
