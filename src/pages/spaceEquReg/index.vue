<template>
  <div class="inner">
    <Header title="空间设施登记" @backFun="goBack">
      <div style="color: #3562db" @click="goTransitionRegister">变动记录</div>
    </Header>
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left"><span class="lineTitle"></span><span>空间信息</span></div>
          </div>
          <div class="list-item" v-for="(item, index) in labelList" :key="index">
            <span class="list-itemTitle">{{ item.name }}</span>
            <span class="list-itemContent">{{ roomInfo[item.key] }}</span>
          </div>
        </div>
      </div>
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left"><span class="lineTitle"></span><span>设施信息</span></div>
          </div>
          <van-cell
            style="margin-bottom: 2px"
            v-for="(item, index) in deviceList"
            is-link
            clickable
            :key="index"
            :title="title(item)"
            @click="toggle(item)"
            :class="[`backgroundStatus${item.status}`]"
            :value-class="[`colorStatus${item.status}`]"
          >
          </van-cell>
        </div>
      </div>
    </div>
    <div class="buttom">
      <!-- 底部 -->
      <van-button color="#3562db" @click="equReg">开始设施清查</van-button>
    </div>
  </div>
</template>
<script>
import { get } from "jquery";

export default {
  computed: {},

  data() {
    return {
      labelList: [
        {
          name: "房间名称：",
          key: "localSpaceName"
        },
        {
          name: "房间号：",
          key: "roomCode"
        },
        {
          name: "位置：",
          key: "simName"
        },
        {
          name: "归属科室：",
          key: "dmName"
        }
      ],
      roomInfo: {
        simName: ""
      },
      deviceList: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getSpaceInfo();
    this.getDeviceList();
  },
  methods: {
    toggle(item) {
      let facilitiesList = [];
      item.children.forEach(z => {
        delete z.children;
        facilitiesList.push(z);
      });
      this.$router.push({
        path: "/selectTypes",
        query: {
          facilitiesList: JSON.stringify(facilitiesList),
          type: "details",
          title: item.secondaryFacilityTypeName
        }
      });
    },
    title(item) {
        return item.secondaryFacilityTypeName + " " + "(" + item.deviceQuantity + ")";
    },
    //跳转变更记录
    goTransitionRegister() {
      this.$router.push({
        path: "/transitionRegister",
        query: {
          id: localStorage.getItem("roomCode")
        }
      });
    },
    // 获取空间信息
    getSpaceInfo() {
      this.$api.lookUpById({ id: localStorage.getItem("roomCode") }).then(res => {
        this.roomInfo = res;
      });
    },
    // 获取设备列表
    getDeviceList() {
      this.$api.spaceDeviceList({ spaceId: localStorage.getItem("roomCode") }).then(res => {
        this.deviceList = res;
      });
    },
    goBack() {
      if (localStorage.getItem("JumpType") == "H5") {
        this.$router.push({
          path: "spaceFacilities"
        });
      } else {
        this.$YBS.apiCloudCloseFrame();
      }
      localStorage.removeItem("otherEquList");
      localStorage.removeItem("JumpType");
      localStorage.removeItem("roomCode");
    },
    equReg() {
      this.$router.push({ name: "selectType" });
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  // height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 140px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }

    .list-wrap {
      .list-title {
        font-size: 16px;
        color: #1d2129;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        height: 0.58rem;

        .title-left {
          margin: auto 0;
          .lineTitle {
            height: 0.35rem;
            width: 0.1rem;
            background-color: #3562db;
            display: inline-block;
            margin-right: 6px;
            vertical-align: bottom;
          }
        }
      }

      .list-item {
        font-size: 16px;
        padding: 10px 0;
        display: flex;
        .list-itemTitle {
          min-width: 92px;
          color: #4e5969;
        }
        .list-itemContent {
          color: #1d2129;
          word-wrap: break-word;
        }
      }
    }
  }
  .buttom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 60px;
    line-height: 40px;
    padding: 10px;
    background-color: #fff;
    box-sizing: border-box;
    .van-button {
      width: 100%;
      height: 40px;
    }
  }
}
</style>
