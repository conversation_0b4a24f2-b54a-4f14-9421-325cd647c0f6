<template>
  <div class="inner">
    <Header title="空间设施登记" @backFun="goBack"></Header>
    <van-search v-model="localSpaceName" background="#fff" clearable placeholder="搜索空间名称" @input="getSpaceList"> </van-search>
    <div v-if="tableData.length > 0" class="content">
      <div class="titSty">
        <p v-for="(item, index) in title" :key="index">{{ item.name }}</p>
      </div>
      <div class="content-baseInfo">
        <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <div v-for="(item, index) in tableData" :key="index" class="listSty" @click="details(item)">
              <p>{{ index + 1 }}</p>
              <p>
                {{ item.localSpaceName }}
              </p>
              <p>
                {{ item.roomCode }}
              </p>
              <p>{{ item.simName }}</p>
              <p>{{ item.functionDictName }}</p>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      localSpaceName: "",
      title: [
        {
          name: "序号"
        },
        {
          name: "空间名称"
        },
        {
          name: "房间号"
        },
        {
          name: "所属位置"
        },
        {
          name: "功能类型"
        }
      ],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      size: 20,
      total: 0,
      tableData: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.spaceList();
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    details(row) {
      this.$router.push({
        path: "/spaceDeviceReg",
        query: {
          roomCode: row.id
        }
      });
      localStorage.setItem("roomCode", row.id);
      localStorage.setItem("JumpType", "H5");
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.tableData = [];
      this.spaceList();
    },
    getSpaceList() {
      if (this.localSpaceName) {
        this.tableData = [];
        this.current = 1;
        this.spaceList();
      } else {
        this.clearCancel();
      }
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.current++;
      this.spaceList();
    },

    clearCancel() {
      this.finished = false;
      this.loading = true;
      this.localSpaceName = "";
      this.current = 1;
      this.tableData = [];
      this.spaceList();
    },
    spaceList() {
      let params = {
        hospitalCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
        unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
        queryType: this.queryType,
        current: this.current,
        size: this.size,
        simCode: "",
        keywords: this.localSpaceName,
        simCode: "#," + this.$route.query.simCode
      };

      this.$api.spaceList(params).then(res => {
        this.loading = false;

        res.records.forEach(item => {
          this.tableData.push(item);
        });

        if (this.tableData.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    }
  }
};
</script>

<style scoped lang="stylus">
.inner {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sty {
  font-size: 18px;
  font-weight: bold;
}

.col {
  color: #86909C;
}

.p {
  line-height: 30px;
}

/deep/ .van-nav-bar__title {
  margin: 0 15px;
}

/deep/ .van-grid-item__content {
  padding: 12px 0;
}

.titSty {
  height: 30px;
  display: flex;
  align-items: center;
  padding: 10px 0;
  background-color: #e6effc;
  justify-content: space-around;

  // margin-top:60px;
  >P:nth-child(1) {
    font-weight: bold;
    text-align: center;
    flex: 1;
  }

  >P:nth-child(2) {
    font-weight: bold;
    text-align: center;
    flex: 2;
  }

  >P:nth-child(3) {
    font-weight: bold;
    text-align: center;
    flex: 2;
  }

  >P:nth-child(4) {
    font-weight: bold;
    text-align: center;
    flex: 2;
  }

  >P:nth-child(5) {
    font-weight: bold;
    text-align: center;
    flex: 2;
  }
}

.listSty {
  display: flex;
  justify-content: space-around;
  align-items: center;
  // margin-top:5px;
  border-bottom: 1px solid #eee;

  >P:nth-child(1) {
    text-align: center;
    flex: 1;
    line-height: 20px;
    padding: 7px 0;
    // height:30px
  }

  >P:nth-child(2) {
    text-align: center;
    flex: 2;
    line-height: 20px;
    padding: 7px 0;
    // height:30px
  }

  >P:nth-child(3) {
    text-align: center;
    flex: 2;
    line-height: 20px;
    padding: 7px 0;
    // height:30px
  }

  >P:nth-child(4) {
    text-align: center;
    flex: 2;
    line-height: 20px;
    padding: 7px 0;
    // height:30px
  }

  >P:nth-child(5) {
    text-align: center;
    flex: 2;
    line-height: 20px;
    padding: 7px 0;
    // height:30px
  }
}

.color {
  width: 100%;
  height: 100%;
  color: #3562DB !important;
  background-color: #f3f7ff;
  padding: 10px;
  1px;
}

.nav {
  // height:200px;
  // overflow: scroll
}

.content {
  height: calc(100% - 130px);
}

.content-baseInfo {
  height: calc(100% - 55px);
  overflow: auto;
}

.notList {
  position: relative;
  height: calc(90% - 1.24rem);

  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url('../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
    background-size: 100% auto;

    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}

.color1 {
  color: #3562DB;
}

.flexbj {
  margin-top: 10px;
  display: flex;
  justify-content: space-around;
  text-align: center;

  >div {
    flex: 1;
  }
}
</style>
