<template>
  <div class="inner">
    <Header title="未分类设备" @backFun="goback"></Header>
    <van-form @submit="onSubmit">
      <div style="width: 100%; height: calc(100vh - 140px); overflow-y: scroll">
        <div class="listStyle" v-for="(item, index) in equlist" :key="index">
          <div class="title">
            <div style="display: flex; align-items: center"><i class="public"></i>设备{{ index + 1 }}</div>
            <van-icon v-if="equlist.length > 1" name="delete-o" color="red" @click="deleteItem(index)" />
          </div>
          <div class="list_item">
            <div class="situation">
              设备数量
              <van-stepper v-model="item.deviceQuantity" min="1" default-value="1" integer />
            </div>
          </div>
          <div class="list_item">
            <div class="situation">设备描述</div>
            <van-field v-model="item.repairExplain" rows="2" autosize type="textarea" maxlength="200" :placeholder="`请输入设备描述`" show-word-limit />
          </div>
          <div class="list_item">
            <div class="situation">
              <span>附件图片</span>
              <span style="color: #ccc">{{ item.attachmentUrl.length }}/3</span>
            </div>
            <van-uploader
              ref="uplodImg"
              v-model="item.fileList"
              :max-count="3"
              accept="image/*"
              :after-read="file => afterRead(file, index)"
              :before-read="beforeRead"
              @delete="file => deleteImg(file, index)"
              :preview-full-image="false"
              @click-preview="previewImg(index)"
            >
            </van-uploader>
          </div>
        </div>
        <div class="add_btn" @click="addEqu">
          <van-icon name="plus" />
          <span>添加设备</span>
        </div>
      </div>
    </van-form>
    <div class="buttom">
      <van-button color="#3562db" @click="onSubmit">下一步</van-button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import ImageCompressor from "image-compressor.js";
import { Toast, ImagePreview } from "vant";
import axios from "axios";
export default {
  data() {
    return {
      equlist: [
        {
          repairExplain: "",
          attachmentUrl: [],
          fileList: []
        }
      ]
    };
  },
  computed: {
    ...mapState(["h5Mode"])
  },
  created() {
    if (JSON.parse(localStorage.getItem("otherEquList"))) {
      this.equlist = JSON.parse(localStorage.getItem("otherEquList"));
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    deleteItem(index) {
      this.equlist.splice(index, 1);
    },
    addEqu() {
      let obj = {
        repairExplain: "",
        attachmentUrl: [],
        fileList: []
      };
      this.equlist.push(obj);
    },
    beforeRead(file) {
      let typeList = ["image/png", "image/jpg", "image/jpeg"];
      if (!typeList.includes(file.type)) {
        Toast("请上传 jpg 格式图片");
        return false;
      }
      return true;
    },
    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        if (this.h5Mode == "apicloud") {
          resolve(file.file);
        } else {
          new ImageCompressor(file.file, {
            quality: 0.6,
            checkOrientation: false,
            success(res) {
              let file = new window.File([res], res.name, { type: res.type });
              resolve(file);
            },
            error(e) {
              reject();
            }
          });
        }
      });
    },
    afterRead(files, index) {
      files.status = "uploading";
      files.message = "上传中...";
      // 使用formdata上传
      const params = new FormData();
      params.append("file", files.file);
      axios
        .post(__PATH.SPACE_API + "/lease/upload", params, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          if ((res.data.code = "200")) {
            this.equlist[index].attachmentUrl.push({
              url: res.data.data
            });
            files.status = "success";
            files.message = "上传成功";
          } else {
            files.status = "failed";
            files.message = "上传失败";
          }
        });
    },

    deleteImg(file, index) {
      this.equlist[index].attachmentUrl.splice(index, 1);
    },
    previewImg(index) {
      ImagePreview({
        images: this.equlist[index].fileList.map(i => i.content),
        showIndex: true, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    },
    onSubmit() {
      let noPicIndex = this.equlist.findIndex(item => item.attachmentUrl.length === 0);
      if (noPicIndex !== -1) {
        this.$toast.fail("请上传设备图片");
        return;
      }
      localStorage.setItem("otherEquList", JSON.stringify(this.equlist));
      this.$router.push({
        name: "selectType",
        query: {
          id: this.$route.query.id
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  .listStyle {
    width: 100%;
    background-color: #fff;
    margin-top: 10px;
    padding: 16px;
    box-sizing: border-box;
    &:first-child {
      margin-top: 0px;
    }
    .title {
      color: #1d2129;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      .public {
        display: inline-block;
        width: 4px;
        height: 16px;
        margin-right: 5px;
        background: #3562db;
      }
    }
    .list_item {
      margin-bottom: 16px;
      border-bottom: 1px solid #e5e5e5;
      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
      .situation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }
    }
  }

  .add_btn {
    width: 100%;
    background: #fff;
    padding: 10px;
    padding-top: 0;
    box-sizing: border-box;
    text-align: center;
    color: #3562db;
  }
}

.buttom {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 60px;
  line-height: 40px;
  padding: 10px;
  background-color: #fff;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  .van-button {
    width: 100%;
    height: 40px;
  }
}
</style>
