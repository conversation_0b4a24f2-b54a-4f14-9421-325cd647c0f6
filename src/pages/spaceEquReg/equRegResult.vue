<template>
  <div class="inner">
    <Header :showLeftBtn="false" title="空间设施登记" ></Header>
    <div class="content">
      <div class="content-baseInfo">
        <img src="@/assets/images/icon-wrapper.png" alt="">
        <div class="result">提交成功</div>
        <div class="tips">空间设施登记表提交成功</div>
        <div class="btnGrout">
          <van-button color="#3562db" type="primary" @click="goRecord">查看申请</van-button>
          <van-button color="#3562db" type="primary" plain @click="goBack">返回</van-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  methods: {
    goRecord () {
      this.$router.push({
        name: 'submitRecord',
        query: {
          id: this.$route.query.id
        }
      })
    },
    goBack () {
      this.$router.replace({
        name: 'spaceDeviceReg',
        query: {
          roomCode: this.$route.query.id,
           from:"H5"
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.inner {
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  font-size: 14px;
  color: #353535;
  box-sizing: border-box;
  .content{
    box-sizing: border-box;
    margin: 5px 5px 10px 5px;
    height: calc(100% - 140px);
    .content-baseInfo {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 10px 10px;
      margin-bottom: 5px;
      padding-bottom: 100px;
      box-sizing: border-box;
      img{
        width: 48px;
        height: 48px;
      }
      .result{
        margin-top: 20px;
        font-size: 18px;
        color: #1D2129;
      }
      .tips{
        margin-top: 30px;
        font-size: 14px;
        color: #9CA2A9;
      }
      .btnGrout{
        margin-top: 20px;
        width: 100%;
        box-sizing: border-box;
        .van-button{
          width: 100%;
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
