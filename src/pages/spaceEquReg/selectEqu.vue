<template>
  <div class="inner">
    <Header :title="headerTitle" @backFun="goBack"></Header>
    <div class="content">
      <div class="content-baseInfo">
        <div class="list-wrap">
          <div class="list-title">
            <div class="title-left"><span class="lineTitle"></span><span>设备信息</span></div>
          </div>
          <div v-for="(item, index) in facilitiesList" :key="index" class="list-item">
            <img :src="$YBS.imgUrlTranslation(item.deviceImage)" alt="" />
            <div class="list-itemTitle">{{ item.secondaryFacilityTypeName }}</div>
            <van-stepper
              v-model="item.deviceQuantity"
              @change="value => stepperChange(value, index)"
              integer
              min="0"
              max="999"
              input-width="30px"
              button-size="20px"
              default-value=""
              placeholder="请输入"
              :disabled="$route.query.type == 'details'"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="buttom" v-if="$route.query.type != 'details'">
      <div class="btnGroup">
        <!-- 底部 -->
        <van-button color="#3562db" @click="next">保存</van-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      headerTitle: "",
      facilitiesList: []
    };
  },
  created() {},
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getQueryFacilityDeviceByApp();
  },
  methods: {
    stepperChange(value, index) {
      if (value === "" || value === null) {
        this.facilitiesList[index].deviceQuantity = 0;
      } else {
        this.facilitiesList[index].deviceQuantity = value;
      }
    },
    getQueryFacilityDeviceByApp() {
      let params = {
        spaceId: localStorage.getItem("roomCode"),
        primaryFacilityTypeId: this.$route.query.id
      };
      this.$api.queryFacilityDeviceByApp(params).then(res => {
        this.headerTitle = res[0].primaryFacilityTypeName;
        if (this.$route.query.type == "details") {
          this.facilitiesList = res.filter(item => item.deviceQuantity != 0);
        } else {
          this.facilitiesList = res;
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    next() {
      let params = {
        spaceId: localStorage.getItem("roomCode"),
        primaryFacilityTypeId: this.facilitiesList[0].primaryFacilityTypeId,
        primaryFacilityTypeName: this.facilitiesList[0].primaryFacilityTypeName,
        jsonString: JSON.stringify(this.facilitiesList)
      };
      this.$api.saveDeviceBuffer(params).then(res => {
        this.$router.push({
          name: "selectType"
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  // height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 140px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
      .list-wrap {
        .list-title {
          font-size: 16px;
          color: #1d2129;
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          height: 0.58rem;

          .title-left {
            margin: auto 0;
            .lineTitle {
              height: 0.35rem;
              width: 0.1rem;
              background-color: #3562db;
              display: inline-block;
              margin-right: 6px;
              vertical-align: bottom;
            }
          }
        }

        .list-item {
          font-size: 16px;
          padding: 10px 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid #ccc;
          &:last-child {
            border-bottom: none;
          }
          img {
            width: 88px;
            height: 88px;
            margin-right: 10px;
          }
          .list-itemTitle {
            width: 150px;
            color: #4e5969;
          }
          .list-itemContent {
            color: #1d2129;
            word-wrap: break-word;
          }
        }
      }
    }
  }
  .buttom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    // height: 90px;
    background-color: #fff;
    box-sizing: border-box;

    .btnGroup {
      display: flex;
      height: 40px;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;

      .van-button {
        width: 100%;
        height: 40px;
      }
    }
  }
}
</style>
