<template>
  <div class="inner">
    <Header title="选择类型" @backFun="goBack"></Header>
    <div class="content">
      <div class="content-baseInfo" v-if="equTypeList.length">
        <div class="list-wrap">
          <van-cell
            style="margin-bottom: 2px"
            v-for="(item, index) in equTypeList"
            is-link
            clickable
            :key="index"
            :value="statusType(item.status)"
            :title="title(item)"
            @click="toggle(item)"
            :class="[`backgroundStatus${item.status}`]"
            :value-class="[`colorStatus${item.status}`]"
          >
          </van-cell>
        </div>
        <div class="list-wrap" v-if="unknoisList != ''">
          <van-cell is-link clickable :title="`未知设备(${unknoisList.length})`" @click="goItemDetail"> </van-cell>
        </div>
      </div>
    </div>

    <div class="buttom">
      <div class="btnGroup">
        <!-- 底部 -->
        <van-button color="#3562db" plain @click="reportType">登记未知设施</van-button>
        <van-button color="#3562db" @click="submit">提交</van-button>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      equTypeList: [],
      result: [],
      switchFlag: "",
      unknoisList: ""
    };
  },
  created() {
    if (JSON.parse(localStorage.getItem("otherEquList"))) {
      this.unknoisList = JSON.parse(localStorage.getItem("otherEquList"));
    }
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getEquType();
  },
  methods: {
    title(item) {
      if (item.status != 2) {
        if (item.status == 1) {
          return item.secondaryFacilityTypeName + " " + "(" + item.deviceQuantity + ")";
        } else {
          return item.secondaryFacilityTypeName;
        }
      } else {
        return item.secondaryFacilityTypeName + " " + "(" + item.deviceQuantity + ")";
      }
    },
    statusType(status) {
      const statusMap = {
        0: "未清查",
        1: "已清查",
        2: ""
      };
      return statusMap[status];
    },
    submit() {
      let flag = this.equTypeList.some(item => item.status == 0);
      if (flag)
        return this.$dialog
          .confirm({
            title: "存在未清查项",
            message: "完成所有项目清查后才能提交",
            confirmButtonColor: "#3562db",
            showCancelButton: false
          })
          .then(() => {});

      let deviceList = [];
      if (localStorage.getItem("otherEquList")) {
        let equlist = JSON.parse(localStorage.getItem("otherEquList"));

        let obj = {
          primaryFacilityTypeName: "未分类设备",
          primaryFacilityTypeId: "-1",
          secondDevices: []
        };
        equlist.forEach(el => {
          let newEquItem = {
            secondaryFacilityTypeId: "",
            secondaryFacilityTypeName: "",
            deviceQuantity: el.deviceQuantity,
            deviceImage: el.attachmentUrl.map(el => el.url).join(","),
            deviceDescription: el.repairExplain
          };
          obj.secondDevices.push(newEquItem);
        });
        deviceList.push(obj);
      }

      this.equTypeList.forEach(i => {
        i.children.forEach(j => {
          let obj2 = {};
          obj2.primaryFacilityTypeId = i.secondaryFacilityTypeId + "/" + j.secondaryFacilityTypeId;
          obj2.primaryFacilityTypeName = i.secondaryFacilityTypeName + "/" + j.secondaryFacilityTypeName;
          obj2.secondDevices = j.children;
          deviceList.push(obj2);
        });
      });
      deviceList.forEach(k => {
        k.secondDevices.forEach(z => {
          z.deviceDescription = z.deviceDescription ? z.deviceDescription : null;
          delete z.children;
          delete z.status;
        });
      });
      this.$api.lookUpById({ id: localStorage.getItem("roomCode") }).then(result => {
        this.$api
          .saveRegistration({
            devices: deviceList,
            spaceId: localStorage.getItem("roomCode"),
            fullSpaceId: result.simCode,
            initiatorDepartment: this.loginInfo.deptId,
            initiator: this.loginInfo.staffId
          })
          .then(res => {
            localStorage.removeItem("otherEquList");
            this.$router.push({ name: "equRegResult", query: { id: localStorage.getItem("roomCode") } });
            this.$toast.success(res.msg);
          });
      });
    },
    goItemDetail() {
      this.$router.push({
        name: "reportType"
      });
    },
    getEquType() {
      this.$api
        .queryFacilityDeviceTreeByApp({
          spaceId: localStorage.getItem("roomCode")
        })
        .then(res => {
          this.equTypeList = res;
        });
    },
    goBack() {
      this.$router.push({
        path: "spaceDeviceReg"
      });
    },
    toggle(item) {
      let facilitiesList = [];
      item.children.forEach(z => {
        delete z.children;
        facilitiesList.push(z);
      });
      this.$router.push({
        path: "/selectTypes",
        query: {
          facilitiesList: JSON.stringify(facilitiesList),
          title: item.secondaryFacilityTypeName
        }
      });
    },
    reportType() {
      this.$api.getSpaceFacilityConfig().then(res => {
        if (res.switchFlag == 0) {
          this.$dialog
            .confirm({
              title: "无法登记未知设施",
              message: "未开启管理员审核无法登记未知设施",
              confirmButtonColor: "#3562db",
              showCancelButton: false
            })
            .then(() => {});
        } else {
          this.$router.push({ name: "reportType", query: { id: localStorage.getItem("roomCode") } });
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  width: 100vw;
  // height: 100vh;
  background-color: #f2f4f9;
  font-size: 14px;
  overflow-y: scroll;
  color: #353535;
  .content {
    margin: 5px 5px 10px 5px;
    height: calc(100vh - 140px);
    overflow: scroll;
    .content-baseInfo {
      padding: 10px 10px;
      background-color: #ffffff;
      margin-bottom: 5px;
    }
  }
  .buttom {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    // height: 90px;
    background-color: #fff;
    box-sizing: border-box;
    .bottomTips {
      height: 40px;
      box-sizing: border-box;
      background: #e6effc;
      color: #2749bf;
      padding: 10px 5px;
      line-height: 20px;
      text-align: center;
    }
    .btnGroup {
      display: flex;
      height: 40px;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;

      .van-button {
        width: calc(50% - 8px);
        height: 40px;
      }
    }
  }
}
.backgroundStatus0 {
  background-color: #ffece8;
}
.backgroundStatus1 {
  background-color: #e8ffea;
}
.colorStatus0 {
  color: #f53f3f;
}
.colorStatus1 {
  color: #00b42a;
}
</style>
