<template>
  <div class="list">
    <div class="toptab">
      <ul>
        <li :class="{current:active=='day'}" @click="tabChange('day')"><span :class="{current:active=='day'}">今日</span></li>
        <li :class="{current:active=='week'}" @click="tabChange('week')"><span :class="{current:active=='week'}">本周</span></li>
        <li :class="{current:active=='month'}" @click="tabChange('month')"><span :class="{current:active=='month'}">本月</span></li>
        <li :class="{current:active=='year'}" @click="tabChange('year')"><span :class="{current:active=='year'}">本年</span></li>
        <li :class="{current:active=='other'}" @click="tabChange('other')"><span :class="{current:active=='other'}">自定义</span></li>
      </ul>
    </div>
    <div class="datepicker" @click="selectTime" v-if="active=='other'">
      <span class="datepickerShow" >筛选</span>
      <img src="../../assets/images/arrows.png" alt="">
      <span class="startTime" style="margin-left:0.4rem">{{startTimeText}}</span> <span style="margin:0 0.4rem">-</span> 
      <span class="endTime">{{endTimeText}}</span>
    </div>
    <div class="wrapper">
        <!-- 滚动DOM -->
      <div class="content" ref="wrapperHistory">
        <ul class="listData" >
          <li v-for="item in listData" :key="item.id">
            <div class="main">
              <div class="title">
                收集时间:
              </div>
              {{item.gatherTime}}<br>
              <div class="fixBox">
                <div class="title" style="margin-top:0.32rem">
                  收集重量:
                </div>
                {{item.gatherWeigh}}kg
              </div>
              <div class="title">
                收集袋数:
              </div>
              {{item.count}}袋<br>
              <div class="fixBox">
                <div class="title" style="margin-top:0.32rem">
                  收集人:
                </div>
                {{item.receivedPersonName}}
              </div>
              <div class="title">
                状态:
              </div>
              已签字
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import BScroll from 'better-scroll'
  export default {
    data () {
      return {
        active:'',
        historyRecord:0,
        checkbox1:true,
        dateValue:'',
        startTimeText:'选择初始时间',
        endTimeText:'选择终止时间',
        startTime:'',
        endTime:'',
        listData:[]
      }
    },
    methods:{
      tabChange(value){
        this.active = value
        value != 'other' && this.getList()
        sessionStorage.setItem('historyActive',value)
        if(value == 'other'){
          this.listData = []
          this.historyRecord = 0
        }
      },
      clickitem(){
        console.log(this.radioVal);
      },
      save(){

      },
      selectTime () {
        this.$router.push('/time')
      },
      formatTime (time) {
        let timeStr = '';
        if(time.includes('/')){
          timeStr = time.split('/')
        }else{
          timeStr = time.split('-')
        }
        return timeStr[0] + '年' + timeStr[1] + '月' + timeStr[2] + '日'
      },
      getList(from){
        let officeId = localStorage.staffInfo ? JSON.parse(localStorage.staffInfo).officeId : ""
        this.active = from == 'fromFather'? 'other' : this.active
        let data = {
          isSignature:1,
          dateType:this.active,
          officeId:officeId,
          beginGatherTime:this.active == 'other' ? this.startTime : '',
          endGatherTime:this.active == 'other' ? this.endTime : ''
        }
        this.$api.findWeightRecordListBySign(data).then(res=>{
          this.listData = res.data
          this.historyRecord = !!res.count? res.count : 0
          console.log(this.historyRecord);
          this.listData.map(item=>{
            item.gatherWeigh = item.gatherWeigh.length>5?item.gatherWeigh.substring(0,5):item.gatherWeigh
          })
        })
      },
      getHistory(){
        this.$emit('count',this.historyRecord)
      }
    },
    watch:{
      historyRecord(newVal,oldVal){
        this.getHistory()
      }
    },
    mounted () {
      this.scroll = new BScroll(this.$refs.wrapperHistory,{click:true})
      if(!!sessionStorage.selectTime && JSON.parse(sessionStorage.selectTime).count == 1){
        let time = JSON.parse(sessionStorage.selectTime).time
        let time1 = time.split('-')[0]  //2018/11/04
        let time2 = time.split('-')[1]
        this.startTimeText = this.formatTime(time1)
        this.endTimeText = this.formatTime(time2)
        this.startTime = time1.replace(/\//g,"-")
        this.endTime = time2.replace(/\//g,"-")
        if(!!sessionStorage.getItem('historyActive')){
          this.active = sessionStorage.getItem('historyActive')
        }
      } else {
        console.log(1);
        this.getList()
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
.toptab 
  border-bottom: 0.01rem solid rgb(239, 240, 244);
  ul 
    display: flex;
    justify-content: space-between;
    height: 0.88rem;
    line-height: 0.88rem;
    box-sizing: border-box;
    width: 100%;
    .current 
      color: rgba(56, 199, 196, 1);
    li 
      flex 1
      font-size: 15px;
      text-align center
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #999999;
      .current
        position relative
        display inline-block
        height calc(100%)
.datepicker 
  height 0.98rem
  line-height 0.98rem
  border-bottom 0.01rem solid rgb(239, 240, 244)
  padding 0 0.3rem
  box-sizing border-box
  color:#999;
  img
    width 0.18rem
    height 0.09rem
    margin-left 0.1rem
  .endTime,.startTime
    font-family: PingFangSC;
    font-weight: 400;
    color: #333333;
.wrapper
  height calc(100% - 54px - 1.2rem) 
  overflow: hidden;
.content
  height calc(100%)
.listData
  overflow auto
  width 100%
  background-color rgb(239, 240, 244) 
  li 
    height: 2.01rem;
    width 100%
    margin-bottom 0.01rem
    background-color #fff
    padding 0.25rem 0.32rem
    box-sizing border-box
    .box 
      height 100%
      width 0.92rem
      display flex
      float left
      justify-content center
      align-items center
    .main 
      font-family: PingFang SC;
      font-weight: 500;
      color: #888888;
      .fixBox
        width 3.69rem
        display inline-block
      .title
        font-size: 0.3rem;
        width 1.35rem;
        display inline-block
        font-family: PingFang SC;
        font-weight: 500;
        color: #353535;
</style>
