<template>
  <div id="wrapper">
     
    <!-- <div class="title">
      <span class="title-content"></span>
    </div> -->
    <div class="signature" >
      <div class="signature-title">
        <div></div>
        <div class="clear" ref="clearCanvas" @click="clear" >
          <img  class="clear-img"  src="@/assets/images/ic-resign.png" >
          <span>重新书写</span>
        </div>
      </div>
      <div class="canvas-wrapper">
      <!-- <div  v-if="guide" @click="isguide" class="guide">手签图片区</div> -->
      <img class="img" v-show="imgif" :src="imgurl" alt="">
      <!-- <img src="../../assets/images/timg.png" v-if="!imgif" class="canvas_bgp"> -->
      <canvas  id="canvas" v-show="!imgif"></canvas>
      </div>
    </div>
    <div class="btns">
      <button 
          class="sure-btn"
          ref="saveCanvas"
          @click="save"
          :disabled="imgurl?true:false"
      > 确定
      </button>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import wx from "weixin-js-sdk";
  let flag = 0
  let draw,width,height
  let preHandler = function(e){e.preventDefault()}
  width = document.documentElement.clientWidth - 40
  height = document.documentElement.clientHeight * 1 - 130
  class Draw {
    constructor(el) {
      this.el = el
      this.canvas = document.getElementById(this.el)
      this.cxt = this.canvas.getContext('2d')
      this.canvas.width = width
      this.canvas.height = height
      this.wen="lalala"
      this.stage_info = canvas.getBoundingClientRect()
      this.path = {
        beginX: 0,
        beginY: 0,
        endX: 0,
        endY:  0
      }
      

    }
    text(name){
      console.log(name)
     
      this.cxt.font="38px sans-serif"
      this.cxt.fillText(name, 100, 150);
      this.save()
      
    }
    init(btn) {
      let that = this

      this.canvas.addEventListener('touchstart', function(event) {
        document.addEventListener('touchstart', preHandler, true);
        that.drawBegin(event)
      })
      this.canvas.addEventListener('touchend', function(event) {
        document.addEventListener('touchend', preHandler, true);
        that.drawEnd()

      })
      this.clear(btn)
    }
    isDrawFlag(){
      return flag
    }
    drawBegin(e) {
      flag = 1
      let that = this
      window.getSelection() ? window.getSelection().removeAllRanges() : document.selection.empty()
      this.cxt.strokeStyle = "#000"

      this.cxt.beginPath()
      this.cxt.moveTo(
          e.changedTouches[0].clientX - this.stage_info.left,
          e.changedTouches[0].clientY - this.stage_info.top
      )
      this.path.beginX = e.changedTouches[0].clientX - this.stage_info.left
      this.path.beginY = e.changedTouches[0].clientY - this.stage_info.top
      canvas.addEventListener("touchmove",function(){
        that.drawing(event)
      })
    }
    drawing(e) {
      this.cxt.lineTo(
          e.changedTouches[0].clientX - this.stage_info.left,
          e.changedTouches[0].clientY - this.stage_info.top
      )
      this.path.endX = e.changedTouches[0].clientX - this.stage_info.left
      this.path.endY = e.changedTouches[0].clientY - this.stage_info.top
      this.cxt.stroke()
    }
    drawEnd() {
      document.removeEventListener('touchstart', preHandler, true);
      document.removeEventListener('touchend', preHandler, true);
      document.removeEventListener('touchmove', preHandler, true);
    }
    clear(btn) {
      this.cxt.clearRect(0, 0, width, height)
      flag = 0
      this.guide=true
    }
    //保存图片并添加背景色
    save(backgroundColor){
        var w = canvas.width;
        var h = canvas.height;
        var data;
        if(backgroundColor)
        {
          let context = canvas.getContext('2d')
          data = context.getImageData(0, 0, w, h);
          var compositeOperation = context.globalCompositeOperation;
          context.globalCompositeOperation = "destination-over";
          context.fillStyle = backgroundColor;
          context.fillRect(0,0,w,h);
        }
      var imageData = this.canvas.toDataURL("image/png");
        if(backgroundColor)
        {
          let context = canvas.getContext('2d')
          context.clearRect (0,0,w,h);
          context.putImageData(data, 0,0);
          context.globalCompositeOperation = compositeOperation;
        }

        //返回Base64编码数据url字符串
        return imageData;
    }
  }
  export default {
    name: "SignaturePage",
    data () {
      return {
        yfUnitCode:'', //医废项目中的上级单位编号
        yfHospitalCode:'',//医废项目中的医院编号
        yfId:'',//医废项目中的科室医废ID
        imgBase:null,
        info:{},
        list:[],
        guide:true,
        officeId:'',
        month:'',
        imgurl:"",
        imgif:false,
        officePrincipal:""
      }
    },
    mounted() {
      draw = new Draw('canvas')
      draw.init()
      //解决微信浏览器下拉露底的问题
      document.getElementById('wrapper').addEventListener('touchmove', (e) => {
        e.preventDefault()
      }, {passive: false});
    },
    methods:{
      isguide(){
        this.guide=false
      },
      /**
       * 清空画布
       */
      clear:function(){
        draw.clear();
      },
      /**
       * 保存图片
       */
      save:function(){
        let drawFlag = draw.isDrawFlag()
        if(drawFlag == 1){//在画布上绘画了
          let data = draw.save("#fff")
          this.submitImg(data)
          // console.log(data);
        }
        if(drawFlag == 0){//没在画布上绘画
          $.toast("请签字","text")
        }
        if(this.imgurl){
          $.toast("已签字","text")
          this.$router.go(-1)       
        }
      },
      /**
       * 医废提交手签信息
       */
      submitImg: function (imgBase) {
        let data = {
          imgBase:imgBase,
          id:this.$route.query.ids
        }
        this.$api.officeSignature(data).then(res=>{
          $.toast("核对成功","text")
          setTimeout(()=>{
            this.$router.go(-1)  
          },1000)
        })
      },
      imgshow(){
        if(this.imgurl){
          this.imgif=true
        }else{
          this.imgif=false
        }
      },
      submitImgSucc (data) {
        if(data.data.code == 200){
          $.toast("上传成功","text")
          wx.closeWindow()
          this.$router.go(-1)
        }else{
          $.toast(data.data.message,"text")
          wx.closeWindow()
        }
      },
      request(){
        this.$api
          .getOfficeWasteInfo({
            unitCode:this.yfUnitCode  ,
            hospitalCode:this.yfHospitalCode ,
            id:this.yfId 
          })
          .then(res => {
            this.info = res;
            this.list =res.list;
            this.imgurl=res.officeSignature;
            this.officePrincipal=res.officePrincipal;
            this.imgshow()            
          });
      }
    },
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  #wrapper
    background-color: #fff
    height: 100%
    overflow-y :auto
    .title
      height: .55rem
      display: flex
      justify-content: center
      align-items: center
      .line
        width: 2.5rem
      .title-content
        position: absolute
        padding: 0 7px
        background: $bgColor
        font-size: 14px
        color: $textColor
    .canvas-wrapper
      display: flex
      justify-content: center
      position :relative
      // margin-bottom: .98rem
      #canvas
        background-color: #fff
        // border-radius: 5px
        background url(../../assets/images/timg.png) no-repeat center center
        background-size 100% 100%
      .img
        width :90%
        text-align :center
        border:1px solid #e5e5e5
    .btns
      width: 100%
      position: fixed;
      bottom: 0;
      right: 0;
      background :#fff
      padding : 0.22rem 0
      text-align :center
      .rewrite-btn
        background-color: transparent
        color: $color
      .sure-btn
        width :90%
        height: .88rem
        border-radius: 5px
        font-size: 18px
        font-family: unset
        background-color: $color
        color: #fff
  .check 
    width :100%
    background :#fff
    margin-bottom:.2rem
    .collection
      padding-left:.32rem
      height :.98rem
      line-height: .98rem
      border-bottom:1px solid #eff0f4
    .signature-quantity
      padding-left:.32rem
      padding-top:.35rem
      display:flex
  .count
    margin-right:.5rem
  .signature-divst
    width: 2.5rem
    position :relative
    top: -.15 rem
    // left: 3rem
    padding : .22rem .13rem .2rem .3rem
    border-radius:4px;
    font-size:.26rem
    color :#5B5B74
    background #f3f3f8
    margin-bottom : .1rem

    .wastelist
      display :block
      width : 2rem 
      line-height : .4rem 
    .bor
      position :absolute
      top: .16rem
      left:  -.19rem
  .contentColor
    color:$contentColor
    font-size:.28rem
  .titleColor
    color:#353535
    font-size:.3rem
    margin-right:.3rem
  .signature
    background:#fff
    // padding-bottom:.5rem
  
    .signature-title
      display :flex
      justify-content :space-between
      line-height :.98rem
      font-size:.3rem
      padding-left: .32rem
      padding-right: .36rem
      .clear
        color: $btnColor
        .clear-img
          width :.28rem
          margin-top:-.04rem
  .guide
    width :374px
    height:336px
    position:absolute
    top:0
    left:.32rem
    text-align :center
    line-height :336px
    z-index:100
    font-size: 1rem
    color:#ebebef
  .canvas_bgp
      width: calc(100% - 40px);
      height: 100%;
      position: absolute;
      top: 0;
      left: 20px;

</style>
