<template>
  <div class="list">
    <div class="toptab">
      <ul>
        <li :class="{current:active==0}" @click="tabChange(0)"><span :class="{current:active==0}">未签字记录({{noRecord}})</span></li>
        <li :class="{current:active==1}" @click="tabChange(1)"><span :class="{current:active==1}">历史记录({{historyRecord || 0}})</span></li>
      </ul>
    </div>
    <template v-if="active == 0">
      <div class="wrapper">
        <!-- 滚动DOM -->
        <div class="content" ref="wrapper">
          <ul class="listData" >
            <li v-for="(item,index) in listData" :key="item.id" @click="changeCheckStatus(index)">
              <div class="box">
                  <van-checkbox 
                  v-model="item.check"
                  >
                </van-checkbox>
              </div>
              <div class="main" @click="changeCheckStatus(index)">
                <div class="title">
                  收集时间:
                </div>
                {{item.gatherTime}}<br>
                <div class="fixBox">
                  <div class="title" style="margin-top:0.32rem">
                    收集重量:
                  </div>
                  {{item.gatherWeigh}}kg
                </div>
                <div class="title">
                  收集袋数:
                </div>
                {{item.count}}袋<br>
                <div class="fixBox">
                  <div class="title" style="margin-top:0.32rem">
                    收集人:
                  </div>
                  {{item.receivedPersonName}}
                </div>
                <div class="title">
                  状态:
                </div>
                <span style="color:#FF0000">未签字</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="btns">
        <span class="checkAll" @click="checkAll" v-if="!isCheckAll">全选</span>
        <span class="checkAll" @click="checkAll" v-if="isCheckAll">全不选</span>
        <button 
            class="sure-btn"
            ref="saveCanvas"
            @click="goSignature"
        > 签字
        </button>
      </div>
    </template>
    <template v-if="active == 1">
      <keep-alive>
        <historyRecord @count='getHistoryCount' ref="historyRecordCom"></historyRecord>
      </keep-alive>
    </template>
  </div>
</template>

<script type=text/ecmascript-6>
import BScroll from 'better-scroll'
import historyRecord from './historyRecord'
  export default {
    components:{
      historyRecord
    },
    data () {
      return {
        active:0,
        noRecord:0,
        isCheckAll:false,
        historyRecord:0,
        checkbox1:true,
        listData:[]
      }
    },
    methods:{
      tabChange(value){
        this.active = value
        if(value == 0){
          this.$nextTick(()=>{
            this.scroll = new BScroll(this.$refs.wrapper,{click:true})
          })
        } else {
          this.$nextTick(()=>{
            this.$refs.historyRecordCom.getList()
          })
        }
      },
      getHistoryCount(value){
        console.log(value);
        this.historyRecord = value || 0
      },
      clickitem(){
        console.log(this.radioVal);
      },
      goSignature(){
        let tempArr = []
        this.listData.forEach(item=>{
          item.check?tempArr.push(item.id):null
        })
        console.log(tempArr);
        tempArr.length > 0?this.$router.push({
          path:'yfNewSignature',
          query:{
            ids:tempArr.join(',')
          }
        }):$.toast("请选择记录","text")
      },
      checkAll(){
        this.listData.map(item=>{
          item.check = !this.isCheckAll
        })  
        this.isCheckAll = !this.isCheckAll
      },
      changeCheckStatus(index){
        console.log(index);
        this.listData[index].check = !this.listData[index].check
      },
      getList(value){
        let officeId = localStorage.staffInfo ? JSON.parse(localStorage.staffInfo).officeId : ""
        this.$api.findWeightRecordListBySign({officeId:officeId,isSignature:2}).then(res=>{
          this.listData = res.data
          console.log(JSON.stringify(this.listData));
          this.listData.map(item=>{
            item.gatherWeigh = item.gatherWeigh.length>5?item.gatherWeigh.substring(0,5):item.gatherWeigh
          })
          this.noRecord = res.count
        })
        if(value){
          this.$api.findWeightRecordListBySign({officeId:officeId,isSignature:1}).then(res=>{
            this.historyRecord = res.count
          })
        }
      }
    },
    beforeRouteEnter(to, from, next) {
      if(from.path == '/time'){
        next(vm => {
        vm.active = 1
        vm.getList()
        vm.$nextTick(()=>{
          vm.$refs.historyRecordCom.getList('fromFather')
        })
      })
      } else {
        next(vm => {
        vm.getList(true)
      })
      }
    },
    created(){
      if(!localStorage.getItem('loginInfo')){
        this.$router.push('')
      } else {
      }
    },
    mounted () {
      // this.getList()
      this.scroll = new BScroll(this.$refs.wrapper,{click:true})
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
.list 
  overflow hidden
  height 100%
  width 100%
.toptab 
  border-bottom: 10px solid rgb(239, 240, 244);
  ul 
    display: flex;
    justify-content: space-between;
    height: 0.88rem;
    line-height: 0.88rem;
    box-sizing: border-box;
    padding: 0 16px;
    width: 100%;
    .current 
      color: rgba(56, 199, 196, 1);
    li 
      flex 1
      font-size: 15px;
      text-align center
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
      .current
        position relative
        display inline-block
        height calc(100%)
      .current:after 
        content: '';
        position: absolute;
        left: 10%;
        bottom: 0;
        right: auto;
        height: 3px;
        width: 80%
        background-color: rgba(56, 199, 196, 1);
.btns
  width: 100%
  position: fixed;
  bottom: 0;
  right: 0;
  background :#fff
  padding : 0.22rem 0
  text-align :center
  .checkAll
    color $color
    float left
    margin-left 0.57rem
    font-size: 0.3rem
    margin-top 0.3rem
  .rewrite-btn
    background-color: transparent
    color: $color
  .sure-btn
    float right
    margin-right 0.32rem
    width :5.08rem
    height: .88rem
    border-radius: 5px
    font-size: 18px
    font-family: unset
    background-color: $color
    color: #fff
.wrapper
  height calc(100% - 54px - 1.2rem) 
  overflow: hidden;
.content
  height calc(100%)
.listData
  overflow auto
  width 100%
  background-color rgb(239, 240, 244) 
  li 
    height: 2.01rem;
    width 100%
    margin-bottom 0.01rem
    background-color #fff
    padding 0.25rem 0
    box-sizing border-box
    .box 
      height 100%
      width 0.92rem
      display flex
      float left
      justify-content center
      align-items center
    .main 
      font-family: PingFang SC;
      font-weight: 500;
      color: #888888;
      .fixBox
        width 3.69rem
        display inline-block
      .title
        font-size: 0.3rem;
        display inline-block
        width 1.35rem
        font-family: PingFang SC;
        font-weight: 500;
        color: #353535;
</style>
