<template>
  <div class='inner'>
    <Header title="库房盘点" @backFun="goBack">
    </Header>
    <div class="inner-content">
      <van-tabs v-model="active" offset-top="10vh" sticky title-inactive-color="#86909C" title-active-color="#1D2129"
        color="#3562DB" @click="changeActive">
        <van-tab title="进行中" name="1"></van-tab>
        <van-tab title="已完成" name="2"></van-tab>
      </van-tabs>
      <div class="listWarp" v-if="listData.length > 0">
        <van-pull-refresh v-model="isLoading" class="listItem" @refresh="onRefresh">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <div v-for="(i, ind) in listData" :key="ind" class="list" @click="goTaskDetail(i)">
              <div class="title">
                <span class="task-name">{{ i.takeStockName }}</span>
                <span class="typeWarp">
                  {{ i.completion * 100||'0.00'}}%
                </span>
              </div>
              <div class="item">
                <span class="item-title">盘点单号</span>
                <span class="item-value">{{ i.takeStockCode }} </span>
              </div>
              <div class="item">
                <span class="item-title">计划期限</span>
                <span class="item-value">{{ i.planStartDate }} 至 {{ i.planEndDate }}</span>
              </div>
              <div class="item">
                <span class="item-title">责任人</span>
                <span class="item-value">{{ i.responsiblePersonName }}</span>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Toast } from "vant";
export default {
  name: 'warehouseInventory',
  data() {
    return {
      active: '1',
      listData: [],
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      loading: false,
      finished: false,
      isLoading: false,
      loginInfo: {},
      takeStockInfo: {}
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.getListData()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    changeActive(name, title) {
      this.pageParmes.pageNo = 1
      this.listData = []
      this.getListData()
    },
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = false
      this.loading = true
      this.getListData()
    },
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.listData = []
      this.getListData()
    },
    //列表
    getListData() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        current: this.pageParmes.pageNo,
        size: this.pageParmes.pageSize,
        userName: this.loginInfo.staffName,
        userId: this.loginInfo.staffId,
        responsiblePersonId: this.loginInfo.staffId,//当前登陆人id
        userDeptId: this.loginInfo.deptId,
        status: this.active
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.getTakeStockByPage(params).then(res => {
        Toast.clear()
        if (res) {
          this.listData = this.listData.concat(res.list)
          this.isLoading = false
          if (res.length == 10) {
            this.finished = false
          } else {
            this.finished = true
          }
          this.loading = false
        }
      }).catch(() => {
        Toast.clear()
      });
    },
    //详情
    goTaskDetail(row) {
      this.$router.push({
        path: "/warehouseInventoryDetail",
        query: {
          id: row.id,
        },
      });
    },
    goBack() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
}
</script>
<style  lang="scss" scoped >
.inner {
  height: 100%;
  background-color: #f2f4f9;
  .inner-content {
    height: calc(100% - 14vh);
  }
}
.notList {
  position: relative;
  height: calc(100% - 1rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
.listWarp {
  height: calc(100% - 1.28rem);
  padding: 0.1rem 0.2rem;
  overflow-y: auto;
  .list {
    padding: 0.32rem 0.32rem;
    position: relative;
    background-color: #fff;
    margin-bottom: 0.2rem;
  }
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 0.2rem;
    font-size: 0.28rem;
  }
  .task-name {
    color: #1d2129;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .typeWarp {
    display: flex;
    align-items: center;
  }
  .item {
    display: flex;
    padding: 0.14rem 0;
    .item-title {
      font-weight: 400;
      color: #4e5969;
      width: 100px;
    }
    .item-value {
      flex: 1;
      font-weight: 500;
      text-align: left;
      color: #1d2129;
    }
  }
}
::v-deep .van-tabs {
  height: calc(100% - 10vh);
  .van-tabs__line {
    background-color: #3562db;
    width: 0.48rem;
  }
  .van-tabs__content {
    height: calc(100% - 0.88rem);
  }
  .van-tab__pane {
    overflow-y: auto;
    border-top: 0.2rem solid #f2f4f9;
  }
  .van-tab--active {
    .van-tab__text {
      color: #1d2129;
      font-weight: bold !important;
    }
    .van-tab__text--ellipsis {
      overflow: visible;
    }
  }
}
</style>
