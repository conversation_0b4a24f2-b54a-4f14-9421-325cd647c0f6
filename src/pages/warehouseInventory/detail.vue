<template>
  <div class='inner'>
    <Header title="盘点详情" @backFun="goBack">
    </Header>
    <div class="inner-content">
      <div class="list" style="margin-bottom:0">
        <div class="title">
          <span class="task-name">{{ takeStockInfo.takeStockName }}</span>
          <span class="typeWarp">
            {{ takeStockInfo.completion*100|| '0.00' }}%
          </span>
        </div>
        <div class="item">
          <span class="item-title">盘点单号</span>
          <span class="item-value">{{ takeStockInfo.takeStockCode }} </span>
        </div>
        <div class="item">
          <span class="item-title">计划期限</span>
          <span class="item-value">{{ takeStockInfo.planStartDate }} 至 {{ takeStockInfo.planEndDate }}</span>
        </div>
        <div class="item ">
          <span class="item-title">责任人</span>
          <span class="item-value">{{ takeStockInfo.responsiblePersonName }}</span>
        </div>
      </div>
      <div class="tab">
        <div class="unsold" :class="{ active: activeTab === 'unsold' }" @click="changeTab('unsold')">
          <span>未盘</span><span>（{{unsoldCount||0}}）</span>
        </div>
        <div class="finished" :class="{ active: activeTab === 'finished' }" @click="changeTab('finished')">
          <span>已盘</span><span>（{{finishedCount||0}}）</span>
        </div>
      </div>
      <van-search v-model="keyWords" background="#fff" clearable :show-action="true" placeholder="搜索关键字"
        @search="searchList" @clear="searchCancel">
      </van-search>
      <div class="listWarp" v-if="listData.length > 0">
        <div v-for="(i, ind) in listData" :key="ind" class="list">
          <div class="title">
            <span class="task-name">{{ i.materialName }}({{i.materialCode}})</span>
            <span class="typeWarp">
              {{ i.warehouseName}}
            </span>
          </div>
          <div class="item">
            <span class="item-title">耗材类型</span>
            <span class="item-value">{{ i.materialTypeName }} </span>
          </div>
          <div class="item">
            <span class="item-title">规格型号</span>
            <span class="item-value">{{ i.model }} </span>
          </div>
          <div class="item item-last">
            <span class="item-title">计量单位</span>
            <span class="item-value">{{ i.basicUnitName }}</span>
            <span class="review" @click="review(i)">盘点</span>
          </div>
        </div>
      </div>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Toast } from "vant";
export default {
  name: 'warehouseInventoryDetail',
  data() {
    return {
      listData: [],
      loginInfo: {},
      takeStockInfo: {
        planStartDate: '',
        planEndDate: '',
        completion: '',
        takeStockCode: '',
        responsiblePersonName: '',
      },
      finishedCount: 0,
      unsoldCount: 0,
      activeTab: 'unsold',
      keyWords: '',
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.getInventoryDetails()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    searchList() {
      this.listData = [];
      this.getInventoryDetails();
    },
    // 取消搜索
    searchCancel() {
      this.keyWords = "";
      this.listData = [];
      this.getInventoryDetails();
    },

    changeTab(tab) {
      this.activeTab = tab;
      this.keyWords = ""
      this.listData = []
      this.getInventoryDetails()
    },
    getInventoryDetails() {
      let params = {
        id: this.$route.query.id,
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        keyWords: this.keyWords
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.getTakeStockById(params).then(res => {
        if (res) {
          Toast.clear()
          this.takeStockInfo = res
          this.finishedCount = res.checkedCount
          this.unsoldCount = res.unCheckedCount
          if (this.activeTab === 'unsold') {
            this.listData = res.unCheckedSlaveList
          } else if (this.activeTab === 'finished') {
            this.listData = res.checkedSlaveList
          } else {
            this.listData = []
          }
        } else {
          Toast.clear()
        }
      });
    },
    //盘点
    review(row) {
      this.$router.push({
        path: "/warehouseInventoryReview",
        query: {
          takeStockId: this.$route.query.id,
          takeStockSlaveId: row.id
        },
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
}
</script>
<style  lang="scss" scoped >
.inner {
  height: 100%;
  background-color: #f2f4f9;
  .inner-content {
    height: calc(100% - 3rem);
    padding: 0.2rem;
  }
}
.notList {
  position: relative;
  height: calc(100% - 3.8rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
.tab {
  display: flex;
  padding: 0.25rem 0.1rem;
  font-weight: 600 !important;
  .finished {
    margin-left: 0.6rem;
  }
  .unsold,
  .finished {
    transition: all 0.3s ease;
    &.active {
      color: #3562db; /* 高亮颜色 */
    }
  }
}
.listWarp {
  height: calc(100% - 3.8rem);
  padding: 0.2rem 0.1rem;
  overflow-y: auto;
}
.list {
  padding: 0.32rem 0.32rem;
  position: relative;
  background-color: #fff;
  margin-bottom: 0.2rem;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 0.2rem;
    font-size: 0.28rem;
  }
  .task-name {
    color: #1d2129;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .typeWarp {
    display: flex;
    align-items: center;
  }
  .item {
    display: flex;
    padding: 0.14rem 0;
    .item-title {
      font-weight: 400;
      color: #4e5969;
      width: 100px;
    }
    .item-value {
      flex: 1;
      font-weight: 500;
      text-align: left;
      color: #1d2129;
    }
  }
  .item-last {
    padding-bottom: 0;
  }
  .review {
    text-align: right;
    padding: 0.1rem 0.25rem;
    background-color: #3562db;
    color: #fff;
    border-radius: 0.05rem;
  }
}
</style>
