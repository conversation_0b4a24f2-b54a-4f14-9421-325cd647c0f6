<template>
  <div class='inner'>
    <Header title="盘点" @backFun="goBack">
    </Header>
    <div class="inner-content">
      <van-cell-group>
        <van-cell class="custom-cell" title="耗材名称" center :value="takeStockInfo.materialName||'--'" />
        <van-cell class="custom-cell" title="耗材编码" center :value="takeStockInfo.materialCode||'--'" />
        <van-cell class="custom-cell" title="耗材类型" center :value="takeStockInfo.materialTypeName||'--'" />
        <van-cell class="custom-cell" title="规格型号" center :value="takeStockInfo.model||'--'" />
        <van-cell class="custom-cell" title="计量单位" center :value="takeStockInfo.basicUnitName||'--'" />
        <van-cell class="custom-cell" title="所在仓库" center :value="takeStockInfo.warehouseName||'--'" />
        <van-cell class="custom-cell" title="库存数量" center>
          <template #default>
            <van-stepper v-model="takeStockInfo.actualNum" min="0" integer input-width="100px"
              class="stepper-custom-style" button-size="32px" />
          </template>
        </van-cell>
      </van-cell-group>
      <div class="sureBtn">
        <van-button type="info" :loading="formLoading" @click="onSubmit">确定</van-button>
      </div>
    </div>
  </div>
</template>
<script>
import { Toast } from "vant";
export default {
  name: 'warehouseInventoryReview',
  data() {
    return {
      takeStockInfo: {
        basicUnitName: '',
        materialName: '',
        materialCode: '',
        materialTypeName: '',
        model: '',
        warehouseName: '',
        actualNum: 0,
      },
      value: 100,
      formLoading: false,
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.getInventoryDetails()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    getInventoryDetails() {
      let params = {
        id: this.$route.query.takeStockSlaveId,
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.getStockSlaveById(params).then(res => {
        if (res) {
          Toast.clear()
          this.takeStockInfo = res.data
        }
      });
    },
    onSubmit() {
      let params = {
        actualNum: this.takeStockInfo.actualNum,
        takeStockId: this.takeStockInfo.takeStockId,
        takeStockSlaveId: this.$route.query.takeStockSlaveId,
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        discrepancy: this.takeStockInfo.actualNum - this.takeStockInfo.takeStockNum
      }
      this.formLoading = true
      this.$api.checkTakeStockInfo(params).then(res => {
        if (res) {
          this.goBack()
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
}
</script>
<style  lang="scss" scoped >
.inner {
  height: 100%;
  background-color: #f2f4f9;

  .inner-content {
    height: calc(100% - 14vh);
    padding: 0.1rem;
  }
  .stepper-custom-style {
    display: inline-block;
  }
}
.custom-box {
  display: flex;
  align-items: center;
  flex: 0.5;
  .custom-title {
    margin-right: 1rem;
  }
}
.sureBtn {
  display: flex;
  width: 100%;
  padding: 0.4rem 0;
  .van-button {
    width: 80%;
    margin: auto;
  }
  .van-button--info {
    color: #fff;
    background-color: #3562db;
    border: 1px solid #3562db;
  }
}

.custom-cell {
  display: flex;
  justify-content: flex-start; /* 或者其他对齐方式 */
  align-items: center; /* 垂直居中 */
}
.custom-cell .van-cell__title {
  flex: none; /* 不让它平分 */
  margin-right: 0.8rem; /* 根据需要调整间距 */
  color: #969799;
}
.custom-cell .van-cell__value {
  flex: none; /* 同上 */
  color: #000000;
}
</style>
