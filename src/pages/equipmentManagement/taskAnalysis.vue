<template>
  <div class="inner">
    <Header title="任务统计" @backFun="goBack"></Header>
    <div v-if="systemNum == '3'" class="topContent">
      <div class="headerText">
        <!-- {{ systemNum == "0" ? "设备巡检统计" : "综合巡检统计" }} -->
        综合巡检统计
      </div>
      <div class="typeBer">
        <div v-for="(item, index) in btnOption" :key="index" class="typeBtn"
          :class="btnActive == index ? 'activeBtn' : ''" @click="changeBtnType(item)">
          {{ item.name }}
        </div>
      </div>
      <div class="borderBar"></div>
      <div class="complete">
        <div class="completeTotal">
          <span>已完成（次）</span>
          <span class="count">{{ completeCharts.completeNum }}</span>
        </div>
        <div class="completeTotal">
          <span>未完成（次）</span>
          <span class="count">{{ completeCharts.notCompleteNum }}</span>
        </div>
        <div class="completeCharts">
          <el-progress type="circle" :stroke-width="8" :percentage="completeCharts.percentage" color="#3562DB"
            style="margin-top: 0.3rem"></el-progress>
          <span class="completeText">完成率</span>
        </div>
      </div>
      <div v-if="echartData.length > 0" id="executeCharts" class="executeCharts"></div>
      <div v-else class="executeCharts notCharts">
        <img height="100%" src="../../assets/images/equipmentManagement/缺省-图表@2x.png" alt="" />
        <span class="nullText">数据为空，请刷新再试</span>
      </div>
      <div class="listWrap">
        <el-table :data="tableData" height="100%">
          <el-table-column align="center" type="index" label="序号" width="50"> </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="plan_name" label="计划名称"> </el-table-column>
          <el-table-column align="center" prop="taskSum" label="任务总数" width="50"> </el-table-column>
          <el-table-column align="center" label="已巡" width="50">
            <template slot-scope="scope">
              <span style="color: #3562db" @click="toTaskList(scope.row)">{{ scope.row.finishNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="完成率">
            <template slot-scope="scope">
              <el-progress :percentage="scope.row.percentage" color="#FF7D00"></el-progress>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'taskAnalysis',
  components: {},
  data() {
    return {
      btnActive: 0,
      dateType: "day",
      btnOption: [
        {
          id: 0,
          type: "day",
          name: "今日",
        },
        {
          id: 1,
          type: "month",
          name: "本月",
        },
        {
          id: 2,
          type: "year",
          name: "本年",
        },
      ],
      show: false,
      tableData: [],
      completeCharts: {
        completeNum: "",
        notCompleteNum: "",
        percentage: 0,
      },
      echartData: [],
      systemNum: this.$route.query.systemNum || "3",
      fromPage: this.$route.query.from || "",
    };
  },
  computed: {},
  // watch: {
  //   dateType() {
  //     this.init();
  //   },
  // },
  created() {
    this.$route.meta.keepAlive = true
    this.init();
  },
  activated() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  methods: {
    init() {
      this.getComplete();
      this.getChratsData();
      this.getTableData();
    },
    changeBtnType(val) {
      if (this.btnActive != val.id) {
        this.btnActive = val.id;
        this.dateType = val.type;
        this.init();
      }
    },
    getComplete() {
      let params = {
        dateType: this.dateType,
        systemCode: this.systemNum,
        type: '0'
      };
      this.$api.taskComplete(params).then((res) => {
        res.forEach((i) => {
          if (i.name == "未巡检") {
            this.completeCharts.notCompleteNum = Number(i.value);
          } else {
            this.completeCharts.completeNum = Number(i.value);
          }
        });
        this.completeCharts.percentage = Number(((this.completeCharts.completeNum / (this.completeCharts.completeNum + this.completeCharts.notCompleteNum)) * 100).toFixed(2)) || 0;
      });
    },
    toTaskList(row) {
      if (row.finishNumber == 0) {
        $.toast("暂无任务", "text");
      } else {
        this.$router.push({
          path: "/completeTask",
          query: {
            from: 'analysis',
            planId: row.plan_id,
            planName: row.plan_name,
            systemNum: this.systemNum,
            dateType: this.dateType
          },
        });
      }
    },
    getChratsData() {
      let params = {
        dateType: this.dateType,
        systemCode: this.systemNum,
        type: '0'
      };
      this.$toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0,
      });
      this.$api.taskAnalysisCharts(params).then((res) => {
        this.$toast.clear();
        res.forEach((item) => {
          if (item.name.length > 8) {
            item.name = item.name.substr(0, 8) + "...";
          }
        });
        this.echartData = res;
        this.$nextTick(() => {
          this.initChart();
        });
      });
    },
    initChart() {
      if (this.echartData.length == 0) {
        const notCharts = document.getElementsByClassName("notCharts")[0];
        const childCharts = notCharts.getElementsByTagName("div");
        if (childCharts.length > 0) {
          childCharts[0].style.display = "none";
        }
      } else {
        const Charts = document.getElementById("executeCharts")
        if (Charts.getElementsByTagName("div")[0]) {
          Charts.getElementsByTagName("div")[0].style.display = "block";
        }
        const myChart = this.$echarts.init(document.getElementById("executeCharts"))
        myChart.setOption({
          backgroundColor: "#fff",
          color: ["rgba(246, 189, 22, 0.85)", "rgba(90, 216, 166, 0.85)", "rgba(91, 143, 249, 0.85)", "rgba(93, 112, 146, 0.85)", "#e3584e"],
          series: [
            {
              type: "pie",
              radius: "60%",
              center: ["50%", "50%"],
              data: this.echartData,
              hoverAnimation: false,
              itemStyle: {
                borderColor: "#fff",
                borderWidth: 1,
                labelLine: {
                  length: 20,
                  length2: 16,
                  show: true,
                  lineStyle: {
                    color: "#ccc",
                  },
                },
              },
              label: {
                position: "outside",
                formatter: "{b}",
                color: "#595959",
              },
            },
          ],
        });
      }
    },
    getTableData() {
      let params = {
        dateType: this.dateType,
        systemCode: this.systemNum == '0' ? this.tabCctive : this.systemNum,
        type: '0'
      };
      this.$api.taskAnalysisList(params).then((res) => {
        this.tableData = res.map((i, index) => {
          i.index = index + 1;
          return i;
        });
      });
    },
    toTaskList(row) {
      if (row.finishNumber == 0) {
        $.toast("暂无任务", "text");
      } else {
        this.$router.push({
          path: "/completeTask",
          query: {
            from: 'analysis',
            planId: row.plan_id,
            planName: row.plan_name,
            systemNum: this.systemNum == '0' ? this.tabCctive : this.systemNum,
            dateType: this.dateType
          },
        });
      }
    },
    goBack() {
      this.$route.meta.keepAlive = false
      if (this.fromPage == "inspection") {
        this.$router.go(-1);
      } else {
        // api.closeFrame();
        this.$YBS.apiCloudCloseFrame();
      }
    },
  },
};
</script>
<style scoped lang="stylus">
.topContent
  // padding: 0 0.32rem;
  background-color #fff !important
  .headerText
    padding 0 0.32rem
    height 1rem
    line-height 1rem
    color #1D2129
  .borderBar
    height 0.2rem
    background-color #fff
  .typeBer
    padding 0.1rem 0.32rem 0 0.32rem
    height 0.72rem
    display flex
    justify-content space-between
    align-items center
    .typeBtn
      width 33%
      height 100%
      display flex
      align-items center
      justify-content center
      color #4E5969
      background-color #F7F8FA
    .typeBtn:nth-child(2)
      margin 0 0.24rem
    .activeBtn
      background-color #E6EFFC
      color #3562DB
  .complete
    padding 0 0.32rem
    height 2.16rem
    display flex
    align-items center
    justify-content space-between
    background-color #fff
    .completeTotal
      height 1.04rem
      display flex
      flex-direction column
      justify-content space-between
      .count
        color #1D2129
        font-size 0.36rem
        font-weight bold
    .completeTotal:nth-child(2)
      margin 0 0.32rem
    .completeCharts
      position relative
      width calc(100% - 4rem)
      height 100%
      display flex
      justify-content center
      /deep/ .el-progress
        height 1.5rem
        width 1.5rem
        .el-progress-circle
          width 100% !important
          height 100% !important
      .completeText
        position absolute
        bottom 0rem
  .executeCharts
    padding 0 0.32rem
    height 28vh
    text-align center
    position relative
    .nullText
      position absolute
      bottom 1rem
      left calc(50% - 1.4rem)
  .listWrap
    height calc(62vh - 4.08rem)
    overflow-y auto
/deep/ .el-progress__text
  color #3562DB !important
/deep/ .el-table__header
  th
    background-color #E6EFFC !important
    color #1D2129 !important
/deep/ .el-progress-bar
  width 90%
</style>
