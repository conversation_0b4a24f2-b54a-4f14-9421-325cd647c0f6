<template>
  <div class="inner">
    <Header title="设备管理" @backFun="goBack"></Header>
    <!-- 导航栏 -->
    <van-search
      v-model="assetName"
      placeholder="请输入关键字"
      input-align="center"
      @input="onSearch"
    >
    </van-search>
    <div v-if="!noDate" class="contentList">
      <van-pull-refresh
        class="content"
        v-model="refreshing"
        @refresh="onRefresh"
      >
        <van-list
          ref="listRef"
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          :immediate-check="false"
          @load="onLoad"
        >
          <div v-for="(item, index) in listData" :key="index" class="listWrap">
            <div>
              <img
                class="row-img"
                width="88"
                height="88"
                :src="lostImg"
                @click="toView(lostImg)"
              />
            </div>
            <div @click="goDetail(item)">
              <div class="row-title">{{ item.assetName }}</div>
              <ul>
                <li>资产编码：{{ item.assetCode }}</li>
                <li>专业类别：{{ item.professionalCategoryName }}</li>
                <li>所在区域：{{ item.regionName }}</li>
              </ul>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-if="noDate" style="height:70vh">
      <van-empty :image="noDataImg" description="暂无数据" />
    </div>
  </div>
</template>
<script>
import { ImagePreview } from "vant";
import lostImg from "@/assets/images/equipmentManagement/lost_img.png";
import axios from 'axios'
export default {
  name: "deviceList",
  data() {
    return {
      loginInfo: "",
      assetName: "",
      lostImg,
      tabName: [],
      listData: [],
      active: "0",
      noDataImg: require("@/assets/images/equipmentManagement/缺省@2x.png"), //暂无数据图片
      competentValue: "",
      paginationData: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      noDate: false, //页面无数据标识
      refreshing: false,
      loading: true,
      finished: false
    };
  },
  computed: {},
  watch: {},
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.getDeviceList()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    getCompetentList() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode
      };
      this.$api.competentList(params).then(res => {
        this.tabName = res;
        this.competentValue = this.tabName[0].dictValue;
        this.onLoad();
      });
    },
    getDeviceList() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        centralizedDepartmentCode: '',
        assetName: this.assetName,
        hideAssetIdentification: 1 // 过滤掉已经报修的设备
      };
      this.$api.deviceList(params).then(res => {
        if (res.assetDetailsList.length == 0 && this.listData.length == 0) {
          this.listData = [];
          this.finished = true;
          this.noDate = true;
        } else {
          this.finished = false;
          this.noDate = false;
        }
        this.paginationData.total = res.sum;
        if (this.paginationData.total != "") {
          if (
            this.paginationData.currentPage >
            Math.ceil(this.paginationData.total / this.paginationData.pageSize)
          ) {
            this.finished = true;
          }
        }
        this.loading = false;
        this.listData = [...this.listData, ...res.assetDetailsList];
      });
    },
    onRefresh() {
      this.paginationData.currentPage = 1;
      // 清空列表数据
      this.finished = false;
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.onLoad();
    },
    onLoad() {
      setTimeout(() => {
        if (this.refreshing) {
          this.listData = [];
          this.refreshing = false;
        }
        this.getDeviceList();
        this.paginationData.currentPage++;
      }, 1000);
    },
    tabClick(name) {
      this.noDate=false
      this.finished = false;
      this.loading = true;
      this.listData = [];
      this.assetName=''
      this.competentValue = name;
      this.paginationData.currentPage = 1;
      this.onLoad();
    },
    onSearch() {
      this.noDate=false
      this.finished = false;
      this.loading = true;
      this.listData = [];
      this.paginationData.currentPage = 1;
      this.onLoad();
    },
    goDetail(item) {
      this.loading = true
      const params = new FormData()
      params.append('unitCode', this.loginInfo.unitCode)
      params.append('hospitalCode', this.loginInfo.hospitalCode)
      params.append('deviceId', item.assetsId)
      // 校验此设备是否在修
      axios.post(
        __PATH.ONESTOP + "/deviceRepair/getDeviceWorkOrderStatus",
        params,
        {
          headers: {
            'Authorization': localStorage.getItem("token")
          }
        }).then(res => {
          this.loading = false
          if (res.data.code == '200') {
            sessionStorage.setItem('equipmentDetail', JSON.stringify(item))
            this.$router.go(-1)
          } else {
            $.toast(res.data.message,'text')
          }
        })
    },
    toView(src) {
      ImagePreview({
        images: [src],
        showIndex: false, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    }
  }
};
</script>
<style scoped lang="stylus">
.inner {
  height: 100%;
  overflow: hidden;
}
.contentList {
  background-color: #fff !important;
  height: calc(90% - 54px);
  overflow: auto;
  .content {
    padding: 0 16px;
    height: auto !important;
    background-color: #fff !important;
    .listWrap {
      background-color: #fff;
      height: 140px;
      display: flex;
      align-items: center;
      .row-title {
        font-size: 16px;
        margin-left: 16PX;
        width: 200px;
        overflow: hidden;  //超出隐藏
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号
      }
      ul {
        margin: 10px 0 0 16px;
        li {
          font-size: 15px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #47515f;
          margin-bottom: 5px;
          word-wrap: break-word;
          width: 230px;
          overflow: hidden;  //超出隐藏
          white-space: nowrap; //不折行
          text-overflow: ellipsis; //溢出显示省略号
        }
      }
    }
  }
}
::v-deep .van-search {
  background: #efeff4;
  box-shadow: 0px 1px 0px 0px #dddddd;
}
::v-deep .van-search__action {
  font-size: 0.85rem;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #2d4a74;
}
>>> .van-tab__text{
  font-size: 16px;
}
</style>
