<template>
  <div class='inner'>
    <Header title="任务列表" @backFun="goBack"></Header>
    <div class="pointTitle planName">
      <span class="planTitle">计划名称</span>
      <span class="point planText">{{ $route.query.planName }}</span>
    </div>
    <div v-if="listData.length > 0" class="lsitWrap">
      <van-pull-refresh v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div v-for="(i, ind) in listData" :key="ind" class="list" @click="goTaskDetail(i)">
            <div class="title">
              <span class="task-name">{{ i.taskName }}</span>
              <span class="typeWarp">
                <span class="taskType">{{ typeOptions.find(j => i.cycleType == j.cycleType).label }}</span>
                <van-icon name="arrow" />
              </span>
            </div>
            <div class="point">
              <div class="pointItem">
                <span class="pointTitle">{{ i.systemCode == '2' ? '应保养点数' : '应巡点数' }}</span>
                <span class="count">{{ i.totalCount }}</span>
              </div>
              <div class="pointItem">
                <span class="pointTitle">{{ i.systemCode == '2' ? '已保养点数' : '已巡点数' }}</span>
                <span class="count">{{ i.hasCount }}</span>
              </div>
            </div>
            <div class="time">
              <span class="timeTitle">{{ i.systemCode == '2' ? '应保养日期' : '应巡日期' }}</span>
              <span class="timeDate">{{ i.cycleType == 6 ? i.taskStartTime : moment(i.taskStartTime).format('YYYY-MM-DD') }}</span>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>
<script>
import { Toast } from "vant";
import moment from 'moment'
export default {
  components: {},
  data() {
    return {
      moment,
      loading: false,
      finished: false,
      isLoading: false,
      listData: [],
      loginInfo: {},
      // 周期类型
      typeOptions: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      systemNum:this.$route.query.systemNum||'' // 判断是巡检（1）还是保养（2）或者是综合巡检（3）
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.getListData()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  methods: {
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = false
      this.loading = true
      this.getListData()
    },
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.listData = []
      this.getListData()
    },
    getListData() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        // distributionTeamId: this.loginInfo.deptId,
        // planPersonCode: this.loginInfo.staffId,
        accomplishType: 1, // 0 待巡检，1 已巡检，2已超期
        systemCode: this.systemNum, // 1：设备 2： 保养 3:综合
        pageNo: this.pageParmes.pageNo,
        pageSize: this.pageParmes.pageSize,
        planId: this.$route.query.planId,
        dateType: this.$route.query.dateType,
        type: '0'
      }
      if (this.$route.query.from == 'analysis') {
        params.useState = '1'
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.taskListByAnalysis(params).then(res => {
        Toast.clear()
        this.listData = this.listData.concat(res)
        this.isLoading = false
        if (res.length == 10) {
          console.log('加载')
          this.finished = false
        } else {
          console.log('无数据')
          this.finished = true
        }
        this.loading = false
      })
    },
    goTaskDetail(row) {
      this.$router.push({
        path: '/taskDetail',
        query: {
          id: row.id,
          menuType: '1' // 0：巡检执行 1：巡检查看
        }
      })
    },
    goBack() {
      this.$router.go(-1);
    }
  }
}
</script>
<style scoped lang="stylus">
.inner {
  height: 100%;
  .planName {
    display: flex;
    height: auto !important;
    line-height: normal !important;
    padding-top: 0.32rem;
    .planTitle {
      display: flex;
      align-items: baseline !important;
      width: 68px;
      min-height: 0.72rem;
      margin-right: 0.4rem;
    }
    .planText {
      width: calc(100% - 68px - 0.4rem);
      margin-left: 0 !important;
      display: flex !important;
      align-items: baseline !important;
    }
  }
  .pointTitle {
    font-size: 0.32rem;
    color: #4E5969;
    padding: 0 0.32rem;
    height: 1.04rem;
    line-height: 1.04rem;
    background-color: #fff;
    border-bottom: 0.2rem solid #F2F4F9;
    .point {
      display: inline-block;
      margin-left: 0.48rem;
      color: #1D2129;
    }
  }
  .lsitWrap {
    height: calc(90vh - 1.24rem);
    overflow: auto;
  }
  .list {
    padding: 0.4rem 0.32rem;
    position: relative;
    font-size: 0.32rem;
    background-color: #fff;
    border-bottom: 1px solid #E5E6EB;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .task-name {
        color: #1D2129;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .typeWarp {
        display: flex;
        align-items: center
        .taskType {
          display: inline-block;
          background-color:#E6EFFC;
          padding: 0.06rem 0.08rem;
          border-radius: 0.04rem;
          font-size: 0.24rem;
          color: #3562DB;
          text-align: center;
          font-weight: normal;
        }
      }
    }
    .point {
      margin: 0.32rem 0;
      display: flex;
      .pointItem {
        width: 50%;
        display: flex;
        align-items: center;
        .pointTitle {
          color: #4E5969;
          margin-right: 0.48rem;
          padding: 0;
          border: 0;
        }
        .count {
          color: #1D2129;
        }
      }
    }
    .time {
      .timeTitle {
        color: #4E5969;
        margin-right: 0.48rem;
      }
      .timeDate {
        color: #1D2129;
      }
    }
  }
  .notList {
    position: relative;
    height: calc(90% - 1.24rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url('../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
</style>
