<template>
  <div class="inner">
    <Header title="任务详情" @backFun="goBack"></Header>
    <div class="task-wrap">
      <div class="task-box task-name">
        <span style="min-width: 68px; min-height: 32px; line-height: 32px">任务名称</span>
        <span class="taskNameText">{{ taskData.taskName }}</span>
      </div>
      <div class="task-box task-time">
        <span>周期类型</span>
        <span>{{ cycleTypeFormat(taskData.cycleType) }}</span>
      </div>
      <div class="task-box task-time">
        <span>{{ taskData.systemCode == "2" ? "应保养日期" : "应巡日期" }}</span>
        <span class="timeDate">{{ taskData.cycleType == 8 ? moment(taskData.taskStartTime).format("YYYY-MM-DD") : taskData.taskStartTime }}</span>
      </div>
    </div>
    <div class="progressWrap">
      <div v-for="(item, index) in pointList" :key="index" class="pointInfo" @click="toContent(item)">
        <div class="pointName">
          <span class="stateColor" :class="item.carryOutFlag == 1 ? 'finish' : 'notFinish'"></span>
          <span style="color: #4E5969; margin-right: 0.2rem;">点位名称：</span>{{ item.taskPointName }}
        </div>
        <div class="pointName" style="line-height: 1.2;" v-if="JSON.parse(item.particulars).regionName || JSON.parse(item.particulars).simName">
          <span style="color: #4E5969; margin-right: 0.2rem;">位置信息：</span>{{ JSON.parse(item.particulars).regionName || JSON.parse(item.particulars).simName }}
        </div>
        <div class="pointName" v-if="JSON.parse(item.particulars).assetsRemarks">
          <div class="remarkDesc" :class="{ 'van-ellipsis': !item.showMoreFlag }">
            {{ JSON.parse(item.particulars).assetsRemarks }}
          </div>
          <div class="collaspe" style="color: #67a9fd" @click.stop="showMore(item)" v-if="!item.showMoreFlag">展开</div>
          <div class="collaspe" style="color: #67a9fd" @click.stop="showMore(item)" v-if="item.showMoreFlag">收起</div>
        </div>
        <div v-if="item.carryOutFlag == 1" class="pointResult">
          <div class="stepLine"></div>
          <div class="resultState">
            {{ taskData.systemCode == "2" ? "保养结果：" : "巡检结果：" }}
            <span class="stateBar" :class="item.state == 2 ? 'qualified' : 'disqualified'">
              {{ item.state == 2 ? "合格" : item.state == "4" ? "异常报修" : "不合格" }}
            </span>
          </div>
          <div>{{ item.executeTime }}</div>
        </div>
        <div v-else class="notInspection">
          <div class="notLine"></div>
        </div>
      
      </div>
       <div>
      <van-popup v-model="showPopupFlag" position="bottom" closeable  @close="onClosePopup">
      <div class="popup-content">
         <div class="popup-title">超时情况说明</div>
        <van-field type="textarea" show-word-limit maxlength='200' v-model="timeoutDeclaration" placeholder="请输入超时情况说明" />
        <van-button style="width:100%;border-radius: 8px" type="info" @click="handleConfirm()">确定</van-button>
      </div>
    </van-popup>
  </div>
    </div>
    
  </div>
</template>
<script>
import moment from "moment";
import YBS from "@/assets/utils/utils.js";
import { Toast } from "vant";
import { Dialog } from "vant";
import axios from "axios";
import qs from "qs";
import { mapState } from "vuex";
export default {
  components: {
    [Dialog.Component.name]: Dialog.Component
  },
  computed: {
    ...mapState(["h5Mode"])
  },
  data() {
    return {
      moment,
       showPopupFlag: false,
      timeoutDeclaration: '',
      taskId: "",
      loginInfo: {},
      taskData: {},
      // 周期类型
      typeOptions: [
        {
          cycleType: 8,
          label: "单次"
        },
        {
          cycleType: 6,
          label: "每日"
        },
        {
          cycleType: 0,
          label: "每周"
        },
        {
          cycleType: 2,
          label: "每月"
        },
        {
          cycleType: 3,
          label: "季度"
        },
        {
          cycleType: 5,
          label: "全年"
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      pointList: [],
      ibeacon: "",
      ibeaconArr: [], // 定位点列表
      menuType: this.$route.query.menuType,
      systemNum: this.$route.query.systemNum || "1", // 判断是巡检（1）还是保养（2）或者是综合巡检（3）
      timer: "",
      toast: null,
      clickRow:{}
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || [];
    this.getDetail();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    onClosePopup(){
    },
     handleConfirm(val) {
      this.taskData.timeoutDeclaration = this.timeoutDeclaration
      this.toContent(this.clickRow)
      this.showPopupFlag = false;
    },
    showMore(item) {
      this.$set(item, "showMoreFlag", !item.showMoreFlag);
    },
    getDetail() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        taskId: this.$route.query.id
      };
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.taskDetail(params).then(res => {
        this.pointList = res.pointList;
        this.taskData = res.taskMap;
        Toast.clear();
      });
    },
    cycleTypeFormat(type) {
      let name = "";
      this.typeOptions.find(i => {
        if (i.cycleType == type) {
          name = i.label;
        }
      });
      return name;
    },
    async toContent(row) {
      this.clickRow = row
      // 判断是否从点详情跳转至任务列表再到任务详情
      if (sessionStorage.getItem("isPointDetail")) {
        if (!this.$route.query.isClould) {
          return;
        } else {
          this.$router.push({
            path: "/completeDetail",
            query: {
              id: row.id,
              systemNum: this.taskData.systemCode
            }
          });
        }
      } 
      else {
        this.ibeaconArr = [];
        sessionStorage.removeItem("whetherLocation");
        // 判断是否为设备巡检点
        const isDevice = JSON.parse(row.particulars).assetsId ? JSON.parse(row.particulars).assetsId : false;
        // 是否拍照
        const isPicture = JSON.parse(row.particulars).isPicture;
        if (this.menuType) {
            const item = {
              taskPointRelease: {
                id: row.id,
                particulars: row.particulars
              },
              id: row.taskId,
              taskPointId: row.taskPointId,
              particulars: row.particulars
            };
          if (this.menuType == "0") {
            // 已执行任务
            if (row.carryOutFlag != "0") {
              return $.toast("已完成，无需操作", "text");
            }
            let dialogConfirm = "";
            // 非顺序执行任务
            if (row.executeOrder != "start" && this.taskData.sortFlag == "0") {
              dialogConfirm = await Dialog.confirm({
                title: "提示",
                message: "该任务需要按顺序执行，请返回上一任务点执行工作。如果继续执行请点击继续执行。",
                confirmButtonColor: "#3562db",
                confirmButtonText: "继续执行",
                cancelButtonText: "忽略"
              })
                .then(() => {
                  return "confirm";
                })
                .catch(() => {
                  return "cancel";
                });
            }
            if (dialogConfirm == "cancel") return;
          
            this.taskExcute().then(res => {
              const isScan = this.taskData.scanFlag == "0" ? true : false;
              const { code, data, message } = res.data;
              if (code == "200") {
                // 是否定位
                if (this.taskData.locationFlag == "0") {
                  if (row.locationPointReleaseList && row.locationPointReleaseList.length) {
                    if (row.locationPointReleaseList[0].locationPointType == "1") {
                      this.getLocation(item, row.maintainProjectRelease, isScan, row.locationPointReleaseList[0].deviceMinor, isDevice, isPicture);
                    } else {
                      sessionStorage.setItem("whetherLocation", "3");
                      if (this.taskData.scanFlag == "0") {
                        if(this.h5Mode == "apicloud") {
                            this.APPScan(row.id, row.taskPointId, isDevice);
                        }else {
                            this.wxScan(row.id, row.taskPointId, isDevice)
                        }
                      } else {
                        if (row.maintainProjectRelease) {
                          // 有任务书
                          let type = null;
                          if (row.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                            type = 0;
                          } else if (row.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                            type = 1;
                          }
                          this.$router.push({
                            path: "/taskContent",
                            query: {
                              id: row.id,
                              type,
                              systemNum: this.taskData.systemCode,
                              taskStartTime: this.taskData.taskStartTime
                            }
                          });
                        } else {
                          // 无任务书
                          if (isPicture == "1") {
                            this.noBookSubmit(item, isDevice);
                          } else {
                            this.$router.push({
                              path: "/taskResult",
                              query: {
                                taskId: this.taskData.id,
                                taskPointReleaseId: row.id,
                                isPicture: "0", // 0:拍照，1:不拍照
                                type: "2",
                                answerMapList: ""
                              }
                            });
                          }
                        }
                      }
                    }
                  } else {
                    Toast({
                      type: "text",
                      message: "未配置定位点",
                      duration: 1500,
                      onClose: () => {
                        sessionStorage.setItem("whetherLocation", "3");
                        if (this.taskData.scanFlag == "0") {
                           if(this.h5Mode == "apicloud") {
                            this.APPScan(row.id, row.taskPointId, isDevice);
                            }else {
                                this.wxScan(row.id, row.taskPointId, isDevice)
                            }
                        } else {
                          if (row.maintainProjectRelease) {
                            // 有任务书
                            let type = null;
                            if (row.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                              type = 0;
                            } else if (row.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                              type = 1;
                            }
                            this.$router.push({
                              path: "/taskContent",
                              query: {
                                id: row.id,
                                type,
                                systemNum: this.taskData.systemCode,
                                taskStartTime: this.taskData.taskStartTime
                              }
                            });
                          } else {
                            // 无任务书
                            if (isPicture == "1") {
                              this.noBookSubmit(item, isDevice);
                            } else {
                              this.$router.push({
                                path: "/taskResult",
                                query: {
                                  taskId: this.taskData.id,
                                  taskPointReleaseId: row.id,
                                  isPicture: "0", // 0:拍照，1:不拍照
                                  type: "2",
                                  answerMapList: ""
                                }
                              });
                            }
                          }
                        }
                      }
                    });
                  }
                } else {
                  sessionStorage.setItem("whetherLocation", "1");
                  // 是否扫码
                  if (isScan) {
                     if(this.h5Mode == "apicloud") {
                            this.APPScan(row.id, row.taskPointId, isDevice);
                        }else {
                            this.wxScan(row.id, row.taskPointId, isDevice)
                        }
                  } else {
                    if (row.maintainProjectRelease) {
                      // 有任务书
                      let type = null;
                      if (row.maintainProjectRelease && row.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                        type = 0;
                      } else if (row.maintainProjectRelease && row.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                        type = 1;
                      } else {
                        type = 2; // 无任务书
                      }
                      this.$router.push({
                        path: "/taskContent",
                        query: {
                          id: item.taskPointRelease.id,
                          type,
                          systemNum: this.taskData.systemCode,
                          taskStartTime: this.taskData.taskStartTime
                        }
                      });
                    } else {
                      // 无任务书
                      if (isPicture == "1") {
                        this.noBookSubmit(item, isDevice);
                      } else {
                        this.$router.push({
                          path: "/taskResult",
                          query: {
                            taskId: this.taskData.id,
                            taskPointReleaseId: item.taskPointRelease.id,
                            isPicture: isPicture, // 0:拍照，1:不拍照
                            type: "2",
                            answerMapList: ""
                          }
                        });
                      }
                    }
                  }
                }
              } else {
                $.toast(message, "text");
              }
            });
          } else {
           
            
            // 已执行任务
            if (row.carryOutFlag != "0") {
              return $.toast("已完成，无需操作", "text");
            }
             if(!this.taskData.timeoutDeclaration || this.taskData.timeoutDeclaration=='') {
              this.showPopupFlag = true
              
              } else {
                if (row.maintainProjectRelease) {
              // 有任务书
              let type = null;
              if (row.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                type = 0;
              } else if (row.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                type = 1;
              }
                this.$router.push({
                path: "/taskContent",
                query: {
                  id: row.id,
                  type,
                  systemNum: this.taskData.systemCode,
                  taskStartTime: this.taskData.taskStartTime,
                  timeoutDeclaration:this.taskData.timeoutDeclaration
                }
              });
              
             
            } else {
              // 无任务书
              if (isPicture == "1") {
                
                this.noBookSubmit(item, isDevice);
              } else {
                this.$router.push({
                  path: "/taskResult",
                  query: {
                    taskId: this.taskData.id,
                    taskPointReleaseId: row.id,
                    isPicture: "0", // 0:拍照，1:不拍照
                    type: "2",
                    answerMapList: ""
                  }
                });
              }
            }
              }
           
          }
        } else {
          // 当前执行时间范围内
          if (
            ((moment(this.taskData.taskStartTime) < moment() || moment(this.taskData.taskStartTime) == moment()) && moment(this.taskData.taskEndTime) > moment()) ||
            moment(this.taskData.taskEndTime) == moment()
          ) {
            if (this.taskData.taskStatus == "1") {
              // taskStatus: 1未完成 2已完成
              // 已执行任务
              if (row.carryOutFlag != "0") {
                return $.toast("已完成，无需操作", "text");
              }
              let dialogConfirm = "";
              // 非顺序执行任务
              if (row.executeOrder != "start" && this.taskData.sortFlag == "0") {
                dialogConfirm = await Dialog.confirm({
                  title: "提示",
                  message: "该任务需要按顺序执行，请返回上一任务点执行工作。如果继续执行请点击继续执行。",
                  confirmButtonColor: "#3562db",
                  confirmButtonText: "继续执行",
                  cancelButtonText: "忽略"
                })
                  .then(() => {
                    return "confirm";
                  })
                  .catch(() => {
                    return "cancel";
                  });
              }
              if (dialogConfirm == "cancel") return;
              const item = {
                taskPointRelease: {
                  id: row.id
                },
                id: row.taskId
              };
              this.taskExcute().then(res => {
                const { code, data, message } = res.data;
                if (code == "200") {
                  const isScan = this.taskData.scanFlag == "0" ? true : false;
                  // 是否定位
                  if (this.taskData.locationFlag == "0") {
                    if (row.locationPointReleaseList && row.locationPointReleaseList.length) {
                      if (row.locationPointReleaseList[0].locationPointType == "1") {
                        this.getLocation(item, row.maintainProjectRelease, isScan, row.locationPointReleaseList[0].deviceMinor, isDevice, isPicture);
                      } else {
                        sessionStorage.setItem("whetherLocation", "3");
                        if (this.taskData.scanFlag == "0") {
                           if(this.h5Mode == "apicloud") {
                            this.APPScan(row.id, row.taskPointId, isDevice);
                        }else {
                            this.wxScan(row.id, row.taskPointId, isDevice)
                        }
                        } else {
                          if (row.maintainProjectRelease) {
                            // 有任务书
                            let type = null;
                            if (row.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                              type = 0;
                            } else if (row.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                              type = 1;
                            }
                            this.$router.push({
                              path: "/taskContent",
                              query: {
                                id: row.id,
                                type,
                                systemNum: this.taskData.systemCode,
                                taskStartTime: this.taskData.taskStartTime
                              }
                            });
                          } else {
                            // 无任务书
                            if (isPicture == "1") {
                              this.noBookSubmit(item, isDevice);
                            } else {
                              this.$router.push({
                                path: "/taskResult",
                                query: {
                                  taskId: this.taskData.id,
                                  taskPointReleaseId: row.id,
                                  isPicture: "0", // 0:拍照，1:不拍照
                                  type: "2",
                                  answerMapList: ""
                                }
                              });
                            }
                          }
                        }
                      }
                    } else {
                      Toast({
                        type: "text",
                        message: "未配置定位点",
                        duration: 1500,
                        onClose: () => {
                          sessionStorage.setItem("whetherLocation", "3");
                          if (this.taskData.scanFlag == "0") {
                             if(this.h5Mode == "apicloud") {
                            this.APPScan(row.id, row.taskPointId, isDevice);
                        }else {
                            this.wxScan(row.id, row.taskPointId, isDevice)
                        }
                          } else {
                            if (row.maintainProjectRelease) {
                              // 有任务书
                              let type = null;
                              if (row.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                                type = 0;
                              } else if (row.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                                type = 1;
                              }
                              this.$router.push({
                                path: "/taskContent",
                                query: {
                                  id: row.id,
                                  type,
                                  systemNum: this.taskData.systemCode,
                                  taskStartTime: this.taskData.taskStartTime
                                }
                              });
                            } else {
                              // 无任务书
                              if (isPicture == "1") {
                                this.noBookSubmit(item, isDevice);
                              } else {
                                this.$router.push({
                                  path: "/taskResult",
                                  query: {
                                    taskId: this.taskData.id,
                                    taskPointReleaseId: row.id,
                                    isPicture: "0", // 0:拍照，1:不拍照
                                    type: "2",
                                    answerMapList: ""
                                  }
                                });
                              }
                            }
                          }
                        }
                      });
                    }
                  } else {
                    sessionStorage.setItem("whetherLocation", "1");
                    // 是否扫码
                    if (isScan) {
                       if(this.h5Mode == "apicloud") {
                            this.APPScan(row.id, row.taskPointId, isDevice);
                        }else {
                            this.wxScan(row.id, row.taskPointId, isDevice)
                        }
                    } else {
                    
                      if (row.maintainProjectRelease) {
                        // 有任务书
                        let type = null;
                        if (row.maintainProjectRelease && row.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                          type = 0;
                        } else if (row.maintainProjectRelease && row.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                          type = 1;
                        } else {
                          type = 2; // 无任务书
                        }
                        this.$router.push({
                          path: "/taskContent",
                          query: {
                            id: item.taskPointRelease.id,
                            type,
                            systemNum: this.taskData.systemCode,
                            taskStartTime: this.taskData.taskStartTime,
                           
                          }
                        });
                      } else {
                        // 无任务书
                        if (isPicture == "1") {
                          this.noBookSubmit(item, isDevice);
                        } else {
                          this.$router.push({
                            path: "/taskResult",
                            query: {
                              taskId: this.taskData.id,
                              taskPointReleaseId: item.taskPointRelease.id,
                              isPicture: isPicture, // 0:拍照，1:不拍照
                              type: "2",
                              answerMapList: ""
                            }
                          });
                        }
                      }
                    }
                  }
                } else {
                  $.toast(message, "text");
                }
              });
            } else {
              this.$router.push({
                path: "/completeDetail",
                query: {
                  id: row.id,
                  systemNum: this.taskData.systemCode
                }
              });
            }
          } else {
            if (row.carryOutFlag == "1") {
              // this.$router.push({
              //   path: "/completeDetail",
              //   query: {
              //     id: row.id,
              //     systemNum: this.taskData.systemCode
              //   }
              // });
               return $.toast("已完成，无需操作", "text");
            } else {
              if (moment(this.taskData.taskStartTime) > moment()) {
                $.toast("任务未开始", "text");
              } else {
                // $.toast("任务已超时", "text");
                 // 已执行任务
            // if (row.carryOutFlag != "0") {
            //   return $.toast("已完成，无需操作", "text");
            // }
             if(!this.taskData.timeoutDeclaration || this.taskData.timeoutDeclaration=='') {
              this.showPopupFlag = true
              
              } else {
                if (row.maintainProjectRelease) {
              // 有任务书
              let type = null;
              if (row.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                type = 0;
              } else if (row.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                type = 1;
              }
                this.$router.push({
                path: "/taskContent",
                query: {
                  id: row.id,
                  type,
                  systemNum: this.taskData.systemCode,
                  taskStartTime: this.taskData.taskStartTime,
                  timeoutDeclaration:this.taskData.timeoutDeclaration
                }
              });
              
             
            } else {
              // 无任务书
              if (isPicture == "1") {
                const item = {
              taskPointRelease: {
                id: row.id,
                particulars: row.particulars
              },
              id: row.taskId,
              taskPointId: row.taskPointId,
              particulars: row.particulars
            };
                this.noBookSubmit(item, isDevice);
              } else {
                this.$router.push({
                  path: "/taskResult",
                  query: {
                    taskId: this.taskData.id,
                    taskPointReleaseId: row.id,
                    isPicture: "0", // 0:拍照，1:不拍照
                    type: "2",
                    answerMapList: ""
                  }
                });
              }
            }
              }
              }
            }
          }
        }
      }
    },
    // 校验任务是否可执行
    async taskExcute() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        taskId: this.taskData.id,
        typeValue: "1"
      };
      const isExcute = await axios({
        method: "post",
        url: __PATH.IPSM_URL + "/planTaskNewApiController/executePlanTaskByTaskId",
        data: qs.stringify(params),
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: localStorage.getItem("token")
        }
      });
      return isExcute;
    },
    // 判断手机 - ios/andriod
    isIOS() {
      const u = navigator.userAgent;
      return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    },
    // 定位
    getLocation(taskData, maintainProjectRelease, scanFlag, deviceMajor, isDevice, isPicture) {
      if (this.h5Mode == "apicloud") {
        if (this.isIOS()) {
          this.toast = Toast({
            type: "loading",
            message: "获取定位中...",
            duration: 5000,
            onOpened: () => {
              this.scanBeacon(deviceMajor, scanFlag, maintainProjectRelease, taskData, isDevice, isPicture);
            },
            onClose: async () => {
              this.ibeacon.stopRanging();
              console.log("符合的设备列表", this.ibeaconArr, "目标设备", deviceMajor);
              if (!this.ibeaconArr.some(i => i == deviceMajor) || this.ibeaconArr.length == 0) {
                const reLocation = await Dialog.confirm({
                  title: "提示",
                  message: "定位失败，是否重新定位",
                  confirmButtonColor: "#3562db",
                  confirmButtonText: "重新定位",
                  cancelButtonText: "放弃定位"
                })
                  .then(() => {
                    return "confirm";
                  })
                  .catch(() => {
                    return "cancel";
                  });
                if (reLocation == "cancel") {
                  return false;
                } else {
                  this.ibeacon = null;
                  this.getLocation(taskData, maintainProjectRelease, scanFlag, deviceMajor, isDevice, isPicture);
                }
              }
            }
          });
        } else {
          var ble = api.require("ble");
          ble.openBluetooth(ret => {
            if (ret) {
              this.toast = Toast({
                type: "loading",
                message: "获取定位中...",
                duration: 5000,
                onOpened: () => {
                  this.scanBeacon(deviceMajor, scanFlag, maintainProjectRelease, taskData, isDevice, isPicture);
                },
                onClose: async () => {
                  this.ibeacon.stopRanging();
                  console.log("符合的设备列表", this.ibeaconArr, "目标设备", deviceMajor);
                  if (!this.ibeaconArr.some(i => i == deviceMajor) || this.ibeaconArr.length == 0) {
                    const reLocation = await Dialog.confirm({
                      title: "提示",
                      message: "定位失败，是否重新定位",
                      confirmButtonColor: "#3562db",
                      confirmButtonText: "重新定位",
                      cancelButtonText: "放弃定位"
                    })
                      .then(() => {
                        return "confirm";
                      })
                      .catch(() => {
                        return "cancel";
                      });
                    if (reLocation == "cancel") {
                      return false;
                    } else {
                      this.ibeacon = null;
                      this.getLocation(taskData, maintainProjectRelease, scanFlag, deviceMajor, isDevice, isPicture);
                    }
                  }
                }
              });
            } else {
              Toast({
                type: "fail",
                message: "扫描失败，请检查蓝牙是否开启",
                duration: 1500
              });
            }
          });
        }
      } else {
        this.toast = Toast({
          type: "loading",
          message: "获取定位中...",
          duration: 5000,
          onOpened: () => {
            // this.scanBeacon(deviceMajor, scanFlag, maintainProjectRelease, taskData, isDevice, isPicture);
            parent.wx.startBeaconDiscovery({
              uuids: ["00000000-0000-0000-0000-000000000006"],
              success(res) {
                console.log("开始扫描设备");
              },
              fail(err) {
                console.log("startBeaconDiscovery_fail", err);
              }
            });
          },
          onClose: async () => {
            parent.wx.getBeacons({
              success: async res => {
                console.log("getBeacons_success", res);
                let isMatch = false;
                if (res.beacons.length > 0) {
                  let ibeaconSearchArr = res.beacons;
                  console.log("搜索到的ibeacon", ibeaconSearchArr);
                  ibeaconSearchArr.forEach(i => {
                    if (i.minor == deviceMajor) {
                      isMatch = true;
                    }
                  });
                }
                if (isMatch) {
                  console.log("beacon设备匹配成功");
                  this.toast.clear();
                  Toast({
                    type: "success",
                    message: "定位成功",
                    duration: 1500
                  });
                  if (!scanFlag) {
                    // 不扫码
                    if (maintainProjectRelease) {
                      // 有任务书
                      let type = null;
                      if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == "日常巡检") {
                        type = 0;
                      } else if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == "专业巡检") {
                        type = 1;
                      } else {
                        type = 2; // 无任务书
                      }
                      this.$router.push({
                        path: "/taskContent",
                        query: {
                          id: taskData.taskPointRelease.id,
                          type,
                          systemNum: this.taskData.systemCode,
                          taskStartTime: this.taskData.taskStartTime
                        }
                      });
                    } else {
                      // 无任务书
                      if (isPicture == "1") {
                        this.noBookSubmit(taskData, isDevice);
                      } else {
                        this.$router.push({
                          path: "/taskResult",
                          query: {
                            taskId: this.taskData.id,
                            taskPointReleaseId: taskData.taskPointRelease.id,
                            isPicture: "0", // 0:拍照，1:不拍照
                            type: "2",
                            answerMapList: ""
                          }
                        });
                      }
                    }
                  } else {
                    this.ibeaconArr = [];
                     if(this.h5Mode == "apicloud") {
                          this.APPScan(taskData.taskPointRelease.id, taskData.taskPointId, isDevice);
                        }else {
                          this.wxScan(taskData.taskPointRelease.id, taskData.taskPointId, isDevice)
                        }
                  }
                } else {
                  console.log("beacon设备匹配失败");
                  const reLocation = await Dialog.confirm({
                    title: "提示",
                    message: "定位失败，是否重新定位",
                    confirmButtonColor: "#3562db",
                    confirmButtonText: "重新定位",
                    cancelButtonText: "放弃定位"
                  })
                    .then(() => {
                      return "confirm";
                    })
                    .catch(() => {
                      return "cancel";
                    });
                  if (reLocation == "cancel") {
                    return false;
                  } else {
                    this.ibeacon = null;
                    this.getLocation(taskData, maintainProjectRelease, scanFlag, deviceMajor, isDevice, isPicture);
                  }
                }
              },
              fail(err) {
                console.log("getBeacons_fail", err);
              },
              complete: () => {
                parent.wx.stopBeaconDiscovery({
                  success(res) {
                    console.log("停止扫描设备");
                  }
                });
              }
            });
          }
        });
      }
    },
    // 扫描beacon
    scanBeacon(deviceMajor, scanFlag, maintainProjectRelease, taskData, isDevice, isPicture) {
      if (this.h5Mode == "apicloud") {
        this.ibeacon = api.require("brightBeaconScan");
        if (this.ibeacon) {
          this.ibeacon.registerApp(
            {
              appKey: "144936ec2c66457796b43facbf263785"
            },
            () => {
              this.ibeacon.startRanging(
                {
                  uuids: ["00000000-0000-0000-0000-000000000006"],
                  type: 1,
                  mode: 2
                },
                (res, err) => {
                  if (res.status) {
                    if (res.eventType == "onSuccess") {
                      res.result.beacons
                        .filter(i => i.uuid == "00000000-0000-0000-0000-000000000006")
                        .forEach(i => {
                          if (this.ibeaconArr.indexOf(i.minor) == -1) {
                            this.ibeaconArr.push(i.minor);
                            if (this.ibeaconArr.some(j => j == deviceMajor)) {
                              this.ibeacon.stopRanging();
                              sessionStorage.setItem("whetherLocation", "2");
                              this.toast.clear();
                              Toast({
                                type: "success",
                                message: "定位成功",
                                duration: 1500
                              });
                              if (!scanFlag) {
                                // 不扫码
                                if (maintainProjectRelease) {
                                  // 有任务书
                                  let type = null;
                                  if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == "日常巡检") {
                                    type = 0;
                                  } else if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == "专业巡检") {
                                    type = 1;
                                  } else {
                                    type = 2; // 无任务书
                                  }
                                  this.$router.push({
                                    path: "/taskContent",
                                    query: {
                                      id: taskData.taskPointRelease.id,
                                      type,
                                      systemNum: this.taskData.systemCode,
                                      taskStartTime: this.taskData.taskStartTime
                                    }
                                  });
                                } else {
                                  // 无任务书
                                  if (isPicture == "1") {
                                    this.noBookSubmit(taskData, isDevice);
                                  } else {
                                    this.$router.push({
                                      path: "/taskResult",
                                      query: {
                                        taskId: this.taskData.id,
                                        taskPointReleaseId: taskData.taskPointRelease.id,
                                        isPicture: "0", // 0:拍照，1:不拍照
                                        type: "2",
                                        answerMapList: ""
                                      }
                                    });
                                  }
                                }
                              } else {
                                this.ibeaconArr = [];
                                if(this.h5Mode == "apicloud") {
                                  this.APPScan(taskData.taskPointRelease.id, taskData.taskPointId, isDevice);
                                }else {
                                  this.wxScan(taskData.taskPointRelease.id, taskData.taskPointId, isDevice)
                                }
                              }
                            }
                          }
                        });
                    }
                  } else {
                    console.log("错误", err);
                  }
                }
              );
            }
          );
        }
      }
    },
    // 无任务书提交
    noBookSubmit(taskData, isDevice) {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        staffId: this.loginInfo.staffId,
        staffName: this.loginInfo.staffName,
        sysForShort: "ipsm",
        platformFlag: 2,
        userName: this.loginInfo.staffName,
        userId: this.loginInfo.staffId,
        officeId: this.loginInfo.deptId,
        officeName: this.loginInfo.deptName,
        taskPointReleaseId: taskData.taskPointRelease.id,
        state: "2",
        taskId: taskData.id,
        spyScan: sessionStorage.getItem("whetherLocation") || 1,
        isBookEmpty: true,
        timeoutDeclaration:this.taskData.timeoutDeclaration,
        excuteStartTime:`${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      };
      Toast.loading({
        message: "正在提交...",
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      console.log('particulars',taskData.taskPointRelease.particulars)
      this.$api.inspectionSubmit(params).then(res => {
        let that = this;
        // 如果是设备需要产生操作记录
        let taskPointTypeCode = JSON.parse(taskData.taskPointRelease.particulars).taskPointTypeCode;
        let ssmId = JSON.parse(taskData.taskPointRelease.particulars).ssmId;
        let zdyId = JSON.parse(taskData.taskPointRelease.particulars).id;
        const record = {
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode,
          assetsId: this.isDevice ? this.isDevice : (taskPointTypeCode === "zdy" ? zdyId : ssmId),
          operationId: taskData.taskPointRelease.id,
          operationCode: this.taskData.systemCode, // 1:巡检 2:保养 3:报修
          operation: this.taskData.systemCode == "2" ? "保养" : "巡检",
          record: `${this.taskData.systemCode == "2" ? "保养" : "巡检"}单号：` + taskData.taskPointRelease.id
        };
        this.$api.saveOperateRecord(record).then(res => {
          console.log(res);
        });
        Toast.clear();
        Toast.success({
          message: "执行成功!",
          duration: 1000
        });
        that.getDetail();
      });
    },
    //APP扫码
    APPScan(id, taskPointId, isDevice) {
      if (!YBS.hasPermission("storage")) {
        YBS.reqPermission(["storage"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
            } else {
            }
          }
        });
        return;
      }
      if (!YBS.hasPermission("camera")) {
        YBS.reqPermission(["camera"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
          }
        });
        return;
      }
      try {
        YBS.scanCode().then(
          item => {
            if (item && item.length) {
              this.scanInfo = item.join(",");
              if (taskPointId == item[3] && (item[0] == "ihsp" || item[0] == 'iaas' || item[0] == 'insp') || taskPointId == item[3]) {
                this.getScanData(id, isDevice, taskPointId);
              } else {
                $.toast("二维码不正确。", "text");
              }
            } else {
              $.toast("未查找到相关设备", "text");
            }
          },
          () => {
            $.toast("无效的二维码,请检查二维码", "text");
          }
        );
      } catch (e) {
        $.toast(e, "text");
      }
    },
    wxScan(id, taskPointId, isDevice) {
      parent.wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
        success: res => {
          //扫码返回的结果
          console.log("wx扫码成功返回的结果", res);
          let ret = {
            content:res.resultStr
          }
          this.scanInfo = ret.content;
          var sacnInfoArr = ret.content.split(",");
          if(sacnInfoArr[0] == "ihsp" || taskPointId == sacnInfoArr[3]) {
            this.getScanData(id, isDevice, taskPointId);
          } else {
            $.toast("二维码不正确。", "text");
          }
        },
        error: res => {
          console.log("wx扫码失败", res);
          this.$toast.fail({
              message: '扫码失败,请重新扫码',
              duration: 2500
          })
        }
      });
    },
    getScanData(id, isDevice, taskPointId) {
      if (this.scanInfo == "") return;
      // 校验二维码是否为当前巡检点的码
      const scanDataArr = this.scanInfo.split(',')
      if (taskPointId != scanDataArr[scanDataArr.length - 1]) {
        return $.toast('当前二维码不正确', "text");
      }
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        planPersonCode: this.loginInfo.staffId, //当前登录人Id
        systemCode: 0, // 设备+保养 传0 综合巡检传 3
        distributionTeamId: this.loginInfo.deptId, //当前登录人部门id
        typeValue: this.scanInfo,
        id: this.taskData.id
      };
      axios({
        method: "post",
        url: __PATH.IPSM_URL + "/planTaskNewApiController/getPerformTask",
        data: qs.stringify(params),
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: localStorage.getItem("token")
        }
      }).then(res => {
        const { code, data, message } = res.data;
        if (code == "200") {
          if (data.length == 1) {
            // 无任务书
            if (!data[0].taskPointRelease.maintainProjectRelease) {
              if (JSON.parse(data[0].taskPointRelease.particulars).isPicture == "1") {
                this.noBookSubmit(data[0], isDevice);
              } else {
                this.$router.push({
                  path: "/taskResult",
                  query: {
                    taskId: data[0].id,
                    taskPointReleaseId: data[0].taskPointRelease.id,
                    isPicture: "0", // 0:拍照，1:不拍照
                    type: "2",
                    answerMapList: ""
                  }
                });
              }
            } else {
              let type = null;
              if (data[0].taskPointRelease.maintainProjectRelease.equipmentTypeName == "日常巡检") {
                type = 0;
              } else if (data[0].taskPointRelease.maintainProjectRelease.equipmentTypeName == "专业巡检") {
                type = 1;
              }
              $.toast("扫码成功", "text");
              this.$router.push({
                path: "/taskContent",
                query: {
                  id,
                  type,
                  systemNum: this.taskData.systemCode,
                  taskStartTime: this.taskData.taskStartTime
                }
              });
            }
          } else if (data.length > 1) {
            $.toast("该二维码关联了" + data.length + "个任务", "text");
          } else {
            $.toast("该二维码无关联任务", "text");
          }
        } else {
          $.toast(message || "扫码失败", "text");
        }
      });
    },
    goBack() {
      if (this.$route.query.type && this.$route.query.type == "message") {
        // api.closeFrame();
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    }
  }
};
</script>
<style scoped lang="stylus">
.inner {
  height: 97%;
  background-color: #F2F4F9;

  .task-wrap {
    padding: 0.2rem 0.32rem;
    background-color: #fff;
    border-bottom: 0.2rem solid #F2F4F9;
    font-size: 0.32rem;

    .task-box {
      min-height: 0.64rem;
      // line-height: 0.64rem;
      display: flex;
      align-items: center;

      .taskNameText {
        display: inline-block;
        overflow-y: auto !important;
      }
    }

    .task-box span:nth-child(1) {
      color: #4E5969;
      margin-right: 0.4rem;
    }

    .task-box span:nth-child(2) {
      color: #1D2129;
      overflow-y: auto !important;
    }
  }

  .task-name {
    align-items: baseline !important;
  }

  .progressWrap {
    height: calc(90% - 3.16rem);
    background-color: #fff;
    margin: 0 0.2rem;
    padding: 0.32rem;
    overflow-y: auto;

    .pointInfo {
      position: relative;
      margin-bottom: 0.16rem;
      padding-left: 0.64rem;
      font-size: 0.3rem;

      .pointName {
        margin-bottom: 0.2rem;
        padding: 0.06rem 0;
        color: #1D2129;

        .remarkDesc {
          color: #b9b9b9 !important;
          width: 100%;
          overflow-wrap: break-word
          // white-space: nowrap;
          // overflow: hidden;
          // text-overflow: ellipsis;
        }
        .collaspe{
          color: #67a9fd;
          margin-top: 5px;
          font-size: 12px;
        }
        .stateColor {
          position: absolute;
          top: 0.14rem;
          left: 0.14rem;
          display: inline-block;
          width: 0.16rem;
          height: 0.16rem;
          border-radius: 50%;
        }

        .finish {
          background-color: #3562DB;
        }

        .notFinish {
          background-color: #F53F3F;
        }
      }

      .notInspection {
        height: 0.1rem;

        .notLine {
          position: absolute;
          top: 0.48rem;
          left: 0.2rem;
          width: 1px;
          height: 0.3rem;
          background-color: #E5E6EB;
        }
      }

      .pointResult {
        color: #4E5969;

        .stepLine {
          position: absolute;
          left: 0.2rem;
          width: 1px;
          height: calc(100% - 0.6rem);
          background-color: #E5E6EB;
        }

        .resultState {
          margin-bottom: 0.12rem;
          padding: 0.06rem 0;
          display: flex;
          align-items: center;

          .stateBar {
            font-size: 0.24rem;
            padding: 0.08rem 0.12rem;
          }

          .qualified {
            background-color: #E8FFEA;
            color: #00B42A;
          }

          .disqualified {
            background-color: #FFECE8;
            color: #F53F3F;
          }
        }
      }
    }

    .pointInfo:last-child {
      .notInspection {
        .notLine {
          display: none;
        }
      }
    }
  }
 .popup-content {
  padding: 20px;
}

.popup-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.van-button {
  width: 100%;
  border-radius: 8px;
}
}
</style>
