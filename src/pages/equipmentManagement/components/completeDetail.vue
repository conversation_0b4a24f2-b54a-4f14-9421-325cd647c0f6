<template>
  <div class='inner'>
    <Header :title="$route.query.systemNum == '2' ? '保养内容' : '巡检内容'" @backFun="goBack"></Header>
    <div class="pointTitle">
      {{ $route.query.systemNum == '2' ? '保养' : '巡检' }}点名称
      <span class="point">{{ pointRelease.taskPointName }}</span>
    </div>
    <div class="taskBookConten">
      <!-- 日常 -->
      <van-collapse v-if="type == '0'" v-model="activeNames" :border="false">
        <van-collapse-item
          :is-link="false"
          title=""
          :name="index"
          v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
          :key="item.id"
        >
          <template #title>
            <div class="inspectionConten">
              <img class="imgIcon" src="../../../assets/images/equipmentManagement/轴.png" alt="">
              <span class="project">{{ $route.query.systemNum == '2' ? '保养' : '巡检' }}项目</span>
            </div>
          </template>
          <div class="item-conten">{{item.detailName}}</div>
          <div class="daily-items">标准要求
            <div class="item-conten">{{item.maintainProjectdetails.standardRequirements}}</div>
          </div>
          <div class="daily-items">{{ $route.query.systemNum == '2' ? '保养' : '巡检' }}依据
            <div class="item-conten">{{item.maintainProjectdetails.inspectionBasis}}</div>
          </div>
          <div class="step-line"></div>
        </van-collapse-item>
      </van-collapse>
      <!-- 专业 -->
      <van-collapse v-if="type == '1'" v-model="activeNames" :border="false">
        <van-collapse-item
          :is-link="false"
          title=""
          v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
          :key="item.id"
          :name="index"
        >
          <template #title>
            <div class="inspectionConten">
              <img class="imgIcon" src="../../../assets/images/equipmentManagement/轴.png" alt="">
              <!-- <span class="project">巡检项目</span> -->
              <span class="project">{{ item.detailName }}</span>
            </div>
          </template>
          <div
            v-for="(item2, inde) in item.maintainProjectdetailsTermReleaseList"
            :key="item2.id"
          >
            <div class="gist type1" v-if="item2.isNum == '1'">
              <span
                >{{ inde + 1 }}. {{ $route.query.systemNum == '2' ? '保养' : '巡检' }}要点：{{
                  item2.content ? item2.content : ""
                }}</span
              >
            </div>
            <div class="gist type2" v-if="item2.isNum == '2'">
              <span
                >{{ inde + 1 }}. {{ $route.query.systemNum == '2' ? '保养' : '巡检' }}要点：{{
                  item2.content ? item2.content : ""
                }}</span
              >
              <van-field
                v-model="item2.contentStandard"
                readonly
                label=""
                rows="1"
                type="textarea"
                autosize
              />
            </div>
            <div class="gist type3" v-if="item2.isNum == '0'">
              <span
                >{{ inde + 1 }}. {{ $route.query.systemNum == '2' ? '保养' : '巡检' }}要点：{{
                  item2.content
                    ? item2.content +
                      "(" +
                      item2.rangeStart +
                      "-" +
                      item2.rangeEnd +
                      (item2.einheitName ? item2.einheitName : '') +
                      ")"
                    : ""
                }}</span
              >
              <van-field
                v-model="item2.contentStandard"
                label=""
                readonly
                type="number"
                autosize
              >
                <div slot="extra" v-if="item2.outRange" style="color:red">输入范围异常</div>
              </van-field>
            </div>
            <div class="gist type4" v-if="item2.isNum == '3'">
              <span
                >{{ inde + 1 }}. {{ $route.query.systemNum == '2' ? '保养' : '巡检' }}要点：{{
                  item2.content ? item2.content : ""
                }}</span
              >
              <van-radio-group v-model="item2.contentStandard">
                <van-radio
                  v-for="item3 in item2.termJson"
                  :key="item3.conText"
                  :name="item3.contText"
                  checked-color="#3562DB"
                  icon-size="16px"
                  disabled
                  >{{ item3.contText }}</van-radio
                >
              </van-radio-group>
            </div>
          </div>
          <div class="step-line"></div>
        </van-collapse-item>
      </van-collapse>
      <!-- 无任务书 -->
      <div v-if="type == '2'">
        <van-empty :image="noDataImg" description="暂无任务书" />
      </div>
      <div style="height: 0.2rem; background-color: #fff;"></div>
      <!-- 巡检结果 -->
      <div class="result">
        <div class="contenTop">
          <div class="topItem">
            <div class="itemTitle">{{ $route.query.systemNum == '2' ? '保养人员' : '巡检人员' }}</div>
            <div class="itemConten">{{ excute.implementPersonName }}</div>
          </div>
          <div class="topItem">
            <div class="itemTitle">{{ $route.query.systemNum == '2' ? '保养部门' : '巡检部门' }}</div>
            <div class="itemConten">{{ excute.distributionTeamName }}</div>
          </div>
          <div class="topItem notBoder">
            <div class="itemTitle">{{ $route.query.systemNum == '2' ? '保养时间' : '巡检时间' }}</div>
            <div class="itemConten">{{ excute.excuteTime }}</div>
          </div>
        </div>
        <div class="contenTop">
          <div class="topItem">
            <div class="itemTitle">{{ $route.query.systemNum == '2' ? '保养结果' : '巡检结果' }}</div>
            <div class="itemConten">{{ result.state=='2'?'合格':result.state=='4'?'异常报修':'不合格' }}</div>
          </div>
          <div class="topItem">
            <div class="itemTitle">定位状态</div>
            <div class="itemConten">{{ excute.spyScan }}</div>
          </div>
        </div>
        <div class="bottom-conten">
          <div class="situation">{{ $route.query.systemNum == '2' ? '保养情况说明' : '巡检情况说明' }}</div>
          <van-field
            v-model="result.desc"
            rows="2"
            autosize
            readonly
            type="textarea"
          />
          <div class="situationUplod">
            <div class="imgTitle">附件图片</div>
            <div v-if="!result.attachmentUrlList.length" class="tolta">暂无</div>
          </div>
          <div style="width: 100%;margin: 0 auto;">
              <div class="resultImgBox" v-if="result.attachmentUrlList.length" >
                <div v-for="(item,index) in result.attachmentUrlList" :key="index"  @click="showImgUrl(result.attachmentUrlList,index)">
                  <img width="30%" :src="$YBS.imgUrlTranslation(item)" />
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Toast } from "vant";
import { ImagePreview } from "vant";
export default {
  components: {},
  data() {
    return {
      activeNames: [],
      loginInfo: {},
      id: '',
      type: '',
      noDataImg: require("@/assets/images/equipmentManagement/缺省@2x.png"), //暂无数据图片
      excute: {},
      result: {
        attachmentUrlList: []
      },
      pointRelease: {
        maintainProjectRelease: {
          maintainProjectdetailsReleaseList: []
        }
      }
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.id = this.$route.query.id
    this.getBookData()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  methods: {
    getBookData() {
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.id
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.taskBookDetail(params).then(res => {
        Toast.clear()
        const { excute, result, pointRelease } = res
        this.excute = excute
        this.result = result
        this.pointRelease = pointRelease
        if (pointRelease.maintainProjectRelease) {
          this.type = pointRelease.maintainProjectRelease.equipmentTypeId
          pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i,index) => {
            this.activeNames.push(index)
          })
        } else {
          this.type = '2'
        }
      })
    },
    showImgUrl(item,index) {
      ImagePreview({
        images: item,
        showIndex: true,
        loop: false,
        startPosition: index,
        swipeDuration: 50
      });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
}
</script>
<style lang="stylus" scoped>
.inner {
  height: 100%;
  background-color: #F2F4F9;
  .pointTitle {
    font-size: 0.32rem;
    color: #4E5969;
    padding: 0 0.32rem;
    height: 1.04rem;
    line-height: 1.04rem;
    background-color: #fff;
    border-bottom: 0.2rem solid #F2F4F9;
    overflow-x:auto;
    white-space:nowrap;
    .point {
      display: inline-block;
      margin-left: 0.48rem;
      color: #1D2129;
    }
  }
  .taskBookConten {
    margin: 0 auto;
    width: calc(100% - 0.4rem);
    height: calc(90% - 1.64rem);
    background-color: #fff;
    padding: 0.2rem 0;
    overflow-y: auto;
    .result {
      border-top: 0.2rem solid #F2F4F9;
      .contenTop {
        padding: 0 0.32rem;
        background-color: #fff;
        .topItem {
          height: 1.08rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #E5E6EB;
          .itemTitle {
            color: #4E5969;
          }
          .itemConten {
            color: #1D2129;
          }
        }
        .notBoder {
          border: none;
        }
      }
      .bottom-conten {
        height: calc(90% - 8.32rem);
        overflow-y: auto;
        border-top: 0.2rem solid #F2F4F9;
        padding: 0 0.32rem;
        background-color: #fff;
        .situation {
          height: 1.08rem;
          line-height: 1.08rem;
          color: #1D2129;
        }
        .situationUplod {
          height: 1.08rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .imgTitle {
            color: #1D2129;
          }
          .tolta {
            color: #86909C;
          }
        }
        .resultImgBox img {
          float: left;
        }
      }
    }
  }
}
/deep/ .van-cell::after {
  border: none;
}
/deep/ .van-collapse-item--border::after {
  border: none;
}
/deep/.van-collapse {
  background-color: #fff !important;
  .van-collapse-item {
    background-color: #fff !important;
  }
  .van-cell {
    padding: 0.12rem 0.32rem;
  }
  .van-cell__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .inspectionConten {
      display: flex;
      align-items: center;
      .imgIcon {
        width: 0.44rem;
        margin-right: 0.24rem;
      }
      .project {
        color: #1d2129;
        font-size: 0.3rem;
      }
    }
  }
  .van-collapse-item__wrapper {
    background-color: #fff !important;
  }
  .van-collapse-item__content {
    background-color: #fff !important;
    padding: 0 0.32rem 0 1rem;
    .daily-items {
      background-color: #fff !important;
      color: #1d2129;
      font-size: 0.3rem;
    }
    .item-conten {
      background-color: #fff !important;
      color: #4E5969;
      font-size: 0.3rem;
      padding: 0.12rem 0;
    }
    .step-line {
      width: 1px;
      height: calc(100% - 0.72rem);
      position: absolute;
      top: 0.64rem;
      left: 0.54rem;
      background-color: #E5E6EB;
      z-index: 999;
    }
    .van-radio {
      padding: 0.16rem 0;
    }
  }
}
</style>