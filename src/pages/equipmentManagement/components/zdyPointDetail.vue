<template>
  <div class="inner">
    <Header title="巡更点详情" @backFun="goBack"></Header>
    <div style="height: 90vh;">
      <van-tabs offset-top="10vh" sticky v-model="active" title-inactive-color="#86909C" title-active-color="#1D2129"
        color="#3562DB" animated line-height="3px">
        <van-tab title="点位信息">
          <div class="content">
            <div class="listWarp">
              <van-list>
                <div v-for="(item, index) in pointDetail" :key="item.id" :class="
                    index == 10 ? 'item items' : 'item'
                  ">
                  <span>{{ item.title }}</span>
                  <span :id="'rowValue' + index" :class="item.flag ? 'overFlow' : ''">{{ item.value }}</span>
                </div>
              </van-list>
            </div>
          </div>
        </van-tab>
        <van-tab title="运维记录">
          <div class="operationStatistics">
            <div class="inpstStatistics"
              :style="{'justify-content': $route.query.systemCode != '3' ? 'space-between' : 'space-around'}">
              <div class="itemStatistics">
                <span>应巡检</span>
                <span class="num"
                  @click="toTaskList($route.query.systemCode == '3' ? '3' : '1', '', $route.query.systemCode == '3' ? pointStatistics.comprehensiveCount : pointStatistics.equipmentCount)">
                  {{ $route.query.systemCode == '3' ? pointStatistics.comprehensiveCount : pointStatistics.equipmentCount }}
                  <span class="unit">次</span>
                </span>
              </div>
              <div class="itemStatistics">
                <span>实际巡检</span>
                <span class="num"
                  @click="toTaskList($route.query.systemCode == '3' ? '3' : '1', '1', $route.query.systemCode == '3' ? pointStatistics.comprehensiveAccomplishCount : pointStatistics.equipmentAccomplishCount)">
                  {{ $route.query.systemCode == '3' ? pointStatistics.comprehensiveAccomplishCount : pointStatistics.equipmentAccomplishCount }}
                  <span class="unit">次</span>
                </span>
              </div>
              <div v-if="$route.query.systemCode != '3'" class="itemStatistics">
                <span>应保养</span>
                <span class="num" @click="toTaskList('2', '', pointStatistics.upkeepCount)">
                  {{ pointStatistics.upkeepCount }}
                  <span class="unit">次</span>
                </span>
              </div>
              <div v-if="$route.query.systemCode != '3'" class="itemStatistics">
                <span>实际保养</span>
                <span class="num" @click="toTaskList('2', '1', pointStatistics.upkeepAccomplishCount)">
                  {{ pointStatistics.upkeepAccomplishCount }}
                  <span class="unit">次</span>
                </span>
              </div>
            </div>
            <div class="inpstDate">
              <span>下次{{ $route.query.systemCode == '2' ? '保养日期：' : '巡检日期：' }}</span>
              <span class="dateTime">{{ pointStatistics.taskStartTime || '暂无' }}</span>
            </div>
          </div>
          <div class="listWarp stepsClass">
            <div class="header" v-for="(item, index) in stepsList" :key="index">
              <div class="stepLine" v-if="index!=stepsList.length-1"></div>
              <div class="title-body">
                <div class="iconfont" v-if="index==0">&#xe681;</div>
                <div class="timeicon" v-else></div>
                <span class="time">{{ item.createTimeStr }}</span>
              </div>
              <p class="value">
                【{{ item.operation }}】{{ item.createDept }}
                {{ item.createName }}&nbsp;&nbsp;&nbsp;{{ item.record }}
              </p>
            </div>
            <p class="bottomPrompt">已经到头了~</p>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { Toast } from "vant";

export default {
  data() {
    return {
      active: 0,
      pointDetail: [
        {
          title: '巡更点名称',
          value: ''
        },
        {
          title: '巡更点编码',
          value: ''
        },
        {
          title: '备注说明',
          value: ''
        }
      ],
      pointStatistics: {},
      loginInfo: {},
      stepsList: [],
      assetsId: this.$route.query.id || '',
    };
  },
  watch: {
    'active'(val) {
      if (val == 0) {
        this.getZdyDetailByid()
      } else {
        this.stepsList = []
        this.getPointAmountStatistics()
        this.getMaintenanceList()
      }
    }
  },
  created() {
    this.$route.meta.keepAlive = true
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo")).unitCode
    this.getZdyDetailByid()
  },
  activated() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  methods: {
    // 获取运维记录
    getMaintenanceList() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        assetsId: this.assetsId
      };
      this.$api.maintenanceList(params).then(res => {
        this.stepsList = res;
      });
    },
    goBack() {
      this.$route.meta.keepAlive = false
      if (this.$route.query.isClould) {
        this.$YBS.apiCloudCloseFrame()
      } else {
        this.$router.go(-1)
      }
    },
    getZdyDetailByid() {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.$route.query.id
      };
      this.$api.zdyPointDetail(params).then(res => {
        this.pointDetail = [
          {
            title: '巡更点名称',
            value: res.taskPointName
          },
          {
            title: '巡更点编码',
            value: res.taskPointCode
          },
          {
            title: '描述',
            value: res.remarks
          }
        ]
        Toast.clear()
      });
    },
    // 运维记录统计
    getPointAmountStatistics() {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        pointCode: this.$route.query.id,
        taskStartTime: this.$route.query.taskStartTime || '',
        systemCode: this.$route.query.systemCode
      };
      this.$api.taskPointAmountStatistics(params).then(res => {
        this.pointStatistics = res
        Toast.clear()
        this.stepsList = []
        this.getMaintenanceList()
      });
    },
    toTaskList(coed, type, num) {
      if (num == 0) {
        return $.toast("暂无更多", 'text');
      }
      sessionStorage.setItem('isPointDetail', true)
      this.$router.push({
        path: "/taskList",
        query: {
          from: 'pointDetail',
          type,
          systemCode: coed,
          pointCode: this.$route.query.id
        }
      });
    }
  }
};
</script>

<style scoped lang="stylus">
.inner
  background-color #F2F4F9
  height 100vh
  /deep/ .van-tabs
    height 100%
    .van-tabs__content
      height calc(100% - 44px)
      .van-tab__pane-wrapper
        overflow auto
        .van-tab__pane
          height calc(100% - 10px)
  .content
    margin-top 1px
    .listWarp
      margin 10px 0 0 0
      .item
        padding 16px 16px
        display flex
        font-size 15px
        color #333
        background-color #fff
        line-height 18px
        font-size 17px
      .item > span:nth-child(1)
        display inline-block
        width 30%
        color #47515F
      .item > span:nth-child(2)
        display inline-block
        width 65%
        word-wrap break-word
      .overFlow
        overflow hidden // 超出隐藏
        white-space nowrap // 不折行
        text-overflow ellipsis // 溢出显示省略号
      .items
        margin-top 0.1875rem
  .bottomPrompt
    margin 0
    padding 0
    font-size 14PX
    text-align center
    color #bbb
  .operationStatistics
    margin 0.2rem 0
    padding 0.3rem 0.64rem
    background-color #fff
    .inpstStatistics
      display flex
      .itemStatistics
        padding 0.3rem 0
        display flex
        flex-direction column
        align-items center
        font-size 16px
        .num
          margin 0.3rem 0
          font-size 18px
          font-weight bold
          color #3562db
          .unit
            font-size 14px
            color #1d2129
    .inpstDate
      display flex
      align-items center
      .dateTime
        font-size 18px
        font-weight bold
        color #1d2129
>>>.van-step--vertical .van-step__line
  top 24px
  left -15px
  width 1px
  height 85%
.step
  p
    word-wrap break-word
    width 100%
    overflow hidden // 超出隐藏
    text-overflow ellipsis // 溢出显示省略号
    white-space normal // 常规默认，会折行
    display -webkit-box
    -webkit-box-orient vertical // 子元素排列 vertical（竖排）orhorizontal（横排）
    -webkit-line-clamp 2 /* 内容限制的行数 需要几行写几就行 */
.stepsClass
  background-color #fff
  height calc(100% - 162px)
  overflow auto
.header
  margin 0.15rem 0 0.15rem 0.1rem
  padding 0 0.24rem
  background #fff
  position relative
  .iconfont
    font-size 0.4rem
    color rgb(53, 98, 219)
    margin-right 0.1875rem
  .title-body
    height 0.6rem
    display flex
    align-items center
    white-space nowrap
    .time
      font-size 0.3rem
      color #1D2129
  .value
    padding-left 0.5rem
    font-size 15px
    color #4E5969
    line-height 0.4rem
    word-wrap break-word
    overflow hidden // 超出隐藏
    text-overflow ellipsis // 溢出显示省略号
    white-space normal // 常规默认，会折行
    display -webkit-box
    -webkit-box-orient vertical // 子元素排列 vertical（竖排）orhorizontal（横排）
    -webkit-line-clamp 2 /* 内容限制的行数 需要几行写几就行 */
.stepLine
  position absolute
  left 0.42rem
  top 0.52rem
  width 1px
  height calc(100% - 0.6rem)
  background-color #E5E6EB
.timeicon
  display inline-block
  margin-left 0.05rem
  width 0.15rem
  height 0.15rem
  border-radius 50%
  background-color #fff
  border 0.06rem solid #3562DB
  margin-right 0.1875rem
</style>
