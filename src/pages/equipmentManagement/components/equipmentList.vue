<template>
  <div :class="[this.$route.query.device ? 'deviceItem' : '']">
    <Header
      v-if="this.$route.query.pageType !== 'borrow'"
      :title="!childList ? '设备列表' : '设备筛选'"
      :leftIcon="
        childList || (pageUrl && !this.$route.query.device) ? 'icon-back' : ''
      "
      @backFun="backFn"
      :rightText="!childList || beforePageUrl !== '/addRepair' ? '筛选' : ''"
      @moreFun="handleMoreEvent"
    ></Header>
    <Header
      v-if="this.$route.query.pageType === 'borrow'"
      :title="!childList ? '设备列表' : '设备筛选'"
      :leftIcon="childList || pageUrl ? 'icon-back' : ''"
      @backFun="backFn"
    ></Header>
    <!-- 搜索部分 -->
    <div
      v-if="!childList"
      :class="[this.$route.query.device ? 'deviceSearchItem' : '']"
    >
      <div class="searchNewBox" v-if="beforePageUrl !== '/addRepair'">
        <van-search
          v-model="searchValue"
          show-action
          placeholder="设备编码、设备名称"
          @search="onSearch"
        >
          <template #action>
            <div @click="onSearch" class="search-btn">搜索</div>
          </template>
        </van-search>
        <div class="right-icon-sty">
          <i class="iconfont iconfontScan" @click="toScan()">&#xe665;</i>
        </div>
      </div>
      <van-search
        v-model="searchValue"
        show-action
        placeholder="设备编码、SN号、设备名称"
        @search="onSearch"
        v-else
      >
        <template #action>
          <div @click="onSearch" class="search-btn">搜索</div>
        </template>
      </van-search>

      <!-- 中间列表部分 -->
      <div class="content">
        <van-pull-refresh
          v-if="!noDate"
          v-model="refreshing"
          @refresh="onRefresh"
        >
          <van-list
            style="background: #fff"
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            ref="listRef"
            @load="onLoad"
            :immediate-check="false"
          >
            <van-row
              v-for="(item, index) in listArr"
              :key="index"
              @click="intoDetail(item)"
            >
              <van-col span="7">
                <img
                  class="row-img"
                  v-if="item.titlePic"
                  :src="item.titlePic"
                />
                <img class="row-img" v-else :src="equipZw" />
              </van-col>
              <van-col span="17">
                <div class="row-title">{{ item.assetsName }}</div>
                <ul>
                  <li>设备编码：{{ item.assetsNumber }}</li>
                  <li>SN号：{{ item.snNumber }}</li>
                  <li>使用部门：{{ item.useDepartmentName }}</li>
                  <li>设备位置：{{ item.areaName }}</li>
                </ul>
              </van-col>
            </van-row>
          </van-list>
        </van-pull-refresh>
        <div v-if="noDate">
          <van-empty :image="noDataImg" description="暂无数据" />
        </div>
      </div>
    </div>
    <!-- <equip-select v-if="childList" @selectBtn="selectBtn"></equip-select> -->
  </div>
</template>
<script>
// import equipSelect from "./equipSelect";
// import { wbEquipmentList, getAssetsListById } from "@/common/api";
// import wx from "weixin-js-sdk";
import { Toast } from "vant";
export default {
  name: "equipmentList",
  components: {
    // equipSelect,
  },
  data() {
    return {
      pageUrl: "",
      assetsNumber: "",
      assetsName: "",
      model: "",
      areaName: "",
      adminName: "",
      equipmentTypeIds: "",
      useDepartmentId: "",
      stateValue: "",
      markValue: "",
      childList: false,
      listArr: [],
      searchValue: "", //输入的搜索内容
      noDataImg: require("@/assets/images/empty.png"), //暂无数据图片
      equipZw: require("@/assets/images/equipNo.png"), //暂无数据图片
      dataLoading: false,
      formData: {},
      paginationData: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      refreshing: false,
      loading: false,
      finished: false,
      noDate: false, //页面无数据标识
      currentLoginInfo: "",
      beforePageUrl: "",
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.beforePageUrl = from.path;
    });
  },
  created: function () {
    this.pageUrl = this.$route.query.pageUrl;
    this.currentLoginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.formPage();

    // this.init();
  },
  methods: {
    /* 扫码 */
    toScan() {
      if (this.utils.isWechat()) {
        // 如果是在微信端。
        this.WXScan();
      } else {
        this.APPScan();
      }
    },
    //APP扫码
    APPScan() {
      const YBS = this.utils;
      if (!YBS.hasPermission("storage")) {
        YBS.reqPermission(["storage"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
            } else {
            }
          }
        });
        return;
      }
      if (!YBS.hasPermission("camera")) {
        YBS.reqPermission(["camera"], function (ret) {
          console.log(ret);
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
          }
        });
        return;
      }
      try {
        this.utils.scanCode().then(
          (item) => {
            this.utils
              .getScanInfo(item)
              .then((ret) => {
                this.getAssetsListFn(ret.result);
              })
              .catch((err) => {
                // console.log('bad')
                this.$toast.fail("未查找到相关设备");
              });
          },
          () => {
            this.$toast.fail("未查找到相关设备");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    // 微信扫码
    WXScan() {
      // let _self = this;
      // wx.error(function (res) {
      //   console.log(res, "ooooooooooooooooooo");
      // });
      // wx.scanQRCode({
      //   needResult: 1,
      //   success: function (res) {
      //     const { resultStr } = res;
      //     _self.utils
      //       .getScanInfo(resultStr)
      //       .then((ret) => {
      //         console.log("扫码获取retretretret", ret.result);
      //         _self.getAssetsListFn(ret.result);
      //       })
      //       .catch((err) => {
      //         _self.$toast.fail("未查找到相关设备");
      //       });
      //   },
      //   fail: function (res) {
      //     _self.$toast.fail("扫码失败");
      //   },
      //   error: function (res) {
      //     if (res.errMsg.indexOf("function_not_exist") > 0) {
      //       _self.$toast.fail("版本过低请升级");
      //     }
      //   },
      // });
    },
    //扫码完成获取设备信息，并跳转到详情页面
    getAssetsListFn(val) {
      console.log("scan val", val);
      if (!val) return this.$toast.fail("未查找到相关设备");
      let params = {
        assetsNumber: val,
        currentPage: 1,
        pageSize: 1,
      };
      getAssetsListById(params).then((res) => {
        const { code, data, message } = res;
        if (code == 200) {
          let strItem = JSON.stringify(res.data.list[0]);
          this.$router.push({
            path: "/equipmentDetail",
            query: {
              data: encodeURIComponent(strItem),
            },
          });
        } else {
        }
      });
    },
    // 初始化
    init() {
      this.onRefresh();
    },
    // 判断从效益分析页面过来
    formPage() {
      if (this.pageUrl == "benifitAnalysix") {
        this.markValue = 4;
        // this.childList = true;
      } else if (this.pageUrl == "repair") {
        this.markValue = 3; //3：表示维保
      } else {
        this.markValue = 3; //3：表示维保
      }
      this.onRefresh();
    },
    handleMoreEvent() {
      this.childList = true;
      // this.$router.push({
      //   path: "/equipSelect"
      // });
    },
    // 筛选
    selectBtn(data) {
      this.listArr = [];
      this.assetsNumber = data.assetsNumber;
      this.assetsName = data.assetsName;
      this.model = data.model;
      this.areaName = data.areaName;
      this.adminName = data.adminName;
      this.equipmentTypeIds = data.equipmentTypeIds;
      this.useDepartmentId = data.useDepartmentId;
      this.stateValue = data.stateValue;
      this.markValue = data.markValue;
      this.childList = false;
      this.onRefresh();
    },
    // 搜索事件
    onSearch() {
      this.listArr = [];
      this.onRefresh();
    },
    onRefresh() {
      this.paginationData.currentPage = 1;
      // 清空列表数据
      this.finished = false;
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.onLoad();
    },
    onLoad() {
      setTimeout(() => {
        if (this.refreshing) {
          this.listArr = [];
          this.refreshing = false;
        }
        if (this.pageUrl == "benifitAnalysix" || this.pageUrl == "repair") {
          this.gianCheckData();
        } else {
          this.getEquipmentList();
        }

        this.paginationData.currentPage++;
      }, 1000);
    },
    // 从接口获取数据
    gianCheckData() {
      let params = {
        compound: this.searchValue,
        assetsName: this.assetsName, //设备名称
        assetsNumber: this.assetsNumber, //	设备编号
        model: this.model, // 规格型号
        areaName: this.areaName, //区域名称
        adminName: this.adminName, //操作人员
        useDepartmentId: this.useDepartmentId,
        competentDeptTypeIds: this.equipmentTypeIds,
        useStatus: this.stateValue,
        assetsMark: this.markValue || 3, //设备标记
        moduleIdentity: "IMES_CORE",
        sysIdentity:
          this.pageUrl == "repair"
            ? "systemAdminCode"
            : this.currentLoginInfo.sysIdentity,
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
      };
      // this.utils.imasGetDepartment(this, params, "getAssetsList", false); // 获取设备列表划数据
      this.utils.getDepartment(this, params, "getAssetsList", false); // 获取设备列表划数据
    },
    // 设备列表数据回调
    getAssetsList(data) {
      // console.log('接口',data)
      if (data.list.length == 0 && this.listArr.length == 0) {
        this.listArr = [];
        this.finished = true;
        this.noDate = true;
      } else {
        this.finished = false;
        this.noDate = false;
      }
      this.paginationData.total = data.sum;
      if (this.paginationData.total != "") {
        if (
          this.paginationData.currentPage >
          Math.ceil(this.paginationData.total / this.paginationData.pageSize)
        ) {
          this.finished = true;
        }
      }
      this.loading = false;
      this.listArr = [...this.listArr, ...data.list];
    },
    getEquipmentList() {
      let params = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        sysIdentity: this.currentLoginInfo.sysIdentity,
        roleCode: this.currentLoginInfo.roleCode,
        interfaceNum: 3,
        userType: this.currentLoginInfo.type,
        unitCode: this.currentLoginInfo.unitCode,
        hospitalCode: this.currentLoginInfo.hospitalCode,
        userId: this.currentLoginInfo.userId,
        systemCode: "immas",
        // params.systemCode ='imes';
        officeCode: this.currentLoginInfo.officeCode,
        moduleIdentity: this.currentLoginInfo.moduleIdentity,
        userName: this.currentLoginInfo.name,
        companyCode: this.currentLoginInfo.companyCode,
        teamIds: this.currentLoginInfo.teamIds,
        compound: this.searchValue,
        maintainFlag: "1",
        assetsName: this.assetsNumber,
        assetsNumber: this.assetsName,
        model: this.model,
        areaName: this.areaName,
        adminName: this.adminName,
        useDepartmentId: this.equipmentTypeIds,
        competentDeptTypeIds: this.useDepartmentId,
        useStatus: this.stateValue,
        assetsMark: this.markValue,
      };
      wbEquipmentList(params).then((res) => {
        if (res.code == "200") {
          const { data } = res;
          if (data.list.length == 0 && this.listArr.length == 0) {
            this.listArr = [];
            this.finished = true;
            this.noDate = true;
          } else {
            this.finished = false;
            this.noDate = false;
          }
          this.paginationData.total = data.sum;
          if (this.paginationData.total != "") {
            if (
              this.paginationData.currentPage >
              Math.ceil(
                this.paginationData.total / this.paginationData.pageSize
              )
            ) {
              this.finished = true;
            }
          }
          this.loading = false;
          this.listArr = [...this.listArr, ...data.list];
        }
      });
    },
    backFn() {
      if (this.pageUrl) {
        this.$router.go(-1);
      }
      this.childList = false;
    },
    // 进入详情
    intoDetail(item) {
      const { pageUrl, plan } = this.$route.query;
      console.log(this.$route.query);
      if (plan && plan == "planOut") {
        if (
          localStorage.getItem("selectEquip") &&
          JSON.parse(localStorage.getItem("selectEquip")).length
        ) {
          let arr = JSON.parse(localStorage.getItem("selectEquip"));
          arr.forEach((val, index) => {
            if (val.id == item.id) {
              arr.splice(index, 1);
            }
          });
          localStorage.setItem("selectEquip", JSON.stringify(arr));
        }
        this.$router.go(-1);
        localStorage.setItem("planCheckedEquip", JSON.stringify(item));
        return false;
      }
      if (
        pageUrl &&
        !this.$route.query.device &&
        (pageUrl == "repair" || pageUrl == "assetsTransfer")
      ) {
        console.log("fromPathAddRepair");
        this.$router.go(-1);
        localStorage.setItem("checkedEquip", item.assetsCode);
        localStorage.setItem("assetsRecordId", item.id);
      } else if (pageUrl && pageUrl == "repair" && this.$route.query.device) {
        console.log("fromPathEquipment");
        let strItem = JSON.stringify(item);
        this.$router.push({
          path: "/equipmentDetail",
          query: {
            data: encodeURIComponent(strItem),
          },
        });
      } else {
        let strItem = JSON.stringify(item);
        this.$router.push({
          path: "/equipmentDetail",
          query: {
            data: encodeURIComponent(strItem),
          },
        });
      }
    },
  },
};
</script>
<style scoped lang="scss">
.deviceItem {
  width: 100%;
  height: 100vh;
  background: #f5f6fb;
  overflow: auto;
}
.deviceSearchItem {
  height: calc(100vh - 2rem);
}
.right-icon-sty {
  margin-right: 14px;
}
.right-icon-sty i {
  font-size: 22px;
}

.iconfontRight {
  color: #d8dee7;
}

.iconfontScan {
  // color: $main-bgColor;
}
.content {
  height: calc(100vh - 4.7rem);

  /deep/ .van-row {
    display: flex;
    align-items: center;
    padding: 15px 0;
    box-shadow: 0px 1px 0px 0px #f5f6fb;
  }

  .row-img {
    width: 4.4rem;
    height: 4.4rem;
  }

  .row-title {
    font-size: 0.8rem;
    font-family: PingFang-SC-Medium, PingFang-SC;
    font-weight: 500;
    color: #333333;
  }

  ul {
    margin-top: 10px;

    li {
      font-size: 0.75rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #47515f;
      margin-bottom: 5px;
      word-wrap: break-word;
    }
  }
}
.searchNewBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #efeff4;
  box-shadow: 0px 1px 0px 0px #dddddd;
  padding-right: 12px;
  width: 100%;
  /deep/ .van-search {
    background: #efeff4;
    box-shadow: 0px 1px 0px 0px #dddddd;
    margin-right: 10px;
    width: 80%;
  }
}

/deep/ .van-search {
  background: #efeff4;
  box-shadow: 0px 1px 0px 0px #dddddd;
  // margin-right: 10px;
  // width: 80%;
}

/deep/ .van-search__action {
  font-size: 0.85rem;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #2d4a74;
  display: flex;
  align-items: center;
}
</style>
