<template>
  <div class='inner'>
    <Header :title="systemCode=='0'?'设备管理':'综合巡检'" showRightBtn rightIcon="scan" @backFun="goBack"
      @taskScanCode="scanCode"></Header>
    <div class="topContent">
      <div class="ImagesLine">
        <div class="imageBar" v-for="(item, index) in topImageData" :key="index" @click="toItemList(item.name)">
          <van-badge :content="getCount(item)">
            <img :src="item.url" :alt="item.name">
          </van-badge>
          <div class="barText">{{ item.name }}</div>
        </div>
      </div>
      <div v-if="bottomImageData.length > 0" class="ImagesLine" style="margin-top: 0.2rem;">
        <div class="imageBar" v-for="(item, index) in bottomImageData" :key="index" @click="toItemList(item.name)">
          <img :src="item.url" :alt="item.name">
          <div class="barText">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <div class="listWarp" :class="systemCode == '3' ? 'listWarp1' : ''">
      <div class="listTitle">
        <span></span>
        今日任务提醒({{ systemCode=='0' ? countData.equipmentDayCount || 0 : countData.comprehensiveCount || 0 }})
      </div>
      <van-pull-refresh v-if="listData.length > 0" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div v-for="(i, index) in listData" :key="index" class="list" @click="goTaskDetail(i)">
            <div class="title">
              <span class="task-name">{{ i.taskName }}</span>
              <span class="typeWarp">
                <span class="taskType">{{ typeOptions.find(j => i.cycleType == j.cycleType).label }}</span>
                <van-icon name="arrow" />
              </span>
            </div>
            <div class="point">
              <div class="pointItem">
                <span class="pointTitle">{{ i.systemCode == '2' ? '应保养点数' : '应巡点数' }}</span>
                <span class="count">{{ i.totalCount }}</span>
              </div>
              <div class="pointItem">
                <span class="pointTitle">{{ i.systemCode == '2' ? '已保养点数' : '已巡点数' }}</span>
                <span class="count">{{ i.hasCount }}</span>
              </div>
            </div>
            <div class="time">
              <span class="timeTitle">{{ i.systemCode == '2' ? '应保养日期' : '应巡日期' }}</span>
              <span
                class="timeDate">{{ i.cycleType == 6 ? i.taskStartTime : moment(i.taskStartTime).format('YYYY-MM-DD') }}</span>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment"
import YBS from "@/assets/utils/utils.js";
import { Toast } from "vant";
import axios from 'axios'
import qs from "qs";
import { mapState } from "vuex";
export default {
  components: {},
  computed: {
    ...mapState(["h5Mode"])
  },
  data() {
    return {
      YBS,
      moment,
      systemCode: this.$route.query.systemCode,
      isLoading: false,
      loading: false,
      finished: false,
      loginInfo: '',
      topImageData: [],
      bottomImageData: [],
      // 周期类型
      typeOptions: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      listData: [],
      countData: {},
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      ibeacon: '',
      isDevice: '',
      timer: '',
      ibeaconArr: []
    }
  },
  beforeRouteEnter(to, from, next) {
    if (to.query.systemCode) {
      to.meta.title = to.query.systemCode == '0' ? '设备管理' : '综合巡检'
    }
    next()
  },
  created() {
    sessionStorage.removeItem('isPointDetail')
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    if (this.systemCode == '0') {
      this.topImageData = [
        {
          url: require('../../assets/images/equipmentManagement/icon／2级／总务报修@2x.png'),
          name: '巡检任务'
        },
        {
          url: require('../../assets/images/equipmentManagement/icon／2级／设备报修@2x.png'),
          name: '保养任务'
        }, {
          url: require('../../assets/images/equipmentManagement/icon／2级／应急保洁@2x.png'),
          name: '设备管理'
        }
      ]
      this.bottomImageData = [
        {
          url: require('../../assets/images/equipmentManagement/icon／2级／运送@2x.png'),
          name: '设备报修'
        },
        {
          url: require('../../assets/images/equipmentManagement/icon／2级／随手拍@2x.png'),
          name: '任务统计'
        }, {
          url: require('../../assets/images/equipmentManagement/icon／2级／公车@2x.png'),
          name: '超时任务'
        }
      ]
    } else if (this.systemCode == '3') {
      this.topImageData = [
        {
          url: require('../../assets/images/equipmentManagement/icon／2级／总务报修@2x.png'),
          name: '巡检任务'
        },
        {
          url: require('../../assets/images/equipmentManagement/icon／2级／随手拍@2x.png'),
          name: '任务统计'
        },
        {
          url: require('../../assets/images/equipmentManagement/icon／2级／公车@2x.png'),
          name: '超时任务'
        }
      ]
      this.bottomImageData = []
    }
    this.getTaskStatistics()
    this.getTaskList()
  },
  mounted() {
    this.getPermission()
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    getCount(item) {
      if (this.systemCode == '0') {
        return item.name == '巡检任务' ? this.countData.equipmentCount || 0 : item.name == '保养任务' ? this.countData.maintainCount || 0 : ''
      } else if (this.systemCode == '3') {
        return item.name == '巡检任务' ? this.countData.comprehensiveCount || 0 : ''
      }
    },
    toItemList(type) {
      if (type == '巡检任务' || type == '超时任务') {
        let menuType = ''
        if (type == '巡检任务') {
          menuType = '0'
        } else {
          menuType = '1'
        }
        this.$router.push({
          path: '/taskList',
          query: {
            from: 'menu',
            systemNum: this.systemCode == '0' ? '1' : '3',
            menuType // 0：巡检执行 1：巡检查看
          }
        })
      } else if (type == '设备管理') {
        this.$router.push({
          path: '/deviceList'
        })
      } else if (type == '设备报修') {
        this.$router.push({
          path: '/equipmentReportRepair',
          query: {
            from: 'inspection'
          }
        })
      } else if (type == '保养任务') {
        this.$router.push({
          path: '/taskList',
          query: {
            from: 'menu',
            systemNum: '2',
            menuType: '0'
          }
        })
      } else if (type == '任务统计') {
        // 设备巡检
        if (this.systemCode == '0') {
          this.$router.push({
            path: '/assetsTaskAnalysis',
            query: {
              systemNum: '0',
              from: 'inspection'
            }
          })
        } else {  //综合巡检
          this.$router.push({
            path: '/taskAnalysis',
            query: {
              systemNum: '3',
              from: 'inspection'
            }
          })
        }
        // this.$router.push({
        //   path: '/taskAnalysis',
        //   query: {
        //     systemNum: this.systemCode == '0' ? '0' : '3',
        //     from: 'inspection'
        //   }
        // })
      }
    },
    goTaskDetail(row) {
      sessionStorage.setItem('taskInfo', JSON.stringify(row))
      this.$router.push({
        path: '/taskDetail',
        query: {
          id: row.id,
          menuType: '0', // 0：巡检执行 1：巡检查看
          systemNum: this.systemCode
        }
      })
    },
    getTaskStatistics() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        distributionTeamId: this.loginInfo.deptId,
        planPersonCode: this.loginInfo.staffId,
        accomplishType: 0
      }
      this.$api.taskStatistics(params).then(res => {
        this.countData = res
      })
    },
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = false
      this.loading = true
      this.getTaskList()

    },
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.listData = []
      this.getTaskStatistics()
      this.getTaskList()
    },
    getTaskList() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        distributionTeamId: this.loginInfo.deptId,
        planPersonCode: this.loginInfo.staffId,
        accomplishType: 0,
        pageNo: this.pageParmes.pageNo,
        pageSize: this.pageParmes.pageSize,
        systemCode: this.systemCode
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.todayTaskList(params).then(res => {
        this.listData = this.listData.concat(res)
        this.isLoading = false
        Toast.clear()
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
      })
    },
    goBack() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
    scanCode() {
      if (this.h5Mode == "apicloud") {
        this.APPScan()
      }else {
        this.wxScan()
      }
    },
    // 获取扫码相关权限
    getPermission() {
      if (!this.YBS.hasPermission("storage")) {
        this.YBS.reqPermission(["storage"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            if (!this.YBS.hasPermission("camera")) {
              this.YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                  const btn = document.getElementById('btn')
                  btn.click()
                } else {
                  this.$toast('请打开相机权限否则将无法拍照')
                }
              });
              return;
            }
          }
        });
        return;
      } else {
        if (!this.YBS.hasPermission("camera")) {
          this.YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            } else {
              this.$toast('请打开相机权限否则将无法拍照')
            }
          });
          return;
        }
      }
    },
    // 扫码逻辑
    APPScan() {
      var FNScanner = api.require('FNScanner')
      return new Promise((resolve, reject) => {
        FNScanner.openScanner({
          autorotation: false,
          isAlbum: true
        }, (ret, err) => {
          if (ret.eventType == 'success') {
            console.log('ret.content', ret.content);
            var sacnInfoArr = ret.content.split(",");
            const pattern = /^KJ,/;
            if (sacnInfoArr.some(i => i.includes('spaceCode'))) {
              console.log(111);
              const codes = ret.content.slice(ret.content.indexOf('?') + 1, ret.content.length).split('&')
              const spaceScanInfo = ['ihsp',]
              codes.forEach(i => {
                if (i.indexOf('unitCode') != -1) {
                  spaceScanInfo[1] = i.slice(i.indexOf('=') + 1, i.length)
                } else if (i.indexOf('hospitalCode') != -1) {
                  spaceScanInfo[2] = i.slice(i.indexOf('=') + 1, i.length)
                } else if (i.indexOf('spaceCode') != -1) {
                  spaceScanInfo[3] = i.slice(i.lastIndexOf(',') + 1, i.length)
                }
              })
              sacnInfoArr = spaceScanInfo
              let spaceCodePattern = /(?:\bspaceCode=)([^&]+)/;
              let spaceCodeMatch = ret.content.match(spaceCodePattern);
              if (spaceCodeMatch && spaceCodeMatch[1]) {
                if(spaceCodeMatch[1].split(',')[0] == 'KJ') {
                  sacnInfoArr[0] = 'ihsp'
                }
                if(spaceCodeMatch[1].split(',')[0] == 'ZC') {
                  sacnInfoArr[0] = 'iaas'
                }
                if(spaceCodeMatch[1].split(',')[0] == 'ZDY') {
                  sacnInfoArr[0] = 'insp'
                }
              }
              // return console.log(sacnInfoArr)
            } else if (pattern.test(ret.content)) {
              sacnInfoArr = ['ihsp', this.loginInfo.unitCode, this.loginInfo.hospitalCode, sacnInfoArr[2]]
            }
            const params = {
              unitCode: this.loginInfo.unitCode,
              hospitalCode: this.loginInfo.hospitalCode,
              distributionTeamId: this.loginInfo.deptId,
              planPersonCode: this.loginInfo.staffId,
              typeValue: sacnInfoArr.join(','),
              systemCode: this.systemCode,
              accomplishType: 0 // 未执行任务
            }
            sessionStorage.setItem('scanCodeData', JSON.stringify(params))
            Toast.loading({
              message: '加载中...',
              forbidClick: true,
              overlay: false,
              duration: 0
            });
            params.pageNo = 1
            params.pageSize = 2
            axios({
              method: 'post',
              url: __PATH.IPSM_URL + '/planTaskNewApiController/getPerformTask',
              data: qs.stringify(params),
              headers: {
                "Content-Type": "application/x-www-form-urlencoded",
                'Authorization': localStorage.getItem("token")
              }
            }).then(res => {
              Toast.clear()
              const { code, data, message } = res.data
              if (code == '200') {
                if (data.length > 1) {
                  sessionStorage.setItem('source', sacnInfoArr[3])
                  this.$router.push({
                    path: 'taskList',
                    query: {
                      from: 'scanCode',
                      source: sacnInfoArr[3],
                      systemNum: this.systemCode,
                      menuType: 0
                    }
                  })
                } else if (data.length == 1) {
                  // 判断是否为设备巡检点
                  this.isDevice = JSON.parse(data[0].taskPointRelease.particulars).assetsId ? JSON.parse(data[0].taskPointRelease.particulars).assetsId : false
                  // 需要定位
                  if (data[0].locationFlag == '0') {
                    const deviceMajor = JSON.parse(data[0].taskPointRelease.particulars).deviceMinor
                    const isPicture = JSON.parse(data[0].taskPointRelease.particulars).isPicture
                    const item = {
                      taskPointRelease: {
                        id: data[0].taskPointRelease.id,
                        particulars: data[0].taskPointRelease.particulars
                      },
                      id: data[0].id,
                      systemCode: data[0].systemCode,
                      taskStartTime: data[0].taskStartTime
                    }
                    if (deviceMajor) {
                      this.getLocation(item, data[0].taskPointRelease.maintainProjectRelease, deviceMajor, isPicture)
                    } else {
                      sessionStorage.setItem('whetherLocation', '3')
                      if (data[0].taskPointRelease.maintainProjectRelease) { // 有任务书
                        let type = null
                        if (data[0].taskPointRelease.maintainProjectRelease.equipmentTypeId == '0') {
                          type = 0
                        } else if (data[0].taskPointRelease.maintainProjectRelease.equipmentTypeId == '1') {
                          type = 1
                        }
                        this.$router.push({
                          path: "/taskContent",
                          query: {
                            id: item.taskPointRelease.id,
                            type,
                            systemNum: item.systemCode,
                            taskStartTime: item.taskStartTime
                          },
                        });
                      } else { // 无任务书
                        if (isPicture == '1') {
                          this.noBookSubmit(item)
                        } else {
                          this.$router.push({
                            path: '/taskResult',
                            query: {
                              taskId: item.id,
                              taskPointReleaseId: item.taskPointRelease.id,
                              isPicture: '0', // 0:拍照，1:不拍照
                              type: '2',
                              answerMapList: ''
                            }
                          })
                        }
                      }
                    }
                  } else {
                    this.oneTask(data[0])
                  }
                } else if (data.length == 0) {
                  if (sacnInfoArr[0].toLowerCase() == 'ihsp') { // 空间
                    this.$router.push({
                      path: "/spaceDetail",
                      query: {
                        id: sacnInfoArr[3],
                        systemCode: this.systemCode
                      }
                    });
                  } else if (sacnInfoArr[0].toLowerCase() == 'iaas') { // 设备
                    this.$router.push({
                      path: "/deviceInfo",
                      query: {
                        id: sacnInfoArr[3],
                        fromInsp: 1,
                        systemCode: this.systemCode
                      }
                    })
                  } else if (sacnInfoArr[0].toLowerCase() == 'insp') { // 自定义
                    this.$router.push({
                      path: "/zdyPointDetail",
                      query: {
                        id: sacnInfoArr[3],
                        systemCode: this.systemCode
                      }
                    })
                  } else {
                    this.$toast.fail('该二维码未关联任务')
                  }
                }
              } else {
                this.$toast.fail(message || '扫码失败')
              }
            })
          } else if (ret.eventType == 'cameraError') {
            this.loding = false
            this.$toast.fail({
              message: '请开启访问摄像头权限',
              duration: 2500
            })
            setTimeout(() => {
              FNScanner.closeView()
            }, 2500)
          } else if (ret.eventType == 'fail') {
            this.loding = false
            this.$toast.fail({
              message: '扫码失败,请重新扫码',
              duration: 2500
            })
            setTimeout(() => {
              FNScanner.closeView()
            }, 2500)
          } else if (ret.eventType == 'cancel') {
            FNScanner.closeView()
          }
        })
      })
    },
    wxScan() {
      parent.wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
        success: res => {
          //扫码返回的结果
          console.log("wx扫码成功返回的结果", res);
          let ret = {
            content:res.resultStr
          }
          var sacnInfoArr = ret.content.split(",");
          const pattern = /^KJ,/;
          if (sacnInfoArr.some(i => i.includes('spaceCode'))) {
              console.log(111);
              const codes = ret.content.slice(ret.content.indexOf('?') + 1, ret.content.length).split('&')
              const spaceScanInfo = ['ihsp',]
              codes.forEach(i => {
                if (i.indexOf('unitCode') != -1) {
                  spaceScanInfo[1] = i.slice(i.indexOf('=') + 1, i.length)
                } else if (i.indexOf('hospitalCode') != -1) {
                  spaceScanInfo[2] = i.slice(i.indexOf('=') + 1, i.length)
                } else if (i.indexOf('spaceCode') != -1) {
                  spaceScanInfo[3] = i.slice(i.lastIndexOf(',') + 1, i.length)
                }
              })
              sacnInfoArr = spaceScanInfo
            } else if (pattern.test(ret.content)) {
              sacnInfoArr = ['ihsp', this.loginInfo.unitCode, this.loginInfo.hospitalCode, sacnInfoArr[2]]
            }
            const params = {
              unitCode: this.loginInfo.unitCode,
              hospitalCode: this.loginInfo.hospitalCode,
              distributionTeamId: this.loginInfo.deptId,
              planPersonCode: this.loginInfo.staffId,
              typeValue: sacnInfoArr.join(','),
              systemCode: this.systemCode,
              accomplishType: 0 // 未执行任务
            }
            sessionStorage.setItem('scanCodeData', JSON.stringify(params))
            Toast.loading({
              message: '加载中...',
              forbidClick: true,
              overlay: false,
              duration: 0
            });
            params.pageNo = 1
            params.pageSize = 2
            axios({
              method: 'post',
              url: __PATH.IPSM_URL + '/planTaskNewApiController/getPerformTask',
              data: qs.stringify(params),
              headers: {
                "Content-Type": "application/x-www-form-urlencoded",
                'Authorization': localStorage.getItem("token")
              }
            }).then(res => {
              Toast.clear()
              const { code, data, message } = res.data
              if (code == '200') {
                if (data.length > 1) {
                  sessionStorage.setItem('source', sacnInfoArr[3])
                  this.$router.push({
                    path: 'taskList',
                    query: {
                      from: 'scanCode',
                      source: sacnInfoArr[3],
                      systemNum: this.systemCode,
                      menuType: 0
                    }
                  })
                } else if (data.length == 1) {
                  // 判断是否为设备巡检点
                  this.isDevice = JSON.parse(data[0].taskPointRelease.particulars).assetsId ? JSON.parse(data[0].taskPointRelease.particulars).assetsId : false
                  // 需要定位
                  if (data[0].locationFlag == '0') {
                    const deviceMajor = JSON.parse(data[0].taskPointRelease.particulars).deviceMinor
                    const isPicture = JSON.parse(data[0].taskPointRelease.particulars).isPicture
                    const item = {
                      taskPointRelease: {
                        id: data[0].taskPointRelease.id
                      },
                      id: data[0].id,
                      systemCode: data[0].systemCode,
                      taskStartTime: data[0].taskStartTime
                    }
                    if (deviceMajor) {
                      this.getLocation(item, data[0].taskPointRelease.maintainProjectRelease, deviceMajor, isPicture)
                    } else {
                      sessionStorage.setItem('whetherLocation', '3')
                      if (data[0].taskPointRelease.maintainProjectRelease) { // 有任务书
                        let type = null
                        if (data[0].taskPointRelease.maintainProjectRelease.equipmentTypeId == '0') {
                          type = 0
                        } else if (data[0].taskPointRelease.maintainProjectRelease.equipmentTypeId == '1') {
                          type = 1
                        }
                        this.$router.push({
                          path: "/taskContent",
                          query: {
                            id: item.taskPointRelease.id,
                            type,
                            systemNum: item.systemCode,
                            taskStartTime: item.taskStartTime
                          },
                        });
                      } else { // 无任务书
                        if (isPicture == '1') {
                          this.noBookSubmit(item)
                        } else {
                          this.$router.push({
                            path: '/taskResult',
                            query: {
                              taskId: item.id,
                              taskPointReleaseId: item.taskPointRelease.id,
                              isPicture: '0', // 0:拍照，1:不拍照
                              type: '2',
                              answerMapList: ''
                            }
                          })
                        }
                      }
                    }
                  } else {
                    this.oneTask(data[0])
                  }
                } else if (data.length == 0) {
                  if (sacnInfoArr[0].toLowerCase() == 'ihsp') { // 空间
                    this.$router.push({
                      path: "/spaceDetail",
                      query: {
                        id: sacnInfoArr[3],
                        systemCode: this.systemCode
                      }
                    });
                  } else if (sacnInfoArr[0].toLowerCase() == 'iaas') { // 设备
                    this.$router.push({
                      path: "/deviceInfo",
                      query: {
                        id: sacnInfoArr[3],
                        fromInsp: 1,
                        systemCode: this.systemCode
                      }
                    })
                  } else if (sacnInfoArr[0].toLowerCase() == 'insp') { // 自定义
                    this.$router.push({
                      path: "/zdyPointDetail",
                      query: {
                        id: sacnInfoArr[3],
                        systemCode: this.systemCode
                      }
                    })
                  } else {
                    this.$toast.fail('该二维码未关联任务')
                  }
                }
              } else {
                this.$toast.fail(message || '扫码失败')
              }
            })
        },
        error: res => {
          console.log("wx扫码失败", res);
          this.$toast.fail({
              message: '扫码失败,请重新扫码',
              duration: 2500
          })
        }
      });
    },
    // 单个任务
    oneTask(taskPoint) {
      // 有任务书(需跳转)
      if (taskPoint.taskPointRelease.maintainProjectRelease) {
        let type = null
        if (taskPoint.taskPointRelease.maintainProjectRelease.equipmentTypeId == '0') {
          type = 0
        } else if (taskPoint.taskPointRelease.maintainProjectRelease.equipmentTypeId == '1') {
          type = 1
        }
        this.$router.push({
          path: "/taskContent",
          query: {
            id: taskPoint.taskPointRelease.id,
            type,
            systemNum: this.systemCode,
            taskStartTime: taskPoint.taskStartTime
          }
        })
      } else {
        const isPicture = JSON.parse(taskPoint.taskPointRelease.particulars).isPicture
        // 需要拍照
        if (isPicture == 0) {
          this.$router.push({
            path: '/taskResult',
            query: {
              taskId: taskPoint.id,
              taskPointReleaseId: taskPoint.taskPointRelease.id,
              isPicture: 0, // 0:拍照，1:不拍照
              type: 2,
              systemNum: this.systemCode,
              isDevice: this.isDevice,
              answerMapList: '',
              from: 'equipmentManagement'
            }
          })
        } else {
          const params = {
            unitCode: this.loginInfo.unitCode,
            hospitalCode: this.loginInfo.hospitalCode,
            staffName: this.loginInfo.staffName,
            taskPointReleaseId: taskPoint.taskPointRelease.id,
            taskId: taskPoint.id,
            spyScan: sessionStorage.getItem('whetherLocation') || 1, // 定位状态
            details: '',
            attachmentUrl: '',
            submitLocation: '',
            staffId: this.loginInfo.staffId,
            state: '2',
            isBookEmpty: true,
            platformFlag: 2,
            userName: this.loginInfo.staffName,
            userId: this.loginInfo.staffId,
            officeId: this.loginInfo.deptId,
            officeName: this.loginInfo.deptName
          }
          this.$api.inspectionSubmit(params).then(res => {
            // 设备巡检点
            let taskPointTypeCode = JSON.parse(taskPoint.taskPointRelease.particulars).taskPointTypeCode
            let ssmId = JSON.parse(taskPoint.taskPointRelease.particulars).ssmId
            let zdyId = JSON.parse(taskPoint.taskPointRelease.particulars).id
            const record = {
              unitCode: this.loginInfo.unitCode,
              hospitalCode: this.loginInfo.hospitalCode,
              assetsId: this.isDevice ? this.isDevice : (taskPointTypeCode === 'zdy' ? zdyId : ssmId),
              operationId: taskPoint.taskPointRelease.id,
              operationCode: this.systemCode, // 1:巡检 2:保养 3:报修
              operation: this.systemCode == '2' ? '保养' : '巡检',
              record: `${this.systemCode == '2' ? '保养' : '巡检'}单号：` + this.$route.query.taskPointReleaseId,
            }
            this.$api.saveOperateRecord(record).then(res => { })
            this.$toast.success('执行成功！')
          }).catch(err => {
            this.$toast.fail(err.data.message || '执行失败！')
          })
        }
      }
    },
    // 判断手机 - ios/andriod
    isIOS() {
      const u = navigator.userAgent;
      return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    },
    // 定位
    getLocation(taskData, maintainProjectRelease, deviceMajor, isPicture) {
      if (this.h5Mode == "apicloud") {
      if (this.isIOS()) {
        this.toast = Toast({
          type: 'loading',
          message: '获取定位中...',
          duration: 5000,
          onOpened: () => {
            this.scanBeacon(deviceMajor, maintainProjectRelease, taskData, isPicture)
          },
          onClose: async () => {
            this.ibeacon.stopRanging()
            console.log('符合的设备列表', this.ibeaconArr, '目标设备', deviceMajor)
            if ((!(this.ibeaconArr.some(i => i == deviceMajor))) || this.ibeaconArr.length == 0) {
              const reLocation = await Dialog.confirm({
                title: "提示",
                message: '定位失败，是否重新定位',
                confirmButtonColor: "#3562db",
                confirmButtonText: "重新定位",
                cancelButtonText: "放弃定位"
              })
                .then(() => {
                  return "confirm";
                })
                .catch(() => {
                  return "cancel";
                });
              if (reLocation == 'cancel') {
                return false
              } else {
                this.ibeacon = null
                this.getLocation(taskData, maintainProjectRelease, deviceMajor, isPicture)
              }
            }
          }
        })
      } else {
        var ble = api.require('ble');
        ble.openBluetooth(ret => {
          if (ret) {
            this.toast = Toast({
              type: 'loading',
              message: '获取定位中...',
              duration: 5000,
              onOpened: () => {
                this.scanBeacon(deviceMajor, maintainProjectRelease, taskData, isPicture)
              },
              onClose: async () => {
                this.ibeacon.stopRanging()
                console.log('符合的设备列表', this.ibeaconArr, '目标设备', deviceMajor)
                if ((!(this.ibeaconArr.some(i => i == deviceMajor))) || this.ibeaconArr.length == 0) {
                  const reLocation = await Dialog.confirm({
                    title: "提示",
                    message: '定位失败，是否重新定位',
                    confirmButtonColor: "#3562db",
                    confirmButtonText: "重新定位",
                    cancelButtonText: "放弃定位"
                  })
                    .then(() => {
                      return "confirm";
                    })
                    .catch(() => {
                      return "cancel";
                    });
                  if (reLocation == 'cancel') {
                    return false
                  } else {
                    this.ibeacon = null
                    this.getLocation(taskData, maintainProjectRelease, deviceMajor, isPicture)
                  }
                }
              }
            })
          } else {
            Toast({
              type: 'fail',
              message: '扫描失败，请检查蓝牙是否开启',
              duration: 1500
            })
          }
        });
      }
      } else {
        this.toast = Toast({
          type: "loading",
          message: "获取定位中...",
          duration: 5000,
          onOpened: () => {
            // this.scanBeacon(deviceMajor, scanFlag, maintainProjectRelease, taskData, isDevice, isPicture);
            parent.wx.startBeaconDiscovery({
              uuids: ["00000000-0000-0000-0000-000000000006"],
              success(res) {
                console.log("开始扫描设备");
              },
              fail(err) {
                console.log("startBeaconDiscovery_fail", err);
              }
            });
          },
          onClose: async () => {
            parent.wx.getBeacons({
              success: async res => {
                console.log("getBeacons_success", res);
                let isMatch = false;
                if (res.beacons.length > 0) {
                  let ibeaconSearchArr = res.beacons;
                  console.log("搜索到的ibeacon", ibeaconSearchArr);
                  ibeaconSearchArr.forEach(i => {
                    if (i.minor == deviceMajor) {
                      isMatch = true;
                    }
                  });
                }
                if (isMatch) {
                  console.log("beacon设备匹配成功");
                  this.toast.clear();
                  Toast({
                    type: "success",
                    message: "定位成功",
                    duration: 1500
                  });
                  if (!scanFlag) {
                    // 不扫码
                    if (maintainProjectRelease) {
                      // 有任务书
                      let type = null;
                      if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == "日常巡检") {
                        type = 0;
                      } else if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == "专业巡检") {
                        type = 1;
                      } else {
                        type = 2; // 无任务书
                      }
                      this.$router.push({
                        path: "/taskContent",
                        query: {
                          id: taskData.taskPointRelease.id,
                          type,
                          systemNum: this.taskData.systemCode,
                          taskStartTime: this.taskData.taskStartTime
                        }
                      });
                    } else {
                      // 无任务书
                      if (isPicture == "1") {
                        this.noBookSubmit(taskData, isDevice);
                      } else {
                        this.$router.push({
                          path: "/taskResult",
                          query: {
                            taskId: this.taskData.id,
                            taskPointReleaseId: taskData.taskPointRelease.id,
                            isPicture: "0", // 0:拍照，1:不拍照
                            type: "2",
                            answerMapList: ""
                          }
                        });
                      }
                    }
                  } else {
                    this.ibeaconArr = [];
                    this.APPScan(taskData.taskPointRelease.id, taskData.taskPointId, isDevice);
                  }
                } else {
                  console.log("beacon设备匹配失败");
                  const reLocation = await Dialog.confirm({
                    title: "提示",
                    message: "定位失败，是否重新定位",
                    confirmButtonColor: "#3562db",
                    confirmButtonText: "重新定位",
                    cancelButtonText: "放弃定位"
                  })
                    .then(() => {
                      return "confirm";
                    })
                    .catch(() => {
                      return "cancel";
                    });
                  if (reLocation == "cancel") {
                    return false;
                  } else {
                    this.ibeacon = null;
                    this.getLocation(taskData, maintainProjectRelease, scanFlag, deviceMajor, isDevice, isPicture);
                  }
                }
              },
              fail(err) {
                console.log("getBeacons_fail", err);
              },
              complete: () => {
                parent.wx.stopBeaconDiscovery({
                  success(res) {
                    console.log("停止扫描设备");
                  }
                });
              }
            });
          }
        });
      }
    },
    // 扫描beacon
    scanBeacon(deviceMajor, maintainProjectRelease, taskData, isPicture) {
      if (this.h5Mode == "apicloud") {
         this.ibeacon = api.require('brightBeaconScan')
      if (this.ibeacon) {
        this.ibeacon.registerApp({
          appKey: '144936ec2c66457796b43facbf263785'
        }, () => {
          this.ibeacon.startRanging({
            uuids: ['00000000-0000-0000-0000-000000000006'],
            type: 1,
            mode: 2
          }, (res, err) => {
            if (res.status) {
              if (res.eventType == "onSuccess") {
                res.result.beacons.filter(i => i.uuid == '00000000-0000-0000-0000-000000000006').forEach(i => {
                  if (this.ibeaconArr.indexOf(i.minor) == -1) {
                    this.ibeaconArr.push(i.minor)
                    if (this.ibeaconArr.some(j => j == deviceMajor)) {
                      this.ibeacon.stopRanging()
                      sessionStorage.setItem('whetherLocation', '2')
                      this.toast.clear()
                      Toast({
                        type: 'success',
                        message: '定位成功',
                        duration: 1500
                      })
                      if (maintainProjectRelease) { // 有任务书
                        let type = null
                        if (maintainProjectRelease && maintainProjectRelease.equipmentTypeId == '0') {
                          type = 0
                        } else if (maintainProjectRelease && maintainProjectRelease.equipmentTypeId == '1') {
                          type = 1
                        } else {
                          type = 2 // 无任务书
                        }
                        this.$router.push({
                          path: "/taskContent",
                          query: {
                            id: taskData.taskPointRelease.id,
                            type,
                            systemNum: taskData.systemCode,
                            taskStartTime: taskData.taskStartTime
                          },
                        });
                      } else { // 无任务书
                        if (isPicture == '1') {
                          this.noBookSubmit(taskData)
                        } else {
                          this.$router.push({
                            path: '/taskResult',
                            query: {
                              taskId: taskData.id,
                              taskPointReleaseId: taskData.taskPointRelease.id,
                              isPicture: '0', // 0:拍照，1:不拍照
                              type: '2',
                              answerMapList: ''
                            }
                          })
                        }
                      }
                    }
                  }
                })
              }
            } else {
              console.log('错误', err)
            }
          })
        })
      }
      }
    },
    noBookSubmit(taskData) {
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        staffId: this.loginInfo.staffId,
        staffName: this.loginInfo.staffName,
        sysForShort: "ipsm",
        platformFlag: 2,
        userName: this.loginInfo.staffName,
        userId: this.loginInfo.staffId,
        officeId: this.loginInfo.deptId,
        officeName: this.loginInfo.deptName,
        taskPointReleaseId: taskData.taskPointRelease.id,
        state: "2",
        taskId: taskData.id,
        spyScan: sessionStorage.getItem('whetherLocation') || 1,
        isBookEmpty: true,
      };
      Toast.loading({
        message: '正在提交...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.inspectionSubmit(params).then(res => {
        // 如果是设备需要产生操作记录
        let taskPointTypeCode = JSON.parse(taskPoint.taskPointRelease.particulars).taskPointTypeCode
        let ssmId = JSON.parse(taskPoint.taskPointRelease.particulars).ssmId
        let zdyId = JSON.parse(taskPoint.taskPointRelease.particulars).id
        const record = {
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode,
          assetsId: this.isDevice ? this.isDevice : (taskPointTypeCode === 'zdy' ? zdyId : ssmId),
          operationId: taskData.taskPointRelease.id,
          operationCode: taskData.systemCode, // 1:巡检 2:保养 3:报修
          operation: taskData.systemCode == '2' ? '保养' : '巡检',
          record: `${taskData.systemCode == '2' ? '保养' : '巡检'}单号：` + taskData.taskPointRelease.id,
        }
        this.$api.saveOperateRecord(record).then(res => {
          console.log(res)
        })
        Toast.clear()
        Toast.success({
          message: "执行成功!",
          duration: 1000
        });
      })
    },
  }
}
</script>
<style scoped lang="stylus">
.inner
  height 100%
  background-color #F2F4F9
  .topContent
    padding 0.2rem 0.32rem
    // height: 4.6rem
    .ImagesLine
      display flex
      justify-content space-between
      .imageBar
        background-color #fff
        padding 0.4rem 0
        width 2.2rem
        height 1.4rem
        text-align center
        img
          width 0.8rem
        .barText
          margin-top 0.2rem
          font-size 0.3rem
      .imageBar:nth-child(2)
        margin 0 0.2rem
  .listWarp
    background-color #fff
    height calc(90vh - 5rem)
    .listTitle
      padding 0.32rem 0.32rem
      display flex
      align-items center
      font-weight bold
      border-bottom 1px solid #E5E6EB
      span
        margin-right 0.16rem
        width 0.08rem
        height 0.32rem
        background-color #3562DB
    .listItem
      height calc(100% - 0.98rem)
      overflow-y auto
      background-color #fff
      .list
        padding 0.4rem 0.32rem
        position relative
        font-size 0.32rem
        border-bottom 1px solid #E5E6EB
        .title
          display flex
          align-items center
          justify-content space-between
          width 100%
          overflow hidden
          text-overflow ellipsis
          white-space nowrap
          .task-name
            color #1D2129
            font-weight 600
            white-space nowrap
            overflow hidden
            text-overflow ellipsis
          .typeWarp
            display flex
            align-items center
            .taskType
              display inline-block
              background-color #E6EFFC
              padding 0.06rem 0.08rem
              border-radius 0.04rem
              font-size 0.24rem
              color #3562DB
              text-align center
              font-weight normal
        .point
          margin 0.32rem 0
          display flex
          .pointItem
            width 50%
            display flex
            align-items center
            .pointTitle
              color #4E5969
              margin-right 0.48rem
              padding 0
            .count
              color #1D2129
        .time
          .timeTitle
            color #4E5969
            margin-right 0.48rem
          .timeDate
            color #1D2129
    .notList
      position relative
      height calc(100% - 1rem)
      .emptyImg
        position absolute
        height 100%
        width 50%
        left 25%
        background url('../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat
        background-size 100% auto
        .emptyText
          position absolute
          width 100%
          text-align center
          bottom 40%
  .listWarp1
    height calc(90vh - 2.6rem)
</style>
