<template>
  <div class="inner">
    <Header title="设备管理" @backFun="goBack"></Header>
    <!-- 导航栏 -->
    <van-tabs
      v-model="active"
      sticky
      offset-top="10vh"
      color="#3562DB"
      title-inactive-color="#86909C"
      title-active-color="#1D2129"
      @click="tabClick"
    >
      <van-tab
        v-for="item in tabName"
        :key="item.dictValue"
        :name="item.dictValue"
        :title="item.dictName + '(' + item.assetNum + ')'"
      >
        <van-search
          v-model="assetName"
          show-action
          placeholder="请输入关键词搜索"
          @search="onSearch"
          @cancel="onCancel"
        >
        </van-search>
        <div v-if="!noDate" class="contentList">
          <van-pull-refresh
            class="content"
            v-model="refreshing"
            @refresh="onRefresh"
          >
            <van-list
              ref="listRef"
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              :immediate-check="false"
              @load="onLoad"
            >
              <div v-for="(item, index) in listData" :key="index" class="listWrap">
                <div>
                  <img
                    class="row-img"
                    width="88"
                    height="88"
                    :src="lostImg"
                    @click="toView(lostImg)"
                  />
                </div>
                <div @click="goDetail(item)">
                  <div class="row-title">{{ item.assetName }}</div>
                  <ul>
                    <li>设备编码：{{ item.assetCode }}</li>
                    <li>系统类别：{{ item.professionalCategoryName }}</li>
                    <li>位置：{{ item.regionName }}</li>
                  </ul>
                </div>
              </div>
            </van-list>
          </van-pull-refresh>
        </div>
        <div v-else class="notList">
          <div class="emptyImg">
            <span class="emptyText">暂无数据</span>
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import { ImagePreview } from "vant";
import lostImg from "@/assets/images/equipmentManagement/lost_img.png";
export default {
  name: "deviceList",
  data() {
    return {
      loginInfo: "",
      assetName: "",
      lostImg,
      tabName: [],
      listData: [],
      active: "0",
      noDataImg: require("@/assets/images/equipmentManagement/缺省@2x.png"), //暂无数据图片
      competentValue: "",
      paginationData: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      noDate: false, //页面无数据标识
      refreshing: false,
      loading: true,
      finished: false
    };
  },
  computed: {},
  watch: {},
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.getCompetentList();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    getCompetentList() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode
      };
      this.$api.competentList(params).then(res => {
        this.tabName = res;
        this.competentValue = this.tabName[0].dictValue;
        this.onLoad();
      });
    },
    getDeviceList() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        centralizedDepartmentCode: this.competentValue,
        assetName: this.assetName
      };
      this.$api.deviceList(params).then(res => {
        if (res.assetDetailsList.length == 0 && this.listData.length == 0) {
          this.listData = [];
          this.finished = true;
          this.noDate = true;
        } else {
          this.finished = false;
          this.noDate = false;
        }
        this.paginationData.total = res.sum;
        if (this.paginationData.total != "") {
          if (
            this.paginationData.currentPage >
            Math.ceil(this.paginationData.total / this.paginationData.pageSize)
          ) {
            this.finished = true;
          }
        }
        this.loading = false;
        this.listData = [...this.listData, ...res.assetDetailsList];
      });
    },
    onRefresh() {
      this.paginationData.currentPage = 1;
      // 清空列表数据
      this.finished = false;
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.onLoad();
    },
    onLoad() {
      setTimeout(() => {
        if (this.refreshing) {
          this.listData = [];
          this.refreshing = false;
        }
        this.getDeviceList();
        this.paginationData.currentPage++;
      }, 1000);
    },
    tabClick(name) {
      this.noDate=false
      this.finished = false;
      this.loading = true;
      this.listData = [];
      this.assetName=''
      this.competentValue = name;
      this.paginationData.currentPage = 1;
      this.onLoad();
    },
    onSearch() {
      this.noDate=false
      this.finished = false;
      this.loading = true;
      this.listData = [];
      this.paginationData.currentPage = 1;
      this.onLoad();
    },
    onCancel(){
      this.assetName=''
      this.noDate=false
      this.finished = false;
      this.loading = true;
      this.listData = [];
      this.paginationData.currentPage = 1;
      this.onLoad();
    },
    goDetail(item) {
      this.$router.push({
        path: "/deviceInfo",
        query:{
          id:item.id,
          assetsId:item.assetsId
        }
      });
    },
    toView(src) {
      ImagePreview({
        images: [src],
        showIndex: false, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    }
  }
};
</script>
<style scoped lang="stylus">
.inner {
  height: 100vh;
  >>> .van-tabs {
    height: 90vh;
    .van-tabs__wrap {
      overflow: visible !important;
      overflow-x: auto !important;
      overflow-y: hidden !important;
      .van-tab {
        background-color: #fff;
        padding: 0 6px;
        .van-tab__text--ellipsis {
          overflow: visible !important;
          white-space: nowrap !important;
          background-color: #fff;
        }
      }
    }
    .van-tabs__content {
      height: calc(90vh - 44px);
      .van-tab__pane {
        height: 100%;
        .contentList {
          background-color: #fff !important;
          height: calc(100% - 54px);
          overflow: auto;
          .content {
            height: auto !important;
            background-color: #fff !important;
          }
        }
      }
    }
  }
}
.content {
  padding: 0 16px;
  .listWrap {
    background-color: #fff;
    height: 140px;
    display: flex;
    align-items: center;
    .row-title {
      font-size: 16px;
      margin-left: 16PX;
      width: 60vw;
      overflow: hidden;  //超出隐藏
      white-space: nowrap; //不折行
      text-overflow: ellipsis; //溢出显示省略号
    }
    ul {
      margin: 10px 0 0 16px;
      width: 60vw;
      li {
        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #47515f;
        margin-bottom: 5px;
        word-wrap: break-word;
        width: 100%;
        overflow: hidden;  //超出隐藏
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号
      }
    }
  }
}
::v-deep .van-search {
  background: #efeff4;
  box-shadow: 0px 1px 0px 0px #dddddd;
}
::v-deep .van-search__action {
  font-size: 0.85rem;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #2d4a74;
}
>>> .van-tab__text{
  font-size: 16px;
}
.notList {
  width:100%;
  position: relative;
  height: 70vh;
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url('../../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
