<!--
 * @Author: hedd
 * @Date: 2023-07-25 14:19:04
 * @LastEditTime: 2023-07-26 15:12:47
 * @FilePath: \ybs_h5\src\pages\equipmentManagement\deviceManagement\deviceOverview.vue
 * @Description:
-->
<template>
  <div class="inner">
    <Header title="资产管理" @backFun="goback"></Header>
    <div class="device-overview">
      <div class="box-title">资产概览</div>
      <div class="box-statistics">
        <div class="div-icon"></div>
        <div class="div-total">
          <span>资产总数</span>
          <span>{{ deviceTotal }}</span>
        </div>
      </div>
      <div
        v-show="pieData.length"
        id="chart1"
        style="width: 100%;height: 150px;"
      ></div>
      <div v-if="!pieData.length" class="executeCharts">
        <img
          height="100%"
          src="@/assets/images/equipmentManagement/缺省-图表@2x.png"
          alt=""
        />
        <span class="nullText">暂无数据</span>
      </div>
    </div>
    <div class="device-list">
      <div class="box-title">资产清单</div>
      <van-pull-refresh
        v-if="!noDate"
        class="list-content"
        v-model="refreshing"
        @refresh="onRefresh"
      >
        <van-list
          ref="listRef"
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          :immediate-check="false"
          @load="onLoad"
        >
          <div v-for="(item, index) in listData" :key="index" class="listWrap">
            <div>
              <img
                class="row-img"
                width="88"
                height="88"
                :src="lostImg"
                @click="toView(lostImg)"
              />
            </div>
            <div @click="goDetail(item)">
              <div class="row-title">{{ item.assetName }}</div>
              <ul>
                <li><span>资产编码</span> {{ item.assetCode }}</li>
                <li><span>专业类别</span> {{ item.professionalCategoryName }}</li>
                <li><span>所在区域</span> {{ item.regionName }}</li>
              </ul>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <ybsEmpty v-else imageType="searchEmpty" style="height: 100%" />
    </div>
  </div>
</template>
<script>
import { ImagePreview } from "vant";
import lostImg from "@/assets/images/equipmentManagement/lost_img.png";
export default {
  name: "deviceOverview",
  data() {
    return {
      deviceTotal: 0,
      pieData: [],
      listData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      lostImg,
      noDate: false, //页面无数据标识
      refreshing: false,
      loading: true,
      finished: false,
    };
  },
  mounted() {
    console.log(this.$route.query);
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getDeviceTypePie();
    this.onLoad();
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    getDeviceTypePie() {
      const params = {
        regionCode: this.$route.query.roomCode
      }
      this.$api.getAssetOverviewPieChart(params).then(res => {
        console.log(res.data);
        this.deviceTotal = res.sum
        this.pieData = res.list;
        if (this.pieData.length) {
          this.$nextTick(() => {
            this.getPieData(this.pieData, "chart1");
          });
        }
      });
    },
    getPieData(data, id) {
      const getchart = this.$echarts.init(document.getElementById(id));
      getchart.setOption({
        backgroundColor: "#fff",
        color: ["#FF7D00", "#3562DB", "#FFCF8B", "#A0B8F6", "#FF8C66"],
        legend: {
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemWidth: 10,
          itemHeight: 10,
          top: "center",
          left: "50%",
          formatter: name => {
            let sum = 0;
            data.forEach(item => {
              sum = sum + Number(item.value);
            });
            const count = data.find(i => i.name == name);
            return (
              name +
              "(" +
              count.value +
              "件" +
              ")" +
              "  " +
              (sum > 0 ? ((count.value / sum) * 100).toFixed(2) : 0) +
              "%"
            );
          }
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2
          },
          type: "pie",
          radius: "80%",
          center: ["20%", "50%"],
          data: data,
          hoverAnimation: false,
          labelLine: {
            normal: {
              show: false,
              lineStyle: {
                color: "#4E5969" // 设置标示线的颜色
              }
            }
          },
          label: {
            normal: {
              show: false,
              textStyle: {
                color: "#4E5969"
              }
            }
          }
        }
      });
    },
    getDeviceList() {
      const params = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        regionCode: this.$route.query.roomCode
      };
      this.$api.getElevatorAssetList(params).then(res => {
        if (res.list.length == 0 && this.listData.length == 0) {
          this.listData = [];
          this.finished = true;
          this.noDate = true;
        } else {
          this.finished = false;
          this.noDate = false;
        }
        this.paginationData.total = res.sum;
        if (this.paginationData.total != "") {
          if (
            this.paginationData.currentPage >
            Math.ceil(this.paginationData.total / this.paginationData.pageSize)
          ) {
            this.finished = true;
          }
        }
        this.loading = false;
        this.listData = [...this.listData, ...res.list];
      });
    },
    onRefresh() {
      this.paginationData.currentPage = 1;
      // 清空列表数据
      this.finished = false;
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.onLoad();
    },
    onLoad() {
      setTimeout(() => {
        if (this.refreshing) {
          this.listData = [];
          this.refreshing = false;
        }
        this.getDeviceList();
        this.paginationData.currentPage++;
      }, 1000);
    },
    goDetail(item) {
      this.$router.push({
        path: "/deviceInfo",
        query: {
          id: item.id,
          assetsId: item.assetsId
        }
      });
    },
    toView(src) {
      ImagePreview({
        images: [src],
        showIndex: false, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.inner {
  background-color: #efeff4;
  min-height: 100vh;
  .box-title {
    height: 22px;
    font-size: 16px;
    font-family: PingFang HK-Regular, PingFang HK;
    color: #1d2129;
  }
  .device-overview {
    margin-top: 1px;
    padding: 16px;
    background-color: #fff;
    .box-statistics {
      margin-top: 20px;
      width: calc(100% - 32px);
      background: #e6effc;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      .div-icon {
        margin-right: 10px;
        width: 28px;
        height: 28px;
        background: url("~@/assets/images/equipmentManagement/icon_device_total.png")
          no-repeat;
      }
      .div-total {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        text-align: center;
        span:first-child {
          font-size: 14px;
          line-height: 20px;
          font-family: PingFang HK-Regular, PingFang HK;
          color: #86909c;
        }
        span:last-child {
          font-size: 18px;
          font-family: Arial-Bold, Arial;
          font-weight: bold;
          line-height: 25px;
          color: #3562db;
        }
      }
    }
    .executeCharts {
      height: 28vh;
      text-align: center;
      position: relative;
      .nullText {
        position: absolute;
        bottom: 1rem;
        left: 43%;
      }
    }
  }
  .device-list {
    margin-top: 10px;
    padding: 16px;
    background-color: #fff;
    .list-content {
      .listWrap {
        background-color: #fff;
        height: 140px;
        display: flex;
        align-items: center;
        .row-title {
          font-size: 16px;
          margin-left: 16px;
          color: #1D2129;
          width: 60vw;
          overflow: hidden; //超出隐藏
          white-space: nowrap; //不折行
          text-overflow: ellipsis; //溢出显示省略号
        }
        ul {
          margin: 10px 0 0 16px;
          width: 60vw;
          li {
            font-size: 15px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #1D2129;
            margin-bottom: 5px;
            word-wrap: break-word;
            width: 100%;
            overflow: hidden; //超出隐藏
            white-space: nowrap; //不折行
            text-overflow: ellipsis; //溢出显示省略号
            span {
              color: #47515f;
              margin-right: 15px;
            }
          }
        }
      }
      .listWrap + .listWrap {
        border-top: 1px solid #E5E6EB;
      }
    }
  }
}
</style>
