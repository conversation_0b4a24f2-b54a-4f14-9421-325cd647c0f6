<template>
  <div class="inner">
    <Header title="设备详情" @backFun="goBack"> </Header>
    <div class="content">
      <van-tabs
        offset-top="10vh"
        sticky
        v-model="active"
        title-inactive-color="#86909C"
        title-active-color="#1D2129"
        color="#3562DB"
        animated
        line-height="3px"
        :class="$route.query.isClould && !$route.query.configType ? 'isClould' : ''"
      >
        <van-tab title="设备信息">
          <div class="listWarp">
            <van-list>
              <div v-for="(item, index) in deviceList" :key="item.id" :class="index == 16 || index == 17 || index == 19 ? 'item items' : 'item'">
                <span>{{ item.title }}</span>
                <span :class="[item.flag ? 'overFlow' : '', item.isOverdue ? 'overdue' : '']">{{ item.value }}</span>
                <div v-show="item.key === 'assetName'" style="min-width: 70px">
                  <span v-show="$route.query.configType == '0'" style="background: #f2f3f5; color: #515c6c; padding: 3px">已过期</span>
                  <span v-show="$route.query.configType == '1'" style="background: #fff7e8; color: #ff7d00; padding: 3px">即将到期</span>
                </div>
                <!-- <van-icon
                  :name="item.flag ? 'arrow' : 'arrow-down'"
                  @click="arrowClick(item)"
                /> -->
              </div>
            </van-list>
          </div>
        </van-tab>
        <van-tab title="运维记录">
          <div v-if="fromInsp" class="operationStatistics">
            <div class="inpstStatistics">
              <div class="itemStatistics">
                <span>应巡检</span>
                <span class="num" @click="toTaskList($route.query.systemCode == '3' ? '3' : '1', '', pointStatistics.equipmentCount)">
                  {{ pointStatistics.equipmentCount || "0" }}
                  <span class="unit">次</span>
                </span>
              </div>
              <div class="itemStatistics">
                <span>实际巡检</span>
                <span class="num" @click="toTaskList($route.query.systemCode == '3' ? '3' : '1', '1', pointStatistics.equipmentAccomplishCount)">
                  {{ pointStatistics.equipmentAccomplishCount || "0" }}
                  <span class="unit">次</span>
                </span>
              </div>
              <div class="itemStatistics">
                <span>应保养</span>
                <span class="num" @click="toTaskList('2', '', pointStatistics.upkeepCount)">
                  {{ pointStatistics.upkeepCount || "0" }}
                  <span class="unit">次</span>
                </span>
              </div>
              <div class="itemStatistics">
                <span>实际保养</span>
                <span class="num" @click="toTaskList('2', '1', pointStatistics.upkeepAccomplishCount)">
                  {{ pointStatistics.upkeepAccomplishCount || "0" }}
                  <span class="unit">次</span>
                </span>
              </div>
            </div>
            <div class="inpstDate">
              <span>下次{{ $route.query.systemCode == "2" ? "保养日期：" : "巡检日期：" }}</span>
              <span class="dateTime">{{ pointStatistics.taskStartTime || "暂无" }}</span>
            </div>
          </div>
          <div class="listWarp" :class="fromInsp ? 'stepsClass1' : 'stepsClass'">
            <div class="header" v-for="(item, index) in stepsList" :key="index">
              <div class="stepLine" v-if="index != stepsList.length - 1"></div>
              <div class="title-body">
                <div class="iconfont" v-if="index == 0">&#xe681;</div>
                <div class="timeicon" v-else></div>
                <span class="time">{{ item.createTimeStr }}</span>
              </div>
              <p class="value" @click="goDetail(item)">【{{ item.operation }}】{{ item.createDept }} {{ item.createName }}&nbsp;&nbsp;&nbsp;{{ item.record }}</p>
            </div>
            <p class="bottomPrompt">已经到头了~</p>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script>
import inactiveIcon from "@/assets/images/equipmentManagement/inactive-icon.png";
import activeIcon from "@/assets/images/equipmentManagement/active-icon.png";
import { Toast } from "vant";
export default {
  components: {},
  data() {
    return {
      fromInsp: this.$route.query.fromInsp,
      inactiveIcon,
      activeIcon,
      active: 0,
      id: this.$route.query.id || "",
      assetsId: this.$route.query.assetsId || "",
      deviceInfo: {
        assetName: "设备名称",
        assetCode: "设备编码",
        assetBrand: "品牌",
        assetModel: "型号",
        dateOfManufacture: "生产日期",
        assetSn: "SN码",
        regionName: "位置",
        startDate: "启用日期",
        serviceLife: "使用期限",
        useDuration: "使用时长",
        assetStatusName: "设备状态",
        produceManufacturer: "生产厂家",
        equipmentParameters: "设备参数",
        serviceArea: "服务区域",
        specialEquipmentFlag: "是否特种设备",
        assetsRemarks: "备注说明",
        centralizedDepartmentName: "归口部门",
        // assetCategoryName: "资产大类",
        // assetSubcategoryName: "资产小类",
        professionalCategoryName: "系统类别",
        systemCategoryName: "子系统类别"
      },
      deviceList: [],
      stepsList: [],
      pointStatistics: {}
    };
  },
  computed: {},
  watch: {
    active(val) {
      this.getDeviceInfo();
      if (this.fromInsp) {
        this.getPointAmountStatistics();
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.active = from.path == "/deviceList" || from.path == "/taskContent" || from.path == "/equipmentManagement" ? 0 : vm.$route.query.configType ? 0 : 1;
    });
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    if (this.$route.query.isClould) {
      this.id = this.$route.query.roomCode;
      // 解决寿命提醒跳转问题
      this.$route.query.configType ? "" : (this.fromInsp = 1);
    }
    this.getDeviceInfo();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    goBack() {
      if (this.$route.query.isClould) {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    getDeviceInfo() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.id
      };
      this.$api.deviceDetails(params).then(res => {
        let newObj = res;
        this.assetsId = res.assetsId;
        this.deviceList = this.mergeObjectsIntoArrayObjects(newObj, this.deviceInfo);
        console.log(this.deviceList);

        if (this.active == 1) {
          this.getMaintenanceList();
        }
      });
    },
    // 对象处理成数组
    mergeObjectsIntoArrayObjects(origin, template) {
      let newArr = [],
        ran_char = (len = 10, min = 26) => String.fromCharCode(Math.floor(Math.random() * len) + min);

      for (let key in template) {
        let value = origin[key];
        // 处理特种设备标志的显示
        if (key === 'specialEquipmentFlag') {
          value = this.getSpecialEquipmentText(origin[key]);
        }
        
        // 处理使用时长，添加单位并判断是否超出期限
        if (key === 'useDuration') {
          value = value ? `${value}天` : '';
          const isOverdue = this.checkIsOverdue(origin.startDate, origin.serviceLife, origin.useDuration);
          
          newArr.push({
            id: ran_char(26, 97) + ran_char(26, 65) + ran_char(10, 48),
            key,
            flag: true,
            title: template[key],
            value: value,
            isOverdue: isOverdue // 添加是否超期的标记
          });
          continue;
        }
        
        // 处理使用期限，添加年单位
        if (key === 'serviceLife') {
          value = value ? `${value}年` : '';
        }
        
        newArr.push({
          id: ran_char(26, 97) + ran_char(26, 65) + ran_char(10, 48),
          key,
          flag: true,
          title: template[key],
          value: value
        });
      }

      return newArr;
    },
    // 检查是否超出使用期限
    checkIsOverdue(startDate, serviceLife, useDuration) {
      if (!startDate || !serviceLife || !useDuration) return false;
      
      // 将使用期限（年）转换为天数
      const lifeDays = parseFloat(serviceLife) * 365;
      
      // 比较使用时长（天）与使用期限（天）
      return parseFloat(useDuration) > lifeDays;
    },
    // 特种设备标志转换方法
    getSpecialEquipmentText(flag) {
      return flag == "1" ? "是" : "否";
    },
    // 获取运维记录
    getMaintenanceList() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        assetsId: this.assetsId
      };
      this.$api.maintenanceList(params).then(res => {
        this.stepsList = res;
      });
    },
    arrowClick(item) {
      item.flag = !item.flag;
    },
    goDetail(item) {
      if (item.operationCode && (item.operationCode == "1" || item.operationCode == "2")) {
        //巡检 保养
        this.$router.push({
          path: "/completeDetail",
          query: {
            id: item.operationId || ""
          }
        });
      } else if (item.operationCode && item.operationCode == "3") {
        //报修
        this.$router.push({
          path: "/workOrderDetail",
          query: {
            detail: {
              taskId: item.operationId || ""
            }
          }
        });
      }
    },
    // 运维记录统计
    getPointAmountStatistics() {
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        pointCode: this.id,
        taskStartTime: this.$route.query.taskStartTime || "",
        systemCode: this.$route.query.systemCode
      };
      this.$api.taskPointAmountStatistics(params).then(res => {
        this.pointStatistics = res;
        Toast.clear();
      });
    },
    toTaskList(coed, type, num) {
      if (num == 0) {
        return $.toast("暂无更多", "text");
      }
      sessionStorage.setItem("isPointDetail", true);
      let queryData = {
        from: "pointDetail",
        type,
        systemCode: coed,
        menuType: 0, // 0：执行 1：查看
        pointCode: this.$route.query.id
      };
      if (this.$route.query.isClould) {
        queryData.isClould = true;
      }
      this.$router.push({
        path: "/taskList",
        query: queryData
      });
    }
  }
};
</script>
<style scoped lang="stylus">
.inner {
  background-color: #EDEEF2FF;
  min-height: 100vh;

  .content {
    height: 90vh;

    /deep/ .van-tabs {
      height: 100%;

      .van-tabs__content {
        height: calc(100% - 44px);

        .van-tab__pane-wrapper {
          overflow: auto;

          .van-tab__pane {
            height: calc(100% - 10px);
          }
        }
      }
    }

    /deep/ .isClould {
      >div:first-child {
        display: none !important;
      }

      .van-tabs__content {
        height: 100%;
      }
    }

    .listWarp {
      margin: 0.2rem 0 0 0;

      .item {
        padding: 0.2rem 0.32rem;
        display: flex;
        font-size: 16px;
        color: #333;
        background-color: #fff;
        line-height: 18px;
      }

      .item > span:nth-child(1) {
        display: inline-block;
        width: 30%;
        color: #47515F;
      }

      .item > span:nth-child(2) {
        display: inline-block;
        width: 65%;
        word-wrap: break-word;
      }

      .overFlow {
        color: #1D2129;
        // overflow: hidden;  //超出隐藏
        // white-space: nowrap; //不折行
        // text-overflow: ellipsis; //溢出显示省略号
      }

      .overdue {
        color: #F56C6C !important;
        font-weight: bold;
      }

      .items {
        margin-top: 0.1875rem;
      }
    }

    .bottomPrompt {
      margin: 0;
      padding: 0;
      font-size: 14PX;
      text-align: center;
      color: #bbb;
    }

    .operationStatistics {
      margin: 0.2rem 0 0 0;
      padding: 0.3rem 0.64rem;
      background-color: #fff;

      .inpstStatistics {
        display: flex;
        justify-content: space-between;

        .itemStatistics {
          padding: 0.2rem 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          font-size: 16px;

          .num {
            margin: 0.2rem 0;
            font-size: 18px;
            font-weight: bold;
            color: #3562db;

            .unit {
              font-size: 14px;
              color: #1d2129;
            }
          }
        }
      }

      .inpstDate {
        display: flex;
        align-items: center;

        .dateTime {
          font-size: 18px;
          font-weight: bold;
          color: #1d2129;
        }
      }
    }
  }
}

.stepsClass {
  background-color: #fff;
  height: 100%;
  overflow: auto;
}

.stepsClass1 {
  background-color: #fff;
  height: calc(100% - 132px);
  overflow: auto;
}

.header {
  margin: 0.15rem 0 0.15rem 0.1rem;
  padding: 0 0.24rem;
  background: #fff;
  position: relative;

  .iconfont {
    font-size: 0.4rem;
    color: rgb(53, 98, 219);
    margin-right: 0.1875rem;
  }

  .title-body {
    height: 0.6rem;
    display: flex;
    align-items: center;
    white-space: nowrap;

    .time {
      font-size: 0.3rem;
      color: #1D2129;
    }
  }

  .value {
    padding-left: 0.5rem;
    font-size: 15px;
    color: #4E5969;
    line-height: 0.4rem;
    word-wrap: break-word;
    overflow: hidden; // 超出隐藏
    text-overflow: ellipsis; // 溢出显示省略号
    white-space: normal; // 常规默认，会折行
    display: -webkit-box;
    -webkit-box-orient: vertical; // 子元素排列 vertical（竖排）orhorizontal（横排）
    -webkit-line-clamp: 2; /* 内容限制的行数 需要几行写几就行 */
  }
}

.stepLine {
  position: absolute;
  left: 0.42rem;
  top: 0.52rem;
  width: 1px;
  height: calc(100% - 0.6rem);
  background-color: #E5E6EB;
}

.timeicon {
  display: inline-block;
  margin-left: 0.05rem;
  width: 0.15rem;
  height: 0.15rem;
  border-radius: 50%;
  background-color: #fff;
  border: 0.06rem solid #3562DB;
  margin-right: 0.1875rem;
}
</style>
