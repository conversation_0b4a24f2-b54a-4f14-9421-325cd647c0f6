<template>
  <div class='inner'>
    <Header :title="menuType == '1' ? '超时任务' :
      systemNum == '2' ? '保养任务' : '巡检任务'" @backFun="goBack">
    </Header>
    <div v-if="$route.query.from == 'menu' || $route.query.from == 'todoItem'" class="inner-content">
      <div v-if="menuType != '1' ">
        <div v-if="systemNum!=='3'">
          <van-tabs v-model="active" offset-top="10vh" sticky title-inactive-color="#86909C"
            title-active-color="#1D2129" color="#3562DB" @click="changeActive">
            <van-tab title="任务分类"></van-tab>
            <van-tab title="设备类别"></van-tab>
            <div class="planTypeWrap" :style="planTypeList.length == 1 ? {'justify-content': 'center'} :
        planTypeList.length == 2 ? {'justify-content': 'space-around'} :
        planTypeList.length == 3 ? {'justify-content': 'space-between'} : ''">
              <div class="tabContent">
                <div v-for="(item, index) in planTypeList" :key="index" class="typeBar"
                  :class="typeActive == index ? 'typeActive' : ''"
                  :style="planTypeList.length > 3 ? {'flex-shrink': '0', 'margin-right': '0.20rem'} : ''"
                  @click="changeType(index)">
                  <van-badge v-if="item.sum > 0" :content="item.sum">
                    <div>{{ item.planTypeName }}</div>
                  </van-badge>
                  <div v-else>
                    {{ item.planTypeName.length > 7 ? (item.planTypeName.substring(0, 7) + '...') : item.planTypeName }}
                  </div>
                </div>
              </div>
              <div class="more">
                <van-icon name="wap-nav" size="0.5rem" @click="classificationShow=true" />
              </div>
            </div>
          </van-tabs>
        </div>
        <div v-else>
          <div class="planTypeWrap" :style="activeOption.length == 1 ? {'justify-content': 'center'} :
              activeOption.length == 2 ? {'justify-content': 'space-around'} :
              activeOption.length == 3 ? {'justify-content': 'space-between'} : ''">
            <div class="tabContent">
              <div v-for="(item, index) in activeOption" :key="index" class="typeBar"
                :class="typeActive == index ? 'typeActive' : ''"
                :style="activeOption.length > 3 ? {'flex-shrink': '0', 'margin-right': '0.20rem'} : ''"
                @click="changeType(index)">
                <van-badge v-if=" sumOption[index] > 0" :content="sumOption[index]">
                  <div>{{ item }}</div>
                </van-badge>
                <div v-else>
                  {{ item&&item.length > 7 ? (item.substring(0, 7) + '...') : item }}
                </div>
              </div>
            </div>
            <div class="more">
              <van-icon name="wap-nav" size="0.5rem" @click="classificationShow=true" />
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <template v-if="systemNum == '3'">
           <div class="planTypeWrap" :style="activeOption.length == 1 ? {'justify-content': 'center'} :
              activeOption.length == 2 ? {'justify-content': 'space-around'} :
              activeOption.length == 3 ? {'justify-content': 'space-between'} : ''">
            <div class="tabContent">
              <div v-for="(item, index) in activeOption" :key="index" class="typeBar"
                :class="typeActive == index ? 'typeActive' : ''"
                :style="activeOption.length > 3 ? {'flex-shrink': '0', 'margin-right': '0.20rem'} : ''"
                @click="changeType(index)">
                <van-badge v-if=" sumOption[index] > 0" :content="sumOption[index]">
                  <div>{{ item }}</div>
                </van-badge>
                <div v-else>
                  {{ item&&item.length > 7 ? (item.substring(0, 7) + '...') : item }}
                </div>
              </div>
            </div>
            <div class="more">
              <van-icon name="wap-nav" size="0.5rem" @click="classificationShow=true" />
            </div>
          </div>
        </template>
        <template v-else>
          <van-tabs v-model="active" offset-top="10vh" sticky title-inactive-color="#86909C" title-active-color="#1D2129"
            color="#3562DB" @click="changeActive">
          <van-tab title="巡检任务"></van-tab>
          <van-tab title="保养任务"></van-tab>
        </van-tabs>
        </template>
      </div>
      <div :class="systemNum != 3 ? 'listWarp2' : 'listWarp1'" v-if="listData.length > 0">
        <van-pull-refresh v-model="isLoading" class="listItem" @refresh="onRefresh">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <div v-for="(i, ind) in listData" :key="ind" class="list" @click="goTaskDetail(i)">
              <div class="title">
                <span class="task-name">{{ i.taskName }}</span>
                <span class="typeWarp">
                  <span class="taskType">{{ typeOptions.find(j => i.cycleType == j.cycleType).label }}</span>
                  <van-icon name="arrow" />
                </span>
              </div>
              <div class="point">
                <div class="pointItem">
                  <span class="pointTitle">{{ i.systemCode == '2' ? '应保养点数' : '应巡点数' }}</span>
                  <span class="count">{{ i.totalCount }}</span>
                </div>
                <div class="pointItem">
                  <span class="pointTitle">{{ i.systemCode == '2' ? '已保养点数' : '已巡点数' }}</span>
                  <span class="count">{{ i.hasCount }}</span>
                </div>
              </div>
              <div class="time">
                <span class="timeTitle">{{ i.systemCode == '2' ? '应保养日期' : '应巡日期' }}</span>
                <span
                  class="timeDate">{{ i.cycleType == 6 ? i.taskStartTime : moment(i.taskStartTime).format('YYYY-MM-DD') }}</span>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
    <!-- 纯列表页面 -->
    <div class="scanDataWarp" v-else>
      <van-pull-refresh v-if="listData.length > 0" v-model="isLoading" class="listItem" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div v-for="(i, ind) in listData" :key="ind" class="list" @click="goTaskDetail(i)">
            <div class="title">
              <span class="task-name">{{ i.taskName }}</span>
              <span class="typeWarp">
                <span class="taskType">{{ typeOptions.find(j => i.cycleType == j.cycleType).label }}</span>
                <van-icon name="arrow" />
              </span>
            </div>
            <div class="point">
              <div class="pointItem">
                <span class="pointTitle">应巡点数</span>
                <span class="count">{{ i.totalCount }}</span>
              </div>
              <div class="pointItem">
                <span class="pointTitle">已巡点数</span>
                <span class="count">{{ i.hasCount }}</span>
              </div>
            </div>
            <div class="time">
              <span class="timeTitle">应巡日期</span>
              <span
                class="timeDate">{{ i.cycleType == 6 ? i.taskStartTime : moment(i.taskStartTime).format('YYYY-MM-DD') }}</span>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div v-else class="notList">
        <div class="emptyImg">
          <span class="emptyText">暂无数据</span>
        </div>
      </div>
    </div>
    <van-popup v-model="classificationShow" closeable position="bottom" :style="{ height: '50%' }">
      <div class="popContent">
        <div class="popTitle">任务分类</div>
        <div class="popBox" v-if="systemNum!=='3'" style="display:flex">
          <div v-for="(item, index) in planTypeList" :key="index" class="typeBarNew"
            :class="typeActive == index ? 'typeNewActive' : ''"
            :style="planTypeList.length > 3 ? {'flex-shrink': '0', 'margin-right': '0.20rem'} : ''"
            @click="changeType(index)">
            <van-badge v-if="item.sum > 0" :content="item.sum">
              <div>{{ item.planTypeName }}</div>
            </van-badge>
            <div v-else>
              {{ item.planTypeName&&item.planTypeName.length > 7 ? (item.planTypeName.substring(0, 7) + '...') : item.planTypeName }}
            </div>
          </div>
        </div>
        <div class="popBox" v-else>
          <div style="display:flex; flex-wrap:wrap;width:100%">
            <div v-for="(item, index) in activeOption" :key="index" class="typeBarNew"
              :class="typeActive == index ? 'typeNewActive' : ''"
              :style="activeOption.length > 3 ? {'flex-shrink': '0', 'margin-right': '0.20rem'} : ''"
              @click="changeType(index)">
              <van-badge v-if=" sumOption[index] > 0" :content="sumOption[index]">
                <div>{{ item }}</div>
              </van-badge>
              <div v-else>
                {{ item&&item.length > 7 ? (item.substring(0, 7) + '...') : item }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import moment from "moment"
import YBS from "@/assets/utils/utils.js";
import { Toast } from "vant";
import { Dialog } from 'vant';
import axios from 'axios'
import qs from "qs";
import { mapState } from "vuex";
export default {
  components: {},
  computed: {
    ...mapState(["h5Mode"])
  },
  data() {
    return {
      moment,
      loading: false,
      finished: false,
      isLoading: false,
      active: Number(sessionStorage.getItem('active')) || 0,
      typeActive: sessionStorage.getItem('typeActive') || 0,
      loginInfo: {},
      activeOption: ['任务分类', '设备类别'],
      planTypeList: [],
      classificationShow: false,
      // 周期类型
      typeOptions: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      listData: [],
      pageParmes: {
        pageNo: 1,
        pageSize: 10
      },
      systemNum: this.$route.query.systemNum || '0', // 判断是巡检（1）还是保养（2）或者是综合巡检（3）
      menuType: this.$route.query.menuType || '0', // 0：巡检执行 1：巡检查看
      sumOption: [],
      from: this.$route.query.from,
      roomCode: '',
      isDevice: false,
      ibeaconArr: []
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    if (this.$route.query.isClould && this.from != 'pointDetail') {
      this.from = 'space'
      this.roomCode = this.$route.query.roomCode
      // 设备扫码跳转
      if (this.$route.query.systemNum) {
        this.systemNum = this.$route.query.systemNum
      }
    }
    if (this.from == 'scanCode') {
      this.getScanList()
    } else if (this.from == 'space') {
      this.getListData()
    } else if (this.from == 'pointDetail') {
      this.getFromPointList()
    } else {
      // 非综合巡检
      if (this.systemNum != '3') {
        // 超时任务
        if (this.$route.query.menuType == '1') {
          this.activeOption = ['巡检任务', '保养任务']
          this.getListData()
        } else {
          if (this.active == 0) {
            this.getPlanType()
          } else {
            this.getDeviceType()
          }
        }
      } else {
        this.getMenuAuth()
      }
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    goBack() {
      sessionStorage.removeItem('active')
      sessionStorage.removeItem('typeActive')
      if (this.$route.query.isClould || this.$route.query.from == 'todoItem') {
        if (this.$route.query.from == 'pointDetail') {
          this.$router.go(-1)
        } else {
          this.$YBS.apiCloudCloseFrame()
        }
      } else {
        this.$router.go(-1)
      }
    },
    changeActive(name, title) {
      this.typeActive = 0
      sessionStorage.setItem('active', name)
      sessionStorage.setItem('typeActive', this.typeActive)
      this.pageParmes.pageNo = 1
      this.listData = []
      // 非综合巡检
      if (this.systemNum != '3') {
        if (name == 0) {
          if (this.$route.query.menuType == '1') {
            this.getListData()
          } else {
            this.getPlanType()
          }
        } else {
          if (this.$route.query.menuType == '1') {
            this.getListData()
          } else {
            this.getDeviceType()
          }
        }
      } else {
        this.getListData()
      }
    },
    // 任务分类
    getPlanType() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        distributionTeamId: this.loginInfo.deptId,
        planPersonCode: this.loginInfo.staffId,
        systemIdentificationClassification: this.systemNum, // 0设备+保养 1设备 2保养 3综合巡检,
        accomplishType: this.menuType == '0' ? 0 : 2, // 0:当前任务 1：已完成 2：超时任务
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.taskType(params).then(res => {
        let total = res.reduce((sum, current) => sum + current.sum, 0);
        this.planTypeList = res
        this.planTypeList.splice(0, 0, {
          planTypeId: '',
          planTypeName: '全部',
          sum: total
        });
        this.$nextTick(() => {
          const wrapDom = document.querySelectorAll('.tabContent .typeBar')
          const scrollDom = document.querySelector('.tabContent')
          this.setScrollToCenter(scrollDom, wrapDom[this.typeActive])
        })
        this.getListData()
      })
    },
    // 滚动到菜单位置
    setScrollToCenter(scrollDom, targetDom) {
      if (!scrollDom || !targetDom) return false
      //如果是浏览器body的滚动条
      if ([window, document, document.documentElement].includes(scrollDom)) {
        scrollDom = document.documentElement
      }
      const { offsetLeft, offsetWidth } = targetDom
      const { clientWidth } = scrollDom
      const targetDistance = offsetLeft
      const scrollClient = clientWidth
      const targetOffset = offsetWidth
      const val = targetDistance - scrollClient / 2 + targetOffset / 2
      const config = { behavior: 'smooth' }
      config.left = val
      scrollDom.scrollTo(config)
    },
    // 设备分类
    getDeviceType() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        distributionTeamId: this.loginInfo.deptId,
        planPersonCode: this.loginInfo.staffId,
        systemCode: this.systemNum,//1巡检 2保养 3综合巡检,
        accomplishType: this.menuType == '0' ? 0 : 2  // 0:当前任务 1：已完成 2：超时任务
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.deviceType(params).then(res => {
        this.planTypeList = res
        let total = res.reduce((sum, current) => sum + current.sum, 0);
        this.planTypeList = res
        this.planTypeList.splice(0, 0, {
          planTypeId: '',
          planTypeName: '全部',
          sum: total
        });
        this.getListData()
      })
    },
    // 综合巡检分类数量
    getInspectionType() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        distributionTeamId: this.loginInfo.deptId,
        planPersonCode: this.loginInfo.staffId,
        systemCode: this.systemNum,//1巡检 2保养 3综合巡检,
        accomplishType: this.menuType == '0' ? 0 : 2  // 0:当前任务 1：已完成 2：超时任务
      }
      this.$api.inspectionType(params).then(res => {
        if (res.length && this.planTypeList.length) {
          this.planTypeList.map((i, index) => {
            res.forEach(j => {
              if (i == j.planTypeId) {
                this.sumOption[index] = j.sum
              }
            })
          })
          const total = res.map(i => i.sum)
          this.sumOption[0] = eval(total.join('+'))
        } else {
          this.planTypeList.forEach((i, index) => {
            this.sumOption[index] = 0
          })
        }
        this.active = Number(sessionStorage.getItem('active')) || 0
        this.getListData()
      })
    },
    // 扫码列表
    getScanList() {
      const params = JSON.parse(sessionStorage.getItem('scanCodeData'))
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.inspectionScanCode(Object.assign(params, this.pageParmes)).then(res => {
        this.listData = this.listData.concat(res)
        Toast.clear()
        this.isLoading = false
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
      })
    },
    onLoad() {
      this.pageParmes.pageNo++
      this.finished = false
      this.loading = true
      if (this.from == 'scanCode') {
        this.getScanList()
      } else if (this.from == 'pointDetail') {
        this.getFromPointList()
      } else {
        this.getListData()
      }
    },
    onRefresh() {
      this.pageParmes.pageNo = 1
      this.finished = false
      this.loading = true
      this.listData = []
      if (this.from == 'scanCode') {
        this.getScanList()
      } else if (this.from == 'pointDetail') {
        this.getFromPointList()
      } else {
        if (this.systemNum != '3') {
          if (this.active == 0) {
            this.getPlanType()
          } else {
            this.getDeviceType()
          }
        } else {
          this.getInspectionType()
        }
      }
    },
    getListData() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        distributionTeamId: this.loginInfo.deptId,
        planPersonCode: this.loginInfo.staffId,
        accomplishType: this.menuType == '0' ? 0 : 2, // 0 待巡检，1 已巡检，2已超期
        systemCode: this.menuType != '0' && this.systemNum != '3' ? this.active + 1 : this.systemNum, // 0:设备+保养 1：设备 2： 保养 3:综合
        pageNo: this.pageParmes.pageNo,
        pageSize: this.pageParmes.pageSize,
        pointCode: this.roomCode
      }
      // 空间扫码
      if (this.from == 'space') {
        if (!this.$route.query.systemNum) {
          params.systemCode = ''
        } else {
          params.systemCode = this.systemNum
        }
      }
      // 超时任务不传planTypeId
      if (this.systemNum == '3') {
        params.planTypeId = this.planTypeList[this.typeActive]
      } else {
        if (this.menuType == '0') {
          params.planTypeId = this.planTypeList[this.typeActive] ? this.planTypeList[this.typeActive].planTypeId : ''
        }
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.todayTaskList(params).then(res => {
        this.listData = this.listData.concat(res)
        Toast.clear()
        this.isLoading = false
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
      })
    },
    changeType(index) {
      this.typeActive = index
      sessionStorage.setItem('typeActive', this.typeActive)
      this.pageParmes.pageNo = 1
      this.listData = []
      this.classificationShow = false
      if (this.from == 'scanCode') {
        this.getScanList()
      } else {
        this.getListData()
      }
      this.$nextTick(() => {
        const wrapDom = document.querySelectorAll('.tabContent .typeBar')
        const scrollDom = document.querySelector('.tabContent')
        this.setScrollToCenter(scrollDom, wrapDom[this.typeActive])
      })
    },
    goTaskDetail(row) {
      if (this.from == 'scanCode') {
        this.ibeaconArr = []
        sessionStorage.removeItem('whetherLocation')
        console.log(row)
        // 巡检点数据
        const pointData = row.taskPointRelease
        // 校验任务是否可执行
        this.taskExcute(row).then(async res => {
          const { code, data, message } = await res.data
          if (code == '200') {
            let dialogConfirm = "";
            // 非顺序执行任务
            if (pointData.executeOrder != "start" && row.sortFlag == '0') {
              dialogConfirm = await Dialog
                .confirm({
                  title: "提示",
                  message:
                    "该任务需要按顺序执行，请返回上一任务点执行工作。如果继续执行请点击继续执行。",
                  confirmButtonColor: "#3562db",
                  confirmButtonText: "继续执行",
                  cancelButtonText: "忽略",
                })
                .then(() => {
                  return "confirm";
                })
                .catch(() => {
                  return "cancel";
                });
            }
            if (dialogConfirm == "cancel") return;
            const item = {
              taskPointRelease: {
                id: pointData.id,
                particulars: pointData.particulars
              },
              id: row.id,
              systemCode: row.systemCode,
              taskStartTime: row.taskStartTime
            }
            // 是否必须拍照
            const isPicture = JSON.parse(pointData.particulars).isPicture
            // 判断是否为设备巡检点
            const isDevice = JSON.parse(pointData.particulars).assetsId ? JSON.parse(pointData.particulars).assetsId : false
            // 是否定位
            if (row.locationFlag == "0") {
              if (pointData.locationPointReleaseList && pointData.locationPointReleaseList.length) {
                if (pointData.locationPointReleaseList[0].locationPointType == '1') {
                  this.getLocation(item, pointData.maintainProjectRelease, pointData.locationPointReleaseList[0].deviceMinor, isDevice, isPicture)
                } else {
                  sessionStorage.setItem('whetherLocation', '3')
                  if (row.maintainProjectRelease) { // 有任务书
                    let type = null
                    if (row.maintainProjectRelease.equipmentTypeName == '0') {
                      type = 0
                    } else if (row.maintainProjectRelease.equipmentTypeName == '1') {
                      type = 1
                    }
                    this.$router.push({
                      path: "/taskContent",
                      query: {
                        id: row.id,
                        type,
                        systemNum: taskData.systemCode,
                        taskStartTime: taskData.taskStartTime
                      },
                    });
                  } else { // 无任务书
                    if (isPicture == '1') {
                      this.noBookSubmit(item, isDevice)
                    } else {
                      this.$router.push({
                        path: '/taskResult',
                        query: {
                          taskId: taskData.id,
                          taskPointReleaseId: row.id,
                          isPicture: '0', // 0:拍照，1:不拍照
                          type: '2',
                          answerMapList: ''
                        }
                      })
                    }
                  }
                }
              } else {
                Toast({
                  type: 'text',
                  message: '未配置定位点',
                  duration: 1500,
                  onClose: () => {
                    sessionStorage.setItem('whetherLocation', '3')
                    if (row.maintainProjectRelease) { // 有任务书
                      let type = null
                      if (row.maintainProjectRelease.equipmentTypeName == '0') {
                        type = 0
                      } else if (row.maintainProjectRelease.equipmentTypeName == '1') {
                        type = 1
                      }
                      this.$router.push({
                        path: "/taskContent",
                        query: {
                          id: row.id,
                          type,
                          systemNum: taskData.systemCode,
                          taskStartTime: taskData.taskStartTime
                        },
                      });
                    } else { // 无任务书
                      if (isPicture == '1') {
                        this.noBookSubmit(item, isDevice)
                      } else {
                        this.$router.push({
                          path: '/taskResult',
                          query: {
                            taskId: taskData.id,
                            taskPointReleaseId: row.id,
                            isPicture: '0', // 0:拍照，1:不拍照
                            type: '2',
                            answerMapList: ''
                          }
                        })
                      }
                    }
                  }
                })
              }
            } else {
              sessionStorage.setItem('whetherLocation', '1')
              if (pointData.maintainProjectRelease) { // 有任务书
                let type = null
                if (pointData.maintainProjectRelease.equipmentTypeId == '0') {
                  type = 0
                } else if (pointData.maintainProjectRelease.equipmentTypeId == '1') {
                  type = 1
                } else {
                  type = 2 // 无任务书
                }
                this.$router.push({
                  path: "/taskContent",
                  query: {
                    id: item.taskPointRelease.id,
                    type,
                    systemNum: row.systemCode,
                    taskStartTime: row.taskStartTime
                  },
                });
              } else { // 无任务书
                if (isPicture == '1') {
                  this.noBookSubmit(item, isDevice)
                } else {
                  this.$router.push({
                    path: '/taskResult',
                    query: {
                      taskId: row.id,
                      taskPointReleaseId: item.taskPointRelease.id,
                      isPicture: isPicture, // 0:拍照，1:不拍照
                      type: '2',
                      answerMapList: ''
                    }
                  })
                }
              }
            }
          } else {
            $.toast(message, 'text')
          }
        })
      } else {
        let queryData = {
          id: row.id,
          menuType: this.menuType // 0：巡检执行 1：巡检查看
        }
        if (this.$route.query.isClould) {
          queryData.isClould = true
        }
        this.$router.push({
          path: '/taskDetail',
          query: queryData
        })
      }
    },
    // 判断手机 - ios/andriod
    isIOS() {
      const u = navigator.userAgent;
      return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    },
    // 定位
    getLocation(taskData, maintainProjectRelease, deviceMajor, isDevice, isPicture) {
      if (this.h5Mode == "apicloud") {
        if (this.isIOS()) {
        this.toast = Toast({
          type: 'loading',
          message: '获取定位中...',
          duration: 5000,
          onOpened: () => {
            this.scanBeacon(deviceMajor, maintainProjectRelease, taskData, isDevice, isPicture)
          },
          onClose: async () => {
            this.ibeacon.stopRanging()
            console.log('符合的设备列表', this.ibeaconArr, '目标设备', deviceMajor)
            if ((!(this.ibeaconArr.some(i => i == deviceMajor))) || this.ibeaconArr.length == 0) {
              const reLocation = await Dialog.confirm({
                title: "提示",
                message: '定位失败，是否重新定位',
                confirmButtonColor: "#3562db",
                confirmButtonText: "重新定位",
                cancelButtonText: "放弃定位"
              })
                .then(() => {
                  return "confirm";
                })
                .catch(() => {
                  return "cancel";
                });
              if (reLocation == 'cancel') {
                return false
              } else {
                this.ibeacon = null
                this.getLocation(taskData, maintainProjectRelease, deviceMajor, isDevice, isPicture)
              }
            }
          }
        })
      } else {
        var ble = api.require('ble');
        ble.openBluetooth(ret => {
          if (ret) {
            this.toast = Toast({
              type: 'loading',
              message: '获取定位中...',
              duration: 5000,
              onOpened: () => {
                this.scanBeacon(deviceMajor, maintainProjectRelease, taskData, isDevice, isPicture)
              },
              onClose: async () => {
                this.ibeacon.stopRanging()
                console.log('符合的设备列表', this.ibeaconArr, '目标设备', deviceMajor)
                if ((!(this.ibeaconArr.some(i => i == deviceMajor))) || this.ibeaconArr.length == 0) {
                  const reLocation = await Dialog.confirm({
                    title: "提示",
                    message: '定位失败，是否重新定位',
                    confirmButtonColor: "#3562db",
                    confirmButtonText: "重新定位",
                    cancelButtonText: "放弃定位"
                  })
                    .then(() => {
                      return "confirm";
                    })
                    .catch(() => {
                      return "cancel";
                    });
                  if (reLocation == 'cancel') {
                    return false
                  } else {
                    this.ibeacon = null
                    this.getLocation(taskData, maintainProjectRelease, deviceMajor, isDevice, isPicture)
                  }
                }
              }
            })
          } else {
            Toast({
              type: 'fail',
              message: '扫描失败，请检查蓝牙是否开启',
              duration: 1500
            })
          }
        });
      }
      }else {
          this.toast = Toast({
          type: "loading",
          message: "获取定位中...",
          duration: 5000,
          onOpened: () => {
            // this.scanBeacon(deviceMajor, scanFlag, maintainProjectRelease, taskData, isDevice, isPicture);
            parent.wx.startBeaconDiscovery({
              uuids: ["00000000-0000-0000-0000-000000000006"],
              success(res) {
                console.log("开始扫描设备");
              },
              fail(err) {
                console.log("startBeaconDiscovery_fail", err);
              }
            });
          },
          onClose: async () => {
            parent.wx.getBeacons({
              success: async res => {
                console.log("getBeacons_success", res);
                let isMatch = false;
                if (res.beacons.length > 0) {
                  let ibeaconSearchArr = res.beacons;
                  console.log("搜索到的ibeacon", ibeaconSearchArr);
                  ibeaconSearchArr.forEach(i => {
                    if (i.minor == deviceMajor) {
                      isMatch = true;
                    }
                  });
                }
                if (isMatch) {
                  console.log("beacon设备匹配成功");
                  this.toast.clear();
                  Toast({
                    type: "success",
                    message: "定位成功",
                    duration: 1500
                  });
                  if (!scanFlag) {
                    // 不扫码
                    if (maintainProjectRelease) {
                      // 有任务书
                      let type = null;
                      if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == "日常巡检") {
                        type = 0;
                      } else if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == "专业巡检") {
                        type = 1;
                      } else {
                        type = 2; // 无任务书
                      }
                      this.$router.push({
                        path: "/taskContent",
                        query: {
                          id: taskData.taskPointRelease.id,
                          type,
                          systemNum: this.taskData.systemCode,
                          taskStartTime: this.taskData.taskStartTime
                        }
                      });
                    } else {
                      // 无任务书
                      if (isPicture == "1") {
                        this.noBookSubmit(taskData, isDevice);
                      } else {
                        this.$router.push({
                          path: "/taskResult",
                          query: {
                            taskId: this.taskData.id,
                            taskPointReleaseId: taskData.taskPointRelease.id,
                            isPicture: "0", // 0:拍照，1:不拍照
                            type: "2",
                            answerMapList: ""
                          }
                        });
                      }
                    }
                  } else {
                    this.ibeaconArr = [];
                    this.APPScan(taskData.taskPointRelease.id, taskData.taskPointId, isDevice);
                  }
                } else {
                  console.log("beacon设备匹配失败");
                  const reLocation = await Dialog.confirm({
                    title: "提示",
                    message: "定位失败，是否重新定位",
                    confirmButtonColor: "#3562db",
                    confirmButtonText: "重新定位",
                    cancelButtonText: "放弃定位"
                  })
                    .then(() => {
                      return "confirm";
                    })
                    .catch(() => {
                      return "cancel";
                    });
                  if (reLocation == "cancel") {
                    return false;
                  } else {
                    this.ibeacon = null;
                    this.getLocation(taskData, maintainProjectRelease, scanFlag, deviceMajor, isDevice, isPicture);
                  }
                }
              },
              fail(err) {
                console.log("getBeacons_fail", err);
              },
              complete: () => {
                parent.wx.stopBeaconDiscovery({
                  success(res) {
                    console.log("停止扫描设备");
                  }
                });
              }
            });
          }
        });
      }
    },
    // 扫描beacon
    scanBeacon(deviceMajor, maintainProjectRelease, taskData, isDevice, isPicture) {
      if (this.h5Mode == "apicloud") {
          this.ibeacon = api.require('brightBeaconScan')
      if (this.ibeacon) {
        this.ibeacon.registerApp({
          appKey: '144936ec2c66457796b43facbf263785'
        }, () => {
          this.ibeacon.startRanging({
            uuids: ['00000000-0000-0000-0000-000000000006'],
            type: 1,
            mode: 2
          }, (res, err) => {
            if (res.status) {
              if (res.eventType == "onSuccess") {
                res.result.beacons.filter(i => i.uuid == '00000000-0000-0000-0000-000000000006').forEach(i => {
                  if (this.ibeaconArr.indexOf(i.minor) == -1) {
                    this.ibeaconArr.push(i.minor)
                    if (this.ibeaconArr.some(j => j == deviceMajor)) {
                      this.ibeacon.stopRanging()
                      sessionStorage.setItem('whetherLocation', '2')
                      this.toast.clear()
                      Toast({
                        type: 'success',
                        message: '定位成功',
                        duration: 1500
                      })
                      if (maintainProjectRelease) { // 有任务书
                        let type = null
                        if (maintainProjectRelease && maintainProjectRelease.equipmentTypeId == '0') {
                          type = 0
                        } else if (maintainProjectRelease && maintainProjectRelease.equipmentTypeId == '1') {
                          type = 1
                        } else {
                          type = 2 // 无任务书
                        }
                        this.$router.push({
                          path: "/taskContent",
                          query: {
                            id: taskData.taskPointRelease.id,
                            type,
                            systemNum: taskData.systemCode,
                            taskStartTime: taskData.taskStartTime
                          },
                        });
                      } else { // 无任务书
                        if (isPicture == '1') {
                          this.noBookSubmit(taskData, isDevice)
                        } else {
                          this.$router.push({
                            path: '/taskResult',
                            query: {
                              taskId: taskData.id,
                              taskPointReleaseId: taskData.taskPointRelease.id,
                              isPicture: '0', // 0:拍照，1:不拍照
                              type: '2',
                              answerMapList: ''
                            }
                          })
                        }
                      }
                    }
                  }
                })
              }
            } else {
              console.log('错误', err)
            }
          })
        })
      }
      }
    },
    // 校验任务是否可执行
    async taskExcute(row) {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        taskId: row.id,
        typeValue: '1'
      }
      const isExcute = await axios({
        method: 'post',
        url: __PATH.IPSM_URL + '/planTaskNewApiController/executePlanTaskByTaskId',
        data: qs.stringify(params),
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          'Authorization': localStorage.getItem("token")
        }
      })
      return isExcute
    },
    // 无任务书提交
    noBookSubmit(taskData, isDevice) {
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        staffId: this.loginInfo.staffId,
        staffName: this.loginInfo.staffName,
        sysForShort: "ipsm",
        platformFlag: 2,
        userName: this.loginInfo.staffName,
        userId: this.loginInfo.staffId,
        officeId: this.loginInfo.deptId,
        officeName: this.loginInfo.deptName,
        taskPointReleaseId: taskData.taskPointRelease.id,
        state: "2",
        taskId: taskData.id,
        spyScan: sessionStorage.getItem('whetherLocation') || 1,
        isBookEmpty: true,
      };
      Toast.loading({
        message: '正在提交...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.inspectionSubmit(params).then(res => {
        let that = this;
        // 如果是设备需要产生操作记录
        let taskPointTypeCode = JSON.parse(taskData.taskPointRelease.particulars).taskPointTypeCode
        let ssmId = JSON.parse(taskData.taskPointRelease.particulars).ssmId
        let zdyId = JSON.parse(taskData.taskPointRelease.particulars).id
        const record = {
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode,
          assetsId: this.isDevice ? this.isDevice : (taskPointTypeCode === 'zdy' ? zdyId : ssmId),
          operationId: taskData.taskPointRelease.id,
          operationCode: taskData.systemCode, // 1:巡检 2:保养 3:报修
          operation: taskData.systemCode == '2' ? '保养' : '巡检',
          record: `${taskData.systemCode == '2' ? '保养' : '巡检'}单号：` + taskData.taskPointRelease.id,
        }
        this.$api.saveOperateRecord(record).then(res => {
          console.log(res)
        })
        Toast.clear()
        Toast.success({
          message: "执行成功!",
          duration: 1000
        });
        this.listData = []
        this.pageParmes.pageNo = 1
        that.getScanList()
      })
    },
    // 获取菜单权限
    getMenuAuth() {
      const params = {
        state: '0',
        userId: this.loginInfo.id
      }
      this.$api.getMenuAuth(params).then(res => {
        const authMenu = res.find(i => i.menuId == 1000).children.find(k => k.menuId == 1004).children.find(j => j.menuId == 284).children
        this.activeOption = authMenu.map(i => i.menuName)
        this.activeOption.splice(0, 0, '全部');
        this.planTypeList = authMenu.map(i => i.pathUrl.slice(i.pathUrl.indexOf(':') + 1, i.pathUrl.length))
        this.planTypeList.splice(0, 0, '');
        for (let i = 0; i < this.activeOption.length; i++) {
          this.sumOption.push(0)
        }
        this.getInspectionType()
      })
    },
    // 根据点id查任务
    getFromPointList() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        accomplishType: this.$route.query.type,
        systemCode: this.$route.query.systemCode,
        pointCode: this.$route.query.pointCode,
        pageNo: this.pageParmes.pageNo,
        pageSize: this.pageParmes.pageSize
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.fromPointList(params).then(res => {
        Toast.clear()
        this.isLoading = false
        if (res.length == 10) {
          this.finished = false
        } else {
          this.finished = true
        }
        this.loading = false
        this.listData = this.listData.concat(res)
      })
    }
  }
}
</script>
<style scoped lang="stylus">
.inner
  height 100%
  background-color #F2F4F9
  /deep/ .van-tabs
    height calc(100% - 10vh)
    .van-tabs__line
      background-color #3562DB
      width 0.48rem
    .van-tabs__content
      height calc(100% - 0.88rem)
      .van-tab__pane
        overflow-y auto
        border-top 0.2rem solid #F2F4F9
    .van-tab--active
      .van-tab__text
        color #1D2129
        font-weight bold !important
    .van-tab__text--ellipsis
      overflow visible
  .planTypeWrap
    position relative
    height 1.24rem
    padding 0 0.32rem
    background-color #fff
    display flex
    align-items center
    overflow-x auto
    overflow-y hidden
    .typeBar
      width 30%
      height 0.72rem
      line-height 0.72rem
      // padding 0 0.2rem
      text-align center
      color #4E5969
      // overflow: hidden;
      text-overflow ellipsis
      white-space nowrap
      /deep/ .van-badge__wrapper
        width 100%
        div:first-child
          overflow hidden
          white-space nowrap
          text-overflow ellipsis
    .typeBar:last-child
      margin-right 0
    .typeActive
      background-color #E6EFFC
      color #3562DB
  .list
    padding 0.4rem 0.32rem
    position relative
    font-size 0.32rem
    background-color #fff
    border-bottom 1px solid #E5E6EB
    .title
      display flex
      align-items center
      justify-content space-between
      width 100%
      overflow hidden
      text-overflow ellipsis
      white-space nowrap
      .task-name
        color #1D2129
        font-weight 600
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
      .typeWarp
        display flex
        align-items center
        .taskType
          display inline-block
          background-color #E6EFFC
          padding 0.06rem 0.08rem
          border-radius 0.04rem
          font-size 0.24rem
          color #3562DB
          text-align center
          font-weight normal
    .point
      margin 0.32rem 0
      display flex
      .pointItem
        width 50%
        display flex
        align-items center
        .pointTitle
          color #4E5969
          margin-right 0.48rem
          padding 0
          border 0
        .count
          color #1D2129
    .time
      .timeTitle
        color #4E5969
        margin-right 0.48rem
      .timeDate
        color #1D2129
  .notList
    position relative
    height calc(100% - 1rem)
    .emptyImg
      position absolute
      height 100%
      width 50%
      left 25%
      background url('../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat
      background-size 100% auto
      .emptyText
        position absolute
        width 100%
        text-align center
        bottom 40%
  .scanDataWarp
    padding-top 0.2rem
    height calc(90% - 0.2rem)
    overflow auto
.tabContent
  width 88%
  height 100%
  display flex
  align-items center
  overflow-x auto
  overflow-y hidden
.more
  flex 1
  text-align right
.popContent
  width 100%
  height 100%
  overflow hidden
.popBox
  width 100%
  height 83%
  flex-wrap wrap
  overflow-y auto
  margin-left 0.23rem
.popTitle
  width 100%
  text-align left
  margin 0.3rem
  font-size 0.4rem
.typeBarNew
  width 28%
  height 0.72rem
  line-height 0.72rem
  text-align center
  background-color #f2f4f9
  color #4E5969
  margin-top 0.23rem
  // overflow: hidden;
  text-overflow ellipsis
  white-space nowrap
.typeNewActive
  background-color #E6EFFC
  color #3562DB
.typeBarNew:last-child
  margin-right 0
.listWarp1
  height 100%
  overflow-y auto
.listWarp2
  height calc(100% - 1.28rem)
  overflow-y auto
.inner-content
  height calc(100% - 14vh)
/deep/ .van-badge__wrapper
  width 100%
  div:first-child
    overflow hidden
    white-space nowrap
    text-overflow ellipsis
</style>
