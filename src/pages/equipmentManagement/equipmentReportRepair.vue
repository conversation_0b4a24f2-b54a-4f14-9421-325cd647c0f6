<template>
  <div class="my-container">
    <Header title="设备报修" @backFun="goBack"></Header>
    <div class="inner" style="background-color: #fff">
      <!-- 有设备 -->
      <div v-if="type == 1">
        <van-field v-model="equipInfo.assetsName" label="设备名称" placeholder="请选择设备" required :rules="[{ required: true, message: '请选择设备' }]" @click="toEquip">
          <template #button>
            <div class="right-icon-sty">
              <van-icon name="arrow" @click.stop="toEquip" />
              <van-icon name="scan" @click.stop="toScan()" />
            </div>
          </template>
        </van-field>
        <div v-if="equipInfo.assetsId">
          <van-field v-model="equipInfo.assetsCode" label="设备编码" disabled placeholder="" />
          <van-field v-model="equipInfo.model" label="规格型号" disabled placeholder="" />
          <van-field v-model="equipInfo.snNumber" label="SN号" :disabled="!SNShow" :placeholder="SNShow ? '请输入SN号' : ''">
            <template #extra>
              <span class="SN-span" size="small" type="info" v-if="equipInfo.snNumber && !SNShow" @click="setSN">修改</span>
              <span class="SN-span" size="small" type="info" v-if="!equipInfo.snNumber && !SNShow" @click="setSN">补录</span>
              <span class="SN-span" size="small" type="info" v-if="SNShow" @click="setSNSubmit">确定</span>
            </template>
          </van-field>
          <van-field v-model="equipInfo.assetsType" label="设备类型" disabled placeholder="" />
          <van-field v-model="equipInfo.brandName" label="设备品牌" disabled placeholder="" />
          <van-field v-model="equipInfo.manufacturerName" label="生产厂家" disabled placeholder="" />
          <van-field v-model="equipInfo.money" label="设备原值" disabled placeholder="" />
          <van-field v-model="equipInfo.teamName" label="使用科室" disabled placeholder="" />
          <div class="regionBox">
            <div>所在区域</div>
            <div>{{ equipInfo.areaName }}</div>
          </div>
          <div class="equipInfoBox">
            <div class="img">
              <img class="row-img" v-if="equipInfo.titlePic" :src="equipInfo.titlePic" />
              <img class="row-img" v-else :src="equipZw" />
            </div>
            <div class="infoBox">
              <div class="bxTimeBox">
                <div class="bxTime">保修期：{{ equipInfo.warrantyContractStartTime ? equipInfo.warrantyContractStartTime + "~" + equipInfo.warrantyContractEndTime : "" }}</div>
                <div class="bxStatus">
                  <span v-if="equipInfo.protectStatus" style="color: #008800; border: 1px solid #008800; background-color: rgba(0, 131, 0, 0.3)">在保</span>
                  <span v-else style="color: #ff0000; border: 1px solid #ff0000; background-color: rgba(212, 0, 0, 0.3)">不在保</span>
                </div>
              </div>
              <div class="costBox">
                <div class="cost">
                  <div>{{ equipInfo.yearPrice }}</div>
                  <div>本年度维修成本</div>
                </div>
                <div class="cost">
                  <div>{{ equipInfo.allPrice }}</div>
                  <div>总维修成本</div>
                </div>
              </div>
            </div>
          </div>
          <div class="record" @click="toPage">
            <div>维修保养记录</div>
            <i class="iconfont iconfontRight">&#xe684;</i>
          </div>
        </div>
      </div>
      <!-- 故障信息 -->
      <div style="height: 0.2rem; background-color: #f2f4f9"></div>
      <!-- <second-title title="故障信息" /> -->
      <van-field v-model="userName" label="报修人" placeholder="请输入" disabled />
      <van-field
        v-model="reportPhone"
        label="联系方式"
        placeholder="请输入"
        disabled
        :rules="[
          { validator: validateMobileNum, message: '请填写正确的手机号' },
          { required: true, message: '请输入联系方式' }
        ]"
      />
      <van-field
        v-model="equipInfo.locationDescription"
        label="设备位置"
        maxlength="50"
        rows="1"
        :autosize="{ maxHeight: 73, minHeight: 30 }"
        type="textarea"
        class="locationClass"
        placeholder="请输入或选择设备位置"
        @input="locationInput"
      >
        <template #button>
          <div class="right-icon-sty">
            <van-icon name="arrow" @click.stop="showAreaPicker" />
            <van-icon name="search" @click.stop="getLocationName()" />
          </div>
        </template>
      </van-field>
      <van-popup v-model="showAreaPickerFlag" position="bottom">
        <div class="search-box">
          <van-search v-model="areaSearchValue" placeholder="请输入搜索关键词" />
        </div>
        <van-picker show-toolbar :columns="filteredAreaList" value-key="showname" @confirm="onAreaConfirm" @cancel="showAreaPickerFlag = false" title="选择服务区域" />
      </van-popup>
      <van-field readonly clickable v-model="sourcesDeptName" label="所属科室" placeholder="请选择所属科室" @click="showDeptPicker">
        <template #button>
          <div class="right-icon-sty">
            <van-icon name="arrow" @click.stop="showDeptPicker" />
            <van-icon name="search" @click.stop="toSelectDept" />
          </div>
        </template>
      </van-field>
      <van-popup v-model="showDeptPickerFlag" position="bottom">
        <div class="search-box">
          <van-search v-model="deptSearchValue" placeholder="请输入搜索关键词" />
        </div>
        <van-picker show-toolbar :columns="filteredDeptList" value-key="name" @confirm="onDeptConfirm" @cancel="showDeptPickerFlag = false" title="选择所属科室" />
      </van-popup>
      <van-field
        readonly
        clickable
        name="datetimePicker"
        :value="reportTime"
        label="发生时间"
        required
        :rules="[{ required: true, message: '请选择发生时间' }]"
        placeholder="请选择时间"
        @click="timeFn"
      />
      <van-field readonly clickable name="datetimePicker" :value="requireAccomplishDate" label="要求完工时间" placeholder="请选择时间" @click="closingTimeFn" />
      <van-field
        class="voice-upper"
        v-model="repairExplain"
        rows="3"
        autosize
        label="故障描述"
        type="textarea"
        maxlength="200"
        placeholder="请输入故障描述或者语音，字数限制在200字内，语言限制60秒"
        :rules="[{ required: true }]"
        show-word-limit
        required
      />
      <sounds-recording v-if="h5Mode == 'apicloud'" ref="voiceController" class="voiceWrap bg-w" @getRecord="getRecord" @getRecordFile="getRecordFile" @recordVoice="recordVoice" />
      <upload-voice class="bg-w" v-else ref="voices" @getvVoiceParams="getRecord" @voiceFlag="deleteVoice" @getVoiceLocalId="translateVoice"></upload-voice>
      <van-field name="repairAttachmentUrl" label="故障图片" class="img-upload">
        <template #label>
          <div class="label-sty">
            <span>故障图片</span>
            <span>注：上传本地图片，最多上传三张</span>
          </div>
        </template>
        <template #input>
          <van-uploader ref="uplodImg" v-model="fileList" multiple :max-count="3" accept="image/*" :after-read="afterRead" @delete="deleteImg" />
        </template>
      </van-field>
      <!-- 其他信息 -->
      <!-- 有设备 -->
      <div v-if="type == 1">
        <!-- <second-title title="其他信息" style="margin-top: 10px" /> -->
<!--        <van-field-->
<!--          readonly-->
<!--          clickable-->
<!--          name="picker"-->
<!--          :value="faultGradeTypeObj.names"-->
<!--          label="故障类别"-->
<!--          placeholder="请选择"-->
<!--          @click="faultGradeTypeFn"-->
<!--          required-->
<!--          :rules="[{ required: true, message: '请选择故障类别' }]"-->
<!--        >-->
<!--          <template #button>-->
<!--            <div class="right-icon-sty">-->
<!--              <van-icon name="arrow" />-->
<!--            </div>-->
<!--          </template>-->
<!--        </van-field>-->
        <van-field
          readonly
          clickable
          name="picker"
          :value="urgencyDegreeName"
          label="紧急程度"
          placeholder="请选择"
          @click="urgencyDegreeFn"
          required
          :rules="[{ required: true, message: '请选择紧急程度' }]"
        >
          <template #button>
            <div class="right-icon-sty">
              <van-icon name="arrow" />
            </div>
          </template>
        </van-field>
        <van-field
          readonly
          clickable
          name="picker"
          :value="faultGradeName"
          label="故障等级"
          placeholder="请选择"
          @click="faultGradeFn"
          required
          :rules="[{ required: true, message: '请选择故障等级' }]"
        >
          <template #button>
            <div class="right-icon-sty">
              <van-icon name="arrow" />
            </div>
          </template>
        </van-field>
      </div>
      <div style="margin: 16px">
        <van-button round block type="info" native-type="submit" :loading="submitLoading" :disabled="submitLoading" @click="handleSubmit">提交</van-button>
      </div>
      <!-- 维修班组弹框 -->
      <van-popup v-model="designateDeptShow" position="bottom" :style="{ height: '40%' }">
        <van-picker show-toolbar :columns="teamTreeListName" @confirm="designateDeptConfirm" @cancel="designateDeptCancel" />
      </van-popup>
      <!-- 紧急程度弹框 -->
      <van-popup v-model="urgencyDegreeShow" round position="bottom" :style="{ height: '40%' }">
        <van-picker value-key="label" show-toolbar :columns="urgencyDegreeList" @confirm="urgencyDegreeConfirm" @cancel="urgencyDegreeCancel" />
      </van-popup>
      <van-popup v-model="faultGradeShow" round position="bottom" :style="{ height: '40%' }">
        <van-picker value-key="label" show-toolbar :columns="faultGradeList" @confirm="faultGradeConfirm" @cancel="faultGradeCancel" />
      </van-popup>
      <!-- 故障类别弹框 -->
      <van-popup v-model="faultGradeTypeShow" position="bottom" round :style="{ height: '40%' }">
        <van-cascader
          v-model="cascaderValue"
          title="选择故障类型"
          :options="itemList"
          @close="faultGradeTypeShow = false"
          @finish="onFinish"
          :field-names="{ text: 'text', value: 'id', children: 'children' }"
        />
      </van-popup>
      <!-- 选择时间弹框 -->
      <van-popup v-model="timeShow" position="bottom" :style="{ height: '40%' }">
        <van-datetime-picker v-model="currentDate" type="date" title="选择发生时间" :min-date="minDate" :max-date="maxDate" @confirm="dateComfirm" @cancel="dateCancel" />
      </van-popup>
      <!-- 选择要求完工时间弹框 -->
      <van-popup v-model="timeShow2" position="bottom" :style="{ height: '40%' }">
        <van-datetime-picker v-model="currentDate2" type="datetime" title="选择要求完工时间" @confirm="dateComfirm2" @cancel="dateCancel" />
      </van-popup>
    </div>
  </div>
</template>

<script>
import YBS from "@/assets/utils/utils.js";
import ImageCompressor from "image-compressor.js";
import { Toast } from "vant";
import axios from "axios";
import qs from "qs";
import moment from "moment";
import { mapState } from "vuex";
export default {
  name: "addRepairItem",
  data() {
    return {
      loginInfo: {},
      staffInfo: {},
      cascaderValue: "",
      itemList: [],
      type: 1,
      SNShow: false,
      equipInfo: {
        assetsName: "", //设备名称
        assetsCode: "",
        equipmentId: "", //设备id
        Location: "", //设备位置id
        locationName: "", //设备位置名称
        assetCode: "", // 设备编码
        assetModel: "", // 设备型号
        locationDescription: "",
        yearPrice: "",
        allPrice: "",
        snNumber: "",
        titlePic: "",
        money: "",
        warrantyContractEndTime: "",
        warrantyContractStartTime: "",
        protectStatus: ""
      },
      requireAccomplishDate: "", //要求完工时间

      useOffice: "", //科室名称
      useOfficeId: "",
      reportPhone: JSON.parse(localStorage.getItem("loginInfo")) ? JSON.parse(localStorage.getItem("loginInfo")).phone : "", //报修人手机号
      reportTime: moment().format("YYYY-MM-DD"), //报修时间
      repairExplain: "",
      userName: JSON.parse(localStorage.getItem("loginInfo")) ? JSON.parse(localStorage.getItem("loginInfo")).staffName : "",
      equipmentNameNone: "",
      currentDate: new Date(),
      currentDate2: new Date(),

      minDate: new Date(2000, 0, 1),
      maxDate: new Date(),

      timeShow: false,
      timeShow2: false,
      /*  紧急程度 */
      urgencyDegreeShow: false,
      urgencyDegreeList: [],
      urgencyDegreeListName: [],
      urgencyDegree: "",
      urgencyDegreeName: "",
      /*  紧急程度 */
      /*  故障等级 */
      faultGradeShow: false,
      faultGradeList: [],
      faultGradeListName: [],
      faultGrade: "",
      faultGradeName: "",
      /*  故障等级 */
      /* 故障类别 */
      faultGradeTypeShow: false,
      faultGradeTypeList: [],
      faultGradeListTypeName: [],
      faultGradeType: "",
      faultGradeTypeName: "",
      faultGradeTypeListAll: [],
      faultGradeTypeObj: {
        serviceItemOneId: "",
        serviceItemOneName: "",
        serviceItemTwoId: "",
        serviceItemTwoName: "",
        serviceItemThreeId: "",
        serviceItemThreeName: "",
        names: ""
      },
      /* 故障类别 */
      /* 维修班组 */
      teamTreeList: [],
      teamTreeListName: [],
      designateDeptShow: false,
      designateDeptName: "",
      designateDeptCode: "",
      /* 维修班组 */

      wxConfig: {}, //微信配置相关
      imgUrl: [],
      repairAttachmentUrl: [],
      maxSize: 5, //
      fileMessage: "",
      fileName: "",
      deptOptions: [], // 使用科室
      recordingInfo: "", //录音。。
      recordingInfoFile: "",
      submitLoading: false,
      fromPage: this.$route.query.from || "",
      // equipZw: require("@/assets/images/equipNo.png"), //暂无数据图片
      equipZw: "",
      hospitalCode: "",
      unitCode: "",
      setHospitalCode: "SHETYX", //SHETYX BJZKYX
      faultGradeTypeListNew: [],
      loginInfo: JSON.parse(localStorage.getItem("loginInfo")),
      selectedOptions: "",
      fileList: [],
      UploadVoice: {}, // 语音暂存
      beforePageUrl: "",
      imgSubStatus: true,
      eqId: "",
      sourcesDeptName: "",
      showDeptPickerFlag: false,
      deptSearchValue: "",
      showAreaPickerFlag: false,
      areaSearchValue: "",
      olgWorkPushNew: {},
      deptList: [],
      areaList: []
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.beforePageUrl = from.path;
    });
  },
  watch: {
    sourcesDept(val) {
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "0" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getLocationListByDept(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    },
    "equipInfo.Location": {
      handler(val) {
        if (val && (this.olgWorkPushNew.departmentServiceLocationCascade == "1" || this.olgWorkPushNew.departmentServiceLocationCascade == "2")) {
          this.getDeptListByLocation(this.olgWorkPushNew.departmentServiceLocationCascade);
        }
      },
      deep: true
    }
  },
  computed: {
    ...mapState(["h5Mode"]),
    // 过滤后的区域列表
    filteredAreaList() {
      if (!this.areaSearchValue) {
        return this.areaList;
      }
      return this.areaList.filter(item => item.name && item.name.toLowerCase().includes(this.areaSearchValue.toLowerCase()));
    },
    // 过滤后的科室列表
    filteredDeptList() {
      if (!this.deptSearchValue) {
        return this.deptList;
      }
      return this.deptList.filter(item => item.name && item.name.toLowerCase().includes(this.deptSearchValue.toLowerCase()));
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.staffInfo = JSON.parse(localStorage.getItem("staffInfo"));
    // 获取储存、相机权限
    if (!YBS.hasPermission("storage")) {
      YBS.reqPermission(["storage"], function (ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          if (!YBS.hasPermission("camera")) {
            YBS.reqPermission(["camera"], function (ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              }
            });
            return;
          } else {
          }
        }
      });
      return;
    }
    if (!YBS.hasPermission("camera")) {
      YBS.reqPermission(["camera"], function (ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
        }
      });
      return;
    }
  },
  activated() {
    // 当页面被keep-alive缓存后重新激活时执行
    if (this.$route.query.selectedOffice) {
      this.sourcesDeptName = this.$route.query.selectedOffice.officeName;
      this.sourcesDept = this.$route.query.selectedOffice.id;
      if (this.olgWorkPushNew.departmentServiceLocationCascade == "0" || this.olgWorkPushNew.departmentServiceLocationCascade == "2") {
        this.getLocationListByDept(this.olgWorkPushNew.departmentServiceLocationCascade);
      }
    }

    // 处理设备详情数据
    if (sessionStorage.getItem("equipmentDetail")) {
      const equipmentDeatil = JSON.parse(sessionStorage.getItem("equipmentDetail"));
      this.equipInfo.assetsName = equipmentDeatil.assetName;
      this.equipInfo.equipmentId = equipmentDeatil.assetsId;
      this.equipInfo.assetCode = equipmentDeatil.assetCode;
      this.equipInfo.assetModel = equipmentDeatil.assetModel;
    }

    // 处理记录详情数据
    if (sessionStorage.getItem("recordDetail")) {
      const recordDetail = JSON.parse(sessionStorage.getItem("recordDetail"));
      this.reportTime = recordDetail.time;
      this.recordingInfo = recordDetail.voice;
      if (Object.keys(recordDetail.UploadVoice || {}).length !== 0) {
        this.$refs.voiceController && this.$refs.voiceController.recordVoice();
      }
      this.fileList = recordDetail.fileList;
      this.faultGradeTypeObj.names = recordDetail.malfunction;
    }

    // 处理区域选择数据
    if (this.beforePageUrl === "/selectAreaNew") {
      let obj = this.$store.state.serviceArea;
      console.log("obj", obj);
      if (obj && obj.name) {
        this.equipInfo.locationDescription = obj.name.join(">");
        this.equipInfo.Location = obj.code.join(",");
        this.equipInfo.locationName = obj.name.join(">");
      }
    } else if (this.beforePageUrl === "/equipmentList") {
      const equipmentDeatil = JSON.parse(sessionStorage.getItem("equipmentDetail"));
      if (equipmentDeatil) {
        this.equipInfo.Location = equipmentDeatil.regionCode;
        this.equipInfo.locationName = equipmentDeatil.regionName;
        this.equipInfo.locationDescription = equipmentDeatil.regionName;
      }
    }

    // 处理云端设备数据
    if (this.$route.query.isClould) {
      this.eqId = this.$route.query.roomCode;
      this.getEquipmentByIdFn();
    }
  },
  mounted() {
    this.$api.getNewSysConfigParam({}).then(res => {
      console.log("获取pc配置", res);
      this.olgWorkPushNew = res.olgWorkPushNew;
    });
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    if (this.beforePageUrl == "/") {
      this.getDialogShow();
    }
    this.getItemList();
    this.getDictionary();
    if (this.$route.query.selectedOffice) {
      this.sourcesDeptName = this.$route.query.selectedOffice.officeName;
      this.sourcesDept = this.$route.query.selectedOffice.id;
    }
    if (sessionStorage.getItem("equipmentDetail")) {
      const equipmentDeatil = JSON.parse(sessionStorage.getItem("equipmentDetail"));
      this.equipInfo.assetsName = equipmentDeatil.assetName;
      this.equipInfo.equipmentId = equipmentDeatil.assetsId;
      this.equipInfo.assetCode = equipmentDeatil.assetCode;
      this.equipInfo.assetModel = equipmentDeatil.assetModel;
      //sessionStorage.removeItem("equipmentDetail");
    }
    if (sessionStorage.getItem("recordDetail")) {
      const recordDetail = JSON.parse(sessionStorage.getItem("recordDetail"));
      this.reportTime = recordDetail.time;
      // this.repairExplain = recordDetail.location;
      this.recordingInfo = recordDetail.voice;
      if (Object.keys(recordDetail.UploadVoice).length !== 0) {
        this.$refs.voiceController.recordVoice();
      }
      this.fileList = recordDetail.fileList;
      this.faultGradeTypeObj.names = recordDetail.malfunction;
    }
    if (this.beforePageUrl === "/selectAreaNew") {
      let obj = this.$store.state.serviceArea;
      this.equipInfo.locationDescription = obj.name.join(">");
      this.equipInfo.Location = obj.code.join(",");
      this.equipInfo.locationName = obj.name.join(">");
    } else if (this.beforePageUrl === "/equipmentList") {
      const equipmentDeatil = JSON.parse(sessionStorage.getItem("equipmentDetail"));
      this.equipInfo.Location = equipmentDeatil.regionCode;
      this.equipInfo.locationName = equipmentDeatil.regionName;
      this.equipInfo.locationDescription = equipmentDeatil.regionName;
    } else if (this.beforePageUrl === "/") {
      sessionStorage.removeItem("equipmentDetail");
    }
    if (this.$route.query.isClould) {
      this.eqId = this.$route.query.roomCode;
      this.getEquipmentByIdFn();
    }
  },
  methods: {
    getDeptListByLocation(configType) {
      // 处理Location值，如果以#开头，去掉#和后面的符号
      let locationId = this.equipInfo.Location;
      if (locationId && locationId.startsWith('#')) {
        // 如果是以#开头，去掉#和紧跟其后的符号（如_）
        locationId = locationId.replace(/^#[_,]?/, '');
      }
      this.$api
        .getDeptListByLocation({
          localtionId: locationId.replace(/,/g, "_")
        })
        .then(res => {
          console.log("根据地点获取科室列表", res);
          this.deptList = res;
          if (configType == "1" || (configType == "2" && !this.sourcesDeptName && res && res.length > 0)) {
            this.sourcesDeptName = res[0].name;
            this.sourcesDept = res[0].id;
          }
        });
    },
    getLocationListByDept(configType) {
      this.$api
        .getLocationListByDept({
          deptId: this.sourcesDept
        })
        .then(res => {
          console.log("根据科室获取地点列表", res);
          // 处理数据，拼接名称和ID
          this.areaList = res.spaceInfoByDeptIdList.map(item => {
            // 处理名称：拼接allParentName和gridName，并去掉所有的>符号
            const name = item.allParentName + item.gridName
            const showname = item.allParentName + ">" + item.gridName;
            // 直接修改item的allParentId属性
            if (item.allParentId && item.allParentId.startsWith("#,")) {
              item.allParentId = item.allParentId.substring(2); // 去掉#和逗号
            } else if (item.allParentId && item.allParentId.startsWith("#")) {
              item.allParentId = item.allParentId.substring(1); // 只去掉#
            }

            return {
              ...item,
              name,
              showname
            };
          });
          if (configType == "0" || (configType == "2" && !this.equipInfo.locationDescription)) {
            this.equipInfo.locationDescription = this.areaList[0].name;
            this.equipInfo.locationName = this.areaList[0].name;
            this.equipInfo.Location = this.areaList[0].allParentId + "," + this.areaList[0].id;
          }
        });
    },
    onAreaConfirm(value) {
      this.equipInfo.locationDescription = value.name;
      this.equipInfo.locationName = value.name;
      this.equipInfo.Location = value.allParentId + "," + value.id;
      this.showAreaPickerFlag = false;
    },
    showAreaPicker() {
      this.areaSearchValue = ""; // 清空搜索值
      this.showAreaPickerFlag = true;
    },
    onDeptConfirm(value) {
      this.sourcesDeptName = value.name; // 使用 name 字段作为显示值
      this.sourcesDept = value.id; // 使用 id 字段作为实际值
      this.showDeptPickerFlag = false;
    },
    toSelectDept() {
      this.$router.push({
        path: "/deptSelectedPage",
        query: {
          fromPath: "equipmentReportRepair"
        }
      });
    },
    showDeptPicker() {
      this.deptSearchValue = ""; // 清空搜索值
      this.showDeptPickerFlag = true;
    },
    getDialogShow() {
      let params = {
        userId: this.loginInfo.staffId,
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getNotCommentWarn(params).then(res => {
        if (res.commentCount > 0 && res.commentStatus == "1") {
          this.$dialog
            .confirm({
              message: "您当前有未评价的工单！",
              confirmButtonText: "去评价",
              cancelButtonText: "忽略",
              confirmButtonColor: "#3562db"
            })
            .then(() => {
              this.$router.push({
                path: "/orderAcceptance"
              });
            })
            .catch(() => {});
        }
      });
    },
    translateVoice(localId) {
      parent.wx.translateVoice({
        localId: localId, // 需要识别的音频的本地Id，由录音相关接口获得，音频时长不能超过60秒
        isShowProgressTips: 1, // 默认为1，显示进度提示
        success: res => {
          // alert(res.translateResult); // 语音识别的结果
          this.repairExplain = res.translateResult;
        }
      });
    },
    handleSubmit() {
      if (this.h5Mode == "apicloud") {
        this.submit();
      } else {
        if (this.$refs.voices.hasVoice == "recorded") {
          this.$refs.voices.saveVoice();
        } else {
          this.submit();
        }
      }
    },
    submit() {
      if (!this.equipInfo.assetsName) return Toast.fail("请选择设备");
      if (!this.reportTime) return Toast.fail("请选择发生时间");
      if (!this.repairExplain) return Toast.fail("请填写故障描述");
      // if (!this.faultGradeTypeObj.names) return Toast.fail("请选择故障类别");
      if (!this.urgencyDegreeName) return Toast.fail("请选择紧急程度");
      if (!this.faultGradeName) return Toast.fail("请选择故障等级");
      if (!this.imgSubStatus) return Toast.fail("请等待图片上传完成");
      Toast.loading({
        message: "正在提交...",
        forbidClick: true,
        duration: 0
      });
      const urls = this.fileList.length
        ? this.fileList.map(item => {
            return item.url;
          })
        : [];
      let params = {
        taskType: 4,
        workTypeCode: 17,
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        workSources: 1,
        userId: this.loginInfo.id,
        createById: this.loginInfo.staffId,
        createByName: this.loginInfo.staffName,
        createByJobnum: this.loginInfo.staffNum,
        createByJob: this.loginInfo.position,
        jobNumber: this.staffInfo.staffNumber, // 工号
        callerCode: this.loginInfo.staffId,
        callerName: this.loginInfo.staffName,
        callerJobNum: this.staffInfo.staffNumber,
        callerJob: this.loginInfo.position,
        deviceLocationDescription: this.equipInfo.locationDescription,
        type: this.staffInfo.teamId ? 2 : 1,
        sourcesDept: this.loginInfo.deptId, // 设备所属科室id
        sourcesDeptName: this.loginInfo.deptName, // 设备所属科室名称
        urgencyDegree: this.urgencyDegree,
        sourcesPhone: this.loginInfo.phone,
        location: this.equipInfo.Location,
        areaCode: this.equipInfo.Location,
        areaName: this.equipInfo.locationName,
        locationName: this.equipInfo.locationName,
        reportTime: this.reportTime,
        questionDescription: this.repairExplain,
        callerTapeUrl: this.recordingInfo,
        attachmentUrl: urls.join(","),
        deviceId: this.equipInfo.equipmentId,
        deviceNumber: this.equipInfo.assetCode, // 设备编码
        deviceName: this.equipInfo.assetsName,
        deviceModel: this.equipInfo.assetModel, // 设备规格型号
        deviceFaultGradeCode: this.faultGrade,
        deviceFaultGradeName: this.faultGradeName,
        serviceItemOneId: this.selectedOptions[0] ? this.selectedOptions[0].id : "",
        serviceItemOneName: this.selectedOptions[0] ? this.selectedOptions[0].name : "",
        serviceItemTwoId: this.selectedOptions[1] ? this.selectedOptions[1].id : "",
        serviceItemTwoName: this.selectedOptions[1] ? this.selectedOptions[1].name : "",
        serviceItemThreeId: this.selectedOptions[2] ? this.selectedOptions[2].id : "",
        serviceItemThreeName: this.selectedOptions[2] ? this.selectedOptions[2].name : "",
        designateDeptCode: "",
        designateDeptName: "",
        deviceTaskType: 1,
        requireAccomplishDate: this.requireAccomplishDate
      };
      axios({
        method: "post",
        url: __PATH.ONESTOP + "/deviceRepair/createWorkOrder",
        data: qs.stringify(params),
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: localStorage.getItem("token")
        }
      })
        .then(res => {
          if (res.data.code == "200") {
            console.log("一站式报修结果", res.data);
            const record = {
              unitCode: this.loginInfo.unitCode,
              hospitalCode: this.loginInfo.hospitalCode,
              assetsId: this.equipInfo.equipmentId,
              operationId: res.data.data.id,
              operationCode: 3, // 1:巡检 2:保养 3:报修
              operation: "报修",
              record: "报修单号：" + res.data.data.workNum
            };
            this.$api.saveOperateRecord(record).then(re => {
              console.log("添加操作记录成功");
              Toast.success({
                duration: 0,
                forbidClick: true,
                message: "报修成功"
              });
              setTimeout(() => {
                Toast.clear();
                this.goBack();
              }, 2000);
            });
          } else {
            $.toast(res.data.message, "text");
          }
        })
        .catch(err => {
          $.toast(err.message || "报修失败", "text");
        });
    },
    onFinish({ selectedOptions }) {
      console.log("---", selectedOptions);
      this.selectedOptions = selectedOptions;
      this.faultGradeTypeShow = false;

      this.faultGradeTypeObj.names = selectedOptions.map(option => option.text).join("/");
    },
    getItemList() {
      let params = {
        id: "17",
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getItemList(params).then(res => {
        this.itemList = YBS.transData(res.list, "id", "parent", "children");
      });
    },
    getDictionary() {
      let params = {
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getDictionary(params).then(res => {
        this.faultGradeList = res.faultGrade;
        this.faultGradeName = res.faultGrade[0].label;
        this.urgencyDegreeList = res.urgencyDegree;
        this.urgencyDegreeName = res.urgencyDegree[0].label;
        if (sessionStorage.getItem("recordDetail")) {
          const recordDetail = JSON.parse(sessionStorage.getItem("recordDetail"));
          this.faultGradeName = recordDetail.faultGradeName;
          this.urgencyDegreeName = recordDetail.urgencyDegreeName;
          sessionStorage.removeItem("recordDetail");
        }
      });
    },
    // 补录/修改提交
    setSNSubmit() {
      // let params = {
      //   snNumber: this.equipInfo.snNumber,
      //   assetsId: this.equipInfo.equipmentId
      // };
      // this.axios.postImes3("modifySNSumber", params, res => {
      //   if (res.code == 200) {
      //     this.$toast.success(res.message);
      //     this.SNShow = false;
      //     this.getRepairInfo(localStorage.getItem("checkedEquip"));
      //   }
      // });
    },
    // 选择设备
    toEquip() {
      // 记录已填项
      const record = {
        location: this.equipInfo.locationDescription, // 设备位置
        time: this.reportTime, // 发生时间
        explain: this.repairExplain, // 故障描述
        voice: this.recordingInfo, // 语音
        fileList: this.fileList, // 图片
        malfunction: this.faultGradeTypeObj.names, // 故障类别
        faultGradeName: this.faultGradeName, // 紧急程度
        urgencyDegreeName: this.urgencyDegreeName, // 故障等级
        UploadVoice: this.UploadVoice // 语音路径
      };
      sessionStorage.setItem("recordDetail", JSON.stringify(record));
      this.$router.push({
        path: "/equipmentList"
      });
    },
    // 跳转维修保养记录
    toPage() {
      this.$router.push({
        path: "/equipmentDetail",
        query: {
          active: "recording",
          recordingId: this.equipInfo.equipmentId
        }
      });
    },
    // 获取维修信息
    getRepairInfo(id) {
      let params = {
        assetsCode: id
      };
      this.axios.postImes3("getRepairInfo", params, res => {
        if (res.code == 200) {
          this.equipInfo.time = res.data.warrantyContractStartTime + "~" + res.data.warrantyContractEndTime;
          this.equipInfo.warrantyContractStartTime = res.data.warrantyContractStartTime;
          this.equipInfo.warrantyContractEndTime = res.data.warrantyContractEndTime;
          this.equipInfo.titlePic = res.data.titlePic;
          this.equipInfo.snNumber = res.data.snNumber;
          this.equipInfo.allPrice = res.data.allPrice;
          this.equipInfo.yearPrice = res.data.yearPrice;
          if (res.data.warrantyContractStartTime) {
            let status = this.nowInDateBetwen(res.data.warrantyContractStartTime, res.data.warrantyContractEndTime);
            this.equipInfo.protectStatus = status;
          } else {
            this.equipInfo.protectStatus = false;
          }
        }
      });
    },
    // 判断是否在某个时间段内
    nowInDateBetwen(d1, d2) {
      //如果时间格式是正确的，那下面这一步转化时间格式就可以不用了
      // var dateBegin = new Date(d1.replace(/-/g, "/"));//将-转化为/，使用new Date
      // var dateEnd = new Date(d2.replace(/-/g, "/"));//将-转化为/，使用new Date
      var dateBegin = new Date(d1); //将-转化为/，使用new Date
      var dateEnd = new Date(d2); //将-转化为/，使用new Date
      var dateNow = new Date(); //获取当前时间

      var beginDiff = dateNow.getTime() - dateBegin.getTime(); //时间差的毫秒数
      var beginDayDiff = Math.floor(beginDiff / (24 * 3600 * 1000)); //计算出相差天数

      var endDiff = dateEnd.getTime() - dateNow.getTime(); //时间差的毫秒数
      var endDayDiff = Math.floor(endDiff / (24 * 3600 * 1000)); //计算出相差天数
      if (endDayDiff < 0) {
        //已过期
        return false;
      }
      if (beginDayDiff < 0) {
        //没到开始时间
        return false;
      }
      return true;
    },
    // 校验手机号
    validateMobileNum(val) {
      // return validateMobile(value);
      if (!Boolean(val)) {
        return true;
      } else {
        const reg = /^1\d{10}$/;
        return reg.test(val);
      }
    },
    getTreeLastData(data) {
      if (data && data.length == 0) {
        return [];
      }
      var lastchildren = [];
      forxh(data);

      function forxh(list) {
        for (var i = 0; i < list.length; i++) {
          var chlist = list[i];
          if (chlist.children && chlist.children.length > 0) {
            forxh(chlist.children);
          } else {
            lastchildren.push(chlist);
          }
        }
      }
      return lastchildren;
    },
    // 获取医院分组
    teamTreeListFn() {
      const { serviceItemOneId, serviceItemTwoId, serviceItemThreeId } = this.faultGradeTypeObj;
      let params = {
        // forShort:'ioms',
        iteamCodeList: serviceItemOneId + "," + serviceItemTwoId + "," + serviceItemThreeId
      };

      this.axios.postBasic("teamTreeList", params, res => {
        const { code, data, message } = res;
        if (code == 200) {
          this.teamTreeList = data;
          this.teamTreeListName = this.teamTreeList.map(item => {
            return item.team_name;
          });
        }
      });
    },
    // 根据扫码后的code获取设备信息
    async getEquipmentByIdFn(val) {
      if (!this.eqId) {
        if (!val) return this.$toast.fail("未查找到相关设备");
      }
      const isMaintain = await this.getDeviceWorkOrderStatus(this.eqId || val[3]);
      if (isMaintain.data.code == "200") {
        let params = {
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode,
          id: this.eqId || val[3]
        };
        this.$api.deviceDetails(params).then(res => {
          this.equipInfo.assetsName = res.assetName;
          this.equipInfo.equipmentId = res.id;
          this.equipInfo.locationDescription = res.regionName;
        });
      } else {
        $.toast(isMaintain.data.message, "text");
      }
    },
    // 查询当前设备是否在修
    getDeviceWorkOrderStatus(id) {
      const params = new FormData();
      params.append("unitCode", this.loginInfo.unitCode);
      params.append("hospitalCode", this.loginInfo.hospitalCode);
      params.append("deviceId", id);
      return axios.post(__PATH.ONESTOP + "/deviceRepair/getDeviceWorkOrderStatus", params, {
        headers: {
          Authorization: localStorage.getItem("token")
        }
      });
    },
    // 获取语音路径,
    getRecord(info) {
      console.log("getRecordInfo", info);
      if (this.h5Mode == "apicloud") {
        this.recordingInfo = info;
      } else {
        // this.recordingInfo = info;
        this.$api
          .voiceId2url({
            voiceUrl: info,
            sourcesFlag: "1"
          })
          .then(res => {
            console.log("voiceRes", res);
            this.recordingInfo = res.fileUrlList;
            this.submit();
          });
      }
    },
    deleteVoice() {
      this.recordingInfo = "";
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
    },
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD");
    },
    formatDate2(date) {
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    //时间弹窗
    dateComfirm(e) {
      this.reportTime = this.formatDate(e);
      this.dateCancel();
    },
    dateComfirm2(e) {
      this.requireAccomplishDate = this.formatDate2(e);
      this.dateCancel();
    },
    dateCancel() {
      this.timeShow = false;
      this.timeShow2 = false;
    },
    timeFn() {
      this.timeShow = true;
    },
    closingTimeFn() {
      this.timeShow2 = true;
    },
    // 紧急事件弹框
    urgencyDegreeConfirm(value, index) {
      this.urgencyDegreeCancel();
      // this.urgencyDegree = this.urgencyDegreeList[index].value;
      this.urgencyDegreeName = value.label;
      this.urgencyDegree = value.sort;
    },
    urgencyDegreeCancel() {
      this.urgencyDegreeShow = false;
    },
    // 紧急程度
    urgencyDegreeFn() {
      this.urgencyDegreeShow = true;
    },
    // 故障等级弹框
    faultGradeConfirm(value) {
      this.faultGradeCancel();
      this.faultGradeName = value.label;
      this.faultGrade = value.sort;
    },
    faultGradeCancel() {
      this.faultGradeShow = false;
    },
    faultGradeFn() {
      this.faultGradeShow = true;
    },
    faultGradeTypeCancel() {
      this.faultGradeTypeShow = false;
    },
    // 故障类别
    faultGradeTypeFn() {
      this.faultGradeTypeShow = true;
    },
    // 维修班组弹框
    designateDeptConfirm(value, index) {
      this.designateDeptCancel();
      this.designateDeptCode = this.teamTreeList[index].id;
      this.designateDeptName = value;
    },
    designateDeptCancel() {
      this.designateDeptShow = false;
    },
    designateDeptFn() {
      if (!this.faultGradeTypeObj.serviceItemOneId) return Toast.fail("请先选择故障类别");
      this.designateDeptShow = true;
    },
    /* 扫码 */
    toScan() {
      if (this.h5Mode == "apicloud") {
        this.APPScan();
      } else {
        this.wxScan();
      }
    },
    wxScan() {
      parent.wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
        success: res => {
          //扫码返回的结果
          console.log("wx扫码返回的结果", res);
          let resultArr = res.resultStr.split(",");
          if (resultArr[0] == "iAAS") {
            this.getEquipmentByIdFn(resultArr);
          } else {
            $.toast("无效二维码", "text");
          }
        }
      });
    },
    //APP扫码
    APPScan() {
      if (!YBS.hasPermission("storage")) {
        YBS.reqPermission(["storage"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
            } else {
            }
          }
        });
        return;
      }
      if (!YBS.hasPermission("camera")) {
        YBS.reqPermission(["camera"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
          }
        });
        return;
      }

      try {
        YBS.scanCode().then(
          item => {
            console.log("app扫码结果", item);
            this.getEquipmentByIdFn(item);
          },
          () => {
            this.$toast.fail("未查找到相关设备");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    commonSubmit(url, params) {
      this.axios.postImes(url, params, res => {
        this.submitLoading = false;
        const { code, data, message } = res;
        localStorage.removeItem("checkedEquip");
        if (code == 200) {
          this.$toast.success({
            duration: 1000, // 持续展示 toast
            forbidClick: true,
            message: "报修成功"
          });
          setTimeout(() => {
            let source = sessionStorage.getItem("source") || "";
            if (this.utils.isWechat() || source == "app") {
              // 如果是在微信端。
              this.$router.go(-1);
            } else {
              api.sendEvent({
                name: "backReload",
                extra: {
                  state: "no"
                }
              });
              api.closeWin();
            }
          }, 1000);
        } else {
          if (url == "equipmentRepairAPP") {
            this.$toast.fail(message);
          }
          this.submitLoading = false;
        }
      });
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    // 图片上传成功回调
    async afterRead(files) {
      this.fileList.forEach(i => {
        return (i.status = "uploading");
      });
      this.imgSubStatus = false;
      // 使用formdata上传
      const formData = new FormData();
      const subFile = await this.compressImage(files);
      formData.append("file", subFile);
      this.subImg(formData);
    },
    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        new ImageCompressor(file.file, {
          quality: 0.6,
          checkOrientation: false,
          success(res) {
            let file = new window.File([res], res.name, { type: res.type });
            resolve(file);
          },
          error(e) {
            reject();
          }
        });
      });
    },
    subImg(params) {
      axios({
        method: "post",
        url: __PATH.ONESTOP + "/minio/upload",
        data: params,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: localStorage.getItem("token")
        }
      }).then(res => {
        if ((res.data.code = "200")) {
          this.fileList[this.fileList.length - 1].url = res.data.data.picUrl;
          this.fileList.forEach(i => {
            return (i.status = "done");
          });
          this.imgSubStatus = true;
        }
      });
    },
    deleteImg() {
      this.fileList = this.fileList.filter(i => i.name != e.file.name);
    },
    onFailed(values) {
      this.$toast(values.errors[0].message);
    },
    goBack() {
      if (this.fromPage == "inspection") {
        this.$router.go(-1);
      } else {
        // api.closeFrame();
        this.$YBS.apiCloudCloseFrame();
      }
    },
    recordVoice(path) {
      this.UploadVoice = path;
    },
    // 获取设备位置
    getLocationName() {
      //级联弹窗数据还原
      this.equipInfo.Location = "";
      this.equipInfo.locationName = "";
      this.equipInfo.locationDescription = "";
      this.$router.push({
        path: "/selectAreaNew",
        query: {
          source: "revise"
        }
      });
    },
    locationInput(e) {
      this.equipInfo.Location = "";
      this.equipInfo.locationName = "";
    }
  }
};
</script>
<style scoped lang="stylus">
/deep/ .voice-upper::after
  display none
.bg-w
  width 100%
  background-color #fff
.my-container
  height 100%
  width 100%
  overflow hidden
  .inner
    height 90%
    overflow auto
    .voiceWrap
      width calc(100% - 16px)
      margin-left 16px
/deep/ .van-tabs__line
  background-color #3562DB
/deep/ .van-cascader
  .van-cascader__tab
    font-size 15px !important
  .van-cascader__options
    height auto
  .van-cascader__option--selected
    font-size 15px
    color #3562DB
/deep/ .van-picker
  .van-picker__cancel
    font-size 16px
  .van-picker__confirm
    font-size 16px
/deep/.card-title
  padding 0.4rem 0.8rem !important
  background-color #f2f4f9 !important
  .card-text
    color #86909C !important
    font-size 0.32rem !important
  .card-text::before
    display none !important
/deep/ .van-cell
  padding 0.32rem 0.32rem
/deep/ .sounds-div
  color #3562DB !important
  background #E6EFFC !important
.SN-span
  display inline-block
  width 78px
  height 32px
  text-align center
  line-height 32px
  box-sizing border-box
  background-color #2d4a74
  color #fff
  border-radius 5px
  font-size 14px
.right-icon-sty i
  font-size 0.32rem
.iconfontRight
  color #d8dee7
// .iconfontScan {
// // color: $main-bgColor;
// }
/deep/ .img-upload
  display block
/deep/ .img-upload .van-field__value
  margin-top 10px
/deep/ .sounds-fields .van-cell__title
  line-height 40px
/deep/ .sounds-fields .van-field__button
  width 100%
  display flex
  justify-content flex-end
/deep/ .van-field__label
  font-size 0.32rem
  color #1D2129
/deep/ .van-button--info
  color #fff
  background-color #3562DB
  border 1px solid #3562DB
  font-size 17px
  font-weight 400
/deep/ .van-button--round
  border-radius 2px
/deep/ .van-field__control
  font-size 0.32rem
  font-weight 400
/deep/ .van-field__control:disabled
  -webkit-text-fill-color rgba(#323233, 0.7)
/deep/ .van-field--disabled .van-field__label
  color #1D2129
.equipInfoBox
  display flex
  padding 10px 16px
  box-sizing border-box
  background-color #ffffff
.img
  width 93px
  height 77px
  margin-right 12px
  img
    width 86px
    height 100%
    display block
.infoBox
  width calc(100% - 105px)
  font-size 12px
.bxStatus
  border-bottom 1px solid #cf9236
  padding-bottom 5px
  span
    padding 0px 10px
    border-radius 10px
    display inline-block
    line-height 1.5
.bxTime
  line-height 2
.costBox
  display flex
  font-size 12px
  .cost
    width 50%
    margin-top 5px
    text-align center
    div:first-child
      color #0074d9
      line-height 2em
.record
  display flex
  align-items center
  justify-content space-between
  background-color #ffffff
  padding 10px 16px
  box-sizing border-box
  font-size 15px
  div:first-child
    color #0074d9
.regionBox
  padding 10px 16px
  box-sizing border-box
  background-color #ffffff
  font-size 15px
  display flex
  div:first-child
    width 93px
    margin-right 12px
  div:last-child
    width calc(100% - 105px)
    margin-right 12px
    white-space pre-wrap
    color rgba(50, 50, 51, 0.7)
.label-sty
  width 90vw
  display flex
  justify-content space-between
.label-sty span:nth-child(2)
  font-size 12px
/deep/ .locationClass
  textarea
    padding 0 !important
</style>
