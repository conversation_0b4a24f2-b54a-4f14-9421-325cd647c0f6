<template>
  <div class="inner">
    <Header :title="$route.query.systemNum == '2' ? '保养结果' : '巡检结果'" @backFun="goBack"></Header>
    <div class="contenTop">
      <div class="topItem">
        <div class="itemTitle">{{ $route.query.systemNum == "2" ? "保养" : "巡检" }}人员</div>
        <div class="itemConten">{{ loginInfo.staffName }}</div>
      </div>
      <div class="topItem">
        <div class="itemTitle">{{ $route.query.systemNum == "2" ? "保养" : "巡检" }}部门</div>
        <div class="itemConten">{{ loginInfo.deptName }}</div>
      </div>
      <div class="topItem notBoder">
        <div class="itemTitle">{{ $route.query.systemNum == "2" ? "保养" : "巡检" }}时间</div>
        <div class="itemConten">{{ moment().format("YYYY-MM-DD HH:mm") }}</div>
      </div>
    </div>
    <div class="resultText">{{ $route.query.systemNum == "2" ? "保养" : "巡检" }}结果</div>
    <div class="contenTop">
      <div class="topItem">
        <div class="itemTitle">{{ $route.query.systemNum == "2" ? "保养" : "巡检" }}结果</div>
        <div class="itemConten">{{ resultType == "2" ? "合格" : resultType == "4" ? "异常报修" : "不合格" }}</div>
      </div>
      <div class="topItem">
        <div class="itemTitle">定位状态</div>
        <div class="itemConten">
          {{ whetherLocation == "1" ? "未开启定位" : whetherLocation == "2" ? "定位成功" : "定位失败" }}
        </div>
      </div>
    </div>
    <div class="bottom-conten">
      <div class="situation">{{ $route.query.systemNum == "2" ? "保养" : "巡检" }}情况说明</div>
      <van-field v-model="repairExplain" rows="2" autosize type="textarea" maxlength="200"
        :placeholder="`请输入${$route.query.systemNum == '2' ? '保养' : '巡检'}结果说明，字数限制200字以内`" :rules="[{ required: true }]"
        show-word-limit />
      <sounds-recording v-if="h5Mode == 'apicloud'" style="margin: 10px 0 0 0" @getRecord="getRecord" @getRecordFile="getRecordFile"
        @getInspRecord="getInspRecord" />
        <upload-voice class="bg-w" v-else ref="voices" @getvVoiceParams="getRecord" ></upload-voice>
      <span style="color: #86909C;font-size: 14px;">注：语音最多录制60秒</span>
      <div class="situationUplod">
        <div class="imgTitle">附件图片</div>
        <div style="display: flex;vertical-align: middle;height: 100%">
          <div class="tolta">{{ attachmentUrl.length }}/9</div>
          <ApiGetPicture v-if="h5Mode == 'apicloud'" ref="apiPicture" @submitFile="getApiFile" :limited="attachmentUrl.length" />
        </div>
      </div>
      <div class="picture-select" style="width: 100%; margin: 0 auto">
        <van-uploader ref="uplodImg" :disabled="h5Mode == 'apicloud'" v-model="files" :max-count="9" accept="image/*"
          :after-read="afterRead" @delete="deleteImg" :preview-full-image="false" @click-preview="previewImg">
          <span v-if="h5Mode == 'apicloud'"></span>
        </van-uploader>
        <!-- <van-uploader ref="uplodImg" v-model="files" :multiple="true" :max-count="9" accept="image/*" :after-read="afterRead" @delete="deleteImg" /> -->
      </div>
    </div>
    <div class="bottom-bar">
      <div class="qualified" @click="repairConfirm">确定</div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import axios from "axios";
import ImageCompressor from "image-compressor.js";
import { mapState } from "vuex";
import { Toast, ImagePreview } from "vant";
import YBS from "@/assets/utils/utils.js";
// import { initBmLocation } from "@/assets/utils/bmLocation.js";
export default {
  components: {},
  data() {
    return {
      moment,
      loginInfo: {},
      repairExplain: "",
      files: [],
      resultType: this.$route.query.type,
      isPicture: this.$route.query.isPicture,
      particulars: this.$route.query.particulars,
      attachmentUrl: [], // 图片地址
      locationName: "",
      // isDevice: eval(this.$route.query.isDevice),
      isDevice: '',
      from: this.$route.query.from || "",
      whetherLocation: "",
      recordingInfo: '',
      recordingInfoFile: '',
      inspVoice: '',
    };
  },
  computed: {
    ...mapState(["h5Mode"])
  },
  created() {
    console.log(this.$route.query,'this.$route.querythis.$route.query');
    
    if(this.$route.query.isDevice) {
      this.isDevice = this.$route.query.isDevice
    }
    setTimeout(() => {
      this.power();
    }, 1000);
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    // this.locationName = localStorage.getItem("location") || ''
    this.whetherLocation = sessionStorage.getItem("whetherLocation");
  },
  mounted() {
    this.$YBS.initBmLocation(this);
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    power() {
      // 类似于storage和camera相同权限存在重复校验建议增加hasPermission包裹判断
      if (!YBS.hasPermission("storage")) {
        YBS.monitorDevicePermissions("storage", {}, function () {
          YBS.monitorDevicePermissions("camera", {});
        });
      } else {
        YBS.monitorDevicePermissions("camera", {});
      }
    },
    getApiFile(file) {
      this.files.push(
        {
          content: file.base64Data,
          status: "uploading",
          file: file.file,
          message: ''
        }
      )
      this.setFile(file)
    },
    fileUpload(MouseEvent) {
      MouseEvent.isTrusted = false
      console.log(MouseEvent);
      // this.$refs.apiPicture.uploadFile()
    },
    beforeRead() {
      return false
    },
    previewImg() {
      ImagePreview({
        images: this.files.map(i => i.content),
        showIndex: true, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    },
    // 图片选择
    async afterRead(files, detail) {
      this.setFile(files)
      // setTimeout(() => {
      //   console.log(formData);
      // }, 1000);
    },
    setFile(files) {
      let formData = new FormData();
      const allComressImage = [];
      // 每次提交所有的fileList导致服务器同等照片会存储多遍，目前multiple的兼容性并不友好，未找到合适的测试机型，如后续有可以考虑遍历files，增加当前选中的图片文件
      // this.files.forEach(i => {
      //   i.status = "uploading";
      //   allComressImage.push(this.compressImage(i));
      // });
      this.files[this.files.length - 1].status = "uploading";
      allComressImage.push(this.compressImage(this.files[this.files.length - 1]));
      formData.append("locationName", localStorage.getItem("location") || "");
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      Promise.all(allComressImage)
        .then(results => {
          results.forEach(file => {
            formData.append("file", file);
          });
          // 处理API调用的结果
          axios({
            method: "post",
            url: __PATH.IPSM_URL + "/file/upload",
            data: formData,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              Authorization: localStorage.getItem("token")
            }
          })
            .then(res => {
              if (res.data.code == 200) {
                this.files.forEach(i => {
                  return (i.status = "done");
                });
                // 单个上传
                this.attachmentUrl.push({
                  fileKey: res.data.data.fileKey,
                  name: files.file.name
                });
                // this.attachmentUrl = []
                // if (this.files.length > 1) {
                //   const imgUrl = res.data.data.fileKey.split(",");
                //   this.files.forEach((i, index) => {
                //     this.attachmentUrl.push({
                //       fileKey: imgUrl[index],
                //       name: i.file.name
                //     });
                //   });
                // } else {
                //   this.attachmentUrl.push({
                //     fileKey: res.data.data.fileKey,
                //     name: files.file.name
                //   });
                // }
                this.files.forEach(i => {
                  return (i.status = "done");
                });
              }
            })
            .catch(() => {
              this.files.forEach(i => {
                return (i.status = "failed");
              });
              this.$toast.fail("上传失败");
            });
        })
        .catch(error => {
          // 处理API调用过程中发生的错误
          this.$toast.fail("上传失败");
        });
    },
    //删除图片
    deleteImg(e) {
      this.attachmentUrl = this.attachmentUrl.filter(i => i.name != e.file.name);
    },
    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        if (this.h5Mode == "apicloud") {
          resolve(file.file);
        } else {
          new ImageCompressor(file.file, {
            quality: 0.6,
            checkOrientation: false,
            success(res) {
              let file = new window.File([res], res.name, { type: res.type });
              resolve(file);
            },
            error(e) {
              reject();
            }
          });
        }
      });
    },
    // 提交
    repairConfirm() {
      
      if (this.isPicture == "0" && this.attachmentUrl.length == 0) {
        $.toast("请上传附件图片", "text");
        return false;
      }
      if (this.files.length > 0 && this.files.some(i => i.status != "done")) {
        $.toast("请等待图片上传完成后提交", "text");
        return false;
      }
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        staffId: this.loginInfo.staffId,
        staffName: this.loginInfo.staffName,
        taskPointReleaseId: this.$route.query.taskPointReleaseId,
        taskId: this.$route.query.taskId,
        spyScan: this.whetherLocation || 1, // 定位状态
        details: this.repairExplain,
        attachmentUrl: this.attachmentUrl.length > 0 ? this.attachmentUrl.map(i => i.fileKey).join(",") : "",
        submitLocation: localStorage.getItem("location") || "",
        officeId: this.loginInfo.deptId,
        officeName: this.loginInfo.deptName,
        callerTapeUrl: this.inspVoice, // 语音
        excuteStartTime:this.$route.query.entryTime,
         timeoutDeclaration : this.$route.query.timeoutDeclaration?this.$route.query.timeoutDeclaration:""
      };
      // 无任务书
      if (this.$route.query.answerMapList == "") {
        params.state = "2"; // 合格
        params.isBookEmpty = true;
        params.platformFlag = 2;
        params.userName = this.loginInfo.staffName;
        params.userId = this.loginInfo.staffId;
      } else {
        params.state = this.$route.query.type;
        params.answerMapList = this.$route.query.answerMapList;
      }
      Toast.loading({
        message: "正在提交...",
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api
        .inspectionSubmit(params)
        .then(res => {
          // 设备巡检点
          let taskPointTypeCode = JSON.parse(this.particulars).taskPointTypeCode
          let ssmId = JSON.parse(this.particulars).ssmId
          let zdyId = JSON.parse(this.particulars).id
          const record = {
            unitCode: this.loginInfo.unitCode,
            hospitalCode: this.loginInfo.hospitalCode,
            assetsId: this.isDevice ? this.isDevice : (taskPointTypeCode === 'zdy' ? zdyId : ssmId),
            operationId: this.$route.query.taskPointReleaseId,
            operationCode: this.$route.query.systemNum, // 1:巡检 2:保养 3:报修/巡检
            operation: this.$route.query.systemNum == "2" ? "保养" : "巡检",
            record: `${this.$route.query.systemNum == "2" ? "保养" : "巡检"}单号：` + this.$route.query.taskPointReleaseId
          };
          this.$api.saveOperateRecord(record).then(res => {
            console.log(res,'resresresresresres');
          });
          Toast.clear();
          this.$toast.success("执行成功！");
          if (this.from == "equipmentManagement") {
            this.$router.go(-1);
          } else {
            this.$router.go(-2);
          }
        }).catch(err => {
          console.log(err, 'dasdasd')
          this.$toast.fail(res.message || "执行失败！");
        });
    },
    // 语音相关
    getRecord(info) {
      this.recordingInfo = info;
      if (this.h5Mode == "apicloud") {
        this.recordingInfo = info;
      } else {
        this.$api
          .voiceId2url({
            voiceUrl: info,
            sourcesFlag: "1"
          })
          .then(res => {
            console.log("voiceRes", res);
            this.recordingInfo = res.fileUrlList;
          });
      }
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
    },
    getInspRecord(info) {
      this.inspVoice = info
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>
<style lang="stylus" scoped>
.inner
  height 100%
  font-size 0.32rem
  background-color #F2F4F9
  .contenTop
    padding 0 0.32rem
    background-color #fff
    .topItem
      height 1.08rem
      display flex
      justify-content space-between
      align-items center
      border-bottom 1px solid #E5E6EB
      .itemTitle
        color #4E5969
      .itemConten
        color #1D2129
    .notBoder
      border none
  .resultText
    padding 0 0.32rem
    height 1.16rem
    line-height 1.16rem
    font-size 0.32rem
    color #86909C
  .bottom-conten
    height calc(90% - 8.32rem)
    overflow-y auto
    border-top 0.2rem solid #F2F4F9
    padding 0 0.32rem
    background-color #fff
    .situation
      height 1.08rem
      line-height 1.08rem
      color #1D2129
    .situationUplod
      height 1.08rem
      line-height 1.08rem
      display flex
      align-items center
      justify-content space-between
      .imgTitle
        color #1D2129
      .tolta
        color #86909C
    .picture-select
      display flex
  .bottom-bar
    width calc(100% - 0.64rem)
    height 1.08rem
    background-color #fff
    position fixed
    bottom 0
    padding 0.2rem 0.32rem 0 0.32rem
    display flex
    justify-content space-between
    .qualified
      width 100%
      height 0.88rem
      line-height 0.88rem
      text-align center
      background-color #3562DB
      color #fff
      border-radius 0.04rem
/deep/.van-field__control::-webkit-input-placeholder
  font-size 0.3rem
/deep/ .van-field__control
  font-size 0.32rem
</style>
