<template>
  <div class="inner">
    <Header title="巡检保养统计" @backFun="goBack"></Header>
    <div>
      <van-tabs v-model="tabCctive" offset-top="10vh" sticky title-inactive-color="#86909C" title-active-color="#1D2129"
        color="#3562DB" @click="changeTabs">
        <van-tab v-for="(item, index) in tabOption" :key="index" :name="item.id" :title="item.name" class="topContent">
          <div class="typeBer">
            <div v-for="(item, index) in btnOption" :key="index" class="typeBtn"
              :class="btnActive == index ? 'activeBtn' : ''" @click="changeBtnType(item)">
              {{ item.name }}
            </div>
            <van-calendar v-model="dateShow" type="range" @confirm="onConfirm" :min-date="new Date(1990, 0, 1)" />
          </div>
          <div class="complete">
            <div class="completeTotal">
              <span>已完成（次）</span>
              <span class="count">{{ completeCharts.completeNum }}</span>
            </div>
            <div class="completeTotal">
              <span>未完成（次）</span>
              <span class="count">{{ completeCharts.notCompleteNum }}</span>
            </div>
            <div class="completeCharts">
              <el-progress type="circle" :stroke-width="8" :percentage="completeCharts.percentage" color="#3562DB"
                style="margin-top: 0.3rem"></el-progress>
              <span class="completeText">完成率</span>
            </div>
          </div>
          <div v-if="echartData.length > 0">
            <div class="executeCharts" v-if=" index===0" ref="executeCharts1" id="executeCharts1"></div>
            <div class="executeCharts" v-if=" index===1" ref="executeCharts2" id="executeCharts2"></div>
          </div>
          <div v-else class="executeCharts notCharts">
            <span class="nullText">暂无数据</span>
          </div>
          <div class="listWrap">
            <el-table :data="tableData" height="100%" ref="tableRef">
              <el-table-column align="center" type="index" label="序号" width="50"> </el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="plan_name" label="计划名称"> </el-table-column>
              <el-table-column align="center" prop="taskSum" label="任务总数" width="50"> </el-table-column>
              <el-table-column align="center" label="已巡" width="50">
                <template slot-scope="scope">
                  <span style="color: #3562db" @click="toTaskList(scope.row)">{{ scope.row.finishNumber }}</span>
                </template>
              </el-table-column>
              <el-table-column label="完成率" width="140">
                <template slot-scope="scope">
                  <el-progress :percentage="scope.row.percentage" color="#FF7D00"></el-progress>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script>
import moment from "moment";
export default {
  name: 'assetsTaskAnalysis',
  components: {},
  data() {
    return {
      moment,
      tableData: [],
      completeCharts: {
        completeNum: "",
        notCompleteNum: "",
        percentage: 0,
      },
      echartData: [],
      btnActive: 0,
      dateType: "day",
      tabCctive: '1',
      tabOption: [
        {
          id: '1',
          name: '巡检任务统计'
        },
        {
          id: '2',
          name: '保养任务统计'
        }
      ],
      dateShow: false,//日期组件
      btnOption: [
        {
          id: 0,
          type: "day",
          name: "今日",
        },
        {
          id: 1,
          type: "week",
          name: "本周",
        },
        {
          id: 2,
          type: "month",
          name: "本月",
        },
        {
          id: 3,
          type: "year",
          name: "本年",
        },
        {
          id: 4,
          type: "custom",
          name: "自定义",
        },
      ],
      startTime: '',
      endTime: '',
    };
  },
  created() {
    this.$route.meta.keepAlive = true
    this.init();
  },
  activated() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack)
  },
  methods: {
    init() {
      // eltable滚动取消tooltipPop
      this.$nextTick(() => {
        this.$refs.tableRef[0].bodyWrapper.addEventListener('scroll', () => {
          const tooltipPop = document.querySelector('.el-tooltip__popper')
          if (tooltipPop && tooltipPop.style) {
            if (tooltipPop.style.display != 'none') {
              tooltipPop.style.display = 'none'
            }
          }
        })
      })
      this.getComplete();
      this.getChratsData();
      this.getTableData();
    },
    changeBtnType(val) {
      this.btnActive = val.id;
      this.dateType = val.type;
      if (this.dateType === 'day') {
        this.startTime = this.moment().format('YYYY-MM-DD')
        this.endTime = this.moment().format('YYYY-MM-DD')
        this.init();
      } else if (this.dateType === 'week') {
        this.startTime = this.moment().weekday(1).format("YYYY-MM-DD")
        this.endTime = this.moment().weekday(7).format("YYYY-MM-DD")
        this.init();
      } else if (this.dateType === 'month') {
        this.startTime = this.moment().startOf("month").format("YYYY-MM-DD")
        this.endTime = this.moment().endOf('month').format("YYYY-MM-DD")
        this.init();
      } else if (this.dateType === 'year') {
        this.startTime = this.moment().startOf('year').format('YYYY-MM-DD')
        this.endTime = this.moment().endOf('year').format('YYYY-MM-DD')
        this.init();
      } else {
        this.dateShow = true;
        this.echartData = []
        this.tableData = []
        this.completeCharts = {
          completeNum: "",
          notCompleteNum: "",
          percentage: 0,
        }
      }
    },
    // 日期确认
    onConfirm(date) {
      this.startTime = this.moment(date[0]).format('YYYY-MM-DD')
      this.endTime = this.moment(date[1]).format('YYYY-MM-DD')
      this.init();
      this.dateShow = false;
    },
    getComplete() {
      let params = {
        systemCode: this.tabCctive,
        type: '0'
      };
      if (this.dateType === 'custom') {
        params.taskStartTime = this.startTime
        params.taskEndTime = this.endTime
      } else {
        params.dateType = this.dateType
      }
      this.$api.taskComplete(params).then((res) => {
        res.forEach((i) => {
          if (i.name == "未巡检") {
            this.completeCharts.notCompleteNum = Number(i.value);
          } else {
            this.completeCharts.completeNum = Number(i.value);
          }
        });
        this.completeCharts.percentage = Number(((this.completeCharts.completeNum / (this.completeCharts.completeNum + this.completeCharts.notCompleteNum)) * 100).toFixed(2)) || 0;
      });
    },
    getChratsData() {
      let params = {
        systemCode: this.tabCctive,
        type: '0'
      };
      if (this.dateType === 'custom') {
        params.taskStartTime = this.startTime
        params.taskEndTime = this.endTime
      } else {
        params.dateType = this.dateType
      }
      this.$toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0,
      });
      this.$api.taskAnalysisCharts(params).then((res) => {
        this.$toast.clear();
        if (res.length) {
          this.echartData = res;
          if (this.echartData && this.echartData.length) {
            res.forEach((item) => {
              if (item.name.length > 8) {
                item.name = item.name.substr(0, 8) + "...";
              }
            });
            this.echartData = res;
            this.$nextTick(() => {
              this.initChart();
            });
          }
        } else {
          this.echartData = []
        }
      });
    },
    initChart() {
      const getchart = this.tabCctive === '1' ? this.$echarts.init(document.getElementById("executeCharts1")) : this.$echarts.init(document.getElementById("executeCharts2"))
      getchart.clear();
      getchart.setOption({
        backgroundColor: "#fff",
        color: ["rgba(246, 189, 22, 0.85)", "rgba(90, 216, 166, 0.85)", "rgba(91, 143, 249, 0.85)", "rgba(93, 112, 146, 0.85)", "#e3584e"],
        series: [
          {
            type: "pie",
            radius: "60%",
            center: ["50%", "50%"],
            data: this.echartData,
            hoverAnimation: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
              labelLine: {
                length: 20,
                length2: 16,
                show: true,
                lineStyle: {
                  color: "#ccc",
                },
              },
            },
            label: {
              position: "outside",
              formatter: "{b}",
              color: "#595959",
            },
          },
        ],
      });
    },
    getTableData() {
      let params = {
        systemCode: this.tabCctive,
        type: '0'
      };
      if (this.dateType === 'custom') {
        params.taskStartTime = this.startTime
        params.taskEndTime = this.endTime
      } else {
        params.dateType = this.dateType
      }
      this.$api.taskAnalysisList(params).then((res) => {
        this.tableData = res.map((i, index) => {
          i.index = index + 1;
          return i;
        });
      });
    },
    toTaskList(row) {
      if (row.finishNumber == 0) {
        $.toast("暂无任务", "text");
      } else {
        this.$router.push({
          path: "/completeTask",
          query: {
            from: 'analysis',
            planId: row.plan_id,
            planName: row.plan_name,
            systemNum: this.tabCctive,
            dateType: this.dateType
          },
        });
      }
    },
    goBack() {
      if(this.$route.query.from == 'inspection'){
        this.$router.go(-1);
      }else{
        this.$YBS.apiCloudCloseFrame();
      }
    },
    changeTabs() {
      this.btnActive = 0
      this.dateType = 'day'
      this.init();
    }
  },
};
</script>
<style scoped lang="stylus">
.topContent
  // padding: 0 0.32rem;
  background-color #fff !important
  .headerText
    padding 0 0.32rem
    height 1rem
    line-height 1rem
    color #1D2129
  .borderBar
    height 0.2rem
    background-color #fff
  .typeBer
    padding 0.1rem 0.12rem 0 0.12rem
    height 0.72rem
    display flex
    justify-content space-between
    align-items center
    .typeBtn
      width calc((100% / 5))
      height 100%
      display flex
      align-items center
      justify-content center
      color #4E5969
      background-color #ffffff
    .activeBtn
      background-color #E6EFFC
      color #3562DB
  .complete
    padding 0 0.32rem
    height 2.16rem
    display flex
    align-items center
    justify-content space-between
    background-color #fff
    .completeTotal
      height 1.04rem
      display flex
      flex-direction column
      justify-content space-between
      .count
        color #1D2129
        font-size 0.36rem
        font-weight bold
    .completeTotal:nth-child(2)
      margin 0 0.32rem
    .completeCharts
      position relative
      width calc(100% - 4rem)
      height 100%
      display flex
      justify-content center
      /deep/ .el-progress
        height 1.5rem
        width 1.5rem
        .el-progress-circle
          width 100% !important
          height 100% !important
      .completeText
        position absolute
        bottom 0rem
  .executeCharts
    padding 0 0.32rem
    height 28vh
    text-align center
    position relative
    .nullText
      position absolute
      bottom 1rem
      left calc(50% - 0.4rem)
  .listWrap
    height calc(62vh - 4.08rem)
    overflow-y auto
/deep/ .el-progress__text
  color #3562DB !important
/deep/ .el-table__header
  th
    background-color #E6EFFC !important
    color #1D2129 !important
/deep/ .el-progress-bar
  width 90%
</style>
