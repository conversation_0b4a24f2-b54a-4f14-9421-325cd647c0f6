<template>
  <div class="inner">
    <Header title="设备统计" @backFun="goBack"></Header>
    <div class="content">
      <div class="topStatistics">
        <div class="assetsInfo">
          <img src="@/assets/images/equipmentManagement/assets.png" alt="">
          <span class="name">设备总数</span>
          <span class="count">{{deviceNumber||0}}</span>
        </div>
      </div>
      <div class="chartContent">
        <div class="classificationBox">
          <div v-if="classificationEchartData.length > 0" id="classificationCharts" class="classificationCharts"
            ref="classificationCharts"></div>
          <div v-else class="classificationCharts notCharts">
            <span class="nullText">暂无数据</span>
          </div>
        </div>
        <div class="statusBox">
          <div v-if="statusEchartData.length > 0" id="statusCharts" class="statusCharts" ref="statusCharts"></div>
          <div v-else class="statusCharts notCharts">
            <span class="nullText">暂无数据</span>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      classificationEchartData: [],
      statusEchartData: [],
      deviceNumber: 0,
    };
  },
  created() {
    this.init()
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    init() {
      this.getAssetsStatusData()
      this.getAssetsClassificationData()
    },
    // 设备分类
    getAssetsClassificationData() {
      this.$api.getEquipmentTypeStatisticsData({}).then((res) => {
        if (res.length) {
          this.classificationEchartData = res;
          this.classificationEchartData.forEach(item => {
            this.deviceNumber += Number(item.dataSum)
          })
          this.$nextTick(() => {
            this.getPieData(this.classificationEchartData)
          });
        } else {
          this.classificationEchartData = []
        }
      });
    },
    // 设备状态
    getAssetsStatusData() {
      this.$toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0,
      });
      this.$api.getEquipmentStatusStatisticsData({}).then((res) => {
        this.$toast.clear();
        let count = res.normalAssets + res.idleAssets + res.retiredAssets + res.deactivationAsset + res.deactivationAsset + res.repairedAssets
        this.statusEchartData = [
          {
            name: '正常',
            value: res.normalAssets,
            percentage: (res.normalAssets / count) * 100
          },
          {
            name: '报废',
            value: res.idleAssets,
            percentage: (res.idleAssets / count) * 100
          },
          {
            name: '闲置',
            value: res.retiredAssets,
            percentage: (res.retiredAssets / count) * 100
          },
          {
            name: '停用',
            value: res.deactivationAsset,
            percentage: (res.deactivationAsset / count) * 100
          },
          {
            name: '待维修',
            value: res.repairedAssets,
            percentage: (res.repairedAssets / count) * 100
          }
        ]
        this.$nextTick(() => {
          this.getPieData1(this.statusEchartData)
        });
      });
    },
    getPieData1(arr) {
      const getchart = this.$echarts.init(this.$refs.statusCharts);
      const nameList = Array.from(arr, item => item.name);
      const valueList = Array.from(arr, item =>
        item.value ? item.value : 0
      );
      const percentageList = Array.from(arr, item => item.percentage.toFixed(2));
      const data = [];
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i],
          percentage: percentageList[i]
        });
      }
      getchart.clear();
      getchart.setOption({
        backgroundColor: "#fff",
        color: ['#3562DB', '#985EE1', '#FF6461', '#A0B8F6', '#08CB83', '#FF9435'],
        legend: {
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemWidth: 10,
          itemHeight: 10,
          top: "15%",
          bottom: '10%',
          right: "10%",
          formatter: function (name) {
            const res = data.filter(n => { return n.name === name })
            if (!res.length) return
            return `${name}  ${res[0].value}  ${res[0].percentage}%`
          },
          textStyle: {
            fontSize: 13,
            color: '#4E5969',
          },
        },
        tooltip: {
          trigger: 'item',
          // 关闭tooltip指示线
          axisPointer: {
            type: 'none'
          }
        },
        title: {
          text: "设备状态统计",
          left: "3%",
          top: '3%',
          textStyle: {
            fontSize: 14,
            color: "#1D2129",
          },
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2,
            normal: {
              label: {
                show: false   //隐藏文字
              },
              labelLine: {
                show: false   //隐藏指示线
              }
            },
          },
          type: "pie",
          radius: "60%",
          center: ["22%", "58%"],
          data: data,
          hoverAnimation: false,
          labelLine: {
            show: false,
            normal: {
              lineStyle: {
                color: "#4E5969" // 设置标示线的颜色
              }
            }
          },
          label: {
            normal: {
              textStyle: {
                color: "#4E5969"
              }
            }
          }
        }
      });
    },
    getPieData(arr) {
      const getchart = this.$echarts.init(this.$refs.classificationCharts);
      const nameList = Array.from(arr, item => item.baseName);
      const valueList = Array.from(arr, item =>
        item.dataSum ? item.dataSum : 0
      );
      const percentageList = Array.from(arr, item => item.dataSum ? ((item.dataSum / this.deviceNumber) * 100).toFixed(2) + '%' : '');
      const data = [];
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i],
          percentage: percentageList[i]
        });
      }
      getchart.clear();
      getchart.setOption({
        backgroundColor: "#fff",
        color: ['#3562DB', '#985EE1', '#FF6461', '#A0B8F6', '#08CB83', '#FF9435'],
        legend: {
          type: "scroll",
          pageIconSize: 14,
          orient: "vertical",
          itemWidth: 10,
          itemHeight: 10,
          top: "15%",
          bottom: '10%',
          right: "1%",
          formatter: function (name) {
            const res = data.filter(n => { return n.name === name })
            if (!res.length) return
            return `${name}  ${res[0].value}  ${res[0].percentage}`
          },
          textStyle: {
            fontSize: 13,
            color: '#4E5969',
          },
        },
        tooltip: {
          trigger: 'item',
          // 关闭tooltip指示线
          axisPointer: {
            type: 'none'
          }
        },
        title: {
          text: "设备分类统计",
          left: "3%",
          top: '3%',
          textStyle: {
            fontSize: 14,
            color: "#1D2129",
          },
        },
        series: {
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 2,
            normal: {
              label: {
                show: false   //隐藏文字
              },
              labelLine: {
                show: false   //隐藏指示线
              }
            },
          },
          type: "pie",
          radius: "60%",
          center: ["22%", "58%"],
          data: data,
          hoverAnimation: false,
          labelLine: {
            show: false,
            normal: {
              lineStyle: {
                color: "#4E5969" // 设置标示线的颜色
              }
            }
          },
          label: {
            normal: {
              textStyle: {
                color: "#4E5969"
              }
            }
          }
        }
      });
    },
    goBack() {
      this.$YBS.apiCloudCloseFrame();
    },
  }
};
</script>
<style lang="scss" scoped>
.inner {
  min-height: 100vh;
  background-color: #f2f4f9;
  .content {
    padding: 10px;
    .topStatistics {
      height: 70px;
      background-color: #fff;
      .assetsInfo {
        padding: 0 10px;
        height: 100%;
        background-color: #e6effc;
        display: flex;
        align-items: center;
        justify-content: center;
        .name {
          font-size: 14px;
          color: #86909c;
          margin: 0 10px;
        }
        .count {
          font-size: 16px;
          font-weight: bold;
          color: #1d2129;
        }
      }
    }
    .chartContent {
      .classificationCharts,
      .statusBox {
        margin-top: 10px;
      }
    }
    .notCharts {
      background: #ffffff;
    }
    .statusCharts,
    .classificationCharts {
      height: 28vh;
      text-align: center;
      position: relative;
      .nullText {
        position: absolute;
        bottom: 50%;
        left: calc(50% - 0.4rem);
      }
    }
  }
}
</style>
