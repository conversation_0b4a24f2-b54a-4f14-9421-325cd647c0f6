<template>
  <div id="taskConten-inner" class='inner'>
    <Header :title="$route.query.systemNum == '2' ? '保养内容' : '巡检内容'" @backFun="goBack"></Header>
    <div v-if="!isRepair" class="pointTitle" style="justify-content: space-between">
      <div style="display: flex;align-items: center;width: calc(100% - 54px);">
        <span style="width: 1.8rem;">{{ $route.query.systemNum == '2' ? '保养点名称' : '巡检点名称' }}</span>
        <span class="point" style="width: calc(100% - 2.28rem);">{{ pointRelease.taskPointName }}</span>
      </div>
      <span style="color: #3562db; width: 54px;" @click="toInsppointDetail">
        详情
        <van-icon name="arrow" size="14" />
      </span>
    </div>
    <div v-else class="pointTitle">
      {{ $route.query.systemNum == '2' ? '请选择巡检异常项' : '巡检点名称' }}
      <span v-if="$route.query.systemNum != '2'" style="margin-left:0.5rem">{{pointRelease.taskPointName}}</span>
    </div>
    <div class="taskBookConten">
      <!-- 日常 -->
      <div class="selectAll" v-if="isRepair">
        <span>全选</span>
        <div><van-checkbox v-model="selectAllOption"
            @click.native.stop="selectAllChange(selectAllOption)"></van-checkbox></div>
      </div>
      <van-collapse v-if="type == '0'" v-model="activeNames" :border="false">
        <van-collapse-item :is-link="false" title="" :name="index"
          v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList" :key="item.id">
          <template #title>
            <div class="inspectionConten">
              <img class="imgIcon" src="../../assets/images/equipmentManagement/轴.png" alt="">
              <span class="project">巡检项目</span>
            </div>
              <div style="color: #3562db" @click.stop="basisClick(item)">依据</div>
            <div v-if="isRepair">
              <van-checkbox v-model="repairOption[index]" @click.native.stop
                @change="radioChange(repairOption[index],index)"></van-checkbox>
            </div>
          </template>
          <!-- <div class="item-conten">{{item.detailName}}</div> -->
          <div class="daily-items">巡检内容
            <div class="item-conten">{{item.detailName}}</div>
          </div>
          <div class="daily-items">标准要求
            <div class="item-conten">{{item.maintainProjectdetails.standardRequirements}}</div>
          </div>
          <!-- <div class="daily-items">巡检依据
            <div class="item-conten">{{item.maintainProjectdetails.inspectionBasis}}</div>
          </div> -->
            <van-dialog   v-model="showDialog" :show-cancel-button="false" title="规范依据">
       <div class="dialogBasis">{{inspectionBasisDialog }}</div>
    </van-dialog>

            <!-- 新增合格/不合格单选框 -->
            <div class="quality-check" v-if="!isRepair">
              <van-radio-group v-model="item.normal" direction="horizontal">
                <van-radio name="0" checked-color="#3562DB" icon-size="16px">合格</van-radio>
                <van-radio name="1" checked-color="#3562DB" icon-size="16px">不合格</van-radio>
              </van-radio-group>
            </div>

            <!-- 新增上传照片证明 -->
            <div class="photo-proof" v-if="!isRepair">
              <div style="display: flex;vertical-align: middle;height: 100%;justify-content: space-between;align-items: center;">
                <div class="photo-proof-title">上传照片证明</div>
                <div style="display: flex;vertical-align: middle;height: 100%">
                  <div class="tolta">{{ item.proofPhotos ? item.proofPhotos.length : 0 }}/5</div>
                  <ApiGetPicture ref="apiProofPicture" @submitFile="(file) => getApiProofFile(file, index)" :limited="5" />
                </div>
              </div>
              <div class="picture-select" style="width: 100%; margin: 0 auto">
                <van-uploader ref="uploadProofImg" :disabled="h5Mode == 'apicloud'" v-model="item.proofFiles" :max-count="5" accept="image/*"
                  :after-read="(file) => afterReadProof(file, index)" @delete="(file) => deleteProofImg(file, index)" :preview-full-image="false" 
                  @click-preview="(e) => previewImg((item.proofFiles || []).map(i => i.content))">
                  <span v-if="h5Mode == 'apicloud'"></span>
                </van-uploader>
              </div>
            </div>

          <div class="step-line"></div>
        </van-collapse-item>
      </van-collapse>
      <!-- 专业 -->
      <van-collapse v-if="type == '1'" v-model="activeNames" :border="false">
        <van-collapse-item :is-link="false" title=""
          v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList" :key="item.id"
          :name="index">
          <template #title>
            <div class="inspectionConten">
              <img class="imgIcon" src="../../assets/images/equipmentManagement/轴.png" alt="">
              <!-- <span class="project">巡检项目</span> -->
              <span class="project">{{ item.detailName }}</span>
              
            </div>
             <div style="color: #3562db" @click.stop="basisClick(item)">依据</div>
            <div v-if="isRepair">
              <van-checkbox v-model="repairOption[index]" @click.native.stop
                @change="radioChange(repairOption[index],index)"></van-checkbox>
            </div>
          </template>
             <van-dialog   v-model="showDialog" :show-cancel-button="false" title="规范依据">
             <div class="dialogBasis">{{inspectionBasisDialog }}</div>
             </van-dialog>
          <div v-for="(item2, inde) in item.maintainProjectdetailsTermReleaseList" :key="item2.id">
            <div class="gist type1" v-if="item2.isNum == '1'">
              <span>{{ inde + 1 }}. {{ $route.query.systemNum == '2' ? '保养要点：' : '巡检要点：' }}{{
                  item2.content ? item2.content : ""
                }}</span>
            </div>
            <div class="gist type2" v-if="item2.isNum == '2'">
              <span>{{ inde + 1 }}. {{ $route.query.systemNum == '2' ? '保养要点：' : '巡检要点：' }}{{
                  item2.content ? item2.content : ""
                }}</span>
              <van-field v-model="item2.value" placeholder="请输入" @focus="clickField" @blur="checkField(item2)"
                :error-message="item2.error" maxlength="50" label="" rows="1" type="textarea" autosize />
            </div>
            <div class="gist type3" v-if="item2.isNum == '0'">
              <span>{{ inde + 1 }}. {{ $route.query.systemNum == '2' ? '保养要点：' : '巡检要点：' }}{{
                  item2.content
                    ? item2.content +
                      "(" +
                      item2.rangeStart +
                      "-" +
                      item2.rangeEnd +
                      (item2.einheitName ? item2.einheitName : '') +
                      ")"
                    : ""
                }}</span>
              <van-field v-model="item2.value" label="" placeholder="请输入" @focus="clickField" @blur="checkField(item2)"
                :error-message="item2.error" type="number" autosize maxlength="20">
                <div slot="extra" v-if="item2.outRange" style="color:red">输入范围异常</div>
              </van-field>
            </div>
            <div class="gist type4" v-if="item2.isNum == '3'">
              <span>{{ inde + 1 }}. {{ $route.query.systemNum == '2' ? '保养要点：' : '巡检要点：' }}{{
                  item2.content ? item2.content : ""
                }}</span>
              <van-radio-group v-model="activeChecked[index][inde]" direction="horizontal">
                <van-radio v-for="(item3, i) in item2.termJson" :key="item3.conText" :name="i" checked-color="#3562DB"
                  icon-size="16px" @click="tapRadio(item2)">{{ item3.contText }}</van-radio>
              </van-radio-group>
            </div>

            <!-- 新增上传照片证明 -->
            <div class="photo-proof" v-if="!isRepair">
              <div style="display: flex;vertical-align: middle;height: 100%;justify-content: space-between;align-items: center;">
                <div class="photo-proof-title">上传照片证明</div>
                <div style="display: flex;vertical-align: middle;height: 100%">
                  <div class="tolta">{{ item2.proofPhotos ? item2.proofPhotos.length : 0 }}/5</div>
                  <ApiGetPicture ref="apiProofPicture" @submitFile="(file) => getApiProofFile(file, index, inde)" :limited="5" />
                </div>
              </div>
              <div class="picture-select" style="width: 100%; margin: 0 auto">
                <van-uploader ref="uploadProofImg" :disabled="h5Mode == 'apicloud'" v-model="item2.proofFiles" :max-count="5" accept="image/*"
                  :after-read="(file) => afterReadProof(file, index, inde)" @delete="(file) => deleteProofImg(file, index, inde)" :preview-full-image="false" 
                  @click-preview="(e) => previewImg((item2.proofFiles || []).map(i => i.content))">
                  <span v-if="h5Mode == 'apicloud'"></span>
                </van-uploader>
              </div>
            </div>
              
          </div>
          <div class="step-line"></div>
        </van-collapse-item>
      </van-collapse>
    
    </div>
    <div v-if="!isRepair" :class="['bottom-bar ', isShowBtn ? 'disabledBtn' : '']">
      <div class="qualified qualified1" @click="confirm()">提交</div>
      <!-- <div class="qualified" @click="confirm('3')">不合格</div> -->
      <div class="qualified" @click="repair(0)">报修</div>
      <div class="qualified" @click="repair(1)">自修</div>
    </div>
    <!-- 移除报修确认按钮，因为现在直接进入报修页面 -->
  </div>
</template>
<script>
import { Toast, ImagePreview } from "vant";
import { Dialog } from 'vant';
import ImageCompressor from "image-compressor.js";
import axios from "axios";
import { mapState } from "vuex";
export default {
  data() {
    return {
      inspectionBasisDialog:"",
      showDialog:false,
      activeNames: [],
      loginInfo: {},
      taskBookType: '',
      id: '',
      type: '',
      excute: {},
      result: {},
      pointRelease: {
        maintainProjectRelease: {
          maintainProjectdetailsReleaseList: []
        }
      },
      selectAllOption: false,//全选
      activeChecked: [], // 单选默认选中
      isRepair: false, // 是否报修
      repairOption: [], // 报修项
      inspectionType: 'zdy',
      isShowBtn: true,
      isRepairSelfFlag:0,
      entryTime:"",
      whetherLocation:"",
      repairExplain:"",
    }
  },
  computed: {
    ...mapState(["h5Mode"])
  },
  created() {
     this.whetherLocation = sessionStorage.getItem("whetherLocation");
     const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    this.entryTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.id = this.$route.query.id
    this.getBookData()
  },
  mounted() {
    setInterval(() => {
      this.isShowBtn = false
    }, 3000);
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    window.onresize = function () {
      console.log('change')
    }
  },
  methods: {
  //点击依据
  basisClick(item){
    console.log(item);
    
    this.inspectionBasisDialog = (item.maintainProjectdetails && item.maintainProjectdetails.inspectionBasis !='')?item.maintainProjectdetails.inspectionBasis:'无规范依据'
     this.showDialog = true
  },
 
    // 判断手机 - ios/andriod
    isIOS() {
      const u = navigator.userAgent;
      return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    },
    clickField(e) {
      if (this.isIOS()) {
        window.addEventListener('focusin', function () {
          if (
            document.activeElement.tagName === 'INPUT' ||
            document.activeElement.tagName === 'TEXTAREA'
          ) {
            setTimeout(function () {
              document.documentElement.scrollTop = document.body.scrollHeight;
            }, 0);
          }
        });
      } else {
        document.activeElement.scrollIntoView()
      }
    },
    getBookData() {
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.id
      }
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        overlay: false,
        duration: 0
      });
      this.$api.taskBookDetail(params).then(res => {
        const { excute, result, pointRelease } = res
        
        this.excute = excute
        this.result = result
        this.pointRelease = pointRelease
        if (pointRelease.maintainProjectRelease) {
          this.type = pointRelease.maintainProjectRelease.equipmentTypeId
        }
        this.inspectionType = JSON.parse(pointRelease.particulars).taskPointTypeCode || JSON.parse(pointRelease.particulars).inspectionPointType
        pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => {
          this.activeNames.push(index)
          this.repairOption.push(false)
          
          // 初始化照片数组和normal属性
          this.$set(i, 'proofPhotos', [])
          this.$set(i, 'proofFiles', [])
          this.$set(i, 'pictureUrl', '')
          // 只为日常巡检项添加normal属性
          if (this.type == '0') {
            this.$set(i, 'normal', '')
          }
          
          if (i.maintainProjectdetailsTermReleaseList) {
            const iii = []
            i.maintainProjectdetailsTermReleaseList.forEach(j => {
              // 初始化专业巡检要点的照片数组
              this.$set(j, 'proofPhotos', [])
              this.$set(j, 'proofFiles', [])
              this.$set(j, 'pictureUrl', '')
              this.$set(j, 'normal', '') // 添加合格/不合格属性
              
              if (j.isNum == '3') {
                j.termJson.forEach((k, ind) => {
                  if (k.isDefault == '0') {
                    iii.push(ind)
                  }
                })
              } else {
                iii.push('')
              }
            })
            this.activeChecked.push(iii)
          }
        })
        Toast.clear()
      })
    },
    // 输入校验
    checkField(item2) {
      console.log('item2', item2)
      if (!item2.value || item2.value.length == 0) {
        item2.error = "内容不能为空";
      } else {
        item2.error = "";
      }
      if (item2.value &&
        (item2.value < parseInt(item2.rangeStart) ||
          item2.value > parseInt(item2.rangeEnd))
      ) {
        item2.outRange = true
      } else {
        item2.outRange = false
      }
      this.$forceUpdate();
    },
    tapRadio(item) {
      this.$forceUpdate();
    },
    repair(type) {
      if(type==1){
        this.isRepairSelfFlag = 1
      }else {
        this.isRepairSelfFlag = 0
      }
      
      // 直接进入报修页面，无需先选择
      let bookArr = [];
      const arr = [];
      
      this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, ind) => {
        bookArr.push(i);
        
        if (this.type == 0) { // 日常
          const item = {}
          item.id = i.id
          item.normal = 0
          item.inspectionBasis = i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:""
          item.templateType = this.type
          arr.push(item)
        } else {
          i.maintainProjectdetailsTermReleaseList.forEach((item2, index) => {
            if (item2.isNum == "3") {
              let selectedIndex = this.activeChecked[ind] && this.activeChecked[ind][index] !== undefined ? this.activeChecked[ind][index] : 0;
              let selectedTerm = item2.termJson && item2.termJson[selectedIndex] ? item2.termJson[selectedIndex] : {};
              let obj = {
                id: item2.id,
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                value: selectedTerm.contText || '',
                normal: selectedTerm.qualified === true ? '0' : '1',
                pictureUrl: item2.pictureUrl || '',
                templateType: this.type
              };
              arr.push(obj);
            } else if (item2.isNum != "1") {
              let obj = {
                id: item2.id,
                value: item2.value || '',
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                normal: '0',
                pictureUrl: item2.pictureUrl || '',
                templateType: this.type
              };
              arr.push(obj);
            } else if(item2.isNum == "1") {
              let obj = {
                value: '',
                id: item2.id,
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                normal: '0',
                pictureUrl: item2.pictureUrl || '',
                templateType: this.type
              };
              arr.push(obj);
            }
          })
        }
      });
      
      this.$router.push({
        path: '/inspectionFeedback',
        query: {
          bookArr,
          answerMapList: JSON.stringify(arr),
          isRepairSelf: this.isRepairSelfFlag==1?1:0,
          pointRelease: this.pointRelease,
          entryTime: this.entryTime,
          timeoutDeclaration: this.$route.query.timeoutDeclaration?this.$route.query.timeoutDeclaration:"",
          isDevice: JSON.parse(this.pointRelease.particulars).assetsId ? JSON.parse(this.pointRelease.particulars).assetsId : false
        }
      })
    },
    // 报修提交
    // 报修提交
    repairConfirm() {
      if (this.repairOption.every(i => !i)) {
        $.toast("未选择报修项", 'text');
        this.isRepair = false
      } else {
         let bookArr = [];
       const qualifiedArr = []
      const arr = []
      let outRange = false
      let hasTrue = 0
      this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, ind) => {
          if (this.repairOption[ind]) {
            bookArr.push(i);
          }
        if (this.type == 0) { // 日常
          const item = {}
          item.id = i.id
          item.normal = 0
          item.inspectionBasis = i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:""
          item.templateType = this.type
          arr.push(item)
        } else {
          i.maintainProjectdetailsTermReleaseList.forEach((item2, index) => {
            if (item2.isNum == "3") {
              qualifiedArr.push(item2.termJson[this.activeChecked[ind][index]].qualified)
              if(qualifiedArr.some(element => element == false)){
                hasTrue = 1
              }
              let obj = {
                id: item2.id,
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                value: item2.termJson[this.activeChecked[ind][index]].contText,
                normal: item2.termJson[this.activeChecked[ind][index]].qualified==true?'0':'1',
                // 添加照片URL
                pictureUrl: item2.pictureUrl || '',
                templateType: this.type
              };
              arr.push(obj);
            }  else if (item2.isNum != "1") {
              this.$forceUpdate();
              if (item2.outRange) {
                outRange = true
              }
              let obj = {
                id: item2.id,
                value: item2.value,
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                normal: (outRange||!item2.value)?'1':'0',
                // 添加照片URL
                pictureUrl: item2.pictureUrl || '',
                templateType: this.type
              };
              if(item2.isNum == "2") {
              item2.value?obj.normal = '0':obj.normal = '1'
              }
              arr.push(obj);
            }else if(item2.isNum == "1") {
               let obj = {
                value: '',
                id: item2.id,
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                normal: '0',
                // 添加照片URL
                pictureUrl: item2.pictureUrl || '',
                templateType: this.type
              };
               arr.push(obj);
            }
          })
        }
      })
        this.$router.push({
          path: '/inspectionFeedback',
          query: {
            bookArr,
             answerMapList: JSON.stringify(arr),
            isRepairSelf:this.isRepairSelfFlag==1?1:0,
            pointRelease: this.pointRelease,
             entryTime:this.entryTime,
             timeoutDeclaration : this.$route.query.timeoutDeclaration?this.$route.query.timeoutDeclaration:"",
            isDevice: JSON.parse(this.pointRelease.particulars).assetsId ? JSON.parse(this.pointRelease.particulars).assetsId : false
          }
        })
      }
      this.isRepair = false
    },
    // 合格/不合格
    confirm(type) {
      // 添加合格/不合格必填校验
      if (this.type == '0') {
        // 检查日常巡检项是否已选择合格/不合格
        const hasUnselected = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.some(item => {
          return item.normal !== '0' && item.normal !== '1';
        });
        
        if (hasUnselected) {
          this.$toast.fail('请先选择所有巡检项的合格/不合格');
          return;
        }
      }
      
      const qualifiedArr = []
      const arr = []
      let outRange = false
      let hasTrue = 0
      this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, ind) => {
        if (this.type == 0) { // 日常
          const item = {}
          item.id = i.id
          item.normal = i.normal || 0  // 使用选择的normal值，默认0
          item.inspectionBasis = i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:""
          // 添加照片URL
          item.pictureUrl = i.pictureUrl || ''
          item.templateType = this.type
          arr.push(item)
        } else {
          i.maintainProjectdetailsTermReleaseList.forEach((item2, index) => {
            if (item2.isNum == "3") {
              qualifiedArr.push(item2.termJson[this.activeChecked[ind][index]].qualified)
              console.log(qualifiedArr,'qualifiedArrqualifiedArrqualifiedArrqualifiedArr');
              
              if(qualifiedArr.some(element => element == false)){
                hasTrue = 1
              }
              let obj = {
                id: item2.id,
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                value: item2.termJson[this.activeChecked[ind][index]].contText,
                normal: item2.termJson[this.activeChecked[ind][index]].qualified==true?'0':'1',
                pictureUrl: item2.pictureUrl || '',
                templateType: this.type
              };
              arr.push(obj);
            }  else if (item2.isNum != "1") {
              if (!item2.value || item2.value.length == 0) {
                item2.error = "内容不能为空";
              } else {
                item2.error = "";
              }
              this.$forceUpdate();
              if (item2.outRange) {
                outRange = true
              }
              let obj = {
                id: item2.id,
                value: item2.value,
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                normal: outRange?'1':'0',
                pictureUrl: item2.pictureUrl || '',
                templateType: this.type
              };
              if(item2.isNum == "2") {
                obj.normal = '0'
              }
              arr.push(obj);
            } else if(item2.isNum == "1") {
               let obj = {
                value: '',
                id: item2.id,
                inspectionBasis: i.maintainProjectdetails?i.maintainProjectdetails.inspectionBasis:"",
                normal: '0',
                pictureUrl: item2.pictureUrl || '',
                isNum:'1',
                templateType: this.type
              };
               arr.push(obj);
            }
          })
        }
      })
      
      const isDevice = JSON.parse(this.pointRelease.particulars).assetsId ? JSON.parse(this.pointRelease.particulars).assetsId : false
      if (this.type == '0') { // 日常
        // 检查是否有不合格项
        const hasUnqualified = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.some(item => 
          item.normal === '1'
        );
        // 如果有一项不合格，整个结果为不合格(1)，否则为合格(0)
        this.toSubmisPage(hasUnqualified ? 3 : 2, arr, isDevice)
      } else { // 专业
        if (outRange || hasTrue ==1) {
          if (arr.some(i => !i.value&& !i.isNum)) {
            this.$toast.fail('请先完成任务书！')
          } else {
             this.toSubmisPage(3, arr, isDevice)
          }
         
        } else {
          // 校验所有填写项是否填写
          if (arr.some(i =>  !i.value&& !i.isNum)) {
            this.$toast.fail('请先完成任务书！')
          } else {
            this.toSubmisPage(2, arr, isDevice)
            // if (outRange) {
            //   this.$toast.fail('输入范围异常！')
            // } else {
            //   this.toSubmisPage(type, arr, isDevice)
            // }
          }
        }
      }
    },
    toSubmisPage(type, arr, isDevice) {
      this.$router.push({
        path: '/taskResult',
        query: {
          entryTime:this.entryTime,
          taskId: this.pointRelease.taskId,
          taskPointReleaseId: this.pointRelease.maintainProjectRelease.taskPointReleaseId,
          isPicture: JSON.parse(this.pointRelease.particulars).isPicture, // 0:拍照，1:不拍照
          particulars: this.pointRelease.particulars,// 保存纪录需要用
          type,
          systemNum: this.$route.query.systemNum,
          isDevice,
          answerMapList: JSON.stringify(arr),
          timeoutDeclaration : this.$route.query.timeoutDeclaration?this.$route.query.timeoutDeclaration:""
        }
      })
    },
    goBack() {
      sessionStorage.removeItem('isPointDetail')
      this.$router.go(-1)
    },
    // 全选
    selectAllChange(e) {
      this.repairOption = []
      if (!e) {
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => {
          this.repairOption.push(true)
        })
      } else {
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => {
          this.repairOption.push(false)
        })
      }
    },
    // 单选
    radioChange(e, index) {
      const hasEmptyString = this.repairOption.every((i) => i === true);
      if (!hasEmptyString) {
        this.selectAllOption = false
      } else {
        this.selectAllOption = true
      }
    },
    toInsppointDetail() {
      if (this.inspectionType == '2') {
        const { id, assetsId } = JSON.parse(this.pointRelease.particulars)
        this.$router.push({
          path: "/deviceInfo",
          query: {
            id,
            assetsId,
            fromInsp: 1,
            systemCode: this.$route.query.systemNum,
            taskStartTime: this.$route.query.taskStartTime
          }
        })
      } else if (this.inspectionType == '1') {
        const { id, ssmId } = JSON.parse(this.pointRelease.particulars)
        this.$router.push({
          path: "/spaceDetail",
          query: {
            id,
            ssmId,
            systemCode: this.$route.query.systemNum,
            taskStartTime: this.$route.query.taskStartTime
          }
        });
      } else if (this.inspectionType == 'zdy') {
        const { id } = JSON.parse(this.pointRelease.particulars)
        this.$router.push({
          path: "/zdyPointDetail",
          query: {
            id,
            systemCode: this.$route.query.systemNum
          }
        });
      }
    },
    // 照片证明相关方法
    // 照片选择
    afterReadProof(files, index, inde) {
      if (inde !== undefined) {
        // 专业巡检项
        this.setProofFileForProfessional(files, index, inde)
      } else {
        // 日常巡检项
        this.setProofFile(files, index)
      }
    },
    
    // 删除证明照片
    deleteProofImg(e, index, inde) {
      if (inde !== undefined) {
        // 专业巡检项
        const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
        const item2 = item.maintainProjectdetailsTermReleaseList[inde]
        item2.proofPhotos = item2.proofPhotos.filter(i => i.name != e.file.name);
        
        // 更新pictureUrl
        if (item2.pictureUrl) {
          const urls = item2.pictureUrl.split(',');
          const filteredUrls = urls.filter(url => {
            // 通过比较文件名来确定要删除的URL
            const fileName = url.split('/').pop();
            return fileName !== e.file.name.split('/').pop();
          });
          item2.pictureUrl = filteredUrls.join(',');
        }
      } else {
        // 日常巡检项
        const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
        item.proofPhotos = item.proofPhotos.filter(i => i.name != e.file.name);
        
        // 更新pictureUrl
        if (item.pictureUrl) {
          const urls = item.pictureUrl.split(',');
          const filteredUrls = urls.filter(url => {
            // 通过比较文件名来确定要删除的URL
            const fileName = url.split('/').pop();
            return fileName !== e.file.name.split('/').pop();
          });
          item.pictureUrl = filteredUrls.join(',');
        }
      }
    },
    
    getApiProofFile(files, index, inde) {
      if (inde !== undefined) {
        // 专业巡检项
        const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
        const item2 = item.maintainProjectdetailsTermReleaseList[inde]
        if (item2.proofFiles && item2.proofFiles.length === 5) {
          return this.$toast('最多上传五个附件')
        }
        if (!item2.proofFiles) {
          this.$set(item2, 'proofFiles', []);
        }
        item2.proofFiles.push(
          {
            content: files.base64Data,
            status: "uploading",
            file: files.file,
            message: ''
          }
        )
        this.setProofFileForProfessional(files, index, inde)
      } else {
        // 日常巡检项
        const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
        if (item.proofFiles && item.proofFiles.length === 5) {
          return this.$toast('最多上传五个附件')
        }
        if (!item.proofFiles) {
          this.$set(item, 'proofFiles', []);
        }
        item.proofFiles.push(
          {
            content: files.base64Data,
            status: "uploading",
            file: files.file,
            message: ''
          }
        )
        this.setProofFile(files, index)
      }
    },
    
    // 专业巡检项的照片处理
    setProofFileForProfessional(files, index, inde) {
      const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
      const item2 = item.maintainProjectdetailsTermReleaseList[inde]
      let formData = new FormData();
      const allComressImage = [];
      
      if (!item2.proofFiles) {
        this.$set(item2, 'proofFiles', []);
      }
      
      // 每次提交所有的fileList导致服务器同等照片会存储多遍，目前multiple的兼容性并不友好，未找到合适的测试机型，如后续有可以考虑遍历files，增加当前选中的图片文件
      item2.proofFiles[item2.proofFiles.length - 1].status = "uploading";
      allComressImage.push(this.compressImage(item2.proofFiles[item2.proofFiles.length - 1]));
      formData.append("locationName", localStorage.getItem("location") || "");
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      Promise.all(allComressImage)
        .then(results => {
          results.forEach(file => {
            formData.append("file", file);
          });
          // 处理API调用的结果
          axios({
            method: "post",
            url: __PATH.SPACE_API + "/lease/upload",
            data: formData,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              Authorization: localStorage.getItem("token"),
              'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
            }
          })
            .then(res => {
              if (res.data.code == 200) {
                item2.proofFiles.forEach(i => {
                  return (i.status = "done");
                });
                
                if (!item2.proofPhotos) {
                  this.$set(item2, 'proofPhotos', []);
                }
                
                // 单个上传
                item2.proofPhotos.push({
                  fileKey: this.$YBS.imgUrlTranslation(res.data.data),
                  name: files.file.name
                });
                // 将照片URL存入item2.pictureUrl
                if (!item2.pictureUrl) {
                  item2.pictureUrl = res.data.data;
                } else {
                  item2.pictureUrl += ',' + res.data.data;
                }
                item2.proofFiles.forEach(i => {
                  return (i.status = "done");
                });
              }
            })
            .catch(() => {
              item2.proofFiles.forEach(i => {
                return (i.status = "failed");
              });
              this.$toast.fail("上传失败");
            });
        })
        .catch(error => {
          // 处理API调用过程中发生的错误
          this.$toast.fail("上传失败");
        });
    },
    
    // 日常巡检项的照片处理
    setProofFile(files, index) {
      const item = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList[index]
      let formData = new FormData();
      const allComressImage = [];
      
      if (!item.proofFiles) {
        this.$set(item, 'proofFiles', []);
      }
      
      // 每次提交所有的fileList导致服务器同等照片会存储多遍，目前multiple的兼容性并不友好，未找到合适的测试机型，如后续有可以考虑遍历files，增加当前选中的图片文件
      item.proofFiles[item.proofFiles.length - 1].status = "uploading";
      allComressImage.push(this.compressImage(item.proofFiles[item.proofFiles.length - 1]));
      formData.append("locationName", localStorage.getItem("location") || "");
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      formData.append("unitCode", this.loginInfo.unitCode);
      Promise.all(allComressImage)
        .then(results => {
          results.forEach(file => {
            formData.append("file", file);
          });
          // 处理API调用的结果
          axios({
            method: "post",
            url: __PATH.SPACE_API + "/lease/upload",
            data: formData,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              Authorization: localStorage.getItem("token"),
              'hospitalSth': localStorage.getItem("hospitalSth") ? localStorage.getItem("hospitalSth") : ""
            }
          })
            .then(res => {
              if (res.data.code == 200) {
                item.proofFiles.forEach(i => {
                  return (i.status = "done");
                });
                
                if (!item.proofPhotos) {
                  this.$set(item, 'proofPhotos', []);
                }
                
                // 单个上传
                item.proofPhotos.push({
                  fileKey: this.$YBS.imgUrlTranslation(res.data.data),
                  name: files.file.name
                });
                // 将照片URL存入item.pictureUrl
                if (!item.pictureUrl) {
                  item.pictureUrl = res.data.data;
                } else {
                  item.pictureUrl += ',' + res.data.data;
                }
                item.proofFiles.forEach(i => {
                  return (i.status = "done");
                });
              }
            })
            .catch(() => {
              item.proofFiles.forEach(i => {
                return (i.status = "failed");
              });
              this.$toast.fail("上传失败");
            });
        })
        .catch(error => {
          // 处理API调用过程中发生的错误
          this.$toast.fail("上传失败");
        });
    },

    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        if (this.h5Mode == "apicloud") {
          resolve(file.file);
        } else {
          new ImageCompressor(file.file, {
            quality: 0.6,
            checkOrientation: false,
            success(res) {
              let file = new window.File([res], res.name, { type: res.type });
              resolve(file);
            },
            error(e) {
              reject();
            }
          });
        }
      });
    },

    previewImg(images) {
      ImagePreview({
        images: images,
        showIndex: true, // 是否显示页码
        closeable: false // 是否显示关闭图标
      });
    },
  }
}
</script>
<style lang="stylus" scoped>
.selectAll
  width 100%
  display flex
  margin 0.1rem 0
  justify-content flex-end
  span
    margin 0.1rem 0.2rem 0 0
  div
    margin-right 0.16rem
.inner
  height 100%
  background-color #F2F4F9
  .pointTitle
    font-size 0.32rem
    color #4E5969
    padding 0 0.32rem
    height 1.04rem
    line-height 1.04rem
    background-color #fff
    border-bottom 0.2rem solid #F2F4F9
    display flex
    .point
      display inline-block
      margin 0 0.24rem
      color #1D2129
      overflow-y auto
      white-space nowrap
  .taskBookConten
    margin 0 auto
    width calc(100% - 0.4rem)
    height calc(90% - 2.72rem)
    background-color #fff
    padding-top 0.2rem
    overflow-y auto
    border-radius 0.2rem
  .bottom-bar
    width calc(100% - 0.64rem)
    height 1.08rem
    background-color #fff
    position fixed
    bottom 0
    padding 0.2rem 0.32rem 0 0.32rem
    display flex
    justify-content space-between
    .qualified
      width 40%
      height 0.88rem
      line-height 0.88rem
      text-align center
      background-color #E6EFFC
      color #3562DB
      border-radius 0.04rem
    .repairConfirm
      width 100%
    .qualified1
      background-color #3562DB
      color #fff
    .qualified:nth-child(2)
      margin 0 0.24rem
  /deep/ .disabledBtn
    .qualified
      background-color #AAAAAA
      color #4E5969
      pointer-events none
  .qualified1
    background-color #AAAAAA
      color #4E5969
      pointer-events none
/deep/ .van-cell::after
  border none
/deep/ .van-collapse-item--border::after
  border none
/deep/.van-collapse
  background-color #fff !important
  .van-collapse-item
    background-color #fff !important
  .van-cell
    padding 0.12rem 0.32rem
  .van-cell__title
    display flex
    justify-content space-between
    align-items center
    .inspectionConten
      display flex
      justify-content space-between
      .imgIcon
        width 0.44rem
        margin-right 0.24rem
      .project
        color #1d2129
        font-size 0.3rem
  .van-collapse-item__wrapper
    background-color #fff !important
  .van-collapse-item__content
    background-color #fff !important
    padding 0 0.32rem 0 1rem
    .daily-items
      background-color #fff !important
      color #1d2129
      font-size 0.3rem
    .item-conten
      background-color #fff !important
      color #4E5969
      font-size 0.3rem
      padding 0.12rem 0
    .step-line
      width 1px
      height calc(100% - 0.72rem)
      position absolute
      top 0.64rem
      left 0.54rem
      background-color #E5E6EB
      z-index 999
    .van-radio
      padding 0.16rem 0
/deep/ .van-checkbox__icon--checked .van-icon
  background-color #3562DB
  border-color #3562DB
.dialogBasis 
    height:3.3rem
    margin: 0.3rem
    overflow: auto
/* 新增样式 */
.quality-check
  margin-top 0.2rem
  margin-bottom 0.2rem

.photo-proof
  margin-top 0.2rem
  margin-bottom 0.2rem
  .photo-proof-title
    color #1D2129
    font-size 0.32rem
    font-weight 600
    margin-bottom 0.1rem
  .tolta
    color #86909C
    margin-right 0.16rem
    display flex
    align-items center
    justify-content center

.gist
  margin-bottom 0.2rem

.picture-select
  margin-top 0.2rem
  display flex
  flex-wrap wrap
</style>
