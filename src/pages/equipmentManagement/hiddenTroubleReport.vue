<template>
  <div class="wrapper">
    <Header title="隐患上报" @backFun="goback"></Header>
    <div class="content">
      <div class="weui-cell border-rightbottom item-style" style="padding-right: 0">
        <div class="weui-cell__hd fa">
          <label :class="['weui-label', 'title', 'star-l', projectName == 'repair' ? 'star' : '']">区域</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input @click="toSelectArea" class="weui-input ipt ellipsis right-aligned-input" type="text" readonly v-model="areaVal" placeholder="请选择区域" />
          <van-icon style="margin: 0 18px" name="arrow" />
          <!-- <span class="iconfont icon-saomiao" @click="scanRepair" style="color: #4e5969; padding-right: 10px"></span> -->
        </div>
      </div>
      <van-field
        readonly
        clickable
        name="datetimePicker"
        :value="requireAccomplishDate"
        label="要求完工时间"
        right-icon="arrow"
        input-align="left"
        placeholder="请选择时间"
        @click="closingTimeFn"
      />
      <!-- 选择要求完工时间弹框 -->

      <van-field
        readonly
        clickable
        name="picker"
        :value="urgencyDegreeName"
        label="隐患等级"
        placeholder="请选择"
        @click="urgencyDegreeFn"
        required
        :rules="[{ required: true, message: '请选择隐患等级' }]"
      >
        <template #button>
          <div class="right-icon-sty">
            <van-icon name="arrow" />
          </div>
        </template>
      </van-field>

      <div class="weui-cell item-style matter-style border-rightbottom" @click="goToSelectMatterlists">
        <div class="weui-cell__hd fa">
          <label :class="['weui-label', 'title', 'star-l', projectName == 'repair' ? 'star' : '']">隐患分类</label>
        </div>
        <div class="weui-cell__bd ipt-content matter-ipt">
          <div class="ipt-pos">
            <input
              class="weui-input ipt ellipsis right-aligned-input"
              type="text"
              v-model="matterlists"
              :class="{ matterInput: matterlistsState }"
              readonly
              placeholder="请选择隐患分类"
            />
          </div>
          <span class="iconfont arrow" style="margin-left: 8px">&#xe646;</span>
        </div>
      </div>
      <!--申报描述-->
      <div class="weui-cells_form desc-form">
        <div class="er-level border-bottom">
          <div class="er-text title">描述</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea"
              :placeholder="placeholder"
              maxlength="500"
              rows="5"
              ref="textarea"
              v-model="value"
              @keydown="keydown($event)"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
        <sounds-recording v-if="h5Mode == 'apicloud'" @getRecord="getRecord" @getRecordFile="getRecordFile" @getDownLoad="getDownLoad" ref="voices" />
        <upload-voice v-else ref="voices" @getvVoiceParams="getRecord" @voiceFlag="deleteVoice" @getVoiceLocalId="translateVoice"></upload-voice>
      </div>
      <!--图片上传-->
      <upload-image class="bottom" style="position: relative" ref="imgs" @getImg="getImg" @delImg="delImg" :isRequired="projectName == 'hiddenTroubleReport'"></upload-image>
    </div>
    <div class="submit-btn" :class="{ active: canShow }">
      <button class="weui-btn weui-btn_primary" @click="submitAfterVrification">上报隐患</button>
    </div>
    <van-popup v-model="timeShow" position="bottom" :style="{ height: '40%' }">
      <van-datetime-picker v-model="currentDate" type="datetime" title="选择要求完工时间" @confirm="dateComfirm" @cancel="dateCancel" />
    </van-popup>
    <van-popup v-model="urgencyDegreeShow" round position="bottom" :style="{ height: '40%' }">
      <van-picker value-key="label" show-toolbar :columns="urgencyDegreeList" @confirm="urgencyDegreeConfirm" @cancel="urgencyDegreeCancel" />
    </van-popup>
  </div>
</template>

<script>
var h5Mode = localStorage.getItem("h5Mode");
if (h5Mode === "apicloud") {
  // 如果当前项目为apicloud内嵌H5页面，返回至原生
  var txFlashFileRecognize = api.require("txFlashFileRecognize");
}

import YBS from "@/assets/utils/utils.js";
import moment from "moment";
import UploadImage from "@/common/uploadImg/uploadImg";
import { mapState } from "vuex";
export default {
  components: {
    UploadImage
  },
  data() {
    return {
      canShow: false,
      value: "",
      placeholder: "请输入您要申报的内容描述、语音，字数限制500字以内，语音限制60秒",
      areaVal: "",
      projectName: "hiddenTroubleReport",
      localtionPlaceName: "",
      localtionPlaceCode: "",
      timeShow: false,
      currentDate: new Date(),
      requireAccomplishDate: "", //要求完工时间
      /*  紧急程度 */
      urgencyDegreeShow: false,
      urgencyDegreeList: [],
      urgencyDegreeListName: [],
      urgencyDegree: "",
      urgencyDegreeName: "",
      /*  紧急程度 */
      matterlistsObj: {}, //隐患分类

      matterlistsState: "", //隐患分类(展示ipt)
      matterlists: "", //隐患分类(展示)
      isExist: true,
      isSubmit: true, //节流标示
      imagesParams: [],
      alarmId: "",
      userType: "", //用户类型

      requiredCode: "" //必选项内容
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "changeLocation", "hospitalInfo", "openId", "serviceAreaTreeData", "h5Mode"])
  },
  activated() {
    setTimeout(() => {
      this.$YBS.apiCloudEventKeyBack(this.goback);
    }, 100);
    if (this.localtionPlaceName != this.changeLocation.localtionPlaceName && this.changeLocation.localtionPlaceName) {
      this.localtionPlaceName = this.changeLocation.localtionPlaceName;
    }

    if (JSON.stringify(this.$route.query).includes("isExist")) {
      //从选择服务区域页面跳转回来携带参数
      let params = this.$route.query;
      this.serviceSiteResId = params.code[2];
      this.localtion = params.code.join(",");
      //        判断服务区域是数组还是字符串
      if (Array.isArray(params.name)) {
        this.areaVal = params.name.join("");
        this.localtionName = params.name.join("");
      } else {
        //          最近服务区域返回的信息
        this.areaVal = params.name;
        this.localtionName = params.name;
      }
      if (this.$route.query.isExist == "false") {
        this.isExist = false;
      } else if (this.$route.query.isExist == "true") {
        this.isExist = true;
      } else {
        this.isExist = this.$route.query.isExist;
        let isHaveRoomName = this.$route.query.isHaveRoomName;
        if (this.isExist && this.changeLocation.localtionPlaceName && !isHaveRoomName) {
          this.localtionPlaceName = "";
          this.localtionPlaceCode = "";
        }
      }
      if (!this.serviceSiteResId) {
        //没选楼层
        let localtionData = this.localtion;
        this.localtion = localtionData.substr(0, localtionData.length - 1);
      }
    }
    let routeQuery = this.$route.query;
    // console.log(routeQuery)
    if (routeQuery.hospitalCode && routeQuery.id) {
      this.scanGetLocation();
      sessionStorage.setItem("unitCode", routeQuery.unitCode);
      sessionStorage.setItem("hospitalCode", routeQuery.hospitalCode);
    }
  },
  mounted() {
    this.getDialogShow();
    let _this = this;
    if (h5Mode === "apicloud") {
      // 如果当前项目为apicloud内嵌H5页面，返回至原生
      txFlashFileRecognize = api.require("txFlashFileRecognize");
      txFlashFileRecognize.init({
        appid: "1300358855",
        secretId: "AKIDwe7MTUyrLFtJbMh76Wgbx7QUQAE7lmvp",
        secretKey: "nIeR2OTN2bClz1mIpAgDqKi23CBzbYHA"
      });
      txFlashFileRecognize.addEventListener(function(ret) {
        // api.alert({ msg: JSON.stringify(ret) });
        console.log(ret);
        if (ret.eventType == "didRecognizeSuccess") {
          _this.value += ret.text;
        }
      });
    }

    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.getDictionary();
    this.alarmId = this.$route.query.alarmId || "";
    this.userType = this.staffInfo.teamId ? 2 : 1;
  },
  watch: {
    //如果route发生变化,再次执行getlistTypeName
    $route(to, from) {
      this.getlistTypeName();
    }
  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame();
    },
    getDialogShow() {
      let params = {
        userId: this.loginInfo.staffId,
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getNotCommentWarn(params).then(res => {
        if (res.commentCount > 0 && res.commentStatus == "1") {
          this.$dialog.confirm({
            message: "您当前有未评价的工单！",
            confirmButtonText: "去评价",
            cancelButtonText: "忽略",
            confirmButtonColor: "#3562db"
          })
            .then(() => {
              this.$router.push({
                path: "/orderAcceptance"
              });
            })
            .catch(() => {});
        }
      });
    },
    translateVoice(localId) {
      parent.wx.translateVoice({
        localId: localId, // 需要识别的音频的本地Id，由录音相关接口获得，音频时长不能超过60秒
        isShowProgressTips: 1, // 默认为1，显示进度提示
        success: res => {
          // alert(res.translateResult); // 语音识别的结果
          this.value = res.translateResult;
        }
      });
    },
    submitAfterVrification() {
      if (this.h5Mode == "apicloud") {
        this.isMissingItem();
      } else {
        if (this.$refs.voices.hasVoice == "recorded") {
          this.$refs.voices.saveVoice();
        } else {
          this.isMissingItem();
        }
      }
    },
    isMissingItem() {
      if (!this.loginInfo) {
        let params = {
          hospitalCode: this.hospitalInfo.hospitalCode,
          unitCode: this.hospitalInfo.unitCode,
          workTypeCode: "18"
        };
        this.getIsRequired(params);
      } else {
        let params = {
          hospitalCode: this.loginInfo.hospitalCode,
          unitCode: this.loginInfo.unitCode,
          workTypeCode: "18"
        };
        this.getIsRequired(params);
      }
    },
    getIsRequired(params) {
      this.axios
        .post(__PATH.ONESTOP + "/appOlgTaskManagement/getIsRequired", this.$qs.stringify(params), {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          res = res.data.data;
          this.requiredCode = res.requiredCode;
          this.requiredParameter(res.requiredCode);
        });
    },
    /**
     * 必选项验证
     */
    requiredParameter(value) {
      if (this.areaVal == "") {
        $.toast("请选择区域", "text");
        return;
      }
      // 校验服务事项
      if (value.indexOf("itemTypeCode", 0) != -1 && !this.matterlistsObj.itemTypeCode && this.projectName == "hiddenTroubleReport") {
        $.toast("请选择隐患分类", "text");
        return;
      }
      if (!this.value && !this.voiceParams) {
        $.toast("请填写描述或录入语音", "text");
        return false;
      }
      // 校验上传图片
      if (this.projectName == "hiddenTroubleReport" && this.imagesParams.length == 0) {
        $.toast("请上传图片", "text");
        return false;
      } else {
        this.submitFun();
      }
    },
    submitFun() {
      if (!this.isSubmit) {
        $.toast("该工单已经提交,请勿重复提交", "text");
        return;
      }

      this.isSubmit = false;
      $.showLoading("提交中…");
      let sourcesFloorName, sourcesFloor;

      if (this.otherProject == "") {
        //如果值为ipms巡检，不需要设置这两个值，直接带过来的
        if (this.changeLocation.localtionPlaceCode) {
          let arr = this.localtion.split(",");
          if (arr.length == 4) {
            arr.splice(arr.length - 1, 1);
            let str = arr.join(",");
            this.localtion = str + "," + this.changeLocation.localtionPlaceCode;
          } else {
            this.localtion = this.localtion + "," + this.changeLocation.localtionPlaceCode;
          }

          this.localtionName = this.areaVal + this.localtionPlaceName;
        }
      }

      let param = null;
      let unitCode = "";
      let hospitalCode = "";
      if (this.isScan) {
        //扫码报修，无论登录不登录都是用二维码中的医院信息
        unitCode = this.unitCode;
        hospitalCode = this.hospitalCode;
      } else {
        unitCode = this.loginInfo ? this.loginInfo.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "";
        hospitalCode = this.loginInfo ? this.loginInfo.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "";
      }
      let params = {
        // callerName:this.loginInfo.staffName,
        hospitalCode: hospitalCode,
        deptCode: this.loginInfo.deptId,
        unitCode: unitCode,
        userId: this.loginInfo.id,
        phoneNumber: this.loginInfo.phone,
        realName: this.loginInfo.staffName,
        workTypeCode: "18",
        deptName: this.staffInfo.officeName,
        questionDescription: this.value,
        jobNumber: this.staffInfo.staffNumber,
        accessToken: this.accessToken,
        // type: this.loginInfo ? this.loginInfo.type : 1,
        type: this.userType,
        job: this.staffInfo.administrativePost,
        appointmentType: this.loginInfo ? (this.reservation ? "1" : "0") : 0,
        appointmentDate: this.loginInfo ? (this.reservation ? this.$refs.serviceHours.reservation : "") : "",
        localtionName: this.localtionName,
        localtion: this.localtion,
        contactNumber: this.phone,
        itemDetailCode: this.matterlistsObj.itemDetailCode,
        itemDetailName: this.matterlistsObj.itemDetailName,
        itemServiceCode: this.matterlistsObj.itemServiceCode,
        itemServiceName: this.matterlistsObj.itemServiceName,
        itemTypeCode: this.matterlistsObj.itemTypeCode,
        itemTypeName: this.matterlistsObj.itemTypeName,
        sourcesFloorName: sourcesFloorName,
        sourcesFloor: sourcesFloor,
        staffId: this.staffInfo.staffId,
        workSources: "1", //巡检为3,其余为1
        callerCompanyCode: this.loginInfo ? this.loginInfo.companyCode : "", //用于巡检
        callerCompanyName: this.loginInfo ? this.loginInfo.companyName : "", //用于巡检

        contactName: this.contactName, //未登录扫描报修加入的联系人
        dispatchingConfig: this.requireCode, //	移动申报自动派工标识
        requireAccomplishDate: this.requireAccomplishDate //要求完工时间
      };
      if (this.alarmId) {
        params.sysForShort = this.alarmId;
      }
      params.callerTape = this.voiceParams;
      params.attachment = JSON.stringify(this.imagesParams);
      param = this.$qs.stringify(params);
      this.axios
        .post(__PATH.ONESTOP + "/appOlgTaskManagement.do?saveTaskByWeChat", param, {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(this.handleRequestSucc);
    },
    /**
     * 工单提交成功处理函数
     * @param res
     */
    handleRequestSucc(res) {
      $.hideLoading();
      if (res.data.code == "200") {
        $.toast("工单提交成功", "success", () => {
          sessionStorage.removeItem("hospitalCode");
          sessionStorage.removeItem("unitCode");
          sessionStorage.removeItem("url");
          this.$YBS.apiCloudCloseFrame();
        });
        // setTimeout(() => {
        //   // api.closeFrame();
        //   this.$YBS.apiCloudCloseFrame();
        // }, 1000);
      } else {
        $.toast(res.data.message, "text");
        if (res.data.code == 40001) {
          this.voiceParams = "";
          this.$refs.voices.handleDelVoiceClick();
        }
      }
      // $.hideLoading();
    },
    getImg(img) {
      console.log("img", img);
      this.imagesParams.push(img);
    },
    delImg(url) {
      this.imagesParams = this.imagesParams.filter(item => {
        return item != url;
      });
    },
    //空格校验
    keydown(event) {
      if (event.keyCode == 32) {
        event.returnValue = false;
      }
    },
    getDownLoad(path) {
      this.recognize(path);
    },
    recognize(path) {
      // console.log(txFlashFileRecognize);
      txFlashFileRecognize.recognize(
        {
          path: path
        },
        function(ret) {}
      );
    },
    getRecord(info) {
      this.recordingInfo = info;
      this.voiceParams = info;
      console.log("recordingInfo", this.recordingInfo);
      if (this.h5Mode == "apicloud") {
        this.voiceParams = info;
      } else {
        // this.recordingInfo = info;
        this.$api
          .voiceId2url({
            voiceUrl: info,
            sourcesFlag: "1"
          })
          .then(res => {
            console.log("voiceRes", res);
            this.voiceParams = res.fileUrlList;
            this.isMissingItem();
          });
      }
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
      console.log("recordingInfoFile", this.recordingInfoFile);
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    // 接收服务事项的数据
    getlistTypeName() {
      if (this.$route.query.source == "service") {
        let typelist = this.$route.query.item || {};
        if (!typelist.itemServiceName) return;
        this.matterlistsObj = typelist;
        this.matterlists = typelist.itemTypeName + "-" + typelist.itemDetailName + "-" + typelist.itemServiceName;
      } else {
        return;
      }
    },
    goToSelectMatterlists() {
      let unitCode = this.unitCode ? this.unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "";
      let hospitalCode = this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "";
      this.$router.push({
        path: "/matterlists",
        query: {
          workTypeCode: 18,
          projectName: this.projectName,
          unitCode: unitCode,
          hospitalCode: hospitalCode
        }
      });
    },
    getDictionary() {
      let params = {
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode
      };
      this.$api.getDictionary(params).then(res => {
        this.faultGradeList = res.faultGrade;
        this.faultGradeName = res.faultGrade[0].label;
        this.urgencyDegreeList = res.urgencyDegree;
        this.urgencyDegreeName = res.urgencyDegree[0].label;
        if (sessionStorage.getItem("recordDetail")) {
          const recordDetail = JSON.parse(sessionStorage.getItem("recordDetail"));
          this.faultGradeName = recordDetail.faultGradeName;
          this.urgencyDegreeName = recordDetail.urgencyDegreeName;
          sessionStorage.removeItem("recordDetail");
        }
      });
    },
    urgencyDegreeConfirm(value, index) {
      this.urgencyDegreeCancel();
      // this.urgencyDegree = this.urgencyDegreeList[index].value;
      this.urgencyDegreeName = value.label;
      this.urgencyDegree = value.sort;
    },
    urgencyDegreeCancel() {
      this.urgencyDegreeShow = false;
    },
    // 紧急程度
    urgencyDegreeFn() {
      this.urgencyDegreeShow = true;
    },
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD HH:mm:ss");
    },
    //时间弹窗

    dateComfirm(e) {
      this.requireAccomplishDate = this.formatDate(e);
      this.dateCancel();
    },
    dateCancel() {
      this.timeShow = false;
    },
    closingTimeFn() {
      this.timeShow = true;
    },
    toSelectArea() {
      //级联弹窗数据还原
      this.localtionPlaceName = "";
      this.localtionPlaceCode = "";
      this.$router.push({
        path: "/selectArea",
        query: {
          routerPath: this.$route.path
        }
      });
    },
    scanRepair() {
      if (!YBS.hasPermission("camera")) {
        var pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function() {
          YBS.reqPermission(["camera"], function(ret) {});
        });
        return;
      }

      try {
        YBS.scanCodeNew(true).then(
          item => {
            console.log(item, "ittt");
            if (item[0] == "KJ") {
              this.scanRepairCallBackNew(item);
            } else {
              this.scanRepairCallBack(item);
            }
          },
          () => {
            this.$toast.fail("无效二维码!");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    scanRepairCallBackNew(scanData) {
      let roomCode = scanData[2];
      if (roomCode) {
        this.$api.lookUpById({id: roomCode}).then(res => {
          this.localtion = res.simCode.split(",").slice(-3).join(",");
          this.areaVal = res.simName.split(">").slice(-3).join("");
          this.localtionName = this.areaVal;
        })
        return
        this.axios
          .get(__PATH.BASE_API + "/space/spaceInfo/lookUpById", {
            params: {
              id: roomCode
            },
            headers: {
              hospitalCode: this.hospitalInfo.hospitalCode,
              unitCode: this.hospitalInfo.unitCode
            }
          })
          .then(res => {
            if (res.data.code == 200) {
              this.localtion = res.data.data.simCode
                .split(",")
                .slice(-3)
                .join(",");

              this.areaVal = res.data.data.simName
                .split(">")
                .slice(-3)
                .join("");
              this.localtionName = this.areaVal;
            }
          });
      }
    },
    scanRepairCallBack(scanData) {
      let result = scanData;
      let unitCode = this.unitCode ? this.unitCode : sessionStorage.unitCode;
      let hospitalCode = this.hospitalCode ? this.hospitalCode : sessionStorage.hospitalCode;
      if (result[1]) {
        this.axios
          .post(
            __PATH.BASE_API + "/space/api/getHospitalSpaceInfoById",
            this.$qs.stringify({
              unitCode: unitCode || this.loginInfo.unitCode,
              hospitalCode: hospitalCode || this.loginInfo.hospitalCode,
              id: result[1]
            }),
            {
              headers: {
                Authorization: "Bearer " + localStorage.getItem("token")
              }
            }
          )
          .then(data => {
            let res = data.data;
            if (res.code == 200) {
              let staffGrid = res.data;
              if (staffGrid.allParentId) {
                let placeNameArr = staffGrid.allParentName.split(">");
                let placeCodeArr = staffGrid.allParentId.split(",");
                //增加未完工工单列表
                let lastAreaCode = placeCodeArr[placeCodeArr.length - 1];
                this.$api
                  .getUnfinishedMineTask({
                    userId: this.loginInfo.id,
                    // staffType: this.loginInfo.type,
                    staffType: this.userType,
                    type: "1,2,3,4",
                    curPage: 1,
                    pageSize: 999,
                    startTime: "",
                    endTime: "",
                    areaCode: lastAreaCode,
                    staffId: this.staffInfo.staffId
                  })
                  .then(resP => {
                    if (this.userType == 2) {
                      if (resP.details.length > 0) {
                        $.toast("当前区域中有未完成工单，待完工后再进行扫码申报！", "text");
                        this.$router.push("/notCompleteOrderList?areaCode=" + lastAreaCode);
                      }
                    } else if (this.userType == 1) {
                      if (resP.length > 0) {
                        $.toast("当前区域中有未完成工单，待完工后再进行扫码申报！", "text");
                        this.$router.push("/notCompleteOrderList?areaCode=" + lastAreaCode);
                      }
                    }
                  });

                this.serviceSiteResId = staffGrid.id; //服务区域id
                this.localtion = staffGrid.allParentId; //服务区域id
                if (placeCodeArr.length == 4) {
                  this.localtionPlaceName = placeNameArr.pop();
                } else {
                  this.localtionPlaceName = "";
                }
                this.areaVal = placeNameArr.join(""); //服务区域名称,展示的
                this.localtionName = staffGrid.allParentName.replaceAll(">", "", false); //服务区域名称
              } else {
                $.toast("未找到相关位置", "text");
              }
            }
          });
      } else {
        $.toast("请检查二维码是否正确", "text");
      }
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';
.wrapper {
  height: 100%;
  background-color: $bgColor;

  .content {
    box-sizing: border-box;
    width:100%;
    overflow: hidden;
    min-height: 92%;
    padding-bottom: 76px !important;
    background-color: $bgColor;

    .reservation-btns {
      marginBottom20();
      text-align: center;

      span {
        display: inline-block;
        width: 1.24rem;
        margin: 0 0.36rem;
      }

      .active {
        color: $color;
        border-bottom: 2px solid $color;
      }
    }

    .other-location-name {
      marginBottom20();

      .ipt-content {
        justify-content: flex-end;
        line-height: 1.2;
        text-align: left;
      }
    }

    .adjustable {
      padding: 0 0.32rem;

      > div {
        text-align: center;
      }

      img {
        // width: 0.48rem;
        width: 14px;
      }

      .down {
        transition: all 0.3s;
      }

      .up {
        transform: rotateZ(180deg);
        transition: all 0.3s;
      }
    }

    .up-content {
      transform: rotateX(90deg);
      transform-origin: center top;
      height: 0;
      transition: height 0.3s;
      transition: all 0.3s;
    }

    .down-content {
      transition: all 0.3s;
      transform-origin: center top;
      margin-bottom: 10px;
    }

    .er-level {
      display: flex;
      align-items: center;
      itemBaseStyle();

      .weui-cells_checkbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .redio-content {
          display: flex;
          color: $contentColor;

          .declare-attr {
            padding: 0;
          }
        }
      }
    }

    .hosp {
      marginBottom20();
    }

    .item-style {
      itemBaseStyle();

      .login-tips {
        line-height: 1;
        white-space: nowrap;
        font-size: 0.28rem;
        font-style: italic;
        color: $color;
      }
    }

    .matter-style {
      min-height: 0.98rem;
      line-height: initial;
    }

    .desc-form {
      margin-top: 0;
      background-color: #fff;

      .desc {
        padding: 0.32rem 0.32rem 0;
        font-size: 0.28rem;
        line-height: 1.5em;
        margin-bottom: 0.2rem;
        color: $contentColor;

        textarea {
          display: inline-block;
          text-indent: 2em;
          font-size: 0.3rem;
        }
      }
    }

    .tips {
      color: $contentColor;
      text-align: center;
      margin: 0.6rem 0;

      .key-tips {
        color: $color;
      }
    }
  }
}

.btn {
  padding: 0 1.12rem 0.3rem;
  position: relative;
  user-select: none;

  .delete-voice {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 0.5rem;
  }

  .play-btn {
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 0.3rem;
      position: absolute;
      left: 0.24rem;
    }
  }
}

.recording {
  position: fixed;
  z-index: 2;
  top: 2rem;
  left: 50%;
  margin-left: -1.2rem;

  img {
    width: 2.4rem;
    opacity: 0.7;
  }
}

.title-content {
  itemBaseStyle();
  margin-top: $marginb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ipt-content {
  text-align: right;
  display: flex;
  align-items: center;
  color: #999;
}

.matter-ipt {
  display: flex;
  justify-content: flex-end;

  .ipt-pos {
    position: relative;
    width: 100%;

    .matterInput {
      display: none;
    }

    span {
      font-family: serif;
      text-align: left;
      display: flex;
      font-size: 15px;
    }
  }
}

.img-content {
  background: #fff;
  padding: 0 0.3rem;
  display: flex;
  justify-content: space-around;

  .img-wrapper {
    flex: 1;
    width: 0;
    margin: $marginb;
    position: relative;

    img {
      width: 100%;
    }

    .icon-img {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.submit-btn {
  padding: 0.22rem 0.32rem;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: 66px;
  margin-top: -66px;
  position: fixed;
  bottom: 0;
  z-index: 999;
}

.popups-content {
  width: 80%;
  margin: 0 auto;
  position: fixed;
  max-width: 300px;
  border-radius: 3px;
  overflow: hidden;

  & > div {
    margin: 0 auto;
    background: #fff;
    padding-bottom: 15px;
    border-bottom: 1px solid #efefef;
    height: 1.18rem;
    align-items: flex-end;
    box-sizing: border-box;
  }

  .cfm-phone-num-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 48px;
    font-size: 18px;
    padding: 0;
    color: #3562DB;
  }
}

.play-void-img {
  position: fixed;
  width: 35%;
  top: 50%;
  left: 50%;
  margin-left: -70px;
  z-index: 99999;
}

.title {
  font-size: 0.32rem;
}

.er-level-redio {
  display: flex;
  background-color: #fff;
  font-size: 0.3rem;
  color: $contentColor;
}

.redio-content {
  position: relative;

  .select-time {
    position: absolute;
    left: 1rem;
    bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 5em);

    .text {
      width: 50px;
      line-height: initial;
    }

    .ipt {
      flex: 1;
      text-align: right;
    }
  }

  .textColor {
    color: #999;
  }

  .disPointer {
    pointer-events: none;
  }
}

.hint {
  display: inline-block;
  margin: 0 3px;
}

.reminder{
  height:0.68rem;
  background:#FAECE9;
  padding-left:.32rem;
  line-height:.68rem;
  .reminder-img{
    width:.28rem;
  }
  .reminder-text{
    margin-left:.24rem;
    color:#FF7859;
    font-size:.26rem;
  }

}

.ellipsis{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.hide-box {
  height: 36px;
  background-color: #fff;
  border-radius: 100px 100px 100px 100px;
  border: 1px solid #E5E6EB;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.icon-saomiao {
  font-size: 20px;
}
.right-aligned-input::placeholder {
  text-align: right;
  color: #86909C;
}
.weui-textarea::placeholder {
  color: #86909C;
}
.star::before {
  position: absolute;
  left: -10px;
  top: 5px;
  content:'*';
  color:red;
}
.fa {
  position: relative;
}
.star-l::before {
  position: absolute;
  left: -9px;
  top: 5px;
  content:'*';
  color:red;
}
>>> .van-field__label{
  font-size:0.32rem;
  color:#353535
}
>>> .van-field__control{
  font-size:0.32rem;

}
</style>
