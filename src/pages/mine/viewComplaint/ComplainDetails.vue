<template>
  <div class="complain-content">
    <div class="header">
      <div class="iconfont">&#xe681;</div>
      <div class="title-body">
        <span class="dot"></span>
        <span class="title">{{ complainInfo.operationType }}</span>
        <span class="time">{{ timeFunc(complainInfo.createDate,'.') }}</span>
      </div>
    </div>
    <div class="content">
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">身份隐匿</label></div>
        <div class="weui-cell__bd content-css">{{complainInfo.signatureFlag == 1?'匿名投诉':'署名投诉'}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">申报人</label></div>
        <div class="weui-cell__bd content-css">{{complainInfo.signatureFlag == 1?'***':complainInfo.callerName}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">申报人工号</label></div>
        <div class="weui-cell__bd content-css">{{complainInfo.signatureFlag == 1?'***':complainInfo.callerJobNum}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">申报人职称</label></div>
        <div class="weui-cell__bd content-css">{{complainInfo.signatureFlag == 1?'***': complainInfo.callerJob }}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">电话</label></div>
        <div class="weui-cell__bd content-css">{{complainInfo.signatureFlag == 1?'***':complainInfo.sourcesPhone}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">来电号码</label></div>
        <div class="weui-cell__bd content-css">{{complainInfo.signatureFlag == 1?'***':complainInfo.needPhone}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">关联工单</label></div>
        <div
          class="weui-cell__bd content-css relevance-num"
          v-if="complainInfo.relevanceWorkNum"
          @click="checkDetailInfo(complainInfo.relevanceWorkNum)"
        >
         {{complainInfo.relevanceWorkNum}}
        </div>
        <div
            class="weui-cell__bd content-css"
            v-else
        >

        </div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">关联班组</label></div>
        <div class="weui-cell__bd content-css">{{ complainInfo.designateDeptName }}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">投诉标题</label></div>
        <div class="weui-cell__bd content-css complain-title">{{complainInfo.complaintTitle}}</div>
      </div>
      <div class="weui-cell item border-rightbottom complain-desc">
        <div class="weui-cell__hd"><label class="weui-label title">投诉描述</label></div>
        <div class="weui-cell__bd content-css">{{complainInfo.questionDescription}}</div>
      </div>
      <!--录音-->
      <!-- 中心申报不显示录音 pc
        pc端配置不显示的时候录音隐藏 isShowCallerTape
      -->
      <div class="voice-desc border-bottom" v-if="complainInfo.workSources != 2 && complainInfo.isShowCallerTape=='true' ">
        <!--<div>语音附件</div>-->
        <span class="text-title title">录音</span>
        <div v-if="complainInfo.callerTapeUrl == ''" class="content-css"></div>
        <div v-if="complainInfo.callerTapeUrl != ''" class="voice-content">
          <div class="voice-btn">
            <button
                class="weui-btn weui-btn_primary"
                v-if="play"
                @click="handlePlayAudioClick"
            >
              <div class="play-icon">
                <img src="~images/icon_wify2.png" class="play-img">
                <span>{{audioDuration}}</span>
              </div>
              点击播放
            </button>
            <button
                class="weui-btn weui-btn_primary"
                v-else
                @click="handlePauseAudioClick"
            >
              <div class="play-icon">
                <img src="~images/icon_wify1.gif" class="play-img">
                <span>{{audioDuration}}</span>
              </div>
              正在播放
            </button>
          </div>
        </div>
      </div>
      <!--附件-->
      <div class="img-desc" v-if="complainInfo.workSources == 1">
        <div class="text-title item border-rightbottom img-desc-title">
          <span class="title">图片附件</span>
          <span v-if="complainInfo.attachment.length == 0" class="content-css"></span>
        </div>
        <div class="img-content">
          <div
              class="img-wrapper"
              v-for="item of complainInfo.attachment"
          >
            <div class="img-box">
              <img
                  v-preview="item"
                  :src="item"
                  class="img"
                  preview-title-enable="true"
                  preview-nav-enable="true"
                  preview-top-title-tnable="true"
                  preview-title-extend="false"
              >
            </div>
          </div>
        </div>
      </div>
     <div class="height-placeholder" style="height:38px"></div>
    </div>
    <audio
        class="audio"
        ref="audio"
        @canplay="handleCanPlay"
        @ended="handleAudioEnded"
        :src="complainInfo.callerTapeUrl"
    ></audio>
  </div>
</template>

<script type=text/ecmascript-6>
  import {mapState} from 'vuex'
  import Global from "@/utils/Global.js"
  export default {
    name: "ComplainDetails", //后勤投诉工单详情
    computed: {
      ...mapState(['loginInfo'])
    },
    data () {
      return {
        complainInfo:{},
        play: true,
        isCallerTapeUrl: true,
        audioDuration: '',
        taskId:''
      }
    },
    methods: {
      /**
       * 获取详情请求
       * @param taskId 工单id
       */
      getComplainDetailsInfo (taskId) {
        $.showLoading()
        this.axios.get(__PATH.ONESTOP + '/appOlgTaskManagement.do?getTaskDetail', {
              params: {
                hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
                unitCode: this.loginInfo.userOffice[0].unitCode,
                taskId: taskId
              }
            })
            .then(this.getComplainDetailsInfoSucc)
      },
      /**
       * 详情请求成功处理函数
       */
      getComplainDetailsInfoSucc (res) {
        if (res.data.code == 200){
          $.hideLoading()
          this.complainInfo = res.data.data[0]
          this.timeFunc = Global.timestampToTime
        }else{
          $.toast(res.data.message,'text')
        }
      },
      /**
       * 点击关联工单号查看详情
       */
      checkDetailInfo (taskNum) {
        this.$router.push({
          name:'ShowDetails',
          params:{
            taskNum:taskNum
          }
        })
      },
      /**
       * 时间格式化载体
       */
      timeFunc () {},
      /**
       * 播放音频
       */
      handlePlayAudioClick () {
        this.play = false
        const audio = this.$refs.audio
        audio.play();
      },
      handleCanPlay () {
        this.audioDuration = Math.round(this.$refs.audio.duration) + '″'
      },
      /**
       * 暂停播放音频
       */
      handlePauseAudioClick () {
        this.play = true
      },
      /**
       *音频播放结束事件
       */
      handleAudioEnded () {
        this.play = true
      },
    },
    mounted () {
      this.taskId = this.$route.params.taskId
      this.getComplainDetailsInfo(this.taskId)
    },
    activated () {
      if(this.taskId != this.$route.params.taskId && this.$route.params.taskId){
        this.taskId = this.$route.params.taskId
        this.getComplainDetailsInfo(this.taskId)
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .complain-content
    overflow: hidden
    .header
      height: .57rem
      display: flex
      align-items: center
      margin: .15rem 0 .15rem .3rem
      background: #fff
      .iconfont
        font-size: .4rem
        color: $btnColor
      .title-body
        height: .6rem
        display: flex
        align-items: center
        margin-left: 10px
        background: #eceef8
        padding: 0 .24rem
        border-radius: .3rem
        white-space: nowrap
        .dot
          display: inline-block
          width: .09rem
          height: .09rem
          background: $btnColor
        .title
          font-size: .28rem
          font-weight: 700
          margin: 0 .45rem 0 .08rem
        .time
          font-size: .3rem
          color: #4F87FB
    .content
      timelineContent()
      border: 0
      .voice-desc
        display: flex
        align-items: center
        padding: .54rem .32rem
        background-color: #fff
        font-size: .32rem
        .text-title
          width: 105px
        .voice-content
          width: 70%
          padding-left: 10px
          button
            display: flex
            align-items: center
            justify-content: center
            .play-icon
              display: flex
              position: absolute
              align-items: center
              left: 0
              justify-content: center
              padding-left: 5px
              .play-img
                width: .4rem
              span
                font-size: 14px
                padding-left: 5px
    .img-desc
      .text-title
        line-height: 1rem
        padding-left: .32rem
      .img-desc-title
        display: inline-flex
        .title
          display: inline-block
          width: 105px
      .img-content
        background: #fff
        padding: 0 .3rem
        display: flex
        .img-wrapper
          width: 30%
          height: 1.4rem
          margin: .1rem
          position: relative
          .img-box
            height: 100%
            overflow: hidden
            .img
              width: 100%
    .audio
      display: none
    .item
      itemBaseStyle()
      display: flex
      justify-content: space-between
      font-size: .32rem
      .relevance-num
        color: $color
      .complain-title
        line-height: 1.5em
    .complain-desc
      min-height:.98rem
      line-height: 1.3
      height:auto
  .content-css
    color: $contentColor
</style>
