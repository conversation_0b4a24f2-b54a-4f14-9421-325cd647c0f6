<template>
  <div class="complaint-list-wrapper">
    <div class="time-content border-bottom" @click="selectTime">
      <span class="time-text">筛选</span>
      <span class="time-wrapper">
        <span class="start">{{ startTimeText }}</span>
      -
      <span class="end">{{ endTimeText }}</span>
      </span>
    </div>
    <div class="select-area"  v-if="isHaveData">
      <pull-to :bottom-load-method="refresh" :is-top-bounce=false>
        <div class="weui-cells_radio">
        <ul class="region">
          <li
              class="border-bottom"
              v-for="(item,index) of dataArr"
              :key="item.taskId"
              @click="enterDetailInfo(item.taskId)"
          >
              <input type="radio" class="weui-check" name="radio1">
            <div class="left">
              <img src="~images/ic-complain.png" class="ic-style-img">
            </div>
            <div class="middle">
              <div class="middel-text">
                投诉标题：{{item.complaintTitle}}
              </div>
              <div class="time-text-content">
                <img src="~images/ic-time.png" class="ic-time-img">
                <span class="time-text">{{item.createDate}}</span>
              </div>
            </div>
          </li>
        </ul>
        </div>
      </pull-to>
    </div>

    <div v-if="!isHaveData" class="img-content">
      <img src="@/assets/images/noDataDefault/search-empty.png" alt="">
      <p>暂时没有工单信息~</p>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import global from '@/utils/Global'
  import PullTo from 'vue-pull-to'
  import {mapState} from 'vuex'
  export default {
    name: "ComplaintList",
    components: {
      PullTo
    },
    computed: {
      ...mapState(['loginInfo','staffInfo'])
    },
    data () {
      return {
        flag: false,
        isHaveData: true,
        dataArr: [],
        curPage: 0,
        startTime:'',
        startTimeText:'选择初始时间',
        endTime:'',
        endTimeText:'选择终止时间'
      }
    },
    methods: {
      /**
       * 上拉加载更多
       */
      refresh(loaded){
        this.getOrderList()
        if (this.flag) {
          loaded('done')
          this.flag = false
        }
      },
      getOrderList () {
        $.showLoading()
        let lastPage = this.curPage
        this.curPage = ++lastPage
        this.axios.get(__PATH.ONESTOP + '/appOlgTaskManagement.do?' + sessionStorage.sourceMethodName,{
          params:{
            hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
            unitCode: this.loginInfo.userOffice[0].unitCode,
            userId: this.loginInfo.id,
            // staffType: this.loginInfo.type,
            staffType: this.staffInfo.teamId ? 2 : 1,
            curPage: this.curPage,
            pageSize: 15,
            startTime:this.startTime,
            endTime:this.endTime,
            staffId:this.staffInfo.staffId
          }
        })
            .then(this.getOrderListSucc)
      },
      /**
       * 工单列表请求成功函数
       */
      getOrderListSucc (res) {
        let data = res.data
        if(data.code == 200){
          if(data.details.length == 0){
            $.hideLoading()
            //第一次请求或者由时间查询结构
            if(this.dataArr.length == 0 || this.curPage == 1){
              this.dataArr = []
              this.isHaveData = false
            }
            $.toast('没有更多的数据啦', 'text')
          }else{
            this.isHaveData = true
            if (data.code == 200) {
              $.hideLoading()
              this.dataArr = this.dataArr.concat(data.details)
            } else {
              $.toast(res.data.message, 'text')
            }
          }
          this.flag = true
        }else{
          $.toast(res.data.message,'text')
        }
      },
      /**
       * 进入投诉工单详情页的入口
       */
      enterDetailInfo (taskId) {
        this.$router.push({
          name:'ComplainDetails',
          params:{
            taskId:taskId,
          }
        })
      },
      /**
       * 进入时间筛选
       */
      selectTime () {
        this.$router.push('/time')
      },
      /**
       * 将-的时间格式化成年、月、日形式
       * @param time
       */
      formatTime (time) {
        let timeStr = '';
        if(time.includes('/')){
          timeStr = time.split('/')
        }else{
          timeStr = time.split('-')
        }
        return timeStr[0] + '年' + timeStr[1] + '月' + timeStr[2] + '日'
      }
    },
    mounted () {
      if(this.$route.params.source){//从个人中心的后勤投诉或者我的投诉进入
        if(this.$route.params.source == 'hqts'){
          sessionStorage.sourceMethodName = 'getMineByComplaint'
        }
        if(this.$route.params.source == 'wdts'){
          sessionStorage.sourceMethodName = 'getMineComplaint'
        }
      }
      this.getOrderList()
    },
    activated () {
      try {
        if(!sessionStorage.selectTime){
          this.startTimeText ='选择初始时间'
          this.endTimeText = '选择终止时间'
          this.startTime = ''
          this.endTime = ''
          return this.getOrderList()
        }
        if(JSON.parse(sessionStorage.selectTime).count == 1){
          let time = JSON.parse(window.sessionStorage.selectTime).time
          let time1 = time.split('-')[0]  //2018/11/04
          let time2 = time.split('-')[1]
          this.startTimeText = this.formatTime(time1)
          this.endTimeText = this.formatTime(time2)
          this.startTime = time1.replace('/','-').replace('/','-')
          this.endTime = time2.replace('/','-').replace('/','-')

          this.curArea = sessionStorage.selectType
          this.curPage = 0
          this.dataArr = []
          this.getOrderList()
        }
      }catch (error){}
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .vue-pull-to-wrapper
    width: 100%

  .complaint-list-wrapper
    background-color: $bgColor
    height: 100%
    overflow: hidden
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    .time-content
      font-size: .34rem
      height: .98rem
      background-color: #fff
      padding: 0 .32rem
      display: flex
      align-items: center
      z-index: 2
      .time-text
        display: inline-block
        width: 1.4rem
      .time-wrapper
        flex: 1
        display: flex
        justify-content: space-between
    .select-area
      width: 100%
      height: calc(100% - 1.08rem)
      background: transparent
      display: flex
      position: absolute
      top: 1.08rem
      .region
        background-color: #fff
        width: 100%
        li
          height: 1.4rem
          display: flex
          align-items: center
          font-size: .3rem
          padding: 0 .32rem
          .left
            .ic-style-img
              width: .68rem
          .middle
            flex: 1
            padding-left: .38rem
            overflow: hidden
            .middel-text
              padding: .1rem 0
              overflow: hidden
              white-space: nowrap
              text-overflow: ellipsis
            .time-text-content
              color: #c2c2c2
              padding: .1rem 0
              .ic-time-img
                width: .22rem
                vertical-align: middle
              .time-text
                vertical-align: middle
    .img-content
      height: calc(100% - .98rem)
      display: flex
      flex-direction: column
      justify-content: center
      align-items: center
      background-color: #fff
      img
        width: 3rem
      p
        color: #999
        font-size: 0.3rem
        margin: 10px
</style>
