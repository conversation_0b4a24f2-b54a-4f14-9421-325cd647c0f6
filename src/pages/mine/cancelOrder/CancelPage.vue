<template>
  <div class="wrapper">
    <Header title="取消" @backFun="goBack"></Header>
    <div class="content">
      <div
        class="weui-cell__bd item cancel-content"
        @click="handleSelectCancelReason"
        :class="{ pointerE: isSubmited }"
      >
        <div class="title">取消理由</div>
        <div class="to-cancel">
          <span class="reason">{{ cancelReasonName }}</span>
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <div class="weui-cells weui-cells_form desc-form">
        <div class="er-level border-bottom item">
          <div class="er-text title">取消说明</div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd">
            <textarea
              class="weui-textarea cancel-textarea"
              placeholder="请输入您要取消工单的原因描述，字数在120字以内"
              maxlength="120"
              rows="5"
              :readonly="isSubmited"
              v-model="cancelDesc"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
    <div class="submit-btn">
      <button class="weui-btn weui-btn_primary" @click="handleCancleClick">
        {{ btnText }}
      </button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "CancelPage",
  mixins: [fontSizeMixin],
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  data() {
    return {
      btnText: "确定取消",
      cancelReasonName: "请选择",
      cancelReasonCode: "",
      cancelDesc: "",
      taskId: "",
      isDis: true,
      disabled: true,
      isSubmited: false
    };
  },
  activated() {
    if (this.$route.params.cancelReasonName) {
      this.cancelReasonName = this.$route.params.cancelReasonName;
      this.taskId = this.$route.params.taskId;
    }
    if (this.$route.params.cancelReasonCode) {
      this.cancelReasonCode = this.$route.params.cancelReasonCode;
      this.cancelDesc = this.$route.params.cancelDesc;
      this.taskId = this.$route.params.taskId;
    }
    if (this.$route.params.cancelDesc) {
      this.cancelDesc = this.$route.params.cancelDesc;
      this.taskId = this.$route.params.taskId;
    }
    if (this.$route.params.taskId) {
      this.taskId = this.$route.params.taskId;
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    /**
     * 跳转选择原因页面
     */
    handleSelectCancelReason() {
      this.$router.push({
        name: "CancelReason",
        params: {
          cancelDesc: this.cancelDesc,
          taskId: this.taskId
        }
      });
    },
    /**
     * 提交取消工单
     */
    handleCancleClick() {
      if (!this.cancelReasonCode) {
        $.toast("请选择取消理由", "text");
        return;
      }
      $.showLoading();
      this.axios
        .get(__PATH.ONESTOP + "/appOlgTaskManagement.do?toCancelTask", {
          params: {
            hospitalCode: this.loginInfo.hospitalCode,
            unitCode: this.loginInfo.unitCode,
            taskId: this.taskId,
            userId: this.loginInfo.id,
            cancelReasonCode: this.cancelReasonCode,
            cancelExplain: this.cancelDesc,
            realName: this.loginInfo.staffName
          },
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(this.handleCancleClickSucc);
    },
    /**
     * 提交成功函数
     * @param res
     */
    handleCancleClickSucc(res) {
      $.hideLoading();
      if (res.data.code == 200) {
        this.isSubmited = true;
        this.isDis = true;
        this.disabled = true;
        this.btnText = "已取消";
        $.toast(res.data.message, "success", () => {
          this.$router.push({
            path: "/myorder",
            query: {
              type: "2",
              leaguerType: "1"
            }
          });
        });
      } else {
        $.toast(res.data.message, "text");
      }
    }
  },
  mounted() {
    if (this.$route.params.cancelReasonName) {
      this.cancelReasonName = this.$route.params.cancelReasonName;
      this.taskId = this.$route.params.taskId;
    }
    if (this.$route.params.cancelReasonCode) {
      this.cancelReasonCode = this.$route.params.cancelReasonCode;
      this.cancelDesc = this.$route.params.cancelDesc;
      this.taskId = this.$route.params.taskId;
    }
    if (this.$route.params.cancelDesc) {
      this.cancelDesc = this.$route.params.cancelDesc;
      this.taskId = this.$route.params.taskId;
    }
    if (this.$route.params.taskId) {
      this.taskId = this.$route.params.taskId;
    }
  },
  watch: {
    cancelReasonName(val) {
      if (val != "请选择" && this.cancelDesc != "") {
        this.isDis = false;
        this.disabled = false;
      } else {
        this.isDis = true;
        this.disabled = true;
      }
    },
    cancelDesc(val) {
      if (val != "" && this.cancelReasonName != "请选择") {
        this.isDis = false;
        this.disabled = false;
      } else {
        this.isDis = true;
        this.disabled = true;
      }
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.wrapper {
  height: 100%;
  background-color: $bgColor;

  .content {
    box-sizing: border-box;
    min-height: 90%;
    padding-bottom: 76px;
    background-color: $bgColor;

    .pointerE {
      pointer-events: none;
    }

    .item {
      display: flex;
      align-items: center;
      itemBaseStyle();

      .title {
        font-size: calc(16px * var(--font-scale));
        line-height: 1.3;
        display: flex;
        align-items: center;
      }
    }

    .cancel-content {
      display: flex;
      justify-content: space-between;
      marginBottom20();

      .to-cancel {
        display: flex;
        align-items: center;

        .reason {
          text-align: right;
          color: $textColor;
          margin-right: 5px;
          font-size: calc(16px * var(--font-scale));
          line-height: 1.3;
        }

        .arrow {
          color: #e5e5e5;
        }
      }
    }

    .desc-form {
      margin-top: 0;
    }
  }

  .submit-btn {
    padding: 0.22rem 0.32rem;
    background-color: #fff;
    width: 100%;
    box-sizing: border-box;
    height: 66px;
    margin-top: -66px;
  }
  .cancel-textarea {
    font-size: calc(16px * var(--font-scale));
  }
  .weui-btn {
    font-size: calc(16px * var(--font-scale))
    height: 50px
    line-height: 50px
  }
}
</style>
