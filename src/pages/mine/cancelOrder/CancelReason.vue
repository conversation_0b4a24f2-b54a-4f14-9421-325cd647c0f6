<template>
  <div class="cancel-wrapper">
    <Header title="取消理由" @backFun="goBack"></Header>
    <div class="weui-cells_radio cancel-content">
      <label
        class="weui-cell weui-check__label item"
        :for="index"
        v-for="(item, index) of list"
        :key="index"
        @click="getVal(index)"
      >
        <div class="weui-cell__ft select-item">
          <p style="text-align: left;">{{ item.cancelReasonName }}</p>
          <span class="iconfont" v-if="item.check">&#xeaf1;</span>
        </div>
      </label>
    </div>
    <div class="submit-btn">
      <button
        class="weui-btn weui-btn_primary"
        :class="{ 'weui-btn_disabled': isDis }"
        :disabled="disabled"
        @click="submitCancelReason"
      >
        确定
      </button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "CancelReason",
  mixins: [fontSizeMixin],
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  data() {
    return {
      isDis: true,
      disabled: true,
      flag: true,
      cancelReasonCode: "",
      cancelReasonName: "",
      cancelDesc: "",
      taskId: "",
      list: []
    };
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    /**
     * 获取取消原因请求
     */
    getCancelReason() {
      this.$api.getCancelReason({}).then(this.getCancelReasonSucc);
    },
    /**
     * 获取取消原因成功函数
     */
    getCancelReasonSucc(res) {
      //每一项添加选中状态表示check
      for (let i = 0; i < res.length; i++) {
        res[i].check = false;
      }
      this.list = res;
    },
    /**
     * 获取选中值
     * @param idx
     */
    getVal(idx) {
      if (this.flag) {
        this.isDis = false;
        this.disabled = false;
        this.flag = false;
      }
      for (let i = 0; i < this.list.length; i++) {
        this.list[i].check = false;
      }
      this.list[idx].check = true;
      this.cancelReasonCode = this.list[idx].cancelReasonCode;
      this.cancelReasonName = this.list[idx].cancelReasonName;
    },
    /**
     * 返回取消原因
     */
    submitCancelReason() {
      this.$router.push({
        name: "CancelPage",
        params: {
          cancelReasonName: this.cancelReasonName,
          cancelReasonCode: this.cancelReasonCode,
          cancelDesc: this.cancelDesc,
          taskId: this.taskId
        }
      });
    }
  },
  mounted() {
    this.getCancelReason();
    this.taskId = this.$route.params.taskId;
    if (this.$route.params.cancelDesc) {
      this.cancelDesc = this.$route.params.cancelDesc;
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/mixins.styl"
@import "~styles/varibles.styl"
.cancel-wrapper
  height: 100%
  background-color: $bgColor
  .cancel-content
    box-sizing: border-box
    min-height: 90%
    padding-bottom: 76px
    background-color: $bgColor
    margin-top: 0
    .item
      itemBaseStyle()
      font-size: calc(16px * var(--font-scale))
      border-bottom: 1px solid $bgColor
      .select-item
        display: flex
        justify-content: space-between
        width: 100%
        p
          color: $darkTextColor
        .iconfont
          color: $color
  .submit-btn
    padding: 0.22rem 0.32rem
    background-color: #fff
    width: 100%
    box-sizing: border-box
    height: 66px
    margin-top: -66px
  .weui-btn
    font-size: calc(16px * var(--font-scale))
    height: 50px
    line-height: 50px
</style>
