<template>
  <div class="Feedback-dtails">
    <Header :title="readonlyFlag == 1 ? '意见反馈' : '意见详情'" @backFun="goback"></Header>
    <div class="main">
      <div>
        <!-- 反馈提交 -->
        <div v-if="readonlyFlag == 1" class="details-info">
          <div class="identity">
            <div class="anonymous" @click="ifidentity(0)">
              <img v-if="identity == 0" src="@/assets/images/selected.png" alt="" style="width:0.52rem" />
              <img v-else src="@/assets/images/unselected.png" alt="" />
              <span class="identity-name" :class="{ ifidentity: identity == 1 }">匿名</span>
            </div>
            <div class="autonym" @click="ifidentity(1)">
              <img v-if="identity == 1" src="@/assets/images/selected.png" alt="" style="width:0.52rem" />
              <img v-else src="@/assets/images/unselected.png" alt="" />
              <span class="identity-name" :class="{ ifidentity: identity == 0 }">实名</span>
            </div>
          </div>
          <div class="idea">
            <div class="idea-content border-bottom">
              <div class="idea-title headline">标题</div>
              <div class="idea-input subtitle">
                <textarea
                  placeholder="请填写意见标题，最多32个字"
                  maxlength="32"
                  style="padding-left:10px"
                  rows="2"
                  v-model="opinionTitle"
                  class="weui-textarea titlecolor"
                ></textarea>
                <div class="length">{{ opinionTitle.length }}/32</div>
              </div>
            </div>
            <div class="idea-content">
              <div class="idea-title headline">内容</div>
              <div class="idea-input subtitle">
                <textarea
                  v-if="readonlyFlag == 1"
                  placeholder="请填写意见内容"
                  maxlength="500"
                  style="padding-left:10px;"
                  rows="6"
                  @blur="handleTextareaBlurEvent"
                  v-model="content"
                  class="weui-textarea"
                ></textarea>
                <div class="length numcolor">{{ content.length }}/500</div>
              </div>
            </div>
            <!--图片上传-->
            <upload-image class="bottom" style="position:relative" ref="imgs" v-if="readonlyFlag == 1" @getImg="getImg"></upload-image>
          </div>
          <div class="user-rights">
            <span class="rights" @click="userRights">用户须知与条款</span>
            <span @click="Rights">
              <img v-if="ifRights" src="@/assets/images/selected.png" style="width:0.38rem" />
              <img v-else src="@/assets/images/unselected.png" />
              <span class="text">我已阅读并接受用户须知与条款</span>
            </span>
          </div>
        </div>
        <!-- 反馈详情 -->
        <div v-if="readonlyFlag == 2" class="details-info">
          <!-- 标题 -->
          <div class="info-content">
            <div class="weui-cell  item line-css  border-bottom">
              <div class="weui-cell__hd">
                <label class="weui-label title">申请人员</label>
              </div>
              <div class="weui-cell__bd">{{ loginInfo.staffName }}</div>
            </div>
            <div class="weui-cell item line-css border-bottom">
              <div class="weui-cell__hd">
                <label class="weui-label title">联系电话</label>
              </div>
              <div class="weui-cell__bd">{{ phone }}</div>
            </div>
            <!-- <div class="cell  item line-css">
              <div class="title">意见标题</div>
              <div class="content-text">{{ opinionTitle }}</div>
            </div>
            <div class="cell item line-css">
              <div class="title ">意见内容</div>
              <div class=" content-text">{{ content }}</div>
            </div> -->
          </div>
          <div class="idea">
            <div class="content-box">
              <div class="c-title">意见标题</div>
              <div class="op-title">{{ opinionTitle }}</div>
              <div class="c-title">意见内容</div>
              <div class="op-content">{{ content }}</div>
            </div>
            <!--附件-->
            <div class="img-desc border-rightbottom">
              <!--<div class="text-title title">图片</div>-->
              <div class="img-desc-content">
                <!-- <span class="text-title title">附件</span> -->
                <span class="content-css"></span>
              </div>
              <div class="img-content" v-if="attachment.length > 0">
                <div class="img-wrapper" v-for="ele of attachment" :key="ele">
                  <div class="img-box">
                    <img
                      v-preview="ele"
                      :src="ele"
                      class="img"
                      preview-title-enable="true"
                      preview-nav-enable="true"
                      preview-top-title-tnable="true"
                      preview-title-extend="false"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 意见反馈回复 -->
      <div class="ge"></div>
      <div v-if="readonlyFlag == 2" class="write">
        <div class="write-content">
          <div class="content-tit headline">意见回复</div>
          <div v-if="replyList.length > 0">
            <div class="content-info" v-for="(item, index) of replyList" :key="index">
              <div class="info-title">
                <div>{{ item.createByName }}</div>
                <span class="date">{{ item.createDate }}</span>
              </div>
              <div class="info-span subtitle">{{ item.feedbackExplain }}</div>
            </div>
          </div>
          <div v-else class="no">
            <img src="@/assets/images/noDataDefault/search-empty.png" />
            <div class="no-text">暂无回复~</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 按钮 -->
    <div class="btns">
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="write" v-if="readonlyFlag == 2">
        我要回复
      </button>
      <div v-else>
        <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="writeSubmit(true)" v-if="ifRights">
          提交意见反馈
        </button>
        <button class="weui-btn weui-btn_mini weui-btn_primary btn ifRights" v-else @click="writeSubmit(false)">
          提交意见反馈
        </button>
      </div>
    </div>
  </div>
</template>
<script>
import UploadImage from "@/common/uploadImg/uploadImg";
import { mapState } from "vuex";
export default {
  name: "FeedbackDetails",
  components: {
    UploadImage
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  data() {
    return {
      a: false,
      readonlyFlag: 1, //按钮样式和禁用 1是提交 2是回复
      phone: "",
      feedbackPhone: "",
      imagesParams: [],
      imageConfig: [],
      replyList: [],
      opinionTitle: "", //意见反馈标题
      content: "", //意见反馈内容
      attachment: [],
      accessToken: "",
      allowPhoto: true,
      identity: 1, //默认实名
      ifRights: true, //用户须知选中
      amount: {
        //上传图片限制
        quantity: 9,
        capital: "九"
      }
    };
  },
  activated() {
    this.phone = sessionStorage.telFeed || this.loginInfo.phone;
    this.readonlyFlag = this.$route.query.readonlyFlag || 1;
    if (this.$route.query.readonlyFlag == 2) {
      this.getOpinionDetail();
    } else {
      // this.getConfigParams();
    }
    // if (
    //    !this.loginInfo.name || !this.loginInfo.phone ||
    //   (!this.staffInfo.officeName && !this.staffInfo.deptName)
    // ) {
    //   $.modal({
    //     title: "提示信息",
    //     text: "请完善个人信息",
    //     buttons: [
    //       {
    //         text: "确定",
    //         onClick: () => {
    //           this.$router.push({
    //             path:"/improveInfo",
    //             query:{
    //               source:"feed"
    //             }
    //           });
    //         }
    //       },
    //       {
    //         text: "取消",
    //         className: "default",
    //         onClick: () => {
    //           this.$router.replace("Feedback");
    //         }
    //       }
    //     ]
    //   });
    // }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.name == "Feedback") {
        vm.content = "";
        vm.opinionTitle = "";
        vm.attachment = [];
      }
    });
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    getImg(img) {
      console.log("img", img);
      this.imagesParams.push(img);
    },
    getOpinionDetail() {
      let params = {
        id: this.$route.query.id
      };
      this.$api.getOpinionDetail(params).then(res => {
        this.content = res.content;
        let arr = [];
        res.attachment.forEach(item => {
          arr.push(this.$YBS.imgUrlTranslation(item));
        });
        console.log("11111", arr);
        this.attachment = arr;
        this.phone = res.feedbackPhone;
        this.opinionTitle = res.opinionTitle;
        this.signatureFlag = res.signatureFlag;
        this.replyList = res.timeAxis.filter(item => {
          return item.operationCode == 13;
        });
      });
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    // 修改电话
    mod() {
      this.$router.push({
        path: "/setTel",
        query: {
          source: "feed"
        }
      });
    },
    inputVerification() {
      this.phone = this.phone.replace(/[^0-9]/g, "");
    },
    /**
     * 获取上传图片路径
     * @param params
     */
    doUpload(params, localId, i) {
      return new Promise((resolve, reject) => {
        this.axios
          .post(
            __PATH.ONESTOP + "/appOlgTaskManagement.do?uploadByWeChat",
            // __PATH.ONESTOP + "/minio.doupload",
            this.$qs.stringify({
              hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
              unitCode: this.loginInfo.userOffice[0].unitCode,
              accessToken: this.accessToken,
              appId: process.env.WxAppid,
              mediaId: params[i],
              localId: localId[i],
              type: "0"
            })
          )
          .then(res => {
            resolve(res.data.data);
          });
      });
    },
    //图片处理
    async getImages(params, localId) {
      this.imagesParams = [];
      this.imageConfig = [];
      for (var i = 0; i < params.length; i++) {
        $.showLoading("上传图片中...");
        await this.doUpload(params, localId, i).then(res => {
          $.hideLoading();
          this.imagesParams[i] = res.ossUrl;
          this.imageConfig[i] = res;
        });
      }
    },
    /**
     * 删除图片
     */
    deleteImg(itemImgLocalId, index) {
      this.imagesParams.splice(index, 1);
      let tempIdx = "";
      let tempOssUrl =
        this.imageConfig.find(item => {
          return item.localId == itemImgLocalId;
        }) || {};
      tempOssUrl = tempOssUrl.ossUrl;
      this.axios
        .post(
          __PATH.ONESTOP + "/appOlgTaskManagement.do?delOssFile",
          this.$qs.stringify({
            hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
            unitCode: this.loginInfo.userOffice[0].unitCode,
            accessToken: this.accessToken,
            appId: process.env.WxAppid,
            ossURL: tempOssUrl
          })
        )
        .then(res => {});
    },
    /**跳转到回复页面 */
    write() {
      this.$router.push({
        path: "Reply",
        query: {
          id: this.$route.query.id,
          opinionTitle: this.opinionTitle
        }
      });
    },
    writeSubmit(goon) {
      if (!goon) {
        $.toast("请先阅读并同意用户须知", "text");
        return;
      }
      if (!this.opinionTitle) {
        $.toast("请输入意见标题", "text");
        return;
      }
      if (!this.content) {
        $.toast("请输入意见内容", "text");
        return;
      }
      this.submit();
    },
    submit() {
      let params = {
        content: this.content,
        userId: this.loginInfo.id,
        staffId: this.staffInfo.staffId,
        feedbackName: this.loginInfo.staffName,
        feedbackPhone: this.phone,
        opinionTitle: this.opinionTitle,
        signatureFlag: this.identity,
        sourcesDeptName: this.loginInfo.deptName || "",
        sourcesDept: this.loginInfo.deptId || "",
        attachmentUrl: this.imagesParams.join(",")
      };
      this.$api.saveOpinion(params).then(res => {
        sessionStorage.removeItem("telFeed");
        $.toast("提交成功");
        this.$router.replace("Feedback");
      });
    },

    /**
     * 实名匿名切换
     */
    ifidentity(i) {
      this.identity = i;
      // this.signatureFlag=value;
    },
    /**
     * 阅读用户须知切换
     */
    Rights() {
      this.ifRights = !this.ifRights;
    },
    /**
     * 跳转用户须知页面
     */
    userRights() {
      this.$router.push({
        path: "userRights",
        query: {
          id: this.$route.query.id
        }
      });
    }
  },
  watch: {
    opinionTitle() {
      this.opinionTitle = this.opinionTitle.replace(
        /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,
        ""
      );
    },
    content() {
      this.content = this.content.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g, "");
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.main {
  height: 79%;
  overflow: auto;
  background:#f2f4f9;
}

.ze {
  height: 8rem;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0);
}

.details-info {
  .row {
    padding-left: 0.32rem;
    border-bottom: 1px solid #eff0f4;

    .title {
      width: 22%;
    }

    .tel {
      width: 62%;
    }
  }

  .idea {
    margin-top: 0.2rem;
    background :#fff;


    .idea-content {
      padding-left: 0.1rem;

      .idea-title {
        padding-top: 0.3rem;
        padding-left: 0.22rem;
        margin-bottom: 0.2rem;
      }

      .idea-input {
        padding-right: 0.1rem;
        overflow: auto;
        .length{
          width :100%
          line-height : 0.4rem;
          text-align :right;
          padding-left:10px;
          padding-right:10px;
          box-sizing:border-box;
          font-size:.0.24rem;
          color:$contentColor;
        }
        .numcolor{
          background:#F5F5F5;
          border-radius:0px 0px 5px 5px;

        }
        .weui-textarea{
          box-sizing:border-box;
          background:#F5F5F5;
          overflow-y:auto;
          border-radius:5px 5px 0 0;
          padding-left:10px;
          padding-right:0.32rem;
          line-height :0.4rem;
        }
        .titlecolor{
          background:#FFF;
        }
      }
    }
  }
}

.modification {
  text-align: right;
  color: #999;
}

.flex {
  height: 0.98rem;
  line-height: 0.98rem;
  display: flex;
}

.headline {
  font-size: 0.3rem;
  color: #353535;
}

.subtitle {
  font-size: 0.28rem;
  color: $contentColor;
}

.fen {
  width: 100%;
  height: 0.2rem;
  background-color: $bgColor;
}

.ge{
  height :.2rem;
  background:#eff0f4;
}

.write {
  background: #fff;

  .write-content {
    padding-top: 0.35rem;
    padding-left: 0.32rem;

    .content-tit {
      padding-bottom: 0.35rem;
    }
  }
}

.content-info {
  padding-right: 0.32rem;
  border-bottom: 1px solid #efeff4;

  .info-title {
    display: flex;
    justify-content: space-between;
    margin-top: 0.29rem;
    margin-bottom: 0.32rem;

    .date {
      font-size: 0.26rem;
      color: #C2C2C2;
    }
  }

  .info-span {
    margin-bottom: 0.31rem;
    word-break: break-all;
    line-height :0.36rem;
  }
}

.btns {
  width: 100%;
  background: #fff;
  height: 79px;
  line-height: 66px;
  text-align: center;
  border-top: 1px solid #e5e5e5;

  button {
    width: 94%;
    height: 0.88rem;
    font-size: 0.32rem;
    line-height: 0.88rem;
  }
}

.no {
  width: 100%;
  text-align: center;
  font-size: 0.3rem;
  color: $contentColor;

  img {
    width: 2.68rem;
  }

  .no-text {
    color: #86909C;
    margin-top: 0.59rem;
    padding-bottom:0.59rem;
  }
}

.img-desc {
  background-color: #fff;
  padding-bottom:.16rem;

  .text-title {
    line-height: 1rem;
    padding-left: 0.32rem;
  }
}

.img-content {
  overflow :auto;
  display: flex;
  background: #fff;
  padding: 0 0.3rem;

  .img-wrapper {
    flex-shrink: 0;
    width: 30%;
    height: 1.38rem;
    margin: 0.1rem;
    position: relative;

    .img-box {
      height: 100%;
      overflow: hidden;

      .img {
        width: 100%;
      }
    }
  }
}

.Feedback-dtails {
  height: 100vh;
  overflow: hidden;
}
.identity{
  display :flex;
  padding-left:.32rem;
  line-height :0.98rem;
  margin-bottom:  .2rem;
  background :#fff;
  .anonymous{

  }
  .autonym{
    margin-left:2rem
  }
  .identity-name{
    margin-top:4px;
    line-height : .98rem;
    font-size:.3rem;
    color:#353535;
  }
  .ifidentity{
    color:#999999;
  }

  img{
    width :.46rem;
    margin-right:.2rem;
  }
}
.user-rights{
  padding-left:0.5rem
  font-size:0.28rem;
  img{
    width : 0.34rem;
    padding-left:3px;
    padding-right:3px;
  }
  .rights{
    color:#3562DB;
  }
  .text{
    color:$textColor;
  }
}
.info-content{
  background:#fff;
  color:$contentColor;
  font-size :.28rem;
  .line-css{
    // border-bottom:1px solid #E5E5E5;
    padding:0 15px;
  }
  .title{
    width:105px;
    line-height: 0.98rem;
    color:#4E5969;
    font-size:0.3rem;
  }
  .content-css{
    padding-top:.35rem;
    padding-bottom:.35rem;
    .conent-title{
      // width :105px;
    }
  }
}
.ifRights{
  background :#d7d7d7 !important ;
}

.cell{
  display: flex;
  .content-text{
    width:calc(100% - 105px);
    margin-top:.34rem;
    line-height :0.36rem;
    word-wrap: break-word;
    word-break: break-all;
  }
}
.bottom{
  padding-right:0.3rem;
}
.weui-cell__bd {
  color:#1D2129;
}
.weui-cell {
  height: 32px;
}
.content-box {
  padding-top: 16px;
}
.idea .content-box {
  padding: 0 16px;
  padding-top: 16px;
}
.c-title {
  font-size:13px;
  color: #4E5969;
}
.op-title {
  font-weight:500;
  font-size:16;
  color: #1D2129;
  margin:8px 0;
}
.op-content {
  font-weight:500;
  font-size:16;
  color: #1D2129;
  margin:8px 0;
}
</style>
