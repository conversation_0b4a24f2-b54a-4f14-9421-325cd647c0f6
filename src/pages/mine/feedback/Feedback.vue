<template>
  <div class="feedback">
    <Header title="职工意见箱" @backFun="goback"></Header>
    <div class="content">
      <div v-if="opinionList.length > 0" style="padding:0 10px;margin-top:8px">
        <div
          class="fee-list"
          v-for="(item, i) in opinionList"
          :key="i"
          @click="goDetails(2, item.id)"
        >
          <!-- <div class="title-img">
            <img src="@/assets/images/icon/serve.png" />
          </div> -->
          <div class="list-text">
            <div>
              <div class="details op-title">
                <!-- <span>意见标题：</span> -->
                <span>{{ item.opinionTitle }}</span>
              </div>
              <div class="details op-content">
                <!-- <span>意见内容：</span> -->
                <span>{{ item.content }}</span>
              </div>
            </div>
            <div class="nameinfo">
              <img class="icon-notice" src="@/assets/images/<EMAIL>" />
              <span class="naem">{{ item.feedbackName }}</span>
              <div class="time">
                <!-- <img src="@/assets/images/ic-time.png" /> -->
                <span>{{ item.createTime }}</span>
              </div>
            </div>
            <div class="detail">
              <span>查看详情</span>
              <van-icon color="#86909C" name="arrow" />
            </div>
          </div>
          <!-- <div class="iconimg">
            <span class="iconfont arrow">&#xe646;</span>
          </div> -->
        </div>
      </div>
      <div v-else class="no">
        <img src="@/assets/images/noDataDefault/search-empty.png" />
        <div class="no-text">暂无意见反馈~</div>
      </div>
      <div class="footer weui-loadmore">
        <span v-if="loadMode" class="weui-loadmore__tips">
          <i class="weui-loading"></i>正在加载
        </span>
        <span v-else>{{ hintMessage }}</span>
      </div>
    </div>

    <div class="btns">
      <button
        class="weui-btn weui-btn_mini weui-btn_primary btn"
        @click="goDetails(1)"
      >
        意见反馈
      </button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import moment from "moment";
export default {
  name: "Feedback",
  data() {
    return {
      a: true,
      loading: true,
      opinionList: [],
      hintMessage: "",
      flag: false,
      loadMode: false
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  methods: {
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
    //查询角色
    getUserRole() {
      this.axios
        .get(__PATH.ONESTOP + "/appDisUserLoginController.do?getUserRole", {
          params: {
            staffId: this.staffInfo.staffId
          },
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(res => {
          this.roleCode = res.data.data.join(",") || "";
          this.getOpinionList();
        });
    },
    goDetails(i, id) {
      event.preventDefault();
      if (this.flag) {
        return;
      }
      this.$router.push({
        path: "FeedbackDetails",
        query: {
          readonlyFlag: i,
          id: id
        }
      });
    },
    getOpinionList(pageInfo, flag) {
      pageInfo = pageInfo || {};
      let params = {
        userId: this.loginInfo.id,
        staffId: this.loginInfo.staffId,
        roleCode: this.roleCode,
        pageSize: pageInfo.pageSize || 20,
        curPage: pageInfo.curPage || 1
      };
      this.$api.getOpinionList(params).then(res => {
        this.loadMode = false;
        pageInfo.curPage = pageInfo.curPage ? pageInfo.curPage : 1;
        if (pageInfo.curPage <= 1) {
          this.opinionList = res;
        } else {
          if (res.length == 0) {
            this.hintMessage = "没有更多数据了";
            this.loading = false;
          } else {
            this.opinionList = this.opinionList.concat(res);
          }
        }
        if (flag) {
          this.flag = false;
          $(".content").pullToRefreshDone();
        }
      });
    }
  },
  created() {
    this.getUserRole();
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if (systemType === "ios") {
      document.querySelector(".content").style.minHeight = "80%";
    }
    let i = 1;
    $(".content")
      .infinite()
      .on("infinite", () => {
        if (this.loading) {
          this.loadMode = true;
          this.getOpinionList({
            pageSize: 20,
            curPage: i++
          });
        }
      });
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.feedback {
  height: 100vh;
  overflow: hidden;
  background-color: #eff0f4;
}

.content {
  height: 80%;
  overflow: auto;
  background-color : #F2F4F9;
}

.fee-list {
  // height: 1.9rem;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #eff0f4;
  padding-left: 0.32rem;
  padding-right: 0.32rem;
  background-color #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  padding-top:8px;

  .title-img {
    line-height: 1.4rem;

    img {
      width: 0.62rem;
    }
  }

  .iconimg {
    line-height: 1.4rem;
  }
}

.list-text {
  width: 100%;
  // padding: 0 0.32rem;

  .nameinfo {
    display: flex;
    // margin-top: 0.32rem;
    // margin-bottom: 0.28rem;
    .name {
      color: #353535;
      font-size: 0.32rem;
      font-weight:bold;
    }

    .time {
      color: #86909C;
      font-size: 0.26rem;

      img {
        width: 0.21rem;
        margin-top: -0.02rem;
      }
    }
  }

  .details {
    // width: 5.2rem;
    // height: 30px;
    // line-height: 0.36rem;
    color: $contentColor;
    // margin :0.17rem 0;
    font-size: 0.28rem;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}

.footer {
  color: #C2C2C2;
  font-size: 0.26rem;
}

.btns {
  width: 100%;
  background: #fff;
  height: 66px;
  line-height: 66px;
  text-align: center;
  border-top: 1px solid #e5e5e5;

  button {
    width: 94%;
    height: 0.88rem;
    font-size: 0.32rem;
    line-height: 0.88rem;
  }
}

.no {
  width: 100%;
  margin-top: 2.24rem;
  text-align: center;
  font-size: 0.3rem;
  color: $contentColor;

  img {
    width: 2.68rem;
  }

  .no-text {
    margin-top: 0.59rem;
  }
}

.dis {
  display: none;
}
.icon-notice {
  width: 16px;
  height: 16px;
}
.naem {
  color: #86909C;
  margin:0 8px;
  fontsize: 14px;
}
.op-content >span{
  color: #1D2129;
  font-size: 14px;
}
.op-title > span{
  font-weight: bold;
  font-size: 17px;
  color: #1D2129;
}
.op-title {
  height: 35px;
  line-height: 35px;
}
.op-content {
  margin-top:2px;
  margin-bottom:10px;
}
.time > span {
  font-size:14px;
}
.detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  border-top: 1px solid #E5E6EB;
  margin-top: 8px;
}
.detail > span {
  color: #86909C;
}
</style>
