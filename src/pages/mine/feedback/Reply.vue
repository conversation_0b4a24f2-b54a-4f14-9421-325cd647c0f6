<template>
  <div class="reply">
    <Header title="回复" @backFun="goback"></Header>
    <div class="reply-body" v-if="$route.query.source == 'custom'">
      <div class="reply-title">回复内容</div>
      <div class="reply-text">
        <textarea
          placeholder="请填写您的回复,内容不超过120个字"
          v-model="content"
          maxlength="120"
          rows="5"
          class="weui-textarea"
        ></textarea>
      </div>
    </div>
    <div class="reply-body" v-else>
      <div class="weui-cell  item line-css  border-bottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">意见标题</label>
        </div>
        <div class="weui-cell__bd" style="color:#1D2129">{{ $route.query.opinionTitle }}</div>
      </div>
      <div class="reply-title">意见回复</div>
      <div class="reply-text">
        <textarea
          placeholder="请填写您的回复,内容不超过500个字"
          v-model="content"
          maxlength="500"
          style="padding:3px 10px;"
          rows="8"
          class="reply-info"
        ></textarea>
        <div class="length numcolor">{{ content.length }}/500</div>
      </div>
    </div>

    <div class="btns">
      <button
        class="weui-btn weui-btn_mini weui-btn_primary btn"
        @click="submit"
      >
        确定回复
      </button>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
import { mapState } from "vuex";
export default {
  name:"Reply",
  data() {
    return {
     content:"",
     replyType:'',  //记录型工单回复需要传值,意见箱回复传空
    };
  },
    computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  methods:{
    goback() {
      this.$router.go(-1);
    },
    submit(){
      if(!this.content){
        $.toast("请输入回复内容","text")
        return
      }
      let params = {
        content:this.content,
        feedbackName:this.loginInfo.staffName,
        feedbackPhone:this.loginInfo.phone,
        userId:this.loginInfo.id,
        staffId: this.staffInfo.staffId,
        id:this.$route.query.id,
        replyType:this.replyType
      }
      this.$api.replyOpinion(params).then(res=>{
        $.toast("回复成功")
        this.$router.go(-1)
      })
    }
  },
  mounted(){
    if(this.$route.query.source == 'custom'){
      document.title = "回复"
      this.replyType="replyType"
    }
  },
  watch:{
    content(){
      this.content = this.content.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,'');
    },
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.reply{
  height: 100vh;
  overflow: hidden;
}

.reply-body {
  height: calc(90% - 79px);
  overflow: auto;
  // padding-left: 0.32rem;

  .reply-title {
    line-height: 0.98rem;
    font-size: 0.32rem;
    color: #353535;
    padding-left: 0.32rem;

  }

  .reply-text {
    margin-right :.23rem;
    line-height: 0.36rem;
    font-size: 0.28rem;
    color: $contentColor;
    padding-left: 0.32rem;

  }
  .reply-info{
    width 100%;
    box-sizing:border-box;
    background:#fff;
    overflow-y:auto;
    border-radius:5px 5px 0 0;
    line-height :0.4rem;
    resize:none
    color:$contentColor;
  }
  .length{
    width :100%
    line-height : 0.4rem;
    text-align :right;
    padding-left:10px;
    padding-right:10px;
    box-sizing:border-box;
    font-size:.0.24rem;
    color:$contentColor;
    background:#fff;
    border-radius:0px 0px 5px 5px;


  }
}

.btns {
  width: 100%;
  background: #fff;
  height: 66px;
  line-height: 66px;
  text-align: center;
  border-top: 1px solid #e5e5e5;

  button {
    width: 94%;
    height: 0.88rem;
    font-size: 0.32rem;
    line-height: 0.88rem;
  }
}
.line-css{
  border-bottom:1px solid #E5E5E5;
  padding:0 15px;
  color:$contentColor;
  font-size:.28rem;
}
.title{
  line-height: 0.98rem;
  color:#353535;
  font-size:0.3rem;
}
.weui-cell__bd{
   word-wrap:break-word;
   word-break :break-all;
}
</style>
