<template>
  <div class="wrapper">
    <pull-to :top-load-method="reload" :top-config="TOP_DEFAULT_CONFIG">
      <div class="center-content" :class="{ bottomPadding: bottomPadding }">
        <personal-name :leaguerType="leaguerType"></personal-name>
        <worker-type
          v-if="userType == 2"
          :workList="workList"
          :leaguerType="leaguerType"
        ></worker-type>
        <myorder-type
          v-if="workList.length != 0 && userType == 1"
          :workList="workList"
          :leaguerType="leaguerType"
        ></myorder-type>
        <!-- <div> -->
        <div v-if="userType == 2">
          <div
            class="item-css my-order border-bottom no-mBottom"
            @click="showMyOrderList"
          >
            <span>我的申报</span>
            <span class="iconfont">&#xe646;</span>
          </div>
          <div
            class="item-css my-order border-bottom no-mBottom"
            @click="toComplainList('hqts')"
          >
            <span>后勤投诉</span>
            <span class="iconfont">&#xe646;</span>
          </div>
        </div>
        <!--没有职工信息代表为临时用户B类型，只显示我的申报-->
        <div v-if="ifIomsList">
          <div
            class="item-css my-order border-bottom no-mBottom"
            @click="toComplainList('wdts')"
            v-if="staffInfo"
          >
            <span>我的投诉</span>
            <span class="iconfont">&#xe646;</span>
          </div>
        </div>

        <!-- <div
          class="item-css my-order border-bottom no-mBottom"
          @click="myWorkbench()"
        >
          <span>设备运维</span>
          <span class="iconfont">&#xe646;</span>
        </div> -->
        <!-- <div
          class="item-css my-order border-bottom  no-mBottom"
          @click="serviceInspection()"
        >
          <span>服务巡检</span>
          <span class="iconfont">&#xe646;</span>
        </div> -->
        <!-- <div
          class="item-css my-order border-bottom no-mBottom"
          @click="assetsInventory()"
        >
          <span>资产盘点</span>
          <span class="iconfont">&#xe646;</span>
        </div> -->
        <!-- <div
          class="item-css my-order border-bottom no-mBottom"
          @click="assetsInventoryImes()"
        >
          <span>医疗设备盘点</span>
          <span class="iconfont">&#xe646;</span>
        </div> -->
        <!-- <div
          class="item-css my-order border-bottom no-mBottom"
          @click="MyQuestionnaire()"
        >
          <span>我的问卷</span>
          <span class="iconfont">&#xe646;</span>
        </div> -->
        <div
          class="item-css my-order border-bottom no-mBottom"
          @click="medicalWaste()"
        >
          <span>医疗废物</span>
          <span class="iconfont">&#xe646;</span>
        </div>
        <!-- <div
          class="item-css my-order border-bottom"
          @click="goPurchase()"
        >
          <span>过会投票</span>
          <span class="iconfont">&#xe646;</span>
        </div> -->
        <div class="item-css my-order border-bottom" @click="getOpenId()">
          <span>版本检测</span>
          <span class="iconfont">&#xe646;</span>
        </div>
      </div>
      <div class="item-css drop-out" @click="handleBtnClick">退出登录</div>
    </pull-to>
  </div>
</template>

<script>
import PullTo from "vue-pull-to";
import PersonalName from "./components/PersonalInfo";
import myorderType from "./components/myOrderType";
import WorkerType from "./components/workerType";
import { mapState, mapMutations } from "vuex";
import global from "@/utils/Global";
export default {
  name: "PersonalCenter",
  components: {
    PersonalName,
    myorderType,
    WorkerType,
    PullTo
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "isLogin"])
  },
  data() {
    return {
      userType: null, //院内职工
      leaguerType: "",
      workList: [],
      bottomPadding: false, //ios使用该属性，底部被返回按钮遮挡
      loadSucc: true,
      TOP_DEFAULT_CONFIG: {},
      ifIomsList: false //是否展示我的投诉
    };
  },
  methods: {
    ...mapMutations(["saveLeaguerType"]),
    /**
     * 下拉刷新
     */
    reload(loaded) {
      this.getOrderData();
      if (this.loadSucc) {
        loaded("done");
        this.loadSucc = false;
      }
    },
    /**
     * 获取列表数据请求
     */
    getOrderData() {
      //院内保持原样 院外暂时使用获取统计的接口
      if (this.userType == 1) {
        this.$api
          .getPersonalCentre({
            userId: this.loginInfo.id,
            staffType: this.userType,
            startTime: "",
            endTime: "",
            leaguerType: this.leaguerType,
            staffId: this.staffInfo.staffId
          })
          .then(this.getOrderDataSucc);
      } else {
        this.getWorkerOrderList();
      }
    },
    //获取班组人员 任务统计数据
    getWorkerOrderList() {
      this.$api
        .getTeamWorkInfo({
          userId: this.loginInfo.id,
          staffType: this.userType,
          startTime: "",
          endTime: "",
          leaguerType: this.leaguerType,
          staffId: this.staffInfo.staffId,
          designateDeptCode: this.staffInfo.teamId,
          designatePersonCode: this.staffInfo.teamPersonId
        })
        .then(res => {
          // console.log(res,'resx1');
          this.workList = [res];
        });
    },
    /**
     * 获取列表数据成功函数
     */
    getOrderDataSucc(res) {
      if (res.code == 500 || res.code == 400) {
        return;
      }
      this.ifIomsList = true;
      if (this.userType == 1) {
        //院内

        this.workList = res.workList;
      } else {
        //院外
        this.$api
          .getHospitalDispatchingConfig({
            hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
            unitCode: this.loginInfo.userOffice[0].unitCode
          })
          .then(item => {
            //item 1：自动派工，2：非自动派工
            if (item == 1) {
              if (this.leaguerType == 1) {
                this.workList = res.workList.concat(res.myTask);
              } else {
                if (this.workList.length == 0) this.workList.push(res.myTask);
              }
            } else {
              this.workList = res.workList.concat(res.myTask);
            }
          });
      }

      this.loadSucc = true;
    },
    /**
     * 获取职工角色信息的请求
     */
    getUserRole() {
      this.axios
        .get(__PATH.ONESTOP + "/appDisUserLoginController.do?getUserRole", {
          params: {
            staffId: this.staffInfo.staffId
          },
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          }
        })
        .then(this.handleGetUserRoleSucc);
    },
    /**
     * 获取职工角色成功回调函数
     */
    handleGetUserRoleSucc(response) {
      let res = response.data;
      if (res.code == 200) {
        let allRole = global.leaderLists;
        for (let i = 0; i < res.data.length; i++) {
          if (allRole.indexOf(res.data[i]) > -1) {
            //角色为组长
            this.leaguerType = 1;
          } else {
            //角色为职员
            this.leaguerType = 2;
          }
        }
        if (res.data.length == 0) {
          //角色为职员
          this.leaguerType = 2;
        }
        this.saveLeaguerType(this.leaguerType);
        // this.getOrderData(true); //初次进入该页面，需要显示toast数据加载中
      }
    },
    /**
     * 跳转我的申报页面
     */
    showMyOrderList() {
      this.$router.push({
        path: "/myorder",
        query: {
          leaguerType: this.leaguerType,
          type: "2"
        }
      });
    },
    /**
     * 跳转到后勤投诉/我的投诉页
     */
    toComplainList(source) {
      this.$router.push({
        name: "ComplaintList",
        params: {
          source: source
        }
      });
    },
    /**
     * 跳转设备运维
     */
    myWorkbench() {
      window.location.href = process.env.WX + "/imas";
    },
    /**
     * 跳转服务巡检
     */
    serviceInspection() {
      window.location.href = process.env.WX + "/ipms/mobile.html";
    },
    /**
     * 跳转资产盘点
     */
    assetsInventory() {
      window.location.href = process.env.WX + "/iaash5";
    },
    /**
     * 跳转医疗设备盘点
     */
    assetsInventoryImes() {
      window.location.href = process.env.WX + "/imesh5";
    },
    /**
     * 跳转我的问卷
     */
    MyQuestionnaire() {
      // window.location.href = process.env.WX + `/nair/#/questionList?id=${this.loginInfo.id}&unitCode=${this.loginInfo.userOffice[0].unitCode}&hospitalCode=${this.loginInfo.userOffice[0].hospitalCode}`;
      window.location.href = process.env.WX + "/nair/#questionList";
    },
    /**
     * 跳转医疗废物
     */
    medicalWaste() {
      window.location.href = process.env.WX + "/imws";
    },
    /**
     * 跳转过会投票
     */
    goPurchase() {
      // window.location.href = process.env.WX + '/iaash5/#/purchaseList';
      window.location.href =
        "http://wx.shxasset.logimis.com/#/" + "purchaseList";
    },
    /**
     * 退出登录
     */
    handleBtnClick() {
      $.confirm(
        "确定退出登录？",
        "",
        function() {
          //点击确认后的回调函数
          try {
            $.toast("已退出登录", "success", () => {
              sessionStorage.clear();
              localStorage.clear();
              global.delCookie("isLogiifn");
              global.delCookie("loginInfo");
              global.delCookie("staffInfo");
              global.delCookie("prevLinkUrl");
              global.delCookie("openId");
              global.delCookie("unionid");
              window.location.href = process.env.WX + "/login";
            });
          } catch (e) {}
        },
        function() {
          //点击取消后的回调函数
        }
      );
    },
    /**
     * 判断手机终端（安卓、ios）
     */
    downApp() {
      if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
        //Ios
        this.bottomPadding = true;
      } else if (/(Android)/i.test(navigator.userAgent)) {
        //Android终端
      }
    },
    /**
     * 获取openId（临时版本使用）
     */
    getOpenId() {
      let appid = process.env.WxAppid;
      let locationUrl = process.env.WX;
    }
  },
  mounted() {
    this.downApp();
    // this.userType = 1;
    //2是班组 1是院内
    this.userType = this.staffInfo.teamId ? 2 : 1;
    this.getUserRole();
    try {
      //清空之前选择的时间记录
      sessionStorage.removeItem("selectTime");
    } catch (err) {}
    this.TOP_DEFAULT_CONFIG = {
      pullText: "下拉刷新", // 下拉时显示的文字
      triggerText: "松手刷新", // 下拉到触发距离时显示的文字
      loadingText: "加载中...", // 加载中的文字
      doneText: "已刷新", // 加载完成的文字
      failText: "刷新失败", // 加载失败的文字
      loadedStayTime: 400, // 加载完后停留的时间ms
      stayDistance: 50, // 触发刷新后停留的距离
      triggerDistance: 70 // 下拉刷新触发的距离
    };
    /*临时版本添加获取openId*/
    if (location.href.includes("code")) {
      $.toast("检测中……", "text");
      const code = global.getQueryString();
      this.$api
        .getOpenid({
          code: code,
          appCode: process.env.WxAppcode
        })
        .then(res => {
          let getOpenId = res.openId;
          let getUnionid = res.unionid;
          this.$api
            .saveUserWechat({
              wechat: getOpenId,
              unionid: getUnionid,
              userId: this.loginInfo.id,
              type: this.userType
            })
            .then(res => {
              //更改缓存中登录信息中的openId
              localStorage.openId = getOpenId;
              localStorage.unionid = getUnionid;
              let cacheLoginInfo = JSON.parse(localStorage.loginInfo);
              let cacheStaffInfo = JSON.parse(localStorage.staffInfo);
              cacheLoginInfo.wechat = getOpenId;
              cacheLoginInfo.unionid = getUnionid;
              cacheStaffInfo.wechat = getOpenId;
              cacheStaffInfo.unionid = getUnionid;
              localStorage.loginInfo = JSON.stringify(cacheLoginInfo);
              localStorage.staffInfo = JSON.stringify(cacheStaffInfo);

              global.setCookie("openId", getOpenId, 365);
              global.setCookie("unionid", getUnionid, 365);
              let cacheStaffInfoCookie = JSON.parse(
                global.getCookie("staffInfo")
              );
              let cacheLoginInCookie = JSON.parse(
                global.getCookie("loginInfo")
              );
              cacheLoginInCookie.wechat = getOpenId;
              cacheLoginInCookie.unionid = getUnionid;
              cacheStaffInfoCookie.wechat = getOpenId;
              cacheStaffInfoCookie.unionid = getUnionid;
              global.setCookie(
                "loginInfo",
                JSON.stringify(cacheLoginInCookie),
                365
              );
              global.setCookie(
                "staffInfo",
                JSON.stringify(cacheStaffInfoCookie),
                365
              );
              $.toast("检测完成", "success");
            });
        });
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.wrapper {
  height: 100%;
  background-color: $bgColor;

  .center-content {
    background-color: $bgColor;

    .iconfont {
      color: #e5e5e5;
    }

    .my-order {
      display: flex;
      justify-content: space-between;
    }
  }

  .bottomPadding {
    // padding-bottom: 50px
  }

  .item-css {
    itemBaseStyle();
    marginBottom20();
    font-size: 0.32rem;
    text-align: center;
    color: $darkTextColor;
  }

  .no-mBottom {
    margin-bottom: 0;
  }

  .drop-out {
    margin: 0;
    width: 100%;
    padding: 0;
    border-top: 1px solid $bgColor;
  }
}
</style>
