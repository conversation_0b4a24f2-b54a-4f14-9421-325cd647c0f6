<template>
  <div class="edit-wrapper">
    <div class="item border-bottom" v-if="editNumJob">
      <span class="left">职工工号</span>
      <input
        type="text"
        class="right"
        v-model="jobNumberVal"
        v-focus
      >
    </div>
    <div class="item border-bottom" v-if="editProfession">
      <span class="left">工种</span>
      <!--<span class="right" @click="getProfessionData">{{ professionVal }}</span>-->
      <input
          type="text"
          class="right"
          v-model="professionVal"
          @click="getProfessionData"
      >
    </div>
    <div class="item border-bottom" v-if="editMobile">
      <span class="left">联系电话</span>
      <input
        type="text"
        class="right"
        v-model="mobileVal"
        v-focus
      >
    </div>
    <div class="item border-bottom" v-if="editMail">
      <span class="left">电子邮箱</span>
      <input
        type="text"
        class="right"
        v-model="mailVal"
        v-focus
      >
    </div>
    <div class="btn" @click="saveEditedInfo">
      <div class="save">保存</div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import { mapState } from 'vuex'
import global from '@/utils/Global'
  export default {
    name: "EditeInfo",
    computed: {
        ...mapState(["loginInfo"])
    },
    data () {
      return {
        editNumJob: false,
        editProfession: false,
        editMobile: false,
        editMail: false,
        jobNumberVal:'',
        professionVal:'',
        mobileVal:'',
        mailVal:'',
        baseInfo:{}
      }
    },
    methods: {
      /**
       * 获取工种所有数据
       */
      getProfessionData () {
        this.axios.post(process.env.API_BASE + '/dictController/getDictArray',
            this.$qs.stringify({
              type:'profession'
            })
            )
            .then(this.getProfessionDataSucc)
      },
      /**
       * 获取工种成功函数
       */
      getProfessionDataSucc (res) {
        let that = this
        if (res.data.code == 200 && res.data.data.length > 0){
          let arrData = res.data.data
          let emptyArr = []
          for (let [idx,val] of arrData.entries()) {
            emptyArr.push({
              text: val,
              onClick: function() {
                that.professionVal = val
              }
            })
          }
          $.actions({
            actions: emptyArr
          })
        }else{
          $.toast("未获取到工种")
        }
      },
      /**
       * 点击保存按钮
       */
      saveEditedInfo () {
        if (this.editNumJob) {
          this.baseInfo.jobNum = this.jobNumberVal
        }else if (this.editMobile){
          if(!global.regTel(this.mobileVal)){
            $.toast("请输入正确的手机号", "text");
            return false
          }
          this.baseInfo.phone = this.mobileVal
        }else if (this.editMail){
          if(!global.regEmail(this.mailVal)){
            $.toast("邮箱格式不正确", "text");
            return false
          }
          this.baseInfo.email = this.mailVal
        }else if (this.editProfession){
          this.baseInfo.profession = this.professionVal
        }
        this.saveUpdateStaffInfo()
      },
      /**
       * 保存已更改的信息
       */
      saveUpdateStaffInfo ()  {
        $.showLoading()
        this.axios.post(
            process.env.API_HOST + '/userController/updateMyCenter',
            this.$qs.stringify(this.baseInfo)
        )
            .then(this.getUpdateStaffInfoSucc)
      },
      /**
       * 更新信息成功函数
       */
      getUpdateStaffInfoSucc (res) {
        $.hideLoading()
        let that = this
        if (res.data.code == 200) {
          this.updateCacheInfo()
        }else{
          $.toast(res.data.message,'text');
        }
      },
      /**
       * 更新缓存信息
       */
      updateCacheInfo () {
        if (this.editNumJob) {
          this.synchronizeCache('jobNumber','')
        }else if (this.editMobile){
          this.synchronizeCache('mobile','phone')
        }else if (this.editMail){
          this.synchronizeCache('mail','email')
        }else if (this.editProfession){
          this.synchronizeCache('profession','')
        }
      },
      /**
       * 同步缓存中的内容
       */
      synchronizeCache (staffInfoField,loginInfoField) {
        //更新localStorage中的信息
        if(loginInfoField){  //空或undefined时说明此字段在loginInfo中不存在
          let cacheLoginInfo = JSON.parse(localStorage.loginInfo)
          switch (loginInfoField) {
            case 'phone':
              cacheLoginInfo.phone = this.mobileVal
              break
            case 'email':
              cacheStaffInfo.email = this.mailVal
              break
          }

          let cacheLoginInfoStr = JSON.stringify(cacheLoginInfo)
          localStorage.loginInfo = cacheLoginInfoStr
          global.setCookie('loginInfo',cacheLoginInfoStr,365)
          this.$store.state.loginInfo = cacheLoginInfo
        }

        let cacheStaffInfo = JSON.parse(localStorage.staffInfo)
        switch (staffInfoField) {
          case 'mobile':
            cacheStaffInfo.mobile = this.mobileVal
            break
          case 'jobNumber':
            cacheStaffInfo.jobNumber = this.jobNumberVal
            break
          case 'mail':
            cacheStaffInfo.mail = this.mailVal
            break
        }

        let cacheStaffInfoStr = JSON.stringify(cacheStaffInfo)
        localStorage.staffInfo = cacheStaffInfoStr

        global.setCookie('staffInfo',cacheStaffInfoStr,365)
        this.$store.state.staffInfo = cacheStaffInfo
        let that = this
        $.toast("修改成功",function(){
          that.$router.push('/personalCenter')
        });
      },
    },
    created () {
      this.baseInfo = {
        hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
        unitCode: this.loginInfo.userOffice[0].unitCode,
        userId: this.loginInfo.id,
        // type:this.loginInfo.type,
        type: this.staffInfo.teamId ? 2 : 1,
        interfaceNum:'1'
      }
      let key = this.$route.params.key;
      let val = this.$route.params.value;
      //判断编辑的是哪条信息并显示
      switch (key) {
        case 'editNumJob':
          this.editNumJob = true
          this.jobNumberVal = val == "无" ? "" : val
          break
        case 'editProfession':
          this.editProfession = true
          this.professionVal = val == "无" ? "" : val
          break
        case 'editMobile':
          this.editMobile = true
          this.mobileVal = val == "无" ? "" : val
          break
        case 'editMail':
          this.editMail = true
          this.mailVal = val == "无" ? "" : val
          break
      }
    },
    directives: {
      focus: {
        // 指令的定义
        inserted: function (el) {
          el.focus()
        }
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .edit-wrapper
    height: 100%
    background-color: $bgColor
    .item
      itemBaseStyle()
      display: flex
      justify-content: space-between
      font-size: .32rem
      padding: 0 0.4rem 0 .32rem
      .right
        color: $contentColor
        text-align: right
    .btn
      padding: 0 .3rem
      width: 100%
      margin-top: 20px
      box-sizing border-box
      .save
        height: .88rem
        width: 100%
        display: flex
        justify-content: center
        align-items: center
        color: #fff
        font-size: .36rem
        background-color: $btnColor
        border-radius: 5px

</style>
