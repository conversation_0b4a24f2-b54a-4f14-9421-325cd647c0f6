<template>
  <div class="wrapper">
    <div class="name border-bottom">
      <div class="title">姓名</div>
      <div class="content">
        <input type="text" placeholder="请输入2-20位汉字" v-model="name" minlength="2" maxlength="20" >   
        <span class="iconfont">&#xe675;</span>
      </div>
    </div>
    <div class="job-num border-bottom">
      <div class="title">工号</div>
      <div class="content">
        <input type="text" placeholder="2-20位数字 字母 _ - 任意组合" v-model="jobNum" minlength="2" maxlength="20" >
        <span class="iconfont">&#xe675;</span>
      </div>
    </div>
    <!--院内的都可以修改科室-->
    <div class="dept border-bottom" v-if="userType == 1">
      <div class="title">科室</div>
      <div class="content" @click="toSelectDept">
        <span class="value">{{ deptInfo.deptName || staffInfo.officeName }}</span>
        <span class="iconfont">&#xe646;</span>
      </div>
    </div>
    <!--院外不可以修改，只是展示-->
    <div class="dept border-bottom" v-if="userType == 2">
      <div class="title">班组</div>
      <div class="content">
        <span class="value value-bz">{{ dept.deptName }}</span>
      </div>
    </div>
    <div class="psw border-bottom">
      <div class="title">登录密码</div>
      <div class="content" @click="toSetPsw">
        <span class="value"></span>
        <span class="iconfont">&#xe646;</span>
      </div>
    </div>
    <div class="phone border-bottom">
      <div class="title">电话</div>
      <div class="content" @click="toSetTel">
        <!-- <span class="value">{{ loginInfo.phone }}</span> -->
        <span class="value">{{ phone }}</span>
        <span class="iconfont">&#xe646;</span>
      </div>
    </div>
    <div class="btn" @click="saveTemporaryUserInfo">
      <button class="weui-btn weui-btn_primary">保存</button>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import utils from '@/utils/Global'
import qs from 'qs'
import { mapState,mapMutations } from 'vuex'
import PopBottom from '@/common/PopupBottom'
  export default {
    name: "ImproveInfo",
    components: {
      PopBottom
    },
    computed: {
      ...mapState(['hospitalInfo','deptInfo','loginInfo','staffInfo'])
    },
    data () {
      return {
        psw: "",//密码
        name: "",//姓名
        jobNum: "",//工号
        phone: "",//手机号码
        isLeader:"",//是否为领导,1为领导
        dept:{},//部门/班组编号
        userType: null
      }
    },
    methods: {
      /**
       * 保存
       */
      saveTemporaryUserInfo () {
        if(!this.deptInfo.deptName && !this.staffInfo.officeName){
          $.toast("请选择科室","text")
          return
        }
        // if(!this.jobNum){
        //   $.toast("请输入工号","text")
        //   return
        // }
        if(!this.name){
          $.toast("请输入姓名","text")
          return
        }
        let params = {
          interfaceNum:'1',
          type:this.userType,
          userId:this.staffInfo.staffId,
          phone:this.phone == this.loginInfo.phone ? "" : this.phone,//电话
          jobNum:this.jobNum == this.staffInfo.jobNumber ? "" : this.jobNum,//工号
          password:this.psw,//密码
          name:this.name == this.loginInfo.staffName ? "" : this.name,//姓名
          deptCode:this.userType == 1 ? this.deptInfo.deptCode　: this.dept.deptCode,//科室，部门
        };
        if(!params.deptCode){
          params.deptCode = this.staffInfo.officeId;
        }
        const headers = {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
        Object.assign(params, {
          hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
          unitCode: this.loginInfo.userOffice[0].unitCode,
        })
        // this.$api.updateMyCenter(params).then( res => {
        this.axios.post(process.env.API_HOST + '/userController/updateMyCenter', qs.stringify(params), headers).then( res => {
          let _this = this
          const data = res.data
          if(data.code == 200){
            $.toast("更新成功",function() {
              _this.reSetLocationInfo()
              if(_this.$route.query.source == "feed"){
                _this.$router.replace({path: "FeedbackDetails"})
              }else{
                _this.$router.push("/personalCenter")
              }
           })
          } else {
            $.toast(data.message,"text")
          }
        })
      },
      /**
       * 更改本地缓存中的信息
       */
      reSetLocationInfo () {
        let localLoginInfo = this.loginInfo
        let localStaffInfo = this.staffInfo

        if(this.phone == this.loginInfo.phone ? "" : this.phone){
          //修改了手机号
          localLoginInfo.phone = this.phone
          localStaffInfo.mobile = this.phone
        }
        if(this.name == this.staffInfo.name ? "" : this.name){
          //修改了姓名
          localStaffInfo.name = this.name
          localLoginInfo.name = this.name
        }
        if(this.jobNum == this.staffInfo.jobNumber ? "" : this.jobNum){
          //修改了工号
          localStaffInfo.jobNumber = this.jobNum
        }
        if(this.userType == 1 && this.deptInfo.deptName != "" && this.deptInfo.deptName != this.staffInfo.officeName){
          //院内的，修改了科室
          localStaffInfo.officeName = this.deptInfo.deptName
          //同时重新请求职工信息接口，更新职工信息
          axios.get( process.env.API_BASE + "/hospitalStaff/hospital-staff/getDetails",
          // process.env.API_BASE + "/outsourcedController/getStaffInfoByType",
          {
            params: {
              id: localLoginInfo.userOffice[0].staffId
            },
            headers: {
              unitCode: localLoginInfo.userOffice[0].unitCode,
              hospitalCode: localLoginInfo.userOffice[0].hospitalCode,
            }
          // this.$api.getStaffInfoByType({
            // id: this.loginInfo.id,
            // userId: this.loginInfo.id,
            // type: "1"
          }).then(res => {
            const item = res.data.data
            item.staffId = item.id;
            localStaffInfo = item
            this.setStaffInfo(item)
            localStorage.staffInfo = JSON.stringify(item)
          })
        }
        utils.setCookie('loginInfo',JSON.stringify(localLoginInfo),365)
        utils.setCookie('staffInfo',JSON.stringify(localStaffInfo),365)
        this.setLoginInfo(localLoginInfo)
        this.setStaffInfo(localStaffInfo)
        localStorage.loginInfo = JSON.stringify(localLoginInfo)
        localStorage.staffInfo = JSON.stringify(localStaffInfo)
        sessionStorage.removeItem('psw')
        sessionStorage.removeItem('tel')
      },
      /**
       * 设置密码
       */
      toSetPsw () {
        this.$router.push({
          path:"/setPwd",
          query:{
            source:this.$route.query.source
          }
        })
      },
      /**
       * 设置电话
       */
      toSetTel () {
        this.$router.push({
          path:"/setTel",
          query:{
            source:this.$route.query.source
          }
        })
      },
      /**
       * 选择科室（院内）
       */
      toSelectDept () {
        this.$router.push({
          path:'/department',
          query:{
            hospitalCode:this.loginInfo.userOffice[0].hospitalCode,
            unitCode:this.loginInfo.userOffice[0].unitCode,
            source:this.$route.query.source,
            pathName:"/improveInfo"
          }
        })
      },
      ...mapMutations(['setLoginInfo','setStaffInfo']),
    },
    created () {
      this.isLeader = this.$route.query.leaguerType
      this.userType = this.staffInfo.teamId ? 2 : 1
    },
    activated () {
      this.name = this.name || this.loginInfo.staffName
      this.jobNum = this.jobNum || this.staffInfo.jobNumber
      this.psw = sessionStorage.getItem('psw') || ""
      if(this.$route.query.SetTel){
        this.phone = this.loginInfo.phone
      }else{
        this.phone = sessionStorage.getItem('tel')  || this.loginInfo.phone
      }
      let str = this.$route.query.deptInfo
      if(!((typeof str=='string')&&str.constructor==String)){
        this.dept = this.$route.query.deptInfo || {deptCode:this.staffInfo.officeId,deptName:this.staffInfo.officeName}
      }else{
        this.dept = this.dept
      }
    },
    watch:{
      name(){
        this.name = this.name.replace(/[^\u4e00-\u9fa5]{2,20}/g,'');
      },
      jobNum(){
        this.jobNum = this.jobNum ? this.jobNum.replace(/[^\w]{2,20}/g,'') : '';
      },
    },
    
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
.wrapper
  background: $bgColor
  height 100%
  > div
    itemBaseStyle()
    display flex
    .title
      width: 1.97rem
    .content
      color: $textColor
      display: flex
      justify-content: space-between
      flex: 1
      align-items: center
      .value
        color: $contentColor
        line-height: 1
  input
    &::-webkit-input-placeholder
       color: $textColor
    color: $contentColor
    width: 100%
  .btn
    padding-top: .22rem
    padding-bottom: .22rem
    position: fixed
    bottom: 0
    left: 0
    right: 0
.value-bz
    display: inline-block;
    max-width: 260px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
</style>
