<template>
  <div class="data-wrapper">
    <div class="item border-bottom">
      <span class="left">姓名</span>
      <span class="right">{{ staffInfo.name }}</span>
    </div>
    <div class="item border-bottom" @click="goToEditInfoPage('editNumJob',staffInfo.jobNumber)">
      <span class="left">职工工号</span>
      <span class="right">{{ staffInfo.jobNumber ? staffInfo.jobNumber : "无" }}<span class="iconfont changed">&#xe646;</span></span>
    </div>
    <div class="item border-bottom" v-if="staffInfo.type == 1">
      <span class="left">所属医院</span>
      <span class="right">{{ staffInfo.hospitalName }}</span>
    </div>
    <div class="item border-bottom" v-if="staffInfo.type == 1">
      <span class="left">所属部门</span>
      <span class="right">{{ staffInfo.officeName ? staffInfo.officeName : "无" }}</span>
    </div>
    <div class="item border-bottom" v-if="staffInfo.type == 1">
      <span class="left">职务/职级</span>
      <span class="right">{{ staffInfo.administrativePost ? staffInfo.administrativePost : "无" }}</span>
    </div>
    <div class="item border-bottom" v-if="staffInfo.type == 2">
      <span class="left">所属公司</span>
      <span class="right">{{ staffInfo.companyName }}</span>
    </div>
    <div class="item border-bottom" v-if="staffInfo.type == 2">
      <span class="left">所在班组</span>
      <span class="right">{{ staffInfo.officeName ? staffInfo.officeName : "无" }}</span>
    </div>
    <div class="item border-bottom" v-if="staffInfo.type == 2">
      <span class="left">工种</span>
      <span class="right">{{ staffInfo.profession ? staffInfo.profession : "无" }}</span>
    </div>
    <div class="item border-bottom" @click="goToEditInfoPage('editMobile',staffInfo.mobile)">
      <span class="left">联系电话</span>
      <span class="right">{{ staffInfo.mobile ? staffInfo.mobile : "无" }}<span class="iconfont changed">&#xe646;</span></span>
    </div>
    <div class="item border-bottom" v-if="staffInfo.type == 1" @click="goToEditInfoPage('editMail',staffInfo.mail)">
      <span class="left">电子邮箱</span>
      <span class="right">{{ staffInfo.mail ? staffInfo.mail : "无" }}<span class="iconfont changed">&#xe646;</span></span>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import { mapState } from 'vuex'
  export default {
    name: "MyData",
    computed: {
      ...mapState(['staffInfo'])
    },
    methods:{
      /**
       * 跳转编辑信息页面
       */
      goToEditInfoPage (key,val) {
        this.$router.push({
          name:"EditInfoPage",
          params:{
            key:key,
            value:val
          }
        })
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
.data-wrapper
  height: 100%
  background-color: $bgColor
  .item
    itemBaseStyle()
    display: flex
    justify-content: space-between
    font-size: .32rem
    padding: 0 0.4rem 0 .32rem
    position: relative
    .right
      color: #888
      text-align: right
    .changed
      color: #cecece
      font-size: 14px
      position: absolute
      right:2px

</style>