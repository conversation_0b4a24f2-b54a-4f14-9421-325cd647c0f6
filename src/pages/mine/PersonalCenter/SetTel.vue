<template>
  <div class="wrapper">
    <div class="ipts">
      <div class="weui-cell weui-cell_select weui-cell_select-before ipt-tel-num">
        <div class="phone-prefix border-bottom">
          <div class="phone-refix-content">
            <span class="tel-prefix">+86</span>
            <span class="iconfont select-arrow">&#xe600;</span>
          </div>
          <phone-text :isShowPhoneText='false' @getSmsCode="handleGetSmsCode"></phone-text>
        </div>
      </div>
      <div class="weui-cell vertify-code border-topbottom">
        <input class="weui-input" maxlength="6" type="text" v-model="smsCode" placeholder="请输入验证码">
      </div>
    </div>
    <div class="login-btn">
      <button
          class="weui-btn weui-btn_primary"
          :class="{'weui-btn_disabled':isDis2}"
          :disabled="disabled2"
          @click="handleLoginSmsClick"
      >
        确定
      </button>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import PhoneText from "@/common/PhoneText"
  export default {
    name: "SetTel",
    components: {
      PhoneText
    },
    data () {
      return {
        disabled: true,
        type: "text",
        isShowPsw: true,
        username: '',
        password: '',
        phone: '',
        smsCode:'',
        getSmsCode:'',
        isDis1:true,
        disabled1:true,
        isDis2:true,
        disabled2:true,
        goToRegister:false
      }
    },
    methods: {
      /**
       * 获取手机短信验证码
       */
      handleGetSmsCode (smscode,phone) {
        this.phone = phone
        this.getSmsCode = smscode
      },
      /**
       * 点击确认
       */
      handleLoginSmsClick () {
        if(this.getSmsCode != this.smsCode){
          $.toast("验证码错误，请重新输入","text")
        }else{
          if(this.$route.query.source == "feed"){
            sessionStorage.setItem('telFeed',this.phone);
            this.$router.go(-1);
          }else{
            sessionStorage.setItem('tel',this.phone);
            this.$router.push({
              path:"/improveInfo",
              query:{
                source:this.$route.query.source
              }
            });
          }
        }
      },
    },
    updated () {
      if(this.phone != "" && this.smsCode != ''){
        this.isDis2 = false
        this.disabled2 = false
      }else{
        this.isDis2 = true
        this.disabled2 = true
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  .wrapper
    .title
      font-size: .48rem
      margin: 0.46rem 0.32rem 0.36rem;
    .ipts
      padding: 0 .5rem
      position: relative
      > div
        margin-top: .8rem
      .ipt-tel-num
        padding-right: 0
        .phone-prefix
          display: flex
          border-bottom: 1px solid #efefef
          .phone-refix-content
            margin-right: -20px
            display: flex
            align-items: center
            .weui-select
            .tel-prefix
              width: 35px
              height: 40px
              padding-right: 0
            .tel-prefix
              line-height: 45px
              padding-left: 15px
            .select-arrow
              font-size: 12px
              color: #a7a7a7
              padding-right: 5px
              margin-bottom: -5px
    .login-btn
      margin: .8rem .2rem 0
    .registerBtn
      margin: .4rem 0
      text-align: center
      color: $textColor
</style>
