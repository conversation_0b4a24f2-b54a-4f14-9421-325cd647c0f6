<template>
  <div class="type-wrapper">
    <div class="item" v-for="(item,index) of workList" :key="index">
      <div class="title">
        <span>我的申报</span>
        <span class="all" @click="getOrderList(2)">全部申报 <span class="iconfont">&#xe646;</span> </span>
      </div>
      <div class="line"></div>
      <ul class="orders">
        <li @click="getOrderList(2)">
          <span class="num">{{ item.untreated || 0 }}</span>
          <span class="status">未派工</span>
        </li>
        <li @click="getOrderList(3)">
          <span class="num">{{ item.dispatched || 0 }}</span>
          <span class="status">已派工</span>
        </li>
        <li @click="getOrderList(4)">
          <span class="num">{{ item.register || 0 }}</span>
          <span class="status">已挂单</span>
        </li>
        <!--<li @click="getOrderList(5)">
          <span class="num">{{ item.completed || 0 }}</span>
          <span class="status">已结束</span>
        </li>-->
      </ul>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import { mapState } from 'vuex'
  export default {
    name: "OrderType",
    props:['workList',"leaguerType"],
    computed: {
        ...mapState(['loginInfo','staffInfo'])
    },
    data () {
      return {
        type:1
      }
    },
    methods: {
      /**
       * 跳转到列表页
       * @param flowtype
       */
      getOrderList (flowtype) {
        this.$router.push({
          path:'/myorder',
          query:{
            type:flowtype,
            leaguerType:this.leaguerType
          }
        })
      }
    },
    mounted () {
      // this.type = this.loginInfo.type
      this.type = this.staffInfo.teamId ? 2 : 1
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .type-wrapper
    color: $darkTextColor
    .item
      padding: 0 .32rem
      background-color: #fff
      marginBottom20()
      .title
        height: .98rem
        line-height: .98rem
        font-size: .32rem
        .all
          float: right
          color: $textColor
          font-size: .28rem
          .iconfont
            font-size: .28rem
      .line
        width: 100%
        height: 1px
        background-color: $bgColor
      .orders
        display: flex
        height: 1.55rem
        li
          display: flex
          flex-direction: column
          align-items: center
          justify-content: center
          flex: 1
          .num
            font-size: .4rem
            margin-bottom: .32rem
          &:nth-child(1)
            .num
              color: #3CD4B9
          &:nth-child(2)
            .num
              color: #67A1FF
          &:nth-child(3)
            .num
              color: #FFA545
          &:nth-child(4)
            .num
              color: #AD98F1
          .status
            font-size: .3rem
</style>
