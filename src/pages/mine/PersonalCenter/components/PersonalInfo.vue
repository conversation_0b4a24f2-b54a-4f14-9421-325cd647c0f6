<template>
  <div class="info-wrapper">
    <span class="iconfont" @click="viewInfo">&#xe646;</span>
    <div class="avatar-name">
      <div class="avatar">
        <img :src="staffInfo.picture" alt="头像" v-if="staffInfo.picture">
        <img src="@/assets/images/icon-test-avatar.png" v-else alt="头像">
      </div>
      <div class="name">{{ staffInfo.staffName }}</div>
    </div>
    <ul>
      <li>
        <span class="content">{{ staffInfo.officeName? staffInfo.officeName:'无' }}</span>
        <span class="title">{{ staffInfo.type == 2? "班组":"科室"}}</span>
      </li>
      <li>
        <span class="line first"></span>
        <span class="content">{{ staffInfo.mobile ? staffInfo.mobile : loginInfo.phone }}</span>
        <span class="title">手机号</span>
        <span class="line next"></span>
      </li>
      <li v-if="staffInfo.type == 2">
        <span class="content text-css">{{ staffInfo.profession? staffInfo.profession:'无'}}</span>
        <span class="title">归属专业</span>
      </li>
      <li v-else>
        <span class="content text-css">{{ staffInfo.administrativePost? staffInfo.administrativePost:'无'}}</span>
        <span class="title">职位</span>
      </li>
    </ul>
  </div>
</template>

<script type=text/ecmascript-6>
  import { mapState } from 'vuex'
  export default {
    name: "PersonalName",
    props:["leaguerType"],
    computed: {
      ...mapState(['staffInfo','loginInfo'])
    },
    methods:{
      viewInfo () {
        this.$router.push('/improveInfo')
        this.$router.push({
          path:"/improveInfo",
          query:{
            leaguerType:this.leaguerType,
            SetTel:true
          }
        })
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
.info-wrapper
  height: 4.04rem
  background-color: $color
  position: relative
  marginBottom20()
  .iconfont
    color: #fff
    position: absolute
    top: 1rem
    right: .5rem
  .avatar-name
    display: flex
    flex-direction: column
    align-items: center
    .avatar
      width: 1.1rem
      height: 1.1rem
      border-radius: 50%
      margin: .67rem 0 .24rem 0
      img
        width: 100%
        height: 100%
        border-radius: 50%
    .name
      color: #fff
      font-size: .32rem
  ul
    display: flex
    color: #fff
    margin-top: .66rem
    li
      display: flex
      flex-direction:column
      align-items: center
      flex: 1
      width: 33.33%
      &:nth-child(2)
      &:nth-child(3)
        position: relative
      .line
        width: 1px
        height: .55rem
        background-color: rgba(255,255,255,0.6)
        position: absolute
        top: .11rem
      .first
          left: 0
      .next
          right: 0
      .title
        font-size: .28rem
        color: rgba(255,255,255,.6)
        padding-top: .2rem
      .content
        color: #fff
        font-size: .32rem
        text-align: center
        display: inline-block;
        max-width: 120px;
        overflow: hidden;
        height: 20px;
        line-height: 25px;
        white-space: nowrap;
        text-overflow: ellipsis;
      .text-css
        white-space: nowrap
        width: 2.3rem
        text-overflow: ellipsis
        overflow: hidden


</style>
