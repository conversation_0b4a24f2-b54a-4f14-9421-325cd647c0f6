<template>
  <div class="type-wrapper">
    <div class="item" v-for="(item,index) of workList" :key="index">
      <div class="title">
        <span>{{ item.workTypeName }}</span>
        <!-- <span>我的任务</span> -->
        <span class="all" @click="goToListPage(item.workTypeCode?2:3,item.workTypeCode)">全部 <span class="iconfont">&#xe646;</span> </span>
      </div>
      <div class="line"></div>
      <ul class="orders">
        <li @click="goToListPage(2,item.workTypeCode)" v-show="item.untreated">
          <span class="num">{{ item.untreated }}</span>
          <span class="status">未派工</span>
        </li>
        <li @click="goToListPage(3,item.workTypeCode)">
          <span class="num">{{ item.dispatched }}</span>
          <span class="status">已派工</span>
        </li>
        <li @click="goToListPage(4,item.workTypeCode)">
          <span class="num">{{ item.register }}</span>
          <span class="status">已挂单</span>
        </li>
        <!--<li @click="goToListPage(5,item.workTypeCode)">
          <span class="num">{{ item.completed }}</span>
          <span class="status">已结束</span>
        </li>-->
      </ul>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import {mapState} from 'vuex'
  import global from '@/utils/Global'
  export default {
    name: "worderType",
    props:['workList','leaguerType'],
    computed: {
      ...mapState(['staffInfo','saveLeaguerType'])
    },
    methods: {
      /**
       * 跳转到列表页
       * @param type 工单类型
       */
      goToListPage (type,workTypeCode) {
        this.$router.push({
          path:'/workbench',
          query:{
            type: type,
            workTypeCode:workTypeCode,
            leaguerType:this.saveLeaguerType
          }
        })
      },
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
  .type-wrapper
    color: $darkTextColor
    .item
      padding: 0 .32rem
      background-color: #fff
      marginBottom20()
      .title
        height: .98rem
        line-height: .98rem
        font-size: .32rem
        .all
          float: right
          color: $textColor
          font-size: .28rem
          .iconfont
            font-size: .28rem
      .line
        width: 100%
        height: 1px
        background-color: $bgColor
      .orders
        display: flex
        height: 1.55rem
        li
          display: flex
          flex-direction: column
          align-items: center
          justify-content: center
          flex: 1
          .num
            font-size: .4rem
            margin-bottom: .32rem
          &:nth-child(1)
            .num
              color: #FF437D
          &:nth-child(2)
            .num
              color: #67A1FF
          &:nth-child(3)
            .num
              color: #FFA545
          &:nth-child(4)
            .num
              color: #AD98F1
          .status
            font-size: .3rem
</style>