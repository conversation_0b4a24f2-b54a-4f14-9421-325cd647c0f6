<template>
  <div class="set-psw-wrapper">
    <div class="name border-bottom">
      <div class="title">新密码</div>
      <div class="content">
        <input type="text" placeholder="6-18位字母、数字和符号任意组合" maxlength="18" v-model="newPsw">
      </div>
    </div>
    <div class="job-num border-bottom">
      <div class="title">确认密码</div>
      <div class="content">
        <input type="text" placeholder="请输入密码" v-model="confirmPsw">
      </div>
    </div>
    <div class="btn" @click="sureBtn">
      <button class="weui-btn weui-btn_primary">确认</button>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import utils from '@/utils/Global'
  export default {
    name: "SetPwd",
    data () {
      return {
        newPsw:'',
        confirmPsw:''
      }
    },
    methods: {
      /**
       * 点击确认按钮
       */
      sureBtn () {
        if(!this.newPsw){
          $.toast("请输入新密码","text")
        }else if(this.newPsw && !utils.regPsw(this.newPsw)){
          $.toast("密码格式不正确","text")
        }else if(!this.confirmPsw){
          $.toast("请输入确认密码","text")
        }else if(this.newPsw != this.confirmPsw){
          $.toast("两次输入密码不一致，请重新输入","text")
        }else{
          sessionStorage.setItem('psw',this.newPsw)
          this.$router.push({
          path:"/improveInfo",
          query:{
            source:this.$route.query.source
          }
        })
        }
      },

    },
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/varibles.styl"
  @import "~styles/mixins.styl"
.set-psw-wrapper
  > div
    itemBaseStyle()
    display flex
    .title
      width: 1.97rem
    .content
      color: $textColor
      display: flex
      justify-content: space-between
      flex: 1
      input
        width: 100%
  .btn
    margin-top: .48rem
</style>