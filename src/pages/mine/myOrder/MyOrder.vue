<template>
  <div class="wrapper">
    <order-list :leaguerType="$route.query.leaguerType"></order-list>
  </div>
</template>

<script>
import OrderList from "./components/OrderList";
import { mapState } from "vuex";
export default {
  name: "MyOrder",
  components: {
    OrderList
  },
  computed: {
    ...mapState(["loginInfo"])
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped></style>
