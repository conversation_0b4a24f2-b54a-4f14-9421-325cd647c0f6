<template>
  <div class="area-content">
    <Header title="我的服务" @backFun="goBack"></Header>
    <!-- <div class="time-content border-bottom">
      <van-popup
        v-model="timeShow"
        position="bottom"
        :style="{ height: '50%' }"
      >
        <van-datetime-picker
          v-model="startTime"
          @cancel="timeShow = false"
          @confirm="selectStartTime"
          type="date"
          title="选择初始时间"
        />
      </van-popup>
      <van-popup
        v-model="timeShow2"
        position="bottom"
        :style="{ height: '50%' }"
      >
        <van-datetime-picker
          v-model="endTime"
          @cancel="timeShow2 = false"
          @confirm="selectEndTime"
          type="date"
          title="选择终止时间"
        />
      </van-popup>
      <span class="time-wrapper">
        <span class="start" @click="timeShow = true">{{ startTimeText }}</span>
        -
        <span class="end" @click="timeShow2 = true">{{ endTimeText }}</span>
        <span class="time-reset" @click="resetTime">重置</span>
      </span>
    </div> -->
    <div class="tab-content">
      <!-- v-if="staffType == 2"-->
      <div class="tab-title">
        <div @click="judgeIsSame('2')" :class="{ textStyle: curArea == 2 }">
          <span class="num">{{ untreated > 99 ? "99+" : untreated }}</span>
          <span class="title">未派工</span>
        </div>
        <div @click="judgeIsSame('3')" :class="{ textStyle: curArea == 3 }">
          <span class="num">{{ dispatched > 99 ? "99+" : dispatched }}</span>
          <span class="title">已派工</span>
        </div>
        <div @click="judgeIsSame('4')" :class="{ textStyle: curArea == 4 }">
          <span class="num">{{ register > 99 ? "99+" : register }}</span>
          <span class="title">已挂单</span>
        </div>
        <div @click="judgeIsSame('5')" :class="{ textStyle: curArea == 5 }">
          <span class="num">{{ completed > 99 ? "99+" : completed }}</span>
          <span class="title">已结束</span>
        </div>
      </div>
    </div>

    <div class="select-area mixing" v-if="isHaveData">
      <!-- :class="{same:staffType==1,mixing:staffType==2}"-->
      <pull-to :bottom-load-method="refresh" :is-top-bounce="false">
        <div class="weui-cells_checkbox">
          <ul class="region">
            <li class="border-bottom" v-for="item of dataArr" :key="item.taskId">
              <label class="weui-cell weui-check__label" v-if="fromNewComplain">
                <div class="weui-cell__hd">
                  <input type="radio" class="weui-check" name="checkbox1" checked="checked" :value="item"
                    v-model="checkedValue" />
                  <i class="weui-icon-checked"></i>
                </div>
              </label>
              <div class="content" @click="enterDetailInfo(item.taskId, item.flowcode)">
                <div class="middle">
                  <div class="middel-text modify_middle">
                    <div style="font-size: calc(16px * var(--font-scale))">
                      {{ item.workTypeName }}
                    </div>
                    <div class="right">
                      <span class="flowtype red bg-red" v-if="item.flowcode == '1'">{{ item.flowtype }}</span>
                      <span class="flowtype blue bg-green" v-if="item.flowcode == '2'">{{ item.flowtype }}</span>
                      <span class="flowtype green bg-green" v-if="item.flowcode == '3'">{{ item.flowtype }}</span>
                      <span class="flowtype yellow bg-green" v-if="item.flowcode == '4'">{{ item.flowtype }}</span>
                      <span class="flowtype yellow bg-green" v-if="item.flowcode == '5'">{{ item.flowtype }}</span>
                      <span class="flowtype yellow bg-green" v-if="item.flowcode == '6'">{{ item.flowtype }}</span>
                      <span class="flowtype yellow bg-green" v-if="item.flowcode == '15'">{{ item.flowtype }}</span>
                      <span class="iconfont">&#xe646;</span>
                    </div>
                  </div>
                  <div class="time-text-content" style="padding: 12px 0; font-size: calc(14px * var(--font-scale))">
                    <img src="@/assets/images/ic-time.png" class="ic-time-img" />
                    <span class="time-text">{{ item.createDate }}</span>
                  </div>
                  <div class="time-text">
                    <div style="display: flex">
                      <div class="time-text-title">工&nbsp;&nbsp;单&nbsp;&nbsp;号：</div>
                      <div class="department">
                        <span>{{ item.workNum }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="time-text" style="display: flex">
                    <div class="time-text-title">所属科室：</div>
                    <div class="department">
                      <span>{{ item.sourcesDeptName || '' }}</span>
                    </div>
                  </div>
                  <div class="time-text" style="display: flex">
                    <div class="time-text-title">服务部门：</div>
                    <div class="department">
                      <span>{{ item.designateDeptName  }}</span>
                    </div>
                  </div>
                  <div class="time-text" style="display: flex">
                    <div class="time-text-title">服务人员：</div>
                    <div class="department">
                      <span>{{ item.designatePersonName  }}</span>
                    </div>
                  </div>
                  <div class="time-text" style="display: flex">
                    <div class="time-text-title">服务事项：</div>
                    <div class="department">
                      <span>{{ item.serviceItemShow  }}</span>
                    </div>
                  </div>
                  <div class="time-text" style="display: flex">
                    <div class="time-text-title">服务地点：</div>
                    <div class="department">
                      <span>{{ item.localtionName  }}</span>
                    </div>
                  </div>
                  <div class="time-text" style="display: flex">
                    <div class="time-text-title">申报描述：</div>
                    <div class="department">
                      <span>{{ item.questionDescription  }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </pull-to>
    </div>
    <div class="submit-btn" v-if="fromNewComplain">
      <button class="weui-btn weui-btn_primary" :class="{ 'weui-btn_disabled': isDis }" :disabled="disabled"
        @click="completeSelection">确定</button>
    </div>
    <div v-if="!isHaveData" class="img-content">
      <img :src="emptyImg" alt="" />
      <p>暂时没有工单信息~</p>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import PullTo from "vue-pull-to";
import global from "@/utils/Global";
import emptyImg from "@/assets/images/noDataDefault/search-empty.png";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "OrderList",
  props: ["leaguerType"],
  mixins: [fontSizeMixin],
  components: {
    PullTo
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo", "h5Mode"])
  },
  data() {
    return {
      fromRoute: null,
      emptyImg,
      flag: false,
      staffType: "",
      type: "",
      isHaveData: true,
      curArea: "",
      // selectType: 2,
      curPage: 0,
      isDiff: false,
      dataArr: [],
      resNameData: [],
      resCodeData: [],
      startTime: "",
      startTimeText: "选择初始时间",
      endTime: "",
      endTimeText: "选择终止时间",
      untreated: 0,
      dispatched: 0,
      completed: 0,
      register: 0,
      fromNewComplain: false, //来源于新建投诉工单的关联工单号选择
      checkedValue: "",
      isDis: true,
      disabled: true,
      sourceFlag: "",
      timeShow: false,
      timeShow2: false,
      timer: ""
    };
  },
  methods: {
    goBack() {
      let isMy = this.$route.query.isMy;
      if (isMy) {
        // api.closeFrame();
        if(this.h5Mode == "apicloud") {
          try {
          api.sendEvent({
            name: "updateUserInfo"
          });
          this.$YBS.apiCloudCloseFrame();
        } catch (error) {
          console.log(error);
        }
        } else {
          window.parent.postMessage({ type: "closeIframe" }, "*");
        }
      } else {
        this.$router.go(-1);
      }
    },
    /**
     * 判断是否连续请求同一类型的工单列表
     * @param type 工单类型
     */
    judgeIsSame(type) {
      if (this.curArea != type) {
        //进行切换
        this.curPage = 0;
        this.isDiff = true;
        this.dataArr = [];
        //sessionStorage.selectType = type
        this.curArea = type;
        this.getOrderList(type);
      } else {
        this.isDiff = false;
      }
    },
    /**
     * 发送工单列表请求
     */
    getOrderList(type) {
      $.showLoading();
      let lastPage = this.curPage;
      this.curPage = ++lastPage;
      this.$api
        .getMineTask({
          // userId: this.staffInfo.teamPersonId,
          staffType: this.staffType,
          type: type,
          curPage: this.curPage,
          pageSize: 15,
          startTime: this.startTime,
          endTime: this.endTime,
          staffId: this.staffInfo.staffId
        })
        .then(this.getOrderListSucc);

      if (this.staffType == 1) {
        this.getWorkList(this.startTime, this.endTime);
      }
    },
    /**
     * 工单列表请求成功函数
     */
    getOrderListSucc(data) {
      console.log("data", data);
      $.hideLoading();
      if (this.staffType == 2) {
        //院内和院外返回数据格式不同
        //所有状态都没有值的时候，返回来的数据data为[]
        if (data.length == 0) {
          this.isHaveData = false;
          this.untreated = 0;
          this.dispatched = 0;
          this.register = 0;
          this.completed = 0;
          return;
        } else {
          this.untreated = data.untreated;
          this.dispatched = data.dispatched;
          this.register = data.register;
          this.completed = data.completed;
        }

        if (data.details.length == 0) {
          //this.curArea = JSON.parse(this.selectType)
          //第一次请求或者由时间查询结构
          if (this.dataArr.length == 0 || this.curPage == 1) {
            this.dataArr = [];
            this.isHaveData = false;
          }
          $.toast("没有更多的数据啦", "text");
        } else {
          // this.curArea = JSON.parse(this.selectType)
          this.isHaveData = true;
          if (this.isDiff) {
            this.dataArr = data.details;
          } else {
            this.dataArr = this.dataArr.concat(data.details);
          }
        }
      } else if (this.staffType == 1) {
        if (data.length == 0) {
          //第一次请求或者由时间查询结构
          if (this.dataArr.length == 0 || this.curPage == 1) {
            this.dataArr = [];
            this.isHaveData = false;
          }
          $.toast("没有更多的数据啦", "text");
        } else {
          this.isHaveData = true;
          if (this.isDiff || this.curPage == 1) {
            this.dataArr = data;
          } else {
            this.dataArr = this.dataArr.concat(data);
          }
        }
      }
      this.flag = true;
    },
    /**
     * 进入工单详情
     */
    enterDetailInfo(id, flowCode) {
      // return alert(flowCode)
      this.$router.push({
        path: "/details",
        query: {
          id: id,
          source: this.sourceFlag,
          flowCode
        }
      });
    },
    /**
     * 上拉加载更多
     */
    refresh(loaded) {
      this.isDiff = false;
      this.getOrderList(this.curArea);
      if (this.flag) {
        loaded("done");
        this.flag = false;
      }
    },
    /**
     * 进入时间筛选
     */
    selectTime() {
      this.$router.push("/time");
    },
    /**
     * 将-的时间格式化成年、月、日形式
     * @param time
     */
    formatTime(time) {
      let timeStr = "";
      if (time.includes("/")) {
        timeStr = time.split("/");
      } else {
        timeStr = time.split("-");
      }
      return timeStr[0] + "年" + timeStr[1] + "月" + timeStr[2] + "日";
    },
    /**
     * 将选择的关联工单号以及指派班组携带到投诉任务发布页面
     */
    completeSelection() {
      this.$router.push({
        name: "ComplainWorkOrder",
        params: {
          workNum: this.checkedValue.workNum,
          designateDeptCode: this.checkedValue.designateDeptCode,
          designateDeptName: this.checkedValue.designateDeptName
        }
      });
    },
    getWorkList(startTime, endTime) {
      this.$api
        .getPersonalCentre({
          userId: this.loginInfo.id,
          staffType: this.staffType,
          startTime: startTime || "",
          endTime: endTime || "",
          leaguerType: this.leaguerType,
          staffId: this.staffInfo.staffId
        })
        .then(res => {
          this.untreated = res.workList[0].untreated;
          this.dispatched = res.workList[0].dispatched;
          this.register = res.workList[0].register;
          this.completed = res.workList[0].completed;
        });
    },
    selectStartTime(val) {
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      if (month < 10) {
        month = "0" + month;
      }
      if (day < 10) {
        day = "0" + day;
      }
      this.startTimeText = year + "-" + month + "-" + day;
      this.timeShow = false;
    },
    selectEndTime(val) {
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      if (month < 10) {
        month = "0" + month;
      }
      if (day < 10) {
        day = "0" + day;
      }
      this.endTimeText = year + "-" + month + "-" + day;
      this.timeShow2 = false;
    },
    resetTime() {
      if (this.startTimeText == "选择初始时间" && this.endTimeText == "选择终止时间") return;
      this.startTimeText = "选择初始时间";
      this.endTimeText = "选择终止时间";
      this.startTime = "";
      this.endTime = "";
      this.curPage = 0;
      this.isDiff = true;
      this.getOrderList(this.curArea, this.workTypeCode);
    }
  },
  watch: {
    checkedValue(val) {
      if (val != "") {
        this.isDis = false;
        this.disabled = false;
      }
    },
    startTimeText(val) {
      if (this.startTimeText != "选择初始时间" && this.endTimeText != "选择终止时间") {
        this.startTime = this.startTimeText;
        this.endTime = this.endTimeText;
        this.curPage = 0;
        this.isDiff = true;
        this.getOrderList(this.curArea, this.workTypeCode);
      }
    },
    endTimeText(val) {
      if (this.startTimeText != "选择初始时间" && this.endTimeText != "选择终止时间") {
        this.startTime = this.startTimeText;
        this.endTime = this.endTimeText;
        this.curPage = 0;
        this.isDiff = true;
        this.getOrderList(this.curArea, this.workTypeCode);
      }
    },
    $route(to, from) {
      this.fromRoute = from;
    }
  },
  mounted() {
    if (systemType == "ios") {
      document.querySelector(".tab-content").style.top = "18vh";
      document.querySelector(".mixing").style.top = "27vh";
    }
    try {
      //判断是否从新建投诉工单关联工单号选择入口进入
      if (localStorage.prevLinkUrl == "/complain") {
        this.fromNewComplain = true;
      }
    } catch (error) { }
    //判断来源投诉申报工单
    if (this.$route.query.source == "newComplainOrder") {
      this.sourceFlag = this.$route.query.source;
    }
    // this.staffType = this.loginInfo.type
  },
  activated() {
    setTimeout(() => {
      this.$YBS.apiCloudEventKeyBack(this.goBack);
    }, 100);
    this.timer = setInterval(() => {
      if (this.staffInfo.staffId) {
        this.staffType = this.staffInfo.teamId ? 2 : 1;
        try {
          if (!sessionStorage.selectTime && this.$route.query.type) {
            this.startTimeText = "选择初始时间";
            this.endTimeText = "选择终止时间";
            this.startTime = "";
            this.endTime = "";
            this.curPage = 0;
            this.dataArr = [];
            console.log("fromRoute", this.fromRoute);
            if (this.fromRoute.path == "/details") {
              this.getOrderList(this.curArea);
            } else {
              this.curArea = this.$route.query.type;
              this.getOrderList(this.$route.query.type);
            }
          } else if (JSON.parse(sessionStorage.selectTime).count == 1) {
            let time = JSON.parse(sessionStorage.selectTime).time;
            let time1 = time.split("-")[0]; //2018/11/04
            let time2 = time.split("-")[1];

            this.startTimeText = this.formatTime(time1);
            this.endTimeText = this.formatTime(time2);
            this.startTime = time1.replace(/\//g, "-");
            this.endTime = time2.replace(/\//g, "-");
            this.curPage = 0;
            this.dataArr = [];
            this.getOrderList(this.curArea);
          }
        } catch (error) { }
        if ((this.$route.query.type && this.type != this.$route.query.type && this.curArea != this.$route.query.type) || this.$route.query.source == "newComplainOrder") {
          this.curPage = 0;
          this.dataArr = [];
          this.type = this.$route.query.type;
          this.curArea = this.$route.query.type;
          this.getOrderList(this.$route.query.type);
        }
        clearInterval(this.timer);
      }
    }, 50);
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl'
.time-reset
  color #666
  font-size 100%
>>> .van-overlay
  z-index 999 !important
.vue-pull-to-wrapper
  width 100%
.area-content >>> .action-block
  display none
.area-content
  display block
  position absolute
  background $bgColor
  width 100%
  height 100%
  overflow auto
  &:after
    display block
    content '.'
    height 0
    line-height 0
    clear both
    visibility hidden
  .time-content
    font-size 0.34rem
    height 0.98rem
    background-color #fff
    padding 0 0.32rem
    display flex
    align-items center
    position fixed
    width 100%
    box-sizing border-box
    z-index 2
    .time-text
      display inline-block
      width 2.5em
      border-right 1px solid #888
      margin-right 0.5em
    .time-wrapper
      flex 1
      display flex
      justify-content space-between
      white-space nowrap
  .tab-content
    background-color #fff
    height 10vh
    border-top 1px solid #e5e6eb
    .line
      height 0.2rem
      background-color $bgColor
    .tab-title
      height 95%
      display flex
      font-size 0.3rem
      background-color #fff
      color #999
      div
        flex 1
        display flex
        flex-direction column
        align-items center
        justify-content flex-end
        line-height 100%
        .title
          font-size calc(15px * var(--font-scale))!important
          padding 0.17rem 0 0.23rem 0
          color #86909C
          padding-top 0.3rem
        .num
          font-size calc(20px * var(--font-scale))!important
          color #1D2129
      .disClick
        pointer-events none
      .textStyle
        background linear-gradient(180deg, #FFFFFF 27%, #F0F5FF 100%)
      .tabItemActive
        position absolute
        width 0.7rem
        height 3px
        background $btnColor
  .select-area
    box-sizing border-box
    padding 0 16px
    width 100%
    background #F2F4F9
    display flex
    position absolute
  .mixing
    height 80vh
  .same
    height calc(100% - 1.08rem)
    top 1.08rem
  .img-content
    height 80vh
    display flex
    flex-direction column
    justify-content center
    align-items center
    background-color #fff
    img
      width 3rem
    p
      color #999
      font-size 0.3rem
      margin 10px
  .submit-btn
    padding 0.22rem 0.32rem
    background-color #fff
    width 100%
    box-sizing border-box
    height 66px
    position fixed
    bottom 0
.flowtype
  color $color
.region
  background-color #F2F4F9
  width 100%
  li
    background-color #fff
    min-height 16vh
    display flex
    align-items center
    font-size 0.3rem
    padding 0 0.4rem
    border-radius 8px
    overflow hidden
  label
    width 0.6rem
    padding-right 0
    padding-left 5px
  .content
    display flex
    align-items center
    font-size calc(15px * var(--font-scale))!important
    padding 18px 0
    background-color #fff
    flex 1
    .left
      .ic-style-img
        width 0.68rem
    .middle
      flex 1
      // padding-left: .38rem
      .middel-text
        // padding: .1rem 0
      .time-text-content
        color #c2c2c2
        padding 0.1rem 0
        .ic-time-img
          width 0.22rem
          vertical-align middle
        .time-text
          vertical-align middle
    .right
      .iconfont
        color #e5e5e5
      .purple
        color #978fff
      .pink
        color #FF7D9C
      .yellow
        color #FFB034
      .green
        color #87D0C0
      .blue
        color #77AFFD
      .gray
        color #C2C2C2
</style>
<style lang="scss" scoped>
.textStyle .num {
  color: #3562db !important;
}
.textStyle .title {
  color: #3562db !important;
}
.region {
  border-radius: 8px;
  padding-top: 12px;
}
.border-bottom {
  border-radius: 8px;
  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
}
.time-text {
  margin-bottom: 8px;
}
.department {
  span {
    display: inline-block;
    width: 100%;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    line-height: 1.2;
    
    /* 控制最大显示行数（例如最多显示2行） */
    display: -webkit-box;
    -webkit-line-clamp: 5; /* 显示的行数 */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
.modify_middle {
  display: flex;
  justify-content: space-between;
}
.tranform_style {
  transform: translateY(6px);
  word-wrap: break-word;
  word-break: break-all;
  line-height: 18px;
}
.choice {
  display: flex;
  justify-content: space-between;
  height: 50px;
  align-items: center;
}
.option-box {
  padding: 0 16px;
}
/deep/ .van-button__text {
  font-size: 14px;
}
.active-date {
  color: #3562db;
}
.date-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.date-box .date-text {
  background-color: #f2f3f5;
  padding: 8px 16px;
  border-radius: 3px;
  width: 30vw;
  height: 20px;
  text-align: center;
  line-height: 20px;
}
.date-line {
  width: 10px;
  height: 2px;
  background-color: #c9cdd4;
  margin: 0 6px;
}
.van-datetime-picker {
  margin-bottom: 12px;
}
.bg-green {
  background-color: #e8ffea !important;
  color: #00b42a !important;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: calc(12px * var(--font-scale));
}
.bg-red {
  background-color: #ffece8 !important;
  color: #f53f3f !important;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: calc(12px * var(--font-scale));
}
.time-text-title {
  white-space: nowrap;
}
</style>
