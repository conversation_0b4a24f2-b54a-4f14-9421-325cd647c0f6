<template>
  <!-- 未完工工单列表 -->
  <div class="wrapper">
    <Header title="工单列表" @backFun="goback"></Header>
    <div class="area-content">
      <div class="time-content border-bottom" @click="selectTime">
        <span class="time-text">筛选</span>
        <span class="time-wrapper">
          <span class="start">{{ startTimeText }}</span>
          -
          <span class="end">{{ endTimeText }}</span>
        </span>
      </div>
      <div class="tab-content" v-if="false">
        <!-- v-if="staffType == 2"-->
        <div class="tab-title">
          <div @click="judgeIsSame('2')" :class="{ textStyle: curArea == 2 }">
            <span class="num">{{ untreated > 99 ? "99+" : untreated }}</span>
            <span class="title">未派工</span>
            <span :class="{ tabItemActive: curArea == 2 }"></span>
          </div>
          <div @click="judgeIsSame('3')" :class="{ textStyle: curArea == 3 }">
            <span class="num">{{ dispatched > 99 ? "99+" : dispatched }}</span>
            <span class="title">已派工</span>
            <span :class="{ tabItemActive: curArea == 3 }"></span>
          </div>
          <div @click="judgeIsSame('4')" :class="{ textStyle: curArea == 4 }">
            <span class="num">{{ register > 99 ? "99+" : register }}</span>
            <span class="title">已挂单</span>
            <span :class="{ tabItemActive: curArea == 4 }"></span>
          </div>
          <!-- <div
              @click="judgeIsSame('5')"
              :class="{textStyle:curArea == 5}"
          >
            <span class="num">{{ completed > 99?"99+":completed }}</span>
            <span class="title">已结束</span>
            <span :class="{tabItemActive:curArea == 5}"></span>
          </div> -->
        </div>
        <div class="line"></div>
      </div>

      <div class="select-area mixing">
        <!-- :class="{same:staffType==1,mixing:staffType==2}"-->
        <pull-to :bottom-load-method="refresh" :is-top-bounce="false">
          <div class="weui-cells_checkbox">
            <ul class="region">
              <li
                class="border-bottom"
                v-for="(item, index) of dataArr"
                :key="item.taskId"
              >
                <!-- <label class="weui-cell weui-check__label" v-if="fromNewComplain">
                  <div class="weui-cell__hd">
                    <input type="radio" class="weui-check" name="checkbox1" checked="checked" :value="item" v-model="checkedValue">
                    <i class="weui-icon-checked"></i>
                  </div>
                </label> -->
                <div
                  class="content"
                  @click="enterDetailInfo(item.taskId, item.flowCode)"
                >
                  <div class="left">
                    <img
                      src="@/assets/images/ic-repare.png"
                      class="ic-style-img"
                      v-if="item.workTypeCode == 1"
                    />
                    <img
                      src="@/assets/images/ic-cleaning.png"
                      class="ic-style-img"
                      v-if="item.workTypeCode == 2"
                    />
                    <img
                      src="@/assets/images/ic-transport.png"
                      class="ic-style-img"
                      v-if="item.workTypeCode == 3"
                    />
                    <img
                      src="@/assets/images/ic-complain.png"
                      class="ic-style-img"
                      v-if="item.workTypeCode == 5"
                    />
                    <img
                      src="@/assets/images/ic-service.png"
                      class="ic-style-img"
                      v-if="item.workTypeCode == 6"
                    />
                    <img
                      src="@/assets/images/ic-car.png"
                      class="ic-style-img"
                      v-if="item.workTypeCode == 15"
                    />
                    <img
                      src="@/assets/images/ic-comton.png"
                      class="ic-style-img"
                      v-if="item.workTypeCode.length > 2"
                    />
                  </div>
                  <div class="middle">
                    <div class="middel-text">
                      <div style="font-size:0.24rem">
                        工单类型：{{ item.workTypeName }}
                      </div>
                    </div>
                    <div
                      class="time-text-content"
                      style="padding:0;font-size:0.24rem;"
                    >
                      <img
                        src="@/assets/images/ic-time.png"
                        class="ic-time-img"
                      />
                      <span class="time-text">{{ item.createDate }}</span>
                    </div>
                    <div
                      class="time-text"
                      style="margin-top:0.12rem;font-size:0.24rem;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width:4rem"
                    >
                      服务事项：{{ item.serviceItemShow }}
                    </div>
                    <div
                      class="time-text"
                      style="margin-top:0.12rem;font-size:0.24rem;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width:4rem"
                    >
                      服务地点：{{ item.locationName }}
                    </div>
                  </div>
                  <div class="right">
                    <span class="flowtype pink" v-if="item.flowcode == '1'">{{
                      item.flowtype
                    }}</span>
                    <span class="flowtype purple" v-if="item.flowcode == '2'">{{
                      item.flowtype
                    }}</span>
                    <span class="flowtype yellow" v-if="item.flowcode == '3'">{{
                      item.flowtype
                    }}</span>
                    <span class="flowtype green" v-if="item.flowcode == '4'">{{
                      item.flowtype
                    }}</span>
                    <span class="flowtype blue" v-if="item.flowcode == '5'">{{
                      item.flowtype
                    }}</span>
                    <span class="flowtype gray" v-if="item.flowcode == '6'">{{
                      item.flowtype
                    }}</span>
                    <span class="iconfont">&#xe646;</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </pull-to>
      </div>
      <!-- <div class="submit-btn" v-if="fromNewComplain">
        <button
            class="weui-btn weui-btn_primary"
            :class="{'weui-btn_disabled':isDis}"
            :disabled="disabled"
            @click="completeSelection"
        >确定
        </button>
      </div> -->
      <!-- <div v-if="!isHaveData" class="img-content">
        <img src="@/assets/images/noDataDefault/search-empty.png" alt="">
        <p>暂时没有工单信息~</p>
      </div> -->
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import PullTo from "vue-pull-to";
import global from "@/utils/Global";
export default {
  name: "notCompleteOrderList",
  components: {
    PullTo
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"])
  },
  data() {
    return {
      flag: false,
      staffType: "",
      type: "",
      isHaveData: true,
      curArea: "",
      // selectType: 2,
      curPage: 0,
      isDiff: false,
      dataArr: [],
      resNameData: [],
      resCodeData: [],
      startTime: "",
      startTimeText: "选择初始时间",
      endTime: "",
      endTimeText: "选择终止时间",
      untreated: 0,
      dispatched: 0,
      completed: 0,
      register: 0,
      // fromNewComplain: false,   //来源于新建投诉工单的关联工单号选择
      // checkedValue:'',
      isDis: true,
      disabled: true,
      sourceFlag: ""
    };
  },
  methods: {
    goback() {
      this.$router.go(-1);
    },
    /**
     * 判断是否连续请求同一类型的工单列表
     * @param type 工单类型
     */
    judgeIsSame(type) {
      if (this.curArea != type) {
        //进行切换
        this.curPage = 0;
        this.isDiff = true;
        this.dataArr = [];
        //sessionStorage.selectType = type
        this.curArea = type;
        this.getOrderList(type);
      } else {
        this.isDiff = false;
      }
    },
    /**
     * 发送工单列表请求
     */
    getOrderList(type) {
      $.showLoading();
      let lastPage = this.curPage;
      this.curPage = ++lastPage;
      this.$api
        .getUnfinishedMineTask({
          type: "1,2,3,4",
          curPage: this.curPage,
          pageSize: 15,
          startTime: this.startTime,
          endTime: this.endTime,
          userId: this.loginInfo.id,
          staffType: this.staffType,
          areaCode: this.$route.query.areaCode,
          staffId: this.staffInfo.staffId
        })
        .then(this.getOrderListSucc);

      if (this.staffType == 1) {
        this.getWorkList(this.startTime, this.endTime);
      }
    },
    /**
     * 工单列表请求成功函数
     */
    getOrderListSucc(data) {
      $.hideLoading();
      if (this.staffType == 2) {
        //院内和院外返回数据格式不同
        //所有状态都没有值的时候，返回来的数据data为[]
        if (data.length == 0) {
          this.isHaveData = false;
          this.untreated = 0;
          this.dispatched = 0;
          this.register = 0;
          this.completed = 0;
          return;
        } else {
          this.untreated = data.untreated;
          this.dispatched = data.dispatched;
          this.register = data.register;
          this.completed = data.completed;
        }

        if (data.details.length == 0) {
          //this.curArea = JSON.parse(this.selectType)
          //第一次请求或者由时间查询结构
          if (this.dataArr.length == 0 || this.curPage == 1) {
            this.dataArr = [];
            this.isHaveData = false;
          }
          $.toast("没有更多的数据啦", "text");
        } else {
          // this.curArea = JSON.parse(this.selectType)
          this.isHaveData = true;
          if (this.isDiff) {
            this.dataArr = data.details;
          } else {
            this.dataArr = this.dataArr.concat(data.details);
          }
        }
      } else if (this.staffType == 1) {
        if (data.length == 0) {
          //第一次请求或者由时间查询结构
          if (this.dataArr.length == 0 || this.curPage == 1) {
            this.dataArr = [];
            this.isHaveData = false;
          }
          $.toast("没有更多的数据啦", "text");
        } else {
          this.isHaveData = true;
          if (this.isDiff || this.curPage == 1) {
            this.dataArr = data;
          } else {
            this.dataArr = this.dataArr.concat(data);
          }
        }
      }
      this.flag = true;
    },
    /**
     * 进入工单详情
     */
    enterDetailInfo(id, flowCode) {
      this.$router.push({
        path: "/details",
        query: {
          id: id,
          source: this.sourceFlag,
          flowCode
        }
      });
    },
    /**
     * 上拉加载更多
     */
    refresh(loaded) {
      this.isDiff = false;
      this.getOrderList(this.curArea);
      if (this.flag) {
        loaded("done");
        this.flag = false;
      }
    },
    /**
     * 进入时间筛选
     */
    selectTime() {
      this.$router.push("/time");
    },
    /**
     * 将-的时间格式化成年、月、日形式
     * @param time
     */
    formatTime(time) {
      let timeStr = "";
      if (time.includes("/")) {
        timeStr = time.split("/");
      } else {
        timeStr = time.split("-");
      }
      return timeStr[0] + "年" + timeStr[1] + "月" + timeStr[2] + "日";
    },
    /**
     * 将选择的关联工单号以及指派班组携带到投诉任务发布页面
     */
    // completeSelection () {
    //   this.$router.push({
    //     name:'ComplainWorkOrder',
    //     params: {
    //       workNum: this.checkedValue.workNum,
    //       designateDeptCode:this.checkedValue.designateDeptCode,
    //       designateDeptName:this.checkedValue.designateDeptName
    //     }
    //   })
    // },
    getWorkList(startTime, endTime) {
      this.$api
        .getPersonalCentre({
          userId: this.staffInfo.id,
          staffType: this.staffType,
          startTime: startTime || "",
          endTime: endTime || "",
          leaguerType: this.leaguerType,
          staffId: this.staffInfo.staffId
        })
        .then(res => {
          this.untreated = res.workList[0].untreated;
          this.dispatched = res.workList[0].dispatched;
          this.register = res.workList[0].register;
          this.completed = res.workList[0].completed;
        });
    }
  },
  watch: {},
  mounted() {
    // this.staffType = this.loginInfo.type
    this.staffType = this.staffInfo.teamId ? 2 : 1;
  },
  activated() {
    try {
      if (!sessionStorage.selectTime) {
        this.startTimeText = "选择初始时间";
        this.endTimeText = "选择终止时间";
        this.startTime = "";
        this.endTime = "";
        this.curPage = 0;
        this.dataArr = [];
        this.curArea = this.$route.query.type;
        this.getOrderList(this.$route.query.type);
      } else if (JSON.parse(sessionStorage.selectTime).count == 1) {
        let time = JSON.parse(sessionStorage.selectTime).time;
        let time1 = time.split("-")[0]; //2018/11/04
        let time2 = time.split("-")[1];

        this.startTimeText = this.formatTime(time1);
        this.endTimeText = this.formatTime(time2);
        this.startTime = time1.replace(/\//g, "-");
        this.endTime = time2.replace(/\//g, "-");
        this.curPage = 0;
        this.dataArr = [];
        this.getOrderList(this.curArea);
      }
    } catch (error) {}
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/varibles.styl"
  .vue-pull-to-wrapper
    width: 100%
  .area-content >>>  .action-block
      display: none
  .area-content
    display: block
    position: absolute
    background: $bgColor
    width: 100%
    height: 100%
    overflow: auto
    &:after
      display: block
      content: "."
      height: 0
      line-height: 0
      clear: both
      visibility: hidden
    .time-content
      font-size: .34rem
      height: .98rem
      background-color: #fff
      padding: 0 .32rem
      display: flex
      align-items: center
      position: fixed
      width: 100%
      box-sizing: border-box
      z-index: 2
      .time-text
        display: inline-block
        width: 2.5em
        border-right: 1px solid #888
        margin-right: .5em
      .time-wrapper
        flex: 1
        display: flex
        justify-content: space-between
        white-space: nowrap
    .tab-content
      position: fixed
      top: .98rem
      z-index: 999
      width: 100%
      .line
        height: .2rem
        background-color: $bgColor
      .tab-title
        height: 1.25rem
        display: flex
        font-size: .3rem
        background-color: #fff
        color: #999
        div
          flex: 1
          display: flex
          flex-direction: column
          align-items: center
          justify-content: flex-end
          line-height: 100%
          .title
            font-size: .3rem
            padding: 0.17rem 0 .23rem 0
          .num
            font-size: .4rem
        .disClick
          pointer-events: none
        .textStyle
          color: $color
        .tabItemActive
          position: absolute
          width: .7rem
          height: 3px
          background: $btnColor
    .select-area
      width: 100%
      background: transparent
      display: flex
      position: absolute
    .mixing
      height: calc(100% - 1rem)
      top: 7vh
    .same
      height: calc(100% - 1.08rem)
      top: 1.08rem
    .img-content
      height:100%
      display: flex
      flex-direction: column
      justify-content: center
      align-items: center
      background-color: #fff
      img
        width: 3rem
      p
        color: #999
        font-size: .3rem
        margin: 10px
    .submit-btn
      padding: 0.22rem 0.32rem
      background-color: #fff
      width: 100%
      box-sizing: border-box
      height: 66px
      position: fixed
      bottom: 0
  .flowtype
    color: $color

  .region
    background-color: #fff
    width: 100%
    li
      display: flex
      align-items: center
      label
        width: 0.6rem
        padding-right: 0
        padding-left: 5px
      .content
        height: 1.6rem
        display: flex
        align-items: center
        font-size: .3rem
        padding: 0 .32rem
        background-color: #fff
        flex:1
        .left
          .ic-style-img
            width: .68rem
        .middle
          flex: 1
          padding-left: .38rem
          .middel-text
            padding: .1rem 0
          .time-text-content
            color: #c2c2c2
            padding: .1rem 0
            .ic-time-img
              width: .22rem
              vertical-align: middle
            .time-text
              vertical-align: middle
        .right
          .iconfont
            color: #e5e5e5
          .purple
            color:#978fff
          .pink
            color: #FF7D9C
          .yellow
            color: #FFB034
          .green
            color: #87D0C0
          .blue
            color: #77AFFD
          .gray
            color: #C2C2C2
</style>
