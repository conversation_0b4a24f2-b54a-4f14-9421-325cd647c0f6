<template>
  <!-- 我的申报 -->
  <div class="wrapper" :class="{ 'large-font': isLargeFont }">
    <Header title="工单详情" @backFun="goBack"></Header>
    <div class="simplification" v-if="!isShowSign">
      <div class="item" v-for="(item, idx) of workOrdeInfoDate" :key="idx" @click.stop="handleFold($event, idx)">
        <!--基本详情-->
        <div v-if="item.operationCode == 1">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <base-info :baseData="item"></base-info>
        </div>
        <!--已受理-->
        <div v-if="item.operationCode == 2 && item.type != 2">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <service-site :serviceData="item" :workTypeCode="workTypeCode" :template="template"></service-site>
        </div>
        <!--已派工-->
        <div v-if="item.operationCode == 3">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <person :persData="item" :workTypeCode="workTypeCode"></person>
        </div>
        <!--已挂单-->
        <div v-if="item.operationCode == 4">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <pending-details :pendingData="item"></pending-details>
        </div>
        <!--已完工-->
        <div v-if="item.operationCode == 6">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <evaluation-info ref="evaluation" :finishedData="item" :workTypeCode="workTypeCode" @setDisDegreeToEmpty="setDisDegreeToEmpty" :template="template"></evaluation-info>
        </div>
          <!--回退-->
        <div v-if="item.operationCode == 30">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <retroversion :cancelData="item"></retroversion>
        </div>
        <!--回访-->
        <div v-if="item.operationCode == 5">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <return-visit :returnVisitdData="item" :name="item.operationType"></return-visit>
        </div>
        <!--督促-->
        <div v-if="item.operationCode == 8">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <return-visit :returnVisitdData="item" :name="item.operationType"></return-visit>
        </div>
        <!--取消-->
        <div v-if="item.operationCode == 7">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <cancle-page :cancleData="item"></cancle-page>
        </div>
        <!--已转单-->
        <div v-if="item.operationCode == 9">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <change-order :changeOrderData="item"></change-order>
        </div>
        <!-- 转派 -->
        <div v-if="item.operationCode == 11">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <change-order :changeOrderData="item"></change-order>
        </div>
        <!-- 已变更 -->
        <div v-if="item.operationCode == 10">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <changed :changedData="item" :workTypeCode="workTypeCode" :template="template"></changed>
        </div>
        <!-- 回复 -->
        <div v-if="item.operationCode == 13">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <Reply :replyData="item" :workTypeCode="workTypeCode"></Reply>
        </div>
        <!-- 到达 -->
        <div v-if="item.operationCode == 31">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <arrival :persData="item"></arrival>
        </div>
      </div>
    </div>
    <!-- 精简工单 -->
    <!-- <simplify-work v-else :workOrdeInfoDate="workOrdeInfoDate"></simplify-work> -->
    <!--评价-->
    <div class="height-placeholder" style="height: 38px"></div>
    <div v-if="isShowSign" class="item appraise">
      <star-page ref="stars"></star-page>
    </div>
    <!--底部按钮-->
    <div class="btns" v-if="flowCode != 5 && currentPage">
      <span class="btns" v-if="workType != '2'">
        <!-- <button v-if="flowCode != 4 && flowCode != 15" class="weui-btn weui-btn_mini weui-btn_primary btn" @click="urgeWorkOrder">催单</button> -->
        <!-- v-if="flowCode == '4' && disDegreed == ''" -->
        <button v-if="(flowCode == '4' || flowCode == '5') && isShowEvaluate" class="weui-btn weui-btn_mini weui-btn_primary btn" @click="goEvaluate">评价</button>
        <!-- <button class="weui-btn weui-btn_mini weui-btn_primary btn" v-if="flowCode == 1 || flowCode == ''" @click="goToCancelPage">取消</button> -->
        <button class="weui-btn weui-btn_mini weui-btn_primary btn" v-if="flowCode != 1" @click="goToCleaningPage">投诉</button>
      </span>
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" v-else @click="goToReply">回复</button>
    </div>
    <div class="btns" v-if="sure">
      <!--确定评价-->
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="submitEval">确定</button>
    </div>
  </div>
</template>

<script>
import global from "@/utils/Global.js";
import { mapState } from "vuex";
import BaseInfo from "../../components/BaseInfo";
import ServiceSite from "../../components/Accepted";
import Annex from "../../components/Annex";
import Person from "../../components/Dispatched";
import PendingDetails from "../../components/PendingDetails";
import EvaluationInfo from "../../components/EvaluationInfo";
import CancelInfo from "../../components/CancelInfo";
import ReturnVisit from "../../components/ReturnVisit";
import CanclePage from "../../components/CanclePage";
import retroversion from "../../components/retroversion";
import StarPage from "@/pages/form/formDetails/components/Star";
import ChangeOrder from "@/pages/form/formDetails/components/ChangeOrderInfo";
import SimplifyWork from "@/pages/form/formDetails/components/SimplifyWork";
import Changed from "@/pages/mine/components/Changed";
import Reply from "@/pages/mine/components/Reply";
import Arrival from "@/pages/mine/components/arrival";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "AcceptedDetails",
  mixins: [fontSizeMixin],
  components: {
    BaseInfo,
    ServiceSite,
    Annex,
    Person,
    PendingDetails,
    EvaluationInfo,
    CancelInfo,
    ReturnVisit,
    CanclePage,
    retroversion,
    StarPage,
    ChangeOrder,
    SimplifyWork,
    Changed,
    Reply,
    Arrival,
  },
  computed: {
    ...mapState(["loginInfo", "isLogin", "staffInfo"]),
    isShowEvaluate() {
      let flag = false;
      if (this.workOrdeInfoDate[0].sourcesPhone) {
        if (this.workOrdeInfoDate[0].sourcesPhone == this.staffInfo.mobile && this.workOrdeInfoDate[0].isAcceptanceCheck != "1") {
          flag = true;
        }
      } else if (this.workOrdeInfoDate[0].createBy == this.staffInfo.staffId && this.workOrdeInfoDate[0].isAcceptanceCheck != "1") {
        flag = true;
      }
      return flag;
    },
    isLargeFont() {
      return localStorage.getItem("fontSizePreference") === "x-large" || localStorage.getItem("fontSizePreference") === "large";
    },
  },
  data() {
    return {
      workOrdeInfoDate: [],
      workTypeCode: "",
      kindTypeArr: ["1", "2", "3", "4", "5", "6"], //一般工作类型
      workType: "",
      flowCode: "",
      workNum: "",
      taskNum: "",
      disDegreed: "1",
      isShowSign: false,
      taskId: "",
      currentPage: true,
      sure: false,
      starCount: "",
      simplification: false,
      template: "",
    };
  },
  methods: {
    goBack() {
      if (this.$route.query.isRedirect) {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    goEvaluate() {
      this.$router.push({
        path: "/evaluation",
        query: {
          taskId: this.taskId,
        },
      });
    },
    /**
     *获取工单任务详情信息
     */
    getOderDetailsInfo() {
      this.$api
        .getTaskDetail({
          taskId: this.taskId,
          workNum: this.taskNum,
        })
        .then(this.getOderDetailsInfoSucc);
    },
    /**
     * 接收时间格式化函数
     */
    timeFunc() {},
    /**
     * 获取工单任务详情信息成功函数
     * @param res
     */
    getOderDetailsInfoSucc(res) {
      let newRes = res.map((item) => {
        item.fold = false;
        return item;
      }); // 时间轴内容折叠/展开
      let baseInfo = newRes[0];
      newRes.forEach((item) => {
        if (item.operationCode == 10) {
          item.itemType = baseInfo.itemType;
          item.transportName = baseInfo.transportName;
          item.sourcesDeptName = baseInfo.sourcesDeptName;
          item.transportEndLocalOfficeName = baseInfo.transportEndLocalOfficeCode;
          item.transportEndLocalOffice = baseInfo.transportEndLocalOffice;
          item.transportEndLocal = baseInfo.transportEndLocal;
          item.transportStartLocal = baseInfo.transportStartLocal;
          item.transportStartLocalOffice = baseInfo.transportStartLocalOffice;
          item.transportName = baseInfo.transportName;
          item.customtransportNum = baseInfo.customtransportNum;
          item.questionDescription = baseInfo.questionDescription;
        }
      });

      this.workOrdeInfoDate = newRes;
      // 工单模版类型（3运送类；6综合类）
      this.template = this.workOrdeInfoDate[0].template;
      console.log(this.template);
      console.log(this.workOrdeInfoDate[0]);

      this.timeFunc = global.timestampToTime;
      this.workTypeCode = this.workOrdeInfoDate[0].workTypeCode;
      this.workType = this.workOrdeInfoDate[0].type;
      this.flowCode = this.workOrdeInfoDate[0].flowCode;
      if (this.flowCode == 1 || this.flowCode == 2) {
        this.simplification = true;
      }
      this.workNum = this.workOrdeInfoDate[0].workNum;
      this.taskId = this.workOrdeInfoDate[0].taskId;
      this.designateDeptCode = this.workOrdeInfoDate[0].designateDeptCode;
      this.designateDeptName = this.workOrdeInfoDate[0].designateDeptName;
    },
    /**
     * 跳转取消工单页面，进行工单取消操作
     */
    goToCancelPage() {
      this.$router.push({
        name: "CancelPage",
        params: {
          taskId: this.taskId,
        },
      });
    },
    /**
     * 回复
     */
    goToReply() {
      this.$router.push({
        path: "Reply",
        query: {
          source: "custom",
          id: this.$route.query.id,
        },
      });
    },
    /**
     * 跳转投诉工单，并携带工单号，指派班组信息
     */
    goToCleaningPage() {
      let url =
        "/complain?source=workOrderDetails&workNum=" + this.workNum + "&designateDeptCode=" + this.designateDeptCode + "&designateDeptName=" + encodeURI(this.designateDeptName);
      this.$router.push({
        path: url,
      });
    },
    //催单
    urgeWorkOrder() {
      this.$api
        .urgeOderByWeChat({
          id: this.workOrdeInfoDate[0].id,
        })
        .then((res) => {
          // console.log(res);
          $.toast("催单成功！", "text");
        });
    },
    /**
     * 展示满意度评价
     */
    showCommented() {
      this.isShowSign = true;
      this.currentPage = false;
      this.sure = true;
    },
    /**
     * 接收子组件的评价结果，并更改当页面的disDegreed值
     */
    setDisDegreeToEmpty() {
      this.disDegreed = "";
    },
    /**
     * 提交评价
     */
    submitEval() {
      this.starCount = this.$refs.stars.starIdx; //满意度
      // let evaluate = this.$refs.stars.evaluate //评价内容
      if (this.starCount == "") {
        this.disDegreed = "5";
      } else {
        this.disDegreed = this.starCount;
      }
      // console.log(evaluate, this.starCount);
      this.$api
        .toSaveEvaluate({
          taskId: this.taskId,
          userId: this.loginInfo.id,
          disDegree: this.disDegreed,
        })
        .then(this.handleRequestSucc);
    },
    /**
     * 评价提交成功函数
     */
    handleRequestSucc() {
      if (this.$refs.evaluation[0]) {
        this.$refs.evaluation[0].isHave = true;
        this.$refs.evaluation[0].onStarNum = this.starCount;
        this.$refs.evaluation[0].offStarNum = 5 - this.starCount;
      }
      this.currentPage = true;
      this.sure = false;
      this.isShowSign = false;
      $.toast("评价成功", "text");
      setTimeout(() => {
        location.reload();
        //兼容微信浏览器不支持
        // var {search,href} = window.location;
        // href = href.replace(/&?t_reload=(\d+)/g,'')
        // window.location.href = href+(search?'&':'?')+"t_reload="+new Date().getTime()
      }, 1000);
    },
    /**
     * 折叠/展开时间轴
     */
    handleFold(e, idx) {
      console.log(e);
      if (e.target.className == "header" || e.target.tagName == "I") {
        this.workOrdeInfoDate[idx].fold = !this.workOrdeInfoDate[idx].fold;
      }
    },
  },
  activated() {
    //来源于我的后勤投诉点击关联工单号通过工单号查看
    if (this.$route.params.taskNum) {
      this.taskNum = this.$route.params.taskNum;
      this.taskId = "";
      this.currentPage = false;
      this.getOderDetailsInfo();
    }
    //通过工单id查看
    if (this.$route.query.id) {
      this.taskId = this.$route.query.id;
      this.taskNum = "";
      //来源于后勤投诉新建工单的选择关联工单号
      if (this.$route.query.source == "newComplainOrder") {
        this.currentPage = false;
      }
      this.getOderDetailsInfo();
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    //来源于我的后勤投诉点击关联工单号通过工单号查看
    if (this.$route.params.taskNum) {
      this.taskNum = this.$route.params.taskNum;
      this.taskId = "";
      this.currentPage = false;
      this.getOderDetailsInfo();
    }
    //通过工单id查看
    if (this.$route.query.id) {
      this.taskId = this.$route.query.id;
      this.taskNum = "";
      //来源于后勤投诉新建工单的选择关联工单号
      if (this.$route.query.source == "newComplainOrder") {
        this.currentPage = false;
      }
      this.getOderDetailsInfo();
    }
  },
  beforeRouteEnter(to, from, next) {
    if (to.matched.some((m) => m.meta.auth)) {
      next((vm) => {
        // 对路由进行验证
        if (!vm.loginInfo) {
          // 未登录则跳转到登陆界面，query:{ Rurl: to.fullPath}表示把当前路由信息传递过去方便登录后跳转回来；
          next({ path: "/login", query: { Rurl: to.fullPath } });
        } else {
          //进入后先判断是否来自预约提醒工单推送
          let url = location.search; //?unitCode=1212&hospitalCode=12&interfaceNum=0&id=1221&project=ioms
          let str = url.substr(1); //unitCode=1212&hospitalCode=12&interfaceNum=0&id=1221&project=ioms
          let strs = str.split("&"); //[]
          let theRequest = {};
          if (str.indexOf("project") > -1) {
            //从推送消息链接进入
            for (let i = 0; i < strs.length; i++) {
              theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
            }
            vm.taskId = theRequest.id;
            vm.getOderDetailsInfo();
          } else {
            next();
          }
        }
      });
    }
  },
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.wrapper {
  .wrapper {
    &:nth-child {
      color: red;
    }
  }
}

.wrapper {
  padding-bottom: 80px;

  .item {
    .header {
      // height: 0.57rem;
      display: flex;
      align-items: center;
      margin: 0.15rem 0 0.15rem 0.3rem;
      background: #fff;
      position: relative;

      .iconfont {
        font-size: 0.4rem;
        color: $btnColor;
      }

      .title-body {
        max-width: 73%;
        height: calc(35px * var(--font-scale));
        display: flex;
        align-items: center;
        margin-left: 10px;
        background: #eceef8;
        padding: 0 0.24rem;
        border-radius: 0.3rem;
        // white-space: nowrap;

        .dot {
          display: inline-block;
          width: 0.09rem;
          height: 0.09rem;
          background: $btnColor;
        }

        .title {
          font-size: calc(16px * var(--font-scale));
          font-weight: 700;
          margin: 0 0.45rem 0 0.08rem;
        }

        .time {
          font-size: calc(16px * var(--font-scale));
          color: #4F87FB;
          max-width: 65%;
        }
      }

      .arrows-down {
        display: inline-block;
        width: 13px;
        height: 7px;
        background: url('~images/<EMAIL>');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100%;
        position: absolute;
        top: 50%;
        right: 20px;
        transform: translateY(-50%);
      }

      .arrows-up {
        background-image: url('~images/<EMAIL>');
      }
    }
  }

  .btns {
    height: 1.32rem;
    padding: 0.16rem;
    margin-top: 0.2rem;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #eceef8;
    z-index: 2;

    .btn {
      width: 2.08rem;
      font-size: calc(16px * var(--font-scale));
      margin: 0.1rem 0.16rem;
      line-height: inherit;
      white-space: nowrap;
    }
  }
}

.pers-sign {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 2;
}
.large-font .time {
  max-width: 50%!important;
}
</style>
