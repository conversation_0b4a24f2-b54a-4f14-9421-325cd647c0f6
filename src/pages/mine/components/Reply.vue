<template>
  <div class="dispatched-wrapper" :class="{'fold': fold}">
    <div class="write-content">
      <div class="content-info" >
        <!-- <div class="info-title">
          <div>{{replyData.createByName}}</div>
          <span class="date">{{replyData.createDate}}</span>
        </div> -->
        <div class="info-span subtitle">{{replyData.feedbackExplain}}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Reply",
  props: ["replyData", "workTypeCode"],
  computed: {
    fold() {
      return this.replyData.fold;
    }
  },
  mounted(){
  }
};
</script>


<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.dispatched-wrapper {
  timelineContent();
  margin: -6px 0 -6px 0.45rem;

  .item {
    itemBaseStyle();
    display: flex;
    height: 45px;
    align-items: center;

    .content-css {
      color: $contentColor;
      display: flex;
      align-items: center;
    }
  }

  .consumables-content {
    box-sizing: border-box;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;

    .list {
      display: flex;
      justify-content: space-between;
      font-size: 0.3rem;
      itemBaseStyle();
      padding: 0 1.2rem;

      .count {
        .num {
          display: inline-block;
          text-align: center;
          width: 50px;
        }
      }
    }
  }
}

.row-css {
  min-height: 0.98rem;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.5em;
}

.fold {
  display: none;
}

.write-content {
  padding-top: 0.35rem;
  padding-left: 0.32rem;

  .content-tit {
    padding-bottom: 0.35rem;
  }
}
</style>