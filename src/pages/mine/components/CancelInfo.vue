<template>
  <div class="cancle-wrapper">
    <div class="">
      <div class="weui-cell__hd item border-bottom">
        <label class="weui-label title">取消理由</label>
      </div>
      <div class="weui-cell__bd border-bottom">
          <textarea
              class="weui-textarea textarea cancel-reason content-css"
              rows="4"
              v-model="cancelData.cancelReasonId"
              readonly
          ></textarea>
      </div>
    </div>
    <div>
      <div class="er-level border-top item border-rightbottom">
        <div class="er-text title">取消说明</div>
      </div>
      <div class="weui-cell desc border-rightbottom">
        <div class="weui-cell__bd">
          <textarea
              class="weui-textarea textarea content-css"
              rows="4"
              v-model="cancelData.cancelExplain"
              readonly
          ></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "CancelInfo",
    props: ['cancelData']
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .cancle-wrapper
    background-color: $bgColor
    .item
      itemBaseStyle()
      align-items: flex-start
      .title
        font-size: .32rem
      .cancel
        background-color: #fff
    .desc
      background-color: #fff
      textarea
        text-indent: 2em
  .content-css
    text-indent: 2em
    padding-top: 5px
    color: $contentColor
    line-height: 1.5em
</style>