<template>
  <div class="cancle-wrapper"
   :class="{'fold': fold}">
    <div class="">
      <div class="weui-cell__hd item border-bottom">
        <label class="weui-label title">原因说明</label>
      </div>
      <div class="weui-cell__bd border-bottom">
          <textarea
              class="weui-textarea textarea cancel-reason content-css"
              rows="4"
              v-model="cancelData.rollbackExplain"
              readonly
          ></textarea>
      </div>
    </div>
  <div class="img-desc border-rightbottom">
     <div class="img-desc-content">
        <span class="text-title title">图片</span>
        <span
          v-if="cancelData.rollbackImage&&cancelData.rollbackImage!= ''"
          class="content-css"
        ></span>
      </div>
      <div class="img-content">
        <div
          class="img-wrapper"
          v-for="ele of cancelData.rollbackImage.split(',')"
          :key="ele"
        >
          <div class="img-box">
            <img
              v-preview="$YBS.imgUrlTranslation(ele)"
              :src="$YBS.imgUrlTranslation(ele)"
              class="img"
              preview-title-enable="true"
              preview-nav-enable="true"
              preview-top-title-tnable="true"
              preview-title-extend="false"
            />
          </div>
        </div>
      </div>
  </div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "retroversion",
    props: ['cancelData'],
    computed: {
    fold() {
      return this.cancelData.fold;
    }
  }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
 .img-desc {
    background-color: #fff;
    font-size: 0.32rem;

    .img-desc-content {
      display: flex;
      justify-content: start;
      align-items: center;

      .text-title {
        display: inline-block;
        line-height: 1rem;
        padding-left: 0.32rem;
        width: 105px;
        font-size: calc(16px * var(--font-scale))!important
      }

      .content-css {
        display: inline;
      }
    }

    .img-content {
      background: #fff;
      padding: 0 0.3rem;
      display: flex;

      .img-wrapper {
        width: 30%;
        height: 1.4rem;
        margin: 0.1rem;
        position: relative;

        .img-box {
          height: 100%;
          overflow: hidden;

          .img {
            width: 100%;
          }
        }
      }
    }
  }
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .cancle-wrapper
    timelineContent();
    // background-color: $bgColor
    .item
      itemBaseStyle()
      align-items: flex-start
      .title
        font-size: calc(16px * var(--font-scale))!important
      .cancel
        background-color: #fff
    .desc
      background-color: #fff
      textarea
        text-indent: 2em
  .content-css
    text-indent: 2em
    padding-top: 5px
    color: $contentColor
    line-height: 1.5em
    font-size: calc(16px * var(--font-scale))!important
  .fold
    display none
</style>
