<template>
<!-- 已变更 -->
  <div
    class="dispatched-wrapper"
    :class="{'fold': fold}"
  >
  <!-- 运送 -->
    <div v-if="workTypeCode == 3 || template == 3">
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务起点</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.transportStartLocal}}</div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label title">起点科室</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.transportStartLocalOffice}}</div>
      </div>

      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务终点</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.transportEndLocal}}</div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label title">终点科室</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.transportEndLocalOffice}}</div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务部门</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.designateDeptName}}</div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.transportName}}</div>
      </div>
      <!-- 申报描述 -->
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">申报描述</label>
        </div>
      </div>
      <div class="weui-cell__bd">
        <div class="desc content-css">{{ changedData.questionDescription }}</div>
      </div>

    </div>
    <!-- 其他 -->
    <div v-else>
      <div class="weui-cell border-rightbottom item" style="height: auto;">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务地点</label>
        </div>
        <div class="weui-cell__bd content-css row-css" style="line-height: 1.2;">{{changedData.itemType[0].localtion}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务科室</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.sourcesDeptName}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务部门</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.designateDeptName}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div
          class="weui-cell__bd questionDetail content-css row-css"
          v-if="workTypeCode == 1 || workTypeCode == 2 || workTypeCode.length>2"
        >{{serviceMatters}}</div>
        <div
          class="weui-cell__bd questionDetail content-css row-css"
          v-else
        >{{changedData.transportName}}</div>
      </div>
      <div
        class="weui-cell item"
        v-if="workTypeCode.length>2"
      >
        <div class="weui-cell__hd">
          <label class="weui-label title">数量</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.customtransportNum}}</div>
      </div>

      <!-- <div class="weui-cell item">
        <div class="weui-cell__hd">
          <label class="weui-label title">申报描述</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.questionDescription}}</div>
      </div> -->
      <div v-if="changedData.designatePersonName" class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务人员</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.designatePersonName}}</div>
      </div>

      <div v-if="changedData.typeSources" class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">申报属性</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.typeSources}}</div>
      </div>

      <div v-if="changedData.callerJobNum" class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">工号</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.callerJobNum}}</div>
      </div>

      <div v-if="changedData.callerName" class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">联系人</label>
        </div>
        <div class="weui-cell__bd content-css row-css">{{changedData.callerName}}</div>
      </div>

      <div v-if="changedData.sourcesPhone" class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">电话</label>
        </div>
        <div class="weui-cell__bd content-css row-css" style="color:#1aaff1" @click="makePhoneCall(changedData.sourcesPhone)">{{changedData.sourcesPhone}}</div>
      </div>

      <div v-if="changedData.urgencyDegree" class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">紧急程度</label>
        </div>
        <div class="weui-cell__bd content-css row-css" :style="urgencyDegreeColor(changedData.urgencyDegreeCode)">{{changedData.urgencyDegree}}</div>
      </div>

      <div v-if="changedData.appointmentType !== undefined" class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务时间</label>
        </div>
        <div class="weui-cell__bd content-css row-css">
          {{changedData.appointmentType == 0 ? '立刻' : '预约' + changedData.appointmentDate}}
        </div>
      </div>

      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">申报描述</label>
        </div>
      </div>
      <div class="weui-cell__bd">
        <div class="desc content-css">{{ changedData.questionDescription }}</div>
      </div>

    </div>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  name: "Changed",
  props: ["changedData", "workTypeCode","template"],
  computed: {
    fold() {
      return this.changedData.fold;
    },
    serviceMatters() {
      let matter = "";
      let itemType = this.changedData.itemType[0];
      if (itemType.itemServiceCode) {
        matter =
          itemType.itemTypeName +
          "-" +
          itemType.itemDetailName +
          "-" +
          itemType.itemServiceName;
      } else if (itemType.itemDetailCode) {
        matter = itemType.itemTypeName + "-" + itemType.itemDetailName;
      } else {
        matter = itemType.itemTypeName;
      }
      return matter;
    }
  },
  mounted() {
    // console.log(this.changedData.designateDeptName)
    // console.log(this.changedData.customtransportNum )
    // console.log(this.changedData.transportNum)
  },
  methods: {
     //点击电话号码拨打电话
     makePhoneCall(phoneNumber) {
      // 创建一个 a 元素
      const link = document.createElement('a');
      // 设置 href 属性为 tel 协议链接
      link.href = `tel:${phoneNumber}`;
      // 触发点击事件
      link.click();
    },
    urgencyDegreeColor(val) {
      return {
        color: ["1", "0"].includes(val) ? "#FF0000" : ""
      };
    },
  }
};
</script>
<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.dispatched-wrapper {
  timelineContent();
  margin: -6px 0 -6px 0.45rem;

  .item {
    itemBaseStyle();
    display: flex;
    height: 45px;
    align-items: center;

    .content-css {
      color: $contentColor;
      display: flex;
      align-items: center;
      font-size: calc(16px * var(--font-scale))!important
    }
    .title {
      font-size: calc(16px * var(--font-scale))!important
    }
  }

  .consumables-content {
    box-sizing: border-box;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;

    .list {
      display: flex;
      justify-content: space-between;
      font-size: 0.3rem;
      itemBaseStyle();
      padding: 0 1.2rem;

      .count {
        .num {
          display: inline-block;
          text-align: center;
          width: 50px;
        }
      }
    }
  }
}

.row-css {
  min-height: 0.98rem;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.5em;
}

.fold {
  display: none;
}

.desc {
  line-height: 28px;
  padding: 0 10px;
  box-sizing: border-box;
  background-color: #fff;
  text-indent: 2em;
  word-wrap: break-word;
  font-size: calc(14px * var(--font-scale))!important
}
</style>
