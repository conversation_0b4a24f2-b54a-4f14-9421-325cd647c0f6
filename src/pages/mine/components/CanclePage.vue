<template>
  <div class="cancle-wrapper" :class="{'fold': fold}">
    <div class="weui-cell border-bottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">取消理由</label>
      </div>
      <div class="weui-cell__bd will-solve content-css">{{cancleData.cancelReasonId}}</div>
    </div>
    <div class="weui-cell border-bottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">取消说明</label>
      </div>
      <div class="weui-cell__bd will-solve content-css row-css">{{cancleData.cancelExplain}}</div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
export default {
  name: "CanclePage",
  props: ["cancleData"],
  computed: {
    fold() {
      return this.cancleData.fold;
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.cancle-wrapper {
  timelineContent();

  .item {
    display: flex;
    align-items: baseline;

    .title {
      font-size: calc(16px * var(--font-scale))!important
    }

    .row-css {
      word-wrap: break-word;
      word-break: break-word;
    }

    .content-css {
      color: $contentColor;
      line-height: 1.5em;
      font-size: calc(16px * var(--font-scale))!important
    }
  }
}
  .fold 
    display none
</style>
