<template>
  <div class="dispatched-wrapper" :class="{ fold: fold }">
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">到达时间</label>
      </div>
      <div class="weui-cell__bd content-css row-css">
        {{ formattedCreateDate }}
      </div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">现场图片</label>
      </div>
      <div class="weui-cell__bd content-css row-css">
        <div v-if="imageList.length > 0" class="image-container">
          <img
            v-for="(ele, index) in imageList"
            :key="index"
            v-preview="$YBS.imgUrlTranslation(ele)"
            :src="$YBS.imgUrlTranslation(ele)"
            class="img"
            preview-title-enable="true"
            preview-nav-enable="true"
            preview-top-title-tnable="true"
            preview-title-extend="false"
          />
        </div>
      </div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">拍照时间</label>
      </div>
      <div class="weui-cell__bd content-css row-css">
        {{ persData.photoDate }}
      </div>
    </div>
  </div>
</template>
  
  <script>
export default {
  name: "Arrival",
  props: {
    persData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    fold() {
      return this.persData.fold;
    },
    formattedCreateDate() {
      if (!this.persData.createDate) return "";
      const date = new Date(Number(this.persData.createDate));
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(
        date.getMinutes()
      ).padStart(2, "0")}:${String(date.getSeconds()).padStart(2, "0")}`;
    }
  },
  data() {
    return {
      imageList: []
    };
  },
  watch: {
    persData: {
      handler(val) {
        if (val.arrivalPicPath) {
          // 将字符串转换为数组
          this.imageList = val.arrivalPicPath.split(",");
        } else {
          this.imageList = [];
        }
      },
      immediate: true
    }
  }
};
</script>
  
  <style rel="stylesheet/stylus" lang="stylus" scoped>
  @import '~styles/mixins.styl';
  @import '~styles/varibles.styl';

  .dispatched-wrapper {
    timelineContent();
    margin: -6px 0 -6px 0.45rem;

    .item {
      itemBaseStyle();
      min-height: 0.98rem;
      height: auto;
      display: flex;
      align-items: baseline;

      .content-css {
        color: $contentColor;
      }
    }

    .consumables-content {
      box-sizing: border-box;
      border-radius: 5px;
      overflow: hidden;
      background-color: #fff;

      .list {
        display: flex;
        justify-content: space-between;
        font-size: 0.3rem;
        itemBaseStyle();
        padding: 0 1.2rem;

        .count {
          .num {
            display: inline-block;
            text-align: center;
            width: 50px;
          }
        }
      }
    }
  }

  .row-css {
    min-height: 0.68rem;
    word-wrap: break-word;
    word-break: break-all;
    line-height: 1.5em;
  }

  .image-container {
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .img {
    width: 100px; /* 设置固定宽度 */
    height: 100px; /* 设置固定高度 */
    object-fit: cover; /* 保持图片比��并填充容器 */
    border-radius: 4px; /* 添加圆角 */
  }

  .fold {
    display: none;
  }
  .title {
    font-size: calc(16px * var(--font-scale))!important
  }
  .content-css {
    font-size: calc(14px * var(--font-scale))!important
  }
</style>
  