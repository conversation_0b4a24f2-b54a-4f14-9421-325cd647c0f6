<template>
  <div class="dispatched-wrapper" :class="{ fold: fold }">
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">服务人员</label>
      </div>
      <div class="weui-cell__bd content-css row-css">
        {{ persData.designatePersonName }}
      </div>
    </div>
    <div class="weui-cell border-bottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">人员电话</label>
      </div>
      <div class="weui-cell__bd content-css row-css">
        {{ persData.designatePersonPhone }}
      </div>
    </div>
    <div class="weui-cell border-bottom item" v-if="workTypeCode == '16'">
      <div class="weui-cell__hd">
        <label class="weui-label title">到达状态</label>
      </div>
      <div class="weui-cell__bd content-css row-css">
        {{ persData.arrivalStatus | arrivalStatus }}
      </div>
    </div>
    <div class="weui-cell border-bottom item" v-if="workTypeCode == '16'">
      <div class="weui-cell__hd">
        <label class="weui-label title">到达人员</label>
      </div>
      <div class="weui-cell__bd content-css row-css">
        {{ persData.arrivalPersonName }}
      </div>
    </div>
    <div class="weui-cell border-bottom item" v-if="workTypeCode == '16'">
      <div class="weui-cell__hd">
        <label class="weui-label title">到达时间</label>
      </div>
      <div class="weui-cell__bd content-css row-css">
        {{ persData.arrivalDate | arrivalDate }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "PersCom",
  props: ["persData", "workTypeCode"],
  computed: {
    fold() {
      return this.persData.fold;
    },
    // workTypeCode() {
    //   return this.workDetails[0].workTypeCode;
    // }
  },
  filters: {
    arrivalStatus(val) {
      switch (val) {
        case "0":
          return "未到达";
          break;
        case "1":
          return "已到达";
          break;
        default:
          return "";
      }
    },
    arrivalDate(val) {
      if (!val) {
        return "";
      } else {
        let date = new Date(val);
        let Y = date.getFullYear();
        let M =
          date.getMonth() + 1 < 10
            ? "0" + (date.getMonth() + 1)
            : date.getMonth() + 1;
        let D = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
        let hours = date.getHours();
        let minutes =
          date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
        let seconds =
          date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
        date =
          Y + "-" + M + "-" + D + " " + hours + ":" + minutes + ":" + seconds;
        console.log(date);
        return date;
      }
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import "~styles/mixins.styl"
@import "~styles/varibles.styl"
.dispatched-wrapper
  timelineContent()
  margin: -6px 0 -6px .45rem
  .item
    itemBaseStyle()
    font-size: calc(16px * var(--font-scale))!important
    min-height: .98rem
    height: auto
    display: flex
    align-items: baseline
    .content-css
      color: $contentColor
      font-size: calc(16px * var(--font-scale))!important
  .consumables-content
    box-sizing: border-box
    border-radius: 5px
    overflow: hidden
    background-color: #fff
    .list
      display: flex
      justify-content: space-between
      font-size: .30rem
      itemBaseStyle()
      padding: 0 1.2rem
      .count
        .num
          display: inline-block
          text-align: center
          width: 50px
.row-css
  min-height: .68rem
  word-wrap: break-word
  word-break: break-all
  line-height: 1.5em
.fold {
  display: none;
}
</style>
