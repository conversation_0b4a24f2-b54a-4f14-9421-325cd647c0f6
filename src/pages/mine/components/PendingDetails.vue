<template>
  <div class="pending-wrapper" :class="{'fold': fold}">
    <div class="pendOrder-desc border-rightbottom">
      <div class="weui-cell item title">
        <div class="weui-cell__hd"><label class="weui-label title">挂单说明</label></div>
      </div>
      <div class="weui-cell__bd desc content-css">
         {{ pendingData.disEntryOrdersReason }}
      </div>
    </div>
    <div class="weui-cells_form desc-form">
      <div class="er-level border-top item">
        <div class="er-text title">解决方案</div>
      </div>
      <div class="weui-cell__bd desc content-css border-rightbottom">
        {{ pendingData.disEntryOrdersSolution }}
      </div>
      <div class="weui-cell item">
        <div class="weui-cell__hd"><label class="weui-label title">预计解决</label></div>
        <div class="weui-cell__bd will-solve content-css">{{ disPlanSolutionTime }}</div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  import global from '@/utils/Global.js'
  export default {
    name: "PendingDetails",
    props:['pendingData'],
    computed:{
      disPlanSolutionTime (){
        let time = 0
        time = this.pendingData.disPlanSolutionTime == 0?'':global.timestampToTime(this.pendingData.disPlanSolutionTime,'.')
        let index = time.indexOf(" ");
        time = time.substring(0,index);
        return time
      },
      fold() {
        return this.pendingData.fold
      }
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
.pending-wrapper
  timelineContent()
  .pendOrder-desc
    background-color: #fff
  .desc
    line-height: 28px
    padding: 5px .32rem 0
    box-sizing: border-box
    background-color: #fff
    font-size: calc(16px * var(--font-scale))!important
    text-indent: 2em
    word-wrap: break-word
    word-break: break-word
  .desc-form
    margin-top: 0
  .text-wrapper
    color: $textColor
    font-size: .28rem
    display: flex
    align-items: center
    .ipt
      text-align: right
  .item
    itemBaseStyle()
    .title
      font-size: calc(16px * var(--font-scale))!important
    .will-solve
      text-align: right
  .pointer
    pointer-events:none
  .content-css
    color: $contentColor
    font-size: calc(16px * var(--font-scale))!important
.fold 
  display none
</style>
