<template>
  <div class="creat-wrapper" :class="{ fold: fold }">
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">工单号</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.workNum }}</div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">工单类型</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.workTypeName }}</div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">所属科室</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.sourcesDeptName }}</div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">申报来源</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.workSources == 0 ? "电话申报" : baseData.workSources == 1 ? "移动申报" : "中心申报" }}</div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">紧急程度</label>
      </div>
      <div class="weui-cell__bd content-css">
        <span :class="{ urgent: ['1','0'].includes(baseData.urgencyDegreeCode) }">{{ baseData.urgencyDegree }}</span>
      </div>
    </div>
    <div v-if="baseData.workTypeCode == 5">
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label title">身份隐匿</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.signatureFlagName }}</div>
      </div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">联系人</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.signatureFlag == 1 ? "***" : baseData.callerName }}</div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">电话</label>
      </div>
      <div class="weui-cell__bd content-css" style="color:#1aaff1" @click="makePhoneCall(baseData.sourcesPhone)">{{ baseData.signatureFlag == 1 ? "***" : baseData.sourcesPhone }}</div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">职工工号</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.signatureFlag == 1 ? "***" : baseData.callerJobNum }}</div>
    </div>
    <!-- 2021.12.06随后拍增加服务地点展示 -->
    <div v-if="baseData.workTypeCode == 11">
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务地点</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.itemType[0].localtion }}</div>
      </div>
    </div>
    <!-- 增加巡检报修工单服务地点展示 -->
    <div v-if="baseData.workTypeCode == 10">
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务地点</label>
        </div>
        <div class="weui-cell__bd content-css" style="line-height: 1.2">{{ baseData.itemType[0].localtion }}</div>
      </div>
    </div>
    <div v-if="baseData.workTypeCode == 6">
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务地点</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.itemType[0].localtion }}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.transportName }}</div>
      </div>
    </div>
    <div v-if="baseData.workTypeCode == 15">
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务事项</label>
        </div>
        <div class="weui-cell__bd content-css">公车预定</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">车辆信息</label>
        </div>
        <div class="weui-cell__bd content-css"></div>
      </div>
      <div class="tableCar" v-if="baseData.carList.length > 0">
        <table>
          <tr>
            <th>车辆品牌</th>
            <th>车牌号</th>
            <th>颜色</th>
            <th>座位数</th>
          </tr>
          <tr v-for="(item, ii) in baseData.carList" :key="ii">
            <td>{{ item.brand }}</td>
            <td>{{ item.number }}</td>
            <td>{{ item.colour }}</td>
            <td>{{ item.seatsNum }}</td>
          </tr>
        </table>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">开始时间</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.startDate | dateFilter }}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">结束时间</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.endDate | dateFilter }}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">出发地</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.transportStartLocal }}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">目的地</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.transportEndLocal }}</div>
      </div>
    </div>
    <!-- <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd"><label class="weui-label title">职务</label></div>
      <div class="weui-cell__bd content-css">{{baseData.signatureFlag == 1?'***': baseData.callerJob }}</div>
    </div>-->
    <div v-if="(baseData.workTypeCode == 1 || baseData.workTypeCode == 2) && baseData.workSources == 1">
      <!--保洁和维修并且来源于移动申报-->
      <div v-for="(item, index) of baseData.itemType" :key="index">
        <div class="weui-cell item border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务地点</label>
          </div>
          <div class="weui-cell__bd content-css">{{ item.localtion }}</div>
        </div>
        <div class="weui-cell item border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务事项</label>
          </div>
          <div class="weui-cell__bd questionDetail content-css" v-if="baseData.workTypeCode == 1 || baseData.workTypeCode == 2 || baseData.workTypeCode.length > 2">
            {{ serviceMatters }}
          </div>
          <div class="weui-cell__bd questionDetail content-css" v-else>{{ baseData.transportName }}</div>
        </div>
      </div>
    </div>
    <!-- 自定义工单 -->
    <!-- 自定义工单综合类 2021-2-4 -->
    <div v-if="baseData.workTypeCode.length > 2 && baseData.template != 3">
      <div v-for="(item, index) of baseData.itemType" :key="index">
        <div class="weui-cell item border-rightbottom border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务地点</label>
          </div>
          <div class="weui-cell__bd content-css">{{ item.localtion }}</div>
        </div>
        <div class="weui-cell item border-rightbottom">
          <div class="weui-cell__hd">
            <label class="weui-label title">服务事项</label>
          </div>
          <div class="weui-cell__bd questionDetail content-css" v-if="baseData.workTypeCode == 1 || baseData.workTypeCode == 2 || baseData.workTypeCode.length > 2">
            {{ serviceMatters }}
          </div>
          <div class="weui-cell__bd questionDetail content-css" v-else>{{ baseData.transportName }}</div>
        </div>
      </div>
      <!-- 数量 -->
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">数量</label>
        </div>
        <div class="weui-cell__bd content-css">{{ baseData.customtransportNum }}</div>
      </div>
    </div>
    <!--问题描述-->
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">问题描述</label>
      </div>
    </div>
    <div class="weui-cell__bd">
      <div class="desc content-css" style="white-space: pre-wrap">{{ baseData.questionDescription }}</div>
    </div>

    <!--录音-->
    <!-- 中心申报不显示录音
      pc端配置不显示的时候录音隐藏
    -->
    <div class="voice-desc border-bottom" v-if="baseData.workSources != 2 && baseData.workTypeCode != 15 && baseData.isShowCallerTape == 'true'">
      <span class="text-title title">录音</span>
      <div v-if="baseData.callerTapeUrl == ''" class="content-css"></div>
      <div v-if="baseData.callerTapeUrl != ''" class="voice-content">
        <div v-if="isCallerTapeUrl" class="voice-btn">
          <button class="weui-btn weui-btn_primary" v-if="play" @click="handlePlayAudioClick">
            <div class="play-icon">
              <img src="~images/icon_wify2.png" class="play-img" />
              <span>{{ audioDuration }}</span>
            </div>
            点击播放
          </button>
          <button class="weui-btn weui-btn_primary" v-else @click="handlePauseAudioClick">
            <div class="play-icon">
              <img src="~images/icon_wify1.gif" class="play-img" />
              <span>{{ audioDuration }}</span>
            </div>
            正在播放
          </button>
        </div>
      </div>
    </div>
    <!--附件-->
    <div class="img-desc border-rightbottom" v-if="baseData.workTypeCode != 15 && baseData.workSources == 1">
      <!--<div class="text-title title">图片</div>-->
      <div class="img-desc-content">
        <span class="text-title title">图片</span>
        <span v-if="baseData.attachment.length == 0" class="content-css"></span>
      </div>
      <div class="img-content">
        <div class="img-wrapper" v-for="ele of baseData.attachment" :key="ele">
          <div class="img-box">
            <img
              v-preview="$YBS.imgUrlTranslation(ele)"
              :src="$YBS.imgUrlTranslation(ele)"
              class="img"
              preview-title-enable="true"
              preview-nav-enable="true"
              preview-top-title-tnable="true"
              preview-title-extend="false"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="weui-cell border-rightbottom item" v-if="!(baseData.appointmentType!='预约'&& baseData.urgencyDegree=='一般')">
      <div class="weui-cell__hd">
        <label class="weui-label title">服务时间</label>
      </div>
      <div class="weui-cell__bd content-css" v-if="baseData.appointmentDate == '0'">立刻</div>
      <div class="weui-cell__bd content-css" v-else style="color:red">{{ appointmentDate }}</div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title" style="width: 120px">要求完工时间</label>
      </div>

      <div v-if="baseData.requireAccomplishDate" class="weui-cell__bd content-css">{{ moment(baseData.requireAccomplishDate).format("YYYY.MM.DD HH:mm:ss") }}</div>
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd">
        <label class="weui-label title">来电号码</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.needPhone }}</div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd">
        <label class="weui-label title">返修工单</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.repairWork == 1 ? "否" : "是" }}</div>
    </div>
    <div class="weui-cell item" v-if="baseData.workTypeCode != 15">
      <div class="weui-cell__hd">
        <label class="weui-label title">申报属性</label>
      </div>
      <div class="weui-cell__bd content-css">{{ baseData.typeSources }}</div>
    </div>
    <audio class="audio" controls ref="audio" :src="$YBS.imgUrlTranslation(baseData.callerTapeUrl)" @canplay="handleCanPlay" @ended="handleAudioEnded"></audio>
    <!-- src="http://sjtlinen.logimis.com:10001/ipsm/d015fc9a90254bde9b249f74dbd5a22f.mp3?Content-Disposition=attachment%3B%20filename%3D%22d015fc9a90254bde9b249f74dbd5a22f.mp3%22&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20220908%2F%2Fs3%2Faws4_request&X-Amz-Date=20220908T064622Z&X-Amz-Expires=432000&X-Amz-SignedHeaders=host&X-Amz-Signature=bebf4a4de889da5fe8f55b1aab318ee758f71a8be4291e1dee9ddf523ed3d0c5" -->
    <!-- src="http://sjtlinen.logimis.com:10001/ipsm/e22c60f24e0242e6a39ca04fe3418e46.mp3?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20220905%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20220905T124659Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=6507ded7cdc7e0a4b80f269d92b3a76a5dd4121e602aa53aab8f46e589e8b31b" -->
    <!-- src="http://sjtlinen.logimis.com:10001/ipsm/d932d946b8be41bd84b2ea8c1d1e2da9.mp3?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20220905%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20220905T114812Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=e6fa44903c331e8faa11bb203daf949f257d15ec8ab07595ad0c96f0fb5d830b" -->
  </div>
</template>

<script type=text/ecmascript-6>
import global from "@/utils/Global.js";
import moment from "moment";
export default {
  name: "BaseInfo",
  props: ["baseData"],
  data() {
    return {
      moment,
      play: true,
      isCallerTapeUrl: true,
      audioDuration: "",
      appointmentDate: ""
    };
  },
  computed: {
    fold() {
      return this.baseData.fold;
    },
    serviceMatters() {
      let matter = "";
      let itemType = this.baseData.itemType[0];
      if (itemType.itemServiceCode) {
        matter = itemType.itemTypeName + "-" + itemType.itemDetailName + "-" + itemType.itemServiceName;
      } else if (itemType.itemDetailCode) {
        matter = itemType.itemTypeName + "-" + itemType.itemDetailName;
      } else {
        matter = itemType.itemTypeName;
      }
      return matter;
    }
  },
  filters: {
    dateFilter(msg) {
      return moment(msg).format("YYYY-MM-DD HH:mm");
    }
  },
  methods: {
    //点击电话号码拨打电话
     makePhoneCall(phoneNumber) {
      // 创建一个 a 元素
      const link = document.createElement('a');
      // 设置 href 属性为 tel 协议链接
      link.href = `tel:${phoneNumber}`;
      // 触发点击事件
      link.click();
    },
    /**
     * 播放音频
     */
    handlePlayAudioClick() {
      // 如果baseData.callerTapeUrl不是以http或者https开头的，就不执行播放
      if (!this.baseData.callerTapeUrl.startsWith("http") && !this.baseData.callerTapeUrl.startsWith("https")) {
        $.toast("无效的录音地址", "text");
        return;
      }
      this.play = false;
      const audio = this.$refs.audio;
      audio.play();
    },
    handleCanPlay() {
      this.audioDuration = Math.round(this.$refs.audio.duration) + "″";
    },
    /**
     * 暂停播放音频
     */
    handlePauseAudioClick() {
      this.play = true;
    },
    /**
     *音频播放结束事件
     */
    handleAudioEnded() {
      this.play = true;
    },
    /**
     * 时间格式化载体
     */
    timeFunc() {}
  },
  mounted() {
    this.appointmentDate = global.timestampToTime(this.baseData.appointmentDate, ".");
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.tableCar {
  width: calc(100% - 16px);
  // margin-right 16px
  box-sizing: border-box;
  overflow-x: scroll;

  >table {
    // width calc(100% - 16px)
    text-align: center;

    th, td {
      font-size: 0.32rem;
      border: 1px solid #eee;
      padding: 15px 15px;
      white-space: nowrap;
    }

    th {
      background: #E8ECFF;
    }
  }
}

.creat-wrapper {
  timelineContent();

  .item {
    itemBaseStyle();

    .urgent {
      color: red;
    }
  }

  .desc {
    line-height: 28px !important;
    padding: 0.2rem 0.32rem;
    box-sizing: border-box;
    background-color: #fff;
    text-indent: 2em;
    font-size: 0.32rem;
    word-wrap: break-word;
    word-break: break-all;
  }

  .voice-desc {
    display: flex;
    align-items: center;
    padding: 0.54rem 0.32rem;
    background-color: #fff;
    font-size: 0.32rem;

    .text-title {
      width: 105px;
    }

    button {
      display: flex;
      align-items: center;
      justify-content: center;

      .play-icon {
        display: flex;
        position: absolute;
        align-items: center;
        left: 0;
        justify-content: center;
        padding-left: 5px;

        .play-img {
          width: 0.4rem;
        }

        span {
          font-size: 14px;
          padding-left: 5px;
        }
      }
    }
  }

  .content-css {
    color: $contentColor;
    line-height: 1.5em;
    display: flex;
    align-items: center;
    font-size: calc(16px * var(--font-scale))!important
  }

  .img-desc {
    background-color: #fff;
    font-size: 0.32rem;

    .img-desc-content {
      display: flex;
      justify-content: start;
      align-items: center;

      .text-title {
        display: inline-block;
        line-height: 1rem;
        padding-left: 0.32rem;
        width: 105px;
      }

      .content-css {
        display: inline;
      }
    }

    .img-content {
      background: #fff;
      padding: 0 0.3rem;
      display: flex;

      .img-wrapper {
        width: 30%;
        height: 1.4rem;
        margin: 0.1rem;
        position: relative;

        .img-box {
          height: 100%;
          overflow: hidden;

          .img {
            width: 100%;
          }
        }
      }
    }
  }

  .audio {
    display: none;
  }
}

.title {
  font-size: calc(16px * var(--font-scale))!important
}

.voice-content {
  width: 100%;

  .voice-btn {
    flex: 1;

    .weui-btn_primary {
      line-height: 0.78rem;
    }
  }
}

.fold {
  display: none;
}
</style>
