<!--
 * @Description:
-->
<template>
  <div class="return-visit-wrapper flod" :class="{'fold': fold}">
    <div class="item border-rightbottom">
      <div class="weui-cell__hd"><label class="weui-label title">{{ titleName }}说明</label></div>
      <div class="weui-cell__bd will-solve content-css">{{returnVisitdData.feedbackExplain}}</div>
      <div class="weui-cell__hd"><label class="weui-label title">{{ titleName }}人</label></div>
      <div class="weui-cell__bd will-solve content-css">{{returnVisitdData.createByName}}</div>
      <div class="weui-cell__hd"><label class="weui-label title">{{ titleName }}时间</label></div>
      <div class="weui-cell__bd will-solve content-css">{{ timeFunc(returnVisitdData.createDate, ".") }}</div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import global from "@/utils/Global";
  export default {
    name: "ReturnVisit",
    props: ['returnVisitdData', 'name'],
    computed: {
    fold() {
      return this.returnVisitdData.fold;
    }
   },
    data () {
      return {
        titleName: ''
      }
    },
    mounted () {
      this.timeFunc = global.timestampToTime;
      this.titleName = this.name.substr(1)
    },
    methods:{
      timeFunc() {},
    },
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
.return-visit-wrapper
  timelineContent()
  .title
    font-size: calc(16px * var(--font-scale))!important
    line-height: 0.98rem;
    padding: 0 .32rem;
  .content-css
    color: $contentColor
    font-size: calc(16px * var(--font-scale))!important
    word-break: break-word;
    padding: 0 .32rem;
    text-indent: 2em;
    line-height: 2em;
.flod
  display block;
.fold
  display: none;

</style>
