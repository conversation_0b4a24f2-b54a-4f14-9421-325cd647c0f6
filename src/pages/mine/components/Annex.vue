<template>
  <div class="wrapper">
    <div class="second">
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd"><label class="weui-label title">申报描述</label></div>
      </div>
      <div class="weui-cell__bd">
      <!--type:1 我的工单 2：我的工作台-->
          <textarea
              class="weui-textarea desc content-css"
              rows="4"
              disabled
              v-if="annexData.type == 1 && annexData.flowCode == ''"
          >{{annexData.questionDescription}}</textarea>
          <textarea
              class="weui-textarea desc content-css"
              rows="4"
              disabled
              v-if="annexData.type == 2 || annexData.flowCode != ''"
          >{{annexData.questionDescription}}</textarea>
      </div>
      <!-- 录音 -->
      <div class="voice-desc border-bottom" v-if="annexData.isShowCallerTape=='true'">
        <span class="text-title title">录音</span>
        <div v-if="annexData.callerTapeUrl != ''" class="voice-content">
          <div v-if="isCallerTapeUrl" class="voice-btn">
            <button
                class="weui-btn weui-btn_primary"
                v-if="play"
                @click="handlePlayAudioClick"
            >
              <div class="play-icon">
                <img src="~images/icon_wify2.png" class="play-img">
                <span>{{audioDuration}}</span>
              </div>
              点击播放
            </button>
            <button
                class="weui-btn weui-btn_primary"
                v-else
                @click="handlePauseAudioClick"
            >
              <div class="play-icon">
                <img src="~images/icon_wify1.gif" class="play-img">
                <span>{{audioDuration}}</span>
              </div>
              正在播放
            </button>
          </div>
        </div>
      </div>
      <!-- 附件 -->
      <div class="img-desc">
        <div class="text-title title">图片</div>
        <div class="img-content">
          <div
            class="img-wrapper"
            v-for="item of annexData.attachment"
          >
            <div class="img-box">
              <img
                v-preview="item"
                :src="item"
                class="img"
                preview-title-enable="true"
                preview-nav-enable="true"
                preview-top-title-tnable="true"
                preview-title-extend="false"
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <audio
        class="audio"
        controls
        ref="audio"
        @canplay="handleCanPlay"
        @ended="handleAudioEnded"
        :src="annexData.callerTapeUrl"
    ></audio>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "AnnexInfo",
    props:['annexData'],
    data () {
      return {
        play: true,
        isCallerTapeUrl: true,
        audioDuration: '',
      }
    },
    methods: {
      /**
       * 播放音频
       */
      handlePlayAudioClick () {
        this.play = false
        const audio = this.$refs.audio
        audio.play();
//        $.showLoading("加载中……")
      },
      handleCanPlay () {
        this.audioDuration = Math.round(this.$refs.audio.duration) + '″'
      },
      /**
       * 暂停播放音频
       */
      handlePauseAudioClick () {
        this.play = true
      },
      /**
       *音频播放结束事件
       */
      handleAudioEnded () {
        this.play = true
      },
    }
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .wrapper
    background-color: $bgColor
    .item
      itemBaseStyle()
      .questionDetail
        color:$contentColor
        line-height: 1.5em

    .second
      background-color: #fff
      .desc
        line-height: 28px
        padding: 0 10px
        box-sizing: border-box
        background-color: #fff
        text-indent: 2em
      .voice-desc
        display: flex
        align-items: center
        padding: .54rem .32rem
        .text-title
          width: 2.4rem
          color: #888
        button
          display: flex
          align-items: center
          justify-content: center
          .play-icon
            display: flex
            position: absolute
            align-items: center
            left: 0
            justify-content: center
            padding-left: 5px
            .play-img
              width: .4rem
            span
              font-size: 14px
              padding-left: 5px
      .img-desc
        .text-title
          color: #888888
          line-height: 1rem
          padding-left: .32rem
        .img-content
          background: #fff
          padding: 0 .3rem
          display: flex
          .img-wrapper
            width: 30%
            height: 1.4rem
            margin: .1rem
            position: relative
            .img-box
              height: 100%
              overflow: hidden
              .img
                width: 100%
    .audio
      display: none
  .content-css
    color: $contentColor
  .title
    font-size: .32rem
  .voice-content
    width: 100%
    .voice-btn
      flex: 1
</style>