<template>
  <div class="handle-wrapper">
    <!--转派中的指派班组-->
    <div class="notice-wrapper">
      <div class="weui-cell item designate-css border-rightbottom">
        <div class="weui-cell__hd">
          <label class="weui-label title">服务部门</label>
        </div>
        <div class="weui-cell__bd ipt-content">
          <input class="weui-input ipt content-css" id="job" type="text" readonly placeholder="请选择服务部门" v-model="teamInfo.name" data-target @click="selectTeam" />
          <span class="iconfont arrow">&#xe646;</span>
        </div>
      </div>
      <div class="weui-cell border-rightbottom item">
        <div class="weui-cell__hd">
          <label class="weui-label">指派人员</label>
        </div>
        <div class="weui-cell__bd text-wrapper content-css" @click="handleDesignatePersonnel()">
          <span v-if="personnelVal" class="content-css line-css">{{ designatePersonnel }}</span>
          <span v-else class="please-select content-css">
            请选择
            <span class="iconfont arrow">&#xe646;</span>
          </span>
        </div>
      </div>
      <pop-bottom :dataInfo="popData" @setVal="handleTeamVal"></pop-bottom>
      <PublicServiceDepartment :showPicker="showPicker" :dataObj="dataObj" @cancel="showPicker = false" @confirm="onConfirm"></PublicServiceDepartment>
      <div class="weui-cells_form desc-form pendOrder-desc">
        <div class="weui-cell">
          <div class="weui-cell__hd">
            <label class="weui-label title">转派说明</label>
          </div>
        </div>
        <div class="weui-cell desc">
          <div class="weui-cell__bd content-css">
            <textarea
              class="weui-textarea textarea"
              placeholder="请输入转派说明，字数在120字以内"
              maxlength="120"
              rows="8"
              v-model="textareaValue"
              @blur="handleTextareaBlurEvent"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import PopBottom from "@/common/PopupBottom";
import global from "@/utils/Global";
import { mapState } from "vuex";
import PublicServiceDepartment from "@/pages/form/newForm/components/publicServiceDepartment.vue";
export default {
  name: "ChangeOrderHandle",
  components: {
    PopBottom,
    PublicServiceDepartment
  },
  computed: {
    ...mapState(["addDesignatePersonnel"])
  },
  props: ["title", "localtionId", "itemTypeCode", "workTypeCode", "designateDeptCode", "workOrdeInfoDate"],
  data() {
    return {
      teamInfo: window.teamInfo || {},
      popData: {},
      personnelVal: false,
      designatePersonnel: "", //指派人员
      designatePersonnelId: "", //指派人员id
      designatePersonnelTel: "", //指派人员电话
      designatePersonnelOpenid: "", //指派人员openId
      textareaValue: window.textareaValue || "",
      teamOldCode: "",
      teamNewCode: "",
      showPicker: false,
      dataObj: {},
    };
  },
  watch: {
   
    textareaValue() {
      // 转派说明限制禁止输入表情
      this.textareaValue = this.textareaValue.replace(
        /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g,
        ""
      );
    },
    teamInfo (value, oldvalue) {
       
      if (oldvalue.id) {
        this.teamNewCode = value.id;
        this.teamOldCode = oldvalue.id;
      }
      if (oldvalue.id != value.id) {
        this.personnelVal = false;
        this.designatePersonnel = ""; //指派人员
        this.designatePersonnelId = ""; //指派人员id
        this.designatePersonnelTel = ""; //指派人员电话
        this.designatePersonnelOpenid = ""; //指派人员openId
      }
    }
  },
  created () {
    this.teamInfo.name = ''
    const { workTypeCode } = this.$route.query;
    this.dataObj = {
      workTypeCode
    };
  },
  mounted () {
    // this.$api
    //   .getTeamsByTask({
    //     workTypeCode: this.workTypeCode, //工单类型
    //     localtionId: this.localtionId, //区域ID（最后一级）
    //     itemTypeCode: this.itemTypeCode //服务事项一级分类
    //   })
    //   .then(resp => {
    //     const res = resp.list;
    //     res.forEach(ele => {
    //       ele.name = ele.team_name;
    //     });
    //     this.teamInfo =
    //       res.find(item => {
    //         return item.id == this.designateDeptCode;
    //       }) || {};
    //     this.popData = {
    //       flag: 2,
    //       data: res,
    //       title: "班组",
    //       nameAndCode: true
    //     };
    //   });
      
    if (this.addDesignatePersonnel.length > 0 && this.$route.query.backParams) {
      this.havePendingPeo = true;
      this.personnelVal = true;
      let addDesignatePers = this.addDesignatePersonnel;
      let allAtten = "";
      let allAttenId = "";
      let allAttenNotice = "";
      let allAttenTel = "";
      let allAttenOpenId = "";
      for (let i = 0; i < addDesignatePers.length; i++) {
        allAtten += addDesignatePers[i].designatePersonName + ",";
        allAttenId += addDesignatePers[i].designatePersonCode + ",";
        allAttenTel += addDesignatePers[i].designatePersonPhone + ",";
        allAttenOpenId += addDesignatePers[i].openId + ",";
        allAttenNotice += addDesignatePers[i].designatePersonName + addDesignatePers[i].designatePersonPhone + ",";
      }
      if (allAttenId == "") {
        this.personnelVal = false;
      } else {
        this.personnelVal = true;
      }
      this.designatePersonnel = allAtten.slice(0, allAtten.length - 1);
      this.designatePersonnelId = allAttenId.slice(0, allAttenId.length - 1);
      this.designatePersonnelTel = allAttenTel.slice(0, allAttenTel.length - 1);
      this.designatePersonnelOpenid = allAttenOpenId.slice(0, allAttenOpenId.length - 1);
    }
    if (this.$route.query.source == "myWorkbench") {
      //清空指派人员信息（已填写的信息缓存）
      this.clearDesignateP();
    }
  },
  methods: {
    onConfirm (val) {
      this.teamInfo.name = val.team_name;
      this.teamInfo.id = val.id;
      this.showPicker = false;
    },
    /**
     * 获取指派班组
     */
    selectTeam() {
      this.showPicker = true;
      return;
      // this.getTeamInfo();
    },
    handleTeamVal(val) {
      this.teamInfo = val.resData;
    },
    handleTextareaBlurEvent() {
      let scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
      window.scrollTo(0, Math.max(scrollHeight - 1, 0));
    },
    /**
     * 时间格式化载体
     */
    timeFunc() {},
    getTeamInfo() {
      $("#education").popup();
    },
    handleDesignatePersonnel() {
      window.textareaValue = this.textareaValue;
      window.teamInfo = this.teamInfo;
      this.$route.query.backParams = this.$route.query.backParams ? this.$route.query.backParams : {};
      this.teamInfo = this.teamInfo ? this.teamInfo : {};
      this.$router.push({
        path: "/personnel",
        query: {
          source: "transfer",
          designateDeptCode: this.teamInfo.id || this.$route.query.designateDeptCode,
          workOrderId: this.$route.query.workOrderId || this.$route.query.backParams.workOrderId,
          workOrdeInfoDate: this.workOrdeInfoDate || this.$route.query.backParams.workOrdeInfoDate,
          handleChangeOderInfo: true,
          handleComponentsTitle: "转派处理",
          workTypeCode: this.workTypeCode || this.$route.query.backParams.workTypeCode,
          localtionId: this.$route.query.localtionId || this.$route.query.backParams.localtionId,
          itemTypeCode: this.$route.query.itemTypeCode || this.$route.query.backParams.itemTypeCode
        }
      });
    },
    /**
     * 清空已选指派人员
     */
    clearDesignateP() {
      this.designatePersonnel = "";
      this.designatePersonnelId = "";
      this.designatePersonnelTel = "";
      this.designatePersonnelOpenid = "";
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.arrow {
  float: right;
}

.handle-wrapper {
  .consumables-wrapper {
    background: #fff;
    overflow: hidden;

    .consumables-content {
      box-sizing: border-box;
      border-radius: 5px;
      overflow: hidden;
      margin: 0.32rem;
    }
  }

  .notice-wrapper {
    height: auto;
    min-height: 0.98rem;
    line-height: 1.5em;
    // padding: 0px 0.45rem 0px 0.37rem;
    margin-top: 0.25rem;
  }

  .designate-css {
    margin-top: -5px;
    padding-top: 10px;

    .ipt-content {
      display: flex;

      .content-css {
        color: $contentColor;
        font-family: Arial, 'Microsoft Yahei', 'Helvetica Neue', Helvetica, sans-serif;
        font-size: calc(16px * var(--font-scale))!important;
      }
    }
  }

  .item {
    .header {
      height: 0.57rem;
      display: flex;
      align-items: center;
      margin: 0.15rem 0 0.15rem 0.3rem;
      background: #fff;

      .iconfont {
        font-size: 0.4rem;
        color: $btnColor;
      }

      .title-body {
        height: 0.6rem;
        display: flex;
        align-items: center;
        margin-left: 10px;
        background: #eceef8;
        padding: 0 0.24rem;
        border-radius: 0.3rem;
        white-space: nowrap;

        .dot {
          display: inline-block;
          width: 0.09rem;
          height: 0.09rem;
          background: $btnColor;
        }

        .title {
          font-size: 0.28rem;
          font-weight: 700;
          margin: 0 0.45rem 0 0.08rem;
        }

        .time {
          font-size: 0.3rem;
          color: #4F87FB;
        }
      }
    }
  }
}

.pendOrder-desc {
  background-color: #fff;
  padding-top: 10px;

  .textarea {
    text-indent: 2em;
  }
}

.title {
  font-size: calc(16px * var(--font-scale));
}

.line-css {
  line-height: 0.52rem !important;
}
.content-css {
  font-size: calc(16px * var(--font-scale))!important;
}
</style>
