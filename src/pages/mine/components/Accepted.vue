<template>
<!-- 我的申报 已受理 -->
  <div class="content" :class="{'fold': fold}">
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd"><label class="weui-label title">调度员</label></div>
      <div class="weui-cell__bd content-css">{{ serviceData.createByName }}</div>
    </div>
    <div class="weui-cell item border-rightbottom">
      <div class="weui-cell__hd"><label class="weui-label title">职工工号</label></div>
      <div class="weui-cell__bd content-css">{{ serviceData.createByNo }}</div>
    </div>
    <div v-if="workTypeCode == 3 || template == 3" >
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">搬运类型</label></div>
        <div class="weui-cell__bd content-css row-css">{{serviceData.transportTypeName}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">携带工具</label></div>
        <div class="weui-cell__bd content-css row-css">{{serviceData.carryTools}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">需用人数</label></div>
        <div class="weui-cell__bd content-css">{{serviceData.needNum}}</div>
      </div>
    </div>
    <div v-if="(workTypeCode == 1 || workTypeCode == 2) && serviceData.workSources != 1"><!--保洁和维修-->
      <div v-for="(item,index) of serviceData.itemType" :key="index">
        <div class="weui-cell item border-rightbottom border-rightbottom">
          <div class="weui-cell__hd"><label class="weui-label title">服务地点</label></div>
          <div class="weui-cell__bd content-css row-css">{{item.localtion}}</div>
        </div>
        <div class="weui-cell item border-rightbottom border-rightbottom">
          <div class="weui-cell__hd"><label class="weui-label title">服务事项</label></div>
            <div class="weui-cell__bd questionDetail content-css" v-if="workTypeCode == 1 || workTypeCode == 2">{{item.itemTypeName}}-{{item.itemDetailName}}-{{item.itemServiceName}}</div>
            <div class="weui-cell__bd questionDetail content-css" v-else>{{serviceData.transportName}}</div>
        </div>
      </div>
    </div>
    <div v-if="workTypeCode == 3 || template == 3 "> <!--运输-->
       <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">服务起点</label></div>
        <div class="weui-cell__bd content-css row-css">{{serviceData.transportStartLocal}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">起点科室</label></div>
        <div class="weui-cell__bd  content-css row-css">{{serviceData.transportStartLocalOffice}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">服务终点</label></div>
        <div class="weui-cell__bd content-css row-css">{{serviceData.transportEndLocal}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">终点科室</label></div>
        <div class="weui-cell__bd content-css row-css">{{serviceData.transportEndLocalOffice}}</div>
      </div>
      <!-- <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">接收人</label></div>
        <div class="weui-cell__bd content-css">{{serviceData.recipientPersonName}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">接收人电话</label></div>
        <div class="weui-cell__bd content-css">{{serviceData.recipientPersonPhone}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">接收人职务</label></div>
        <div class="weui-cell__bd content-css">{{ serviceData.recipientPersonJob }}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">职工工号</label></div>
        <div class="weui-cell__bd content-css">{{serviceData.recipientPersonJobnum}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">所属科室</label></div>
        <div class="weui-cell__bd content-css">{{serviceData.recipientPersonDept}}</div>
      </div>
      <div class="weui-cell item border-rightbottom">
        <div class="weui-cell__hd"><label class="weui-label title">科室电话</label></div>
        <div class="weui-cell__bd content-css">{{serviceData.recipientPersonDeptPhone}}</div>
      </div> -->
    </div>
    <!--申报描述-->
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd"><label class="weui-label title">申报描述</label></div>
    </div>
    <div class="weui-cell__bd desc content-css border-rightbottom">
        {{ serviceData.questionDescription }}
    </div>
    <div class="weui-cell border-rightbottom item">
      <div class="weui-cell__hd"><label class="weui-label title">服务部门</label></div>
      <div class="weui-cell__bd content-css">{{serviceData.designateDeptName}}</div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
  export default {
    name: "ServiceSite",
    props: ['serviceData', 'workTypeCode',"template"],
    computed: {
      fold() {
        return this.serviceData.fold
      },
    },
  }
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"

  .content
    timelineContent()
    .item
      itemBaseStyle()
      font-size: calc(16px * var(--font-scale))!important
    .content-css
      color:$contentColor
      font-size: calc(16px * var(--font-scale))!important
      line-height: 1.2!important
      .title
        font-size: calc(16px * var(--font-scale))!important
    .questionDetail
      color:$contentColor
    .row-css
      line-height: 1.5em
    .desc
      line-height: 28px
      font-size: calc(16px * var(--font-scale))!important
      padding: 0 10px
      box-sizing: border-box
      background-color: #fff
      text-indent: 2em
      word-wrap: break-word
  .fold {
    display: none;
  }
</style>
