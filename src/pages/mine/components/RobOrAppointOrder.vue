<template>
  <div class="container">
    <!--时间轴标题-->
    <div class="item">
      <div class="header">
        <div class="iconfont">&#xe681;</div>
        <div class="title-body">
          <span class="dot"></span>
          <span class="title">{{ designatePersonnelTitle }}</span>
          <span class="time">{{ timeFunc(Date.parse(new Date()),'.') }}</span>
        </div>
      </div>
    </div>
      <!--选择指派人员信息-->
      <div class="weui-cell item notice-wrapper designate-css">
        <div class="weui-cell__hd"><label class="weui-label">指派人员</label></div>
        <div class="weui-cell__bd text-wrapper" @click="handleDesignatePersonnel()"
              :class="{disClick:isVieOder,disabledClick:disabled}">
          <span v-if="personnelVal && workOrdeInfoDate">{{designatePersonnel}}</span>
          <span v-else>请选择 <span class="iconfont">&#xe646;</span></span>
        </div>
      </div>
      <div class="weui-cell item notice-wrapper" v-if="!isVieOder">
        <div class="weui-cell__hd"><label class="weui-label">消息通知</label></div>
        <div class="weui-cell__bd text-wrapper">
          <span>{{noticePeople}}</span>
        </div>
        <div class="weui-cell__ft">
        </div>
      </div>
  </div>
</template>

<script type=text/ecmascript-6>
import global from '@/utils/Global.js'
import {mapState} from 'vuex'
export default {
  name: "RobOrAppointOrder",
  props: {
    isVieOder: {//是否显示消息通知（为派工工单） true只显示抢单同时不显示消息通知
      type: Boolean,
      default: true
    },
    personnelVal: {
      type: Boolean,
      default: false
    },
    designatePersonnelTitle: {
      type: String,
      default: '抢单'
    },
    workOrdeInfoDate: {
      type: Array,
      default: function() {
        return []
      }
    },
    noticePeople: {
      type: String,
      default: '派工人员'
    }
  },
  data() {
    return {
      disabled: false,
      isAppoint: '派工',
      isShow: true,     //从推送消息进入后的指派人员等相关信息是否显示 true 不显示
      // noticePeople: '派工人员',
      designatePersonnel: '',
      designatePersonnelId: '',
      designatePersonnelTel: '',
      designatePersonnelOpenId: '',
    }
  },
  computed: {
    ...mapState(['loginInfo', 'staffInfo','addDesignatePersonnel','isLogin']),
  },
  watch: {
  },
  methods: {
    /**
     * 时间格式化载体
     */
    timeFunc () {},
    /**
     * 跳转到指派人员页面
     */
    handleDesignatePersonnel () {
      this.$router.push({
        path:'/personnel',
        query: {
          isShow: this.isShow,
          isVieOder: this.isVieOder,
          noticePeople: this.noticePeople,
          disabled: this.disabled,
          designateDeptCode: this.workOrdeInfoDate[0].designateDeptCode
        }
      })
    },
    /**
     * 判断是否为抢单
     */
    isRobOrder() {
      if (this.personnelVal) {
          this.designatePersonnel= this.staffInfo.staffName
          this.designatePersonnelId= this.staffInfo.teamPersonId
          this.designatePersonnelTel= this.staffInfo.mobile
          this.designatePersonnelOpenId= this.staffInfo.wechat
        }
    },
  },
  mounted() {
    this.timeFunc = global.timestampToTime
    console.log(this.$route);
    this.isRobOrder()
  }
}
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import "~styles/mixins.styl"
  @import "~styles/varibles.styl"
  .container
    font-size: .32rem
    .item
      .header
        height: .57rem
        display: flex
        align-items: center
        margin: .15rem 0 .15rem .3rem
        background: #fff
        position: relative
        .iconfont
          font-size: .4rem
          color: $btnColor
        .title-body
          height: .6rem
          display: flex
          align-items: center
          margin-left: 10px
          background: #eceef8
          padding: 0 .24rem
          border-radius: .3rem
          white-space: nowrap
          .dot
            display: inline-block
            width: .09rem
            height: .09rem
            background: $btnColor
          .title
            font-size: .28rem
            font-weight: 700
            margin: 0 .45rem 0 .08rem
          .time
            font-size: .3rem
            color: #4F87FB
    .vie-info
      .consumables-wrapper
        background: #fff
        overflow: hidden
        .consumables-content
          box-sizing: border-box
          border-radius: 5px
          overflow: hidden
          margin: .32rem
      .notice-wrapper
        height: auto
        min-height: .98rem
        line-height: 1.5em
        padding: 0px .45rem 0px 0.37rem
        margin-left: 0.45rem
        border-left: 1px solid #e5e5e5
      .designate-css
        margin-top: -5px
        padding-top: 10px
    .disClick
    .disabledClick
      pointer-events: none
</style>