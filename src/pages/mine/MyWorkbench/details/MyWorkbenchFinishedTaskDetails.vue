<template>
  <div class="deskDetails-conteent">
    <Header title="工单详情" @backFun="goback"></Header>
    <div class="wrapper" :class="{ 'large-font': isLargeFont }">
      <div class="item" v-for="(item, idx) of workOrdeInfoDate" :key="idx" @click.stop="handleFold($event, idx)">
        <!--基本详情-->
        <div v-if="item.operationCode == 1">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <base-info :baseData="item"></base-info>
        </div>
        <!--已受理-->
        <div v-if="item.operationCode == 2">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <service-site :serviceData="item" :workTypeCode="workTypeCode" :template="template"></service-site>
        </div>
        <!--已派工-->
        <div v-if="item.operationCode == 3">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <person :persData="item" :workDetails="workOrdeInfoDate"></person>
        </div>
        <!--已挂单-->
        <div v-if="item.operationCode == 4">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <pending-details :pendingData="item"></pending-details>
        </div>
        <!--已完工-->
        <div v-if="item.operationCode == 6">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <evaluation-info ref="evaluation" :finishedData="item" :workTypeCode="workTypeCode" :template="template"></evaluation-info>
        </div>
        <!--回退-->
        <div v-if="item.operationCode == 30">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <retroversion :cancelData="item"></retroversion>
        </div>
        <!--回访-->
        <div v-if="item.operationCode == 5">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <return-visit :returnVisitdData="item" :name="item.operationType"></return-visit>
        </div>
        <!--督办-->
        <div v-if="item.operationCode == 8">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <return-visit :returnVisitdData="item" :name="item.operationType"></return-visit>
        </div>

        <!-- 转单 -->
        <div v-if="item.operationCode == 9">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <change-order :changeOrderData="item"></change-order>
        </div>
        <!-- 转派 -->
        <div v-if="item.operationCode == 11">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <change-order :changeOrderData="item"></change-order>
        </div>
        <!--取消-->
        <div v-if="item.operationCode == 7">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <cancle :cancleData="item"></cancle>
        </div>
        <!-- 已变更 -->
        <div v-if="item.operationCode == 10">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <changed :changedData="item" :workTypeCode="workTypeCode" :template="template"></changed>
        </div>
        <!-- 回复 -->
        <div v-if="item.operationCode == 13">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <Reply :replyData="item" :workTypeCode="workTypeCode"></Reply>
        </div>
        <!-- 到达 -->
        <div v-if="item.operationCode == 31">
          <div class="header">
            <div class="iconfont">&#xe681;</div>
            <div class="title-body">
              <span class="dot"></span>
              <span class="title">{{ item.operationType }}</span>
              <span class="time">{{ timeFunc(item.createDate, ".") }}</span>
            </div>
            <i class="arrows-down" :class="{ 'arrows-up': item.fold }" />
          </div>
          <arrival :persData="item"></arrival>
        </div>
      </div>

      <div class="height-placeholder" style="height: 10px"></div>
    </div>
    <!-- <div class="btns" v-if="(flowCode == 4 || flowCode == 15) && workTypeCode != 15 && isShow && staffInfo.leaguerType == 1">
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="handleReviseOder" v-if="workType != '2'">修改</button>
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="goToReply" v-else>回复</button>
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="goEvaluate" v-if="workType != '2' && isShowEvaluate">评价</button>
    </div>
    <div class="btns" v-if="(flowCode == 4 || flowCode == 15) && workTypeCode == 15 && isShow && staffInfo.leaguerType == 1">
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="goEvaluate" v-if="workType != '2' && isShowEvaluate">评价</button>
    </div> -->
    <div class="btns" v-if="(flowCode == 4 || flowCode == 15) && workTypeCode != 15 && isShow && staffInfo.leaguerType == 1 && workType != '2' && isShowEvaluate">
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="goEvaluate">评价</button>
    </div>
    <div class="btns" v-if="(flowCode == 4 || flowCode == 15) && workTypeCode == 15 && isShow && staffInfo.leaguerType == 1 && workType != '2' && isShowEvaluate">
      <button class="weui-btn weui-btn_mini weui-btn_primary btn" @click="goEvaluate">评价</button>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import BaseInfo from "../../components/BaseInfo";
import ChangeOrder from "../../../form/formDetails/components/ChangeOrderInfo"; //'./components/ChangeOrderInfo'
import ServiceSite from "../../components/Accepted";
import PersCom from "../../components/Dispatched";
import PendingDetails from "../../components/PendingDetails";
import EvaluationInfo from "../../components/EvaluationInfo";
import CancelInfo from "../../components/CancelInfo";
import retroversion from "../../components/retroversion";
import ReturnVisit from "../../components/ReturnVisit";
import Cancle from "../../components/CanclePage";
import Changed from "../../components/Changed";
import Person from "../../components/Dispatched";
import Global from "@/utils/Global.js";
import Reply from "@/pages/mine/components/Reply";
import Arrival from "@/pages/mine/components/arrival";
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  name: "WorkbenchDetails",
  mixins: [fontSizeMixin],
  components: {
    BaseInfo,
    ServiceSite,
    PendingDetails,
    EvaluationInfo,
    CancelInfo,
    ReturnVisit,
    Person,
    Cancle,
    Changed,
    ChangeOrder,
    Reply,
    retroversion,
    Arrival
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"]),
    isShowEvaluate() {
      let flag = false;
      if (this.workOrdeInfoDate[0].sourcesPhone) {
        if (this.workOrdeInfoDate[0].sourcesPhone == this.staffInfo.mobile && this.workOrdeInfoDate[0].isAcceptanceCheck != "1") {
          flag = true;
        }
      } else if (this.workOrdeInfoDate[0].createBy == this.staffInfo.staffId && this.workOrdeInfoDate[0].isAcceptanceCheck != "1") {
        flag = true;
      }
      return flag;
    },
    isLargeFont() {
      return localStorage.getItem('fontSizePreference') === 'x-large' || localStorage.getItem('fontSizePreference') === 'large';
    }
  },
  data() {
    return {
      workOrdeInfoDate: [],
      isShowSign: false,
      workTypeCode: "",
      isShowSign: false,
      workOrderId: "",
      workType: "1",
      flowCode: 0,
      template: "",
      isShow: true,
      isReviseOder: false
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.path == "/reviseOder") {
        vm.isReviseOder = true;
      }
    });
  },
  methods: {
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      }
      // else if (this.isReviseOder) {
      //   this.$router.go(-3);
      // }
      else {
        this.$router.go(-1);
      }
    },
    goEvaluate() {
      this.$router.push({
        path: "/evaluation",
        query: {
          taskId: this.workOrderId
        }
      });
    },
    /**
     *获取工单任务详情信息
     */
    getOderDetailsInfo(taskId) {
      this.$api
        .getTaskDetail({
          taskId: taskId
        })
        .then(this.getOderDetailsInfoSucc);
    },
    /**
     *工单修改
     */
    handleReviseOder() {
      sessionStorage.setItem("pageState", true);
      this.$router.push({
        path: "/reviseOder",
        query: {
          workTypeCode: this.workTypeCode,
          id: this.workOrderId,
          lastQuery: this.$route.query
        }
      });
    },
    /**
     * 回复
     */
    goToReply() {
      this.$router.push({
        path: "Reply",
        query: {
          source: "custom",
          id: this.$route.query.id
        }
      });
    },
    /**
     * 接收时间格式化函数
     */
    timeFunc() {},
    /**
     * 获取工单任务详情信息成功函数
     * @param res
     */
    getOderDetailsInfoSucc(res) {
      let newRes = res.map(item => {
        item.fold = false;
        return item;
      }); // 时间轴内容折叠/展开
      let baseInfo = newRes[0];
      this.flowCode = baseInfo.flowCode;
      this.workType = baseInfo.type;
      newRes.forEach(item => {
        if (item.operationCode == 10) {
          item.itemType = baseInfo.itemType;
          item.transportName = baseInfo.transportName;
          item.sourcesDeptName = baseInfo.sourcesDeptName;
          item.transportEndLocalOfficeName = baseInfo.transportEndLocalOfficeCode;
          item.transportEndLocalOffice = baseInfo.transportEndLocalOffice;
          item.transportEndLocal = baseInfo.transportEndLocal;
          item.transportStartLocal = baseInfo.transportStartLocal;
          item.transportStartLocalOffice = baseInfo.transportStartLocalOffice;
          item.transportName = baseInfo.transportName;
          item.customtransportNum = baseInfo.customtransportNum;
          item.questionDescription = baseInfo.questionDescription;
        }
      });
      this.workOrdeInfoDate = newRes;
      // 工单模版类型（3运送类；6综合类）
      this.template = this.workOrdeInfoDate[0].template;

      this.timeFunc = Global.timestampToTime;
      this.workTypeCode = this.workOrdeInfoDate[0].workTypeCode;
      this.workOrderId = this.workOrdeInfoDate[0].taskId;
    },
    /**
     * 折叠/展开时间轴
     * @param idx
     */
    handleFold(e, idx) {
      if (e.target.className == "header" || e.target.tagName == "I") {
        this.workOrdeInfoDate[idx].fold = !this.workOrdeInfoDate[idx].fold;
      }
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.taskId = this.$route.query.id;
    this.getOderDetailsInfo(this.taskId);
    if (this.$route.query.from == "qualityAnalysis") {
      this.isShow = false;
    }
  }
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/mixins.styl';
@import '~styles/varibles.styl';

.deskDetails-conteent{
  height :100vh;
}
.wrapper {
  height:calc(90% - 1.32rem)
  // height:100%;
  overflow : auto
  // margin-bottom: 10px;

  .item {
    .header {
      // height: 0.57rem;
      display: flex;
      align-items: center;
      margin: 0.15rem 0 0.15rem 0.3rem;
      background: #fff;
      position: relative;

      .iconfont {
        font-size: 0.4rem;
        color: $btnColor;
      }

      .title-body {
        max-width: 73%;
        height: calc(35px * var(--font-scale));
        display: flex;
        align-items: center;
        margin-left: 10px;
        background: #eceef8;
        padding: 0 0.24rem;
        border-radius: 0.3rem;
        // white-space: nowrap;

        .dot {
          display: inline-block;
          width: 0.09rem;
          height: 0.09rem;
          background: $btnColor;
        }

        .title {
          font-size: calc(15px * var(--font-scale))!important;
          font-weight: 700;
          margin: 0 0.45rem 0 0.08rem;
        }

        .time {
          font-size: calc(15px * var(--font-scale))!important;
          color: #4F87FB;
          max-width: 65%;
        }
      }
      .arrows-down {
        display: inline-block;
        width: 13px;
        height: 7px;
        background: url('~images/<EMAIL>');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100%;
        position: absolute;
        top: 50%;
        right: 20px;
        transform: translateY(-50%);
      }

      .arrows-up {
        background-image: url('~images/<EMAIL>');
      }
    }
  }
}

.btns {
  display:flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  z-index: 10;
  bottom: 0;
  background: #fff;
  height: 1.32rem;
  line-height: 1.32rem;
  text-align: center;
  border-top: 1px solid #e5e5e5;
  padding: 0 16px;
  box-sizing: border-box;

  button {
    width:100%;
    height: 0.88rem;
    font-size: 0.32rem;
    line-height: 0.88rem;
    margin: 0 4px;
  }
}
      .large-font .time {
        max-width: 50%!important;
      }
</style>
