<template>
  <div class="area-content">
    <Header :title="titleText" @backFun="goback"></Header>
    <div class="top-tools">
   <div class="dropdown-container">
       <van-dropdown-menu active-color="#3562DB" >
        <van-dropdown-item ref="filterOrderType" :title="newValue.length?selectValueOrder:'工单类型'">
          <div style="max-height: 350px; overflow-y: scroll">
            <van-checkbox-group v-model="newValue" direction="horizontal" style="margin-top: 10px"  @change="changeGroup">
              <van-cell v-for="(item, index) in TypeOption" :key="index">
                <van-checkbox :name="item.value" shape="square">{{ item.text }}
                </van-checkbox>
              </van-cell>
            </van-checkbox-group>
          </div>

            <div class="bottomButton">
              <van-button ref="regionResetBtn" style="width:40%;"   plain hairline type="info" size="small" @click="resetOrderType"
                class="btn-group-button">重置</van-button>
              <van-button  type="info" style="width:40%" size="small" @click="confirmOrderType" class="btn-group-button">确认</van-button>
            </div>

        </van-dropdown-item>
        <van-dropdown-item
          :title="dateType == 'week' ? '本周' : dateType == 'month' ? '本月' : dateType == 'all' ? '全部时间' : '自定义'"
          ref="filter_item">
          <div class="option-box">
            <div class="choice" @click="confirmDateType('all')">
              <span :class="{ 'active-date': dateType == 'all' }">全部时间</span>
              <i v-if="dateType == 'all'" class="van-icon van-icon-success van-dropdown-item__icon"
                style="color: rgb(53, 98, 219)">
                <!----></i>
            </div>
            <div class="choice" @click="confirmDateType('week')">
              <span :class="{ 'active-date': dateType == 'week' }">本周</span>
              <i v-if="dateType == 'week'" class="van-icon van-icon-success van-dropdown-item__icon"
                style="color: rgb(53, 98, 219)">
                <!----></i>
            </div>
            <div class="choice" @click="confirmDateType('month')">
              <span :class="{ 'active-date': dateType == 'month' }">本月</span>
              <i v-if="dateType == 'month'" class="van-icon van-icon-success van-dropdown-item__icon"
                style="color: rgb(53, 98, 219)">
                <!----></i>
            </div>
            <div class="choice" @click="handleDiy">
              <span :class="{ 'active-date': isDiy }">自定义</span>
              <i v-if="isDiy" class="van-icon van-icon-success van-dropdown-item__icon"
                style="color: rgb(53, 98, 219)"></i>
            </div>
          </div>
          <div v-if="isDiy" style="padding: 5px 16px">
            <div class="date-box">
              <div class="date-text" @click="switchDateType('start')">
                {{ startTime }}
              </div>
              <span class="date-line"></span>
              <div class="date-text" @click="switchDateType('end')">
                {{ endTime }}
              </div>
            </div>
            <van-datetime-picker v-if="currentDateType == 'start'" cancel-button-text=" " confirm-button-text=" "
              type="date" @change="dateFormatter" title="选择初始时间" />
            <van-datetime-picker v-else cancel-button-text=" " confirm-button-text=" " @change="dateFormatter"
              type="date" title="选择结束时间" />
            <van-button type="primary" color="#3562DB" block @click="onConfirm"> 确认 </van-button>
          </div>
        </van-dropdown-item>
        <van-dropdown-item v-model="currentStatusType" :options="statusTypeOption" @change="handleStatusChanged" />
      </van-dropdown-menu>
      <div class="dropdown-icon" @click="clickIcon">    <van-icon name="wap-nav" size="0.5rem" @click="classificationShow=true" /></div>

   </div>
   <!-- 更多搜素 -->
      <van-popup  style="height:36%" v-model="showPicker" round position="bottom" closeable ref="popupFooter">
       <div class="popup-title">
      <span style="font-weight:700">工单筛选</span>
    </div>
        <!-- 留出导航栏高度的间距 -->
        <div class="contentPofooter">
          <div class="serviceDepartment">服务部门</div>
          <input  @click="clickService" style="width:100%;font-size: 12px" v-model="selectedNames" readonly type="text" placeholder="请选择服务部门" />
          <van-divider />
            <div class="serviceDepartment">服务科室</div>
          <input @click="clickOfficetment" style="width:100%;font-size: 12px" v-model="selectedOfficeNames" readonly type="text" placeholder="请选择服务科室" />
        </div>
  <!-- 底部按钮 -->
    <div class="popup-footer">
      <van-button  style="width:45%"  plain hairline type="info" size="small" @click="resetPofooter">重置</van-button>
      <van-button style="width:45%" type="info" size="small" @click="confirmPofooter">确认</van-button>
    </div>
      </van-popup>
      <div class="tab-content" :class="$route.query.from == 'qualityAnalysis' ? 'tab-content1' : ''">
        <div class="tab-title">
          <div @click="judgeIsSame('')" :class="{ textStyle: curArea == '' }">
            <span class="num">{{ countObj.all > 99 ? "99+" : countObj.all }}</span>
            <span class="title">全部</span>
          </div>
          <template v-if="statusType != '2'">
            <div @click="judgeIsSame('2')" :class="{ textStyle: curArea == 2 }">
              <span class="num">{{ countObj.untreated > 99 ? "99+" : countObj.untreated }}</span>
              <span class="title">未派工</span>
            </div>
            <div @click="judgeIsSame('3')" :class="{ textStyle: curArea == 3 }">
              <span class="num">{{ countObj.dispatched > 99 ? "99+" : countObj.dispatched }}</span>
              <span class="title">已派工</span>
            </div>
            <div @click="judgeIsSame('4')" :class="{ textStyle: curArea == 4 }">
              <span class="num">{{ countObj.register > 99 ? "99+" : countObj.register }}</span>
              <span class="title">已挂单</span>
            </div>
          </template>
          <div @click="judgeIsSame('5')" :class="{ textStyle: curArea == 5 }" v-if="statusType != '1'">
            <span class="num">{{ countObj.completed > 99 ? "99+" : countObj.completed }}</span>
            <span class="title">已结束</span>
          </div>
        </div>
        <!-- <div class="line"></div> -->
      </div>
    </div>
    <div class="tools-replace"></div>
    <van-pull-refresh v-model="isLoading" @refresh="onRefresh" :disabled="refreshDisabled">
      <div class="select-area" :class="$route.query.from == 'qualityAnalysis' ? 'select-area1' : ''" v-show="isHaveData"
        ref="listBox">
        <ul class="region">
          <li class="border-bottom" v-for="item of dataArr" :key="item.taskId"
            @click="enterDetailInfo(item.hospitalCode, item.unitCode, item.flowcode, item.taskId)">
            <div class="middle" style="width: 100%">
              <div class="middel-text modify_middle">
                <div style="font-size: calc(16px * var(--font-scale))">
                  <van-radio v-if="$route.query.outbound" v-model="workOrderNumber" :name="item.workNum"
                    checked-color="#3562db">{{ item.workTypeName }}</van-radio>
                  <span v-else>{{ item.workTypeName }}</span>
                </div>
                <div class="right">
                  <span class="flowtype red bg-red" v-if="item.flowcode == '1'">{{ item.flowtype }}</span>
                  <span class="flowtype blue bg-green" v-if="item.flowcode == '2'">{{ item.flowtype }}</span>
                  <span class="flowtype green bg-green" v-if="item.flowcode == '3'">{{ item.flowtype }}</span>
                  <span class="flowtype yellow bg-green" v-if="item.flowcode == '4'">{{ item.flowtype }}</span>
                  <span class="flowtype yellow bg-green" v-if="item.flowcode == '5'">{{ item.flowtype }}</span>
                  <span class="flowtype yellow bg-green" v-if="item.flowcode == '15'">{{ item.flowtype }}</span>
                  <span class="iconfont">&#xe646;</span>
                </div>
              </div>
              <div class="time-text-content" style="padding: 8px 0; font-size: 14px">
                <img src="@/assets/images/ic-time.png" class="ic-time-img" />
                <span class="time-text" style="font-size: calc(14px * var(--font-scale))">{{ item.createDate }}</span>
              </div>
              <div class="time-text" style="display: flex">
                <div class="time-text-title">工&nbsp;&nbsp;单&nbsp;&nbsp;号：</div>
                <div class="department">
                  <span>{{ item.workNum }}</span>
                </div>
              </div>
              <div class="time-text" style="display: flex">
                <div class="time-text-title">所属科室：</div>
                <div class="department">
                  <span>{{ item.sourcesDeptName || '' }}</span>
                </div>
              </div>
              <div class="time-text" style="display: flex">
                <div class="time-text-title">服务部门：</div>
                <div class="department">
                  <span>{{ item.designateDeptName }}</span>
                </div>
              </div>
              <div class="time-text" style="display: flex">
                <div class="time-text-title">服务人员：</div>
                <div class="department">
                  <span>{{ item.designatePersonName }}</span>
                </div>
              </div>
              <div class="time-text" style="display: flex">
                <div class="time-text-title">服务事项：</div>
                <div class="department">
                  <span>{{ item.serviceItemShow }}</span>
                </div>
              </div>
              <div class="time-text" style="display: flex">
                <div class="time-text-title">服务地点：</div>
                <div class="department">
                  <span>{{ item.localtionName }}</span>
                </div>
              </div>
              <div class="time-text" style="display: flex">
                <div class="time-text-title">申报描述：</div>
                <div class="department">
                  <span>{{ item.questionDescription }}</span>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </van-pull-refresh>
    <div class="btn" v-if="$route.query.outbound">
      <van-button style="width: 90%" color="#3562db" @click="goApplicationForm">确定</van-button>
    </div>
    <div v-show="!isHaveData" class="img-content">
      <img src="@/assets/images/noDataDefault/search-empty.png" alt="" />
      <p>暂时没有工单信息~</p>
    </div>
  </div>
</template>

<script>
  import {
    mapState
  } from "vuex";
  import fontSizeMixin from "@/mixins/fontSizeMixin";
  import { Button as VanButton, Divider, Empty } from "vant";
  import YxInput from "../../../../common/workFlowForm/formComp/input.vue";
  import YxSelect from "../../../../common/workFlowForm/formComp/select.vue";
  import YxCheckbox from "../../../../common/workFlowForm/formComp/checkbox.vue";
  import YxDatetime from "../../../../common/workFlowForm/formComp/datetime.vue";
  import YxDatetimerange from "../../../../common/workFlowForm/formComp/datetimerange.vue";
  import YxTimerange from "../../../../common/workFlowForm/formComp/timerange.vue";
  import YxDaterange from "../../../../common/workFlowForm/formComp/daterange.vue";
  import YxUpload from "../../../../common/workFlowForm/formComp/upload.vue";
  export default {
    name: "MyWorkbench",
    mixins: [fontSizeMixin],
    computed: {
      ...mapState(["loginInfo", "staffInfo", "saveLeaguerType", "h5Mode"]),
      selectedItems() {
      return this.newValue.map(value => {
        const item = this.TypeOption.find(item => item.value === value);
        return {
          value: item.value,
          text: item.text
        };
      });
    }
    },
    components: {
      "van-divider": Divider,
    },
    data() {
      return {
        workOrderNumber: "",
        fromRoute: null,
        flag: false,
        isHaveData: true,
        curArea: "",
        type: "",
        curPage: 0,
        isDiff: false,
        dataArr: [],
        resNameData: [],
        resCodeData: [],
        leaguerType: 2,
        startTime: "",
        startTimeText: "选择初始时间",
        endTime: "",
        endTimeText: "选择终止时间",
        workTypeCode: "",
        orderParams: "",
        time: "",
        timeShow: false,
        timeShow2: false,
        titleText: "工单处理",
        currentStatusType: "",
        statusType: "",
        countObj: {
          completed: 0,
          dispatched: 0,
          register: 0,
          untreated: 0
        },
        statusTypeOption: [{
            text: "全部状态",
            value: ""
          },
          {
            text: "未完成",
            value: "1"
          },
          {
            text: "已结束",
            value: "2"
          }
        ],
        TypeOption: [
          // {
          //   text: "全部",
          //   value: ""
          // },
          {
            text: "工单报修",
            value: "1"
          },
          {
            text: "应急保洁",
            value: "2"
          },
          {
            text: "运送服务",
            value: "3"
          },
          {
            text: "订餐",
            value: "4"
          },
          {
            text: "后勤投诉",
            value: "5"
          },
          {
            text: "综合服务",
            value: "6"
          },
          {
            text: "医疗设备",
            value: "7"
          },
          {
            text: "巡检整改",
            value: "9"
          },
          {
            text: "巡检报修",
            value: "10"
          },
          {
            text: "随手拍",
            value: "11"
          },
          {
            text: "公车预定",
            value: "15"
          },
          {
            text: "确警",
            value: "16"
          },
          {
            text: "后勤设备",
            value: "17"
          },
          {
            text: "隐患上报",
            value: "18"
          }
        ],
        dateType: "all",
        isDiy: false,
        currentDateType: "start",
        isLoading: false,
        refreshDisabled: false,
        scrollHeight: 0,
        timer: "",
        newValue:[],
        selectValueOrder:"",
        showPicker:false,
        selectedNames:"",
        selectedIds:"",
        selectedOfficeNames:"",
        selectedOfficeIds:"",
      };
    },

    created() {
      if (this.$route.query.from && this.$route.query.from == "qualityAnalysis") {
        this.startTime = this.$route.query.startTime;
        this.endTime = this.$route.query.endTime;
      }
    },
      mounted() {
      let that = this;
      try {
        api.addEventListener({
            name: "keyback666"
          },
          function (ret, err) {
            that.goback();
          }
        );
      } catch (error) {}
      this.$refs.listBox.addEventListener("scroll", this.handleScroll);
      this.curPage = 0;
      this.dataArr = [];
      this.type = "";
      this.curArea = "";
      this.workTypeCode = "";
      this.leaguerType = "1";
      // this.timer = setInterval(() => {
      //   if (this.staffInfo.staffId) {
      //     this.getCount(this.type, this.workTypeCode);
      //     this.getOrderList(this.type, this.workTypeCode);
      //     clearInterval(this.timer);
      //   }
      // }, 100);
    },
    activated() {
      let that = this;
       that.newValue=[]
       that.workTypeCode = ''
      try {
        api.addEventListener({
            name: "keyback666"
          },
          function (ret, err) {
            that.goback();
          }
        );
      } catch (err) {}
      this.orderParams = this.$route.query;
      if (this.$route.query.from && this.$route.query.from == "qualityAnalysis") {
        this.startTime = this.$route.query.startTime;
        this.endTime = this.$route.query.endTime;
        if (this.$route.query.dateType == "day") {
          this.titleText = "本日工单";
        } else if (this.$route.query.dateType == "monts") {
          this.titleText = "本月工单";
        }
      }
      if(this.fromRoute.path == "/serviceDepartment"){
         this.selectedIds = this.$route.query.selectedIds && this.$route.query.selectedIds.length>0?this.$route.query.selectedIds.join(','):''
         this.selectedNames = this.orderParams.selectedNames && this.orderParams.selectedNames.length>0?this.orderParams.selectedNames.join(','):''
      } else if(this.fromRoute.path == "/serviceOfficetment"){
         this.selectedOfficeIds = this.$route.query.selectedOfficeIds && this.$route.query.selectedOfficeIds.length>0?this.$route.query.selectedOfficeIds.join(','):''
       this.selectedOfficeNames = this.orderParams.selectedOfficeNames && this.orderParams.selectedOfficeNames.length>0?this.orderParams.selectedOfficeNames.join(','):''
      }
      else if (this.fromRoute.path == "/completed" || this.fromRoute.path == "/vieform" || this.fromRoute.path ==
        "/deskDetails") {
        this.curPage = 0;
        this.timer = setInterval(() => {
          if (this.staffInfo.staffId) {
            this.getCount(this.type, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
            this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
            clearInterval(this.timer);
          }
        }, 50);
      } else {
        this.curArea = "";
        this.curPage = 0;
        this.timer = setInterval(() => {
          if (this.staffInfo.staffId) {
            this.getCount("", this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
            this.getOrderList("", this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
            clearInterval(this.timer);
          }
        }, 50);
      }

      // this.getCount("", this.workTypeCode);
      // this.getOrderList(this.type, this.workTypeCode);
      // this.curPage = 0;
      // this.dataArr = [];
      // this.type = "3";
      // this.curArea = "3";
      // this.workTypeCode = "";
      // this.leaguerType = "1";
      // this.getOrderList(this.type, this.workTypeCode);
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.fromRoute = from;
      });
    },
    watch: {
      scrollHeight(newVal) {
        if (newVal <= 0) {
          this.refreshDisabled = false;
        } else {
          this.refreshDisabled = true;
        }
      }
    },
    methods: {
      //工单类型筛选确定
      confirmOrderType(val){
        this.workTypeCode = this.newValue.join(',');
        this.curPage = 0;
        this.isDiff = true;
        this.dataArr = [];
        this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.getCount(this.type, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.$refs.filterOrderType.toggle();
      },
        //工单类型筛选重置
      resetOrderType(){
        this.workTypeCode = ''
        this.newValue = []
        this.curPage = 0;
        this.isDiff = true;
        this.dataArr = [];
        this.getOrderList(this.curArea);
        this.getCount(this.type);
       this.$refs.filterOrderType.toggle();
      },
       //工单类型复选框
      changeGroup(val){
        if(this.newValue.length){
          this.selectValueOrder = this.selectedItems[0].text
        }

      },
      //点击右侧图标
      clickIcon(){
        this.showPicker = true
      },
      //工单筛选确认、
      confirmPofooter(){
        this.curPage = 0;
        this.isDiff = true;
        this.dataArr = [];
        this.type = "";
        this.curArea = "";
        this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.getCount(this.type, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.showPicker = false
        // this.selectedNames = ''
        // this.selectedOfficeNames = ''
        // this.selectedIds = ''
        // this.selectedOfficeIds = ''
      },
        //工单筛选重置、
      resetPofooter(){
        this.selectedNames = ''
        this.selectedOfficeNames = ''
        this.selectedIds = ''
        this.selectedOfficeIds = ''
        this.curPage = 0;
        this.isDiff = true;
        this.dataArr = [];
        this.type = "";
        this.curArea = "";
        this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.getCount(this.type, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.showPicker = false
      },
      //点击选择服务部门
      clickService(){
      this.$router.push({
        path: "/serviceDepartment",
        query: {
        selectedIds: this.selectedIds.split(',')
        }
      });
      },
      //点击服务科室
     clickOfficetment(){
       this.$router.push({
        path: "/serviceOfficetment",
        query: {
         selectedOfficeIds: this.selectedOfficeIds.split(',')
        }
      });
     },
      goApplicationForm() {
        this.$router.push({
          path: "/applicationForm",
          query: {
            workOrderNumber: this.workOrderNumber
          }
        });
      },
      goback() {
        let isMy = this.$route.query.isMy;
        if (this.$route.query.outbound) {
          this.$router.push("/applicationForm");
        } else if (isMy) {
          // api.closeFrame();
          if (this.h5Mode == "apicloud") {
            try {
              api.sendEvent({
                name: "updateUserInfo"
              });
              this.$YBS.apiCloudCloseFrame();
            } catch (error) {
            }
          } else {
            window.parent.postMessage({
              type: "closeIframe"
            }, "*");
          }
        } else {
          this.$router.push("/orderIndex");
          // this.$router.go(-1);
        }
      },
      onRefresh() {
        this.curPage = 0;
        this.dataArr = [];
        this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.getCount("", this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
      },
      handleScroll() {
        let scrollTotalHeight = this.$refs.listBox.scrollHeight; // 滚动元素的总的高度
        let scrollHeight = this.$refs.listBox.scrollTop; // 盒子被卷去的高度
        this.scrollHeight = this.$refs.listBox.scrollTop;
        if (scrollHeight + this.$refs.listBox.clientHeight >= scrollTotalHeight - 10) {
          this.isDiff = false;
          this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        }
      },
      dateFormatter(picker) {
        let dateArr = picker.getValues();
        if (this.currentDateType == "start") {
          setTimeout(() => {
            this.startTime = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;
          }, 300);
        } else {
          this.endTime = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;
        }
      },
      switchDateType(type) {
        this.currentDateType = type;
      },
      handleDiy() {
        this.isDiy = true;
        this.dateType = "";
      },
      confirmDateType(type) {
        this.isDiy = false;
        this.dateType = type;
        this.curPage = 0;
        this.isDiff = true;
        this.dataArr = [];
        this.startTime = "";
        this.endTime = "";
        this.getCount(this.type, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.$refs.filter_item.toggle();
      },


      handleStatusChanged(value) {
        this.statusType = value;
        this.curPage = 0;
        this.isDiff = true;
        this.dataArr = [];
        this.type = "";
        this.curArea = "";
        this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.getCount(this.type, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
      },
      onConfirm() {
        this.$refs.filter_item.toggle();
        this.curPage = 0;
        this.dataArr = [];
        this.getCount(this.type, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
      },
      /**
       * 判断是否连续请求同一类型的工单列表
       * @param type 工单类型
       */
      judgeIsSame(type) {
        if (this.curArea != type) {
          //进行切换
          this.curPage = 0;
          this.isDiff = true;
          this.dataArr = [];
          this.curArea = type;
          this.type = type;
          this.getOrderList(type, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
          this.getCount("", this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        } else {
          this.isDiff = false;
        }
      },
      /**
       * 发送工单列表请求
       */
      getOrderList(type, workTypeCode,queryTeamId,queryDeptId) {
        $.showLoading();
        let lastPage = this.curPage;
        this.curPage = ++lastPage;
        let params = {
          hospitalCode: this.loginInfo.hospitalCode,
          unitCode: this.loginInfo.unitCode,
          staffId: this.staffInfo.staffId,
          // staffType: this.loginInfo.type,
          staffType: this.staffInfo.teamId ? 2 : 1,
          type: type,
          curPage: this.curPage,
          pageSize: 20,
          startTime: this.startTime,
          endTime: this.endTime,
          workTypeCode: workTypeCode,
          leaguerType: this.leaguerType,
          dateType: this.dateType,
          statusType: this.statusType,
          queryTeamId:queryTeamId,
          queryDeptId:queryDeptId,
        };
        if (type == 2) {
          params.designateDeptCode = this.staffInfo.teamId;
        } else {
          params.userId = this.staffInfo.staffId;
        }
        // 如果this.dateType是all,不传dateType
        if (this.dateType == "all") {
          delete params.dateType;
        }
        // 如果this.statusType是空,不传statusType
        if (this.statusType == "") {
          delete params.statusType;
        }
        this.axios
          .get(__PATH.ONESTOP + "/appOlgTaskManagement.do?getMineTaskPage", {
            params,
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          })
          .then(this.getOrderListSucc);
      },
      getCount(type, workTypeCode,queryTeamId,queryDeptId) {
        let params = {
          hospitalCode: this.loginInfo.hospitalCode,
          unitCode: this.loginInfo.unitCode,
          staffId: this.staffInfo.staffId,
          // staffType: this.loginInfo.type,
          staffType: this.staffInfo.teamId ? 2 : 1,
          type: "",
          curPage: this.curPage,
          pageSize: 20,
          startTime: this.startTime,
          endTime: this.endTime,
          workTypeCode: workTypeCode,
          leaguerType: this.leaguerType,
          dateType: this.dateType,
          statusType: this.statusType,
          queryTeamId:queryTeamId,
          queryDeptId:queryDeptId,
        };
        if (type == 2) {
          params.designateDeptCode = this.staffInfo.teamId;
        } else {
          params.userId = this.staffInfo.staffId;
        }
        // 如果this.dateType是all,不传dateType
        if (this.dateType == "all") {
          delete params.dateType;
        }
        // 如果this.statusType是空,不传statusType
        if (this.statusType == "") {
          delete params.statusType;
        }
        this.axios
          .get(__PATH.ONESTOP + "/appOlgTaskManagement.do?getTaskCountMineTaskPage", {
            params,
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token")
            }
          })
          .then(res => {
            if (res.data.code == 200) {
              this.countObj = res.data.data;
            }
          });
      },
      /**
       * 工单列表请求成功函数
       */
      getOrderListSucc(res) {
        this.isLoading = false;
        if (res.data.code == 200) {
          if (res.data.details.length == 0) {
            $.hideLoading();
            //第一次请求或者由时间查询结构
            if (this.dataArr.length == 0 || this.curPage == 1) {
              this.dataArr = [];
              this.isHaveData = false;
            }
            // $.toast("没有更多的数据啦", "text");
          } else {
            this.isHaveData = true;
            if (res.data.code == 200) {
              $.hideLoading();
              if (this.isDiff || this.curPage == 1) {
                this.dataArr = res.data.details;
              } else {
                this.dataArr = this.dataArr.concat(res.data.details);
              }
            } else {
              $.toast(res.data.message, "text");
            }
          }
          this.flag = true;
        } else {
          $.toast(res.data.message, "text");
        }
      },
      /**
       * 进入工单详情
       */
      enterDetailInfo(hospitalCode, unitCode, code, id) {
        if (this.$route.query.outbound) {
          return;
        } else {
          //未处理
          if (code == 1) {
            this.$router.push({
              path: "/vieform",
              query: {
                source: "myWorkbench",
                interfaceNum: 0,
                id: id,
                flowcode: code,
                from: this.$route.query.from || ""
              }
            });
          }
          //已派工
          if (code == 2) {
            this.$router.push({
              path: "/completed",
              query: {
                source: "myWorkbench",
                interfaceNum: 0,
                id: id,
                flowcode: code,
                isMy: this.$route.query.isMy,
                from: this.$route.query.from || ""
              }
            });
          }
          //已挂单
          if (code == 3) {
            this.$router.push({
              path: "/completed",
              query: {
                source: "myWorkbench",
                interfaceNum: 0,
                id: id,
                hanged: true,
                flowcode: code,
                from: this.$route.query.from || ""
              }
            });
          }
          //已结束
          if (code == 4 || code == 5 || code == 15) {
            this.$router.push({
              path: "/deskDetails",
              query: {
                id: id,
                flowcode: code,
                from: this.$route.query.from || ""
              }
            });
          }
        }
      },
      /**
       * 上拉加载更多
       */
      refresh(loaded) {
        this.isDiff = false;
        this.getOrderList(this.curArea, this.workTypeCode,this.selectedIds,this.selectedOfficeIds);
        if (this.flag) {
          loaded("done");
          this.flag = false;
        }
      },
      /**
       * 进入时间筛选
       */
      selectTime() {
        this.$router.push("/time");
      },
      /**
       * 将-的时间格式化成年、月、日形式
       * @param time
       */
      formatTime(time) {
        let timeStr = "";
        if (time.includes("/")) {
          timeStr = time.split("/");
        } else {
          timeStr = time.split("-");
        }
        return timeStr[0] + "年" + timeStr[1] + "月" + timeStr[2] + "日";
      }
    },

  };

</script>
<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl'
@import '~styles/mixins.styl'
>>> .van-overlay
  z-index 999 !important
.vue-pull-to-wrapper
  width 100%
.area-content >>> .action-block
  display none
.area-content
  display block
  position absolute
  background #F2F4F9
  width 100%
  height 100%
  overflow hidden
  &:after
    display block
    content '.'
    height 0
    line-height 0
    clear both
    visibility hidden
  .time-content
    font-size 0.34rem
    height 0.98rem
    background-color #fff
    padding 0 0.32rem
    display flex
    align-items center
    position fixed
    width 100%
    box-sizing border-box
    z-index 2
    marginBottom20()
    .time-text
      display inline-block
      width 2.5em
      border-right 1px solid #888
      margin-right 0.5em
    .time-wrapper
      flex 1
      display flex
      justify-content space-between
      white-space nowrap
  .tab-content
    background-color #fff
    height 10vh
    border-top 1px solid #e5e6eb
    .line
      height 0.2rem
      background-color $bgColor
    .tab-title
      height 95%
      display flex
      font-size 0.3rem
      background-color #fff
      color #999
      div
        flex 1
        display flex
        flex-direction column
        align-items center
        justify-content flex-end
        line-height 100%
        .title
          font-size calc(15px * var(--font-scale))
          padding 0.17rem 0 0.23rem 0
          color #86909C
          padding-top 0.3rem
        .num
          font-size calc(20px * var(--font-scale))
          color #1D2129
      .disClick
        pointer-events none
      .textStyle
        background linear-gradient(180deg, #FFFFFF 27%, #F0F5FF 100%)
      .tabItemActive
        position absolute
        width 0.7rem
        height 3px
        background $btnColor
  .tab-content1
    top 10vh
  .select-area
    width 100%
    height 70vh
    box-sizing border-box
    padding 0 16px
    background transparent
    display flex
    overflow auto
    .region
      background-color #F2F4F9
      width 100%
      height auto
      li
        background-color #fff
        min-height 12vh
        display flex
        align-items center
        font-size calc(15px * var(--font-scale))
        padding 16px 16px
        padding-left 10px
        border-radius 8px
        overflow hidden
        .left
          .ic-style-img
            width 0.68rem
        .middle
          flex 1
          // padding-left: .38rem
          padding-left 0.2rem
          .middel-text
            // padding: .1rem 0
          .time-text-content
            color #c2c2c2
            padding 0.1rem 0
            .ic-time-img
              width 0.22rem
              vertical-align middle
            .time-text
              vertical-align middle
        .right
          .iconfont
            color #e5e5e5
          .red
            color #FF0000
          .blue
            color #67a1ff
          .green
            color #87D0C0
          .yellow
            color #ffa545
          .gray
            color #C2C2C2
  .select-area1
    top calc(10vh + 1.08rem)
    height calc(90vh - 1.08rem)
  .img-content
    height 75%
    display flex
    flex-direction column
    justify-content center
    align-items center
    background-color #fff
    img
      width 3rem
    p
      color #4E5969
      font-size 0.3rem
      // margin: 10px
.flowtype
  color $color
.time-reset
  color #666
  font-size 100%
</style>
<style lang="scss" scoped>
.top-tools {
  position: fixed;
  width: 100%;
  height: 17.5vh;
  z-index: 99999;
}
.tools-replace {
  height: 17.5vh;
}
/deep/ .van-dropdown-menu__bar {
  box-shadow: none;
}
.textStyle .num {
  color: #3562db !important;
}
.textStyle .title {
  color: #3562db !important;
}
.region {
  border-radius: 8px;
}
.border-bottom {
  margin-bottom: 10px;
  position: relative;
}
.time-text {
  margin-bottom: 8px;
}
.department {
  span {
    display: inline-block;
    width: 100%;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    line-height: 1.2;

    /* 控制最大显示行数（例如最多显示2行） */
    display: -webkit-box;
    -webkit-line-clamp: 5; /* 显示的行数 */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
.modify_middle {
  display: flex;
  justify-content: space-between;
}
.tranform_style {
  transform: translateY(6px);
  word-break: break-all;
  line-height: 20px;
}
.choice {
  display: flex;
  justify-content: space-between;
  height: 50px;
  align-items: center;
}
.option-box {
  padding: 0 16px;
}
/deep/ .van-button__text {
  font-size: 14px;
}
.active-date {
  color: #3562db;
}
.date-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.date-box .date-text {
  background-color: #f2f3f5;
  padding: 8px 16px;
  border-radius: 3px;
  width: 30vw;
  height: 20px;
  text-align: center;
  line-height: 20px;
}
.date-line {
  width: 10px;
  height: 2px;
  background-color: #c9cdd4;
  margin: 0 6px;
}
.van-datetime-picker {
  margin-bottom: 12px;
}
.bg-green {
  background-color: #e8ffea !important;
  color: #00b42a !important;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: calc(12px * var(--font-scale));
}
.bg-red {
  background-color: #ffece8 !important;
  color: #f53f3f !important;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: calc(12px * var(--font-scale));
}
/deep/ .van-dropdown-item__content {
  max-height: 95%;
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 5px;
}
.time-text-title {
  white-space: nowrap;
}
/deep/ .van-dropdown-menu__title {
  font-size: calc(15px * var(--font-scale));
}
/deep/ .top-tools .van-cell , .top-tools .choice{
  font-size: calc(14px * var(--font-scale));
}
.bottomButton {
  margin: 0.2rem 0;
  display: flex;
  justify-content: space-around;
}
.dropdown-container {
  position: relative; /* 为绝对定位的图标提供参考 */
  // display: flex;
  // align-items: center;
}

.dropdown-icon {
  position: absolute;
  right: 2%; /* 紧贴容器右侧 */
  top: 50%;
  transform: translateY(-50%);
  // width: 20px; /* 仅占图标本身宽度 */
  // height: 20px;
  cursor: pointer;
}
/deep/  .van-dropdown-menu__bar {
  width: 90%;
}
/deep/ .van-dropdown-menu {
  background-color: #fff;
}
.popup-title {
  padding: .4rem;
  text-align: center;
}
.popup-footer {
  display: flex;
  justify-content: space-around;
  padding: 10px 16px;
  //border-top: 1px solid #eee;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.contentPofooter {
padding-left: .3rem;
.serviceDepartment {
  margin: 0.4rem 0;
  font-weight: 600;
}
}

</style>
