<template>
  <div class="matters-wrapper">
    <Header title="选择服务部门" @backFun="goback"></Header>
    <van-search v-model="teamName" show-action placeholder="请输入搜索关键词" @clear="onClear" @search="onSearch">
      <template #action>
        <div @click="onSearch">搜索</div>
      </template>
    </van-search>
    <div class="contentClass">
      <van-checkbox-group v-model="newValue" direction="horizontal" style="margin-top: 10px">
        <van-cell v-for="(item, index) in TypeOption" :key="index">
          <van-checkbox :name="item.id" shape="square">{{ item.team_name }}
          </van-checkbox>
        </van-cell>
      </van-checkbox-group>
    </div>
    <div class="bottomButton">
      <van-button ref="regionResetBtn" style="width:40%;margin-top:0.4rem" plain hairline type="info" size="small"
        @click="resetOrderType" class="btn-group-button">重置</van-button>
      <van-button type="info" style="width:40%;margin-top:0.4rem" size="small" @click="confirmOrderType"
        class="btn-group-button">确认
      </van-button>
    </div>
  </div>
</template>
<script type=text/ecmascript-6>
  import fontSizeMixin from "@/mixins/fontSizeMixin";
  export default {
    name: "ServiceMatters",
    mixins: [fontSizeMixin],
   computed: {
    // 实时计算选中的项
    selectedItems() {
      return this.newValue.length>0? this.TypeOption.filter(item =>
        this.newValue.includes(item.id)
      ):[];
    },
    selectedIds() {
      return this.selectedItems.map(item => item.id);
    },
    selectedNames() {
      return this.selectedItems.map(item => item.team_name);
    }
  },
    data() {
      return {
        newValue: [],
        TypeOption: [],
        localInfo: {},
        teamName: ""
      };
    },
    created() {
      this.localInfo = JSON.parse(localStorage.getItem("loginInfo"));
    },
    mounted() {

      this.getList()
    },
    activated() {
      this.$YBS.apiCloudEventKeyBack(this.goback);
      this.newValue = this.$route.query.selectedIds
      this.getList()
    },
    watch: {},
    methods: {
      //关键字搜索清空
      onClear() {
        this.getList()
      },
      //关键字搜索
      onSearch() {
        this.getList()
      },
      //重置
      resetOrderType() {
        this.newValue = []
      },

      //确定
      confirmOrderType() {
        if (this.newValue.length == 0) {
          this.$toast.fail('请选择内容')
          return
        } else {
          this.$router.push({
            path: "/workbench",
            query: {
            selectedIds:this.selectedIds,
            selectedNames:this.selectedNames,
            }
          });
        }
        this.newValue = []
      },
      //服务部门数据
      getList() {
        this.$api.getTeamsByTaskAll({
          unitCode: this.localInfo.unitCode,
          hospitalCode: this.localInfo.hospitalCode,
          teamName: this.teamName
        }).then((res) => {
          this.TypeOption = res.list
        })
      },
      goback() {
       // 使用 replace 方法返回上一页，并传递没有参数的路径
      this.$router.replace({
        path: "/workbench",
        query: {}
      });
      },
    },
  };

</script>
<style rel="stylesheet/stylus" lang="stylus" scoped>
  @import '~styles/varibles.styl';
  @import '~styles/mixins.styl';

  .sort_list {
    padding-right: 45px;
  }

  .matters-wrapper {
    height: 100vh;
    overflow-y: hidden;
    background-color: #F2F4F9;

    .header {
      #gosearch {
        height: 0.7rem;
        background-color: #fff;
        padding: 0.2rem;

        .ipt-area {
          height: 0.78rem;
          background-color: #F2F3F5;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $contentColor;

          .iconfont {
            padding-right: 7px;
          }
        }
      }
    }

    .bottomButton {
      margin-top: 0.3rem;
      display: flex;
      justify-content: space-around;
      background-color: #fff;
      height: 10%
    }

    .contentClass {
      height: 68%;
      overflow-y: scroll;
      background-color: #fff;
      margin-top: 0.3rem
    }
  }

</style>
