<template>
  <div class="page-container">
    <Header title="关联出库单" @backFun="goBack"></Header>

    <!-- 筛选区域 -->
    <div class="filter-area">
      <van-dropdown-menu active-color="#3562DB">
        <van-dropdown-item v-model="outboundType" :options="outboundTypeOptions" @change="handleFilterChange" />
        <van-dropdown-item v-model="warehouseId" :options="warehouseOptions" @change="handleFilterChange" />
        <!-- 把原有的简单日期下拉框替换为更复杂的日期选择组件 -->
        <van-dropdown-item
          :title="dateType == 'week' ? '本周' : dateType == 'month' ? '本月' : dateType == 'all' ? '全部时间' : '自定义'"
          ref="filter_item">
          <div class="option-box">
            <div class="choice" @click="confirmDateType('all')">
              <span :class="{ 'active-date': dateType == 'all' }">全部时间</span>
              <i v-if="dateType == 'all'" class="van-icon van-icon-success van-dropdown-item__icon"
                style="color: rgb(53, 98, 219)">
                <!----></i>
            </div>
            <div class="choice" @click="confirmDateType('week')">
              <span :class="{ 'active-date': dateType == 'week' }">本周</span>
              <i v-if="dateType == 'week'" class="van-icon van-icon-success van-dropdown-item__icon"
                style="color: rgb(53, 98, 219)">
                <!----></i>
            </div>
            <div class="choice" @click="confirmDateType('month')">
              <span :class="{ 'active-date': dateType == 'month' }">本月</span>
              <i v-if="dateType == 'month'" class="van-icon van-icon-success van-dropdown-item__icon"
                style="color: rgb(53, 98, 219)">
                <!----></i>
            </div>
            <div class="choice" @click="handleDiy">
              <span :class="{ 'active-date': isDiy }">自定义</span>
              <i v-if="isDiy" class="van-icon van-icon-success van-dropdown-item__icon"
                style="color: rgb(53, 98, 219)"></i>
            </div>
          </div>
          <div v-if="isDiy" style="padding: 5px 16px">
            <div class="date-box">
              <div class="date-text" @click="switchDateType('start')">
                {{ startTime }}
              </div>
              <span class="date-line"></span>
              <div class="date-text" @click="switchDateType('end')">
                {{ endTime }}
              </div>
            </div>
            <van-datetime-picker v-if="currentDateType == 'start'" cancel-button-text=" " confirm-button-text=" "
              type="date" @change="dateFormatter" title="选择初始时间" />
            <van-datetime-picker v-else cancel-button-text=" " confirm-button-text=" " @change="dateFormatter"
              type="date" title="选择结束时间" />
            <van-button type="primary" color="#3562DB" block @click="onConfirm"> 确认 </van-button>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
    </div>

    <!-- 列表区域 -->
    <div class="list-area">
      <van-pull-refresh v-model="isRefreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="list-item" v-for="(item, index) in relatedExportList" :key="index">
            <div class="radio-cell">
              <van-radio v-model="selectedExport" :name="item.recordNumber" checked-color="#3562db" />
            </div>
            <div class="content-cell" @click="viewExportDetail(item)">
              <div class="info-row">
                <span class="label">出库单号：</span>
                <span class="value">{{ item.recordNumber }}</span>
              </div>
              <div class="info-row">
                <span class="label">出库仓库：</span>
                <span class="value">{{ item.warehouseName }}</span>
              </div>
              <div class="info-row">
                <span class="label">出库类型：</span>
                <span class="value">{{ item.outwarehouseTypeName }}</span>
              </div>
              <div class="info-row">
                <span class="label">出库日期：</span>
                <span class="value">{{ item.outwarehouseDate }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>

      <!-- 空状态 -->
      <div class="empty-state" v-if="relatedExportList.length === 0 && !loading">
        <img src="../../assets/images/noDataDefault/content-error.png" class="empty-img" />
        <p>暂无相关出库单</p>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btn">
      <van-button type="primary" color="#3562db" block @click="confirmSelection">确定</van-button>
    </div>
  </div>
</template>

<script>
import fontSizeMixin from "@/mixins/fontSizeMixin";
import { mapState } from "vuex";

export default {
  mixins: [fontSizeMixin],
  data() {
    return {
      selectedExport: "",
      outboundType: "",
      warehouseId: "",
      dateType: "all",
      loading: false,
      finished: false,
      isRefreshing: false,
      currentPage: 1,
      pageSize: 10,
      relatedExportList: [],
      workOrderNumber: "",

      outboundTypeOptions: [],
      warehouseOptions: [],
      dateOptions: [
        { text: "全部时间", value: "all" },
        { text: "本周", value: "week" },
        { text: "本月", value: "month" }
      ],
      isDiy: false,
      currentDateType: 'start', // 'start' or 'end'
      startTime: '',
      endTime: '',
      datePicker: null,
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  created() {
    this.workOrderNumber = this.$route.query.workNum || "";
    this.getOutboundType();
    this.getWarehouseList();
  },
  activated() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    getOutboundType() {
      this.$api
        .getDictByPage({
          pageSize: 99999,
          currentPage: 1,
          userType: 1,
          userId: this.loginInfo.staffId,
          userName: this.loginInfo.staffName,
          dictionaryCategoryId: "outbound_type"
        })
        .then(res => {
          this.outboundTypeOptions = res.map(item => ({
            text: item.dictionaryDetailsName,
            value: item.dictionaryDetailsId
          }));
          this.outboundTypeOptions.unshift({ text: "出库类型", value: "" });
        });
    },
    getWarehouseList() {
      this.$api
        .getWarehouseList({
          pageSize: 99999,
          CurrentPage: 1
        })
        .then(res => {
          this.warehouseOptions = res.list.map(item => ({
            text: item.warehouseName,
            value: item.id
          }));
          this.warehouseOptions.unshift({ text: "出库仓库", value: "" });
        });
    },
    handleFilterChange() {
      // 筛选条件变化时，重置数据并重新加载
      this.currentPage = 1;
      this.relatedExportList = [];
      this.finished = false;
      this.fetchData(false);
    },

    onRefresh() {
      this.currentPage = 1;
      this.relatedExportList = [];
      this.finished = false;
      this.fetchData(false);
      this.isRefreshing = false;
    },

    onLoad() {
      this.currentPage++;
      this.fetchData(true);
    },

    fetchData(isLoadMore) {
      this.loading = true;

      // isLoadMore参数现在由调用方明确传递，不再需要默认值和类型转换
      
      // 处理日期范围
      let outwarehouseDateBeginDate = "";
      let outwarehouseDateEndDate = "";
      
      if (this.dateType === "custom") {
        outwarehouseDateBeginDate = this.startTime;
        outwarehouseDateEndDate = this.endTime;
      } else if (this.dateType === "week") {
        // 计算本周的开始日期和结束日期
        const now = new Date();
        const dayOfWeek = now.getDay() || 7; // 如果是0（周日）则设为7
        const mondayDate = new Date(now);
        mondayDate.setDate(now.getDate() - dayOfWeek + 1); // 本周一
        const sundayDate = new Date(now);
        sundayDate.setDate(now.getDate() + (7 - dayOfWeek)); // 本周日
        
        outwarehouseDateBeginDate = this.formatDate(mondayDate);
        outwarehouseDateEndDate = this.formatDate(sundayDate);
      } else if (this.dateType === "month") {
        // 计算本月的开始日期和结束日期
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        outwarehouseDateBeginDate = this.formatDate(firstDay);
        outwarehouseDateEndDate = this.formatDate(lastDay);
      }

      this.$api
        .getOutboundList({
          createCode: this.loginInfo.staffId,
          pageSize: this.pageSize,
          currentPage: this.currentPage,
          userId: this.loginInfo.staffId,
          userName: this.loginInfo.staffName,
          workNum: this.workOrderNumber,
          outboundType: this.outboundType,
          warehouseId: this.warehouseId,
          outwarehouseDateBeginDate: outwarehouseDateBeginDate,
          outwarehouseDateEndDate: outwarehouseDateEndDate
        })
        .then(res => {
          this.loading = false;
          console.log("获取出库单数据:", res);

          const list = res.list || [];

          if (isLoadMore) {
            this.relatedExportList = [...this.relatedExportList, ...list];
          } else {
            this.relatedExportList = list;
          }

          // 判断是否已加载完所有数据
          this.finished = list.length < this.pageSize;
        })
        .catch(() => {
          this.loading = false;
          this.finished = true;
        });
    },

    confirmSelection() {
      if (!this.selectedExport) {
        this.$toast.fail("请选择出库单");
        return;
      }

      // 找到选中的出库单对象
      const selectedItem = this.relatedExportList.find(item => item.id === this.selectedExport);

      this.$router.replace({
        path: "/applicationForm",
        query: {
          ...this.$route.query,
          relatedExport: this.selectedExport
        }
      });
    },
    handleDiy() {
      this.isDiy = true;
      this.dateType = "";
      this.currentDateType = 'start';
      this.startTime = '';
      this.endTime = '';
      // 不再关闭下拉菜单
    },
    switchDateType(type) {
      this.currentDateType = type;
      // 不再关闭下拉菜单
    },
    dateFormatter(picker) {
      let dateArr = picker.getValues();
      if (this.currentDateType == "start") {
        setTimeout(() => {
          this.startTime = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;
        }, 300);
      } else {
        this.endTime = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;
      }
    },
    onConfirm() {
      if (!this.startTime) {
        this.$toast('请选择开始时间');
        return;
      }
      if (!this.endTime) {
        this.$toast('请选择结束时间');
        return;
      }
      this.dateType = 'custom';
      this.$refs.filter_item.toggle();
      this.fetchData(false); // 重新加载数据
    },
    confirmDateType(type) {
      this.dateType = type;
      this.isDiy = false;
      this.$refs.filter_item.toggle();
      this.fetchData(false); // 重新加载数据
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    viewExportDetail(item) {
      this.$router.push({
        path: '/applicationForm/exportDetail',
        query: {
          id: item.id,
          recordNumber: item.recordNumber,
          workNum: this.workOrderNumber
        }
      });
    }
  },
  mounted() {
    this.fetchData();
  }
};
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f2f4f9;
}

.filter-area {
  position: fixed;
  top: 84px;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #fff;
}

.list-area {
  flex: 1;
  margin-top: 60px;
  margin-bottom: 60px;
  overflow-y: auto;
  padding: 0 16px;
}

.list-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 16px;
}

.radio-cell {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.content-cell {
  flex: 1;
  position: relative;
}

.content-cell::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-top: 2px solid #999;
  border-right: 2px solid #999;
  transform: translateY(-50%) rotate(45deg);
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  line-height: 1.4;
}

.label {
  white-space: nowrap;
  color: #666;
  font-size: calc(14px * var(--font-scale));
}

.value {
  flex: 1;
  word-break: break-all;
  font-size: calc(14px * var(--font-scale));
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 80px;

  .empty-img {
    width: 120px;
    height: 120px;
    margin-bottom: 12px;
  }

  p {
    color: #999;
    font-size: calc(14px * var(--font-scale));
  }
}

.bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 16px;
  background-color: #fff;
  z-index: 11;
}

.large-font {
  .label,
  .value {
    font-size: calc(16px * var(--font-scale));
  }
}

.option-box {
  padding: 0 16px;
}

.choice {
  display: flex;
  justify-content: space-between;
  height: 50px;
  align-items: center;
}

.active-date {
  color: #3562db;
}

.date-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-box .date-text {
  background-color: #f2f3f5;
  padding: 8px 16px;
  border-radius: 3px;
  width: 30vw;
  height: 20px;
  text-align: center;
  line-height: 20px;
}

.date-line {
  width: 10px;
  height: 2px;
  background-color: #c9cdd4;
  margin: 0 6px;
}

.van-datetime-picker {
  margin-bottom: 12px;
}
</style>
