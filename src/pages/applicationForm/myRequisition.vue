<template>
  <div class="inner">
    <Header title="我的领用" @backFun="goBack"></Header>
    <div class="top-tools">
      <van-dropdown-menu active-color="#3562DB">
        <van-dropdown-item :title="dateType == 'week' ? '本周' : dateType == 'month' ? '本月' : dateType == 'all' ? '全部时间' : '自定义'" ref="filter_item">
          <div class="option-box">
            <div class="choice" @click="confirmDateType('all')">
              <span :class="{ 'active-date': dateType == 'all' }">全部时间</span>
              <i v-if="dateType == 'all'" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmDateType('week')">
              <span :class="{ 'active-date': dateType == 'week' }">本周</span>
              <i v-if="dateType == 'week'" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmDateType('month')">
              <span :class="{ 'active-date': dateType == 'month' }">本月</span>
              <i v-if="dateType == 'month'" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="handleDiy">
              <span :class="{ 'active-date': isDiy }">自定义</span>
              <i v-if="isDiy" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"></i>
            </div>
          </div>
          <div v-if="isDiy" style="padding: 5px 16px">
            <div class="date-box">
              <div class="date-text" @click="switchDateType('start')">
                {{ startTime }}
              </div>
              <span class="date-line"></span>
              <div class="date-text" @click="switchDateType('end')">
                {{ endTime }}
              </div>
            </div>
            <van-datetime-picker
              v-if="currentDateType == 'start'"
              v-model="currentDate"
              cancel-button-text=" "
              confirm-button-text=" "
              type="date"
              @change="dateFormatter"
              title="选择初始时间"
            />
            <van-datetime-picker v-else v-model="currentDate" cancel-button-text=" " confirm-button-text=" " @change="dateFormatter" type="date" title="选择结束时间" />
            <van-button type="primary" color="#3562DB" block @click="onConfirm"> 确认 </van-button>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
      <van-search
        v-model="name"
        background="#fff"
        clearable
        :show-action="true"
        placeholder="搜索物料名称或者出库单号"
        @search="getRequisitionList"
        @clear="searchCancel"
        @input="appMaterialInput"
      >
      </van-search>
    </div>
    <div class="tools-replace"></div>
    <div v-if="requisitionList.length > 0">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width: 100%">
            <div v-for="item in requisitionList" :key="item.id" class="listStyle">
              <div class="txt">
                <div class="txt_l">出库单号</div>
                <div class="txt_r">{{ item.depotNumber }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">出库类型</div>
                <div class="txt_r">{{ item.type }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">关联工单</div>
                <div class="txt_r">{{ item.workNum }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">领用申请时间</div>
                <div class="txt_r">{{ item.createDate }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">物料名称</div>
                <div class="txt_r">{{ item.depProductName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">物料分类</div>
                <div class="txt_r">{{ item.type }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">规格</div>
                <div class="txt_r">{{ item.specification }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">工单类型</div>
                <div class="txt_r">{{ item.workTypeName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">数量</div>
                <div class="txt_r">{{ item.changeNum }}</div>
              </div>
              <!-- <div class="txt">
                <div class="txt_l">单价</div>
                <div class="txt_r">{{ item.price }}</div>
              </div> -->
              <div class="txt">
                <div class="txt_l">单位</div>
                <div class="txt_r">{{ item.unit }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">备注</div>
                <div class="txt_r">{{ item.remark }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  data() {
    return {
      currentDate: new Date(),
      isDiy: false,
      currentDateType: "start",
      dateType: "all",
      startTime: "",
      endTime: "",
      name: "",
      pageNo: 1,
      pageSize: 10,
      total: 0,
      requisitionList: [],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getList();
  },
  methods: {
    onConfirm() {
      this.$refs.filter_item.toggle();
      let date1 = new Date(this.startTime);
      let date2 = new Date(this.endTime);
      if (date1 > date2) return this.$toast.fail("开始时间不能大于结束时间");
      this.pageNo = 1;
      this.requisitionList = [];
      this.getList();
    },
    switchDateType(type) {
      this.currentDateType = type;
    },
    handleDiy() {
      this.isDiy = true;
      this.dateType = "";
    },
    dateFormatter(picker) {
      let dateArr = picker.getValues();
      if (this.currentDateType == "start") {
        setTimeout(() => {
          this.startTime = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;
        }, 300);
      } else {
        this.endTime = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;
      }
    },
    confirmDateType(type) {
      this.isDiy = false;
      this.dateType = type;
      this.startTime = "";
      this.endTime = "";
      if (type == "all") {
        this.tartTime = "";
        this.endTime = "";
        this.pageNo = 1;
        this.requisitionList = [];
        this.getList();
      } else if (type == "week") {
        this.week();
      } else if (type == "month") {
        this.month();
      }
      this.$refs.filter_item.toggle();
    },
    //本周
    week() {
      this.$nextTick(() => {
        let now = new Date();
        let nowDayOfWeek = now.getDay();
        let day = nowDayOfWeek || 7;
        let nowDay = now.getDate();
        let nowMonth = now.getMonth();
        let weekStar = this.formatDate(new Date(now.getFullYear(), nowMonth, nowDay + 1 - day));
        let nows = new Date();
        let nowDayOfWeeks = nows.getDay();
        let days = nowDayOfWeeks || 7;
        let nowDays = nows.getDate();
        let nowMonths = nows.getMonth();
        let weekEnd = this.formatDate(new Date(nows.getFullYear(), nowMonths, nowDays + 7 - days));
        this.startTime = weekStar;
        this.endTime = weekEnd;
        this.pageNo = 1;
        this.requisitionList = [];
        this.getList();
      });
    },
    //本月
    month() {
      this.$nextTick(() => {
        const data = new Date();
        const year = data.getFullYear();
        const month = data.getMonth();
        let monthStarDate = new Date(year, month, 1);
        let monthEndDate = new Date(year, month + 1, 0);
        monthStarDate = this.formatDate(monthStarDate);
        monthEndDate = this.formatDate(monthEndDate);
        this.startTime = monthStarDate;
        this.endTime = monthEndDate;
        this.pageNo = 1;
        this.requisitionList = [];
        this.getList();
      });
    },
    formatDate(date) {
      let myyear = date.getFullYear();
      let mymonth = date.getMonth() + 1;
      let myweekday = date.getDate();
      if (mymonth < 10) {
        mymonth = "0" + mymonth;
      }
      if (myweekday < 10) {
        myweekday = "0" + myweekday;
      }
      return myyear + "-" + mymonth + "-" + myweekday;
    },
    onRefresh() {
      this.pageNo = 1;
      this.finished = false;
      this.loading = true;
      this.requisitionList = [];
      this.getList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.pageNo++;
      this.getList();
    },
    getRequisitionList() {
      this.pageNo = 1;
      this.requisitionList = [];
      this.getList();
    },
    getList() {
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        name: this.name,
        recordUserId: this.loginInfo.staffId,
        startTime: this.startTime,
        endTime: this.endTime
      };
      this.$api.findDepotManagementRecordPage(params).then(res => {
        this.loading = false;
        res.list.forEach(item => {
          this.requisitionList.push(item);
        });
        if (this.requisitionList.length >= res.count) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },

    // 取消搜索
    searchCancel() {
      this.name = "";
      this.pageNo = 1;
      this.requisitionList = [];
      this.getList();
    },
    appMaterialInput(value) {
      if (!value) {
        this.name = "";
        this.pageNo = 1;
        this.requisitionList = [];
        this.getList();
      }
    },
    goBack() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
  overflow: auto;
  font-size: 16px;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .alarmLevel0 {
        color: #00b42a;
      }
      .alarmLevel1 {
        color: #3562db;
      }
      .alarmLevel2 {
        color: #ff7d00;
      }
      .alarmLevel3 {
        color: #f53f3f;
      }
      .txt_l {
        margin: auto 0;
        width: 100px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
  .notList {
    position: relative;
    height: calc(100% - 1.44rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
/deep/ .van-dropdown-menu__bar {
  box-shadow: none;
}
.top-tools {
  position: fixed;
  width: 100%;
  height: 13.5vh;
  z-index: 99999;
}
.tools-replace {
  height: 13.5vh;
}
.textStyle .num {
  color: #3562db !important;
}
.textStyle .title {
  color: #3562db !important;
}
.region {
  border-radius: 8px;
}
.border-bottom {
  margin-bottom: 10px;
  position: relative;
}
.time-text {
  margin-bottom: 8px;
}
.department {
  width: 67vw;
  span {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.modify_middle {
  display: flex;
  justify-content: space-between;
}
.tranform_style {
  transform: translateY(6px);
  word-break: break-all;
  line-height: 20px;
}
.choice {
  display: flex;
  justify-content: space-between;
  height: 50px;
  align-items: center;
}
.option-box {
  padding: 0 16px;
}
/deep/ .van-button__text {
  font-size: 14px;
}
.active-date {
  color: #3562db;
}
.date-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.date-box .date-text {
  background-color: #f2f3f5;
  padding: 8px 16px;
  border-radius: 3px;
  width: 30vw;
  height: 20px;
  text-align: center;
  line-height: 20px;
}
.date-line {
  width: 10px;
  height: 2px;
  background-color: #c9cdd4;
  margin: 0 6px;
}
.van-datetime-picker {
  margin-bottom: 12px;
}
.bg-green {
  background-color: #e8ffea !important;
  color: #00b42a !important;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: 12px;
}
.bg-red {
  background-color: #ffece8 !important;
  color: #f53f3f !important;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: 12px;
}
/deep/ .van-dropdown-item__content {
  max-height: 95%;
}
</style>
