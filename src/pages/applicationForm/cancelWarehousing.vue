<template>
  <div class="inner">
    <Header title="取消申请" @backFun="goBack"></Header>
    <van-field v-model="approvalComment" rows="10" autosize label="备注" type="textarea" maxlength="500" placeholder="请输入备注" show-word-limit />
    <div class="btn">
      <van-button style="width: 45%" color="#3562db" @click="$router.go(-1)">取消</van-button><van-button style="width: 45%" color="#3562db" @click="submit">确定</van-button>
    </div>
  </div>
</template>

<script>
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  mixins: [fontSizeMixin],
  data() {
    return {
      title: "",
      approvalComment: ""
    };
  },

  created() {
    this.title = this.$route.query.type == 2 ? "驳回" : "通过";
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    submit() {
      let params = {
        approvalControlId: this.$route.query.id,
        approvalComment: this.approvalComment,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        userName: JSON.parse(localStorage.getItem("loginInfo")).staffName
      };
      this.$api.inventoryRevokeApproval(params).then(res => {
        this.$toast.success("取消成功");
        this.$router.push({
          path: "/myAccessRequest"
        });
      });
    },

    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style   scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
/deep/ .van-field .van-field__label > span {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .van-field__control {
  font-size: calc(14px * var(--font-scale))!important;
}
/deep/ .van-button--normal {
  font-size: calc(15px * var(--font-scale))!important;
}
</style>
