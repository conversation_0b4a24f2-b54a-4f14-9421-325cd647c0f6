<template>
  <div class="page-container">
    <Header title="出库单详情" @backFun="goBack"></Header>

    <!-- 上半部分：出库单信息 -->
    <div class="info-section">
      <div class="info-card">
        <div class="info-item">
          <span class="label">出库单号：</span>
          <span class="value">{{ exportDetail.recordNumber }}</span>
        </div>
        <div class="info-item">
          <span class="label">出库仓库：</span>
          <span class="value">{{ exportDetail.warehouseName }}</span>
        </div>
        <div class="info-item">
          <span class="label">出库类型：</span>
          <span class="value">{{ exportDetail.outwarehouseTypeName }}</span>
        </div>
        <div class="info-item">
          <span class="label">出库日期：</span>
          <span class="value">{{ exportDetail.outwarehouseDate }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联工单：</span>
          <span class="value">{{ exportDetail.workNum || '无' }}</span>
        </div>
      </div>
    </div>

    <!-- 下半部分：耗材列表 -->
    <div class="materials-section">
      <div class="section-title">出库明细</div>
      <div class="materials-list">
        <div class="material-card" v-for="(item, index) in materialsList" :key="index">
          <div class="material-info">
            <div class="material-name-row">
              <div class="material-name">{{ item.materialName }}</div>
              <div class="material-quantity">{{ item.operateCount }} {{ item.basicUnitName }}</div>
            </div>
            <div class="material-item">
              <span class="item-label">耗材编码：</span>
              <span class="item-value">{{ item.materialCode }}</span>
            </div>
            <div class="material-item">
              <span class="item-label">耗材类型：</span>
              <span class="item-value">{{ item.materialTypeName }}</span>
            </div>
            <div class="material-item">
              <span class="item-label">规格型号：</span>
              <span class="item-value">{{ item.model }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div class="empty-state" v-if="materialsList.length === 0 && !loading">
        <img src="../../assets/images/noDataDefault/content-error.png" class="empty-img" />
        <p>暂无耗材明细</p>
      </div>
    </div>

    <!-- 加载中 -->
    <div class="loading-container" v-if="loading">
      <van-loading color="#3562db" />
    </div>
  </div>
</template>

<script>
import fontSizeMixin from "@/mixins/fontSizeMixin";
import { mapState } from "vuex";

export default {
  mixins: [fontSizeMixin],
  data() {
    return {
      loading: false,
      exportId: '',
      exportDetail: {},
      materialsList: [],
      recordNumber: ''
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  created() {
    this.exportId = this.$route.query.id || "";
    this.recordNumber = this.$route.query.recordNumber || "";
    if (this.exportId) {
      this.fetchExportDetail();
    } else {
      this.$toast.fail('未指定出库单');
      this.goBack();
    }
  },
  activated() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    fetchExportDetail() {
      this.loading = true;
      
      // 获取出库单详情
      this.$api
        .getOutboundDetail({
          id: this.exportId,
          userId: this.loginInfo.staffId,
          userName: this.loginInfo.staffName,
          recordNumber: this.recordNumber
        })
        .then(res => {
          this.loading = false;
          if (res) {
            this.exportDetail = res;
            // 获取出库单耗材明细
            this.materialsList = res.materialRecordList || [];
          } else {
            this.$toast.fail('获取出库单详情失败');
          }
        })
        .catch(() => {
          this.loading = false;
          this.$toast.fail('获取出库单详情失败');
        });
    }
  }
};
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f2f4f9;
  padding-bottom: 20px;
}

.info-section {
  padding: 16px;
}

.info-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  width: 90px;
  color: #666;
  font-size: calc(14px * var(--font-scale));
  white-space: nowrap;
}

.value {
  flex: 1;
  color: #333;
  font-size: calc(14px * var(--font-scale));
  word-break: break-all;
}

.materials-section {
  padding: 0 16px;
}

.section-title {
  font-size: calc(16px * var(--font-scale));
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  padding-left: 8px;
  position: relative;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: #3562db;
  border-radius: 2px;
}

.materials-list {
  margin-bottom: 20px;
}

.material-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.material-info {
  flex: 1;
}

.material-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.material-name {
  font-size: calc(15px * var(--font-scale));
  font-weight: 500;
  color: #333;
}

.material-quantity {
  font-size: calc(15px * var(--font-scale));
  font-weight: 500;
  color: #3562db;
}

.material-item {
  display: flex;
  margin-bottom: 8px;
  line-height: 1.4;
}

.material-item:last-child {
  margin-bottom: 0;
}

.item-label {
  color: #666;
  font-size: calc(13px * var(--font-scale));
  white-space: nowrap;
}

.item-value {
  flex: 1;
  color: #333;
  font-size: calc(13px * var(--font-scale));
  word-break: break-all;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;

  .empty-img {
    width: 120px;
    height: 120px;
    margin-bottom: 12px;
  }

  p {
    color: #999;
    font-size: calc(14px * var(--font-scale));
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.large-font {
  .label,
  .value {
    font-size: calc(16px * var(--font-scale));
  }
  
  .material-name,
  .material-quantity {
    font-size: calc(17px * var(--font-scale));
  }
  
  .item-label,
  .item-value {
    font-size: calc(15px * var(--font-scale));
  }
}
</style> 