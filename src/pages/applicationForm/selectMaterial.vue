<template>
  <div class="inner">
    <Header title="选择物料" @backFun="goBack"></Header>
    <div class="top-tools">
      <!-- 筛选区域 -->
      <van-search
        v-model="depProductName"
        background="#fff"
        clearable
        :show-action="true"
        placeholder="搜索物料"
        @search="getMaterialList"
        @cancel="searchCancel"
        @input="appMaterialInput"
      >
      </van-search>
      <van-dropdown-menu active-color="#3562DB">
        <van-dropdown-item v-model="warehouseId" :options="warehouseOptions" @change="handleFilterChange" />
        <van-dropdown-item v-model="materialTypeId" :options="materialTypeOptions" @change="handleFilterChange" />
      </van-dropdown-menu>
    </div>
    <div class="tools-replace"></div>
    <van-checkbox-group v-if="materialList.length > 0" v-model="result" @change="checkboxChange">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width: 100%">
            <div v-for="(item, index) in materialList" :key="index" class="listStyle">
              <div class="name">
                <van-checkbox :name="item.materialCode" checked-color="#3562db">{{ item.materialName }}</van-checkbox>
                <div class="warehouse" v-if="item.warehouseName">{{ item.warehouseName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">耗材类型</div>
                <div class="txt_r">{{ item.materialTypeName }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">规格型号</div>
                <div class="txt_r">{{ item.model }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">计量单位</div>
                <div class="txt_r">{{ item.basicUnitName }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div class="btn">
        <van-button style="width: 90%" color="#3562db" @click="goApplicationForm">确定</van-button>
      </div>
    </van-checkbox-group>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ImagePreview } from "vant";
import fontSizeMixin from "@/mixins/fontSizeMixin";
import { mapState } from "vuex";
export default {
  mixins: [fontSizeMixin],
  data() {
    return {
      result: [],
      depProductName: "",
      warehouseId: "", // 仓库ID
      materialTypeId: "", // 耗材类型ID
      warehouseOptions: [], // 仓库选项
      materialTypeOptions: [], // 耗材类型选项
      pageNo: 1,
      pageSize: 10,
      total: 0,
      materialList: [],
      materialData: [],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.materialData = this.$route.query.materialData || [];
    // console.log(this.materialData, "------------------");
    if (this.materialData.length > 0) {
      this.result = this.materialData.map(obj => obj.materialCode);
    }
    this.getWarehouseList();
    this.getMaterialTypeList();
    this.getList();
  },
  methods: {
    previewImage(url) {
      if (!url) return;
      ImagePreview({
        images: [this.$YBS.imgUrlTranslation(url)],
        closeable: true
      });
    },
    checkboxChange(list) {
      this.materialList.forEach(i => {
        list.forEach(j => {
          if (i.materialCode == j) {
            i.changeNum = "";
            this.materialData.push(i);
          }
        });
      });
      let pmaterialArr = this.materialData.filter(
        (item, index, self) => index === self.findIndex(t => t.materialCode === item.materialCode)
      );
      this.materialData = pmaterialArr.filter(obj => list.includes(obj.materialCode));
    },
    goApplicationForm() {
      // 使用Vuex存储选中的物料数据
      this.$store.commit('setMaterialData', this.materialData);
      this.$router.go(-1);
      // this.$router.push({
      //   path: "/applicationForm",
      //   query: {
      //     list: this.materialData
      //   }
      // });
    },
    onRefresh() {
      this.pageNo = 1;
      this.finished = false;
      this.loading = true;
      this.materialList = [];
      this.getList();
    },
    onLoad() {
      // 如果已经完成加载，则不再触发
      if (this.finished) {
        return;
      }
      this.loading = true;
      this.pageNo++;
      this.getList();
    },
    getMaterialList() {
      this.pageNo = 1;
      this.materialList = [];
      this.getList();
    },
    // 获取仓库列表
    getWarehouseList() {
      this.$api
        .getWarehouseListForApp({
          currentUserDeptCode: this.loginInfo.deptId,
        })
        .then(res => {
          this.warehouseOptions = res.map(item => ({
            text: item.warehouseName,
            value: item.id
          }));
          this.warehouseOptions.unshift({ text: "全部仓库", value: "" });
        });
    },
    
    // 获取耗材类型列表
    getMaterialTypeList() {
      this.$api
        .getDictByPage({
          userType: 1,
          dictionaryDetailsStatus:1,
          userId: this.loginInfo.staffId,
          userName: this.loginInfo.staffName,
          dictionaryCategoryId: "consumables_type" // 假设耗材类型的字典ID为material_type
        })
        .then(res => {
          this.materialTypeOptions = res.map(item => ({
            text: item.dictionaryDetailsName,
            value: item.dictionaryDetailsId
          }));
          this.materialTypeOptions.unshift({ text: "全部类型", value: "" });
        });
    },
    
    // 筛选条件变化处理
    handleFilterChange() {
      this.pageNo = 1;
      this.materialList = [];
      this.finished = false;
      this.getList();
    },
    getList() {
      let params = {
        currentPage: this.pageNo,
        pageSize: this.pageSize,
        keyWords: this.depProductName,
        currentUserDeptCode: this.loginInfo.deptId,
        warehouseId: this.warehouseId, // 添加仓库ID筛选
        materialTypeCode: this.materialTypeId // 添加耗材类型ID筛选
      };
      
      // 根据是否有depotNumber来决定使用哪个接口
      if (this.$route.query.depotNumber) {
        // 删除分页参数
        delete params.currentPage;
        delete params.pageSize;
        // 添加recordNumber和workNum参数
        params.recordNumber = this.$route.query.depotNumber;
        if (this.$route.query.workNum) {
          params.workNum = this.$route.query.workNum;
        }
        // 使用getOutDetailList接口（不分页）
        this.$api.getOutDetailList(params).then(res => {
          this.loading = false;
          this.isLoading = false;
          
          // 直接设置列表数据
          this.materialList = res.data || [];
          
          // 由于不分页，所以直接设置为已完成
          this.finished = true;
        }).catch(() => {
          this.loading = false;
          this.isLoading = false;
        });
      } else {
        // 添加recordNumber和workNum参数（如果存在）
        if (this.$route.query.recordNumber) {
          params.recordNumber = this.$route.query.recordNumber;
        }
        if (this.$route.query.workNum) {
          params.workNum = this.$route.query.workNum;
        }
        // 使用原来的分页接口
        this.$api.getInventoryListForApp(params).then(res => {
          this.loading = false;
          this.isLoading = false;
          // 添加数据到列表
          if (res.list && res.list.length > 0) {
            res.list.forEach(item => {
              this.materialList.push(item);
            });
          }
          
          // 判断是否加载完所有数据
          if (res.list.length < this.pageSize || this.materialList.length >= res.count) {
            this.finished = true;
          }
        }).catch(() => {
          this.loading = false;
          this.isLoading = false;
        });
      }
    },
    // 取消搜索
    searchCancel() {
      this.depProductName = "";
      this.pageNo = 1;
      this.materialList = [];
      this.getList();
    },
    appMaterialInput(value) {
      if (!value) {
        this.depProductName = "";
        this.pageNo = 1;
        this.materialList = [];
        this.getList();
      }
    },
    goBack() {
      // 使用Vuex存储选中的物料数据
      this.$store.commit('setMaterialData', this.materialData);
      this.$router.go(-1);
      // this.$router.push({
      //   path: "/applicationForm",
      //   query: {
      //     list: this.materialData
      //   }
      // });
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
  overflow: auto;
  font-size: calc(16px * var(--font-scale))!important;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
      align-items: center;
      /deep/ .van-checkbox__label {
        line-height: 1.2;
      }
      .warehouse {
        color: #86909c;
        font-size: calc(14px * var(--font-scale));
        white-space: nowrap;
        margin-left: 10px;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .alarmLevel0 {
        color: #00b42a;
      }
      .alarmLevel1 {
        color: #3562db;
      }
      .alarmLevel2 {
        color: #ff7d00;
      }
      .alarmLevel3 {
        color: #f53f3f;
      }
      .txt_l {
        margin: auto 0;
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
  .notList {
    position: relative;
    height: calc(100% - 1.44rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}

.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.top-tools {
  position: fixed;
  width: 100%;
  z-index: 99999;
}
.tools-replace {
  height: 14vh; /* 增加高度以适应新增的筛选条件 */
}
.material-img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}
/deep/ .btn .van-button--normal {
  font-size: calc(15px * var(--font-scale))!important;
}
/deep/ .van-search__content .van-cell,
/deep/ .van-search .van-search__action {
  font-size: calc(14px * var(--font-scale))!important;
}

/deep/ .van-dropdown-menu {
  height: 40px;
  background-color: #fff;
}

/deep/ .van-dropdown-menu__item {
  font-size: calc(14px * var(--font-scale))!important;
}
</style>
