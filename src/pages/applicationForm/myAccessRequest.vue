<template>
  <div class="inner">
    <Header title="我的出入库" @backFun="goBack"></Header>
    <div class="top-tools">
      <van-dropdown-menu active-color="#3562DB">
        <van-dropdown-item :title="dateType == 'week' ? '本周' : dateType == 'month' ? '本月' : dateType == 'all' ? '全部时间' : '自定义'" ref="filter_item">
          <div class="option-box">
            <div class="choice" @click="confirmDateType('all')">
              <span :class="{ 'active-date': dateType == 'all' }">全部时间</span>
              <i v-if="dateType == 'all'" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmDateType('week')">
              <span :class="{ 'active-date': dateType == 'week' }">本周</span>
              <i v-if="dateType == 'week'" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmDateType('month')">
              <span :class="{ 'active-date': dateType == 'month' }">本月</span>
              <i v-if="dateType == 'month'" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="handleDiy">
              <span :class="{ 'active-date': isDiy }">自定义</span>
              <i v-if="isDiy" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"></i>
            </div>
          </div>
          <div v-if="isDiy" style="padding: 5px 16px">
            <div class="date-box">
              <div class="date-text" @click="switchDateType('start')">
                {{ startTime }}
              </div>
              <span class="date-line"></span>
              <div class="date-text" @click="switchDateType('end')">
                {{ endTime }}
              </div>
            </div>
            <van-datetime-picker
              v-if="currentDateType == 'start'"
              v-model="currentDate"
              cancel-button-text=" "
              confirm-button-text=" "
              type="date"
              @change="dateFormatter"
              title="选择初始时间"
            />
            <van-datetime-picker v-else v-model="currentDate" cancel-button-text=" " confirm-button-text=" " @change="dateFormatter" type="date" title="选择结束时间" />
            <van-button type="primary" color="#3562DB" block @click="onConfirm"> 确认 </van-button>
          </div>
        </van-dropdown-item>

        <van-dropdown-item title="申请类型" ref="typeFilter_item">
          <div class="option-box">
            <div class="choice" @click="confirmApplyType('all')">
              <span :class="{ 'active-date': applyTypeList.includes('all') }">全部</span>
              <i v-if="applyTypeList.includes('all')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmApplyType('1')">
              <span :class="{ 'active-date': applyTypeList.includes('1') }">库存增加</span>
              <i v-if="applyTypeList.includes('1')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmApplyType('2')">
              <span :class="{ 'active-date': applyTypeList.includes('2') }">出库归还</span>
              <i v-if="applyTypeList.includes('2')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmApplyType('3')">
              <span :class="{ 'active-date': applyTypeList.includes('3') }">库存减少</span>
              <i v-if="applyTypeList.includes('3')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmApplyType('4')">
              <span :class="{ 'active-date': applyTypeList.includes('4') }">工单领用</span>
              <i v-if="applyTypeList.includes('4')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"></i>
            </div>
          </div>
        </van-dropdown-item>

        <van-dropdown-item title="状态" ref="statusFilter_item">
          <div class="option-box">
            <div class="choice" @click="confirmStatusType('all')">
              <span :class="{ 'active-date': statusType.includes('all') }">全部</span>
              <i v-if="statusType.includes('all')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmStatusType('0')">
              <span :class="{ 'active-date': statusType.includes('0') }">待审批</span>
              <i v-if="statusType.includes('0')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmStatusType('1')">
              <span :class="{ 'active-date': statusType.includes('1') }">已通过</span>
              <i v-if="statusType.includes('1')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmStatusType('2')">
              <span :class="{ 'active-date': statusType.includes('2') }">已驳回</span>
              <i v-if="statusType.includes('2')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"><!----></i>
            </div>
            <div class="choice" @click="confirmStatusType('3')">
              <span :class="{ 'active-date': statusType.includes('3') }">已取消</span>
              <i v-if="statusType.includes('3')" class="van-icon van-icon-success van-dropdown-item__icon" style="color: rgb(53, 98, 219)"></i>
            </div>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
      <van-search
        v-model="searchKeyword"
        background="#fff"
        clearable
        :show-action="true"
        placeholder="搜索单号或工单号"
        @search="getApprovalList"
        @clear="searchCancel"
        @input="appMaterialInput"
      >
      </van-search>
    </div>
    <div class="tools-replace"></div>
    <div v-if="approvalList.length > 0">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div style="width: 100%">
            <div v-for="item in approvalList" :key="item.id" class="listStyle" @click="details(item)">
              <div class="name">
                <div>{{ getApplyTypeName(JSON.parse(item.businessJson || "{}").changeType) }}</div>
                <div class="state">
                  <span :class="['approvalStatus', `approvalStatus${item.approvalStatus}`]"> {{ approvalType(item.approvalStatus) }}</span>
                  <span><van-icon name="arrow" /></span>
                </div>
              </div>
              <div class="txt">
                <div class="txt_l">单号</div>
                <div class="txt_r">{{ JSON.parse(item.businessJson || "{}").depotNumber || "—" }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">申请时间</div>
                <div class="txt_r">{{ JSON.parse(item.businessJson || "{}").createData || "—" }}</div>
              </div>
              <div class="txt">
                <div class="txt_l">关联工单</div>
                <div class="txt_r">{{ JSON.parse(item.businessJson || '{}').workNum || "—" }}</div>
              </div>
              <div class="txt" v-if="JSON.parse(item.businessJson || '{}').changeType == 1 || JSON.parse(item.businessJson || '{}').changeType == 2">
                <div class="txt_l">关联出库单</div>
                <div class="txt_r">{{ JSON.parse(item.businessJson || '{}').depotOutNumber || "—" }}</div>
              </div>
              <div class="txt" v-if="JSON.parse(item.businessJson || '{}').changeType == 3 || JSON.parse(item.businessJson || '{}').changeType == 4">
                <div class="txt_l">领用部门</div>
                <div class="txt_r">{{ JSON.parse(item.businessJson || '{}').useDeptName || "—" }}</div>
              </div>
              <div class="txt" v-if="JSON.parse(item.businessJson || '{}').changeType == 3 || JSON.parse(item.businessJson || '{}').changeType == 4">
                <div class="txt_l">领用人员</div>
                <div class="txt_r">{{ JSON.parse(item.businessJson || '{}').usePersonName || "—" }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import fontSizeMixin from "@/mixins/fontSizeMixin";

export default {
  mixins: [fontSizeMixin],
  data() {
    return {
      currentDate: new Date(),
      isDiy: false,
      currentDateType: "start",
      dateType: "all",
      startTime: "",
      endTime: "",
      applicationNumber: "",
      pageNo: 1,
      pageSize: 4,
      total: 0,
      approvalList: [],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      statusType: ["all"],
      applyTypeList: ["all"],
      searchKeyword: ""
    };
  },
  computed: {
    ...mapState(["loginInfo"])
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getList();
  },
  methods: {
    details(row) {
      this.$router.push({
        path: "/myAccessRequestDetails",
        query: {
          id: row.id
        }
      });
    },
    approvalType(approvalState) {
      const approvalStatusMap = {
        0: "待审批",
        1: "已通过",
        2: "已驳回",
        3: "已取消"
      };
      return approvalStatusMap[approvalState];
    },
    confirmStatusType(type) {
      this.finished = false;
      this.loading = true;
      this.pageNo = 1;
      if (type == "all") {
        this.statusType = ["all"];
        this.$refs.statusFilter_item.toggle();
        this.approvalList = [];
        this.getList();
        return;
      } else {
        this.statusType = this.statusType.filter(i => i != "all");
      }
      if (this.statusType.includes(type)) {
        this.statusType = this.statusType.filter(i => i != type);
      } else {
        this.statusType.push(type);
      }
      if (this.statusType.length == 0) {
        this.statusType = ["all"];
        this.$refs.statusFilter_item.toggle();
      }
      this.approvalList = [];
      this.getList();
    },
    onConfirm() {
      this.$refs.filter_item.toggle();
      let date1 = new Date(this.startTime);
      let date2 = new Date(this.endTime);
      if (date1 > date2) return this.$toast.fail("开始时间不能大于结束时间");
      this.pageNo = 1;
      this.approvalList = [];
      this.getList();
    },
    switchDateType(type) {
      this.currentDateType = type;
    },
    handleDiy() {
      this.isDiy = true;
      this.dateType = "";
    },
    dateFormatter(picker) {
      let dateArr = picker.getValues();
      if (this.currentDateType == "start") {
        setTimeout(() => {
          this.startTime = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;
        }, 300);
      } else {
        this.endTime = `${dateArr[0]}-${dateArr[1]}-${dateArr[2]}`;
      }
    },
    confirmDateType(type) {
      this.isDiy = false;
      this.dateType = type;
      this.startTime = "";
      this.endTime = "";
      if (type == "all") {
        this.tartTime = "";
        this.endTime = "";
        this.pageNo = 1;
        this.approvalList = [];
        this.getList();
      } else if (type == "week") {
        this.week();
      } else if (type == "month") {
        this.month();
      }
      this.$refs.filter_item.toggle();
    },
    //本周
    week() {
      this.$nextTick(() => {
        let now = new Date();
        let nowDayOfWeek = now.getDay();
        let day = nowDayOfWeek || 7;
        let nowDay = now.getDate();
        let nowMonth = now.getMonth();
        let weekStar = this.formatDate(new Date(now.getFullYear(), nowMonth, nowDay + 1 - day));
        let nows = new Date();
        let nowDayOfWeeks = nows.getDay();
        let days = nowDayOfWeeks || 7;
        let nowDays = nows.getDate();
        let nowMonths = nows.getMonth();
        let weekEnd = this.formatDate(new Date(nows.getFullYear(), nowMonths, nowDays + 7 - days));
        this.startTime = weekStar;
        this.endTime = weekEnd;
        this.pageNo = 1;
        this.approvalList = [];
        this.getList();
      });
    },
    //本月
    month() {
      this.$nextTick(() => {
        const data = new Date();
        const year = data.getFullYear();
        const month = data.getMonth();
        let monthStarDate = new Date(year, month, 1);
        let monthEndDate = new Date(year, month + 1, 0);
        monthStarDate = this.formatDate(monthStarDate);
        monthEndDate = this.formatDate(monthEndDate);
        this.startTime = monthStarDate;
        this.endTime = monthEndDate;
        this.pageNo = 1;
        this.approvalList = [];
        this.getList();
      });
    },
    formatDate(date) {
      let myyear = date.getFullYear();
      let mymonth = date.getMonth() + 1;
      let myweekday = date.getDate();
      if (mymonth < 10) {
        mymonth = "0" + mymonth;
      }
      if (myweekday < 10) {
        myweekday = "0" + myweekday;
      }
      return myyear + "-" + mymonth + "-" + myweekday;
    },
    onRefresh() {
      this.pageNo = 1;
      this.finished = false;
      this.loading = true;
      this.approvalList = [];
      this.getList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.pageNo++;
      this.getList();
    },
    getApprovalList() {
      this.pageNo = 1;
      this.approvalList = [];
      this.getList();
    },
    getList() {
      let params = {
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        currentPage: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.startTime,
        endTime: this.endTime,
        approvalStatus: this.statusType.toString(),
        applicationNumber: this.applicationNumber,
        workNum: this.searchKeyword,
        approvalTitle: this.applyTypeList.includes("all") ? "" : this.applyTypeList.toString()
      };
      this.$api.queryMyApprovalList(params).then(res => {
        console.log(res, "ssss");
        this.loading = false;
        res.responseList.forEach(item => {
          this.approvalList.push(item);
        });
        if (this.approvalList.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },

    // 取消搜索
    searchCancel() {
      this.searchKeyword = "";
      this.pageNo = 1;
      this.approvalList = [];
      this.getList();
    },
    appMaterialInput(value) {
      if (!value) {
        this.searchKeyword = "";
        this.pageNo = 1;
        this.approvalList = [];
        this.getList();
      }
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "h5") {
        this.$router.go(-1);
      } else {
        this.$YBS.apiCloudCloseFrame();
      }
    },
    confirmApplyType(type) {
      this.finished = false;
      this.loading = true;
      this.pageNo = 1;
      if (type == "all") {
        this.applyTypeList = ["all"];
        this.$refs.typeFilter_item.toggle();
        this.approvalList = [];
        this.getList();
        return;
      } else {
        this.applyTypeList = this.applyTypeList.filter(i => i != "all");
      }
      if (this.applyTypeList.includes(type)) {
        this.applyTypeList = this.applyTypeList.filter(i => i != type);
      } else {
        this.applyTypeList.push(type);
      }
      if (this.applyTypeList.length == 0) {
        this.applyTypeList = ["all"];
        this.$refs.typeFilter_item.toggle();
      }
      this.approvalList = [];
      this.getList();
    },
    getApplyTypeName(type) {
      const typeMap = {
        1: "库存增加",
        2: "出库归还",
        3: "库存减少",
        4: "工单领用"
      };
      return typeMap[type] || "-";
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f2f4f9;
  overflow: auto;
  font-size: 16px;
  .listStyle {
    width: 95%;
    background-color: #fff;
    border-radius: 10px;
    margin: 10px auto;
    padding: 5px 0;
    .name {
      padding: 10px 10px;
      color: #1d2129;
      display: flex;
      justify-content: space-between;
    }
    .state {
      .approvalStatus {
        padding: 5px 8px;
        border-radius: 4px;
      }
      .approvalStatus0 {
        background: #e6effc;
        color: #3562db;
      }
      .approvalStatus1 {
        background: #e8ffea;
        color: #00b42a;
      }

      .approvalStatus2 {
        background: #ffece8;
        color: #f53f3f;
      }
      .approvalStatus3 {
        background: #f2f3f5;
        color: #4e5969;
      }
    }
    .txt {
      display: flex;
      padding: 7px 10px;

      .alarmLevel0 {
        color: #00b42a;
      }
      .alarmLevel1 {
        color: #3562db;
      }
      .alarmLevel2 {
        color: #ff7d00;
      }
      .alarmLevel3 {
        color: #f53f3f;
      }
      .txt_l {
        margin: auto 0;
        width: 100px;
        color: #4e5969;
        font-weight: 300;
      }
      .txt_r {
        color: #1d2129;
        flex: 1;
      }
    }
    .txtOpertaion {
      display: flex;
      height: 35px;
      line-height: 35px;
      padding: 0px 10px;
      .txt_l {
        width: 90px;
        color: #4e5969;
        font-weight: 300;
      }
      .btn {
        flex: 1;
        text-align: right;
        span {
          display: inline-block;
          height: 30px;
          line-height: 30px;
          padding: 0px 15px;
          background-color: #3562db;
          color: #fff;
        }
      }
    }
  }
  .notList {
    position: relative;
    height: calc(100% - 1.44rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
/deep/ .van-dropdown-menu__bar {
  box-shadow: none;
}
.top-tools {
  position: fixed;
  width: 100%;
  height: 13.5vh;
  z-index: 99999;
}
.tools-replace {
  height: 13.5vh;
}
.textStyle .num {
  color: #3562db !important;
}
.textStyle .title {
  color: #3562db !important;
}
.region {
  border-radius: 8px;
}
.border-bottom {
  margin-bottom: 10px;
  position: relative;
}
.time-text {
  margin-bottom: 8px;
}
.department {
  width: 67vw;
  span {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.modify_middle {
  display: flex;
  justify-content: space-between;
}
.tranform_style {
  transform: translateY(6px);
  word-break: break-all;
  line-height: 20px;
}
.choice {
  display: flex;
  justify-content: space-between;
  height: 50px;
  align-items: center;
}
.option-box {
  padding: 0 16px;
}
/deep/ .van-button__text {
  font-size: 14px;
}
.active-date {
  color: #3562db;
}
.date-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.date-box .date-text {
  background-color: #f2f3f5;
  padding: 8px 16px;
  border-radius: 3px;
  width: 30vw;
  height: 20px;
  text-align: center;
  line-height: 20px;
}
.date-line {
  width: 10px;
  height: 2px;
  background-color: #c9cdd4;
  margin: 0 6px;
}
.van-datetime-picker {
  margin-bottom: 12px;
}
.bg-green {
  background-color: #e8ffea !important;
  color: #00b42a !important;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: 12px;
}
.bg-red {
  background-color: #ffece8 !important;
  color: #f53f3f !important;
  padding: 6px 8px;
  border-radius: 3px;
  font-size: 12px;
}
/deep/ .van-dropdown-item__content {
  max-height: 95%;
}
/deep/ .van-dropdown-menu__title {
  font-size: calc(16px * var(--font-scale))!important;
  line-height: 1.2;
}
/deep/ .van-popup .option-box {
  font-size: calc(16px * var(--font-scale))!important;
}
/deep/ .van-search .van-field__control,
/deep/ .van-search .van-search__action {
  font-size: calc(14px * var(--font-scale))!important;
}
.listStyle {
  font-size: calc(14px * var(--font-scale))!important;
}
</style>
