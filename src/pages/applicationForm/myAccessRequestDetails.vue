<template>
  <div class="inner">
    <Header title="我的出入库" @backFun="goBack"></Header>
    <div class="exactness">
      <!-- <div class="icon">
        <div>
          <div :class="[`approvalStatus${approvalDetails.approvalStatus}`]"></div>
        </div>
        <div>{{ approvalType(approvalDetails.approvalStatus) }}</div>
      </div> -->

      <van-tabs v-model="activeTab" color="#3562db">
        <van-tab title="基本信息">
          <div class="listStyle listStyle-fix">
            <div class="new-title">申请信息</div>
            <div class="txt">
              <div class="txt_l">申请类型</div>
              <div class="txt_r" style="font-weight: bold">{{ getApplyTypeName(businessJson.changeType) }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">申请单状态</div>
              <span :class="['approvalStatus', `approvalStatus${approvalDetails.approvalStatus}`]"> {{ approvalType(approvalDetails.approvalStatus) }}</span>
            </div>
            <div class="txt">
              <div class="txt_l">单号</div>
              <div class="txt_r">{{ businessJson.depotNumber || "—" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">申请人</div>
              <div class="txt_r">{{ approvalDetails.creatorName || "—" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">申请时间</div>
              <div class="txt_r">{{ businessJson.createData || "—" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">关联工单</div>
              <div class="txt_r" style="color: #3562db" @click="goWorkOrderDetails">{{ businessJson.workNum || "—" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">关联工单科室</div>
              <div class="txt_r">{{ approvalDetails.sourcesDeptName || "—" }}</div>
            </div>
            <div class="txt" v-if="businessJson.changeType == 1 || businessJson.changeType == 2">
              <div class="txt_l">关联出库单</div>
              <div class="txt_r">{{ businessJson.depotOutNumber || "—" }}</div>
            </div>
            <div class="txt" v-if="businessJson.changeType == 3 || businessJson.changeType == 4">
              <div class="txt_l">领用部门</div>
              <div class="txt_r">{{ businessJson.useDeptName || "—" }}</div>
            </div>
            <div class="txt" v-if="businessJson.changeType == 3 || businessJson.changeType == 4">
              <div class="txt_l">领用人员</div>
              <div class="txt_r">{{ businessJson.usePersonName || "—" }}</div>
            </div>
            <div class="txt" v-if="approvalDetails.approvalStatus == 3">
              <div class="txt_l">取消人</div>
              <div class="txt_r">{{ approvalDetails.actualApprovalName || "—" }}</div>
            </div>
            <div class="txt" v-if="approvalDetails.approvalStatus == 3">
              <div class="txt_l">取消时间</div>
              <div class="txt_r">{{ approvalDetails.updateDate || "—" }}</div>
            </div>
            <div class="txt" v-if="approvalDetails.approvalStatus == 3">
              <div class="txt_l">备注</div>
              <div class="txt_r">{{ approvalDetails.approvalComment || "—" }}</div>
            </div>
          </div>
          <div class="listStyle listStyle-fix">
            <div class="new-title">审批信息</div>
            <div class="txt">
              <div class="txt_l">编号</div>
              <div class="txt_r">{{ approvalDetails.applicationNumber || "—" }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">审批人</div>
              <div class="txt_r">{{ approvalDetails.approvalStatus == '3' ? '—' : (approvalDetails.actualApprovalName || "—") }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">审批时间</div>
              <div class="txt_r">{{ approvalDetails.approvalStatus == '3' ? '—' : (approvalDetails.updateDate || "—") }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">备注</div>
              <div class="txt_r">{{ approvalDetails.approvalStatus == '3' ? '—' : (approvalDetails.approvalComment || "—") }}</div>
            </div>
          </div>
        </van-tab>

        <van-tab title="物料明细">
          <div v-for="item in businessJson.materialData" :key="item.id" class="listStyle depot-list" style="background-color: #fff">
            <div class="txt">
              <div class="txt_l">物料名称</div>
              <div class="txt_r">{{ item.depProductName }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">物料分类</div>
              <div class="txt_r">{{ item.type }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">工单类型</div>
              <div class="txt_r">{{ item.workTypeName }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">规格</div>
              <div class="txt_r">{{ item.specification }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">单位</div>
              <div class="txt_r">{{ item.unit }}</div>
            </div>
            <div class="txt">
              <div class="txt_l">数量</div>
              <div class="txt_r">{{ item.changeNum }}</div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    <div :style="approvalDetails.approvalStatus == '0' ? 'height:60px' : 'height:10px'"></div>
    <div class="btn" v-if="approvalDetails.approvalStatus == '0'">
      <van-button style="width: 90%" color="#3562db" @click="cancelWarehousing">取消</van-button>
    </div>
  </div>
</template>

<script>
import fontSizeMixin from "@/mixins/fontSizeMixin";
export default {
  mixins: [fontSizeMixin],
  data() {
    return {
      approvalDetails: "",
      businessJson: [],
      activeTab: 0
    };
  },
  created() {},
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);

    this.getQueryApprovalDetails();
  },
  methods: {
    goWorkOrderDetails() {
      this.$router.push({
        path: "/workOrderDetail",
        query: {
          detail: {
            workNum: this.businessJson.workNum
          }
        }
      });
    },
    approvalType(approvalState) {
      const approvalStatusMap = {
        0: "待审批",
        1: "已通过",
        2: "已驳回",
        3: "已取消"
      };
      return approvalStatusMap[approvalState];
    },
    getQueryApprovalDetails() {
      this.$api.queryApprovalControlById({ approvalControlId: this.$route.query.id }).then(res => {
        this.approvalDetails = res;
        this.businessJson = JSON.parse(res.businessJson);
      });
    },
    cancelWarehousing() {
      this.$router.push({
        path: "/cancelWarehousing",
        query: {
          id: this.$route.query.id
        }
      });
    },
    goBack() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    approvalType(approvalState) {
      const approvalStatusMap = {
        0: "待审批",
        1: "已通过",
        2: "已驳回",
        3: "已取消"
      };
      return approvalStatusMap[approvalState];
    },
    getApplyTypeName(type) {
      const typeMap = {
        1: "库存增加",
        2: "出库归还",
        3: "库存减少",
        4: "工单领用"
      };
      return typeMap[type] || "-";
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  width: 100%;
  height: 100%;
  background-color: #f0f2f8;
  font-size: calc(16px * var(--font-scale))!important;
  .exactness {
    width: 100%;
    .listStyle-fix {
      width: 100% !important;
      border-radius: 0 !important;
      margin: 0 !important;
      margin-top: 10px !important;
    }
    .depot-list .txt_l {
      width: 100px !important;
    }
    .listStyle {
      width: 95%;
      background-color: #fff;
      border-radius: 10px;
      margin: 10px auto;
      padding: 5px 0;
      .txt {
        display: flex;
        padding: 7px 10px;

        .alarmLevel0 {
          color: #00b42a;
        }
        .alarmLevel1 {
          color: #3562db;
        }
        .alarmLevel2 {
          color: #ff7d00;
        }
        .alarmLevel3 {
          color: #f53f3f;
        }
        .txt_l {
          margin: auto 0;
          width: 120px;
          color: #4e5969;
          font-weight: 300;
        }
        .txt_r {
          color: #1d2129;
          flex: 1;
        }
      }
      .txtOpertaion {
        display: flex;
        height: 35px;
        line-height: 35px;
        padding: 0px 10px;
        .txt_l {
          width: 90px;
          color: #4e5969;
          font-weight: 300;
        }
        .btn {
          flex: 1;
          text-align: right;
          span {
            display: inline-block;
            height: 30px;
            line-height: 30px;
            padding: 0px 15px;
            background-color: #3562db;
            color: #fff;
          }
        }
      }
    }
    // .icon {
    //   width: 100%;
    //   > div {
    //     display: flex;
    //     justify-content: center;
    //     .approvalStatus1 {
    //       margin: 10px 0;
    //       width: 40px;
    //       height: 40px;
    //       background: url("../../assets/images/icon-wrapper.png");
    //       background-size: 100% auto;
    //     }
    //     .approvalStatus2 {
    //       margin: 10px 0;
    //       width: 40px;
    //       height: 40px;
    //       background: url("../../assets/images/icon-wrong.png");
    //       background-size: 100% auto;
    //     }
    //     .approvalStatus0 {
    //       margin: 10px 0;
    //       width: 40px;
    //       height: 40px;
    //       background: url("../../assets/images/icon-reviewed.png");
    //       background-size: 100% auto;
    //     }
    //     .approvalStatus3 {
    //       margin: 10px 0;
    //       width: 40px;
    //       height: 40px;
    //       background: url("../../assets/images/icon-canc.png");
    //       background-size: 100% auto;
    //     }
    //   }
    // }
  }
}
.btn {
  width: 100%;
  display: flex;
  background-color: #fff;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.btn2 {
  width: 100%;
  display: flex;
  justify-content: space-around;
}
.btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
.new-title {
  font-weight: bold;
  font-size: 16px;
  position: relative;
  padding-left: 12px;
  margin: 10px 0;
  margin-left: 10px;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #3562db;
    border-radius: 2px;
  }
}
.approvalStatus {
  padding: 5px 8px;
  border-radius: 4px;
}
.approvalStatus0 {
  background: #e6effc;
  color: #3562db;
}
.approvalStatus1 {
  background: #e8ffea;
  color: #00b42a;
}

.approvalStatus2 {
  background: #ffece8;
  color: #f53f3f;
}
.approvalStatus3 {
  background: #f2f3f5;
  color: #4e5969;
}
/deep/ .van-tab__text {
  font-size: calc(14px * var(--font-scale))!important;
}
/deep/ .btn .van-button--normal {
  font-size: calc(15px * var(--font-scale))!important;
}
</style>
