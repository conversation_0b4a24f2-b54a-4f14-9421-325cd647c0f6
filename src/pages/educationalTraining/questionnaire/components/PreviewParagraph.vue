<template>
  <div class="paragraph-container">
    <div class="quesName">
      <span class="question-name-text">{{paragraphOption.name}}</span>
      <!-- <span class="starRed" >*</span> -->
    </div>
  </div>
  <!-- <div class="questionContainer">ssss</div> -->
</template>

<script>
  export default {
    props: {
      //当前题目的所有相关信息
      previewOption: {
        type: Object
      }
    },
    created() {
      this.paragraphOption = this.previewOption;
    },
    data () {
      return {
        paragraphOption: {},
        paragraphOption1: {
          name:"段落题目",
        },
      }
    },

    methods: {
      doValided() {
        return false;
      },
       doValidedFlag(){
      return true;
    },
    }
  }
</script>

<style lang="stylus" type="stylesheet/stylus">
.paragraph-container {
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    padding: 10px;
  }
  .question-name-text{
    font-weight: 600;
    font-size: 16px;
    line-height : 20px;
    color : #353535;
  }
  .starRed{
    color: red;
    font-weight: 600;
  }
  .questionContainer {
    padding: 20px 0px 0px 0px
  }
}
</style>