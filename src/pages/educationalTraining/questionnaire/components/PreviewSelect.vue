<template>
  <div class="select-container">
    <div :class="[isValided? 'error-border' : 'common-container']">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}.</span>
        <span class="question-name-text">{{selectOption.name}}</span>
        <span class="starRed" v-if="selectOption.isMust === 1">*</span>
      </div>
      <div class="option-container">
        <cube-select
          v-model="selectedValue"
          :options="options"
          @change="handleSelectChange"
          style="width: 100%"
          :disabled="isAnswered"
        >
        </cube-select>
        <!-- <el-select v-model="selectedValue" placeholder="请选择" style="width:100%;">
          <el-option
            v-for="childitem in selectOption.optionsData"
            :key="childitem.id"
            :label="childitem.name"
            :value="childitem.name">
          </el-option>
        </el-select> -->
      </div>
    </div>
  </div>
</template>

<script>
	import notice from "../notice/notice.js"
export default {
  name:"PreviewSelect",
  props: {
    //当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    //该题目在当前问卷中的序号
    index: {
      type: Number
    },
      //该题目是否已做答，true为已答过，false为未答
    isAnswered: {
      type: Boolean
    },
    //是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    }
  },
  //"489703643769769984"
  created() {
    this.selectOption = this.previewOption;
    if(this.previewOption.answers.length > 0){
      this.selectedValue = this.previewOption.answers[0].optionId;
    }
  },
  data(){
    return{
      selectedValue:"",
      isAnswer: false, //标志当前题目是否已做答
      isValided: false,
      answerVal: [],
      selectOption: {},
      //PC端element-ui的示例数据
      selectOption1: {
        name:"①下拉菜单222",
        optionsData: [
          {
            name: '黄金糕'
          }, {
            name: '双皮奶'
          }, {
            name: '蚵仔煎'
          }, {
            name: '龙须面'
          }, {
            name: '北京烤鸭'
          }
        ],
        isMust:1
      }
    }
  },
  computed: {
    options: {
      set(value) {},
      get() {
        return this.selectOption.optionsData.map((item) => {
          return {
            value: item.id,
            text: item.name
          }
        });
      }
    }
  },
  methods:{
    handleSelectChange(value, index, text) {
      console.log('change', value, index, text);
      this.answerVal = [{
        pvqId:localStorage.getItem('currentQuestionId'), //问卷id
        questionId: this.selectOption.id, //题目id
        questionType:this.selectOption.type, //题目类型
        optionId: value,
        optionName: text,
      }];
      this.isValided = false;
      this.isAnswer = true;
      // this.selectOption.isMust === 1 &&
       this.$parent.setAnswerPercentage(this.selectOption.id,this.isAnswer);
      	 //观察者触发
        notice.emitBrother("questionnaireObserver");
    },
    checkValided(){
      if(this.selectOption.isMust === 0){
        return false;
      }
      //判断当前题目是否必填，必填时才需要判断是否已答
      if(this.selectOption.isMust !== 1){
        return true;
      }
      //判断当前题目是否已答
      if(!this.isAnswer){
        return true;
      }
      return false;
    },
    	doValidedFlag(){
      return this.checkValided();
    },
    doValided() {
      this.isValided = this.checkValided();
      return this.isValided;
    }
  }
}
</script>

<style lang="stylus" type="stylesheet/stylus">
.select-container{
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
  }
  .question-name-text{
    font-weight: 600;
    font-size: 16px;
    line-height : 20px;
    color : #353535;
  }
  .starRed{
    color: red;
    font-weight: 600;
  }
  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }
  .error-border {
    border: 1px solid red;
    padding: 9px;
  }
  .option-container {
    padding: 10px 0;
  }
  .cube-select {
    color : #606266;
  }
  .cube-picker-wheel-item {
    word-break: normal;
    width: auto;
    display: block;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    line-height: 20px;
  }
}
</style>
