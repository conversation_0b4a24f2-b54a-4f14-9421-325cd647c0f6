class Utils {
  guid() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }


  //传入校验数组  和检测数组 要校验的key 返回提示的key   返回值是对象类型 flag为true则存在空值 否则不存在空值
  verificationIsNotEmpty(arr, checkArr, cks, title) {
    for (var a = 0; a < arr.length; a++) {
      for (var b = 0; b < checkArr.length; b++) {
        if (arr[a][cks] == checkArr[b]) {
          return { 'flag': true, 'message': arr[a][title] }
        }
      }

    }
    return { 'flag': false }
  }
  // setLocalStorage 中存储  key是要存储的地址名   val是要存储的内容
  setLocalStorage(key, val) {
    localStorage.setItem(key, JSON.stringify(val));
  }
  //getLocalStorage  获取本地存储数据 path 是要回去的地址名   key是要获取对象中的属性   支持多级获取vak.cks.de
  getLocalStorage(path, key) {
    var getLocalData = localStorage.getItem(path);
    var obj = JSON.parse(getLocalData);
    var arr = key.split(".");
    for (var a = 0; a < arr.length; a++) {
      obj = obj[arr[a]];
    }
    return obj;
  }
}
let utils = new Utils();
export default utils;