<template>
  <div class="sort-container">
    <div :class="[isValided? 'error-border' : 'common-container']">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}.</span>
        <span class="question-name-text">{{sortOption.name}}</span>
        <span class="starRed" v-if="sortOption.isMust === 1">*</span>
      </div>
      <div
        class="option-container"
        v-for="childitem in sortOption.optionsData"
        :key="childitem.id"
      >
        <span class="sort-text">{{getSortIndex(childitem)}}</span>
        <el-checkbox
          :label="childitem.id"
          @change="handleCheckBoxChange(childitem)"
          :disabled="isAnswered"
        >
        <div class="checkbox-text">{{childitem.name}}</div>
        </el-checkbox>
      </div>
    </div>
  </div>
</template>

<script>
	import notice from "../notice/notice.js"
  export default {
    props: {
      //当前题目的所有相关信息
      previewOption: {
        type: Object
      },
      //该题目在当前问卷中的序号
      index: {
        type: Number
      },
        //该题目是否已做答，true为已答过，false为未答
      isAnswered: {
        type: Boolean
      },
      //是否显示题目序号，true为显示，false为不显示
      isQuestionNum: {
        type: Boolean
      }
    },
    created() {
      this.sortOption = this.previewOption;
      if(this.previewOption.answers.length > 0){
        this.previewOption.answers.forEach((item) => {
          this.orderNum[item.sort-1]= { id: item.optionId, name: item.optionName}
        });
      }
    },
    data(){
      return{
        isAnswer: false, //标志当前题目是否已做答
        isValided: false,
        answerVal: [],
        sortOption: {},
        sortOption1: {
          name:"①排序题发过的二二二二不合格或更换",
          isMust:1,
          optionsData:[
            'sjah',
            'ojdisj',
            'kdjkajdjk',
            'kjxksd'
          ],
          rowCount:3 ////排序方式 5.横排 1.竖排 2.一排2个 3.一排3个 4.一排4个
        },
        orderNum: [] //用于显示排序的序号
      }
    },
    methods:{
      getSortIndex(val) {
        let checkedSortItem = -1;
        for (let index = 0; index < this.orderNum.length; index++) {
          const item = this.orderNum[index];
          if(item.id === val.id && item.name === val.name){
            checkedSortItem = index;
            break;
          }
        }
        // this.orderNum.forEach((item,index) => {
        //   if(item.id === val.id && item.name === val.name){
        //     checkedSortItem = index;
        //   }
        // });
        if (checkedSortItem >= 0) {//找到
          return checkedSortItem + 1;
        }else{
          return "";
        }
      },
      handleCheckBoxChange(val) {
        let checkedSortItem = -1;
        this.orderNum.forEach((item,index) => {
          if(item.id === val.id && item.name === val.name){
            checkedSortItem = index;
          }
        });
        if (checkedSortItem >= 0) {//找到
          this.orderNum.splice(checkedSortItem, 1);
        } else {
          this.orderNum.push({ id: val.id, name: val.name });
        }
        this.answerVal = this.orderNum.map((element,index) => {
          return {
            pvqId:this.sortOption.pvqId, //问卷id
            questionId:this.sortOption.id, //题目id
            questionType:this.sortOption.type, //题目类型
            optionId: element.id,
            optionName: element.name,
            sort: index+1
          }
        });
        this.isValided = false;
        this.isAnswer = this.orderNum.length === this.sortOption.optionsData.length ? true : false;
        // this.sortOption.isMust === 1 &&
        this.$parent.setAnswerPercentage(this.sortOption.id,this.isAnswer);
         //观察者触发
        notice.emitBrother("questionnaireObserver");
      },
      checkValided(){
        if(this.sortOption.isMust === 0){
          return false;
      }
        //判断当前题目是否必填，必填时才需要判断是否已答
        if(this.sortOption.isMust !== 1){
          return true;
        }
        //判断当前题目是否已答
        if(!this.isAnswer){
          return true;
        }
        //判断排序题目是否全部选中
        if(this.orderNum.length < this.sortOption.optionsData.length){
          return true;
        }
        return false;
      },
      	doValidedFlag(){
      return this.checkValided();
    },
      doValided() {
        this.isValided = this.checkValided();
        return this.isValided;
      }
    }
  }
</script>

<style lang="stylus" type="stylesheet/stylus">
.sort-container {
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
  }
  .question-name-text{
    font-weight: 600;
    font-size: 16px;
    line-height : 20px;
    color : #353535;
  }
  .starRed {
    color: red;
    font-weight: 600;
  }
  .option-container {
    display: flex;
    padding: 10px 0;
    align-items: center;
  }
  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }
  .error-border {
    border: 1px solid red;
    padding: 9px;
  }
  .sort-text {
    font-size :14px;
    margin-right :6px;
    position: relative;
    bottom: 5px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #5188FC;
    border-color:#5188FC;
  }
  //checkbox的label颜色
  .el-checkbox__input.is-checked+.el-checkbox__label {
    color: #5188FC;
  }
    // chebox在disable时的样式
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    border-color: #f5f7fa;
    background: #f5f7fa;
  }

  // 多选框容器
  .el-checkbox {
    margin-right: 0;
    display : flex;
    margin-bottom : 10px;
  }
  // checkbox文字
  .checkbox-text {
    word-break: normal;
    width: auto;
    display: block;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    line-height: 20px;
  }
  // 多选按钮位置
  .el-checkbox__inner{
    position: relative;
    top: calc( 50% - 7px)
  }
}
</style>
