import checkConfigItem from './checkConfigItem'
const configAr = {
    radio: {  //单选题
        checkBox: [
            checkConfigItem.requiredOrNot,
            checkConfigItem.canBeInput,
        ]
    },
    checkbox: {  //多选题
        checkBox: [
            checkConfigItem.requiredOrNot,
            checkConfigItem.atMostAndAtLeast,
            checkConfigItem.canBeInput,
        ]
    },
    input: { //填空题
        checkBox: [
            checkConfigItem.requiredOrNot,
            checkConfigItem.textCheck,
        ]
    },
    array: {  //矩阵题
        checkBox: [
            checkConfigItem.matrixIsNotEmpty,
        ]
    },
    // paragraph: {  //段落

    // },
    // sort: { //排序

    // },
    // select: {  //下拉选择

    // },
    // secondary: { //二级下拉选择

    // },
    // createQuestion: { //创建问卷名称

    // }

}
export default configAr;