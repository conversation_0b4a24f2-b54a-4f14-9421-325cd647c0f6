const checkConfigItem = {
    
    requiredOrNot: { //是否必填校验 requiredOrNot起调函数 isMust是否必填校验 value 0是否  1是是  mes 返回信息
        fun: "requiredOrNot",
        key: "isMust",
        value: "1",
        mes: "你没有填写呀"
    },
    canBeInput: { //可输入校验 requiredOrNot起调函数 isInput是否必填 value 0是否  1是是  mes 返回信息 max最大输入 min 最输入
        fun: "canBeInput",
        max: 10,
        key: "isInput",
        value: "1",
        min: -1,
        mes: "长度应当小于200"
    },
    matrixIsNotEmpty: {  //矩阵非空检验
        fun: "matrixIsNotEmpty",
        key: "isMust",
        value: "1",
        mes: "你没有填写呀"
    },
    atMostAndAtLeast: {  //多选框  atMostAndAtLeast起调函数   key 需要取值的key   max最多选择几个  minkey最少选择几个   mes错误提示信息
        fun: "atMostAndAtLeast",
        key: "123",
        maxKey: "maxSelect",
        minKey: "minSelect",
        mes: "最多选择和最少选择"
    },
    textCheck: { //填空题校验  fun 填空题中控函数  key需要取值的key  checkFlag需要校验的类型  row文本类型 number数字类型
        fun: "textCheck",
        key: "inputType",
        checkFlag: ['row', 'number'],
        row: {
            fun: "canBeInputRow",//二级起调函数 
            max: 10,
            key: "questionValue",
            mes: "长度应当小于500"
        },
        number: {
            fun: "maxAndMin",  //二级起调函数
            key: "questionValue",
            max: "maxValue",
            min: "minValue",
            mes: "超过限度"
        }

    }

}
export default checkConfigItem;