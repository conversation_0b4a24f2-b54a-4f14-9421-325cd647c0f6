class CheckObject {
    //文本中转函数
    textCheck(config, obj, checkReturnArr) {
        //判断文本框的类型
        if (config.checkFlag.indexOf(obj.question[config.key]) != -1) {
            return this[config[obj.question[config.key]].fun](config[obj.question[config.key]], obj, checkReturnArr);
        }
        return false;
    };

    // 填空题 数字类型校验
    maxAndMin(config, obj, checkReturnArr) {
        var max = obj.question[config.max];
        var min = obj.question[config.min];
        for (var a = 0; a < obj.answer.length; a++) {
            var num = obj.answer[a][config.key];
            if (!(num < max && num > min)) {
                this.loadFunction(config, obj, checkReturnArr);
                return true
            }
        }
        return false;
    }

    // 填空题 文本类型校验
    canBeInputRow(config, obj, checkReturnArr) {
        for (var a = 0; a < obj.answer.length; a++) {
            var str = obj.answer[a][config.key];
            if (str.length > config.max) {
                this.loadFunction(config, obj, checkReturnArr);
                return true
            }
        }
        return false
    }

    //最多选择和最少选择函数
    atMostAndAtLeast(config, obj, checkReturnArr) {
      
        //获取最小值
        var min = obj.question[config.minKey];
        //获取最大值
        var max = obj.question[config.maxKey];
        //获取答题数量
        var count = obj.answer.length;
        if (!(count >= min && count <= max)) {
            this.loadFunction(config, obj, checkReturnArr);
            return true;
        } else {
            return false
        }
    }
    //矩阵未填写校验
    matrixIsNotEmpty(config, obj, checkReturnArr) {
        console.log(config, obj, checkReturnArr, "-----------")
        if (obj.question[config.key] == config.value) {
            if (!obj.answer || obj.answer.length == 0) {
                this.loadFunction(config, obj, checkReturnArr);
                return true;
            }
        }
        //如果没有答案接下来的校验直接终止不走
        if (!obj.answer || obj.answer.length == 0) {
            return true;
        } else {
            return false
        }
    }

    //是否必填校验
    requiredOrNot(config, obj, checkReturnArr) {
        if (obj.question[config.key] == config.value) {
            if (!obj.answer || obj.answer.length == 0) {
                this.loadFunction(config, obj, checkReturnArr);
                return true;
            }
        }
        //如果没有答案接下来的校验直接终止不走
        if (!obj.answer || obj.answer.length == 0) {
            return true;
        } else {
            return false
        }

    }
    //长度校验
    canBeInput(config, obj, checkReturnArr) {
        //循环所有选项 找到答案的选项 获取 可输入配置
        for (var a = 0; a < obj.question.optionsData.length; a++) {
            var qustion = obj.question.optionsData[a];
            if (obj.answer.optionId == qustion.id) {
                if (qustion[config.key] == config.value) {
                    var str = obj.answer.textValue ? obj.answer.textValue : "";
                    //校验输入内容的长度
                    if (str.length > config.max) {
                        this.loadFunction(config, obj, checkReturnArr);
                        return true;
                    }
                }
                break;
            }
        }
        return false
    }

    //装载 数据函数
    loadFunction(config, obj, checkReturnArr) {
        var obj = {
            mes: config.mes,
            questionId: obj.question.id
        }
        checkReturnArr.push(obj);
        console.log(checkReturnArr)
    }
}
let checkObject = new CheckObject();
export default checkObject;