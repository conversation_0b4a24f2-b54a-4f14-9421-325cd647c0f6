<template>
  <div class="inner">
    <Header title="调查问卷" @backFun="goBack"></Header>
    <van-tabs @click="tabclick" class="tabs">
      <van-tab title="进行中" name="publish"> </van-tab>
      <van-tab title="已结束" name="recovery"></van-tab>
    </van-tabs>
    <div v-if="list.length > 0" class="hei">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div v-for="item in list" :key="item.id" class="item" @click="questionnaireDetails(item)">
            <div>
              <div class="accoutName">
                <div class="accout-title">{{ item.questionName || "-" }}</div>
                <div>
                  <span :class="item.answerTag == '0' ? 'normal' : item.answerTag == '1' ? 'unnormal' : 'otherStatus'"
                    >{{ item.answerTag == "0" ? "未开始" : item.answerTag == "1" ? "进行中" : "已结束" }}
                  </span>
                  <van-icon name="arrow" color="#C9CDD4" />
                </div>
              </div>
              <div class="child">
                <span class="child-text">发布部门</span>&nbsp;&nbsp;&nbsp;
                <span>{{ item.officeName || "-" }}</span>
              </div>
              <div class="child">
                <span class="child-text">发布时间</span>&nbsp;&nbsp;&nbsp;
                <span>{{ item.createTime || "-" }}</span>
              </div>
              <div class="child">
                <span class="child-text">开始日期</span>&nbsp;&nbsp;&nbsp;
                <span>{{ item.startTime || "-" }}</span>
              </div>
              <div class="child">
                <span class="child-text">结束日期</span>&nbsp;&nbsp;&nbsp;
                <span>{{ item.endTime || "-" }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { Toast } from "vant";

Vue.use(Toast);
export default {
  data() {
    return {
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      questionStatus: "publish",
      current: 1,
      size: 15,
      total: 0,
      list: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getList();
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    questionnaireDetails(row) {
      if (row.answerTag == "0") return Toast.fail("未开始，不可答题");
      if (row.answerTag == null && row.answerId == "-1") return Toast.fail("该问卷已超时");
      this.$router.push({
        path: "/replyQuestion",
        query: {
          type: "h5",
          questionName: row.questionName,
          questionId: row.questionId
        }
      });
    },
    tabclick(val) {
      this.questionStatus = val;
      this.loading = true;
      this.finished = false;
      this.current = 1;
      this.list = [];
      this.getList();
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getList();
    },
    onLoad() {
      console.log("执行了");
      this.finished = false;
      this.loading = true;
      this.current++;
      this.getList();
    },
    getList() {
      let params = {
        questionStatus: this.questionStatus,
        unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
        hospitalCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        userName: JSON.parse(localStorage.getItem("loginInfo")).staffName,
        pageNum: this.current,
        pageSize: this.size
      };
      this.$api.selfQuestionListToWx(params).then(res => {
        this.loading = false;

        if (res.code == 200) {
          console.log(res, "saaaasa");

          res.data.forEach(item => {
            this.list.push(item);
          });
          if (this.list.length >= res.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        }
      });
    }
  }
};
</script>

<style scoped lang="stylus">
.inner{
  height:100vh
  background-color: #F2F4F9
    overflow hidden
    .item{
      overflow: auto;
    background-color: #fff
    margin:15px .1875rem .1875rem
    padding:.3125rem
    border-radius: .125rem
    font-size: 16px;
    color:#1D2129 ;
    font-family: PingFang SC-Medium
    line-height :20px
    .accoutName{

      display: flex;
      justify-content space-between
      >div:nth-child(1){
        flex: 1
        overflow: hidden;
 white-space: nowrap;
text-overflow: ellipsis;

      }
    }
    .child{
      margin-top:.1875rem
    }
  }
}
.normal{
  display: inline-block
  width 36px
  height: 24px
  background-color #FFF7E8
  color:#FF7D00
  border-radius: 5px;
padding:2px 5px;
  font-size: 12px;
  text-align center;
   line-height:24px;
  border-radius: 5px;
  vertical-align: middle
}
.unnormal{
  display: inline-block
  width 36px
  height: 24px
  background-color #E8FFEA
  color:#00B42A
  border-radius: 5px;
  padding:2px 5px;
  font-size: 12px
   line-height:24px;
  border-radius: 5px;
  text-align center;
  vertical-align: middle
}
.otherStatus{
  display: inline-block;
  width :36px;
  height: 24px;
  line-height:24px;
  border-radius: 5px;
  background-color: #F2F3F5;
  color:#4E5969;
  padding:2px 5px;
  font-size: 12px
  text-align center
  vertical-align: middle
}
/deep/ .van-tabs__line{
  background-color: #3562DB;
}
.tabs{
  height:40px;
}
 .hei{
    height:calc(100% - 120px);
    margin-bottom:40px;
    overflow:auto;
    }
      .notList {
    position: relative;
    height: calc(90% - 1.24rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url('../../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
</style>
