<template>
  <div class="inner">
    <Header title="调查问卷" @backFun="goback()"></Header>
    <div class="my-content ques-content-box">
      <el-progress v-show="questionPreviewList.isProgress == 1 &&
        questionPreviewList.answeredId === '-1'
        " color="#5188FC" :text-inside="true" :stroke-width="13" :percentage="answerPercentage"
        status="success"></el-progress>
      <div class="div">{{ title }}</div>
      <div :class="[
        'main-container',
        questionPreviewList.isProgress == 1
          ? 'main-container-progress'
          : 'main-container-noProgress'
      ]">
        <div v-show="questionPreviewList.startText" class="question-preText">
          {{ questionPreviewList.startText }}
        </div>
        <!-- 问卷题目列表 -->
        <div class="list">
          <div v-for="qSubject in questionPreviewList.questions" :key="qSubject.id" class="questliststy">
            <!-- questionPreviewList.answeredId表示当前问卷是否已做答，-1表示未答 -->
            <component :previewOption="qSubject" :list="questionPreviewList" :index="qSubject.numIndex" ref="child"
              :isAnswered="btnDisabled()" :isQuestionNum="questionPreviewList.isQuestionNum == 1"
              :is="questionPreview[qSubject.type]" :isChecks="isCheck" @biank="biank" />
          </div>
        </div>
      </div>
      <div class="submit-container" v-show="questionPreviewList.questions &&
        questionPreviewList.questions.length !== 0
        ">
        <el-button type="primary" style="width: 80%; background: #3562db;z-index:9999" :class="flag ? '' : 'buttons'"
          @click="submitQuestinAnswer" :disabled="!flag" v-if="questionPreviewList.answeredId == '-1'">提交</el-button>
        <el-button type="primary" style="width: 80%; background: #3562db;z-index:9999;opacity:0.8" v-else
          disabled>已提交</el-button>
        <!-- <el-button
          type="primary"
          style="width: 80%"
          @click="submitQuestinAnswer"
          :disabled="btnDisabled()"
        >提交</el-button>-->
      </div>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { Toast } from "vant";
import { Dialog } from 'vant';
Vue.use(Toast);
Vue.use(Dialog);

import YBS from "@/assets/utils/utils.js";
import PreviewMatrix from "./components/previewMatrix";
import PreviewFillBlank from "./components/PreviewFillBlank";
import PreviewRadio from "./components/PreviewRadio";
import PreviewCheckBox from "./components/PreviewCheckBox";
import PreviewSelect from "./components/PreviewSelect";
import PreviewMulSelect from "./components/PreviewMulSelect";
import PreviewSort from "./components/PreviewSort";
import PreviewParagraph from "./components/PreviewParagraph";
import moment from "moment";
import notice from "./notice/notice.js";
export default {
  components: {
    PreviewMatrix,
    PreviewFillBlank,
    PreviewRadio,
    PreviewCheckBox,
    PreviewSelect,
    PreviewMulSelect,
    PreviewSort,
    PreviewParagraph
  },
  data() {
    return {
      flag1: true,
      flag: true,
      questionPreview: {
        radio: "PreviewRadio",
        checkbox: "PreviewCheckBox",
        input: "PreviewFillBlank",
        array: "PreviewMatrix",
        paragraph: "PreviewParagraph",
        sort: "PreviewSort",
        select: "PreviewSelect",
        nd_select: "PreviewMulSelect"
      },
      questionPreviewList: {}, //当前问卷的题目列表
      openTime: "",
      answerPercentageStep: 0, //进度条的步长
      answerPercentage: 0, //进度条的进度
      questionSubjectPercentage: {},
      questionId: "",
      questionInfor: '',
      test: "", //签名组件
      currentParams: {}, //提交参数
      signatureImg: "", //签名图片
      forceSign: "", //是否需要签名
      minQuestionList: "",
      maxQuestionList: "",
      answerRecordsList: "",
      checkData: "",
      isCheck: false,
      flags: false,
      questionItem: JSON.parse(localStorage.getItem("currentQuestionItem")),
      title: this.$route.query.questionName,
      fromPage: this.$route.query.type || ""
    };
  },
  mounted() {
    console.log(this.$route.query.id, 'this.$route.query.questionId000000000000');

    this.getList();
    // this.$YBS.apiCloudEventKeyBack(this.goback);
    this.openTime = moment(this.obtainEast8Date()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
  },
  methods: {
    biank(val) {
      console.log(val, "vvvvvvvv");
      this.flag1 = val;
    },
    goback() {
      let pageSouce = this.$route.query.pageSouce;
      if (pageSouce == "apicloud") {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    //提交答题结果
    submitQuestinAnswer() {
      const childList = this.$refs.child;
      let answerRecords = [];
      let isBreak = [];
      for (let index = 0; index < childList.length; index++) {
        const item = childList[index];
        // 调用子组件的doValided方法进行题目验证，子组件必须有doValided和checkValided方法，详见子组件
        const isValided = item.doValided();
        console.log(isValided,'isValided');
        
        if (item.$vnode.tag.indexOf("Paragraph") < 0) {
          if (isValided) {
            isBreak.push(index);
          } else {
            answerRecords = answerRecords.concat(item.answerVal);
            console.log(answerRecords,'answerRecordsanswerRecordsanswerRecords');
            
          }
        }
      }
      //校验校验多选项答题规范
      let queList = this.questionPreviewList.questions;
      console.log("题目", queList);
      this.flags = false;
      if (queList && queList.length) {
        queList.forEach((item, index) => {
          if (item.lengthArr && item.lengthArr.length) {
            if (
              (item.maxSelect !== "" &&
                item.maxSelect !== item.minSelect &&
                item.lengthArr.length > item.maxSelect) ||
              item.lengthArr.length < item.minSelect
            ) {
              console.log("不规范呀呀呀");
              this.isCheck = true;
              this.$set(item, "checksed", true);
              this.flags = true;
              // return false;
            } else {
              this.isCheck = false;
              this.$set(item, "checksed", false);
              console.log("规范");
              this.flags = false;
              return true;
            }
          }
        });
      }
      //对单个题目进行校验
      // console.log(this.minQuestionList,this.maxQuestionList)
      // if(answerRecords.length<this.minQuestionList || answerRecords.length>this.maxQuestionList){
      //   this.$message.error("您未作答！请您作答任意一个题均可");
      //   return;
      // }

      //校验（题目做大是否符合规范）
      // checkCentralControl.checkMainFunction(this.questionPreviewList.questions,answerRecords);

      if (isBreak.length === 0) {
        //当前问卷题目均为非必答题时，阻止未做答任何题目点击提交
        if (answerRecords.length === 0) {
          Toast.fail("您未作答！请您作答任意一个题均可");
          // this.$message.error("您未作答！请您作答任意一个题均可");
          return;
        }
        if (this.flags) return Toast.fail("题目作答不符合答题规范！"); //多选题作答不符合规范提示
        const loginInfo = localStorage.getItem("loginInfo");
        const currentLoginInfo = loginInfo ? JSON.parse(loginInfo) : {};
        if (!currentLoginInfo) {
          params.subName = currentLoginInfo.staffName,
            params.userId = JSON.parse(localStorage.getItem("loginInfo")).staffId
          params.userName = JSON.parse(localStorage.getItem("loginInfo")).staffName
        }
        const params = {
          questionId: this.questionInfor.id, //当前问卷ID
          // questionId: this.$route.query.questionId, //当前问卷ID
          openTime: this.openTime, //问卷开始做答的时间
          subTime: moment(this.obtainEast8Date()).format("YYYY-MM-DD HH:mm:ss"), //问卷做答结束的时间
          deviceType: "phone",
          // userId: "", //当前答卷人的ID
          answerRecords: JSON.stringify(answerRecords),
        };

        this.currentParams = params;
        this.saveApi();
      }
    },
    saveApi() {
      let params = this.currentParams;
      if (this.flag1) {
        this.$api.saveAnswer(params).then(res => {
          if (
            // this.questionPreviewList.isProgress == 1 &&
            this.questionPreviewList.endText != ""
          ) {
            Dialog.alert({
              message: `${this.questionPreviewList.endText}`
            }).then(() => {
              if (this.fromPage == "h5") {
                this.$router.go(-1);
              } else {
                // api.closeFrame();
                this.$YBS.apiCloudCloseFrame();
              }
              // on close
            });
          } else {
            Toast.success("提交成功");
            this.$YBS.apiCloudCloseFrame();
            // if (this.fromPage == "h5") {
            //   this.$router.go(-1);
            // } else {
            //   // api.closeFrame();
            //   this.$YBS.apiCloudCloseFrame();
            // }
          }

          // if (this.fromPage == "h5") {
          //   this.$router.go(-1);
          // } else {
          //   this.$YBS.apiCloudCloseFrame();
          // }
        }).catch( error => {
          Toast.fail(error.data.message)
        })
      } //多选题作答不符合规范提示
      //       this.$api.saveAnswer(params).then(res => {
      // Toast.success("提交成功");
      // this.$router.go(-1)
      //       });
    },

    showQuestionEndText() {
      const _this = this;
      //没有后置说明时弹框
      if (_this.questionPreviewList.endText.length === 0) {
        _this.showAlert();
        return;
      }
      this.$createDialog(
        {
          type: "alert",
          onConfirm: (e, promptValue) => {
            _this.$router.go(-1);
          }
        },
        createElement => {
          return [
            createElement(
              "div",
              {
                class: {
                  "endText-title": true
                },
                slot: "title"
              },
              [createElement("p", _this.questionPreviewList.name)]
            ),
            createElement(
              "div",
              {
                class: {
                  "endText-content": true
                },
                slot: "content"
              },
              _this.questionPreviewList.endText
            )
          ];
        }
      ).show();
    },
    showAlert() {
      const _this = this;
      this.$createDialog(
        {
          type: "alert",
          content: "答题成功",
          onConfirm: (e, promptValue) => {
            _this.$router.go(-1);
          }
        },
        createElement => {
          return [
            createElement(
              "div",
              {
                class: {
                  "endText-title": true
                },
                slot: "title"
              },
              [createElement("p", _this.questionPreviewList.name)]
            )
          ];
        }
      ).show();
    },
    getAnswerPercentageStep() {
      let questionSubjectLength = 0;
      this.questionPreviewList.questions.forEach(qSubject => {
        // qSubject.isMust === 1 && questionSubjectLength++;
        questionSubjectLength++;
      });
      this.answerPercentageStep = Math.ceil(100 / questionSubjectLength);
    },
    setAnswerPercentage(questionSubjectId, isAnswer) {

      if (this.questionPreviewList.isProgress != 1) {
        return;
      }
      this.answerPercentage = 0;
      if (isAnswer) {
        this.questionSubjectPercentage[
          questionSubjectId
        ] = this.answerPercentageStep;
      } else {
        this.questionSubjectPercentage.hasOwnProperty(questionSubjectId) &&
          delete this.questionSubjectPercentage[questionSubjectId];
      }
      for (const key in this.questionSubjectPercentage) {
        if (this.questionSubjectPercentage.hasOwnProperty(key)) {
          const element = this.questionSubjectPercentage[key];
          this.answerPercentage =
            this.answerPercentage + element > 100
              ? 100
              : this.answerPercentage + element;
        }
      }
    },
    getList() {
      const loginInfo = localStorage.getItem("loginInfo");
      const currentLoginInfo = loginInfo ? JSON.parse(loginInfo) : {};
      if (!currentLoginInfo) {
        params.unitCode = currentLoginInfo.unitCode
        params.hospitalCode = currentLoginInfo.hospitalCode
        params.userId = currentLoginInfo.userId
      }
      let params = {
        questionId: this.$route.query.id,
      };
      this.$api.getPaperQuestions(params).then(res => {
        console.log(res,'RES');
        this.questionInfor = res
        const questionList = res.questions;
        this.maxQuestionList = res.questions.maxSelect; //最多可选择的选项
        this.minQuestionList = res.questions.minSelect; //最少可选择的选项
        const previewData = [];
        let numIndex = 0;
        this.forceSign = res.forceSign;
        this.signatureImg = res.signature;
        for (let index = 0; index < questionList.length; index++) {
          const item = questionList[index];
          if (item.type === "paragraph") {
            previewData.push({ ...item, numIndex: null });
          } else {
            numIndex++;
            previewData.push({ ...item, numIndex });
          }
        }
        this.questionPreviewList = { ...res, questions: previewData };
        console.log(this.questionPreviewList, "this.questionPreviewList");
        const { answeredId, statusRun, status } = this.questionPreviewList;
        if (answeredId == -1 && statusRun == 0)
          return toast(this, `问卷已暂停`, "error");
        if (answeredId == -1 && status == "recovery")
          return toast(this, `问卷已结束`, "error");
        this.getAnswerPercentageStep();
      });
    },

    btnDisabled() {
      const { answeredId, statusRun, status } = this.questionPreviewList;
      // statusRun==0 暂停 status=='recovery'完成
      return answeredId != -1 || statusRun == 0 || status == "recovery";
    },
    // 获取东八时区
    obtainEast8Date() {
      var timezone = 8; //目标时区时间，东八区   东时区正数 西市区负数
      var offset_GMT = new Date().getTimezoneOffset(); // 本地时间和格林威治的时间差，单位为分钟
      var nowDate = new Date().getTime(); // 本地时间距 1970 年 1 月 1 日午夜（GMT 时间）之间的毫秒数
      var targetDate = new Date(
        nowDate + offset_GMT * 60 * 1000 + timezone * 60 * 60 * 1000
      );
      return targetDate;
    }
  }
};
</script>

<style scoped lang="stylus">
.inner{
  height:100%
  background-color #F2F4F9
}
.buttons{
  background-color:#cccccc !important;
  border-color:#cccccc !important;
}
.ques-content-box {
  margin-bottom:50px;
  bottom: 50px;
  background: #fff;

  .resign-txt {
    font-style: normal;
    margin: auto 0;
  }

  .resign {
    flex: 1;
    text-align: center;
    display: flex;
  }

  .sign-img {
    width: 60px;
    height: 60px;
    margin: auto;
    transform: rotate(90deg);
  }
  .my_flex {
    display: flex;
    align-items: center;
    background: #FFF;
    padding: 16px;
    justify-content: space-between;

    .sign_title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(53, 53, 53, 1);
    }
  }
  .signature_edit {
    width: 252px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #5188FC;

    .no_singature {
      display: inline-block;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #5188FC;
      line-height: 22px;
    }

    img {
      width: 16px;
      margin-right: 4px;
    }
  }
}

.my_signature {
  background: #FFF;
  padding: 0px 20px;
  text-align: center;

  img {
    // padding:20px;
    width: 100px;
    transform: rotate(90deg);
  }
}

// 后置说明的标题
.endText-title {
  padding: 10px 20px;
  font-size: 18px;
  text-align: justify;
  text-justify: unset;
  line-height: normal;
}

.endText-content {
  padding: 0 20px;
  font-size: 14px;
  text-align: justify;
  text-justify: unset;
  line-height: normal;
  height: 180px;
  overflow-y: auto;
}

// .main-container-progress {
// height: calc(100vh - 68px);
// }

// .main-container-noProgress {
// height: calc(100vh - 52px);
// }
.main-container {
  background-color: #fff;
  overflow-y: auto;

  .question-content {
    background-color: #f0f0f4;
  }

  .preview-container {
    background-color: #fff;
    border-bottom: 1px solid #d8dee7;
    margin: 10px 0px;
    padding: 0 10px;
  }

  .question-preText {
    padding: 20px 0;
    border-bottom: 1px solid #f0f0f4;
    text-align: justify;
    text-justify: unset;
    padding: 10px;
    line-height: normal;
  }

  .question-endText {
    padding-bottom: 20px;
    text-align: center;
  }
}

.submit-container {
  // padding: 10px;
  height: 50px;
  position: fixed;
  bottom: 0px;
  width: 100%;
  text-align: center;
  align-items: center;
  justify-content: center;
  display: flex;
  background: #fff;

  .el-button--primary {
    color: #fff;
    background-color: #5188FC;
    border-color: #5188FC;
  }

  .el-button--primary.is-active, .el-button--primary:active {
    background: #5188FC;
    border-color: #5188FC;
    color: #FFF;
  }

  .el-button--primary:focus, .el-button--primary:hover {
    background: #5188FC;
    border-color:#5188FC;
    color: #FFF;
  }

  .el-button--primary.is-disabled, .el-button--primary.is-disabled:active, .el-button--primary.is-disabled:focus, .el-button--primary.is-disabled:hover {
    background-color: #69cecc;
    border-color: #69cecc;
  }
}

.el-progress-bar__outer, .el-progress-bar__inner {
  border-radius: 0px !important;
}
.div{
  background-color:#e6effc
  line-height: 40px;
  padding:0 15px;
  text-align: center;
     overflow: hidden;
 white-space: nowrap;
text-overflow: ellipsis;
}
.list{
  width:100%
  background-color #F2F4F9
padding-bottom 55px !important;

.questliststy{
  width:95%
  background-color #fff
  margin 0 auto 10px
  border-radius: 10px
}
}

</style>
