<template>
  <div class="wrapper">
    <Header title="教育培训" @backFun="goback"></Header>
    <div class="content">
      <div class="card-item" v-for="item in filterMenuList" :key="item.value" @click="goPage(item.name)">
        <van-badge :content="item.count" max="99">
          <img :src="item.img" />
        </van-badge>
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { mapState } from "vuex";
import { Toast } from "vant";
Vue.use(Toast);
export default {
  data() {
    return {
      filterMenuList: [],
      menuList: [
        {
          value: "1",
          name: "调查问卷",
          img: require("../../assets/images/调查问卷.png"),
          count: "",
          path: "/ihcrsYBSApp/questionnaireList"
        },
        {
          value: "2",
          name: "在线考试",
          img: require("../../assets/images/在线考试.png"),
          count: "",
          path: '/ihcrsYBSApp/onlineTest'
        },
        {
          value: "3",
          name: "知识库",
          img: require("../../assets/images/知识库.png"),
          count: "",
          path: '/ihcrsYBSApp/knowlage'
        },
        {
          value: "4",
          name: "学习记录",
          img: require("../../assets/images/学习记录.png"),
          count: "",
          path: '/ihcrsYBSApp/studyRecord'
        }
      ],
      timer: null
    };
  },
   computed: {
    ...mapState(["loginInfo", "staffInfo"]),
  },
  mounted() {
    this.getMenuAuth()
    this.$YBS.apiCloudEventKeyBack(this.goback);

    // this.getQuestionnaireCount();
  },
  methods: {
    // 获取菜单权限
    getMenuAuth() {
      const params = {
        state: "0",
        userId: this.loginInfo.id,
      };
      this.$api.getMenuAuth(params).then((res) => {
        const authMenu = this.$YBS.treeToList(res || []);
          // .find((i) => i.menuId == 1000)
          // .children.find((k) => k.menuId == 1004)
          // .children.find((j) => j.menuId == 280).children;
        this.filterMenuList = this.menuList.filter((item) => {
          return authMenu.some((i) => i.pathUrl == item.path);
        });
        console.log(this.filterMenuList, 'this.filterMenuList');
        this.timer = setInterval(() => {
          if (this.staffInfo.staffId && this.filterMenuList.length) {
            this.getQuestionnaireCount();
            clearInterval(this.timer);
          }
        }, 5);
      });
    },
    goPage(val) {
      if (val != "调查问卷") {
        Toast.fail("模块开发中");
      } else {
        this.$router.push({
          path: "/questionnaireList"
        });
      }
    },
    getQuestionnaireCount() {
      let params = {
        questionStatus: "publish",
        unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
        hospitalCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        userName: JSON.parse(localStorage.getItem("loginInfo")).staffName
      };
      this.$api.selfQuestionCount(params).then(res => {
        if (res > 0) {
          this.filterMenuList[0].count = res;
        } else {
          this.filterMenuList[0].count = "";
        }
      });
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  background-color: #f2f4f9;
  height: 100vh;
}
.content {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  padding: 6px;
}
.card-item {
  width: 29vw;
  height: 32vw;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: calc((100% - 29vw * 3) / 3 / 2);
  // margin-bottom: 16px;
}
.card-item img {
  width: 40px;
}
.card-item span {
  transform: translateY(12px);
}
</style>
