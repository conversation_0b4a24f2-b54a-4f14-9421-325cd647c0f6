<template>
  <div class="spaceInventory-view">
    <Header title="空间盘点" @backFun="goback"></Header>
  </div>
</template>

<script>
export default {
  name: 'spaceInventory',
  data() {
    return {

    }
  },
  computed: {

  },
  mounted() {

    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {

  },
  methods: {
    goback() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
}

</script>

<style lang="scss" scoped>
.spaceInventory-view{

}
</style>
