<template>
  <div class="inner">
    <Header title="空间变更记录" @backFun="goback"></Header>
    <div v-if="spaceList.length > 0">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="lump" v-for="item in spaceList" :key="item.id">
            <div style="color:#262930">空间编码：{{ item.modelCode }}</div>
            <div class="commonality">
              <p class="left">空间本地名称</p>
              <p>{{ item.localSpaceName }}</p>
            </div>
            <div class="commonality">
              <p class="left">本地编码</p>
              <p>{{ item.localSpaceCode }}</p>
            </div>
            <div class="commonality">
              <p class="left">功能类型</p>
              <p>{{ item.functionDictName }}</p>
            </div>
            <div class="commonality">
              <p class="left">归属部门</p>
              <p>{{ item.dmName }}</p>
            </div>
            <div class="commonality">
              <p class="left">空间责任人</p>
              <p>{{ item.principalName }}</p>
            </div>
            <div class="commonality">
              <p class="left">操作人</p>
              <p>{{ item.opretionName }}</p>
            </div>
            <div class="commonality">
              <p class="left">变更日期</p>
              <p>{{ item.opretionTime }}</p>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      spaceList: [],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.spacePageList();
  },
  methods: {
    spacePageList() {
      let data = {
        ...this.pagination,
        spaceIds: this.$route.query.roomCode
      };
      this.$api.spacePageList(data).then(res => {
        this.loading = false;
        res.records.forEach(item => {
          this.spaceList.push(item);
        });
        if (this.spaceList.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    onRefresh() {
      this.pagination.current = 1;
      this.finished = false;
      this.loading = true;
      this.spaceList = [];
      this.spacePageList();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.pagination.current++;
      this.spacePageList();
    },
    goback() {
      this.$YBS.apiCloudCloseFrame();
    }
  }
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f2f4f8;
  min-height: 100vh;
  .lump {
    margin: 10px auto;
    width: 90%;
    height: 240px;
    background-color: #fff;
    padding: 10px;
    border-radius: 10px;
    > div {
      line-height: 30px;
    }
  }
}
.commonality {
  display: flex;
  .left {
    width: 30%;
    color: #9b9fa8;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.notList {
  position: relative;
  height: calc(100vh - 1.44rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    background: url("../../assets/images/equipmentManagement/缺省@2x.png") 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
