<template>
  <div class="taskDetails-view">
    <Header title="清查任务详情" @backFun="goback"></Header>
    <div class="view-list">
      <van-pull-refresh v-model="pullLoading" success-text="刷新成功" @refresh="getDataList('init')">
        <van-list v-model="listLoading" :finished="listFinished" finished-text="没有更多了" @load="getDataList">
          <div class="task-info">
            <div class="info-title">
              <div class="title-left">
                <p class="left-text">{{ dataInfo.inventoryTaskName }}</p>
                <p class="left-code">{{ dataInfo.inventoryTaskNo }}</p>
              </div>
              <p class="title-right" :style="{background: dataInfo.inventoryStatus ? stateInfo[dataInfo.inventoryStatus].color : ''}">{{ dataInfo.inventoryStatus ? stateInfo[dataInfo.inventoryStatus].name : '' }}</p>
            </div>
            <div class="info-main">
              <div class="item-left">
                <img src="../../../assets/images/zongKJ-icon.png">
                <div style="padding-left: 10px;">
                  <p class="left-text">总空间</p>
                  <p class="left-num">{{ dataInfo.departNumPo.totalNum }}</p>
                </div>
              </div>
              <div class="item-right">
                <div class="right-item" style="background: #FFECE8;">
                  <p class="item-text">未盘</p>
                  <p class="item-num" style="color: #F53F3F;">{{ dataInfo.departNumPo.unconfirmedTotalNum }}</p>
                </div>
                <div class="right-item" style="background: #F2F3F5;">
                  <p class="item-text">已盘</p>
                  <p class="item-num" style="color: #1D2129;">{{ dataInfo.departNumPo.confirmedTotalNum }}</p>
                </div>
              </div>
            </div>
            <div class="info-footer">
              <div class="footer-item">
                <p class="footer-label">计划周期</p>
                <p class="footer-value">{{ dataInfo.taskStartTime + ' - ' + dataInfo.taskEndTime }}</p>
              </div>
              <div class="footer-item">
                <p class="footer-label">责任人</p>
                <p class="footer-value">{{ dataInfo.responsibilityPerson }}</p>
              </div>
            </div>
          </div>
          <div v-for="item in dataList" :key="item.id" class="list-item" @click="goToDeptDetails(item)">
            <p class="item-title">{{ item.departName }}</p>
            <div class="item-main">
              <p style="width: 40%;">
                <span>总空间</span>
                <span style="color: #3562DB;">{{ item.departNumPo.totalNum }}</span>
              </p>
              <p style="width: 30%; padding-left: 6px;">
                <span>未盘</span>
                <span style="color: #F53F3F;">{{ item.departNumPo.unconfirmedTotalNum }}</span>
              </p>
              <p style="width: 30%;">
                <span>已盘</span>
                <span style="color: #1D2129;">{{ item.departNumPo.confirmedTotalNum }}</span>
              </p>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
export default {
  name: 'taskDetails',
  data() {
    return {
      pullLoading: false, // 下拉刷新
      listLoading: false, // 列表加载
      listFinished: false, // 列表加载完成
      dataInfo: {departNumPo: {}},
      dataList: [],
      stateInfo: {
        1: { color: '#F7BA1E', name: '未开始' },
        2: { color: '#00B42A', name: '进行中' },
        3: { color: '#C9CDD4', name: '已结束' }
      },
      pageParams: {
        currentPage: 0,
        pageSize: 10
      }
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {

  },
  methods: {
    goToDeptDetails(item) {
      this.$router.push({
        path: '/deptDetails',
        query: {
          departId: item.id
        }
      })
    },
    getDataList(type) {
      if (type == 'init') {
        this.dataList = []
        this.pageParams.currentPage = 1
      } else {
        this.pageParams.currentPage += 1
      }
      let params = {
        inventoryId: this.$route.query.id,
        pageParams: this.pageParams
      }
      this.$api.GetDeptTaskDetails(params).then(res => {
        this.dataInfo = res.inventory
        this.dataList = this.dataList.concat(res.list)
        this.pullLoading = false
        this.listLoading = false
        if (this.pageParams.currentPage >= Math.ceil(res.total / this.pageParams.pageSize)) {
          this.listFinished = true
        } else {
          this.listFinished = false
        }
      });
    },
    goback() {
      this.$router.go(-1);
    }
  }
}

</script>

<style lang="scss" scoped>
.taskDetails-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  .view-list {
    flex: 1;
    background: #F2F4F9;
    padding: 0px 10px;
    overflow-y: auto;
    .task-info {
      margin-top: 10px;
      padding: 16px;
      border-radius: 8px;
      background: #fff;
      .info-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title-left {
          flex: 1;
        }
        .title-right {
          padding: 3px 6px;
          font-size: 12px;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 18px;
          border-radius: 2px;
          margin-left: 10px;
        }
        .left-text {
          font-size: 16px;
          font-weight: bold;
          color: #1D2129;
          line-height: 22px;
        }
        .left-code {
          font-size: 12px;
          font-weight: 400;
          color: #86909C;
          line-height: 17px;
        }
      }
      .info-main {
        display: flex;
        justify-content: space-between;
        padding-top: 10px;
        .item-left {
          width: calc(50% - 5px);
          padding: 26px 0px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #E6EFFC;
          border-radius: 8px;
          img {
            width: 28px;
            height: 28px;
          }
          .left-text {
            font-size: 14px;
            font-weight: 400;
            color: #4E5969;
            line-height: 20px;
          }
          .left-num {
            font-size: 18px;
            font-weight: bold;
            color: #3562DB;
            line-height: 25px;
          }
        }
        .item-right {
          width: calc(50% - 5px);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .right-item {
            height: calc(50% - 5px);
            border-radius: 8px;
            padding-left: 16px;
            display: flex;
            align-items: center;
          }
          .item-text {
            font-size: 14px;
            font-weight: 400;
            color: #4E5969;
            line-height: 20px;
            background: transparent;
          }
          .item-num {
            margin-left: 8px;
            font-size: 18px;
            font-weight: bold;
            line-height: 25px;
          }
        }
      }
      .info-footer {
        padding-top: 8px;
        .footer-item {
          padding: 6px 0px;
          display: flex;
          justify-content: space-between;
        }
        .footer-label {
          font-size: 14px;
          font-weight: 300;
          color: #4E5969;
          line-height: 20px;
        }
        .footer-value {
          font-size: 14px;
          font-weight: 400;
          color: #1D2129;
          line-height: 20px;
        }
      }
    }
    .list-item {
      padding: 19px 16px 16px;
      background: #FFFFFF;
      border-radius: 8px;
      margin-top: 10px;
      .item-title {
        font-size: 16px;
        font-weight: bold;
        color: #1D2129;
        line-height: 22px;
      }
      .item-main {
        display: flex;
        p {
          text-align: left;
          padding: 10px 0px;
          line-height: 25px;
          position: relative;
          box-sizing: border-box;
          span {
            display: inline-block;
          }
          span:first-child {
            font-size: 14px;
            font-weight: 400;
            color: #4E5969;
          }
          span:last-child {
            font-size: 18px;
            font-weight: bold;
            margin-left: 4px;
          }
        }
        p:first-child {
          padding-right: 10px;
        }
        p:first-child::after {
          content: '';
          height: 18px;
          width: 1px;
          background: #E5E6EB;
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    .van-pull-refresh{
      height: calc(100% - 10px);
      overflow: initial;
    }
  }
}
</style>
