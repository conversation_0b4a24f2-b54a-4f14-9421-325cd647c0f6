<template>
  <div class="idleSpace-view">
    <Header title="空间台账" @backFun="goback"></Header>
    <div class="view-head">
      <img src="../../../assets/images/zongKJ-icon.png">
      <p>
        闲置房间
        <span>{{ pageParams.total }}</span>
        间
      </p>
    </div>
    <div class="view-table">
      <div class="thead">
        <div v-for="item in tableColumn" :key="item.label" class="th" :style="{minWidth: item.width + 'px'}">{{ item.label }}</div>
      </div>
      <div class="tbody">
        <van-pull-refresh v-model="pullLoading" success-text="刷新成功" @refresh="getDataList('init')">
          <van-list v-model="listLoading" :finished="listFinished" finished-text="没有更多了" @load="getDataList">
            <div v-for="(item, index) in dataList" :key="item.id" class="tr" @click="selectFinish(item)">
              <div v-for="(v, i) in tableColumn" :key="index + i" class="td" :style="{width: v.width + 'px'}">
                <span v-if="v.prop == 'ssmName'" style="color: #3562DB;" @click.stop="goToDetails(item)">{{ item[v.prop] }}</span>
                <span v-else>{{ item[v.prop] }}</span>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'idleSpace',
  data() {
    return {
      pullLoading: false, // 下拉刷新
      listLoading: false, // 列表加载
      listFinished: false, // 列表加载完成
      dataList: [],
      pageParams: {
        currentPage: 0,
        pageSize: 10,
        total: 0
      },
      tableColumn: [
        {label: '空间本地名称', prop: 'ssmName', width: 110},
        {label: '空间本地编码', prop: 'localSpaceCode', width: 110},
        {label: '建筑面积', prop: 'area', width: 80},
        {label: '功能类型', prop: 'functionDictName', width: 80},
        {label: '位置', prop: 'simName', width: 150},
      ]
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {

  },
  methods: {
    selectFinish(item) {
      this.$store.commit("setSpaceRegistration", item)
      this.$router.go(-1)
    },
    goToDetails(row) {
      this.$router.push({
        path: "/spaceDetails",
        query: {
          id: row.id
        }
      });
    },
    getDataList() {
      let loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
      this.pageParams.currentPage += 1
      let params = {
        hospitalCode: loginInfo.hospitalCode,
        unitCode: loginInfo.unitCode,
        current: this.pageParams.currentPage,
        size: this.pageParams.pageSize,
        spaceState: 0
      };
      this.$api.spaceList(params).then(res => {
        this.pageParams.total = res.total
        this.dataList = this.dataList.concat(res.records)
        this.pullLoading = false
        this.listLoading = false
        if (this.pageParams.currentPage >= res.pages) {
          this.listFinished = true
        } else {
          this.listFinished = false
        }
      });
    },
    goback() {
      this.$router.go(-1)
    }
  }
}

</script>

<style lang="scss" scoped>
.idleSpace-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #F2F4F9;
  .view-head {
    margin: 10px;
    background: #fff;
    border-radius: 8px;
    padding: 16px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 28px;
      height: 28px;
      margin-right: 16px;
    }
    p{
      line-height: 25px;
      font-size: 14px;
      font-weight: 400;
      color: #86909C;
      span {
        display: inline-block;
        font-size: 18px;
        font-weight: bold;
        color: #1D2129;
      }
    }
  }
  .view-table {
    flex: 1;
    overflow: auto;
    .thead {
      display: flex;
      .th {
        z-index: 1;
        box-sizing: border-box;
        background: #E6EFFC;
        padding: 12px 8px;
        font-size: 15px;
        font-weight: 400;
        color: #1D2129;
        line-height: 20px;
      }
    }
    .tbody {
      display: flex;
      .tr {
        display: flex;
        background: #fff;
        border-bottom: 1px solid #E5E6EB;
        .td {
          padding: 16px 8px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
        }
        span {
          width: 100%;
          word-wrap: break-word;
          white-space: pre-line;
          overflow-wrap: break-word;
        }
      }
      .tr:hover {
        background: #F2F4F9;
      }
      .van-pull-refresh{
        overflow: initial;
      }
    }
  }
}
</style>
