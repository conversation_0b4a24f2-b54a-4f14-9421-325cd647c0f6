<template>
  <div class="spaceCheck-view">
    <Header title="空间清查" @backFun="goback"></Header>
    <div class="view-tab">
      <van-tabs v-model="tabActive" color="#3562DB" :line-width="24" title-active-color="#1D2129" title-inactive-color="#86909C" @click="tabClick">
        <van-tab
          v-for="item in tabList"
          :key="item.code"
          :title="item.name"
          :name="item.code"
        ></van-tab>
      </van-tabs>
    </div>
    <div class="view-list">
      <van-pull-refresh v-model="pullLoading" success-text="刷新成功" @refresh="getDataList('init')">
        <van-list v-model="listLoading" :finished="listFinished" finished-text="没有更多了" @load="getDataList">
          <div class="list-item" v-for="item in dataList" :key="item.id" @click="goToTaskDetails(item)">
            <div class="item-title">
              <div class="title-left">
                <p class="left-text">{{ item.inventoryTaskName }}</p>
                <p class="left-code">{{ item.inventoryTaskNo }}</p>
              </div>
              <p class="title-right" :style="{background: stateInfo[item.inventoryStatus].color}">{{ stateInfo[item.inventoryStatus].name }}</p>
            </div>
            <div class="item-main">
              <div class="item-left">
                <img src="../../../assets/images/zongKJ-icon.png">
                <div style="padding-left: 10px;">
                  <p class="left-text">总空间</p>
                  <p class="left-num">{{ item.departNumPo.totalNum }}</p>
                </div>
              </div>
              <div class="item-right">
                <div class="right-item" style="background: #FFECE8;">
                  <p class="item-text">未盘</p>
                  <p class="item-num" style="color: #F53F3F;">{{ item.departNumPo.unconfirmedTotalNum }}</p>
                </div>
                <div class="right-item" style="background: #F2F3F5;">
                  <p class="item-text">已盘</p>
                  <p class="item-num" style="color: #1D2129;">{{ item.departNumPo.confirmedTotalNum }}</p>
                </div>
              </div>
            </div>
            <div class="item-footer">
              <div class="footer-item">
                <p class="footer-label">计划周期</p>
                <p class="footer-value">{{ item.taskStartTime + ' - ' + item.taskEndTime }}</p>
              </div>
              <div class="footer-item">
                <p class="footer-label">责任人</p>
                <p class="footer-value">{{ item.responsibilityPerson }}</p>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
export default {
  name: 'spaceCheck',
  data() {
    return {
      pullLoading: false, // 下拉刷新
      listLoading: false, // 列表加载
      listFinished: false, // 列表加载完成
      tabActive: '',
      dataList: [],
      tabList: [
        { code: '', name: '全部' },
        { code: 1, name: '未开始' },
        { code: 2, name: '进行中' },
        { code: 3, name: '已结束' }
      ],
      stateInfo: {
        1: { color: '#F7BA1E', name: '未开始' },
        2: { color: '#00B42A', name: '进行中' },
        3: { color: '#C9CDD4', name: '已结束' }
      },
      pageParams: {
        currentPage: 0,
        pageSize: 10
      }
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {

  },
  methods: {
    goToTaskDetails(item) {
      if (item.inventoryStatus != 1) {
        this.$router.push({
          path: '/taskDetails',
          query: {
            id: item.id
          }
        })
      } else {
        Toast('请在有效时间内进入！')
      }
    },
    getDataList(type) {
      if (type == 'init') {
        this.dataList = []
        this.pageParams.currentPage = 1
      } else {
        this.pageParams.currentPage += 1
      }
      let loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
      let params = {
        userId: loginInfo.staffId,
        departId: loginInfo.deptId,
        inventoryStatus: this.tabActive,
        pageParams: this.pageParams
      }
      this.$api.GetSpaceCheckList(params).then(res => {
        this.dataList = this.dataList.concat(res.records)
        this.pullLoading = false
        this.listLoading = false
        if (this.pageParams.currentPage >= res.pages) {
          this.listFinished = true
        } else {
          this.listFinished = false
        }
      });
    },
    tabClick(name, title) {
      this.getDataList('init')
    },
    goback() {
      if(this.$route.query.type == 'spaceCheck') {
        this.$router.go(-1);
      } else {
        this.$YBS.apiCloudCloseFrame();
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.spaceCheck-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  .view-list {
    flex: 1;
    background: #F2F4F9;
    padding: 0px 10px;
    overflow-y: auto;
    .list-item {
      margin-top: 10px;
      padding: 16px;
      border-radius: 8px;
      background: #fff;
      .item-title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title-left {
          flex: 1;
        }
        .title-right {
          padding: 3px 6px;
          font-size: 12px;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 18px;
          border-radius: 2px;
          margin-left: 10px;
        }
        .left-text {
          font-size: 16px;
          font-weight: bold;
          color: #1D2129;
          line-height: 22px;
        }
        .left-code {
          font-size: 12px;
          font-weight: 400;
          color: #86909C;
          line-height: 17px;
        }
      }
      .item-main {
        display: flex;
        justify-content: space-between;
        padding-top: 10px;
        .item-left {
          width: calc(50% - 5px);
          padding: 26px 0px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #E6EFFC;
          border-radius: 8px;
          img {
            width: 28px;
            height: 28px;
          }
          .left-text {
            font-size: 14px;
            font-weight: 400;
            color: #4E5969;
            line-height: 20px;
          }
          .left-num {
            font-size: 18px;
            font-weight: bold;
            color: #3562DB;
            line-height: 25px;
          }
        }
        .item-right {
          width: calc(50% - 5px);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .right-item {
            height: calc(50% - 5px);
            border-radius: 8px;
            padding-left: 16px;
            display: flex;
            align-items: center;
          }
          .item-text {
            font-size: 14px;
            font-weight: 400;
            color: #4E5969;
            line-height: 20px;
            background: transparent;
          }
          .item-num {
            margin-left: 8px;
            font-size: 18px;
            font-weight: bold;
            line-height: 25px;
          }
        }
      }
      .item-footer {
        padding-top: 8px;
        .footer-item {
          padding: 6px 0px;
          display: flex;
          justify-content: space-between;
        }
        .footer-label {
          font-size: 14px;
          font-weight: 300;
          color: #4E5969;
          line-height: 20px;
        }
        .footer-value {
          font-size: 14px;
          font-weight: 400;
          color: #1D2129;
          line-height: 20px;
        }
      }
    }
    .van-pull-refresh{
      height: calc(100% - 10px);
      overflow: initial;
    }
  }
}
</style>
