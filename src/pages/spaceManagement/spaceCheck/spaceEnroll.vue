<template>
  <div class="spaceEnroll-view">
    <Header title="空间登记" @backFun="goback"></Header>
    <van-tabs v-model="tabActive" color="#3562DB" :line-width="24" title-active-color="#1D2129" title-inactive-color="#86909C">
      <van-tab title="新的登记">
        <div class="view-add">
          <div class="add-main">
            <div class="add-item" v-for="item in addItemList" :key="item.label">
              <p class="item-label">{{ item.label }}</p>
              <p class="item-value" :style="{color: spaceRegistration[item.key] ? '#1D2129' : '#86909C'}" @click="selectSpace(item)">
                <span>{{ spaceRegistration[item.key] || (item.isSelect ? '请选择' : '') }}</span>
                <van-icon v-if="item.isSelect" style="margin-left: 6px;" name="arrow" color="#C9CDD4" size="16px" />
              </p>
            </div>
          </div>
          <div class="add-foot" id="footer">
            <div class="foot-btn btn-cancel" @click="clearSpace">清空</div>
            <div class="foot-btn btn-confirm" @click="saveSpace">保存</div>
          </div>
        </div>
      </van-tab>
      <van-tab :title="'已登记（'+ total +'）'">
      <div class="view-list">
        <van-pull-refresh v-model="pullLoading" success-text="刷新成功" @refresh="getDataList('init')">
          <van-list v-model="listLoading" :finished="listFinished" :immediate-check="false" finished-text="没有更多了" @load="getDataList">
            <div class="list-item" v-for="item in dataList" :key="item.id">
              <div class="item-title">
                <span class="title-text">{{ item.spaceLocalName }}</span>
                <span class="title-status">已登记</span>
              </div>
              <div class="item-main">
                <p class="main-text">
                  <span>本地编码</span>
                  <span>{{ item.spaceLocalNo || '-' }}</span>
                </p>
                <p class="main-text">
                  <span>归属部门</span>
                  <span>{{ item.deptName || '-' }}</span>
                </p>
                <p class="main-text">
                  <span>位置</span>
                  <span>{{ item.localtion || '-' }}</span>
                </p>
              </div>
              <div class="item-foot">
                <div class="foot-btn btn-confirm" @click="deleteRegistInfo(item)">删除</div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: 'spaceEnroll',
  data() {
    return {
      pullLoading: false, // 下拉刷新
      listLoading: false, // 列表加载
      listFinished: false, // 列表加载完成
      tabActive: 0,
      dataList: [],
      total: 0,
      pageParams: {
        currentPage: 0,
        pageSize: 10
      },
      addItemList: [
        {label: '本地空间名称', key: 'localSpaceName', isSelect: true},
        {label: '本地空间编码', key: 'localSpaceCode', isSelect: false},
        {label: '空间状态', key: 'spaceState', isSelect: false},
        {label: '功能类型', key: 'functionDictName', isSelect: false},
        {label: '建筑面积', key: 'area', isSelect: false},
        {label: '位置', key: 'simName', isSelect: false},
      ]
    }
  },
  watch: {
    tabActive(val) {
      if (val == 1) {
        this.getDataList('init')
      }
    }
  },
  computed: {
    ...mapState([
      "spaceRegistration"
    ])
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if(navigator.userAgent.indexOf('APICloud') !== -1) { // ApiCloud环境
      document.getElementById("footer").style.paddingBottom = api.safeArea.bottom + 'px';
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    deleteRegistInfo(item) {
      this.$api.DeleteRegistInfoById(item.id).then(res => {
        this.getDataList('init')
      });
    },
    clearSpace() {
      this.$store.commit("setSpaceRegistration", {})
    },
    getDataList(type) {
      if (type == 'init') {
        this.dataList = []
        this.pageParams.currentPage = 1
      } else {
        this.pageParams.currentPage += 1
      }
      let params = {
        departId: this.$route.query.departId,
        spaceSourceType: 2,
        pageParams: this.pageParams
      }
      this.$api.GetRegistSpaceInfo(params).then(res => {
        this.dataList = this.dataList.concat(res.records)
        this.total = res.total
        this.pullLoading = false
        this.listLoading = false
        if (this.pageParams.currentPage >= res.pages) {
          this.listFinished = true
        } else {
          this.listFinished = false
        }
      });
    },
    saveSpace() {
      let loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
      let params = {
        checkResult: '3',
        spaceCode: this.spaceRegistration.modelCode,
        checkStatus: 1,
        spaceSourceType: 2,
        confirmPerson: loginInfo.staffName,
        departId: this.$route.query.departId,
        userName: loginInfo.staffName,
        deptName: this.spaceRegistration.dmName,
        spaceLocalName: this.spaceRegistration.localSpaceName,
        spaceLocalNo: this.spaceRegistration.localSpaceCode,
        spaceStatus: this.spaceRegistration.spaceState,
        functionType: this.spaceRegistration.functionDictName,
        spaceArea: this.spaceRegistration.area,
        localtion: this.spaceRegistration.simName,
      }
      this.$api.AddRegistSpaceInfo(params).then(res => {
        this.tabActive = 1
        this.clearSpace()
      });
    },
    selectSpace(item) {
      if(item.isSelect) {
        this.$router.push({
          path: '/idleSpace'
        })
      }
    },
    goback() {
      this.clearSpace()
      this.$router.go(-1)
    }
  }
}

</script>

<style lang="scss" scoped>
.spaceEnroll-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  /deep/ .van-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    .van-tabs__content {
      flex: 1;
      background: #F2F4F9;
      .van-tab__pane {
        height: 100%;
      }
    }
  }
  .view-add {
    height: 100%;
    display: flex;
    flex-direction: column;
    .add-main {
      flex: 1;
      .add-item{
        background: #fff;
        border-bottom: 1px solid #E5E6EB;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        .item-label {
          font-size: 16px;
          font-weight: 400;
          color: #4E5969;
          min-width: 120px;
        }
        .item-value {
          font-size: 15px;
          font-weight: 300;
          display: flex;
          align-items: center;
        }
      }
    }
    .add-foot {
      padding: 10px 16px;
      background: #fff;
      display: flex;
      justify-content: space-between;
      .foot-btn {
        width: calc(50% - 6px);
        text-align: center;
        padding: 12px 0px;
        font-size: 15px;
        font-weight: 400;
        line-height: 21px;
        border-radius: 2px;
      }
      .btn-cancel {
        background: #E6EFFC;
        color: #3562DB;
      }
      .btn-confirm {
        background: #3562DB;
        color: #fff;
      }
    }
  }
  .view-list {
    height: 100%;
    padding: 0px 10px;
    overflow-y: auto;
    .list-item {
      padding: 16px;
      background: #FFFFFF;
      border-radius: 8px;
      margin-top: 10px;
      .item-title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title-text {
          display: inline-block;
          font-size: 16px;
          font-weight: 400;
          color: #1D2129;
          flex: 1;
        }
        .title-status {
          display: inline-block;
          font-size: 12px;
          font-weight: 500;
          line-height: 16px;
          padding: 4px 6px;
          background: #E8FFEA;
          color: #00B42A;
        }
      }
      .item-main {
        .main-text {
          font-size: 16px;
          line-height: 22px;
          padding: 5px 0px;
          display: flex;
          span:first-child {
            color: #4E5969;
            font-weight: 300;
            min-width: 88px;
          }
          span:last-child {
            color: #1D2129;
            font-weight: 400;
          }
        }
      }
      .item-foot {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        .foot-btn {
          padding: 6px 16px;
          border-radius: 2px;
          font-size: 14px;
          font-weight: 300;
          line-height: 20px;
        }
        .btn-confirm {
          background: #3562DB;
          color: #fff;
          margin-left: 16px;
        }
      }
    }
    .van-pull-refresh{
      height: calc(100% - 10px);
      overflow: initial;
    }
  }
}
</style>
