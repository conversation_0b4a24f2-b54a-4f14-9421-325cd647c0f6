<template>
  <div class="deptDetails-view">
    <Header title="科室清查详情" @backFun="goback"></Header>
    <div class="view-header">
      <div class="header-info">
        <div class="info-title">
          <p>{{ deptInfo.departName }}</p>
          <p v-if="inventoryStatus != 3" @click="goToEnroll">
            <img src="../../../assets/images/dengji_icon.png"><span>空间登记</span>
          </p>
        </div>
        <!-- <p class="info-people">
          <span>责任人</span>
          <span>{{ deptInfo.responsibilityPerson || '-' }}</span>
        </p> -->
        <p class="info-plan">
          <span>进行中</span>
          <span>{{ deptInfo.schedule }}%</span>
        </p>
        <div class="info-Progress">
          <p :style="{width: deptInfo.schedule + '%'}"></p>
        </div>
      </div>
      <van-tabs v-model="tabActive" color="#3562DB" :line-width="24" title-active-color="#1D2129" title-inactive-color="#86909C" @click="tabClick">
        <van-tab
          v-for="item in tabList"
          :key="item.code"
          :title="item.name"
          :name="item.code"
        ></van-tab>
      </van-tabs>
    </div>
    <div class="view-list">
      <van-pull-refresh v-model="pullLoading" success-text="刷新成功" @refresh="getDataList('init')">
        <van-list v-model="listLoading" :finished="listFinished" finished-text="没有更多了" @load="getDataList">
          <div class="list-item" v-for="item in dataList" :key="item.id">
            <div class="item-title">
              <span class="title-text">{{ item.spaceLocalName }}</span>
              <span class="title-status" :style="{color: stateInfo[item.checkStatus].color, background: stateInfo[item.checkStatus].bgdColor}">{{ stateInfo[item.checkStatus].text }}</span>
            </div>
            <div class="item-main">
              <p class="main-text">
                <span>本地编码</span>
                <span>{{ item.spaceLocalNo }}</span>
              </p>
              <p class="main-text">
                <span>归属部门</span>
                <span>{{ item.deptName }}</span>
              </p>
              <p class="main-text">
                <span>位置</span>
                <span>{{ item.localtion }}</span>
              </p>
            </div>
            <div class="item-foot">
              <div v-if="item.checkStatus == 1 && inventoryStatus != 3" class="foot-btn btn-cancel" @click="updateSpaceStatus('', 0, item)">取消确认</div>
              <div v-if="item.checkStatus != 1 && inventoryStatus != 3" class="foot-btn btn-cancel" @click="updateSpaceStatus(1, 1, item)">非科室归属</div>
              <div v-if="item.checkStatus != 1 && inventoryStatus != 3" class="foot-btn btn-confirm" @click="updateSpaceStatus(2, 1, item)">确认归属</div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
export default {
  name: 'deptDetails',
  data() {
    return {
      inventoryStatus: 3,
      pullLoading: false, // 下拉刷新
      listLoading: false, // 列表加载
      listFinished: false, // 列表加载完成
      tabActive: '',
      tabList: [
        { code: '', name: '总空间(0)' },
        { code: 0, name: '未确认(0)' },
        { code: 1, name: '已确认(0)' }
      ],
      stateInfo: {
        1: {color: '#00B42A', bgdColor: '#E8FFEA', text: '已确认'},
        0: {color: '#F53F3F', bgdColor: '#FFECE8', text: '未确认'}
      },
      deptInfo: {schedule: 0},
      dataList: [],
      pageParams: {
        currentPage: 0,
        pageSize: 10
      }
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {

  },
  methods: {
    updateSpaceStatus(result, status, item) {
      let params = {
        checkResult: result,
        checkStatus: status,
        spaceId: item.id,
        confimPerson: JSON.parse(localStorage.getItem("loginInfo")).staffName,
      }
      this.$api.UpdateSpaceStatus(params).then(res => {
        this.getDataList('init')
      });
    },
    goToEnroll() {
      this.$router.push({
        path: '/spaceEnroll',
        query: {
          departId: this.$route.query.departId,
        }
      })
    },
    getDataList(type) {
      if (type == 'init') {
        this.dataList = []
        this.pageParams.currentPage = 1
      } else {
        this.pageParams.currentPage += 1
      }
      let params = {
        checkStatus: this.tabActive,
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        departId: this.$route.query.departId,
        pageParams: this.pageParams
      }
      this.$api.GetDeptSpaceDetails(params).then(res => {
        this.inventoryStatus = res.inventoryStatus
        this.dataList = this.dataList.concat(res.page.records)
        this.getSpaceNum(res.departInfo)
        this.pullLoading = false
        this.listLoading = false
        if (this.pageParams.currentPage >= res.page.pages) {
          this.listFinished = true
        } else {
          this.listFinished = false
        }
      });
    },
    getSpaceNum(data) {
      let params = {
        userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        departId: this.$route.query.departId,
      }
      this.$api.GetSpaceNum(params).then(res => {
        this.tabList[0].name = '总空间(' + res.endTotal +')'
        this.tabList[1].name = '未确认(' + res.ing +')'
        this.tabList[2].name = '已确认(' + res.noStart +')'
        this.deptInfo = {...data, ...res}
      });
    },
    tabClick(name, title) {
      console.log(name, title)
      this.getDataList('init')
    },
    goback() {
      this.$router.go(-1)
    }
  }
}

</script>

<style lang="scss" scoped>
.deptDetails-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  .view-header {
    padding: 10px 10px 0;
    .header-info{
      padding: 19px 16px 16px;
      background: #F7F8FA;
      border-radius: 8px;
      // margin-bottom: 10px;
      .info-title {
        display: flex;
        p:first-child {
          flex: 1;
          font-size: 16px;
          font-weight: bold;
          color: #1D2129;
          line-height: 22px;
        }
        p:last-child {
          line-height: 21px;
          img {
            width: 14px;
            height: 14px;
          }
          span {
            display: inline-block;
            margin-left: 4px;
            font-size: 15px;
            font-weight: 300;
            color: #3562DB;
          }
        }
      }
      .info-people {
        line-height: 22px;
        padding-top: 16px;
        span:first-child {
          font-size: 16px;
          font-weight: 300;
          color: #4E5969;
        }
        span:last-child {
          font-size: 16px;
          font-weight: 400;
          color: #1D2129;
          margin-left: 24px;
        }
      }
      .info-plan {
        line-height: 22px;
        padding-top: 16px;
        padding-bottom: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span:first-child {
          font-size: 16px;
          font-weight: 400;
          color: #00B42A;
        }
        span:last-child {
          font-size: 16px;
          font-weight: 400;
          color: #1D2129;
        }
      }
      .info-Progress {
        height: 10px;
        width: 100%;
        background: #E5E6EB;
        overflow: hidden;
        p {
          height: 100%;
          background: #00B42A;
        }
      }
    }
  }
  .view-list {
    flex: 1;
    background: #F2F4F9;
    padding: 0px 10px;
    overflow-y: auto;
    .list-item {
      padding: 16px;
      background: #FFFFFF;
      border-radius: 8px;
      margin-top: 10px;
      .item-title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title-text {
          display: inline-block;
          font-size: 16px;
          font-weight: 400;
          color: #1D2129;
          flex: 1;
        }
        .title-status {
          display: inline-block;
          font-size: 12px;
          font-weight: 500;
          line-height: 16px;
          padding: 4px 6px;
        }
      }
      .item-main {
        .main-text {
          font-size: 16px;
          line-height: 22px;
          padding: 5px 0px;
          span:first-child {
            color: #4E5969;
            font-weight: 300;
            display: inline-block;
            width: 88px;
          }
          span:last-child {
            color: #1D2129;
            font-weight: 400;
          }
        }
      }
      .item-foot {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        .foot-btn {
          padding: 6px 16px;
          border-radius: 2px;
          font-size: 14px;
          font-weight: 300;
          line-height: 20px;
        }
        .btn-cancel {
          background: #E6EFFC;
          color: #3562DB;
        }
        .btn-confirm {
          background: #3562DB;
          color: #fff;
          margin-left: 16px;
        }
      }
    }
    .van-pull-refresh{
      height: calc(100% - 10px);
      overflow: initial;
    }
  }
}
</style>
