<template>
  <div class="spaceApplication-view">
    <Header title="空间申请" @backFun="goback">
      <div class="topSelest" @click="() => {isSelectStatus = true}">
        <p>{{getMenuName}}</p>
        <i class="el-icon-caret-bottom"></i>
      </div>
    </Header>
    <div class="view-list">
      <van-pull-refresh v-model="pullLoading" success-text="刷新成功" @refresh="getSpaceApplyList('init')">
        <van-list v-model="listLoading" :finished="listFinished" finished-text="没有更多了" @load="getSpaceApplyList">
          <div class="list-item" v-for="item in dataList" :key="item.code" @click="viewItem('', item)">
            <div class="item-title">
              <p class="title-code">{{ item.applyNo }}</p>
              <p class="title-status">
                <span :style="{color: statusInfoList[item.spaceApplyStatus].color, background: statusInfoList[item.spaceApplyStatus].bgdColor}">{{ statusInfoList[item.spaceApplyStatus].name }}</span>
                <van-icon name="arrow" color="#C9CDD4" :size="14" />
              </p>
            </div>
            <div class="item-info">
              <div class="info-item">
                <p class="info-label">申请部门</p>
                <p class="info-value">{{ item.applyDept }}</p>
              </div>
              <div class="info-item">
                <p class="info-label">申请时间</p>
                <p class="info-value">{{ item.applyTime }}</p>
              </div>
              <div class="info-item">
                <p class="info-label">申请说明</p>
                <p class="info-value">{{ item.applyDescription }}</p>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div class="view-footer" id="footer">
      <div class="footer-btn" @click="viewItem('add')">发起申请</div>
      <van-popup v-model="isSelectStatus" round position="bottom">
        <van-picker
          show-toolbar
          title="请选择"
          confirm-button-text="完成"
          :columns="statusList"
          value-key="name"
          @cancel="() => {isSelectStatus = false}"
          @confirm="pickerConfirm"
        />
      </van-popup>
    </div>
  </div>
</template>

<script>
export default {
  name: 'spaceApplication',
  data() {
    return {
      pullLoading: false, // 下拉刷新
      listLoading: false, // 列表加载
      listFinished: false, // 列表加载完成
      isSelectStatus: false,
      statusCode: '', // 状态
      statusList: [
        {code: '', name: '全部'},
        {code: 0, name: '待提交'},
        {code: 1, name: '待审核'},
        {code: 2, name: '已审核'},
        {code: 3, name: '未通过'},
        {code: 4, name: '已取消'},
      ],
      statusInfoList: {
        0: {name: '待提交', color: '#FF7D00', bgdColor: '#FFF7E8'},
        1: {name: '待审核', color: '#3562DB', bgdColor: '#E6EFFC'},
        2: {name: '已审核', color: '#00B42A', bgdColor: '#E8FFEA'},
        3: {name: '未通过', color: '#F53F3F', bgdColor: '#FFECE8'},
        4: {name: '已取消', color: '#4E5969', bgdColor: '#F2F3F5'}
      },
      dataList: [],
      pageParams: {
        currentPage: 0,
        pageSize: 10
      }
    }
  },
  computed: {
    getMenuName() {
      return this.statusList.length ? this.statusList.find(item => item.code === this.statusCode).name : '-'
    }
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if(navigator.userAgent.indexOf('APICloud') !== -1) { // ApiCloud环境
      document.getElementById("footer").style.paddingBottom = api.safeArea.bottom + 'px';
    }
  },
  created() {

  },
  methods: {
    getSpaceApplyList(type) {
      if (type == 'init') {
        this.dataList = []
        this.pageParams.currentPage = 1
      } else {
        this.pageParams.currentPage += 1
      }
      let params = {
        applyPersonId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
        spaceApplyStatus: this.statusCode,
        pageParams: this.pageParams
      }
      this.$api.GetSpaceApplyList(params).then(res => {
        this.dataList = this.dataList.concat(res.records)
        this.pullLoading = false
        this.listLoading = false
        if (this.pageParams.currentPage >= res.pages) {
          this.listFinished = true
        } else {
          this.listFinished = false
        }
      });
    },
    viewItem(type, item) {
      if (type == 'add') {
        this.$router.push({path: '/addApplication',})
        return
      }
      if (item.spaceApplyStatus == 0) {
        this.$router.push({
          path: '/addApplication',
          query: {id: item.id}
        })
        return
      }
      this.$router.push({
        path: '/applicationDetails',
        query: {id: item.id}
      })
    },
    // 状态切换
    pickerConfirm(item) {
      this.statusCode = item.code
      this.isSelectStatus = false
      this.getSpaceApplyList('init')
    },
    goback() {
      this.$router.go(-1);
    }
  }
}

</script>

<style lang="scss" scoped>
.spaceApplication-view{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .topSelest{
    display: flex;
    align-items: center;
    p{
      color: #1D2129;
      font-size: 16px;
    }
    .el-icon-caret-bottom{
      color: #C9CDD4;
      margin-left: 4px;
    }
  }
  .view-list {
    flex: 1;
    background: #F2F4F9;
    padding: 0px 10px 0px 10px;
    overflow: auto;
    .list-item{
      padding: 16px;
      margin-top: 10px;
      border-radius: 8px;
      background: #fff;
      .item-title{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-status{
          display: flex;
          align-items: center;
          span {
            display: inline-block;
            padding: 3px 6px;
            font-size: 12px;
            font-weight: 500;
            color: #FF7D00;
            line-height: 16px;
          }
        }
        .title-code{
          font-size: 16px;
          font-weight: 400;
          color: #1D2129;
        }
      }
      .item-info{
        margin-top: 7px;
        .info-item {
          padding: 5px 0px;
          display: flex;
          font-size: 16px;
          line-height: 22px;
        }
        .info-label {
          padding-right: 24px;
          font-weight: 300;
          color: #4E5969;
        }
        .info-value {
          flex: 1;
          font-weight: 400;
          color: #1D2129;
          overflow:hidden;
          text-overflow:ellipsis;
          white-space:nowrap;
        }
      }
    }
    .van-pull-refresh{
      height: calc(100% - 10px);
      overflow: initial;
    }

  }
  .view-footer {
    padding: 10px 16px;
    background: #fff;
    .footer-btn{
      width: 100%;
      height: 44px;
      line-height: 44px;
      text-align: center;
      background: #3562DB;
      border-radius: 2px;
      font-size: 15px;
      color: #FFFFFF;
    }
  }
}
</style>
