<template>
  <div class="addApplication-view">
    <Header title="空间申请" @backFun="goback"></Header>
    <div class="view-main">
      <van-form ref="form" input-align="right" @submit="submit">
        <van-field
          v-model="submitForm.applyNo"
          name="applyNo"
          readonly
          label="申请编码"
          placeholder="请输入"
        />
        <van-field
          v-model="submitForm.applyDept"
          name="applyDept"
          readonly
          label="申请部门"
          placeholder="请输入"
        />
        <van-field
          v-model="submitForm.applyName"
          name="applyName"
          readonly
          label="申请人"
          placeholder="请输入"
        />
        <van-field
          v-model="submitForm.applyTel"
          name="applyTel"
          readonly
          label="电话"
          placeholder="请输入"
        />
        <van-field
          v-model="submitForm.applyArea"
          name="applyArea"
          type="number"
          label="申请使用面积"
          placeholder="请输入"
          :rules="rules.applyArea"
        >
          <template #right-icon>
            <span style="color: #323233;">㎡</span>
          </template>
        </van-field>
        <van-field
          v-model="submitForm.usage"
          name="usage"
          label="用途"
          placeholder="请选择"
          readonly
          clickable
          is-link
          @click-input="() => { isSelectUse = true }"
          :rules="rules.usage"
        />
        <van-field
          class="applyDescription"
          v-model="submitForm.applyDescription"
          rows="3"
          autosize
          label="申请说明"
          name="applyDescription"
          type="textarea"
          maxlength="200"
          input-align="left"
          placeholder="请输入申请说明，字数限制200字以内"
          show-word-limit
          :rules="rules.applyDescription"
        />
      </van-form>
    </div>
    <div class="view-footer" id="footer">
      <div class="footer-btn btn-default" @click="submitInfo(1)">提交审核</div>
      <div v-if="!$route.query.id" class="footer-btn btn-plain" @click="submitInfo(0)">暂存</div>
      <div v-if="!$route.query.id" class="footer-btn btn-plain" @click="wipeData">清空</div>

      <div v-if="$route.query.id" class="footer-btn btn-plain" @click="deleteApplication">删除</div>
      <div v-if="$route.query.id" class="footer-btn btn-plain" @click="goback">取消</div>
      <van-popup v-model="isSelectUse" round position="bottom">
        <van-picker
          show-toolbar
          title="请选择"
          confirm-button-text="完成"
          :columns="useList"
          value-key="dictName"
          @cancel="() => {isSelectUse = false}"
          @confirm="pickerConfirm"
        />
      </van-popup>
    </div>
  </div>
</template>

<script>
import {debounce} from '@/common/lib/util.js'
import { Toast } from "vant";
import moment from "moment";
moment.locale("zh-cn");
export default {
  name: 'addApplication',
  data() {
    return {
      addStatus: null,
      isSelectUse: false, // 用途选择
      useList: [],
      submitForm: {
        applyNo: '', // 申请编码
        applyDept: '', // 申请部门
        applyName: '', // 申请人
        applyTel: '', // 电话
        applyArea: '', // 面积
        usage: '', // 用途
        applyPersonId: '', // 申请人id
        applyDescription: '' // 申请说明
      },
      rules: {
        applyArea: [{ required: true }],
        usage: [{ required: true }],
        applyDescription: [{ required: true }],
      }
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
    if(navigator.userAgent.indexOf('APICloud') !== -1) { // ApiCloud环境
      document.getElementById("footer").style.paddingBottom = api.safeArea.bottom + 'px';
    }
  },
  created() {
    this.getDictionaryList()
    if (this.$route.query.id) {
      this.getSpaceApplyDetails()
    } else {
      this.initData()
    }
  },
  methods: {
    cancel() {
      if (this.$route.query.type == 'edit') {
        this.$router.go(-2);
      } else {
        this.$router.go(-1);
      }
    },
    // 删除暂存
    deleteApplication() {
      this.$api.DeleteApplyById(this.$route.query.id).then(res => {
        if (res) {
          Toast('删除成功')
          this.cancel()
        }
      });
    },
    // 获取详情
    getSpaceApplyDetails() {
      this.$api.GetSpaceApplyDetails(this.$route.query.id).then(res => {
        this.submitForm = res
      });
    },
    // 获取用途列表
    getDictionaryList() {
      this.$api.selectByList({ typeValue: 'SP' }).then(res=>{
        if(res.length){
          this.useList = res
        }
      })
    },
    submit: debounce(function(){
      this.addSpaceApply()
    },1000),
    // 添加审核
    addSpaceApply() {
      this.$api.AddSpaceApply({...this.submitForm, spaceApplyStatus: this.addStatus}).then(res => {
        if (res) {
          Toast(this.addStatus == 0 ? '暂存成功' : '提交审核成功')
          this.cancel()
        }
      });
    },
    // 清除
    wipeData() {
      Object.assign(this.$data.submitForm, this.$options.data().submitForm)
      this.initData()
    },
    initData() {
      let loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
      this.submitForm.applyNo = 'KJSQ' + moment().format("YYYYMMDD")
      this.submitForm.applyDept = loginInfo.deptName
      this.submitForm.applyName = loginInfo.staffName
      this.submitForm.applyTel = loginInfo.phone
      this.submitForm.applyPersonId = loginInfo.staffId
    },
    // 提交信息
    submitInfo(status){
      this.addStatus = status
      this.$refs.form.submit()
    },
    // 状态切换
    pickerConfirm(item) {
      this.submitForm.usage = item.dictName
      this.isSelectUse = false
    },
    goback() {
      this.$router.go(-1);
    }
  }
}

</script>

<style lang="scss" scoped>
.addApplication-view{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .view-main{
    flex: 1;
    background: #F2F4F9;
    padding: 10px 0px 10px 0px;
    overflow: auto;
    .applyDescription{
      display: block;
      margin-top: 10px;
    }
  }
  .view-footer {
    padding: 10px 16px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    .footer-btn{
      width: calc(100% / 3 - 8px);
      height: 44px;
      line-height: 44px;
      text-align: center;
      border-radius: 2px;
      font-size: 15px;
    }
    .btn-default{
      background: #3562DB;
      color: #FFFFFF;
    }
    .btn-plain{
      background: #E6EFFC;
      color: #3562DB;
    }
  }
}
</style>
