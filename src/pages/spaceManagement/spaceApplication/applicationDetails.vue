<template>
  <div class="applicationDetails-view">
    <Header title="空间申请" @backFun="goback"></Header>
    <div class="view-main">
      <p class="main-title">基本信息</p>
      <van-form ref="form" input-align="right">
        <van-field
          v-model="detailedData.applyNo"
          readonly
          label="申请编码"
        />
        <van-field
          v-model="detailedData.applyDept"
          readonly
          label="申请部门"
        />
        <van-field
          v-model="detailedData.applyName"
          readonly
          label="申请人"
        />
        <van-field
          v-model="detailedData.applyTel"
          readonly
          label="电话"
        />
        <van-field
          v-model="detailedData.applyArea"
          type="number"
          readonly
          label="申请使用面积"
        >
          <template #right-icon>
            <span style="color: #323233;">㎡</span>
          </template>
        </van-field>
        <van-field
          v-model="detailedData.usage"
          label="用途"
          readonly
        />
        <van-field
          class="applyDescription"
          v-model="detailedData.applyDescription"
          rows="3"
          autosize
          readonly
          label="申请说明"
          type="textarea"
          maxlength="200"
          input-align="left"
          show-word-limit
        />
      </van-form>
      <p class="main-title">审核信息</p>
      <van-form ref="form" input-align="right">
        <van-field
          v-model="detailedData.reviewTime"
          readonly
          label="审核时间"
        />
        <van-field
          v-model="detailedData.reviewDept"
          readonly
          label="审核部门"
        />
        <van-field
          v-model="detailedData.reviewPerson"
          readonly
          label="审核人员"
        />
        <van-field
          v-model="detailedData.spaceApplyStatus"
          label="审核结论"
          readonly
        >
          <template #input>
            <div v-if="['2', '3', '4'].includes(detailedData.spaceApplyStatus)" class="spaceApplyStatus">
              <img v-if="detailedData.spaceApplyStatus == 2" src="../../../assets/images/icon/check-circle-fill.png">
              <img v-else src="../../../assets/images/icon/close-circle-fill.png">
              <p>{{ detailedData.spaceApplyStatus == 2 ? '通过' : (detailedData.spaceApplyStatus == 3 ? '未通过' : '已取消') }}</p>
            </div>
            <div v-else></div>
          </template>
        </van-field>
        <van-field
          class="applyDescription"
          v-model="detailedData.reviewAdvice"
          rows="3"
          autosize
          readonly
          label="申请意见"
          type="textarea"
          maxlength="200"
          input-align="left"
          show-word-limit
        />
      </van-form>
      <p v-if="detailedData.spaceApplyStatus == 2" class="main-title">空间分配</p>
      <van-form v-if="detailedData.spaceApplyStatus == 2" ref="form" input-align="right">
        <van-field
          v-model="detailedData.allowSpace"
          readonly
          label="已分配空间"
        >
          <template #input>
            <div style="text-align: left;">
              {{ detailedData.allowSpace }}
            </div>
          </template>
        </van-field>
        <van-field
          v-model="detailedData.allowSpaceArea"
          type="number"
          readonly
          label="已分配面积"
        >
          <template #right-icon>
            <span style="color: #323233;">㎡</span>
          </template>
        </van-field>
      </van-form>
    </div>
    <div v-if="detailedData.spaceApplyStatus == 3" class="view-footer" id="footer">
      <div class="footer-btn btn-plain" @click="updateSpaceApply">取消申请</div>
      <div class="footer-btn btn-default" @click="viewItem">重新编辑</div>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
export default {
  name: 'applicationDetails',
  data() {
    return {
      detailedData: {
        spaceApplyStatus: ''
      },
    }
  },
  computed: {

  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  created() {
    this.getSpaceApplyDetails()
  },
  methods: {
    viewItem() {
      this.$router.push({
        path: '/addApplication',
        query: {id: this.detailedData.id, type: 'edit'}
      })
    },
    updateSpaceApply() {
      this.$api.UpdateSpaceApply({applyId: this.detailedData.id, spaceApplyStatus: 4}).then(res => {
        Toast('取消成功')
        this.goback()
      });
    },
    // 获取详情
    getSpaceApplyDetails() {
      this.$api.GetSpaceApplyDetails(this.$route.query.id).then(res => {
        this.detailedData = res
        this.$nextTick(() => {
          if(navigator.userAgent.indexOf('APICloud') !== -1) { // ApiCloud环境
            document.getElementById("footer").style.paddingBottom = api.safeArea.bottom + 'px';
          }
        })
      });
    },
    goback() {
      this.$router.go(-1);
    }
  }
}

</script>

<style lang="scss" scoped>
.applicationDetails-view{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .view-main{
    flex: 1;
    background: #F2F4F9;
    padding: 0px 0px 10px 0px;
    overflow: auto;
    .main-title{
      padding: 10px 16px;
      font-size: 14px;
      font-weight: 300;
      color: #86909C;
      line-height: 20px;
    }
    .applyDescription{
      display: block;
    }
    .spaceApplyStatus{
      display: flex;
      img {
        width: 20px;
        height: 20px;
      }
      p {
        font-size: 16px;
        font-weight: 400;
        color: #1D2129;
        line-height: 22px;
        margin-left: 8px;
      }
    }
  }
  .view-footer {
    padding: 10px 16px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    .footer-btn{
      width: calc(100% / 2 - 6px);
      height: 44px;
      line-height: 44px;
      text-align: center;
      border-radius: 2px;
      font-size: 15px;
    }
    .btn-default{
      background: #3562DB;
      color: #FFFFFF;
    }
    .btn-plain{
      background: #E6EFFC;
      color: #3562DB;
    }
  }
}
</style>
