<template>
  <div class="inner">
    <Header title="空间管理" @backFun="goback()"></Header>
    <div class="topContent">
      <div class="ImagesLine">
        <div class="imageBar" v-for="item in filterMenuList" :key="item.value" @click="skip(item.name)">
          <img :src="item.img" :alt="item.name" />
          <div class="barText">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      menuList: [
        {
          value: "1",
          name: "空间台账",
          img: require("../../assets/images/空间台账.png"),
          path: "/ihcrsYBSApp/spaceLedger",
        },
        {
          value: "2",
          name: "部门台账",
          img: require("../../assets/images/部门台账.png"),
          path: "/ihcrsYBSApp/departmentLedger",
        },
        {
          value: "3",
          name: "空间统计",
          img: require("../../assets/images/空间统计.png"),
          path: "/ihcrsYBSApp/spaceStatistics",
          query: {
            type: "h5",
          },
        },
        {
          value: "4",
          name: "空间申请",
          img: require("../../assets/images/空间申请.png"),
          path: "/ihcrsYBSApp/spaceApplication",
        },
        {
          value: "5",
          name: "空间清查",
          img: require("../../assets/images/空间盘点.png"),
          path: "/ihcrsYBSApp/spaceCheck",
          query: {
            type: "spaceCheck",
          },
        },
      ],
      filterMenuList: [],
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"]),
  },
  mounted() {
    this.getMenuAuth();
    this.$YBS.apiCloudEventKeyBack(this.goback);
  },
  methods: {
    // 获取菜单权限
    getMenuAuth() {
      const params = {
        state: "0",
        userId: this.loginInfo.id,
      };
      this.$api.getMenuAuth(params).then((res) => {
        const authMenu = this.$YBS.treeToList(res || []);
        // .find((i) => i.menuId == 1000)
        // .children.find((k) => k.menuId == 1004)
        // .children.find((j) => j.menuId == 280).children;
        this.filterMenuList = this.menuList.filter((item) => {
          return authMenu.some((i) => i.pathUrl == item.path);
        });
      });
    },
    skip(val) {
      if (val == "空间台账") {
        this.$router.push({
          path: "/spaceLedger",
        });
      } else if (val == "部门台账") {
        this.$router.push({
          path: "/departmentLedger",
        });
      } else if (val == "空间统计") {
        this.$router.push({
          path: "/spaceStatistics",
          query: {
            type: "h5",
          },
        });
      } else if (val == "空间申请") {
        this.$router.push({
          path: "/spaceApplication",
        });
      } else if (val == "空间清查") {
        this.$router.push({
          path: "/spaceCheck",
          query: {
            type: "spaceCheck",
          },
        });
      }
    },
    goback() {
      // api.closeFrame();
      this.$YBS.apiCloudCloseFrame();
    },
  },
};
</script>

<style scoped lang="stylus">
.inner {
  width: 100%;
  height: 100%;
  background-color: #F2F4F9;

  .topContent {
    padding: 0 0.32px 0.2rem 0.12rem;

    // height: 4.6rem
    .ImagesLine {
      display: flex;
      flex-wrap: wrap;

      .imageBar {
        margin: 0.2rem 0 0 0.2rem;
        background-color: #fff;
        padding: 0.4rem 0;
        width: 2.2rem;
        height: 1.4rem;
        text-align: center;
        border-radius: 5px;

        img {
          width: 0.8rem;
        }

        .barText {
          margin-top: 0.2rem;
          font-size: 0.3rem;
        }
      }
    }
  }
}
</style>
