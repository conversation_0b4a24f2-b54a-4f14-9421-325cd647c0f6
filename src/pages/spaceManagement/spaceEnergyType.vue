<template>
  <div class="inner">
    <Header title="选择空间功能类型" @backFun="goBack"></Header>
    <div style="display:flex;">
      <van-sidebar v-model="activeKey" style="flex:1" @change="sidebarChange">
        <van-sidebar-item v-for="item in data" :key="item.id" :title="item.name" />
      </van-sidebar>
      <div style="flex:3;">
        <van-collapse v-model="activeNames" accordion>
          <van-collapse-item v-for="(i, index) in list" :key="i.index" :title="i.name" :name="index">
            <div style="max-height:37.5vh;overflow: auto; ">
              <div v-for="f in i.children" :key="f.id" class="radioSty">
                <div :style="f.id == id ? 'color:#3562db' : ''">{{ f.name }}</div>
                <div><van-radio checked-color="#3562db" v-model="id" :name="f.id"></van-radio></div>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>
    </div>
    <div class="comfirm-box">
      <van-button class="btn" type="primary" color="#3562DB" @click="onSearch">确定</van-button>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { Sidebar, SidebarItem } from "vant";
import { Collapse, CollapseItem } from "vant";
Vue.use(Collapse);
Vue.use(CollapseItem);
Vue.use(Sidebar);
Vue.use(SidebarItem);
export default {
  data() {
    return {
      radio: "",
      id: "",
      activeNames: 0,
      list: [],
      activeKey: 0,
      data: []
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getselectByList();
  },
  methods: {
    sidebarChange(index) {
      this.list = this.data[index].children;
    },
    goBack() {
      if (this.$route.query.isClould) {
        this.$YBS.apiCloudCloseFrame();
      } else {
        this.$router.go(-1);
      }
    },
    onSearch() {
      this.$router.push({
        path: "/spaceLedger",
        query: {
          id: this.id
        }
      });
    },
    getselectByList() {
      let params = {
        dictionaryCategoryId: "SPACE_FUNCTION"
      };
      this.$api.getSuperiorData(params).then(res => {
        this.data = res[0].children;
        this.list = this.data[0].children;
      });
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  overflow: hidden;

  height: 100%;
}
.van-cell {
  width: auto;
  background-color: #f7f8fa;
  margin: 5px;
  color: #4e5969;
}
.van-cell-group {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 60px;
}
.van-cell::after {
  display: none;
}
.van-checkbox {
  width: 0;
}
.van-cell-active {
  background-color: #e6effc;
  color: #3562db;
}
.comfirm-box {
  position: fixed;
  width: 95%;
  margin: 0 auto;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #fff;
}
.btn {
  width: 100%;
}
.radioSty {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f5f6f7;
}
/deep/ .van-collapse-item__content {
  padding: 0 16px !important;
}
/deep/ .van-sidebar-item--select::before {
  background-color: #fff;
}
/deep/ .van-sidebar-item--select {
  color: #3562db;
}
/deep/ .van-sidebar-item {
  text-align: center;
}
</style>
