<template>
  <div class="inner">
    <Header title="空间统计" @backFun="goback"></Header>
    <van-tabs @click="tabclick" class="tabs" title-active-color="#1D2129">
      <van-tab title="按功能类型" name="function"> </van-tab>
      <van-tab title="按部门" name="dept"></van-tab>
    </van-tabs>
    <div v-if="tableData.length > 0" class="hei">
      <div class="titSty" v-if="queryType == 'function'">
        <p v-for="(item, index) in title" :key="index">{{ item.name }}</p>
      </div>
      <div class="titSty" v-else>
        <p v-for="(item, index) in titles" :key="index">{{ item.name }}</p>
      </div>
      <div class="hei2">
        <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <div v-for="(item, index) in tableData" :key="index" class="listSty">
              <p>{{ index + 1 }}</p>
              <p>{{ item.dataName }}</p>
              <p>{{ item.roomAllCount }}</p>
              <p>{{ item.totalArea }}</p>
              <p>{{ item.totalUseArea }}</p>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
export default {
  data() {
    return {
      fromPage: this.$route.query.type || "",
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      title: [
        {
          name: "序号",
        },
        {
          name: "功能类型",
        },
        {
          name: "房间数",
        },
        {
          name: "建筑面积(㎡)",
        },
        {
          name: "使用面积(㎡)",
        },
      ],
      titles: [
        {
          name: "序号",
        },
        {
          name: "部门名称",
        },
        {
          name: "房间数",
        },
        {
          name: "建筑面积(㎡)",
        },
        {
          name: "使用面积(㎡)",
        },
      ],
      queryType: "function",
      current: 1,
      size: 20,
      total: 0,
      tableData: [],
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goback);

    this.getRoomCountAndAreaPageList();
  },
  methods: {
    goback() {
      if (this.fromPage == "h5") {
        this.$router.go(-1);
      } else {
        // api.closeFrame();
        this.$YBS.apiCloudCloseFrame();
      }
    },
    tabclick(val) {
      this.queryType = val;
      this.loading = true;
      this.finished = false;
      this.current = 1;
      this.tableData = [];
      this.getRoomCountAndAreaPageList();
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.tableData = [];
      this.getRoomCountAndAreaPageList();
    },

    onLoad() {
      this.finished = false;
      this.loading = true;
      this.current++;
      this.getRoomCountAndAreaPageList();
    },
    getRoomCountAndAreaPageList() {
      let params = {
        modelCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
        queryType: this.queryType,
        current: this.current,
        size: this.size,
        roomAllCount: "descCloum",
        haveModel: "0",
      };
      this.$toast.loading({
        message: "加载中...",
        forbidClick: true,
        overlay: false,
        duration: 0,
      });
      axios({
        method: "get",
        url: __PATH.BASE_API + "/space/client/getRoomCountAndAreaPageList",
        params: params,
        headers: {
          hospitalCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
          unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
          Authorization: "Bearer " + localStorage.getItem("token"),
        },
      }).then((res) => {
        console.log(res, "res");
        this.$toast.clear();
        this.loading = false;

        if (res.data.code == 200) {
          if (params.queryType == "dept") {
            res.data.data.records.forEach((item) => {
              if (item.dataName == null) {
                item.dataName = "未知";
              }
              this.tableData.push(item);
            });
          } else {
            res.data.data.records.forEach((item) => {
              this.tableData.push(item);
            });
          }

          if (this.tableData.length >= res.data.data.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="stylus">
.inner{
  height:100%;
  overflow hidden
}
/deep/ .van-tabs__line{
  background-color: #3562DB;
}
.titSty{
  height:50px
  padding:5px 0;
display: flex;
align-items: center;
background-color: #e6effc;
justify-content: space-around;
margin-top:5px;
>P{
font-weight: bold;
  text-align center
  flex: 1
}
>P:nth-child(2){
  font-weight: bold;
  text-align center
  flex:2
}
>P:nth-child(5){
  font-weight: bold;
  text-align center
  flex:2
}
>P:nth-child(4){
  font-weight: bold;
  text-align center
  flex:2
}
}
.listSty{
display: flex;
justify-content: space-around;
align-items: center;

margin-top:5px;
border-bottom: 1px solid #eee
>P{
  text-align center
  flex: 1;
  line-height:35px;
}
>P:nth-child(2){
  text-align center
  flex: 2;
  line-height:20px;
  padding:7px 0;
  // height:30px
}
>P:nth-child(4){
  text-align center
  flex: 2;
  line-height:20px;
  padding:7px 0;
  // height:30px
}>P:nth-child(5){
  text-align center
  flex: 2;
  line-height:20px;
  padding:7px 0;
  // height:30px
}
}
  .notList {
    position: relative;
    height: calc(90% - 1.24rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url('../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
  .tabs{
    height:40px;
    }
    .hei{
    height:calc(100% - 70px)
    }
    .hei2{
    height:calc(100% - 110px)
    overflow:auto
    }
  /deep/  .van-tab__text--ellipsis{
      font-weight: bold
    }
</style>
