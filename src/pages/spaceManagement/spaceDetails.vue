<template>
  <div class="inner">
    <Header title="空间详情" @backFun="goback"></Header>
    <div class="content">
      <div class="listWarp">
        <van-list>
          <div
            v-for="(item, index) in deviceList"
            :key="item.id"
            :class="
              index == 10 ? 'item items' : 'item'
            "
          >
            <span>{{ item.title }}</span>
            <span :id="'rowValue' + index" :class="item.flag ? 'overFlow' : ''">{{ item.value }}</span>
            <van-icon
              v-if="item.showArrowIcon"
              :name="item.flag ? 'arrow' : 'arrow-down'"
              @click="arrowClick(item)"
            />
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  data() {
    return {
      formInline: {},
      deviceInfo: {
        localSpaceName: "空间名称",
        simName: "所属位置",
        spaceState: "使用状态",
        localSpaceCode: "本地编码",
        modelCode: "模型编码",
        functionDictName: "功能类型",
        hight: "空间高度(m)",
        useArea: "使用面积(㎡)",
        area: "建筑面积(㎡)",
        remark: "备注说明",
        dmName: "归属部门",
        principalName: "空间负责人",
      },
      deviceList: []
    };
  },
  mounted() {
    console.log(this.$route.query);
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.loofUpByid();
  },
  methods: {
    goback() {
      if (this.$route.query.isClould) {
        this.$YBS.apiCloudCloseFrame()
      } else {
        this.$router.go(-1)
      }
    },
    arrowClick(item) {
      item.flag = !item.flag;
    },
    loofUpByid() {
      let params = {
        id: this.$route.query.id
      };
      axios({
        method: "get",
        url: __PATH.BASE_API + "/space/spaceInfo/lookUpById",
        params: params,
        headers: {
          hospitalCode: JSON.parse(localStorage.getItem("loginInfo"))
            .hospitalCode,
          unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
          Authorization: "Bearer " + localStorage.getItem("token")
        }
      }).then(res => {
        if (res.data.code === 200) {
          let newObj = res.data.data;
          this.deviceList = this.mergeObjectsIntoArrayObjects(
            newObj,
            this.deviceInfo
          );
          this.$nextTick(() => {
            this.deviceList.forEach((item, index) => {
              // 遍历数组 获取每个对象对应rowValue+index的dom元素的宽度和内容宽度
              const dom = document.getElementById("rowValue" + index)
              const itemWidth = dom.offsetWidth
              const itemScrollWidth = dom.scrollWidth
              if (itemWidth < itemScrollWidth) {
                item.showArrowIcon = true
              }
            })
          });
        }
      })
    },
    mergeObjectsIntoArrayObjects(origin, template) {
      let newArr = [],
        ran_char = (len = 10, min = 26) =>
          String.fromCharCode(Math.floor(Math.random() * len) + min);
      for (let key in template) {
        newArr.push({
          id: ran_char(26, 97) + ran_char(26, 65) + ran_char(10, 48),
          key,
          flag: true,
          showArrowIcon: false,
          title: template[key],
          value: origin[key]
        });
      }

      return newArr;
    }
  }
};
</script>

<style scoped lang="stylus">
.inner {
  background-color: #F2F4F9;
  min-height: 100vh;
  .content {
    margin-top: 1px;
    .listWarp {
      margin: 10px 0 0 0;
      .item {
        padding: 16px 16px;
        display: flex;
        font-size: 15px;
        color: #333;
        background-color: #fff;
        line-height:18px
        font-size: 17px
      }
      .item > span:nth-child(1) {
        display: inline-block;
        width:30%;
        color: #47515F;
      }
      .item > span:nth-child(2) {
        display: inline-block;
        width:65%;
        word-wrap:break-word;
      }
      .overFlow {
        overflow: hidden;  //超出隐藏
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号
      }
      .items{
        margin-top: .1875rem
      }
    }
    .bottomPrompt {
      margin: 0;
      padding: 0;
      font-size: 14PX;
      text-align: center;
      color: #bbb;
    }
  }
}
>>>.van-step--vertical .van-step__line {
  top: 24px;
  left: -15px;
  width: 1px;
  height: 85%;
}
.step{
  p{
    word-wrap:break-word;
    width: 100%;
	  overflow:hidden;   //超出隐藏
    text-overflow:ellipsis;  //溢出显示省略号
    white-space: normal;  //常规默认，会折行
    display:-webkit-box;
    -webkit-box-orient:vertical;  //子元素排列 vertical（竖排）orhorizontal（横排）
    -webkit-line-clamp:2;/*内容限制的行数 需要几行写几就行*/
  }
}
.stepsClass{
  background-color #fff;
  height:81vh;
  overflow: auto
}
</style>
