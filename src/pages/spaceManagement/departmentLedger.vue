<template>
  <div class="inner">
    <Header title="部门台账" @backFun="goBack"></Header>
    <div class="nav">
      <van-grid :border="false" :column-num="2">
        <van-grid-item>
          <div
            @click="space('')"
            :class="coll == '0' ? 'color1' : ''"
            style="
  text-align: center
      "
          >
            <p>
              <span class="sty">{{ spaceData.totalCount || 0 }}</span>
              <span>间</span>
            </p>
            <p class="p">房间总数</p>
          </div>
        </van-grid-item>
        <van-grid-item>
          <div
            @click="space('0')"
            :class="coll == '1' ? 'color1' : ''"
            style="
  text-align: center
      "
          >
            <p>
              <span class="sty">{{ spaceData.idleCount || 0 }}</span>
              <span>间</span>
            </p>
            <p class="p">闲置房间</p>
          </div>
        </van-grid-item>
        <van-grid-item>
          <div
            style="
  text-align: center
      "
          >
            <span class="sty">{{ spaceData.totalArea || 0 }}</span>
            <span class="col">㎡</span>
            <p></p>
            <p class="p">建筑面积</p>
          </div>
        </van-grid-item>
        <van-grid-item>
          <div
            style="
  text-align: center
      "
          >
            <span class="sty">{{ spaceData.publicArea || 0 }}</span>
            <span class="col">㎡</span>
            <p></p>
            <p class="p">公区面积</p>
          </div>
        </van-grid-item>
        <van-grid-item>
          <div
            style="
  text-align: center
      "
          >
            <p>
              <span class="sty">{{ spaceData.useArea || 0 }}</span>
              <span class="col">㎡</span>
            </p>
            <p class="p">使用面积</p>
          </div>
        </van-grid-item>
        <van-grid-item> </van-grid-item>
      </van-grid>
    </div>
    <div v-if="tableData.length > 0" class="hei">
      <div class="titSty">
        <p v-for="(item, index) in title" :key="index">{{ item.name }}</p>
      </div>
      <div class="hei2">
        <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <div v-for="(item, index) in tableData" :key="index" class="listSty">
              <p>{{ index + 1 }}</p>
              <p>{{ item.localSpaceName }}</p>
              <p>{{ item.simName }}</p>
              <p>{{ item.functionDictName }}</p>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
    <div v-else class="notList">
      <div class="emptyImg">
        <span class="emptyText">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
export default {
  data() {
    return {
      coll: "0",

      spaceState: "",
      spaceData: {
        idleCount: 0,
        publicArea: 0,
        totalArea: 0,
        totalCount: 0,
        useArea: 0
      },
      title: [
        {
          name: "序号"
        },
        {
          name: "空间名称"
        },
        {
          name: "所属位置"
        },
        {
          name: "功能类型"
        }
      ],
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      size: 20,
      total: 0,
      tableData: [],
      filters: {
        functionDictId: ""
      }
    };
  },
  mounted() {
    this.$YBS.apiCloudEventKeyBack(this.goBack);
    this.getRoomCountAndArea();
    this.spaceList();
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.tableData = [];
      this.spaceList();
    },

    onLoad() {
      console.log("执行了");
      this.finished = false;
      this.loading = true;
      this.current++;
      this.spaceList();
    },
    space(val) {
      if (val == "") {
        this.coll = 0;
      } else {
        this.coll = 1;
      }
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.spaceState = val;
      this.tableData = [];
      this.spaceList();
    },
    spaceList() {
      let params = {
        hospitalCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
        unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
        queryType: this.queryType,
        current: this.current,
        size: this.size,
        simCode: "",
        // simCode:'#,1574997196057620481,1574997196330250241,1574997196833566721,1574997196959395842',
        spaceState: this.spaceState,
        functionDictId: "",
        departmentId: JSON.parse(localStorage.getItem("loginInfo")).deptId
        // departmentId: 910
      };

      this.$api.spaceList(params).then(res => {
        this.loading = false;

        res.records.forEach(item => {
          this.tableData.push(item);
        });

        if (this.tableData.length >= res.total) {
          this.finished = true;
          this.loading = false;
          return;
        }
      });
    },
    getRoomCountAndArea() {
      let params = {
        modelCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
        deptIds: JSON.parse(localStorage.getItem("loginInfo")).deptId,
        haveModel: "0"
      };

      axios({
        method: "get",
        url: __PATH.BASE_API + "/space/client/getRoomCountAndArea",
        params: params,
        headers: {
          hospitalCode: JSON.parse(localStorage.getItem("loginInfo")).hospitalCode,
          unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
          Authorization: "Bearer " + localStorage.getItem("token")
        }
      }).then(res => {
        if (res.data.code === 200) {
          Object.assign(this.spaceData, res.data.data);
          // 计算公共区域面积 = 建筑面积 - 使用面积
          this.spaceData.publicArea = this.spaceData.publicArea ? this.spaceData.publicArea : this.spaceData.totalArea - this.spaceData.useArea;
        }
      });
    }
  }
};
</script>

<style scoped lang="stylus">
.inner{
  height:100%;
  overflow hidden

}
.sty{
  font-size: 18px;
  font-weight: bold;
}
.col{
  color:#86909C
}
.p{
  line-height:30px
}
/deep/ .van-nav-bar__title{
  margin: 0 15px
}
.titSty{
  height:30px

display: flex;
align-items: center;
  padding:10px 0;
background-color: #e6effc;
justify-content: space-around;
// margin-top:100px;

>P:nth-child(1){
  font-weight: bold;
  text-align center
  flex:1
}>P:nth-child(2){
  font-weight: bold;
  text-align center
  flex:2
}>P:nth-child(3){
  font-weight: bold;
  text-align center
  flex:2
}>P:nth-child(4){
  font-weight: bold;
  text-align center
  flex:2
}
}
.listSty{
display: flex;
justify-content: space-around;
align-items: center;
margin-top:5px;
border-bottom: 1px solid #eee

>P:nth-child(1){
  text-align center
  flex: 1;
  line-height:20px;
  padding:7px 0;
  // height:30px
}
>P:nth-child(2){
  text-align center
  flex: 2;
  line-height:20px;
  padding:7px 0;
  // height:30px
}>P:nth-child(3){
  text-align center
  flex: 2;
  line-height:20px;
  padding:7px 0;
  // height:30px
}>P:nth-child(4){
  text-align center
  flex:2;
  line-height:20px;
  padding:7px 0;
  // height:30px
}
}
.color{
  width:100%;
  height:100%;
  color: #3562DB !important;
  background-color:#f3f7ff
  padding:10px;1px
}
.nav{
    // height:215px;
    // height:60px;
  margin-top: 10px;
  display: flex;
  justify-content: space-around;
  text-align: center;
  >div{
    flex: 1
  }
    }
  .hei{
    height:calc(100% - 165px)
    }
    .hei2{
    height:calc(100% - 200px)
    overflow:auto
    }
      .notList {
    position: relative;
    height: calc(90% - 3.24rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      background: url('../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
    .color1{
    color:#3562DB
  }
</style>
