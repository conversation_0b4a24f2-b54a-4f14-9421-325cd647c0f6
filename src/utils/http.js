import axios from 'axios'
import qs from 'qs'

axios.defaults.timeout = 30000

/*axios.interceptors.request.use(config => {    // 这里的config包含每次请求的内容
  // 获取localStorage中的hospitalCode和unitCode

  if(config.method == "get"){
    config.params = config.params || {}
    try {
      config.params.hospitalCode = JSON.parse(localStorage.getItem('loginInfo')).userOffice[0].hospitalCode
      config.params.unitCode = JSON.parse(localStorage.getItem('loginInfo')).userOffice[0].unitCode
    }catch(error){}
  }
  if(config.method == 'post'){
    config.data = config.data || {}
    try {
      config.data.hospitalCode = localStorage.getItem('loginInfo').userOffice[0].hospitalCode
      config.data.unitCode = localStorage.getItem('loginInfo').userOffice[0].unitCode
      config.data = qs.stringify(config.data)
    }catch(error){}
  }
  return config;
}, err => {
  return Promise.reject(err)
})*/

axios.interceptors.response.use(undefined, function(error){
  $.alert("网络连接超时，请稍后重试","",function() {
    $.hideLoading()
  })
});

// 请求方式的配置
export default axios
