export default class DragHelper {
  constructor(options = {}) {
    this.position = {
      x: options.initX || 0,
      y: options.initY || 0
    };
    this.isDragging = false;
    this.startPos = {
      x: 0,
      y: 0
    };
  }

  // 初始化位置
  initPosition(element) {
    if (element) {
      this.position.x = window.innerWidth - (element.offsetWidth + 20);
      this.position.y = window.innerHeight - 100;
    }
  }

  // 開始拖拽
  dragStart(e) {
    this.isDragging = true;
    this.startPos = {
      x: e.touches[0].clientX - this.position.x,
      y: e.touches[0].clientY - this.position.y
    };
  }

  // 拖拽中
  dragMove(e) {
    if (!this.isDragging) return;

    e.preventDefault();

    // 計算新位置
    let newX = e.touches[0].clientX - this.startPos.x;
    let newY = e.touches[0].clientY - this.startPos.y;

    // 獲取視窗尺寸
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 獲取元素尺寸
    const element = e.target;
    const elementWidth = element.offsetWidth;
    const elementHeight = element.offsetHeight;

    // 限制不超出視窗範圍
    newX = Math.max(0, Math.min(newX, windowWidth - elementWidth));
    newY = Math.max(0, Math.min(newY, windowHeight - elementHeight));

    // 更新位置
    this.position = {
      x: newX,
      y: newY
    };

    return this.position;
  }

  // 結束拖拽
  dragEnd() {
    this.isDragging = false;
  }

  // 判斷是否為點擊事件
  isClick() {
    return !this.isDragging;
  }
}
