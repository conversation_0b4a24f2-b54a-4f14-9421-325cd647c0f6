/*
 * @Author: hedd
 * @Date: 2023-10-09 09:52:58
 * @LastEditTime: 2024-03-20 14:23:16
 * @FilePath: \ybs_h5\src\utils\energyData.js
 * @Description:
 */
const kgceToken="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJvcmdDb3JlIjoiQTAxIiwiaWQiOiI3YzY4MzhmOGI5OTY0YjA4YjQzYTdjYTU4MmNhNGU5MSIsImV4cCI6MTY4MTg5MjQwMSwidXNlcm5hbWUiOiJneV9lbmVyZ3kiLCJyZWFsbmFtZSI6Iuezu-e7n-i_kOe7tOS6uuWRmCJ9.kmwPj-HrWwQF4cdkYxykb6OYy8J4BScJEB7w0NzZGpw";
const electricity={
    id: "SU035",
    name: "电力",
    children:[
        {
            children:[],
            id:"7d5525aa16dc6a284b8048ffbb3c4730",
            name: "设备用电"
        },
        {
            children:[],
            id:"SU061",
            name: "照明用电"
        },
        {
            children:[],
            id:"SU062",
            name: "插座用电"
        }
    ]
}
const water={
    id: "CM020",
    name: "用水",
    children:[
        {
            children:[],
            id: "2f971f3079fa1a86cf84760001574eb6",
            name: "热水用水"
        },
        {
            children:[],
            id: "567cd67b1d0635cb7873ea0939ada40a",
            name: "冷水用水"
        }
    ]
}
const hot={
    id: "4c78a70e0b7deb51b0a227fb2cd9196f",
    name: "热能",
    children:[]
}
const gas={
    id: "66655f36bc0dc86adbaf2cab80d9dce2",
    name: "蒸汽",
    children:[]
}
export default {
    kgceToken,
    electricity,
    water,
    hot,
    gas
}
