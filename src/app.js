import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "@/assets/iconFont/iconfont.css";
import autoComponents from "@/common/autoComponents/index.js";
Vue.use(autoComponents);
import { Slide, Select as CUSelect, Radio as CURadio, Checkbox as CUCheckbox, ActionSheet } from "cube-ui";
import CubeUI from 'cube-ui'
Vue.use(Slide);
Vue.use(CUSelect);
Vue.use(CURadio);
Vue.use(CUCheckbox);
Vue.use(ActionSheet);
Vue.use(CubeUI)
import Api from "./api";
import utils from "@/utils/Global";
import "../static/common.css";
import { CellSwipe } from "mint-ui";

Vue.component(CellSwipe.name, CellSwipe);

import VideoPlayer from 'vue-video-player'
import 'vue-video-player/src/custom-theme.css'
import 'video.js/dist/video-js.css'

Vue.use(VideoPlayer)

import { TimeLine, TimeLineItem } from "@nutui/nutui";
// import "@nutui/nutui/dist/nutui.css";
TimeLine.install(Vue);
TimeLineItem.install(Vue);
import {
  Table,
  TableColumn,
  Link,
  Button as ELButton,
  Progress as ELProgress,
  Form as ELForm,
  FormItem,
  Checkbox as ELCheckbox,
  CheckboxButton,
  CheckboxGroup as ELCheckboxGroup,
  Input,
  RadioGroup as ELRadioGroup,
  Radio as ELRadio,
  RadioButton as ELRadioButton,
  Cascader as ELCascader,
  Option,
  OptionGroup,
  Row as ELRow,
  Col as ELCol,
  Select
} from "element-ui";
Vue.use(Table);
Vue.use(TableColumn);
Vue.use(Link);
Vue.use(ELButton);
Vue.use(ELProgress);
Vue.use(ELForm);
Vue.use(FormItem);
Vue.use(ELCheckbox);
Vue.use(CheckboxButton);
Vue.use(ELCheckboxGroup);
Vue.use(Input);
Vue.use(ELRadioGroup);
Vue.use(ELRadio);
Vue.use(ELRadioButton);
Vue.use(ELCascader);
Vue.use(Option);
Vue.use(OptionGroup);
Vue.use(ELRow);
Vue.use(ELCol);
Vue.use(Select);

//引入基本模板
let echarts = require("echarts/lib/echarts");

// 引入折线图等组件
require("echarts/lib/chart/line");
require("echarts/lib/chart/bar");
require("echarts/lib/chart/radar");
require("echarts/lib/chart/pie");

// 引入提示框和title组件，图例
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
require("echarts/lib/component/legendScroll"); //图例翻译滚动
require("echarts/lib/component/dataZoom");
Vue.prototype.$echarts = echarts;
import YBS from "@/assets/utils/utils.js";
Vue.prototype.$YBS = YBS;
import trunced from "@/assets/utils/trunced.js";
Vue.use(trunced);
import "element-ui/lib/theme-chalk/index.css";
import {
  Sticky,
  Switch,
  Cell,
  Calendar,
  DatetimePicker,
  Popup,
  Tab,
  Tabs,
  Button,
  RadioGroup,
  Radio,
  Field,
  Icon,
  Badge,
  Search,
  List,
  Step,
  Steps,
  Uploader,
  Empty,
  PullRefresh,
  Collapse,
  CollapseItem,
  Checkbox,
  CheckboxGroup,
  Picker,
  IndexBar,
  IndexAnchor,
  TreeSelect,
  Cascader,
  Form,
  Toast,
  Grid,
  GridItem,
  NavBar,
  CellGroup,
  Progress,
  Col,
  Row,
  DropdownMenu,
  DropdownItem,
  Popover,
  Slider,
  Loading,
  Circle,
  ActionSheet as vantActionSheet,
  Dialog,
  Stepper,
  CountDown
} from "vant";
Vue.use(Sticky);
Vue.use(Loading);
Vue.use(Popover);
Vue.use(Switch);
Vue.use(Col);
Vue.use(Row);
Vue.use(Progress);
Vue.use(Cell);
Vue.use(Popup);
Vue.use(Calendar);
Vue.use(DatetimePicker);
Vue.use(Tab);
Vue.use(Tabs);
Vue.use(Button);
Vue.use(RadioGroup);
Vue.use(Radio);
Vue.use(Field);
Vue.use(Icon);
Vue.use(Badge);
Vue.use(Search);
Vue.use(List);
Vue.use(Step);
Vue.use(Steps);
Vue.use(Uploader);
Vue.use(Empty);
Vue.use(PullRefresh);
Vue.use(Collapse);
Vue.use(CollapseItem);
Vue.use(Checkbox);
Vue.use(CheckboxGroup);
Vue.use(Picker);
Vue.use(IndexBar);
Vue.use(IndexAnchor);
Vue.use(TreeSelect);
Vue.use(Cascader);
Vue.use(Form);
Vue.use(Toast);
Vue.use(Grid);
Vue.use(GridItem);
Vue.use(NavBar);
Vue.use(CellGroup);
Vue.use(DropdownMenu);
Vue.use(DropdownItem);
Vue.use(Slider);
Vue.use(Circle);
Vue.use(vantActionSheet);
Vue.use(Dialog);
Vue.use(Stepper);
Vue.use(CountDown);
import fastClick from "fastclick";
console.log("fast");

fastClick.prototype.focus = function (targetElement) {
  let length;
  if (targetElement.setSelectionRange && targetElement.type.indexOf("date") !== 0 && targetElement.type !== "time" && targetElement.type !== "month") {
    length = targetElement.value.length;
    targetElement.focus();
    targetElement.setSelectionRange(length, length);
  } else {
    targetElement.focus();
  }
};

import qs from "qs";
import axios from "./utils/http.js";
import detector from "detector";
const body = document.getElementsByTagName("body")[0];

import vuePicturePreview from "vue-picture-preview";
Vue.use(vuePicturePreview);

Vue.prototype.$api = Api;
Vue.prototype.$qs = qs;
Vue.prototype.axios = axios;
Vue.prototype.detector = detector;
Vue.prototype.body = body;
Vue.prototype.$fastClick = fastClick;

import weui from "jquery-weui/dist/js/jquery-weui.min";
import Weui from "weui/dist/style/weui.min.css";
import JqWeui from "../static/jquery-weui.min.css";
// import JqWeui from "jquery-weui/dist/css/jquery-weui.min.css";

import "styles/reset.css";
import "styles/border.css";
import "styles/iconfont.css";
import "styles/trigger.css";

import "daterangepicker/daterangepicker.css";

import "babel-polyfill";
import Es6Promise from "es6-promise";
Es6Promise.polyfill();

Vue.config.productionTip = false;
fastClick.attach(document.body);

/*出现弹窗时，防抖作用*/
let target = document.querySelector("body");
var observer = new MutationObserver(function (mutations) {
  try {
    if (mutations[0].addedNodes[0]) {
      if (mutations[0].addedNodes[0].classList[0] == "weui-picker-container") {
        body.setAttribute("style", "overflow:hidden");
      }
    }
    if (mutations[0].removedNodes[0]) {
      if (mutations[0].removedNodes[0].classList[0] == "weui-picker-container") {
        body.removeAttribute("style");
      }
    }
  } catch (e) { }
  try {
    if (mutations[0].addedNodes[0]) {
      if (mutations[0].addedNodes[0].classList[0] == "weui-mask") {
        body.setAttribute("style", "overflow:hidden");
      }
    }
    if (mutations[0].removedNodes[0]) {
      if (mutations[0].removedNodes[0].classList[0] == "weui-mask") {
        body.removeAttribute("style");
      }
    }
  } catch (e) { }
});
var config = { attributes: true, childList: true, characterData: true };
observer.observe(target, config);
router.beforeEach((to, from, next) => {
  // 订餐页面特殊处理，如无其他兼容性，此处不做修改
  if (to.query.hasOwnProperty('spaceCode')) {
    const spaceCode = to.query.spaceCode.split(',') || []
    if (spaceCode && spaceCode[0] == 'DC') {
      next({
        path: '/orderPage',
        query: {
          id: spaceCode[1]
        }
      })
    }
  }
  if (to.name) {
    next();
  } else {
    next({ path: "/404" });
  }
});
router.afterEach((to, from) => {
  // 离开页面 清除页面绑定的右滑返回事件
  try {
    api.removeEventListener({
      name: "keyback666"
    })
  } catch (error) {
  }
});
console.log("Build Time:", BUILD_TIME);
new Vue({
  el: "#app",
  router,
  store,
  components: { App },
  template: "<App/>"
});
