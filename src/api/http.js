/*
 * @Author: hedd
 * @Date: 2023-04-04 17:15:16
 * @LastEditTime: 2025-03-20 10:55:20
 * @FilePath: \ybs_h5\src\api\http.js
 * @Description:
 */
// 导入封装好的axios实例
import request from "./server";
import qs from "qs";
function getLoginData() {
  let loginData = JSON.parse(localStorage.getItem("loginData")) || ''
  return loginData;
}

function getUserCode() {
  let unitCode = localStorage.loginInfo ? JSON.parse(localStorage.loginInfo).unitCode : sessionStorage.unitCode ? sessionStorage.getItem("unitCode") : "";
  let hospitalCode = localStorage.loginInfo ? JSON.parse(localStorage.loginInfo).hospitalCode : sessionStorage.hospitalCode ? sessionStorage.getItem("hospitalCode") : "";
  let userCode = {
    hospitalCode: hospitalCode,
    unitCode: unitCode
  };
  return userCode;
}

export function server(data = {}) {
  console.log(data);
  const url = data.url;
  let params = data.data;
  // $.showLoading();
  const config = {
    method: "post",
    url: url,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: "Bearer " + localStorage.getItem("token")
    },
    data: params
  };
  // Object.assign(config.data, params);
  if (
    !url.includes("userLogin") &&
    !url.includes("getOpenid") &&
    !url.includes("getHospitalOfficeInfo") &&
    !url.includes("getDictArray") &&
    !url.includes("getHospitalListAndLogoByUnitCode") &&
    !url.includes("cascadingQueryAreaList") &&
    !url.includes("getHospitalIsRisterByCode")
  ) {
    config.data.unitCode = getUserCode().unitCode;
    config.data.hospitalCode = getUserCode().hospitalCode;
  }
  config.data = qs.stringify(config.data);
  return request(config);
}
export function postRequest(data = {}) {
  $.showLoading();
  let userCode = getUserCode();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "post",
    url: url,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + localStorage.getItem("token"),
      ...userCode
    }
  };
  config.data = { ...params, ...userCode };
  return request(config);
}
export function postRequestNoParams(data = {}) {
  $.showLoading();
  let userCode = getUserCode();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "post",
    url: url,
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + localStorage.getItem("token"),
      ...userCode,
    }
  };
  config.data = { ...params };
  return request(config);
}
export function postProject(data = {}) {
  $.showLoading();
  let userCode = getUserCode();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "post",
    url: url,
    headers: {
      "Content-Type": "application/json",
      userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
      ...userCode
    }
  };
  config.data = { ...params, ...userCode };
  return request(config);
}
export function postParamsQS (data = {}, contentType = "form") {
  $.showLoading();
  let userCode = getUserCode();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "post",
    url: url,
    headers: {
      "Content-Type": contentType == "json" ? "application/json" : "application/x-www-form-urlencoded;charset=utf8",
      Authorization: "Bearer " + localStorage.getItem("token"),
      ...userCode
    }
  };
  config.data = qs.stringify({ ...params, ...userCode });
  return request(config);
}
export function getRequest(data = {}) {
  $.showLoading();
  let userCode = getUserCode();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "get",
    url: url + "?" + qs.stringify(params),
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + localStorage.getItem("token"),
      ...userCode
    }
  };
  if (!localStorage.getItem("token")) {
    delete config.headers.Authorization;
  }
  // config.data = { ...params, ...userCode };
  return request(config);
}
export function getProject(data = {}) {
  $.showLoading();
  let userCode = getUserCode();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "get",
    url: url + "?" + qs.stringify(params),
    headers: {
      "Content-Type": "application/json",
      userId: JSON.parse(localStorage.getItem("loginInfo")).staffId,
      ...userCode
    }
  };
  // config.data = { ...params, ...userCode };
  return request(config);
}
export function getRequestLinen(data = {}) {
  $.showLoading();
  let userCode = getUserCode();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "get",
    url: url + "?" + qs.stringify(userCode) + "&" + qs.stringify(params),
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + localStorage.getItem("token")
    }
  };
  // config.data = { ...params, ...userCode };
  return request(config);
}
export function postRequestKgce(data = {}) {
  $.showLoading();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "post",
    url: url
  };
  if (params) config.data = params;
  return request(config);
}
export function getRequestKgce(data) {
  $.showLoading();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "get",
    url: url,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=utf8",
      "X-Access-Token": sessionStorage.getItem("kgceToken") || ""
    }
  };
  if (params) config.params = params;
  return request(config);
}
export function postRequestNoToken(data = {}) {
  $.showLoading();
  let userCode = getUserCode();
  const url = data.url;
  let params = data.data;
  const config = {
    method: "post",
    url: url,
    headers: {
      "Content-Type": "application/json",
      ...userCode
    }
  };
  config.data = { ...params, ...userCode };
  return request(config);
}
export function post_json(data = {}) {
  const url = data.url
  let params = data.data;
  let baseInfo = getLoginData();
    let userData = {};
    if (baseInfo) {
      userData = {
        unitCode: baseInfo.unitCode,
        hospitalCode: baseInfo.hospitalCode,
        hospitalName: baseInfo.hospitalName,
        userId: baseInfo.id,
        userName: baseInfo.name,
        // gridIds: baseInfo.gridIds,
        userTeamId: baseInfo.controlGroupIds,
        officeId: baseInfo.officeId,
        officeName: baseInfo.officeName,
        // teamId: baseInfo.type != 1 ? baseInfo.officeCode : "",
        controlGroupIds: baseInfo.controlGroupIds,
        controlGroupNames: baseInfo.controlGroupNames,
        roleCode: baseInfo.roleCode,
        positionType: baseInfo.positionType,
        platformFlag: 2,
        phone: baseInfo.phone
      };
    }
    $.showLoading();
    const config = {
      method: "post",
      url: url,
      headers: {
        'Content-Type': 'application/json;charset=utf-8',
        'token': baseInfo.token
          // Authorization: "Bearer " + JSON.parse(localStorage.getItem("loginData.token"))
      },
      data: {...userData,...params}
    };
    return request(config);
}
export function post_formData(data = {}) {
    const url = data.url
    let params = data.data;
    let baseInfo = getLoginData()
    let routeInfo ={}
    if(baseInfo){
      routeInfo.unitCode = baseInfo.unitCode;
      routeInfo.hospitalCode = baseInfo.hospitalCode;
    }
    $.showLoading();
    const config = {
      method: "post",
      url: url,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded;charset=utf8",
       'token': baseInfo.token
      },
    }
    config.data = qs.stringify({...routeInfo,...params});
    return request(config);

}
