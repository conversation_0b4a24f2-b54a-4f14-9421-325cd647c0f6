/*
 * @Author: hedd <EMAIL>
 * @Date: 2025-03-17 13:41:13
 * @LastEditors: hedd <EMAIL>
 * @LastEditTime: 2025-03-20 10:48:11
 * @FilePath: \ybs_h5\src\api\constructionOperation.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { server, postRequest, postParamsQS, getRequest, getRequestLinen, postRequestKgce, getRequestKgce, postProject, getProject, postRequestNoToken, postRequestNoParams } from "./http";
let sApi = __PATH.SPACE_API;
// let sApi2 = 'http://************:9799';
let spaceApi = __PATH.BASE_API;
let sporadicApi = __PATH.SPORADIC_API;
// 施工单位
const uploadImgPro = data => postRequest({ url: `${sApi}/SecurityLedger/upload`, data }); // 上传图片
const addConstructionUnit = data => postRequest({ url: `${sApi}/constructionUnitController/appAddConstructionUnit`, data }); // 添加施工单位
const getConstructionUnitList = data => postRequest({ url: `${sApi}/constructionUnitController/selectConstructionUnit`, data }); // 获取施工单位列表
const getConstructionUnitDetail = data => postRequest({ url: `${sApi}/constructionUnitController/getOperationStaffDetails`, data }); // 获取施工单位详情
const updateConstructionUnit = data => postRequest({ url: `${sApi}/constructionUnitController/updateConstructionUnit`, data }); // 修改施工单位
const getPersonnelList = data => postRequest({ url: `${sApi}/fieldOperationStaffController/selectOperationStaff`, data }); // 获取人员列表
const addPersonnel = data => postRequest({ url: `${sApi}/fieldOperationStaffController/appAddOperationStaff`, data }); // 添加人员
const getPersonnelDetail = data => postRequest({ url: `${sApi}/fieldOperationStaffController/getOperationStaffDetails`, data }); // 获取人员详情
const updatePersonnel = data => postRequest({ url: `${sApi}/fieldOperationStaffController/updateOperationStaff`, data }); // 修改人员
const getWorkTypes = data => postRequest({ url: `${sApi}/assignmentBusinessForm/queryAssignmentBusinessFormAll`, data }); // 获取作业类型列表
const getspaceAll = data => getRequest({ url: `${spaceApi}/space/structure/spaceTree`, data }); // 获取空间列表
const getStartWorkFlowForm = data => postRequest({ url: `${sApi}/assignmentInfo/getStartWorkFlowForm`, data }); // 获取工作流表单
const createAssignment = data => postRequestNoParams({ url: `${sApi}/assignmentInfo/createAssignment`, data }); // 创建作业
const getApprovalList = data => postRequest({ url: `${sApi}/assignmentInfo/queryAssignmentInfoByPage`, data }); // 获取审批列表
const getApprovalDetail = data => postRequest({ url: `${sApi}/assignmentInfo/getAssignmentInfoDetails`, data }); // 获取审批详情
const getDetailByWorkFlow = data => postRequest({ url: `${sApi}/assignmentInfo/getDetailByWorkFlow`, data }); // 获取工作流表单
const projectHandle = data => postRequestNoParams({ url: `${sApi}/assignmentInfo/projectHandle`, data }); // 工作流审批
const getPatroList = data => postRequest({ url: `${sApi}/maintain/plan/pageList`, data }); // 获取巡查列表记录
const getPatroListNew = data => postRequest({ url: `${sApi}/maintain/plan/pageListExecutable`, data }); // 获取巡查列表 新

const getPatroDetail = data => postRequest({ url: `${sApi}/maintain/plan/securityDetail`, data }); // 获取巡查详情(查看时)
const getPatroRecodeDetail = data => postRequest({ url: `${sApi}/maintain/plan/submissionDetail`, data }); // 获取巡查详情(执行时)
const getPatroSubmit = data => postRequest({ url: `${sApi}/maintain/plan/submissionQualified`, data }); // 巡查执行
// const getPatroRecodeList = data => postRequest({ url: `${sApi}/maintain/plan/recordList`, data }); // 巡查执行
const getPatroRecodeList = data => postRequest({ url: `${sApi}/maintain/plan/appRecordList`, data }); // 巡查执行
const getAssignmentTypeStatistics = data => postRequest({ url: `${sApi}/assignmentInfo/getAssignmentStateStatistics`, data }); // 获取作业台账统计
const getAssignmentList = data => postRequest({ url: `${sApi}/assignmentInfo/queryAssignmentInfoByPage`, data }); // 获取作业台账列表
const getApplyDept = data => getRequest({ url: `${spaceApi}/departmentManager/department-manager/getSelectedDept`, data }); // 获取申请科室
const professionalTypeList = data => postRequest({ url: `${sApi}/professionalType/list`, data }); // 获取专业类型列表
const buildingStewardPage = data => postRequest({ url: `${sApi}/buildingSteward/page`, data }); // 获取楼宇列表
const getAssociatedProList = data => postRequest({ url: `${sporadicApi}/projectInfo/queryProjectInfoByDeptId`, data }); // 获取关联项目列表
const sscPatroSubmit = data => postRequest({ url: `${sApi}/maintain/plan/submissionQualifiedSsc`, data }); // 执行随手查
const detailsTabs = data => postRequest({ url: `${sApi}/assignmentInfo/queryTabColumByTabType`, data }); // 过程管理和施工验收
const workCardDetail = data => postRequest({ url: `${sApi}/assignmentInfo/queryWorks`, data }); // 查看作业证
const sscTaskDetail = data => postRequest({ url: `${sApi}/maintain/record/detail`, data }); // 随手查任务详情
const getZysqProjectDefaultField = data => getRequest({ url: `${sporadicApi}/projectInfo/getExtendDataByInstanceId`, data }); // 关联项目默认值
const getSgyqDefaultField = data => getRequest({ url: `${sApi}/assignmentInfo/getProcessDataFromWorkFlow`, data }); // 获取施工要求默认值
const getAppTaskList = data => postRequest({ url: `${sApi}/maintain/record/getAppTaskList`, data }); // 施工巡检任务列表
const getAppTaskListCount = data => postRequest({ url: `${sApi}/maintain/record/getAppTaskListCount`, data }); // 施工巡检任务列表统计

export default {
    uploadImgPro,
    addConstructionUnit,
    getConstructionUnitList,
    getConstructionUnitDetail,
    updateConstructionUnit,
    getPersonnelList,
    addPersonnel,
    getPersonnelDetail,
    updatePersonnel,
    getWorkTypes,
    getspaceAll,
    getStartWorkFlowForm,
    createAssignment,
    getApprovalList,
    getApprovalDetail,
    getDetailByWorkFlow,
    projectHandle,
    getPatroList,
    getPatroListNew,
    getPatroDetail,
    getPatroRecodeDetail,
    getPatroSubmit,
    getPatroRecodeList,
    getAssignmentTypeStatistics,
    getAssignmentList,
    getApplyDept,
    professionalTypeList,
    buildingStewardPage,
    getAssociatedProList,
    sscPatroSubmit,
    detailsTabs,
    workCardDetail,
    sscTaskDetail,
    getZysqProjectDefaultField,
    getSgyqDefaultField,
    getAppTaskList,
    getAppTaskListCount
}
