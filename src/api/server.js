/*
 * @Author: hedd
 * @Date: 2023-04-25 15:41:06
 * @LastEditTime: 2023-11-08 14:36:21
 * @FilePath: \ybs_h5\src\api\server.js
 * @Description:
 */
import axios from "axios";
import Vue from "vue";
// import qs from "qs";
// import store from "../store";

const vm = new Vue();
var instance = axios.create({
  timeout: 3600 * 1000,
  // headers: {
  //   "Content-Type": "application/x-www-form-urlencoded",
  //   // 'Authorization':'Bearer '+localStorage.getItem("token")
  //   'Authorization':'Bearer '+localStorage.getItem("token")
  // },
  withCredentials: false,
});
/**
 * @param {config.ifCode} 是否需要返回带code的对象,不论成功失败
 */

instance.interceptors.request.use(
  function (config) {
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  function (response) {
    $.hideLoading();
    if (response.config.url.indexOf("getAllOffice") >= 0) {
      return Promise.resolve(response.data);
    }
    if (response.data.code == 200) {
      if (response.config.url.indexOf("appDisOlgTask.do?updateTask") >= 0) {
        $.toast(response.data.message, "text");
      }
      if (response.config.url.indexOf("sys/login") != -1 || response.config.url.indexOf("questionPvq/recovery/selfQuestionListToWx") != -1) {
        return Promise.resolve(response.data);
      }
      return Promise.resolve(response.data.data);
    } else if (response.data.code == 400) {
      console.log(response.data.message);
    } else if (response.data.code == 500) {
      if (response.config.ifCode) {
        return Promise.resolve(response.data);
      }
      $.toast(response.data.message || response.data.msg, "text");
    } else if (response.data.code == 205) {
      $.toast(response.data.message, "cancel", function () {
        wx.closeWindow();
      });
    }
    if (response.config.ifCode) {
      return Promise.resolve(response.data);
    }
    return Promise.reject(response);
  },
  function (error) {
    console.log(error, "error");
    return Promise.reject(error);
  }
);

export default instance;
