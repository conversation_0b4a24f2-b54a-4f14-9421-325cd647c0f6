<template>
  <div class="page-layout">
    <!-- 头部区域 -->
    <div v-if="showHeaderComputed" class="page-header">
      <slot name="header">
        <Header :title="computedTitle" @backFun="handleBack"></Header>
      </slot>
    </div>

    <!-- 内容区域 -->
    <div
      class="page-content"
      :class="{
        'has-footer': $slots.footer,
        'no-header': !showHeaderComputed
      }"
    >
      <slot></slot>
    </div>

    <!-- 底部区域 -->
    <div v-if="$slots.footer" class="page-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'PageLayout',
  props: {
    title: {
      type: String,
      default: ''
    },
    showHeader: {
      type: Boolean,
      default: undefined
    },
    customBack: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    ...mapState(["h5Mode"]),
    computedTitle() {
      return this.title || (this.$route.meta && this.$route.meta.title) || this.$route.name || '';
    },
    showHeaderComputed() {
      // 优先使用组件prop
      if (this.showHeader !== undefined) {
        return this.showHeader;
      }
      // 其次使用路由配置
      if (this.$route.meta && this.$route.meta.showHeader !== undefined) {
        return this.$route.meta.showHeader;
      }
      // 最后根据环境判断
      return this.h5Mode !== 'miniPrograms';  // 如果是小程序环境就不显示header，后期可动态调整
    }
  },
  methods: {
    handleBack() {
      // 判断是否有back事件监听
      if (this.$listeners && this.$listeners.back) {
        this.$emit('back');
        return;
      }
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.page-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f2f4f9;

  .page-header {
    flex-shrink: 0;
  }

  .page-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 0px;

    &.has-footer {
      padding-bottom: 70px;
    }

    &.no-header {
      padding-top: 0;
    }
  }

  .page-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 99;
  }
}
</style>
