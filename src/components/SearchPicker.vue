<template>
  <van-picker
    ref="picker"
    :columns="currentColumns"
    show-toolbar
    @confirm="onConfirm"
    @cancel="onCancel"
    :default-index="defaultIndex"
  >
    <template #default>
      <div class="default-box">
        <div class="default-action default-cancel" @click="handleCancel">取消</div>
        <div class="search-wrapper">
          <van-search
            v-model="searchValue"
            placeholder="请输入搜索关键词"
            @search="handleSearch"
            @clear="handleClear"
          />
        </div>
        <div class="default-action default-confirm" @click="handleConfirm">确定</div>
      </div>
    </template>
    <template #option="option">
      <div v-if="mode === 'single'" class="picker-item">
        <span class="item-text">{{ getValueByKey(option, fieldProps.label) }}</span>
      </div>
      <div v-if="mode === 'multiple'" class="picker-item picker-item--multiple">
        <span class="item-text">{{ getValueByKey(option, fieldProps.label) }}</span>
        <van-checkbox
          :value="isItemSelected(option)"
          @click.stop="toggleSelection(option)"
          checked-color="#3562db"
        />
      </div>
    </template>
  </van-picker>
</template>

<script>
export default {
  name: 'SearchPicker',
  props: {
    columns: {
      type: Array,
      required: true,
      default: () => []
    },
    defaultIndex: {
      type: Number,
      default: 0
    },
    mode: {
      type: String,
      default: 'single',
      validator: value => ['single', 'multiple'].includes(value)
    },
    fieldProps: {
      type: Object,
      default: () => ({
        label: 'label', // 显示的文本字段
        value: 'value' // 实际值字段
      })
    },
    value: {
      type: [String, Number, Object, Array],
      default: null
    }
  },
  data() {
    return {
      searchValue: '',
      currentColumns: [],
      selectedIndex: 0,
      selectedValues: [],
      innerValue: null
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (this.mode === 'single') {
          this.initSingleSelection(newVal)
        } else {
          this.initMultipleSelection(newVal)
        }
      }
    },
    columns: {
      immediate: true,
      handler(val) {
        this.currentColumns = val
        if (this.value) {
          if (this.mode === 'single') {
            this.initSingleSelection(this.value)
          } else {
            this.initMultipleSelection(this.value)
          }
        }
      }
    }
  },
  methods: {
    initSingleSelection(value) {
      if (!value || !this.columns.length) return

      const selectedItem = this.columns.find(item => {
        const itemValue = this.getValueByKey(item, this.fieldProps.value)
        return itemValue === value
      })

      if (selectedItem) {
        this.innerValue = selectedItem
        const index = this.columns.indexOf(selectedItem)
        this.selectedIndex = index
        if (this.$refs.picker) {
          this.$refs.picker.setIndexes([index])
        }
      }
    },

    initMultipleSelection(values) {
      if (!values || !Array.isArray(values) || !this.columns.length) {
        this.selectedValues = []
        return
      }

      this.selectedValues = this.columns.filter(item => {
        const itemValue = this.getValueByKey(item, this.fieldProps.value)
        return values.includes(itemValue)
      })
    },

    getValueByKey(item, key) {
      if (typeof item === 'string') return item
      return item[key] || ''
    },

    handleSearch() {
      if (!this.searchValue) {
        this.currentColumns = this.columns
        return
      }
      this.currentColumns = this.columns.filter(item => {
        const searchText = this.getValueByKey(item, this.fieldProps.label)
        return searchText.toLowerCase().includes(this.searchValue.toLowerCase())
      })
    },
    handleClear() {
      this.currentColumns = this.columns
      this.searchValue = ''
    },
    handleItemClick(item, index) {
      if (this.mode === 'single') {
        this.selectedIndex = index
        this.handleConfirm()
      } else {
        this.toggleSelection(item)
      }
    },
    toggleSelection(item) {
      const value = this.getValueByKey(item, this.fieldProps.value)
      const index = this.selectedValues.findIndex(
        selected => this.getValueByKey(selected, this.fieldProps.value) === value
      )
      if (index === -1) {
        this.selectedValues.push(item)
      } else {
        this.selectedValues.splice(index, 1)
      }
    },
    isItemSelected(item) {
      const value = this.getValueByKey(item, this.fieldProps.value)
      return this.selectedValues.some(
        selected => this.getValueByKey(selected, this.fieldProps.value) === value
      )
    },
    // 单选返回对象 多选返回数组
    handleConfirm() {
      if (this.mode === 'single') {
        let indexes = this.$refs.picker.getIndexes();
        const selectedItem = this.currentColumns[indexes[0]]
        this.innerValue = selectedItem

        const result = {
          value: this.getValueByKey(selectedItem, this.fieldProps.value),
          item: selectedItem
        }

        this.$emit('input', result.value)
        this.$emit('confirm', result)
      } else {
        const result = {
          value: this.selectedValues.map(item => this.getValueByKey(item, this.fieldProps.value)),
          item: this.selectedValues
        }

        this.$emit('input', result.values)
        this.$emit('confirm', result)
      }
    },
    handleCancel() {
      this.$emit('cancel')
    },
    onConfirm(value) {
      this.$emit('confirm', {
        value: this.getValueByKey(value, this.fieldProps.value),
        item: value
      })
    },
    onCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.default-box {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  width: 100%;
  box-sizing: border-box;
}

.default-action {
  min-width: 44px;
  padding: 0 8px;
  cursor: pointer;
  white-space: nowrap;
  flex-shrink: 0;
  text-align: center;
}

.default-cancel {
  color: #969799;
}

.default-confirm {
  color: #3562db;
}

.search-wrapper {
  flex: 1;
  min-width: 0;
  margin: 0 8px;
}

:deep(.van-search) {
  padding: 0;
}

:deep(.van-search__content) {
  padding: 0 8px;
}

.picker-column {
  width: 100%;
}

.picker-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
}

/* 单选模式样式 - 居中显示 */
.picker-item:not(.picker-item--multiple) {
  justify-content: center;
}

.picker-item:not(.picker-item--multiple) .item-text {
  margin: 0;
  text-align: center;
}

/* 多选模式样式 - 两端对齐 */
.picker-item--multiple {
  justify-content: space-between;
  padding-right: 12px;
}

.picker-item--multiple .item-text {
  margin: 0;
}

:deep(.van-checkbox) {
  flex-shrink: 0;
  padding: 0;
}

/* 点击效果 */
.picker-item:active {
  background-color: #f2f3f5;
}

:deep(.van-checkbox__icon--checked .van-icon) {
  background-color: #3562db;
  border-color: #3562db;
}
</style>
