export default {
  data() {
    return {
      fontSizeScales: {
        'small': 0.8,
        'normal': 1,
        'large': 1.3,
        'x-large': 1.5
      }
    };
  },
  computed: {
    fontSizePreference() {
      return localStorage.getItem('fontSizePreference') || 'normal';
    },
    currentFontScale() {
      return this.fontSizeScales[this.fontSizePreference] || 1;
    }
  },
  mounted() {
    this.applyFontScale();
    window.addEventListener('storage', this.handleStorageChange);
  },
  beforeDestroy() {
    window.removeEventListener('storage', this.handleStorageChange);
  },
  methods: {
    applyFontScale() {
      if (this.$el) {
        this.$el.style.setProperty('--font-scale', this.currentFontScale);
      }
    },
    handleStorageChange(event) {
      if (event.key === 'fontSizePreference') {
        this.$nextTick(() => {
          this.applyFontScale();
        });
      }
    }
  }
};
