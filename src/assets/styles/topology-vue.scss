@-ms-viewport {
  width: device-width;
}

.scada-preview {

  body,
  html {
    width: 100%;
  }

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  input::-ms-clear,
  input::-ms-reveal {
    display: none;
  }

  article,
  aside,
  dialog,
  figcaption,
  figure,
  footer,
  header,
  hgroup,
  main,
  nav,
  section {
    display: block;
  }

  body {
    margin: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    font-variant: tabular-nums;
    line-height: 1.5;
    background-color: #fff;
    font-feature-settings: "tnum";
  }

  [tabindex="-1"]:focus {
    outline: none !important;
  }

  hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-top: 0;
    margin-bottom: 0.5em;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }

  p {
    margin-top: 0;
    margin-bottom: 1em;
  }

  abbr[data-original-title],
  abbr[title] {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
    border-bottom: 0;
    cursor: help;
  }

  address {
    margin-bottom: 1em;
    font-style: normal;
    line-height: inherit;
  }

  input[type=number],
  input[type=password],
  input[type=text],
  textarea {
    -webkit-appearance: none;
  }

  dl,
  ol,
  ul {
    margin-top: 0;
    margin-bottom: 1em;
  }

  ol ol,
  ol ul,
  ul ol,
  ul ul {
    margin-bottom: 0;
  }

  dt {
    font-weight: 500;
  }

  dd {
    margin-bottom: 0.5em;
    margin-left: 0;
  }

  blockquote {
    margin: 0 0 1em;
  }

  dfn {
    font-style: italic;
  }

  b,
  strong {
    font-weight: bolder;
  }

  small {
    font-size: 80%;
  }

  sub,
  sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline;
  }

  sub {
    bottom: -0.25em;
  }

  sup {
    top: -0.5em;
  }

  a {
    background-color: transparent;
    outline: none;
    cursor: pointer;
    transition: color 0.3s;
    -webkit-text-decoration-skip: objects;
  }

  a:hover {
    color: #40a9ff;
  }

  a:active {
    color: #096dd9;
  }

  a:active,
  a:hover {
    text-decoration: none;
    outline: 0;
  }

  a[disabled] {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
    pointer-events: none;
  }

  code,
  kbd,
  pre,
  samp {
    font-size: 1em;
    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, Courier, monospace;
  }

  pre {
    margin-top: 0;
    margin-bottom: 1em;
    overflow: auto;
  }

  figure {
    margin: 0 0 1em;
  }

  img {
    vertical-align: middle;
    border-style: none;
  }

  svg:not(:root) {
    overflow: hidden;
  }

  [role=button],
  a,
  area,
  button,
  input:not([type=range]),
  label,
  select,
  summary,
  textarea {
    touch-action: manipulation;
  }

  table {
    border-collapse: collapse;
  }

  caption {
    padding-top: 0.75em;
    padding-bottom: 0.3em;
    color: rgba(0, 0, 0, 0.45);
    text-align: left;
    caption-side: bottom;
  }

  th {
    text-align: inherit;
  }

  button,
  input,
  optgroup,
  select,
  textarea {
    margin: 0;
    // color: inherit;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
  }

  button,
  input {
    overflow: visible;
  }

  button,
  select {
    text-transform: none;
  }

  [type=reset],
  [type=submit],
  button,
  html [type=button] {
    -webkit-appearance: button;
  }

  [type=button]::-moz-focus-inner,
  [type=reset]::-moz-focus-inner,
  [type=submit]::-moz-focus-inner,
  button::-moz-focus-inner {
    padding: 0;
    border-style: none;
  }

  input[type=checkbox],
  input[type=radio] {
    box-sizing: border-box;
    padding: 0;
  }

  input[type=date],
  input[type=datetime-local],
  input[type=month],
  input[type=time] {
    -webkit-appearance: listbox;
  }

  textarea {
    overflow: auto;
    resize: vertical;
  }

  fieldset {
    min-width: 0;
    margin: 0;
    padding: 0;
    border: 0;
  }

  legend {
    display: block;
    width: 100%;
    max-width: 100%;
    margin-bottom: 0.5em;
    padding: 0;
    color: inherit;
    font-size: 1.5em;
    line-height: inherit;
    white-space: normal;
  }

  progress {
    vertical-align: baseline;
  }

  [type=number]::-webkit-inner-spin-button,
  [type=number]::-webkit-outer-spin-button {
    height: auto;
  }

  [type=search] {
    outline-offset: -2px;
    -webkit-appearance: none;
  }

  [type=search]::-webkit-search-cancel-button,
  [type=search]::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button;
  }

  output {
    display: inline-block;
  }

  summary {
    display: list-item;
  }

  template {
    display: none;
  }

  [hidden] {
    display: none !important;
  }

  mark {
    padding: 0.2em;
    background-color: #feffe6;
  }

  ::-moz-selection {
    color: #fff;
    background: #1890ff;
  }

  ::selection {
    color: #fff;
    background: #1890ff;
  }

  html {
    --antd-wave-shadow-color: #1890ff;
  }

  * {
    outline: none;
  }

  body,
  html {
    height: 100%;
    margin: 0;
    padding: 0;
    font-size: 13px;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  }

  i {
    font-style: inherit;
  }

  a {
    color: #1890ff;
    text-decoration: none;
    transition: none;
  }

  a:hover {
    color: #096dd9;
  }

  a.hover:hover {
    text-decoration: underline !important;
  }

  img {
    max-width: 100%;
    max-height: 100%;
  }



  ::-webkit-scrollbar {
    width: 6px;
    height: 10px;
    background: transparent;
  }

  ::-webkit-scrollbar-corner {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #e7e7e7;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent !important;
  }

  input::-webkit-inner-spin-button,
  input::-webkit-outer-spin-button {
    -webkit-appearance: none;
  }

  input[type=number] {
    -moz-appearance: textfield;
  }



  .hover:hover {
    color: #1890ff;
    cursor: pointer;
  }

  .page-section {
    width: 100vw;
  }

  .page-section>* {
    max-width: 1200px;
    margin: 0 auto;
  }

  .button,
  a.button {
    display: inline-block !important;
    text-align: center;
    outline: none;
    border: 1px solid #dad7d7;
    border-radius: 2px;
    color: #595959;
    background-color: transparent;
    padding: 0 15px;
    line-height: 28px;
    cursor: pointer;
  }

  .button:hover,
  a.button:hover {
    border-color: #8c8c8c;
    color: #434343;
  }

  .button.primary,
  a.button.primary {
    color: #fff !important;
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .button.primary:hover,
  a.button.primary:hover {
    background-color: #096dd9;
    border-color: #096dd9;
  }

  .rel {
    position: relative;
  }

  .abs {
    position: absolute;
  }

  .nowrap {
    white-space: nowrap;
  }

  .m4 {
    margin: 4px !important;
  }

  .ml4 {
    margin-left: 4px !important;
  }

  .mt4 {
    margin-top: 4px !important;
  }

  .mr4 {
    margin-right: 4px !important;
  }

  .mb4 {
    margin-bottom: 4px !important;
  }

  .mh4 {
    margin-left: 4px !important;
    margin-right: 4px !important;
  }

  .ml8 {
    margin-left: 8px !important;
  }

  .mr8 {
    margin-right: 8px !important;
  }

  .mt8 {
    margin-top: 8px !important;
  }

  .mb8 {
    margin-bottom: 10px;
  }

  .mh8 {
    margin-left: 8px !important;
    margin-right: 8px !important;
  }

  .mr12 {
    margin-right: 12px !important;
  }

  .mb12 {
    margin-bottom: 12px !important;
  }

  .mt12 {
    margin-top: 12px !important;
  }

  .ml16 {
    margin-left: 16px !important;
  }

  .mr16 {
    margin-right: 16px !important;
  }

  .mt16 {
    margin-top: 16px !important;
  }

  .mt64 {
    margin-top: 64px !important;
  }

  .mb16 {
    margin-bottom: 16px !important;
  }

  .ml20 {
    margin-left: 20px !important;
  }

  .mt24 {
    margin-top: 24px !important;
  }

  .ph8 {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  .pt8 {
    padding-top: 8px !important;
  }

  .pb8 {
    padding-bottom: 8px !important;
  }

  .p8 {
    padding: 8px !important;
  }

  .p12 {
    padding: 12px !important;
  }

  .pb12 {
    padding-bottom: 12px !important;
  }

  .pb16 {
    padding-bottom: 16px !important;
  }

  .pl16 {
    padding-left: 16px !important;
  }

  .ph16,
  .pr16 {
    padding-right: 16px !important;
  }

  .ph16 {
    padding-left: 16px !important;
  }

  .pt10 {
    padding-top: 10px !important;
  }

  .full {
    width: 100% !important;
  }

  .flex {
    display: flex;
  }

  .flex>* {
    flex-shrink: 0;
  }

  .flex>.full {
    width: auto !important;
    flex-grow: 1;
  }

  .flex.wrap {
    flex-wrap: wrap;
  }

  .flex.center {
    justify-content: center;
  }

  .flex.middle {
    align-items: center;
  }

  .gray {
    color: #bfbfbf !important;
  }

  .text-left {
    text-align: left;
  }

  .border-right {
    border-right: 1px solid #f3f3f3;
  }

  .block {
    display: block !important;
  }

  .hidden {
    display: none !important;
  }

  .inline {
    display: inline-block !important;
  }

  .primary {
    color: #1890ff;
  }

  .primary:hover {
    text-decoration: underline;
  }

  .warning {
    color: #faad14;
  }

  .center {
    text-align: center;
    margin: auto;
  }

  .pointer {
    cursor: pointer;
  }

  .broder-right {
    border-right: 1px solid #e5e5e5;
  }

  .color {
    position: relative;
  }

  .color input:first-child {
    padding-left: 28px !important;
  }

  .color input[type=color] {
    position: absolute;
    top: 2px;
    left: 5px;
    width: 20px !important;
    padding: 0;
    background: transparent;
    border: none;
  }

  .line {
    display: inline-block;
  }

  .line svg {
    position: relative;
    top: 2px;
    height: 18px;
  }

  .line .t-icon {
    font-size: 20px;
    vertical-align: middle;
  }

  .line .ant-select-arrow-icon svg {
    top: -4px;
  }

  .lineArrow,
  .lineArrow .t-icon {
    height: 26px !important;
  }

  .lineArrow .t-icon {
    font-size: 32px !important;
    display: inline-block;
  }

  .border-bottom {
    border-bottom: 1px solid #e5e5e5;
  }

  .clearfix {
    zoom: 1;
  }

  .clearfix::after,
  .clearfix::before {
    display: table;
    content: "";
  }

  .clearfix::after {
    clear: both;
  }

  .anticon {
    display: inline-block;
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-align: center;
    text-transform: none;
    vertical-align: -0.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .anticon>* {
    line-height: 1;
  }

  .anticon svg {
    display: inline-block;
  }

  .anticon::before {
    display: none;
  }

  .anticon .anticon-icon {
    display: block;
  }

  .anticon[tabindex] {
    cursor: pointer;
  }

  .anticon-spin,
  .anticon-spin::before {
    display: inline-block;
    -webkit-animation: loadingcircle 1s linear infinite;
    animation: loadingCircle 1s linear infinite;
  }

  .fade-appear,
  .fade-enter,
  .fade-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .fade-appear.fade-appear-active,
  .fade-enter.fade-enter-active {
    -webkit-animation-name: antfadein;
    animation-name: antFadeIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .fade-leave.fade-leave-active {
    -webkit-animation-name: antfadeout;
    animation-name: antFadeOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .fade-appear,
  .fade-enter {
    opacity: 0;
  }

  .fade-appear,
  .fade-enter,
  .fade-leave {
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
  }

  @-webkit-keyframes antFadeIn {
    0% {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes antFadeIn {
    0% {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @-webkit-keyframes antFadeOut {
    0% {
      opacity: 1;
    }

    to {
      opacity: 0;
    }
  }

  @keyframes antFadeOut {
    0% {
      opacity: 1;
    }

    to {
      opacity: 0;
    }
  }

  .move-up-appear,
  .move-up-enter,
  .move-up-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .move-up-appear.move-up-appear-active,
  .move-up-enter.move-up-enter-active {
    -webkit-animation-name: antmoveupin;
    animation-name: antMoveUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .move-up-leave.move-up-leave-active {
    -webkit-animation-name: antmoveupout;
    animation-name: antMoveUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .move-up-appear,
  .move-up-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .move-up-leave {
    -webkit-animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
  }

  .move-down-appear,
  .move-down-enter,
  .move-down-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .move-down-appear.move-down-appear-active,
  .move-down-enter.move-down-enter-active {
    -webkit-animation-name: antmovedownin;
    animation-name: antMoveDownIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .move-down-leave.move-down-leave-active {
    -webkit-animation-name: antmovedownout;
    animation-name: antMoveDownOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .move-down-appear,
  .move-down-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .move-down-leave {
    -webkit-animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
  }

  .move-left-appear,
  .move-left-enter,
  .move-left-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .move-left-appear.move-left-appear-active,
  .move-left-enter.move-left-enter-active {
    -webkit-animation-name: antmoveleftin;
    animation-name: antMoveLeftIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .move-left-leave.move-left-leave-active {
    -webkit-animation-name: antmoveleftout;
    animation-name: antMoveLeftOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .move-left-appear,
  .move-left-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .move-left-leave {
    -webkit-animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
  }

  .move-right-appear,
  .move-right-enter,
  .move-right-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .move-right-appear.move-right-appear-active,
  .move-right-enter.move-right-enter-active {
    -webkit-animation-name: antmoverightin;
    animation-name: antMoveRightIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .move-right-leave.move-right-leave-active {
    -webkit-animation-name: antmoverightout;
    animation-name: antMoveRightOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .move-right-appear,
  .move-right-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .move-right-leave {
    -webkit-animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
  }

  @-webkit-keyframes antMoveDownIn {
    0% {
      transform: translateY(100%);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: translateY(0);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @keyframes antMoveDownIn {
    0% {
      transform: translateY(100%);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: translateY(0);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @-webkit-keyframes antMoveDownOut {
    0% {
      transform: translateY(0);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: translateY(100%);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @keyframes antMoveDownOut {
    0% {
      transform: translateY(0);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: translateY(100%);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes antMoveLeftIn {
    0% {
      transform: translateX(-100%);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: translateX(0);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @keyframes antMoveLeftIn {
    0% {
      transform: translateX(-100%);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: translateX(0);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @-webkit-keyframes antMoveLeftOut {
    0% {
      transform: translateX(0);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: translateX(-100%);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @keyframes antMoveLeftOut {
    0% {
      transform: translateX(0);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: translateX(-100%);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes antMoveRightIn {
    0% {
      transform: translateX(100%);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: translateX(0);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @keyframes antMoveRightIn {
    0% {
      transform: translateX(100%);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: translateX(0);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @-webkit-keyframes antMoveRightOut {
    0% {
      transform: translateX(0);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: translateX(100%);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @keyframes antMoveRightOut {
    0% {
      transform: translateX(0);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: translateX(100%);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes antMoveUpIn {
    0% {
      transform: translateY(-100%);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: translateY(0);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @keyframes antMoveUpIn {
    0% {
      transform: translateY(-100%);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: translateY(0);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @-webkit-keyframes antMoveUpOut {
    0% {
      transform: translateY(0);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: translateY(-100%);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @keyframes antMoveUpOut {
    0% {
      transform: translateY(0);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: translateY(-100%);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes loadingCircle {
    to {
      transform: rotate(1turn);
    }
  }

  @keyframes loadingCircle {
    to {
      transform: rotate(1turn);
    }
  }

  [ant-click-animating-without-extra-node=true],
  [ant-click-animating=true] {
    position: relative;
  }


  .ant-click-animating-node,
  [ant-click-animating-without-extra-node=true]::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: block;
    border-radius: inherit;
    box-shadow: 0 0 0 0 #1890ff;
    box-shadow: 0 0 0 0 var(--antd-wave-shadow-color);
    opacity: 0.2;
    -webkit-animation: fadeeffect 2s cubic-bezier(0.08, 0.82, 0.17, 1), waveeffect 0.4s cubic-bezier(0.08, 0.82, 0.17, 1);
    animation: fadeEffect 2s cubic-bezier(0.08, 0.82, 0.17, 1), waveEffect 0.4s cubic-bezier(0.08, 0.82, 0.17, 1);
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    content: "";
    pointer-events: none;
  }

  @-webkit-keyframes waveEffect {
    to {
      box-shadow: 0 0 0 #1890ff;
      box-shadow: 0 0 0 6px var(--antd-wave-shadow-color);
    }
  }

  @keyframes waveEffect {
    to {
      box-shadow: 0 0 0 #1890ff;
      box-shadow: 0 0 0 6px var(--antd-wave-shadow-color);
    }
  }

  @-webkit-keyframes fadeEffect {
    to {
      opacity: 0;
    }
  }

  @keyframes fadeEffect {
    to {
      opacity: 0;
    }
  }

  .slide-up-appear,
  .slide-up-enter,
  .slide-up-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .slide-up-appear.slide-up-appear-active,
  .slide-up-enter.slide-up-enter-active {
    -webkit-animation-name: antslideupin;
    animation-name: antSlideUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .slide-up-leave.slide-up-leave-active {
    -webkit-animation-name: antslideupout;
    animation-name: antSlideUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .slide-up-appear,
  .slide-up-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
  }

  .slide-up-leave {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
  }

  .slide-down-appear,
  .slide-down-enter,
  .slide-down-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .slide-down-appear.slide-down-appear-active,
  .slide-down-enter.slide-down-enter-active {
    -webkit-animation-name: antslidedownin;
    animation-name: antSlideDownIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .slide-down-leave.slide-down-leave-active {
    -webkit-animation-name: antslidedownout;
    animation-name: antSlideDownOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .slide-down-appear,
  .slide-down-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
  }

  .slide-down-leave {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
  }

  .slide-left-appear,
  .slide-left-enter,
  .slide-left-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .slide-left-appear.slide-left-appear-active,
  .slide-left-enter.slide-left-enter-active {
    -webkit-animation-name: antslideleftin;
    animation-name: antSlideLeftIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .slide-left-leave.slide-left-leave-active {
    -webkit-animation-name: antslideleftout;
    animation-name: antSlideLeftOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .slide-left-appear,
  .slide-left-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
  }

  .slide-left-leave {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
  }

  .slide-right-appear,
  .slide-right-enter,
  .slide-right-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .slide-right-appear.slide-right-appear-active,
  .slide-right-enter.slide-right-enter-active {
    -webkit-animation-name: antsliderightin;
    animation-name: antSlideRightIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .slide-right-leave.slide-right-leave-active {
    -webkit-animation-name: antsliderightout;
    animation-name: antSlideRightOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .slide-right-appear,
  .slide-right-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
    animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
  }

  .slide-right-leave {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
  }

  @-webkit-keyframes antSlideUpIn {
    0% {
      transform: scaleY(0.8);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: scaleY(1);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @keyframes antSlideUpIn {
    0% {
      transform: scaleY(0.8);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: scaleY(1);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @-webkit-keyframes antSlideUpOut {
    0% {
      transform: scaleY(1);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: scaleY(0.8);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @keyframes antSlideUpOut {
    0% {
      transform: scaleY(1);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: scaleY(0.8);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes antSlideDownIn {
    0% {
      transform: scaleY(0.8);
      transform-origin: 100% 100%;
      opacity: 0;
    }

    to {
      transform: scaleY(1);
      transform-origin: 100% 100%;
      opacity: 1;
    }
  }

  @keyframes antSlideDownIn {
    0% {
      transform: scaleY(0.8);
      transform-origin: 100% 100%;
      opacity: 0;
    }

    to {
      transform: scaleY(1);
      transform-origin: 100% 100%;
      opacity: 1;
    }
  }

  @-webkit-keyframes antSlideDownOut {
    0% {
      transform: scaleY(1);
      transform-origin: 100% 100%;
      opacity: 1;
    }

    to {
      transform: scaleY(0.8);
      transform-origin: 100% 100%;
      opacity: 0;
    }
  }

  @keyframes antSlideDownOut {
    0% {
      transform: scaleY(1);
      transform-origin: 100% 100%;
      opacity: 1;
    }

    to {
      transform: scaleY(0.8);
      transform-origin: 100% 100%;
      opacity: 0;
    }
  }

  @-webkit-keyframes antSlideLeftIn {
    0% {
      transform: scaleX(0.8);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: scaleX(1);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @keyframes antSlideLeftIn {
    0% {
      transform: scaleX(0.8);
      transform-origin: 0 0;
      opacity: 0;
    }

    to {
      transform: scaleX(1);
      transform-origin: 0 0;
      opacity: 1;
    }
  }

  @-webkit-keyframes antSlideLeftOut {
    0% {
      transform: scaleX(1);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: scaleX(0.8);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @keyframes antSlideLeftOut {
    0% {
      transform: scaleX(1);
      transform-origin: 0 0;
      opacity: 1;
    }

    to {
      transform: scaleX(0.8);
      transform-origin: 0 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes antSlideRightIn {
    0% {
      transform: scaleX(0.8);
      transform-origin: 100% 0;
      opacity: 0;
    }

    to {
      transform: scaleX(1);
      transform-origin: 100% 0;
      opacity: 1;
    }
  }

  @keyframes antSlideRightIn {
    0% {
      transform: scaleX(0.8);
      transform-origin: 100% 0;
      opacity: 0;
    }

    to {
      transform: scaleX(1);
      transform-origin: 100% 0;
      opacity: 1;
    }
  }

  @-webkit-keyframes antSlideRightOut {
    0% {
      transform: scaleX(1);
      transform-origin: 100% 0;
      opacity: 1;
    }

    to {
      transform: scaleX(0.8);
      transform-origin: 100% 0;
      opacity: 0;
    }
  }

  @keyframes antSlideRightOut {
    0% {
      transform: scaleX(1);
      transform-origin: 100% 0;
      opacity: 1;
    }

    to {
      transform: scaleX(0.8);
      transform-origin: 100% 0;
      opacity: 0;
    }
  }

  .swing-appear,
  .swing-enter {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .swing-appear.swing-appear-active,
  .swing-enter.swing-enter-active {
    -webkit-animation-name: antswingin;
    animation-name: antSwingIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  @-webkit-keyframes antSwingIn {

    0%,
    to {
      transform: translateX(0);
    }

    20% {
      transform: translateX(-10px);
    }

    40% {
      transform: translateX(10px);
    }

    60% {
      transform: translateX(-5px);
    }

    80% {
      transform: translateX(5px);
    }
  }

  @keyframes antSwingIn {

    0%,
    to {
      transform: translateX(0);
    }

    20% {
      transform: translateX(-10px);
    }

    40% {
      transform: translateX(10px);
    }

    60% {
      transform: translateX(-5px);
    }

    80% {
      transform: translateX(5px);
    }
  }

  .zoom-appear,
  .zoom-enter,
  .zoom-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .zoom-appear.zoom-appear-active,
  .zoom-enter.zoom-enter-active {
    -webkit-animation-name: antzoomin;
    animation-name: antZoomIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .zoom-leave.zoom-leave-active {
    -webkit-animation-name: antzoomout;
    animation-name: antZoomOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .zoom-appear,
  .zoom-enter {
    transform: scale(0);
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .zoom-leave {
    -webkit-animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .zoom-big-appear,
  .zoom-big-enter,
  .zoom-big-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .zoom-big-appear.zoom-big-appear-active,
  .zoom-big-enter.zoom-big-enter-active {
    -webkit-animation-name: antzoombigin;
    animation-name: antZoomBigIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .zoom-big-leave.zoom-big-leave-active {
    -webkit-animation-name: antzoombigout;
    animation-name: antZoomBigOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .zoom-big-appear,
  .zoom-big-enter {
    transform: scale(0);
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .zoom-big-leave {
    -webkit-animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .zoom-big-fast-appear,
  .zoom-big-fast-enter,
  .zoom-big-fast-leave {
    -webkit-animation-duration: 0.1s;
    animation-duration: 0.1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .zoom-big-fast-appear.zoom-big-fast-appear-active,
  .zoom-big-fast-enter.zoom-big-fast-enter-active {
    -webkit-animation-name: antzoombigin;
    animation-name: antZoomBigIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .zoom-big-fast-leave.zoom-big-fast-leave-active {
    -webkit-animation-name: antzoombigout;
    animation-name: antZoomBigOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .zoom-big-fast-appear,
  .zoom-big-fast-enter {
    transform: scale(0);
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .zoom-big-fast-leave {
    -webkit-animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .zoom-up-appear,
  .zoom-up-enter,
  .zoom-up-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .zoom-up-appear.zoom-up-appear-active,
  .zoom-up-enter.zoom-up-enter-active {
    -webkit-animation-name: antzoomupin;
    animation-name: antZoomUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .zoom-up-leave.zoom-up-leave-active {
    -webkit-animation-name: antzoomupout;
    animation-name: antZoomUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .zoom-up-appear,
  .zoom-up-enter {
    transform: scale(0);
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .zoom-up-leave {
    -webkit-animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .zoom-down-appear,
  .zoom-down-enter,
  .zoom-down-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .zoom-down-appear.zoom-down-appear-active,
  .zoom-down-enter.zoom-down-enter-active {
    -webkit-animation-name: antzoomdownin;
    animation-name: antZoomDownIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .zoom-down-leave.zoom-down-leave-active {
    -webkit-animation-name: antzoomdownout;
    animation-name: antZoomDownOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .zoom-down-appear,
  .zoom-down-enter {
    transform: scale(0);
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .zoom-down-leave {
    -webkit-animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .zoom-left-appear,
  .zoom-left-enter,
  .zoom-left-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .zoom-left-appear.zoom-left-appear-active,
  .zoom-left-enter.zoom-left-enter-active {
    -webkit-animation-name: antzoomleftin;
    animation-name: antZoomLeftIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .zoom-left-leave.zoom-left-leave-active {
    -webkit-animation-name: antzoomleftout;
    animation-name: antZoomLeftOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .zoom-left-appear,
  .zoom-left-enter {
    transform: scale(0);
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .zoom-left-leave {
    -webkit-animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .zoom-right-appear,
  .zoom-right-enter,
  .zoom-right-leave {
    -webkit-animation-duration: 0.2s;
    animation-duration: 0.2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  }

  .zoom-right-appear.zoom-right-appear-active,
  .zoom-right-enter.zoom-right-enter-active {
    -webkit-animation-name: antzoomrightin;
    animation-name: antZoomRightIn;
    -webkit-animation-play-state: running;
    animation-play-state: running;
  }

  .zoom-right-leave.zoom-right-leave-active {
    -webkit-animation-name: antzoomrightout;
    animation-name: antZoomRightOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none;
  }

  .zoom-right-appear,
  .zoom-right-enter {
    transform: scale(0);
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
  }

  .zoom-right-leave {
    -webkit-animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  @-webkit-keyframes antZoomIn {
    0% {
      transform: scale(0.2);
      opacity: 0;
    }

    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes antZoomIn {
    0% {
      transform: scale(0.2);
      opacity: 0;
    }

    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @-webkit-keyframes antZoomOut {
    0% {
      transform: scale(1);
    }

    to {
      transform: scale(0.2);
      opacity: 0;
    }
  }

  @keyframes antZoomOut {
    0% {
      transform: scale(1);
    }

    to {
      transform: scale(0.2);
      opacity: 0;
    }
  }

  @-webkit-keyframes antZoomBigIn {
    0% {
      transform: scale(0.8);
      opacity: 0;
    }

    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes antZoomBigIn {
    0% {
      transform: scale(0.8);
      opacity: 0;
    }

    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @-webkit-keyframes antZoomBigOut {
    0% {
      transform: scale(1);
    }

    to {
      transform: scale(0.8);
      opacity: 0;
    }
  }

  @keyframes antZoomBigOut {
    0% {
      transform: scale(1);
    }

    to {
      transform: scale(0.8);
      opacity: 0;
    }
  }

  @-webkit-keyframes antZoomUpIn {
    0% {
      transform: scale(0.8);
      transform-origin: 50% 0;
      opacity: 0;
    }

    to {
      transform: scale(1);
      transform-origin: 50% 0;
    }
  }

  @keyframes antZoomUpIn {
    0% {
      transform: scale(0.8);
      transform-origin: 50% 0;
      opacity: 0;
    }

    to {
      transform: scale(1);
      transform-origin: 50% 0;
    }
  }

  @-webkit-keyframes antZoomUpOut {
    0% {
      transform: scale(1);
      transform-origin: 50% 0;
    }

    to {
      transform: scale(0.8);
      transform-origin: 50% 0;
      opacity: 0;
    }
  }

  @keyframes antZoomUpOut {
    0% {
      transform: scale(1);
      transform-origin: 50% 0;
    }

    to {
      transform: scale(0.8);
      transform-origin: 50% 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes antZoomLeftIn {
    0% {
      transform: scale(0.8);
      transform-origin: 0 50%;
      opacity: 0;
    }

    to {
      transform: scale(1);
      transform-origin: 0 50%;
    }
  }

  @keyframes antZoomLeftIn {
    0% {
      transform: scale(0.8);
      transform-origin: 0 50%;
      opacity: 0;
    }

    to {
      transform: scale(1);
      transform-origin: 0 50%;
    }
  }

  @-webkit-keyframes antZoomLeftOut {
    0% {
      transform: scale(1);
      transform-origin: 0 50%;
    }

    to {
      transform: scale(0.8);
      transform-origin: 0 50%;
      opacity: 0;
    }
  }

  @keyframes antZoomLeftOut {
    0% {
      transform: scale(1);
      transform-origin: 0 50%;
    }

    to {
      transform: scale(0.8);
      transform-origin: 0 50%;
      opacity: 0;
    }
  }

  @-webkit-keyframes antZoomRightIn {
    0% {
      transform: scale(0.8);
      transform-origin: 100% 50%;
      opacity: 0;
    }

    to {
      transform: scale(1);
      transform-origin: 100% 50%;
    }
  }

  @keyframes antZoomRightIn {
    0% {
      transform: scale(0.8);
      transform-origin: 100% 50%;
      opacity: 0;
    }

    to {
      transform: scale(1);
      transform-origin: 100% 50%;
    }
  }

  @-webkit-keyframes antZoomRightOut {
    0% {
      transform: scale(1);
      transform-origin: 100% 50%;
    }

    to {
      transform: scale(0.8);
      transform-origin: 100% 50%;
      opacity: 0;
    }
  }

  @keyframes antZoomRightOut {
    0% {
      transform: scale(1);
      transform-origin: 100% 50%;
    }

    to {
      transform: scale(0.8);
      transform-origin: 100% 50%;
      opacity: 0;
    }
  }

  @-webkit-keyframes antZoomDownIn {
    0% {
      transform: scale(0.8);
      transform-origin: 50% 100%;
      opacity: 0;
    }

    to {
      transform: scale(1);
      transform-origin: 50% 100%;
    }
  }

  @keyframes antZoomDownIn {
    0% {
      transform: scale(0.8);
      transform-origin: 50% 100%;
      opacity: 0;
    }

    to {
      transform: scale(1);
      transform-origin: 50% 100%;
    }
  }

  @-webkit-keyframes antZoomDownOut {
    0% {
      transform: scale(1);
      transform-origin: 50% 100%;
    }

    to {
      transform: scale(0.8);
      transform-origin: 50% 100%;
      opacity: 0;
    }
  }

  @keyframes antZoomDownOut {
    0% {
      transform: scale(1);
      transform-origin: 50% 100%;
    }

    to {
      transform: scale(0.8);
      transform-origin: 50% 100%;
      opacity: 0;
    }
  }

  .ant-motion-collapse-legacy {
    overflow: hidden;
  }

  .ant-motion-collapse,
  .ant-motion-collapse-legacy-active {
    transition: height 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), opacity 0.15s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
  }

  .ant-motion-collapse {
    overflow: hidden;
  }

  .ant-btn {
    line-height: 1.499;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    border: 1px solid transparent;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
    height: 32px;
    padding: 0 15px;
    font-size: 14px;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
    border-color: #d9d9d9;
  }

  .ant-btn>.anticon {
    line-height: 1;
  }

  .ant-btn,
  .ant-btn:active,
  .ant-btn:focus {
    outline: 0;
  }

  .ant-btn:not([disabled]):hover {
    text-decoration: none;
  }

  .ant-btn:not([disabled]):active {
    outline: 0;
    box-shadow: none;
  }

  .ant-btn.disabled,
  .ant-btn[disabled] {
    cursor: not-allowed;
  }

  .ant-btn.disabled>*,
  .ant-btn[disabled]>* {
    pointer-events: none;
  }

  .ant-btn-lg {
    height: 40px;
    padding: 0 15px;
    font-size: 16px;
    border-radius: 4px;
  }

  .ant-btn-sm {
    height: 24px;
    padding: 0 7px;
    font-size: 14px;
    border-radius: 4px;
  }

  .ant-btn>a:only-child {
    color: currentColor;
  }

  .ant-btn>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn:focus,
  .ant-btn:hover {
    color: #40a9ff;
    background-color: #fff;
    border-color: #40a9ff;
  }

  .ant-btn:focus>a:only-child,
  .ant-btn:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn:focus>a:only-child::after,
  .ant-btn:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn.active,
  .ant-btn:active {
    color: #096dd9;
    background-color: #fff;
    border-color: #096dd9;
  }

  .ant-btn.active>a:only-child,
  .ant-btn:active>a:only-child {
    color: currentColor;
  }

  .ant-btn.active>a:only-child::after,
  .ant-btn:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-disabled,
  .ant-btn-disabled.active,
  .ant-btn-disabled:active,
  .ant-btn-disabled:focus,
  .ant-btn-disabled:hover,
  .ant-btn.disabled,
  .ant-btn.disabled.active,
  .ant-btn.disabled:active,
  .ant-btn.disabled:focus,
  .ant-btn.disabled:hover,
  .ant-btn[disabled],
  .ant-btn[disabled].active,
  .ant-btn[disabled]:active,
  .ant-btn[disabled]:focus,
  .ant-btn[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-disabled.active>a:only-child,
  .ant-btn-disabled:active>a:only-child,
  .ant-btn-disabled:focus>a:only-child,
  .ant-btn-disabled:hover>a:only-child,
  .ant-btn-disabled>a:only-child,
  .ant-btn.disabled.active>a:only-child,
  .ant-btn.disabled:active>a:only-child,
  .ant-btn.disabled:focus>a:only-child,
  .ant-btn.disabled:hover>a:only-child,
  .ant-btn.disabled>a:only-child,
  .ant-btn[disabled].active>a:only-child,
  .ant-btn[disabled]:active>a:only-child,
  .ant-btn[disabled]:focus>a:only-child,
  .ant-btn[disabled]:hover>a:only-child,
  .ant-btn[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-disabled.active>a:only-child::after,
  .ant-btn-disabled:active>a:only-child::after,
  .ant-btn-disabled:focus>a:only-child::after,
  .ant-btn-disabled:hover>a:only-child::after,
  .ant-btn-disabled>a:only-child::after,
  .ant-btn.disabled.active>a:only-child::after,
  .ant-btn.disabled:active>a:only-child::after,
  .ant-btn.disabled:focus>a:only-child::after,
  .ant-btn.disabled:hover>a:only-child::after,
  .ant-btn.disabled>a:only-child::after,
  .ant-btn[disabled].active>a:only-child::after,
  .ant-btn[disabled]:active>a:only-child::after,
  .ant-btn[disabled]:focus>a:only-child::after,
  .ant-btn[disabled]:hover>a:only-child::after,
  .ant-btn[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn.active,
  .ant-btn:active,
  .ant-btn:focus,
  .ant-btn:hover {
    text-decoration: none;
    background: #fff;
  }

  .ant-btn>i,
  .ant-btn>span {
    display: inline-block;
    transition: margin-left 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    pointer-events: none;
  }

  .ant-btn-primary {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }

  .ant-btn-primary>a:only-child {
    color: currentColor;
  }

  .ant-btn-primary>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-primary:focus,
  .ant-btn-primary:hover {
    color: #fff;
    background-color: #40a9ff;
    border-color: #40a9ff;
  }

  .ant-btn-primary:focus>a:only-child,
  .ant-btn-primary:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn-primary:focus>a:only-child::after,
  .ant-btn-primary:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-primary.active,
  .ant-btn-primary:active {
    color: #fff;
    background-color: #096dd9;
    border-color: #096dd9;
  }

  .ant-btn-primary.active>a:only-child,
  .ant-btn-primary:active>a:only-child {
    color: currentColor;
  }

  .ant-btn-primary.active>a:only-child::after,
  .ant-btn-primary:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-primary-disabled,
  .ant-btn-primary-disabled.active,
  .ant-btn-primary-disabled:active,
  .ant-btn-primary-disabled:focus,
  .ant-btn-primary-disabled:hover,
  .ant-btn-primary.disabled,
  .ant-btn-primary.disabled.active,
  .ant-btn-primary.disabled:active,
  .ant-btn-primary.disabled:focus,
  .ant-btn-primary.disabled:hover,
  .ant-btn-primary[disabled],
  .ant-btn-primary[disabled].active,
  .ant-btn-primary[disabled]:active,
  .ant-btn-primary[disabled]:focus,
  .ant-btn-primary[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-primary-disabled.active>a:only-child,
  .ant-btn-primary-disabled:active>a:only-child,
  .ant-btn-primary-disabled:focus>a:only-child,
  .ant-btn-primary-disabled:hover>a:only-child,
  .ant-btn-primary-disabled>a:only-child,
  .ant-btn-primary.disabled.active>a:only-child,
  .ant-btn-primary.disabled:active>a:only-child,
  .ant-btn-primary.disabled:focus>a:only-child,
  .ant-btn-primary.disabled:hover>a:only-child,
  .ant-btn-primary.disabled>a:only-child,
  .ant-btn-primary[disabled].active>a:only-child,
  .ant-btn-primary[disabled]:active>a:only-child,
  .ant-btn-primary[disabled]:focus>a:only-child,
  .ant-btn-primary[disabled]:hover>a:only-child,
  .ant-btn-primary[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-primary-disabled.active>a:only-child::after,
  .ant-btn-primary-disabled:active>a:only-child::after,
  .ant-btn-primary-disabled:focus>a:only-child::after,
  .ant-btn-primary-disabled:hover>a:only-child::after,
  .ant-btn-primary-disabled>a:only-child::after,
  .ant-btn-primary.disabled.active>a:only-child::after,
  .ant-btn-primary.disabled:active>a:only-child::after,
  .ant-btn-primary.disabled:focus>a:only-child::after,
  .ant-btn-primary.disabled:hover>a:only-child::after,
  .ant-btn-primary.disabled>a:only-child::after,
  .ant-btn-primary[disabled].active>a:only-child::after,
  .ant-btn-primary[disabled]:active>a:only-child::after,
  .ant-btn-primary[disabled]:focus>a:only-child::after,
  .ant-btn-primary[disabled]:hover>a:only-child::after,
  .ant-btn-primary[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child) {
    border-right-color: #40a9ff;
    border-left-color: #40a9ff;
  }

  .ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child):disabled {
    border-color: #d9d9d9;
  }

  .ant-btn-group .ant-btn-primary:first-child:not(:last-child) {
    border-right-color: #40a9ff;
  }

  .ant-btn-group .ant-btn-primary:first-child:not(:last-child)[disabled] {
    border-right-color: #d9d9d9;
  }

  .ant-btn-group .ant-btn-primary+.ant-btn-primary,
  .ant-btn-group .ant-btn-primary:last-child:not(:first-child) {
    border-left-color: #40a9ff;
  }

  .ant-btn-group .ant-btn-primary+.ant-btn-primary[disabled],
  .ant-btn-group .ant-btn-primary:last-child:not(:first-child)[disabled] {
    border-left-color: #d9d9d9;
  }

  .ant-btn-ghost {
    color: rgba(0, 0, 0, 0.65);
    background-color: transparent;
    border-color: #d9d9d9;
  }

  .ant-btn-ghost>a:only-child {
    color: currentColor;
  }

  .ant-btn-ghost>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-ghost:focus,
  .ant-btn-ghost:hover {
    color: #40a9ff;
    background-color: transparent;
    border-color: #40a9ff;
  }

  .ant-btn-ghost:focus>a:only-child,
  .ant-btn-ghost:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn-ghost:focus>a:only-child::after,
  .ant-btn-ghost:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-ghost.active,
  .ant-btn-ghost:active {
    color: #096dd9;
    background-color: transparent;
    border-color: #096dd9;
  }

  .ant-btn-ghost.active>a:only-child,
  .ant-btn-ghost:active>a:only-child {
    color: currentColor;
  }

  .ant-btn-ghost.active>a:only-child::after,
  .ant-btn-ghost:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-ghost-disabled,
  .ant-btn-ghost-disabled.active,
  .ant-btn-ghost-disabled:active,
  .ant-btn-ghost-disabled:focus,
  .ant-btn-ghost-disabled:hover,
  .ant-btn-ghost.disabled,
  .ant-btn-ghost.disabled.active,
  .ant-btn-ghost.disabled:active,
  .ant-btn-ghost.disabled:focus,
  .ant-btn-ghost.disabled:hover,
  .ant-btn-ghost[disabled],
  .ant-btn-ghost[disabled].active,
  .ant-btn-ghost[disabled]:active,
  .ant-btn-ghost[disabled]:focus,
  .ant-btn-ghost[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-ghost-disabled.active>a:only-child,
  .ant-btn-ghost-disabled:active>a:only-child,
  .ant-btn-ghost-disabled:focus>a:only-child,
  .ant-btn-ghost-disabled:hover>a:only-child,
  .ant-btn-ghost-disabled>a:only-child,
  .ant-btn-ghost.disabled.active>a:only-child,
  .ant-btn-ghost.disabled:active>a:only-child,
  .ant-btn-ghost.disabled:focus>a:only-child,
  .ant-btn-ghost.disabled:hover>a:only-child,
  .ant-btn-ghost.disabled>a:only-child,
  .ant-btn-ghost[disabled].active>a:only-child,
  .ant-btn-ghost[disabled]:active>a:only-child,
  .ant-btn-ghost[disabled]:focus>a:only-child,
  .ant-btn-ghost[disabled]:hover>a:only-child,
  .ant-btn-ghost[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-ghost-disabled.active>a:only-child::after,
  .ant-btn-ghost-disabled:active>a:only-child::after,
  .ant-btn-ghost-disabled:focus>a:only-child::after,
  .ant-btn-ghost-disabled:hover>a:only-child::after,
  .ant-btn-ghost-disabled>a:only-child::after,
  .ant-btn-ghost.disabled.active>a:only-child::after,
  .ant-btn-ghost.disabled:active>a:only-child::after,
  .ant-btn-ghost.disabled:focus>a:only-child::after,
  .ant-btn-ghost.disabled:hover>a:only-child::after,
  .ant-btn-ghost.disabled>a:only-child::after,
  .ant-btn-ghost[disabled].active>a:only-child::after,
  .ant-btn-ghost[disabled]:active>a:only-child::after,
  .ant-btn-ghost[disabled]:focus>a:only-child::after,
  .ant-btn-ghost[disabled]:hover>a:only-child::after,
  .ant-btn-ghost[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-dashed {
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
    border-color: #d9d9d9;
    border-style: dashed;
  }

  .ant-btn-dashed>a:only-child {
    color: currentColor;
  }

  .ant-btn-dashed>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-dashed:focus,
  .ant-btn-dashed:hover {
    color: #40a9ff;
    background-color: #fff;
    border-color: #40a9ff;
  }

  .ant-btn-dashed:focus>a:only-child,
  .ant-btn-dashed:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn-dashed:focus>a:only-child::after,
  .ant-btn-dashed:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-dashed.active,
  .ant-btn-dashed:active {
    color: #096dd9;
    background-color: #fff;
    border-color: #096dd9;
  }

  .ant-btn-dashed.active>a:only-child,
  .ant-btn-dashed:active>a:only-child {
    color: currentColor;
  }

  .ant-btn-dashed.active>a:only-child::after,
  .ant-btn-dashed:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-dashed-disabled,
  .ant-btn-dashed-disabled.active,
  .ant-btn-dashed-disabled:active,
  .ant-btn-dashed-disabled:focus,
  .ant-btn-dashed-disabled:hover,
  .ant-btn-dashed.disabled,
  .ant-btn-dashed.disabled.active,
  .ant-btn-dashed.disabled:active,
  .ant-btn-dashed.disabled:focus,
  .ant-btn-dashed.disabled:hover,
  .ant-btn-dashed[disabled],
  .ant-btn-dashed[disabled].active,
  .ant-btn-dashed[disabled]:active,
  .ant-btn-dashed[disabled]:focus,
  .ant-btn-dashed[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-dashed-disabled.active>a:only-child,
  .ant-btn-dashed-disabled:active>a:only-child,
  .ant-btn-dashed-disabled:focus>a:only-child,
  .ant-btn-dashed-disabled:hover>a:only-child,
  .ant-btn-dashed-disabled>a:only-child,
  .ant-btn-dashed.disabled.active>a:only-child,
  .ant-btn-dashed.disabled:active>a:only-child,
  .ant-btn-dashed.disabled:focus>a:only-child,
  .ant-btn-dashed.disabled:hover>a:only-child,
  .ant-btn-dashed.disabled>a:only-child,
  .ant-btn-dashed[disabled].active>a:only-child,
  .ant-btn-dashed[disabled]:active>a:only-child,
  .ant-btn-dashed[disabled]:focus>a:only-child,
  .ant-btn-dashed[disabled]:hover>a:only-child,
  .ant-btn-dashed[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-dashed-disabled.active>a:only-child::after,
  .ant-btn-dashed-disabled:active>a:only-child::after,
  .ant-btn-dashed-disabled:focus>a:only-child::after,
  .ant-btn-dashed-disabled:hover>a:only-child::after,
  .ant-btn-dashed-disabled>a:only-child::after,
  .ant-btn-dashed.disabled.active>a:only-child::after,
  .ant-btn-dashed.disabled:active>a:only-child::after,
  .ant-btn-dashed.disabled:focus>a:only-child::after,
  .ant-btn-dashed.disabled:hover>a:only-child::after,
  .ant-btn-dashed.disabled>a:only-child::after,
  .ant-btn-dashed[disabled].active>a:only-child::after,
  .ant-btn-dashed[disabled]:active>a:only-child::after,
  .ant-btn-dashed[disabled]:focus>a:only-child::after,
  .ant-btn-dashed[disabled]:hover>a:only-child::after,
  .ant-btn-dashed[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-danger {
    color: #fff;
    background-color: #ff4d4f;
    border-color: #ff4d4f;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }

  .ant-btn-danger>a:only-child {
    color: currentColor;
  }

  .ant-btn-danger>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-danger:focus,
  .ant-btn-danger:hover {
    color: #fff;
    background-color: #ff7875;
    border-color: #ff7875;
  }

  .ant-btn-danger:focus>a:only-child,
  .ant-btn-danger:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn-danger:focus>a:only-child::after,
  .ant-btn-danger:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-danger.active,
  .ant-btn-danger:active {
    color: #fff;
    background-color: #d9363e;
    border-color: #d9363e;
  }

  .ant-btn-danger.active>a:only-child,
  .ant-btn-danger:active>a:only-child {
    color: currentColor;
  }

  .ant-btn-danger.active>a:only-child::after,
  .ant-btn-danger:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-danger-disabled,
  .ant-btn-danger-disabled.active,
  .ant-btn-danger-disabled:active,
  .ant-btn-danger-disabled:focus,
  .ant-btn-danger-disabled:hover,
  .ant-btn-danger.disabled,
  .ant-btn-danger.disabled.active,
  .ant-btn-danger.disabled:active,
  .ant-btn-danger.disabled:focus,
  .ant-btn-danger.disabled:hover,
  .ant-btn-danger[disabled],
  .ant-btn-danger[disabled].active,
  .ant-btn-danger[disabled]:active,
  .ant-btn-danger[disabled]:focus,
  .ant-btn-danger[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-danger-disabled.active>a:only-child,
  .ant-btn-danger-disabled:active>a:only-child,
  .ant-btn-danger-disabled:focus>a:only-child,
  .ant-btn-danger-disabled:hover>a:only-child,
  .ant-btn-danger-disabled>a:only-child,
  .ant-btn-danger.disabled.active>a:only-child,
  .ant-btn-danger.disabled:active>a:only-child,
  .ant-btn-danger.disabled:focus>a:only-child,
  .ant-btn-danger.disabled:hover>a:only-child,
  .ant-btn-danger.disabled>a:only-child,
  .ant-btn-danger[disabled].active>a:only-child,
  .ant-btn-danger[disabled]:active>a:only-child,
  .ant-btn-danger[disabled]:focus>a:only-child,
  .ant-btn-danger[disabled]:hover>a:only-child,
  .ant-btn-danger[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-danger-disabled.active>a:only-child::after,
  .ant-btn-danger-disabled:active>a:only-child::after,
  .ant-btn-danger-disabled:focus>a:only-child::after,
  .ant-btn-danger-disabled:hover>a:only-child::after,
  .ant-btn-danger-disabled>a:only-child::after,
  .ant-btn-danger.disabled.active>a:only-child::after,
  .ant-btn-danger.disabled:active>a:only-child::after,
  .ant-btn-danger.disabled:focus>a:only-child::after,
  .ant-btn-danger.disabled:hover>a:only-child::after,
  .ant-btn-danger.disabled>a:only-child::after,
  .ant-btn-danger[disabled].active>a:only-child::after,
  .ant-btn-danger[disabled]:active>a:only-child::after,
  .ant-btn-danger[disabled]:focus>a:only-child::after,
  .ant-btn-danger[disabled]:hover>a:only-child::after,
  .ant-btn-danger[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-link {
    color: #1890ff;
    background-color: transparent;
    border-color: transparent;
    box-shadow: none;
  }

  .ant-btn-link>a:only-child {
    color: currentColor;
  }

  .ant-btn-link>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-link:focus,
  .ant-btn-link:hover {
    color: #40a9ff;
    background-color: transparent;
    border-color: #40a9ff;
  }

  .ant-btn-link:focus>a:only-child,
  .ant-btn-link:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn-link:focus>a:only-child::after,
  .ant-btn-link:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-link.active,
  .ant-btn-link:active {
    color: #096dd9;
    background-color: transparent;
    border-color: #096dd9;
  }

  .ant-btn-link.active>a:only-child,
  .ant-btn-link:active>a:only-child {
    color: currentColor;
  }

  .ant-btn-link.active>a:only-child::after,
  .ant-btn-link:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-link-disabled,
  .ant-btn-link-disabled.active,
  .ant-btn-link-disabled:active,
  .ant-btn-link-disabled:focus,
  .ant-btn-link-disabled:hover,
  .ant-btn-link.disabled,
  .ant-btn-link.disabled.active,
  .ant-btn-link.disabled:active,
  .ant-btn-link.disabled:focus,
  .ant-btn-link.disabled:hover,
  .ant-btn-link[disabled],
  .ant-btn-link[disabled].active,
  .ant-btn-link[disabled]:active,
  .ant-btn-link[disabled]:focus,
  .ant-btn-link[disabled]:hover {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
  }

  .ant-btn-link:active,
  .ant-btn-link:focus,
  .ant-btn-link:hover {
    border-color: transparent;
  }

  .ant-btn-link-disabled,
  .ant-btn-link-disabled.active,
  .ant-btn-link-disabled:active,
  .ant-btn-link-disabled:focus,
  .ant-btn-link-disabled:hover,
  .ant-btn-link.disabled,
  .ant-btn-link.disabled.active,
  .ant-btn-link.disabled:active,
  .ant-btn-link.disabled:focus,
  .ant-btn-link.disabled:hover,
  .ant-btn-link[disabled],
  .ant-btn-link[disabled].active,
  .ant-btn-link[disabled]:active,
  .ant-btn-link[disabled]:focus,
  .ant-btn-link[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: transparent;
    border-color: transparent;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-link-disabled.active>a:only-child,
  .ant-btn-link-disabled:active>a:only-child,
  .ant-btn-link-disabled:focus>a:only-child,
  .ant-btn-link-disabled:hover>a:only-child,
  .ant-btn-link-disabled>a:only-child,
  .ant-btn-link.disabled.active>a:only-child,
  .ant-btn-link.disabled:active>a:only-child,
  .ant-btn-link.disabled:focus>a:only-child,
  .ant-btn-link.disabled:hover>a:only-child,
  .ant-btn-link.disabled>a:only-child,
  .ant-btn-link[disabled].active>a:only-child,
  .ant-btn-link[disabled]:active>a:only-child,
  .ant-btn-link[disabled]:focus>a:only-child,
  .ant-btn-link[disabled]:hover>a:only-child,
  .ant-btn-link[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-link-disabled.active>a:only-child::after,
  .ant-btn-link-disabled:active>a:only-child::after,
  .ant-btn-link-disabled:focus>a:only-child::after,
  .ant-btn-link-disabled:hover>a:only-child::after,
  .ant-btn-link-disabled>a:only-child::after,
  .ant-btn-link.disabled.active>a:only-child::after,
  .ant-btn-link.disabled:active>a:only-child::after,
  .ant-btn-link.disabled:focus>a:only-child::after,
  .ant-btn-link.disabled:hover>a:only-child::after,
  .ant-btn-link.disabled>a:only-child::after,
  .ant-btn-link[disabled].active>a:only-child::after,
  .ant-btn-link[disabled]:active>a:only-child::after,
  .ant-btn-link[disabled]:focus>a:only-child::after,
  .ant-btn-link[disabled]:hover>a:only-child::after,
  .ant-btn-link[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-icon-only {
    width: 32px;
    height: 32px;
    padding: 0;
    font-size: 16px;
    border-radius: 4px;
  }

  .ant-btn-icon-only.ant-btn-lg {
    width: 40px;
    height: 40px;
    padding: 0;
    font-size: 18px;
    border-radius: 4px;
  }

  .ant-btn-icon-only.ant-btn-sm {
    width: 24px;
    height: 24px;
    padding: 0;
    font-size: 14px;
    border-radius: 4px;
  }

  .ant-btn-icon-only>i {
    vertical-align: middle;
  }

  .ant-btn-round {
    height: 32px;
    padding: 0 16px;
    font-size: 14px;
    border-radius: 32px;
  }

  .ant-btn-round.ant-btn-lg {
    height: 40px;
    padding: 0 20px;
    font-size: 16px;
    border-radius: 40px;
  }

  .ant-btn-round.ant-btn-sm {
    height: 24px;
    padding: 0 12px;
    font-size: 14px;
    border-radius: 24px;
  }

  .ant-btn-round.ant-btn-icon-only {
    width: auto;
  }

  .ant-btn-circle,
  .ant-btn-circle-outline {
    min-width: 32px;
    padding-right: 0;
    padding-left: 0;
    text-align: center;
    border-radius: 50%;
  }

  .ant-btn-circle-outline.ant-btn-lg,
  .ant-btn-circle.ant-btn-lg {
    min-width: 40px;
    border-radius: 50%;
  }

  .ant-btn-circle-outline.ant-btn-sm,
  .ant-btn-circle.ant-btn-sm {
    min-width: 24px;
    border-radius: 50%;
  }

  .ant-btn::before {
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    z-index: 1;
    display: none;
    background: #fff;
    border-radius: inherit;
    opacity: 0.35;
    transition: opacity 0.2s;
    content: "";
    pointer-events: none;
  }

  .ant-btn .anticon {
    transition: margin-left 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-btn .anticon.anticon-minus>svg,
  .ant-btn .anticon.anticon-plus>svg {
    shape-rendering: optimizeSpeed;
  }

  .ant-btn.ant-btn-loading {
    position: relative;
  }

  .ant-btn.ant-btn-loading:not([disabled]) {
    pointer-events: none;
  }

  .ant-btn.ant-btn-loading::before {
    display: block;
  }

  .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding-left: 29px;
  }

  .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) .anticon:not(:last-child) {
    margin-left: -14px;
  }

  .ant-btn-sm.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding-left: 24px;
  }

  .ant-btn-sm.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) .anticon {
    margin-left: -17px;
  }

  .ant-btn-group {
    display: inline-block;
  }

  .ant-btn-group,
  .ant-btn-group>.ant-btn,
  .ant-btn-group>span>.ant-btn {
    position: relative;
  }

  .ant-btn-group>.ant-btn.active,
  .ant-btn-group>.ant-btn:active,
  .ant-btn-group>.ant-btn:focus,
  .ant-btn-group>.ant-btn:hover,
  .ant-btn-group>span>.ant-btn.active,
  .ant-btn-group>span>.ant-btn:active,
  .ant-btn-group>span>.ant-btn:focus,
  .ant-btn-group>span>.ant-btn:hover {
    z-index: 2;
  }

  .ant-btn-group>.ant-btn:disabled,
  .ant-btn-group>span>.ant-btn:disabled {
    z-index: 0;
  }

  .ant-btn-group>.ant-btn-icon-only {
    font-size: 14px;
  }

  .ant-btn-group-lg>.ant-btn,
  .ant-btn-group-lg>span>.ant-btn {
    height: 40px;
    padding: 0 15px;
    font-size: 16px;
    border-radius: 0;
    line-height: 38px;
  }

  .ant-btn-group-lg>.ant-btn.ant-btn-icon-only {
    width: 40px;
    height: 40px;
    padding-right: 0;
    padding-left: 0;
  }

  .ant-btn-group-sm>.ant-btn,
  .ant-btn-group-sm>span>.ant-btn {
    height: 24px;
    padding: 0 7px;
    font-size: 14px;
    border-radius: 0;
    line-height: 22px;
  }

  .ant-btn-group-sm>.ant-btn>.anticon,
  .ant-btn-group-sm>span>.ant-btn>.anticon {
    font-size: 14px;
  }

  .ant-btn-group-sm>.ant-btn.ant-btn-icon-only {
    width: 24px;
    height: 24px;
    padding-right: 0;
    padding-left: 0;
  }

  .ant-btn+.ant-btn-group,
  .ant-btn-group+.ant-btn,
  .ant-btn-group+.ant-btn-group,
  .ant-btn-group .ant-btn+.ant-btn,
  .ant-btn-group .ant-btn+span,
  .ant-btn-group>span+span,
  .ant-btn-group span+.ant-btn {
    margin-left: -1px;
  }

  .ant-btn-group .ant-btn-primary+.ant-btn:not(.ant-btn-primary):not([disabled]) {
    border-left-color: transparent;
  }

  .ant-btn-group .ant-btn {
    border-radius: 0;
  }

  .ant-btn-group>.ant-btn:first-child,
  .ant-btn-group>span:first-child>.ant-btn {
    margin-left: 0;
  }

  .ant-btn-group>.ant-btn:only-child,
  .ant-btn-group>span:only-child>.ant-btn {
    border-radius: 4px;
  }

  .ant-btn-group>.ant-btn:first-child:not(:last-child),
  .ant-btn-group>span:first-child:not(:last-child)>.ant-btn {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  .ant-btn-group>.ant-btn:last-child:not(:first-child),
  .ant-btn-group>span:last-child:not(:first-child)>.ant-btn {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .ant-btn-group-sm>.ant-btn:only-child,
  .ant-btn-group-sm>span:only-child>.ant-btn {
    border-radius: 4px;
  }

  .ant-btn-group-sm>.ant-btn:first-child:not(:last-child),
  .ant-btn-group-sm>span:first-child:not(:last-child)>.ant-btn {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  .ant-btn-group-sm>.ant-btn:last-child:not(:first-child),
  .ant-btn-group-sm>span:last-child:not(:first-child)>.ant-btn {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .ant-btn-group>.ant-btn-group {
    float: left;
  }

  .ant-btn-group>.ant-btn-group:not(:first-child):not(:last-child)>.ant-btn {
    border-radius: 0;
  }

  .ant-btn-group>.ant-btn-group:first-child:not(:last-child)>.ant-btn:last-child {
    padding-right: 8px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .ant-btn-group>.ant-btn-group:last-child:not(:first-child)>.ant-btn:first-child {
    padding-left: 8px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .ant-btn:active>span,
  .ant-btn:focus>span {
    position: relative;
  }

  .ant-btn>.anticon+span,
  .ant-btn>span+.anticon {
    margin-left: 8px;
  }

  .ant-btn-background-ghost {
    color: #fff;
    background: transparent !important;
    border-color: #fff;
  }

  .ant-btn-background-ghost.ant-btn-primary {
    color: #1890ff;
    background-color: transparent;
    border-color: #1890ff;
    text-shadow: none;
  }

  .ant-btn-background-ghost.ant-btn-primary>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-primary>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-primary:focus,
  .ant-btn-background-ghost.ant-btn-primary:hover {
    color: #40a9ff;
    background-color: transparent;
    border-color: #40a9ff;
  }

  .ant-btn-background-ghost.ant-btn-primary:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-primary:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-primary.active,
  .ant-btn-background-ghost.ant-btn-primary:active {
    color: #096dd9;
    background-color: transparent;
    border-color: #096dd9;
  }

  .ant-btn-background-ghost.ant-btn-primary.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary:active>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-primary.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-primary-disabled,
  .ant-btn-background-ghost.ant-btn-primary-disabled.active,
  .ant-btn-background-ghost.ant-btn-primary-disabled:active,
  .ant-btn-background-ghost.ant-btn-primary-disabled:focus,
  .ant-btn-background-ghost.ant-btn-primary-disabled:hover,
  .ant-btn-background-ghost.ant-btn-primary.disabled,
  .ant-btn-background-ghost.ant-btn-primary.disabled.active,
  .ant-btn-background-ghost.ant-btn-primary.disabled:active,
  .ant-btn-background-ghost.ant-btn-primary.disabled:focus,
  .ant-btn-background-ghost.ant-btn-primary.disabled:hover,
  .ant-btn-background-ghost.ant-btn-primary[disabled],
  .ant-btn-background-ghost.ant-btn-primary[disabled].active,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:active,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:focus,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-background-ghost.ant-btn-primary-disabled.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary-disabled:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary-disabled:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary-disabled:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary-disabled>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary.disabled.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary.disabled:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary.disabled:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary.disabled:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary.disabled>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary[disabled].active>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-primary[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-primary-disabled.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary-disabled:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary-disabled:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary-disabled:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary-disabled>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary.disabled.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary.disabled:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary.disabled:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary.disabled:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary.disabled>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary[disabled].active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary[disabled]:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-primary[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-danger {
    color: #ff4d4f;
    background-color: transparent;
    border-color: #ff4d4f;
    text-shadow: none;
  }

  .ant-btn-background-ghost.ant-btn-danger>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-danger>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-danger:focus,
  .ant-btn-background-ghost.ant-btn-danger:hover {
    color: #ff7875;
    background-color: transparent;
    border-color: #ff7875;
  }

  .ant-btn-background-ghost.ant-btn-danger:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-danger:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-danger.active,
  .ant-btn-background-ghost.ant-btn-danger:active {
    color: #d9363e;
    background-color: transparent;
    border-color: #d9363e;
  }

  .ant-btn-background-ghost.ant-btn-danger.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger:active>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-danger.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-danger-disabled,
  .ant-btn-background-ghost.ant-btn-danger-disabled.active,
  .ant-btn-background-ghost.ant-btn-danger-disabled:active,
  .ant-btn-background-ghost.ant-btn-danger-disabled:focus,
  .ant-btn-background-ghost.ant-btn-danger-disabled:hover,
  .ant-btn-background-ghost.ant-btn-danger.disabled,
  .ant-btn-background-ghost.ant-btn-danger.disabled.active,
  .ant-btn-background-ghost.ant-btn-danger.disabled:active,
  .ant-btn-background-ghost.ant-btn-danger.disabled:focus,
  .ant-btn-background-ghost.ant-btn-danger.disabled:hover,
  .ant-btn-background-ghost.ant-btn-danger[disabled],
  .ant-btn-background-ghost.ant-btn-danger[disabled].active,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:active,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:focus,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-background-ghost.ant-btn-danger-disabled.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger-disabled:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger-disabled:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger-disabled:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger-disabled>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger.disabled.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger.disabled:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger.disabled:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger.disabled:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger.disabled>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger[disabled].active>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-danger[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-danger-disabled.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger-disabled:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger-disabled:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger-disabled:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger-disabled>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger.disabled.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger.disabled:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger.disabled:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger.disabled:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger.disabled>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger[disabled].active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger[disabled]:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-danger[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-link {
    color: #1890ff;
    background-color: transparent;
    border-color: transparent;
    text-shadow: none;
    color: #fff;
  }

  .ant-btn-background-ghost.ant-btn-link>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-link>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-link:focus,
  .ant-btn-background-ghost.ant-btn-link:hover {
    color: #40a9ff;
    background-color: transparent;
    border-color: transparent;
  }

  .ant-btn-background-ghost.ant-btn-link:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-link:hover>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-link:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link:hover>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-link.active,
  .ant-btn-background-ghost.ant-btn-link:active {
    color: #096dd9;
    background-color: transparent;
    border-color: transparent;
  }

  .ant-btn-background-ghost.ant-btn-link.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-link:active>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-link.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link:active>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-background-ghost.ant-btn-link-disabled,
  .ant-btn-background-ghost.ant-btn-link-disabled.active,
  .ant-btn-background-ghost.ant-btn-link-disabled:active,
  .ant-btn-background-ghost.ant-btn-link-disabled:focus,
  .ant-btn-background-ghost.ant-btn-link-disabled:hover,
  .ant-btn-background-ghost.ant-btn-link.disabled,
  .ant-btn-background-ghost.ant-btn-link.disabled.active,
  .ant-btn-background-ghost.ant-btn-link.disabled:active,
  .ant-btn-background-ghost.ant-btn-link.disabled:focus,
  .ant-btn-background-ghost.ant-btn-link.disabled:hover,
  .ant-btn-background-ghost.ant-btn-link[disabled],
  .ant-btn-background-ghost.ant-btn-link[disabled].active,
  .ant-btn-background-ghost.ant-btn-link[disabled]:active,
  .ant-btn-background-ghost.ant-btn-link[disabled]:focus,
  .ant-btn-background-ghost.ant-btn-link[disabled]:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }

  .ant-btn-background-ghost.ant-btn-link-disabled.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-link-disabled:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-link-disabled:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-link-disabled:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-link-disabled>a:only-child,
  .ant-btn-background-ghost.ant-btn-link.disabled.active>a:only-child,
  .ant-btn-background-ghost.ant-btn-link.disabled:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-link.disabled:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-link.disabled:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-link.disabled>a:only-child,
  .ant-btn-background-ghost.ant-btn-link[disabled].active>a:only-child,
  .ant-btn-background-ghost.ant-btn-link[disabled]:active>a:only-child,
  .ant-btn-background-ghost.ant-btn-link[disabled]:focus>a:only-child,
  .ant-btn-background-ghost.ant-btn-link[disabled]:hover>a:only-child,
  .ant-btn-background-ghost.ant-btn-link[disabled]>a:only-child {
    color: currentColor;
  }

  .ant-btn-background-ghost.ant-btn-link-disabled.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link-disabled:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link-disabled:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link-disabled:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link-disabled>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link.disabled.active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link.disabled:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link.disabled:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link.disabled:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link.disabled>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link[disabled].active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link[disabled]:active>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link[disabled]:focus>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link[disabled]:hover>a:only-child::after,
  .ant-btn-background-ghost.ant-btn-link[disabled]>a:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: "";
  }

  .ant-btn-two-chinese-chars::first-letter {
    letter-spacing: 0.34em;
  }

  .ant-btn-two-chinese-chars> :not(.anticon) {
    margin-right: -0.34em;
    letter-spacing: 0.34em;
  }

  .ant-btn-block {
    width: 100%;
  }

  .ant-btn:empty {
    vertical-align: top;
  }

  a.ant-btn {
    padding-top: 0.1px;
    line-height: 30px;
  }

  a.ant-btn-lg {
    line-height: 38px;
  }

  a.ant-btn-sm {
    line-height: 22px;
  }

  .ant-message {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: fixed;
    top: 16px;
    left: 0;
    z-index: 1010;
    width: 100%;
    pointer-events: none;
  }

  .ant-message-notice {
    padding: 8px;
    text-align: center;
  }

  .ant-message-notice:first-child {
    margin-top: -8px;
  }

  .ant-message-notice-content {
    display: inline-block;
    padding: 10px 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    pointer-events: all;
  }

  .ant-message-success .anticon {
    color: #52c41a;
  }

  .ant-message-error .anticon {
    color: #f5222d;
  }

  .ant-message-warning .anticon {
    color: #faad14;
  }

  .ant-message-info .anticon,
  .ant-message-loading .anticon {
    color: #1890ff;
  }

  .ant-message .anticon {
    position: relative;
    top: 1px;
    margin-right: 8px;
    font-size: 16px;
  }

  .ant-message-notice.move-up-leave.move-up-leave-active {
    overflow: hidden;
    -webkit-animation-name: messagemoveout;
    animation-name: MessageMoveOut;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
  }

  @-webkit-keyframes MessageMoveOut {
    0% {
      max-height: 150px;
      padding: 8px;
      opacity: 1;
    }

    to {
      max-height: 0;
      padding: 0;
      opacity: 0;
    }
  }

  @keyframes MessageMoveOut {
    0% {
      max-height: 150px;
      padding: 8px;
      opacity: 1;
    }

    to {
      max-height: 0;
      padding: 0;
      opacity: 0;
    }
  }

  .ant-input-number {
    box-sizing: border-box;
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    width: 100%;
    height: 32px;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 1.5;
    background-color: #fff;
    background-image: none;
    transition: all 0.3s;
    display: inline-block;
    width: 90px;
    margin: 0;
    padding: 0;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }

  .ant-input-number::-moz-placeholder {
    color: #bfbfbf;
    opacity: 1;
  }

  .ant-input-number:-ms-input-placeholder {
    color: #bfbfbf;
  }

  .ant-input-number::-webkit-input-placeholder {
    color: #bfbfbf;
  }

  .ant-input-number:-moz-placeholder-shown {
    text-overflow: ellipsis;
  }

  .ant-input-number:-ms-input-placeholder {
    text-overflow: ellipsis;
  }

  .ant-input-number:placeholder-shown {
    text-overflow: ellipsis;
  }

  .ant-input-number:focus {
    border-color: #40a9ff;
    border-right-width: 1px !important;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .ant-input-number[disabled] {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 1;
  }

  .ant-input-number[disabled]:hover {
    border-color: #d9d9d9;
    border-right-width: 1px !important;
  }

  textarea.ant-input-number {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5;
    vertical-align: bottom;
    transition: all 0.3s, height 0s;
  }

  .ant-input-number-lg {
    height: 40px;
    padding: 6px 11px;
  }

  .ant-input-number-sm {
    height: 24px;
    padding: 1px 7px;
  }

  .ant-input-number-handler {
    position: relative;
    display: block;
    width: 100%;
    height: 50%;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 700;
    line-height: 0;
    text-align: center;
    transition: all 0.1s linear;
  }

  .ant-input-number-handler:active {
    background: #f4f4f4;
  }

  .ant-input-number-handler:hover .ant-input-number-handler-down-inner,
  .ant-input-number-handler:hover .ant-input-number-handler-up-inner {
    color: #40a9ff;
  }

  .ant-input-number-handler-down-inner,
  .ant-input-number-handler-up-inner {
    display: inline-block;
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-align: center;
    text-transform: none;
    vertical-align: -0.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    right: 4px;
    width: 12px;
    height: 12px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 12px;
    transition: all 0.1s linear;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .ant-input-number-handler-down-inner>*,
  .ant-input-number-handler-up-inner>* {
    line-height: 1;
  }

  .ant-input-number-handler-down-inner svg,
  .ant-input-number-handler-up-inner svg {
    display: inline-block;
  }

  .ant-input-number-handler-down-inner::before,
  .ant-input-number-handler-up-inner::before {
    display: none;
  }

  .ant-input-number-handler-down-inner .ant-input-number-handler-down-inner-icon,
  .ant-input-number-handler-down-inner .ant-input-number-handler-up-inner-icon,
  .ant-input-number-handler-up-inner .ant-input-number-handler-down-inner-icon,
  .ant-input-number-handler-up-inner .ant-input-number-handler-up-inner-icon {
    display: block;
  }

  .ant-input-number-focused,
  .ant-input-number:hover {
    border-color: #40a9ff;
    border-right-width: 1px !important;
  }

  .ant-input-number-focused {
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .ant-input-number-disabled {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 1;
  }

  .ant-input-number-disabled:hover {
    border-color: #d9d9d9;
    border-right-width: 1px !important;
  }

  .ant-input-number-disabled .ant-input-number-input {
    cursor: not-allowed;
  }

  .ant-input-number-disabled .ant-input-number-handler-wrap {
    display: none;
  }

  .ant-input-number-input {
    width: 100%;
    height: 30px;
    padding: 0 11px;
    text-align: left;
    background-color: transparent;
    border: 0;
    border-radius: 4px;
    outline: 0;
    transition: all 0.3s linear;
    -moz-appearance: textfield !important;
  }

  .ant-input-number-input::-moz-placeholder {
    color: #bfbfbf;
    opacity: 1;
  }

  .ant-input-number-input:-ms-input-placeholder {
    color: #bfbfbf;
  }

  .ant-input-number-input::-webkit-input-placeholder {
    color: #bfbfbf;
  }

  .ant-input-number-input:-moz-placeholder-shown {
    text-overflow: ellipsis;
  }

  .ant-input-number-input:-ms-input-placeholder {
    text-overflow: ellipsis;
  }

  .ant-input-number-input:placeholder-shown {
    text-overflow: ellipsis;
  }

  .ant-input-number-input[type=number]::-webkit-inner-spin-button,
  .ant-input-number-input[type=number]::-webkit-outer-spin-button {
    margin: 0;
    -webkit-appearance: none;
  }

  .ant-input-number-lg {
    padding: 0;
    font-size: 16px;
  }

  .ant-input-number-lg input {
    height: 38px;
  }

  .ant-input-number-sm {
    padding: 0;
  }

  .ant-input-number-sm input {
    height: 22px;
    padding: 0 7px;
  }

  .ant-input-number-handler-wrap {
    position: absolute;
    top: 0;
    right: 0;
    width: 22px;
    height: 100%;
    background: #fff;
    border-left: 1px solid #d9d9d9;
    border-radius: 0 4px 4px 0;
    opacity: 0;
    transition: opacity 0.24s linear 0.1s;
  }

  .ant-input-number-handler-wrap .ant-input-number-handler .ant-input-number-handler-down-inner,
  .ant-input-number-handler-wrap .ant-input-number-handler .ant-input-number-handler-up-inner {
    display: inline-block;
    font-size: 12px;
    font-size: 7px\9;
    transform: scale(0.58333333) rotate(0deg);
    min-width: auto;
    margin-right: 0;
  }

  :root .ant-input-number-handler-wrap .ant-input-number-handler .ant-input-number-handler-down-inner,
  :root .ant-input-number-handler-wrap .ant-input-number-handler .ant-input-number-handler-up-inner {
    font-size: 12px;
  }

  .ant-input-number-handler-wrap:hover .ant-input-number-handler {
    height: 40%;
  }

  .ant-input-number:hover .ant-input-number-handler-wrap {
    opacity: 1;
  }

  .ant-input-number-handler-up {
    border-top-right-radius: 4px;
    cursor: pointer;
  }

  .ant-input-number-handler-up-inner {
    top: 50%;
    margin-top: -5px;
    text-align: center;
  }

  .ant-input-number-handler-up:hover {
    height: 60% !important;
  }

  .ant-input-number-handler-down {
    top: 0;
    border-top: 1px solid #d9d9d9;
    border-bottom-right-radius: 4px;
    cursor: pointer;
  }

  .ant-input-number-handler-down-inner {
    top: 50%;
    margin-top: -6px;
    text-align: center;
  }

  .ant-input-number-handler-down:hover {
    height: 60% !important;
  }

  .ant-input-number-handler-down-disabled,
  .ant-input-number-handler-up-disabled {
    cursor: not-allowed;
  }

  .ant-input-number-handler-down-disabled:hover .ant-input-number-handler-down-inner,
  .ant-input-number-handler-up-disabled:hover .ant-input-number-handler-up-inner {
    color: rgba(0, 0, 0, 0.25);
  }

  .ant-avatar {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    overflow: hidden;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    background: #ccc;
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
  }

  .ant-avatar-image {
    background: transparent;
  }

  .ant-avatar-string {
    position: absolute;
    left: 50%;
    transform-origin: 0 center;
  }

  .ant-avatar.ant-avatar-icon {
    font-size: 18px;
  }

  .ant-avatar-lg {
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
  }

  .ant-avatar-lg-string {
    position: absolute;
    left: 50%;
    transform-origin: 0 center;
  }

  .ant-avatar-lg.ant-avatar-icon {
    font-size: 24px;
  }

  .ant-avatar-sm {
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 50%;
  }

  .ant-avatar-sm-string {
    position: absolute;
    left: 50%;
    transform-origin: 0 center;
  }

  .ant-avatar-sm.ant-avatar-icon {
    font-size: 14px;
  }

  .ant-avatar-square {
    border-radius: 4px;
  }

  .ant-avatar>img {
    display: block;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
  }

  .le5le-topology .menus {
    height: 50px;
    display: flex;
    border-bottom: 1px solid #e5e5e5;
    background-color: #fff;
    white-space: nowrap;
  }

  .le5le-topology .menus>div>div {
    display: flex;
  }

  .le5le-topology .menus .back {
    padding: 0 8px;
    line-height: 50px;
    display: flex;
    align-items: center;
    margin-right: 16px;
    font-size: 14px;
  }

  .le5le-topology .menus .back img {
    height: 16px;
    margin-right: 4px;
  }

  .le5le-topology .menus .back i {
    font-size: 18px;
    margin-right: 4px;
    font-weight: 700;
  }

  .le5le-topology .menus .menu {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 50px;
    color: #595959;
    flex-shrink: 0;
    font-size: 14px;
    padding: 0 16px;
  }

  .le5le-topology .menus .menu.gray {
    padding: 0;
  }

  .le5le-topology .menus .menu img {
    width: 18px;
    height: 18px;
  }

  .le5le-topology .menus .menu .icon {
    height: 24px;
    line-height: 24px;
  }

  .le5le-topology .menus .menu .icon+div {
    font-size: 12px;
  }

  .le5le-topology .menus .menu:hover {
    color: #1890ff;
  }

  .le5le-topology .menus .menu:hover>.dropdown {
    display: block;
  }

  .le5le-topology .menus .menu:hover>.dropdown.recent {
    display: flex;
  }

  .le5le-topology .menus .dropdown {
    position: absolute;
    display: none;
    min-width: 220px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    z-index: 100;
    background-color: #fff;
    padding: 8px 0;
    left: 0;
    top: 100%;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .le5le-topology .menus .dropdown.right {
    left: auto;
  }

  .le5le-topology .menus .dropdown .item {
    line-height: 30px;
    padding: 0 15px;
    font-size: 13px;
    color: #595959;
    position: relative;
  }

  .le5le-topology .menus .dropdown .item a {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    color: #595959;
  }

  .le5le-topology .menus .dropdown .item:hover {
    background-color: #1890ff;
  }

  .le5le-topology .menus .dropdown .item:hover .dropdown {
    display: block;
  }

  .le5le-topology .menus .dropdown .item:hover .dropdown.recent {
    display: flex;
  }

  .le5le-topology .menus .dropdown .item:hover>a {
    color: #fff;
  }

  .le5le-topology .menus .dropdown .item.divider {
    width: calc(100% - 30px);
    height: 0.5px;
    background-color: #e5e5e5;
    margin: 5px 15px;
  }

  .le5le-topology .menus .dropdown .item .dropdown {
    top: -8px;
    left: 100%;
  }

  .le5le-topology .menus .dropdown .item .dropdown a {
    color: #595959;
  }

  .le5le-topology .menus .dropdown .item .dropdown .item:hover a {
    color: #fff;
  }

  .le5le-topology .menus .dropdown>span {
    color: #595959 !important;
  }

  .le5le-topology .menus .dropdown>span:hover {
    color: #1890ff !important;
  }

  .le5le-topology .menus .dropdown.right {
    left: inherit;
    right: 0;
  }

  .le5le-topology .menus .recent {
    width: 710px;
    height: 600px;
    overflow: auto;
    flex-wrap: wrap;
    align-content: flex-start;
    padding: 12px;
  }

  .le5le-topology .menus .recent>.item {
    position: relative;
    display: flex;
    margin: 8px;
    border: 1px solid #d9d9d9 !important;
    cursor: pointer;
    min-width: 120px !important;
    width: 120px !important;
    height: 120px !important;
    padding: 10px !important;
  }

  .le5le-topology .menus .recent>.item:hover {
    background: none;
    border-color: #1890ff !important;
  }

  .le5le-topology .menus .recent>.item img {
    width: 100%;
    height: 100%;
  }

  .le5le-topology .menus .recent>.item i {
    position: absolute;
    top: 4px;
    right: 12px;
    z-index: 1;
  }

  .le5le-topology .menus .recent>.item i:hover {
    color: #1890ff !important;
  }

  .le5le-topology .menus.clicked .dropdown {
    display: none !important;
  }

  .le5le-topology .menus ::v-deep .ant-avatar-string {
    font-size: 16px;
    margin-top: -1px;
  }

  .le5le-topology .lines :first-child {
    height: 50px;
  }

  .le5le-topology .lines .t-icon {
    font-size: 30px;
  }

  .le5le-topology .lines .item {
    max-height: 30px;
  }

  .le5le-topology .ant-avatar {
    background: #1890ff;
  }

  .ant-input {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    width: 100%;
    height: 32px;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 1.5;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .ant-input::-moz-placeholder {
    color: #bfbfbf;
    opacity: 1;
  }

  .ant-input:-ms-input-placeholder {
    color: #bfbfbf;
  }

  .ant-input::-webkit-input-placeholder {
    color: #bfbfbf;
  }

  .ant-input:-moz-placeholder-shown {
    text-overflow: ellipsis;
  }

  .ant-input:-ms-input-placeholder {
    text-overflow: ellipsis;
  }

  .ant-input:placeholder-shown {
    text-overflow: ellipsis;
  }

  .ant-input:focus,
  .ant-input:hover {
    border-color: #40a9ff;
    border-right-width: 1px !important;
  }

  .ant-input:focus {
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 1;
  }

  .ant-input-disabled:hover {
    border-color: #d9d9d9;
    border-right-width: 1px !important;
  }

  .ant-input[disabled] {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 1;
  }

  .ant-input[disabled]:hover {
    border-color: #d9d9d9;
    border-right-width: 1px !important;
  }

  textarea.ant-input {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5;
    vertical-align: bottom;
    transition: all 0.3s, height 0s;
  }

  .ant-input-lg {
    height: 40px;
    padding: 6px 11px;
    font-size: 16px;
  }

  .ant-input-sm {
    height: 24px;
    padding: 1px 7px;
  }

  .ant-input-group {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    display: table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }

  .ant-input-group[class*=col-] {
    float: none;
    padding-right: 0;
    padding-left: 0;
  }

  .ant-input-group>[class*=col-] {
    padding-right: 8px;
  }

  .ant-input-group>[class*=col-]:last-child {
    padding-right: 0;
  }

  .ant-input-group-addon,
  .ant-input-group-wrap,
  .ant-input-group>.ant-input {
    display: table-cell;
  }

  .ant-input-group-addon:not(:first-child):not(:last-child),
  .ant-input-group-wrap:not(:first-child):not(:last-child),
  .ant-input-group>.ant-input:not(:first-child):not(:last-child) {
    border-radius: 0;
  }

  .ant-input-group-addon,
  .ant-input-group-wrap {
    width: 1px;
    white-space: nowrap;
    vertical-align: middle;
  }

  .ant-input-group-wrap>* {
    display: block !important;
  }

  .ant-input-group .ant-input {
    float: left;
    width: 100%;
    margin-bottom: 0;
    text-align: inherit;
  }

  .ant-input-group .ant-input:focus,
  .ant-input-group .ant-input:hover {
    z-index: 1;
    border-right-width: 1px;
  }

  .ant-input-group-addon {
    position: relative;
    padding: 0 11px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 400;
    font-size: 14px;
    text-align: center;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .ant-input-group-addon .ant-select {
    margin: -5px -11px;
  }

  .ant-input-group-addon .ant-select .ant-select-selection {
    margin: -1px;
    background-color: inherit;
    border: 1px solid transparent;
    box-shadow: none;
  }

  .ant-input-group-addon .ant-select-focused .ant-select-selection,
  .ant-input-group-addon .ant-select-open .ant-select-selection {
    color: #1890ff;
  }

  .ant-input-group-addon>i:only-child::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    content: "";
  }

  .ant-input-group-addon:first-child,
  .ant-input-group-addon:first-child .ant-select .ant-select-selection,
  .ant-input-group>.ant-input:first-child,
  .ant-input-group>.ant-input:first-child .ant-select .ant-select-selection {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .ant-input-group>.ant-input-affix-wrapper:not(:first-child) .ant-input {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .ant-input-group>.ant-input-affix-wrapper:not(:last-child) .ant-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .ant-input-group-addon:first-child {
    border-right: 0;
  }

  .ant-input-group-addon:last-child {
    border-left: 0;
  }

  .ant-input-group-addon:last-child,
  .ant-input-group-addon:last-child .ant-select .ant-select-selection,
  .ant-input-group>.ant-input:last-child,
  .ant-input-group>.ant-input:last-child .ant-select .ant-select-selection {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .ant-input-group-lg .ant-input,
  .ant-input-group-lg>.ant-input-group-addon {
    height: 40px;
    padding: 6px 11px;
    font-size: 16px;
  }

  .ant-input-group-sm .ant-input,
  .ant-input-group-sm>.ant-input-group-addon {
    height: 24px;
    padding: 1px 7px;
  }

  .ant-input-group-lg .ant-select-selection--single {
    height: 40px;
  }

  .ant-input-group-sm .ant-select-selection--single {
    height: 24px;
  }

  .ant-input-group .ant-input-affix-wrapper {
    display: table-cell;
    float: left;
    width: 100%;
  }

  .ant-input-group.ant-input-group-compact {
    display: block;
    zoom: 1;
  }

  .ant-input-group.ant-input-group-compact::after,
  .ant-input-group.ant-input-group-compact::before {
    display: table;
    content: "";
  }

  .ant-input-group.ant-input-group-compact::after {
    clear: both;
  }

  .ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child),
  .ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child),
  .ant-input-group.ant-input-group-compact>.ant-input:not(:first-child):not(:last-child) {
    border-right-width: 1px;
  }

  .ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child):focus,
  .ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child):hover,
  .ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child):focus,
  .ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child):hover,
  .ant-input-group.ant-input-group-compact>.ant-input:not(:first-child):not(:last-child):focus,
  .ant-input-group.ant-input-group-compact>.ant-input:not(:first-child):not(:last-child):hover {
    z-index: 1;
  }

  .ant-input-group.ant-input-group-compact>* {
    display: inline-block;
    float: none;
    vertical-align: top;
    border-radius: 0;
  }

  .ant-input-group.ant-input-group-compact> :not(:last-child) {
    margin-right: -1px;
    border-right-width: 1px;
  }

  .ant-input-group.ant-input-group-compact .ant-input {
    float: none;
  }

  .ant-input-group.ant-input-group-compact>.ant-calendar-picker .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-mention-wrapper .ant-mention-editor,
  .ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selection,
  .ant-input-group.ant-input-group-compact>.ant-time-picker .ant-time-picker-input {
    border-right-width: 1px;
    border-radius: 0;
  }

  .ant-input-group.ant-input-group-compact>.ant-calendar-picker .ant-input:focus,
  .ant-input-group.ant-input-group-compact>.ant-calendar-picker .ant-input:hover,
  .ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input:focus,
  .ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input:hover,
  .ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input:focus,
  .ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input:hover,
  .ant-input-group.ant-input-group-compact>.ant-mention-wrapper .ant-mention-editor:focus,
  .ant-input-group.ant-input-group-compact>.ant-mention-wrapper .ant-mention-editor:hover,
  .ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input:focus,
  .ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input:hover,
  .ant-input-group.ant-input-group-compact>.ant-select-focused,
  .ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selection:focus,
  .ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selection:hover,
  .ant-input-group.ant-input-group-compact>.ant-time-picker .ant-time-picker-input:focus,
  .ant-input-group.ant-input-group-compact>.ant-time-picker .ant-time-picker-input:hover {
    z-index: 1;
  }

  .ant-input-group.ant-input-group-compact>.ant-calendar-picker:first-child .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-cascader-picker:first-child .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-mention-wrapper:first-child .ant-mention-editor,
  .ant-input-group.ant-input-group-compact>.ant-select-auto-complete:first-child .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-select:first-child>.ant-select-selection,
  .ant-input-group.ant-input-group-compact>.ant-time-picker:first-child .ant-time-picker-input,
  .ant-input-group.ant-input-group-compact> :first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  .ant-input-group.ant-input-group-compact>.ant-calendar-picker:last-child .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-cascader-picker-focused:last-child .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-cascader-picker:last-child .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-mention-wrapper:last-child .ant-mention-editor,
  .ant-input-group.ant-input-group-compact>.ant-select-auto-complete:last-child .ant-input,
  .ant-input-group.ant-input-group-compact>.ant-select:last-child>.ant-select-selection,
  .ant-input-group.ant-input-group-compact>.ant-time-picker:last-child .ant-time-picker-input,
  .ant-input-group.ant-input-group-compact> :last-child {
    border-right-width: 1px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input {
    vertical-align: top;
  }

  .ant-input-group-wrapper {
    display: inline-block;
    width: 100%;
    text-align: start;
    vertical-align: top;
  }

  .ant-input-affix-wrapper {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    width: 100%;
    text-align: start;
  }

  .ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
    border-color: #40a9ff;
    border-right-width: 1px !important;
  }

  .ant-input-affix-wrapper .ant-input {
    position: relative;
    text-align: inherit;
  }

  .ant-input-affix-wrapper .ant-input-prefix,
  .ant-input-affix-wrapper .ant-input-suffix {
    position: absolute;
    top: 50%;
    z-index: 2;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.65);
    line-height: 0;
    transform: translateY(-50%);
  }

  .ant-input-affix-wrapper .ant-input-prefix :not(.anticon),
  .ant-input-affix-wrapper .ant-input-suffix :not(.anticon) {
    line-height: 1.5;
  }

  .ant-input-affix-wrapper .ant-input-disabled~.ant-input-suffix .anticon {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  .ant-input-affix-wrapper .ant-input-prefix {
    left: 12px;
  }

  .ant-input-affix-wrapper .ant-input-suffix {
    right: 12px;
  }

  .ant-input-affix-wrapper .ant-input:not(:first-child) {
    padding-left: 30px;
  }

  .ant-input-affix-wrapper .ant-input:not(:last-child) {
    padding-right: 30px;
  }

  .ant-input-affix-wrapper.ant-input-affix-wrapper-input-with-clear-btn .ant-input:not(:last-child) {
    padding-right: 49px;
  }

  .ant-input-affix-wrapper.ant-input-affix-wrapper-textarea-with-clear-btn .ant-input {
    padding-right: 22px;
  }

  .ant-input-password-icon {
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    transition: all 0.3s;
  }

  .ant-input-password-icon:hover {
    color: #333;
  }

  .ant-input-clear-icon {
    color: rgba(0, 0, 0, 0.25);
    font-size: 12px;
    cursor: pointer;
    transition: color 0.3s;
    vertical-align: 0;
  }

  .ant-input-clear-icon:hover {
    color: rgba(0, 0, 0, 0.45);
  }

  .ant-input-clear-icon:active {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-input-clear-icon+i {
    margin-left: 6px;
  }

  .ant-input-textarea-clear-icon {
    color: rgba(0, 0, 0, 0.25);
    font-size: 12px;
    cursor: pointer;
    transition: color 0.3s;
    position: absolute;
    top: 0;
    right: 0;
    margin: 8px 8px 0 0;
  }

  .ant-input-textarea-clear-icon:hover {
    color: rgba(0, 0, 0, 0.45);
  }

  .ant-input-textarea-clear-icon:active {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-input-textarea-clear-icon+i {
    margin-left: 6px;
  }

  .ant-input-search-icon {
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    transition: all 0.3s;
  }

  .ant-input-search-icon:hover {
    color: rgba(0, 0, 0, 0.8);
  }

  .ant-input-search-enter-button input {
    border-right: 0;
  }

  .ant-input-search-enter-button+.ant-input-group-addon,
  .ant-input-search-enter-button input+.ant-input-group-addon {
    padding: 0;
    border: 0;
  }

  .ant-input-search-enter-button+.ant-input-group-addon .ant-input-search-button,
  .ant-input-search-enter-button input+.ant-input-group-addon .ant-input-search-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .ant-spin {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: absolute;
    display: none;
    color: #1890ff;
    text-align: center;
    vertical-align: middle;
    opacity: 0;
    transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .ant-spin-spinning {
    position: static;
    display: inline-block;
    opacity: 1;
  }

  .ant-spin-nested-loading {
    position: relative;
  }

  .ant-spin-nested-loading>div>.ant-spin {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 4;
    display: block;
    width: 100%;
    height: 100%;
    max-height: 400px;
  }

  .ant-spin-nested-loading>div>.ant-spin .ant-spin-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -10px;
  }

  .ant-spin-nested-loading>div>.ant-spin .ant-spin-text {
    position: absolute;
    top: 50%;
    width: 100%;
    padding-top: 5px;
    text-shadow: 0 1px 2px #fff;
  }

  .ant-spin-nested-loading>div>.ant-spin.ant-spin-show-text .ant-spin-dot {
    margin-top: -20px;
  }

  .ant-spin-nested-loading>div>.ant-spin-sm .ant-spin-dot {
    margin: -7px;
  }

  .ant-spin-nested-loading>div>.ant-spin-sm .ant-spin-text {
    padding-top: 2px;
  }

  .ant-spin-nested-loading>div>.ant-spin-sm.ant-spin-show-text .ant-spin-dot {
    margin-top: -17px;
  }

  .ant-spin-nested-loading>div>.ant-spin-lg .ant-spin-dot {
    margin: -16px;
  }

  .ant-spin-nested-loading>div>.ant-spin-lg .ant-spin-text {
    padding-top: 11px;
  }

  .ant-spin-nested-loading>div>.ant-spin-lg.ant-spin-show-text .ant-spin-dot {
    margin-top: -26px;
  }

  .ant-spin-container {
    position: relative;
    transition: opacity 0.3s;
  }

  .ant-spin-container::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    display: none\9;
    width: 100%;
    height: 100%;
    background: #fff;
    opacity: 0;
    transition: all 0.3s;
    content: "";
    pointer-events: none;
  }

  .ant-spin-blur {
    clear: both;
    overflow: hidden;
    opacity: 0.5;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: none;
  }

  .ant-spin-blur::after {
    opacity: 0.4;
    pointer-events: auto;
  }

  .ant-spin-tip {
    color: rgba(0, 0, 0, 0.45);
  }

  .ant-spin-dot {
    position: relative;
    display: inline-block;
    font-size: 20px;
    width: 1em;
    height: 1em;
  }

  .ant-spin-dot-item {
    position: absolute;
    display: block;
    width: 9px;
    height: 9px;
    background-color: #1890ff;
    border-radius: 100%;
    transform: scale(0.75);
    transform-origin: 50% 50%;
    opacity: 0.3;
    -webkit-animation: antspinmove 1s linear infinite alternate;
    animation: antSpinMove 1s linear infinite alternate;
  }

  .ant-spin-dot-item:first-child {
    top: 0;
    left: 0;
  }

  .ant-spin-dot-item:nth-child(2) {
    top: 0;
    right: 0;
    -webkit-animation-delay: 0.4s;
    animation-delay: 0.4s;
  }

  .ant-spin-dot-item:nth-child(3) {
    right: 0;
    bottom: 0;
    -webkit-animation-delay: 0.8s;
    animation-delay: 0.8s;
  }

  .ant-spin-dot-item:nth-child(4) {
    bottom: 0;
    left: 0;
    -webkit-animation-delay: 1.2s;
    animation-delay: 1.2s;
  }

  .ant-spin-dot-spin {
    transform: rotate(45deg);
    -webkit-animation: antrotate 1.2s linear infinite;
    animation: antRotate 1.2s linear infinite;
  }

  .ant-spin-sm .ant-spin-dot {
    font-size: 14px;
  }

  .ant-spin-sm .ant-spin-dot i {
    width: 6px;
    height: 6px;
  }

  .ant-spin-lg .ant-spin-dot {
    font-size: 32px;
  }

  .ant-spin-lg .ant-spin-dot i {
    width: 14px;
    height: 14px;
  }

  .ant-spin.ant-spin-show-text .ant-spin-text {
    display: block;
  }

  @media (-ms-high-contrast: active),
  (-ms-high-contrast: none) {
    .ant-spin-blur {
      background: #fff;
      opacity: 0.5;
    }
  }

  @-webkit-keyframes antSpinMove {
    to {
      opacity: 1;
    }
  }

  @keyframes antSpinMove {
    to {
      opacity: 1;
    }
  }

  @-webkit-keyframes antRotate {
    to {
      transform: rotate(405deg);
    }
  }

  @keyframes antRotate {
    to {
      transform: rotate(405deg);
    }
  }

  .ant-popover {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1030;
    font-weight: 400;
    white-space: normal;
    text-align: left;
    cursor: auto;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  .ant-popover::after {
    position: absolute;
    background: hsla(0, 0%, 100%, 0.01);
    content: "";
  }

  .ant-popover-hidden {
    display: none;
  }

  .ant-popover-placement-top,
  .ant-popover-placement-topLeft,
  .ant-popover-placement-topRight {
    padding-bottom: 10px;
  }

  .ant-popover-placement-right,
  .ant-popover-placement-rightBottom,
  .ant-popover-placement-rightTop {
    padding-left: 10px;
  }

  .ant-popover-placement-bottom,
  .ant-popover-placement-bottomLeft,
  .ant-popover-placement-bottomRight {
    padding-top: 10px;
  }

  .ant-popover-placement-left,
  .ant-popover-placement-leftBottom,
  .ant-popover-placement-leftTop {
    padding-right: 10px;
  }

  .ant-popover-inner {
    background-color: #fff;
    background-clip: padding-box;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.15) \9;
  }

  @media (-ms-high-contrast: none),
  screen and (-ms-high-contrast: active) {
    .ant-popover-inner {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .ant-popover-title {
    min-width: 177px;
    min-height: 32px;
    margin: 0;
    padding: 5px 16px 4px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    border-bottom: 1px solid #e8e8e8;
  }

  .ant-popover-inner-content {
    padding: 12px 16px;
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-popover-message {
    position: relative;
    padding: 4px 0 12px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
  }

  .ant-popover-message>.anticon {
    position: absolute;
    top: 8px;
    color: #faad14;
    font-size: 14px;
  }

  .ant-popover-message-title {
    padding-left: 22px;
  }

  .ant-popover-buttons {
    margin-bottom: 4px;
    text-align: right;
  }

  .ant-popover-buttons button {
    margin-left: 8px;
  }

  .ant-popover-arrow {
    position: absolute;
    display: block;
    width: 8.48528137px;
    height: 8.48528137px;
    background: transparent;
    border-style: solid;
    border-width: 4.24264069px;
    transform: rotate(45deg);
  }

  .ant-popover-placement-top>.ant-popover-content>.ant-popover-arrow,
  .ant-popover-placement-topLeft>.ant-popover-content>.ant-popover-arrow,
  .ant-popover-placement-topRight>.ant-popover-content>.ant-popover-arrow {
    bottom: 6.2px;
    border-top-color: transparent;
    border-right-color: #fff;
    border-bottom-color: #fff;
    border-left-color: transparent;
    box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
  }

  .ant-popover-placement-top>.ant-popover-content>.ant-popover-arrow {
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
  }

  .ant-popover-placement-topLeft>.ant-popover-content>.ant-popover-arrow {
    left: 16px;
  }

  .ant-popover-placement-topRight>.ant-popover-content>.ant-popover-arrow {
    right: 16px;
  }

  .ant-popover-placement-right>.ant-popover-content>.ant-popover-arrow,
  .ant-popover-placement-rightBottom>.ant-popover-content>.ant-popover-arrow,
  .ant-popover-placement-rightTop>.ant-popover-content>.ant-popover-arrow {
    left: 6px;
    border-top-color: transparent;
    border-right-color: transparent;
    border-bottom-color: #fff;
    border-left-color: #fff;
    box-shadow: -3px 3px 7px rgba(0, 0, 0, 0.07);
  }

  .ant-popover-placement-right>.ant-popover-content>.ant-popover-arrow {
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
  }

  .ant-popover-placement-rightTop>.ant-popover-content>.ant-popover-arrow {
    top: 12px;
  }

  .ant-popover-placement-rightBottom>.ant-popover-content>.ant-popover-arrow {
    bottom: 12px;
  }

  .ant-popover-placement-bottom>.ant-popover-content>.ant-popover-arrow,
  .ant-popover-placement-bottomLeft>.ant-popover-content>.ant-popover-arrow,
  .ant-popover-placement-bottomRight>.ant-popover-content>.ant-popover-arrow {
    top: 6px;
    border-top-color: #fff;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: #fff;
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
  }

  .ant-popover-placement-bottom>.ant-popover-content>.ant-popover-arrow {
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
  }

  .ant-popover-placement-bottomLeft>.ant-popover-content>.ant-popover-arrow {
    left: 16px;
  }

  .ant-popover-placement-bottomRight>.ant-popover-content>.ant-popover-arrow {
    right: 16px;
  }

  .ant-popover-placement-left>.ant-popover-content>.ant-popover-arrow,
  .ant-popover-placement-leftBottom>.ant-popover-content>.ant-popover-arrow,
  .ant-popover-placement-leftTop>.ant-popover-content>.ant-popover-arrow {
    right: 6px;
    border-top-color: #fff;
    border-right-color: #fff;
    border-bottom-color: transparent;
    border-left-color: transparent;
    box-shadow: 3px -3px 7px rgba(0, 0, 0, 0.07);
  }

  .ant-popover-placement-left>.ant-popover-content>.ant-popover-arrow {
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
  }

  .ant-popover-placement-leftTop>.ant-popover-content>.ant-popover-arrow {
    top: 12px;
  }

  .ant-popover-placement-leftBottom>.ant-popover-content>.ant-popover-arrow {
    bottom: 12px;
  }

  .ant-modal {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    top: 100px;
    width: auto;
    margin: 0 auto;
    padding-bottom: 24px;
    pointer-events: none;
  }

  .ant-modal-wrap {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    overflow: auto;
    outline: 0;
    -webkit-overflow-scrolling: touch;
  }

  .ant-modal-title {
    margin: 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    word-wrap: break-word;
  }

  .ant-modal-content {
    position: relative;
    background-color: #fff;
    background-clip: padding-box;
    border: 0;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    pointer-events: auto;
  }

  .ant-modal-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    padding: 0;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 700;
    line-height: 1;
    text-decoration: none;
    background: transparent;
    border: 0;
    outline: 0;
    cursor: pointer;
    transition: color 0.3s;
  }

  .ant-modal-close-x {
    display: block;
    width: 56px;
    height: 56px;
    font-size: 16px;
    font-style: normal;
    line-height: 56px;
    text-align: center;
    text-transform: none;
    text-rendering: auto;
  }

  .ant-modal-close:focus,
  .ant-modal-close:hover {
    color: rgba(0, 0, 0, 0.75);
    text-decoration: none;
  }

  .ant-modal-header {
    padding: 16px 24px;
    color: rgba(0, 0, 0, 0.65);
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
  }

  .ant-modal-body {
    padding: 24px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
  }

  .ant-modal-footer {
    padding: 10px 16px;
    text-align: right;
    background: transparent;
    border-top: 1px solid #e8e8e8;
    border-radius: 0 0 4px 4px;
  }

  .ant-modal-footer button+button {
    margin-bottom: 0;
    margin-left: 8px;
  }

  .ant-modal.zoom-appear,
  .ant-modal.zoom-enter {
    transform: none;
    opacity: 0;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .ant-modal-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    filter: alpha(opacity=50);
  }

  .ant-modal-mask-hidden {
    display: none;
  }

  .ant-modal-open {
    overflow: hidden;
  }

  .ant-modal-centered {
    text-align: center;
  }

  .ant-modal-centered::before {
    display: inline-block;
    width: 0;
    height: 100%;
    vertical-align: middle;
    content: "";
  }

  .ant-modal-centered .ant-modal {
    top: 0;
    display: inline-block;
    text-align: left;
    vertical-align: middle;
  }

  @media (max-width: 767px) {
    .ant-modal {
      max-width: calc(100vw - 16px);
      margin: 8px auto;
    }

    .ant-modal-centered .ant-modal {
      flex: 1;
    }
  }

  .ant-modal-confirm .ant-modal-close,
  .ant-modal-confirm .ant-modal-header {
    display: none;
  }

  .ant-modal-confirm .ant-modal-body {
    padding: 32px 32px 24px;
  }

  .ant-modal-confirm-body-wrapper {
    zoom: 1;
  }

  .ant-modal-confirm-body-wrapper::after,
  .ant-modal-confirm-body-wrapper::before {
    display: table;
    content: "";
  }

  .ant-modal-confirm-body-wrapper::after {
    clear: both;
  }

  .ant-modal-confirm-body .ant-modal-confirm-title {
    display: block;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 1.4;
  }

  .ant-modal-confirm-body .ant-modal-confirm-content {
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
  }

  .ant-modal-confirm-body>.anticon {
    float: left;
    margin-right: 16px;
    font-size: 22px;
  }

  .ant-modal-confirm-body>.anticon+.ant-modal-confirm-title+.ant-modal-confirm-content {
    margin-left: 38px;
  }

  .ant-modal-confirm .ant-modal-confirm-btns {
    float: right;
    margin-top: 24px;
  }

  .ant-modal-confirm .ant-modal-confirm-btns button+button {
    margin-bottom: 0;
    margin-left: 8px;
  }

  .ant-modal-confirm-error .ant-modal-confirm-body>.anticon {
    color: #f5222d;
  }

  .ant-modal-confirm-confirm .ant-modal-confirm-body>.anticon,
  .ant-modal-confirm-warning .ant-modal-confirm-body>.anticon {
    color: #faad14;
  }

  .ant-modal-confirm-info .ant-modal-confirm-body>.anticon {
    color: #1890ff;
  }

  .ant-modal-confirm-success .ant-modal-confirm-body>.anticon {
    color: #52c41a;
  }

  .le5le-topology .topology-panel[data-v-8d951cee] {
    padding: 12px;
  }

  .le5le-topology .topology-panel .caption[data-v-8d951cee] {
    display: flex;
    margin: 8px 0;
    align-items: center;
  }

  .le5le-topology .topology-panel .caption i[data-v-8d951cee] {
    font-size: 12px;
  }

  .le5le-topology .topology-panel .caption .hover[data-v-8d951cee] {
    flex-grow: 1;
    line-height: 1;
    display: flex;
    align-items: center;
  }

  .le5le-topology .topology-panel .caption .hover .full[data-v-8d951cee] {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.2;
    width: 100px !important;
    flex-grow: 1;
  }

  .le5le-topology .topology-panel .caption .hover input[data-v-8d951cee] {
    height: auto;
    border-radius: 2px;
    padding: 0 4px;
    margin-right: 8px;
  }

  .le5le-topology .topology-panel .caption .t-delete[data-v-8d951cee] {
    display: none;
    margin-right: 8px;
  }

  .le5le-topology .topology-panel .caption:hover .t-delete[data-v-8d951cee] {
    display: inline-block;
  }

  .le5le-topology .topology-panel .caption .t-file-add[data-v-8d951cee] {
    display: none;
    margin-right: 8px;
  }

  .le5le-topology .topology-panel .caption:hover .t-file-add[data-v-8d951cee] {
    display: inline-block;
  }

  .le5le-topology .topology-panel .files[data-v-8d951cee] {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .le5le-topology .topology-panel .files .thumb[data-v-8d951cee] {
    margin-top: 8px;
    width: 48%;
  }

  .le5le-topology .topology-panel .files .thumb>div[data-v-8d951cee] {
    width: 100%;
  }

  .le5le-topology .topology-panel .files .thumb .image[data-v-8d951cee] {
    padding: 5px;
    border-radius: 2px;
    border: 1px solid #f4f4f4;
  }

  .le5le-topology .topology-panel .files .thumb img[data-v-8d951cee] {
    height: 64px;
  }

  .le5le-topology .topology-panel .files .thumb:hover .image[data-v-8d951cee] {
    border-color: #1890ff;
  }

  .le5le-topology .topology-panel .files .thumb:hover .t-delete[data-v-8d951cee] {
    display: block;
  }

  .le5le-topology .topology-panel .files .thumb .t-delete[data-v-8d951cee] {
    position: absolute;
    right: 4px;
    top: 4px;
    display: none;
    color: #1890ff;
    cursor: pointer;
  }

  .le5le-topology .topology-panel .files .thumb .text[data-v-8d951cee] {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 4px 0;
  }

  .le5le-topology .topology-panel .files .list[data-v-8d951cee] {
    width: 100%;
    margin: 4px 0;
  }

  .le5le-topology .topology-panel .files .list>div[data-v-8d951cee] {
    display: flex;
    align-items: center;
    width: 100%;
    text-align: left;
  }

  .le5le-topology .topology-panel .files .list img[data-v-8d951cee] {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .le5le-topology .topology-panel .files .list .text[data-v-8d951cee] {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1;
  }

  .le5le-topology .system-panel[data-v-335b6a26] {
    padding: 12px;
  }

  .le5le-topology .system-panel .flex.mb8.active[data-v-335b6a26] {
    color: #fb8501;
  }

  .le5le-topology .system-panel .flex[data-v-335b6a26] {
    align-items: center;
    justify-content: space-between;
  }

  .le5le-topology .system-panel .flex i[data-v-335b6a26] {
    font-size: 12px;
  }

  .le5le-topology .system-panel .name[data-v-335b6a26] {
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 88px;
  }

  .le5le-topology .system-panel .thumbs[data-v-335b6a26] {
    display: flex;
    flex-wrap: wrap;
    margin-left: 8px;
  }

  .le5le-topology .system-panel .thumbs .thumb[data-v-335b6a26] {
    width: 25%;
  }

  .le5le-topology .system-panel .thumbs .thumb>div[data-v-335b6a26] {
    width: 100%;
  }

  .le5le-topology .system-panel .thumbs .thumb .icon-size[data-v-335b6a26] {
    font-size: 25px !important;
    width: 25px;
  }

  .le5le-topology .system-panel .thumbs .thumb img[data-v-335b6a26] {
    max-width: 25px;
    max-height: 25px;
    margin: 4px;
  }

  .le5le-topology .system-panel .thumbs .thumb:hover img[data-v-335b6a26] {
    background: #f8f8f8;
  }

  .le5le-topology .system-panel .more-graphical[data-v-335b6a26] {
    width: 200px;
    height: 54px;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    left: 0;
    bottom: 0;
    border-top: 1px solid #e5e5e5;
    border-right: 1px solid #e5e5e5;
    background: #fff;
  }

  .le5le-topology .system-panel .more-graphical .btn-more[data-v-335b6a26] {
    width: 170px;
    height: 30px;
    border-radius: 1;
    color: #434343;
  }

  .le5le-topology .system-panel .more-graphical .btn-more[data-v-335b6a26]:hover {
    border: 1px solid #1d93ff !important;
    color: #434343;
  }

  .le5le-topology .user-panel[data-v-544447cd] {
    padding: 12px;
  }

  .le5le-topology .user-panel .caption[data-v-544447cd] {
    display: flex;
    flex-wrap: wrap;
    margin: 8px 0;
    align-items: center;
  }

  .le5le-topology .user-panel .caption i[data-v-544447cd] {
    font-size: 12px;
  }

  .le5le-topology .user-panel .caption .ant-btn-primary[data-v-544447cd] {
    padding: 0;
    background: none;
    color: #1890ff;
    font-size: 13px;
    border: none !important;
    text-shadow: none !important;
    box-shadow: none !important;
  }

  .le5le-topology .user-panel .caption .ant-btn-primary[data-v-544447cd]:hover {
    color: #096dd9;
  }

  .le5le-topology .user-panel .caption .hover[data-v-544447cd] {
    flex-grow: 1;
    line-height: 1;
    display: flex;
    align-items: center;
  }

  .le5le-topology .user-panel .caption .hover .full[data-v-544447cd] {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.2;
    width: 70px !important;
    flex-grow: 1;
  }

  .le5le-topology .user-panel .caption .hover input[data-v-544447cd] {
    height: auto;
    border-radius: 2px;
    padding: 0 4px;
    margin-right: 8px;
  }

  .le5le-topology .user-panel .caption .t-delete[data-v-544447cd] {
    display: none;
    margin-right: 8px;
  }

  .le5le-topology .user-panel .caption:hover .t-delete[data-v-544447cd] {
    display: inline-block;
  }

  .le5le-topology .user-panel .caption .t-file-add[data-v-544447cd] {
    display: none;
    margin-right: 8px;
  }

  .le5le-topology .user-panel .caption:hover .t-file-add[data-v-544447cd] {
    display: inline-block;
  }

  .le5le-topology .user-panel .caption .t-image_upload[data-v-544447cd] {
    display: none;
    margin-right: 8px;
  }

  .le5le-topology .user-panel .caption:hover .t-image_upload[data-v-544447cd] {
    display: inline-block;
  }

  .le5le-topology .user-panel .files[data-v-544447cd] {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .le5le-topology .user-panel .files .thumb[data-v-544447cd] {
    margin-top: 8px;
    width: 48%;
    cursor: pointer;
  }

  .le5le-topology .user-panel .files .thumb>div[data-v-544447cd] {
    width: 100%;
  }

  .le5le-topology .user-panel .files .thumb .image[data-v-544447cd] {
    padding: 5px;
    border-radius: 2px;
    border: 1px solid #f4f4f4;
  }

  .le5le-topology .user-panel .files .thumb img[data-v-544447cd] {
    height: 64px;
  }

  .le5le-topology .user-panel .files .thumb:hover .image[data-v-544447cd] {
    border-color: #1890ff;
  }

  .le5le-topology .user-panel .files .thumb:hover .t-delete[data-v-544447cd],
  .le5le-topology .user-panel .files .thumb:hover .t-edit[data-v-544447cd] {
    display: block;
  }

  .le5le-topology .user-panel .files .thumb .text[data-v-544447cd] {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 4px 0;
  }

  .le5le-topology .user-panel .files .thumb .t-edit[data-v-544447cd] {
    position: absolute;
    right: 24px;
    top: 4px;
    display: none;
    color: #1890ff;
    cursor: pointer;
  }

  .le5le-topology .user-panel .files .thumb .t-delete[data-v-544447cd] {
    position: absolute;
    right: 4px;
    top: 4px;
    display: none;
    color: #1890ff;
    cursor: pointer;
  }

  .le5le-topology .user-panel .caption .hover .ant-upload {
    color: inherit !important;
  }

  .le5le-topology .materials {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    height: 100%;
    width: 200px;
    border-right: 1px solid #ddd;
    position: relative;
  }

  .le5le-topology .materials .search {
    position: relative;
    width: 199px;
    min-height: 40px;
    margin-top: 10px;
    margin-bottom: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .le5le-topology .materials .search .ant-input {
    font-size: 14px;
    height: 100%;
    width: 180px;
    background: #f3f3f3 !important;
    border-radius: 2px;
    padding-left: 32px;
    border: none;
    outline: 1px solid transparent;
  }

  .le5le-topology .materials .search .ant-input:hover {
    outline: 1px solid #1890ff;
  }

  .le5le-topology .materials .search .t-search {
    position: absolute;
    font-size: 14px;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #d1d1d1;
  }

  .le5le-topology .materials .navs {
    width: 199px;
  }

  .le5le-topology .materials .navs .nav-tab {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #d9d9d9;
    cursor: pointer;
  }

  .le5le-topology .materials .navs .nav-tab .submenu-title-wrapper {
    color: #999;
    font-size: 12px;
    color: #262626;
  }

  .le5le-topology .materials .navs .nav-tab .submenu-title-wrapper:hover {
    color: #1890ff;
  }

  .le5le-topology .materials .navs .nav-tab .submenu-title-wrapper {
    border-bottom: 2px solid transparent;
  }

  .le5le-topology .materials .navs .nav-tab .active {
    color: #1890ff;
    border-bottom: 2px solid #1890ff;
  }

  .le5le-topology .materials .panels {
    overflow-y: scroll;
    height: calc(100% - 87px);
  }

  .le5le-topology .materials .panels.pb40 {
    padding-bottom: 40px;
  }

  .le5le-topology .context-menus {
    color: #000;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    text-align: left;
    padding: 8px 0;
  }

  .le5le-topology .context-menus>div {
    line-height: 30px;
    padding: 0 16px;
  }

  .le5le-topology .context-menus>div:hover {
    color: #fff;
    background-color: #1890ff;
  }

  .le5le-topology .context-menus>div a {
    display: block;
    color: #333;
    font-size: 12px;
    text-decoration: none;
    cursor: pointer;
  }

  .le5le-topology .context-menus>div a.flex {
    display: flex;
  }

  .le5le-topology .context-menus>div a.disabled {
    color: #ccc;
    cursor: default;
  }

  .le5le-topology .context-menus>div a:hover {
    color: #fff;
  }

  .le5le-topology .context-menus .line {
    display: block;
    line-height: 1;
    margin: 4px 16px;
    border-top: 1px solid #e5e5e5;
  }

  .le5le-topology .canvas {
    min-height: 100%;
  }

  .le5le-topology .canvas div[id$=-canvas] {
    width: 100%;
    height: 100%;
    overflow: hidden !important;
    background: center // background-color: #fafafa
  }

  .le5le-topology .canvas div[id$=-canvas] .set-text-input {
    text-align: left;
    font-size: 12px;
    width: 100%;
    padding: 0 8px;
    border-radius: 2px;
    box-shadow: none;
    transition: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .le5le-topology .canvas div[id$=-canvas] .set-text-input:hover {
    border-color: #40a9ff !important;
    border-right-width: 1px !important;
  }

  .le5le-topology .context-menu {
    position: fixed;
    z-index: 9999;
    background: #fff;
    min-width: 200px;
  }

  .le5le-topology .context-menu a {
    color: #434343;
  }

  .le5le-topology .context-menu .divider {
    height: 1px;
    background-color: #ddd;
    margin: 5px 0;
  }

  @-webkit-keyframes antCheckboxEffect {
    0% {
      transform: scale(1);
      opacity: 0.5;
    }

    to {
      transform: scale(1.6);
      opacity: 0;
    }
  }

  @keyframes antCheckboxEffect {
    0% {
      transform: scale(1);
      opacity: 0.5;
    }

    to {
      transform: scale(1.6);
      opacity: 0;
    }
  }

  .ant-checkbox {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    top: -0.09em;
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    vertical-align: middle;
    outline: none;
    cursor: pointer;
  }

  .ant-checkbox-input:focus+.ant-checkbox-inner,
  .ant-checkbox-wrapper:hover .ant-checkbox-inner,
  .ant-checkbox:hover .ant-checkbox-inner {
    border-color: #1890ff;
  }

  .ant-checkbox-checked::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #1890ff;
    border-radius: 2px;
    visibility: hidden;
    -webkit-animation: antcheckboxeffect 0.36s ease-in-out;
    animation: antCheckboxEffect 0.36s ease-in-out;
    -webkit-animation-fill-mode: backwards;
    animation-fill-mode: backwards;
    content: "";
  }

  .ant-checkbox-wrapper:hover .ant-checkbox::after,
  .ant-checkbox:hover::after {
    visibility: visible;
  }

  .ant-checkbox-inner {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: 16px;
    height: 16px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    border-collapse: separate;
    transition: all 0.3s;
  }

  .ant-checkbox-inner::after {
    position: absolute;
    top: 50%;
    left: 22%;
    display: table;
    width: 5.71428571px;
    height: 9.14285714px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(0) translate(-50%, -50%);
    opacity: 0;
    transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
    content: " ";
  }

  .ant-checkbox-input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
  }

  .ant-checkbox-checked .ant-checkbox-inner::after {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: " ";
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-checkbox-disabled {
    cursor: not-allowed;
  }

  .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: rgba(0, 0, 0, 0.25);
    -webkit-animation-name: none;
    animation-name: none;
  }

  .ant-checkbox-disabled .ant-checkbox-input {
    cursor: not-allowed;
  }

  .ant-checkbox-disabled .ant-checkbox-inner {
    background-color: #f5f5f5;
    border-color: #d9d9d9 !important;
  }

  .ant-checkbox-disabled .ant-checkbox-inner::after {
    border-color: #f5f5f5;
    border-collapse: separate;
    -webkit-animation-name: none;
    animation-name: none;
  }

  .ant-checkbox-disabled+span {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  .ant-checkbox-disabled:hover::after,
  .ant-checkbox-wrapper:hover .ant-checkbox-disabled::after {
    visibility: hidden;
  }

  .ant-checkbox-wrapper {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    display: inline-block;
    line-height: unset;
    cursor: pointer;
  }

  .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled {
    cursor: not-allowed;
  }

  .ant-checkbox-wrapper+.ant-checkbox-wrapper {
    margin-left: 8px;
  }

  .ant-checkbox+span {
    padding-right: 8px;
    padding-left: 8px;
  }

  .ant-checkbox-group {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    display: inline-block;
  }

  .ant-checkbox-group-item {
    display: inline-block;
    margin-right: 8px;
  }

  .ant-checkbox-group-item:last-child {
    margin-right: 0;
  }

  .ant-checkbox-group-item+.ant-checkbox-group-item {
    margin-left: 0;
  }

  .ant-checkbox-indeterminate .ant-checkbox-inner {
    background-color: #fff;
    border-color: #d9d9d9;
  }

  .ant-checkbox-indeterminate .ant-checkbox-inner::after {
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background-color: #1890ff;
    border: 0;
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    content: " ";
  }

  .ant-checkbox-indeterminate.ant-checkbox-disabled .ant-checkbox-inner::after {
    background-color: rgba(0, 0, 0, 0.25);
    border-color: rgba(0, 0, 0, 0.25);
  }

  .ant-select {
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    outline: 0;
  }

  .ant-select,
  .ant-select ol,
  .ant-select ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .ant-select>ul>li>a {
    padding: 0;
    background-color: #fff;
  }

  .ant-select-arrow {
    display: inline-block;
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-align: center;
    text-transform: none;
    vertical-align: -0.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 50%;
    right: 11px;
    margin-top: -6px;
    color: rgba(0, 0, 0, 0.25);
    font-size: 12px;
    line-height: 1;
    transform-origin: 50% 50%;
  }

  .ant-select-arrow>* {
    line-height: 1;
  }

  .ant-select-arrow svg {
    display: inline-block;
  }

  .ant-select-arrow::before {
    display: none;
  }

  .ant-select-arrow .ant-select-arrow-icon {
    display: block;
  }

  .ant-select-arrow .ant-select-arrow-icon svg {
    transition: transform 0.3s;
  }

  .ant-select-selection {
    display: block;
    box-sizing: border-box;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-top-width: 1.02px;
    border-radius: 4px;
    outline: none;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .ant-select-selection:hover {
    border-color: #40a9ff;
    border-right-width: 1px !important;
  }

  .ant-select-focused .ant-select-selection,
  .ant-select-selection:active,
  .ant-select-selection:focus {
    border-color: #40a9ff;
    border-right-width: 1px !important;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .ant-select-selection__clear {
    position: absolute;
    top: 50%;
    right: 11px;
    z-index: 1;
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    color: rgba(0, 0, 0, 0.25);
    font-size: 12px;
    font-style: normal;
    line-height: 12px;
    text-align: center;
    text-transform: none;
    background: #fff;
    cursor: pointer;
    opacity: 0;
    transition: color 0.3s ease, opacity 0.15s ease;
    text-rendering: auto;
  }

  .ant-select-selection__clear::before {
    display: block;
  }

  .ant-select-selection__clear:hover {
    color: rgba(0, 0, 0, 0.45);
  }

  .ant-select-selection:hover .ant-select-selection__clear {
    opacity: 1;
  }

  .ant-select-selection-selected-value {
    float: left;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .ant-select-no-arrow .ant-select-selection-selected-value {
    padding-right: 0;
  }

  .ant-select-disabled {
    color: rgba(0, 0, 0, 0.25);
  }

  .ant-select-disabled .ant-select-selection {
    background: #f5f5f5;
    cursor: not-allowed;
  }

  .ant-select-disabled .ant-select-selection:active,
  .ant-select-disabled .ant-select-selection:focus,
  .ant-select-disabled .ant-select-selection:hover {
    border-color: #d9d9d9;
    box-shadow: none;
  }

  .ant-select-disabled .ant-select-selection__clear {
    display: none;
    visibility: hidden;
    pointer-events: none;
  }

  .ant-select-disabled .ant-select-selection--multiple .ant-select-selection__choice {
    padding-right: 10px;
    color: rgba(0, 0, 0, 0.33);
    background: #f5f5f5;
  }

  .ant-select-disabled .ant-select-selection--multiple .ant-select-selection__choice__remove {
    display: none;
  }

  .ant-select-selection--single {
    position: relative;
    height: 32px;
    cursor: pointer;
  }

  .ant-select-selection--single .ant-select-selection__rendered {
    margin-right: 24px;
  }

  .ant-select-no-arrow .ant-select-selection__rendered {
    margin-right: 11px;
  }

  .ant-select-selection__rendered {
    position: relative;
    display: block;
    margin-right: 11px;
    margin-left: 11px;
    line-height: 30px;
  }

  .ant-select-selection__rendered::after {
    display: inline-block;
    width: 0;
    visibility: hidden;
    content: ".";
    pointer-events: none;
  }

  .ant-select-lg {
    font-size: 16px;
  }

  .ant-select-lg .ant-select-selection--single {
    height: 40px;
  }

  .ant-select-lg .ant-select-selection__rendered {
    line-height: 38px;
  }

  .ant-select-lg .ant-select-selection--multiple {
    min-height: 40px;
  }

  .ant-select-lg .ant-select-selection--multiple .ant-select-selection__rendered li {
    height: 32px;
    line-height: 32px;
  }

  .ant-select-lg .ant-select-selection--multiple .ant-select-arrow,
  .ant-select-lg .ant-select-selection--multiple .ant-select-selection__clear {
    top: 20px;
  }

  .ant-select-sm .ant-select-selection--single {
    height: 24px;
  }

  .ant-select-sm .ant-select-selection__rendered {
    margin-left: 7px;
    line-height: 22px;
  }

  .ant-select-sm .ant-select-selection--multiple {
    min-height: 24px;
  }

  .ant-select-sm .ant-select-selection--multiple .ant-select-selection__rendered li {
    height: 16px;
    line-height: 14px;
  }

  .ant-select-sm .ant-select-selection--multiple .ant-select-arrow,
  .ant-select-sm .ant-select-selection--multiple .ant-select-selection__clear {
    top: 12px;
  }

  .ant-select-sm .ant-select-arrow,
  .ant-select-sm .ant-select-selection__clear {
    right: 8px;
  }

  .ant-select-disabled .ant-select-selection__choice__remove {
    color: rgba(0, 0, 0, 0.25);
    cursor: default;
  }

  .ant-select-disabled .ant-select-selection__choice__remove:hover {
    color: rgba(0, 0, 0, 0.25);
  }

  .ant-select-search__field__wrap {
    position: relative;
    display: inline-block;
  }

  .ant-select-search__field__placeholder,
  .ant-select-selection__placeholder {
    position: absolute;
    top: 50%;
    right: 9px;
    left: 0;
    max-width: 100%;
    height: 20px;
    margin-top: -10px;
    overflow: hidden;
    color: #bfbfbf;
    line-height: 20px;
    white-space: nowrap;
    text-align: left;
    text-overflow: ellipsis;
  }

  .ant-select-search__field__placeholder {
    left: 12px;
  }

  .ant-select-search__field__mirror {
    position: absolute;
    top: 0;
    left: 0;
    white-space: pre;
    opacity: 0;
    pointer-events: none;
  }

  .ant-select-search--inline {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .ant-select-search--inline .ant-select-search__field__wrap {
    width: 100%;
    height: 100%;
  }

  .ant-select-search--inline .ant-select-search__field {
    width: 100%;
    height: 100%;
    font-size: 100%;
    line-height: 1;
    background: transparent;
    border-width: 0;
    border-radius: 4px;
    outline: 0;
  }

  .ant-select-search--inline>i {
    float: right;
  }

  .ant-select-selection--multiple {
    min-height: 32px;
    padding-bottom: 3px;
    cursor: text;
    zoom: 1;
  }

  .ant-select-selection--multiple::after,
  .ant-select-selection--multiple::before {
    display: table;
    content: "";
  }

  .ant-select-selection--multiple::after {
    clear: both;
  }

  .ant-select-selection--multiple .ant-select-search--inline {
    position: static;
    float: left;
    width: auto;
    max-width: 100%;
    padding: 0;
  }

  .ant-select-selection--multiple .ant-select-search--inline .ant-select-search__field {
    width: 0.75em;
    max-width: 100%;
    padding: 1px;
  }

  .ant-select-selection--multiple .ant-select-selection__rendered {
    height: auto;
    margin-bottom: -3px;
    margin-left: 5px;
  }

  .ant-select-selection--multiple .ant-select-selection__placeholder {
    margin-left: 6px;
  }

  .ant-select-selection--multiple .ant-select-selection__rendered>ul>li,
  .ant-select-selection--multiple>ul>li {
    height: 24px;
    margin-top: 3px;
    line-height: 22px;
  }

  .ant-select-selection--multiple .ant-select-selection__choice {
    position: relative;
    float: left;
    max-width: 99%;
    margin-right: 4px;
    padding: 0 20px 0 10px;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.65);
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    cursor: default;
    transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-select-selection--multiple .ant-select-selection__choice__disabled {
    padding: 0 10px;
  }

  .ant-select-selection--multiple .ant-select-selection__choice__content {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    transition: margin 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-select-selection--multiple .ant-select-selection__choice__remove {
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-align: center;
    text-transform: none;
    vertical-align: -0.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    right: 4px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 700;
    line-height: inherit;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-block;
    font-size: 12px;
    font-size: 10px\9;
    transform: scale(0.83333333) rotate(0deg);
  }

  .ant-select-selection--multiple .ant-select-selection__choice__remove>* {
    line-height: 1;
  }

  .ant-select-selection--multiple .ant-select-selection__choice__remove svg {
    display: inline-block;
  }

  .ant-select-selection--multiple .ant-select-selection__choice__remove::before {
    display: none;
  }

  .ant-select-selection--multiple .ant-select-selection__choice__remove .ant-select-selection--multiple .ant-select-selection__choice__remove-icon {
    display: block;
  }

  :root .ant-select-selection--multiple .ant-select-selection__choice__remove {
    font-size: 12px;
  }

  .ant-select-selection--multiple .ant-select-selection__choice__remove:hover {
    color: rgba(0, 0, 0, 0.75);
  }

  .ant-select-selection--multiple .ant-select-arrow,
  .ant-select-selection--multiple .ant-select-selection__clear {
    top: 16px;
  }

  .ant-select-allow-clear .ant-select-selection--multiple .ant-select-selection__rendered,
  .ant-select-show-arrow .ant-select-selection--multiple .ant-select-selection__rendered {
    margin-right: 20px;
  }

  .ant-select-open .ant-select-arrow-icon svg {
    transform: rotate(180deg);
  }

  .ant-select-open .ant-select-selection {
    border-color: #40a9ff;
    border-right-width: 1px !important;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .ant-select-combobox .ant-select-arrow {
    display: none;
  }

  .ant-select-combobox .ant-select-search--inline {
    float: none;
    width: 100%;
    height: 100%;
  }

  .ant-select-combobox .ant-select-search__field__wrap {
    width: 100%;
    height: 100%;
  }

  .ant-select-combobox .ant-select-search__field {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
    box-shadow: none;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), height 0s;
  }

  .ant-select-combobox.ant-select-allow-clear .ant-select-selection:hover .ant-select-selection__rendered,
  .ant-select-combobox.ant-select-show-arrow .ant-select-selection:hover .ant-select-selection__rendered {
    margin-right: 20px;
  }

  .ant-select-dropdown {
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: absolute;
    top: -9999px;
    left: -9999px;
    z-index: 1050;
    box-sizing: border-box;
    font-size: 14px;
    font-variant: normal;
    background-color: #fff;
    border-radius: 4px;
    outline: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .ant-select-dropdown.slide-up-appear.slide-up-appear-active.ant-select-dropdown-placement-bottomLeft,
  .ant-select-dropdown.slide-up-enter.slide-up-enter-active.ant-select-dropdown-placement-bottomLeft {
    -webkit-animation-name: antslideupin;
    animation-name: antSlideUpIn;
  }

  .ant-select-dropdown.slide-up-appear.slide-up-appear-active.ant-select-dropdown-placement-topLeft,
  .ant-select-dropdown.slide-up-enter.slide-up-enter-active.ant-select-dropdown-placement-topLeft {
    -webkit-animation-name: antslidedownin;
    animation-name: antSlideDownIn;
  }

  .ant-select-dropdown.slide-up-leave.slide-up-leave-active.ant-select-dropdown-placement-bottomLeft {
    -webkit-animation-name: antslideupout;
    animation-name: antSlideUpOut;
  }

  .ant-select-dropdown.slide-up-leave.slide-up-leave-active.ant-select-dropdown-placement-topLeft {
    -webkit-animation-name: antslidedownout;
    animation-name: antSlideDownOut;
  }

  .ant-select-dropdown-hidden {
    display: none;
  }

  .ant-select-dropdown-menu {
    max-height: 250px;
    margin-bottom: 0;
    padding: 4px 0;
    padding-left: 0;
    overflow: auto;
    list-style: none;
    outline: none;
  }

  .ant-select-dropdown-menu-item-group-list {
    margin: 0;
    padding: 0;
  }

  .ant-select-dropdown-menu-item-group-list>.ant-select-dropdown-menu-item {
    padding-left: 20px;
  }

  .ant-select-dropdown-menu-item-group-title {
    height: 32px;
    padding: 0 12px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    line-height: 32px;
  }

  .ant-select-dropdown-menu-item-group-list .ant-select-dropdown-menu-item:first-child:not(:last-child),
  .ant-select-dropdown-menu-item-group:not(:last-child) .ant-select-dropdown-menu-item-group-list .ant-select-dropdown-menu-item:last-child {
    border-radius: 0;
  }

  .ant-select-dropdown-menu-item {
    position: relative;
    display: block;
    padding: 5px 12px;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    transition: background 0.3s ease;
  }

  .ant-select-dropdown-menu-item:hover:not(.ant-select-dropdown-menu-item-disabled) {
    background-color: #e6f7ff;
  }

  .ant-select-dropdown-menu-item-selected {
    color: rgba(0, 0, 0, 0.65);
    font-weight: 600;
    background-color: #fafafa;
  }

  .ant-select-dropdown-menu-item-disabled,
  .ant-select-dropdown-menu-item-disabled:hover {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  .ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
    background-color: #e6f7ff;
  }

  .ant-select-dropdown-menu-item-divider {
    height: 1px;
    margin: 1px 0;
    overflow: hidden;
    line-height: 0;
    background-color: #e8e8e8;
  }

  .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item {
    padding-right: 32px;
  }

  .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item .ant-select-selected-icon {
    position: absolute;
    top: 50%;
    right: 12px;
    color: transparent;
    font-weight: 700;
    font-size: 12px;
    text-shadow: 0 0.1px 0, 0.1px 0 0, 0 -0.1px 0, -0.1px 0;
    transform: translateY(-50%);
    transition: all 0.2s;
  }

  .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item:hover .ant-select-selected-icon {
    color: rgba(0, 0, 0, 0.87);
  }

  .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-disabled .ant-select-selected-icon {
    display: none;
  }

  .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon,
  .ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected:hover .ant-select-selected-icon {
    display: inline-block;
    color: #1890ff;
  }

  .ant-select-dropdown--empty.ant-select-dropdown--multiple .ant-select-dropdown-menu-item {
    padding-right: 12px;
  }

  .ant-select-dropdown-container-open .ant-select-dropdown,
  .ant-select-dropdown-open .ant-select-dropdown {
    display: block;
  }

  .ant-empty {
    margin: 0 8px;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
  }

  .ant-empty-image {
    height: 100px;
    margin-bottom: 8px;
  }

  .ant-empty-image img {
    height: 100%;
  }

  .ant-empty-image svg {
    height: 100%;
    margin: auto;
  }

  .ant-empty-description {
    margin: 0;
  }

  .ant-empty-footer {
    margin-top: 16px;
  }

  .ant-empty-normal {
    margin: 32px 0;
    color: rgba(0, 0, 0, 0.25);
  }

  .ant-empty-normal .ant-empty-image {
    height: 40px;
  }

  .ant-empty-small {
    margin: 8px 0;
    color: rgba(0, 0, 0, 0.25);
  }

  .ant-empty-small .ant-empty-image {
    height: 35px;
  }

  .ant-upload {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    outline: 0;
  }

  .ant-upload p {
    margin: 0;
  }

  .ant-upload-btn {
    display: block;
    width: 100%;
    outline: none;
  }

  .ant-upload input[type=file] {
    cursor: pointer;
  }

  .ant-upload.ant-upload-select {
    display: inline-block;
  }

  .ant-upload.ant-upload-disabled {
    cursor: not-allowed;
  }

  .ant-upload.ant-upload-select-picture-card {
    display: table;
    float: left;
    width: 104px;
    height: 104px;
    margin-right: 8px;
    margin-bottom: 8px;
    text-align: center;
    vertical-align: top;
    background-color: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: border-color 0.3s ease;
  }

  .ant-upload.ant-upload-select-picture-card>.ant-upload {
    display: table-cell;
    width: 100%;
    height: 100%;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
  }

  .ant-upload.ant-upload-select-picture-card:hover {
    border-color: #1890ff;
  }

  .ant-upload.ant-upload-drag {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: border-color 0.3s;
  }

  .ant-upload.ant-upload-drag .ant-upload {
    padding: 16px 0;
  }

  .ant-upload.ant-upload-drag.ant-upload-drag-hover:not(.ant-upload-disabled) {
    border-color: #096dd9;
  }

  .ant-upload.ant-upload-drag.ant-upload-disabled {
    cursor: not-allowed;
  }

  .ant-upload.ant-upload-drag .ant-upload-btn {
    display: table;
    height: 100%;
  }

  .ant-upload.ant-upload-drag .ant-upload-drag-container {
    display: table-cell;
    vertical-align: middle;
  }

  .ant-upload.ant-upload-drag:not(.ant-upload-disabled):hover {
    border-color: #40a9ff;
  }

  .ant-upload.ant-upload-drag p.ant-upload-drag-icon {
    margin-bottom: 20px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-drag-icon .anticon {
    color: #40a9ff;
    font-size: 48px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-text {
    margin: 0 0 4px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-hint {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }

  .ant-upload.ant-upload-drag .anticon-plus {
    color: rgba(0, 0, 0, 0.25);
    font-size: 30px;
    transition: all 0.3s;
  }

  .ant-upload.ant-upload-drag .anticon-plus:hover,
  .ant-upload.ant-upload-drag:hover .anticon-plus {
    color: rgba(0, 0, 0, 0.45);
  }

  .ant-upload-picture-card-wrapper {
    zoom: 1;
    display: inline-block;
    width: 100%;
  }

  .ant-upload-picture-card-wrapper::after,
  .ant-upload-picture-card-wrapper::before {
    display: table;
    content: "";
  }

  .ant-upload-picture-card-wrapper::after {
    clear: both;
  }

  .ant-upload-list {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    zoom: 1;
  }

  .ant-upload-list::after,
  .ant-upload-list::before {
    display: table;
    content: "";
  }

  .ant-upload-list::after {
    clear: both;
  }

  .ant-upload-list-item-list-type-text:hover .ant-upload-list-item-name-icon-count-1 {
    padding-right: 14px;
  }

  .ant-upload-list-item-list-type-text:hover .ant-upload-list-item-name-icon-count-2 {
    padding-right: 28px;
  }

  .ant-upload-list-item {
    position: relative;
    height: 22px;
    margin-top: 8px;
    font-size: 14px;
  }

  .ant-upload-list-item-name {
    display: inline-block;
    width: 100%;
    padding-left: 22px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .ant-upload-list-item-name-icon-count-1 {
    padding-right: 14px;
  }

  .ant-upload-list-item-card-actions {
    position: absolute;
    right: 0;
    opacity: 0;
  }

  .ant-upload-list-item-card-actions.picture {
    top: 25px;
    line-height: 1;
    opacity: 1;
  }

  .ant-upload-list-item-card-actions .anticon {
    padding-right: 6px;
    color: rgba(0, 0, 0, 0.45);
  }

  .ant-upload-list-item-info {
    height: 100%;
    padding: 0 12px 0 4px;
    transition: background-color 0.3s;
  }

  .ant-upload-list-item-info>span {
    display: block;
    width: 100%;
    height: 100%;
  }

  .ant-upload-list-item-info .anticon-loading,
  .ant-upload-list-item-info .anticon-paper-clip {
    position: absolute;
    top: 5px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }

  .ant-upload-list-item .anticon-close {
    display: inline-block;
    font-size: 12px;
    font-size: 10px\9;
    transform: scale(0.83333333) rotate(0deg);
    position: absolute;
    top: 6px;
    right: 4px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 0;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s;
  }

  :root .ant-upload-list-item .anticon-close {
    font-size: 12px;
  }

  .ant-upload-list-item .anticon-close:hover {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-upload-list-item:hover .ant-upload-list-item-info {
    background-color: #e6f7ff;
  }

  .ant-upload-list-item:hover .ant-upload-list-item-card-actions,
  .ant-upload-list-item:hover .anticon-close {
    opacity: 1;
  }

  .ant-upload-list-item-error,
  .ant-upload-list-item-error .ant-upload-list-item-name,
  .ant-upload-list-item-error .anticon-paper-clip {
    color: #f5222d;
  }

  .ant-upload-list-item-error .ant-upload-list-item-card-actions {
    opacity: 1;
  }

  .ant-upload-list-item-error .ant-upload-list-item-card-actions .anticon {
    color: #f5222d;
  }

  .ant-upload-list-item-progress {
    position: absolute;
    bottom: -12px;
    width: 100%;
    padding-left: 26px;
    font-size: 14px;
    line-height: 0;
  }

  .ant-upload-list-picture-card .ant-upload-list-item,
  .ant-upload-list-picture .ant-upload-list-item {
    position: relative;
    height: 66px;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }

  .ant-upload-list-picture-card .ant-upload-list-item:hover,
  .ant-upload-list-picture .ant-upload-list-item:hover {
    background: transparent;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-error,
  .ant-upload-list-picture .ant-upload-list-item-error {
    border-color: #f5222d;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-info,
  .ant-upload-list-picture .ant-upload-list-item-info {
    padding: 0;
  }

  .ant-upload-list-picture-card .ant-upload-list-item:hover .ant-upload-list-item-info,
  .ant-upload-list-picture .ant-upload-list-item:hover .ant-upload-list-item-info {
    background: transparent;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-uploading,
  .ant-upload-list-picture .ant-upload-list-item-uploading {
    border-style: dashed;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
  .ant-upload-list-picture .ant-upload-list-item-thumbnail {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 48px;
    height: 48px;
    font-size: 26px;
    line-height: 54px;
    text-align: center;
    opacity: 0.8;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-icon,
  .ant-upload-list-picture .ant-upload-list-item-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 26px;
    transform: translate(-50%, -50%);
  }

  .ant-upload-list-picture-card .ant-upload-list-item-image,
  .ant-upload-list-picture .ant-upload-list-item-image {
    max-width: 100%;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-thumbnail img,
  .ant-upload-list-picture .ant-upload-list-item-thumbnail img {
    display: block;
    width: 48px;
    height: 48px;
    overflow: hidden;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-name,
  .ant-upload-list-picture .ant-upload-list-item-name {
    display: inline-block;
    box-sizing: border-box;
    max-width: 100%;
    margin: 0 0 0 8px;
    padding-right: 8px;
    padding-left: 48px;
    overflow: hidden;
    line-height: 44px;
    white-space: nowrap;
    text-overflow: ellipsis;
    transition: all 0.3s;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-name-icon-count-1,
  .ant-upload-list-picture .ant-upload-list-item-name-icon-count-1 {
    padding-right: 18px;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-name-icon-count-2,
  .ant-upload-list-picture .ant-upload-list-item-name-icon-count-2 {
    padding-right: 36px;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-uploading .ant-upload-list-item-name,
  .ant-upload-list-picture .ant-upload-list-item-uploading .ant-upload-list-item-name {
    line-height: 28px;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-progress,
  .ant-upload-list-picture .ant-upload-list-item-progress {
    bottom: 14px;
    width: calc(100% - 24px);
    margin-top: 0;
    padding-left: 56px;
  }

  .ant-upload-list-picture-card .anticon-close,
  .ant-upload-list-picture .anticon-close {
    position: absolute;
    top: 8px;
    right: 8px;
    line-height: 1;
    opacity: 1;
  }

  .ant-upload-list-picture-card.ant-upload-list::after {
    display: none;
  }

  .ant-upload-list-picture-card-container,
  .ant-upload-list-picture-card .ant-upload-list-item {
    float: left;
    width: 104px;
    height: 104px;
    margin: 0 8px 8px 0;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-info {
    position: relative;
    height: 100%;
    overflow: hidden;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-info::before {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: all 0.3s;
    content: " ";
  }

  .ant-upload-list-picture-card .ant-upload-list-item:hover .ant-upload-list-item-info::before {
    opacity: 1;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-actions {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 10;
    white-space: nowrap;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.3s;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete,
  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-download,
  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye-o {
    z-index: 10;
    width: 16px;
    margin: 0 4px;
    color: hsla(0, 0%, 100%, 0.85);
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete:hover,
  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-download:hover,
  .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye-o:hover {
    color: #fff;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-actions:hover,
  .ant-upload-list-picture-card .ant-upload-list-item-info:hover+.ant-upload-list-item-actions {
    opacity: 1;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
  .ant-upload-list-picture-card .ant-upload-list-item-thumbnail img {
    position: static;
    display: block;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-name {
    display: none;
    margin: 8px 0 0;
    padding: 0;
    line-height: 1.5;
    text-align: center;
  }

  .ant-upload-list-picture-card .anticon-picture+.ant-upload-list-item-name {
    position: absolute;
    bottom: 10px;
    display: block;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-uploading.ant-upload-list-item {
    background-color: #fafafa;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-uploading .ant-upload-list-item-info {
    height: auto;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-uploading .ant-upload-list-item-info .anticon-delete,
  .ant-upload-list-picture-card .ant-upload-list-item-uploading .ant-upload-list-item-info .anticon-eye-o,
  .ant-upload-list-picture-card .ant-upload-list-item-uploading .ant-upload-list-item-info::before {
    display: none;
  }

  .ant-upload-list-picture-card .ant-upload-list-item-uploading-text {
    margin-top: 18px;
    color: rgba(0, 0, 0, 0.45);
  }

  .ant-upload-list-picture-card .ant-upload-list-item-progress {
    bottom: 32px;
    padding-left: 0;
  }

  .ant-upload-list .ant-upload-success-icon {
    color: #52c41a;
    font-weight: 700;
  }

  .ant-upload-list .ant-upload-animate-enter,
  .ant-upload-list .ant-upload-animate-inline-enter,
  .ant-upload-list .ant-upload-animate-inline-leave,
  .ant-upload-list .ant-upload-animate-leave {
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-fill-mode: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    animation-fill-mode: cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .ant-upload-list .ant-upload-animate-enter {
    -webkit-animation-name: uploadanimatein;
    animation-name: uploadAnimateIn;
  }

  .ant-upload-list .ant-upload-animate-leave {
    -webkit-animation-name: uploadanimateout;
    animation-name: uploadAnimateOut;
  }

  .ant-upload-list .ant-upload-animate-inline-enter {
    -webkit-animation-name: uploadanimateinlinein;
    animation-name: uploadAnimateInlineIn;
  }

  .ant-upload-list .ant-upload-animate-inline-leave {
    -webkit-animation-name: uploadanimateinlineout;
    animation-name: uploadAnimateInlineOut;
  }

  @-webkit-keyframes uploadAnimateIn {
    0% {
      height: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
    }
  }

  @keyframes uploadAnimateIn {
    0% {
      height: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes uploadAnimateOut {
    to {
      height: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
    }
  }

  @keyframes uploadAnimateOut {
    to {
      height: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes uploadAnimateInlineIn {
    0% {
      width: 0;
      height: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
    }
  }

  @keyframes uploadAnimateInlineIn {
    0% {
      width: 0;
      height: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
    }
  }

  @-webkit-keyframes uploadAnimateInlineOut {
    to {
      width: 0;
      height: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
    }
  }

  @keyframes uploadAnimateInlineOut {
    to {
      width: 0;
      height: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
    }
  }

  .ant-progress {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    display: inline-block;
  }

  .ant-progress-line {
    position: relative;
    width: 100%;
    font-size: 14px;
  }

  .ant-progress-small.ant-progress-line,
  .ant-progress-small.ant-progress-line .ant-progress-text .anticon {
    font-size: 12px;
  }

  .ant-progress-outer {
    display: inline-block;
    width: 100%;
    margin-right: 0;
    padding-right: 0;
  }

  .ant-progress-show-info .ant-progress-outer {
    margin-right: calc(-2em - 8px);
    padding-right: calc(2em + 8px);
  }

  .ant-progress-inner {
    position: relative;
    display: inline-block;
    width: 100%;
    overflow: hidden;
    vertical-align: middle;
    background-color: #f5f5f5;
    border-radius: 100px;
  }

  .ant-progress-circle-trail {
    stroke: #f5f5f5;
  }

  .ant-progress-circle-path {
    -webkit-animation: ant-progress-appear 0.3s;
    animation: ant-progress-appear 0.3s;
  }

  .ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {
    stroke: #1890ff;
  }

  .ant-progress-bg,
  .ant-progress-success-bg {
    position: relative;
    background-color: #1890ff;
    border-radius: 100px;
    transition: all 0.4s cubic-bezier(0.08, 0.82, 0.17, 1) 0s;
  }

  .ant-progress-success-bg {
    position: absolute;
    top: 0;
    left: 0;
    background-color: #52c41a;
  }

  .ant-progress-text {
    display: inline-block;
    width: 2em;
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 1em;
    line-height: 1;
    white-space: nowrap;
    text-align: left;
    vertical-align: middle;
    word-break: normal;
  }

  .ant-progress-text .anticon {
    font-size: 14px;
  }

  .ant-progress-status-active .ant-progress-bg::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #fff;
    border-radius: 10px;
    opacity: 0;
    -webkit-animation: ant-progress-active 2.4s cubic-bezier(0.23, 1, 0.32, 1) infinite;
    animation: ant-progress-active 2.4s cubic-bezier(0.23, 1, 0.32, 1) infinite;
    content: "";
  }

  .ant-progress-status-exception .ant-progress-bg {
    background-color: #f5222d;
  }

  .ant-progress-status-exception .ant-progress-text {
    color: #f5222d;
  }

  .ant-progress-status-exception .ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {
    stroke: #f5222d;
  }

  .ant-progress-status-success .ant-progress-bg {
    background-color: #52c41a;
  }

  .ant-progress-status-success .ant-progress-text {
    color: #52c41a;
  }

  .ant-progress-status-success .ant-progress-inner:not(.ant-progress-circle-gradient) .ant-progress-circle-path {
    stroke: #52c41a;
  }

  .ant-progress-circle .ant-progress-inner {
    position: relative;
    line-height: 1;
    background-color: transparent;
  }

  .ant-progress-circle .ant-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    line-height: 1;
    white-space: normal;
    text-align: center;
    transform: translate(-50%, -50%);
  }

  .ant-progress-circle .ant-progress-text .anticon {
    font-size: 1.16666667em;
  }

  .ant-progress-circle.ant-progress-status-exception .ant-progress-text {
    color: #f5222d;
  }

  .ant-progress-circle.ant-progress-status-success .ant-progress-text {
    color: #52c41a;
  }

  @-webkit-keyframes ant-progress-active {
    0% {
      width: 0;
      opacity: 0.1;
    }

    20% {
      width: 0;
      opacity: 0.5;
    }

    to {
      width: 100%;
      opacity: 0;
    }
  }

  @keyframes ant-progress-active {
    0% {
      width: 0;
      opacity: 0.1;
    }

    20% {
      width: 0;
      opacity: 0.5;
    }

    to {
      width: 100%;
      opacity: 0;
    }
  }

  .ant-tooltip {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: absolute;
    z-index: 1060;
    display: block;
    max-width: 250px;
    visibility: visible;
  }

  .ant-tooltip-hidden {
    display: none;
  }

  .ant-tooltip-placement-top,
  .ant-tooltip-placement-topLeft,
  .ant-tooltip-placement-topRight {
    padding-bottom: 8px;
  }

  .ant-tooltip-placement-right,
  .ant-tooltip-placement-rightBottom,
  .ant-tooltip-placement-rightTop {
    padding-left: 8px;
  }

  .ant-tooltip-placement-bottom,
  .ant-tooltip-placement-bottomLeft,
  .ant-tooltip-placement-bottomRight {
    padding-top: 8px;
  }

  .ant-tooltip-placement-left,
  .ant-tooltip-placement-leftBottom,
  .ant-tooltip-placement-leftTop {
    padding-right: 8px;
  }

  .ant-tooltip-inner {
    min-width: 30px;
    min-height: 32px;
    padding: 6px 8px;
    color: #fff;
    text-align: left;
    text-decoration: none;
    word-wrap: break-word;
    background-color: rgba(0, 0, 0, 0.75);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .ant-tooltip-arrow {
    position: absolute;
    display: block;
    width: 13.07106781px;
    height: 13.07106781px;
    overflow: hidden;
    background: transparent;
    pointer-events: none;
  }

  .ant-tooltip-arrow::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: block;
    width: 5px;
    height: 5px;
    margin: auto;
    background-color: rgba(0, 0, 0, 0.75);
    content: "";
    pointer-events: auto;
  }

  .ant-tooltip-placement-top .ant-tooltip-arrow,
  .ant-tooltip-placement-topLeft .ant-tooltip-arrow,
  .ant-tooltip-placement-topRight .ant-tooltip-arrow {
    bottom: -5.07106781px;
  }

  .ant-tooltip-placement-top .ant-tooltip-arrow::before,
  .ant-tooltip-placement-topLeft .ant-tooltip-arrow::before,
  .ant-tooltip-placement-topRight .ant-tooltip-arrow::before {
    box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
    transform: translateY(-6.53553391px) rotate(45deg);
  }

  .ant-tooltip-placement-top .ant-tooltip-arrow {
    left: 50%;
    transform: translateX(-50%);
  }

  .ant-tooltip-placement-topLeft .ant-tooltip-arrow {
    left: 13px;
  }

  .ant-tooltip-placement-topRight .ant-tooltip-arrow {
    right: 13px;
  }

  .ant-tooltip-placement-right .ant-tooltip-arrow,
  .ant-tooltip-placement-rightBottom .ant-tooltip-arrow,
  .ant-tooltip-placement-rightTop .ant-tooltip-arrow {
    left: -5.07106781px;
  }

  .ant-tooltip-placement-right .ant-tooltip-arrow::before,
  .ant-tooltip-placement-rightBottom .ant-tooltip-arrow::before,
  .ant-tooltip-placement-rightTop .ant-tooltip-arrow::before {
    box-shadow: -3px 3px 7px rgba(0, 0, 0, 0.07);
    transform: translateX(6.53553391px) rotate(45deg);
  }

  .ant-tooltip-placement-right .ant-tooltip-arrow {
    top: 50%;
    transform: translateY(-50%);
  }

  .ant-tooltip-placement-rightTop .ant-tooltip-arrow {
    top: 5px;
  }

  .ant-tooltip-placement-rightBottom .ant-tooltip-arrow {
    bottom: 5px;
  }

  .ant-tooltip-placement-left .ant-tooltip-arrow,
  .ant-tooltip-placement-leftBottom .ant-tooltip-arrow,
  .ant-tooltip-placement-leftTop .ant-tooltip-arrow {
    right: -5.07106781px;
  }

  .ant-tooltip-placement-left .ant-tooltip-arrow::before,
  .ant-tooltip-placement-leftBottom .ant-tooltip-arrow::before,
  .ant-tooltip-placement-leftTop .ant-tooltip-arrow::before {
    box-shadow: 3px -3px 7px rgba(0, 0, 0, 0.07);
    transform: translateX(-6.53553391px) rotate(45deg);
  }

  .ant-tooltip-placement-left .ant-tooltip-arrow {
    top: 50%;
    transform: translateY(-50%);
  }

  .ant-tooltip-placement-leftTop .ant-tooltip-arrow {
    top: 5px;
  }

  .ant-tooltip-placement-leftBottom .ant-tooltip-arrow {
    bottom: 5px;
  }

  .ant-tooltip-placement-bottom .ant-tooltip-arrow,
  .ant-tooltip-placement-bottomLeft .ant-tooltip-arrow,
  .ant-tooltip-placement-bottomRight .ant-tooltip-arrow {
    top: -5.07106781px;
  }

  .ant-tooltip-placement-bottom .ant-tooltip-arrow::before,
  .ant-tooltip-placement-bottomLeft .ant-tooltip-arrow::before,
  .ant-tooltip-placement-bottomRight .ant-tooltip-arrow::before {
    box-shadow: -3px -3px 7px rgba(0, 0, 0, 0.07);
    transform: translateY(6.53553391px) rotate(45deg);
  }

  .ant-tooltip-placement-bottom .ant-tooltip-arrow {
    left: 50%;
    transform: translateX(-50%);
  }

  .ant-tooltip-placement-bottomLeft .ant-tooltip-arrow {
    left: 13px;
  }

  .ant-tooltip-placement-bottomRight .ant-tooltip-arrow {
    right: 13px;
  }

  .le5le-topology .image-panel .content .buttons[data-v-01e0bf19] {
    display: flex;
    justify-content: space-around;
    padding: 0 4px;
    margin-top: 20px;
  }

  .le5le-topology .image-panel .img-list[data-v-01e0bf19] {
    width: 180px;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
  }

  .le5le-topology .image-panel .img-list .img-box[data-v-01e0bf19] {
    margin: 20px 5px 0;
    height: 50px;
    width: 50px;
    position: relative;
    cursor: pointer;
  }

  .le5le-topology .image-panel .img-list .img-box .t-close[data-v-01e0bf19] {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
  }

  .le5le-topology .image-panel .img-list .img-box .t-close[data-v-01e0bf19]:hover {
    color: #1890ff;
  }

  .le5le-topology .image-panel .img-list .img-box:hover .t-close[data-v-01e0bf19] {
    display: block;
  }

  .le5le-topology .image-panel .img-list .img-box .img-content[data-v-01e0bf19] {
    height: 100%;
    width: 100%;
  }

  .color-picker-component[data-v-ee8f3ac2] {
    display: flex;
    align-items: center;
    position: relative;
  }

  .color-picker-component .color-picker-container[data-v-ee8f3ac2] {
    position: absolute;
    left: 8px;
    display: flex;
    align-items: center;
  }

  .color-picker-component .color-picker-container .current-color[data-v-ee8f3ac2] {
    width: 16px;
    height: 16px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    background-color: #000;
    cursor: pointer;
  }

  .color-picker-component .vue-color__chrome[data-v-ee8f3ac2] {
    position: absolute;
    right: 0;
    top: calc(100% + 10px);
    z-index: 999;
  }

  .color-picker-component input.form-control[data-v-ee8f3ac2] {
    padding-left: 28px !important;
    width: 100%;
  }

  .le5le-topology .file-settings {
    height: 100%;
  }

  .le5le-topology .file-settings .settings {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  .le5le-topology .file-settings .settings .tips {
    width: 100%;
  }

  .le5le-topology .file-settings .settings .tips .title {
    width: 100%;
    height: 35px;
    border-bottom: 1px solid #e5e5e5;
    color: #262626;
    padding: 5px 15px;
    margin-top: 10px;
    font-size: 12px;
    font-weight: 600;
  }

  .le5le-topology .file-settings .settings .tips>ul {
    margin-top: 10px;
    font-size: 12px;
  }

  .le5le-topology .file-settings .settings .tips>ul>li {
    margin-top: 12px;
    margin-left: -5px;
  }

  .le5le-topology .file-settings .settings .img-box {
    margin: 20px 5px 0;
    height: 50px;
    width: 50px;
    position: relative;
    cursor: pointer;
  }

  .le5le-topology .file-settings .settings .img-box .t-close {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
  }

  .le5le-topology .file-settings .settings .img-box .t-close:hover {
    color: #1890ff;
  }

  .le5le-topology .file-settings .settings .img-box:hover .t-close {
    display: block;
  }

  .le5le-topology .file-settings .settings .img-box .img-content {
    height: 100%;
    width: 100%;
  }

  .le5le-topology .file-settings .images {
    position: absolute;
    left: 0;
    top: 0;
    width: 240px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    z-index: 1;
  }

  .le5le-topology .tree {
    margin-top: 16px;
  }

  .le5le-topology .tree>.item {
    position: relative;
    margin: 10px;
  }

  .le5le-topology .tree>.item>.content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .le5le-topology .tree>.item>.content:hover {
    color: #1890ff;
  }

  .le5le-topology .tree>.item .text {
    display: flex;
    align-items: center;
  }

  .le5le-topology .tree>.item .text label {
    margin-right: 10px;
  }

  .le5le-topology .tree>.item .text label span {
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    word-break: break-all;
    word-break: break-word;
    -webkit-line-clamp: 1;
    line-height: 1;
    height: 16px;
    padding-right: 8px;
  }

  .le5le-topology .tree>.item .text>.anticon {
    margin-right: 8px;
  }

  .le5le-topology .tree>.item .state {
    white-space: nowrap;
  }

  .le5le-topology .tree .tree {
    margin-top: 0;
    margin-left: 12px;
  }

  .le5le-topology .tree .tree>.item {
    margin-right: 0;
  }

  .le5le-topology .file-panel {
    height: 100%;
    box-sizing: border-box;
  }

  .le5le-topology .icons-panel .content i {
    font-size: 32px;
    margin: 12px;
    cursor: pointer;
  }

  .le5le-topology .icons-panel .content i:hover {
    color: #1890ff;
  }

  .le5le-topology .exterior .images {
    position: absolute;
    left: 0;
    top: 0;
    width: 240px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    z-index: 1;
  }

  .le5le-topology .exterior label img {
    max-height: 20px;
    cursor: pointer;
    vertical-align: sub;
  }

  .le5le-topology .exterior .control {
    align-items: flex-start !important;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-nav-container {
    height: 40px;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-ink-bar {
    visibility: hidden;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    height: 40px;
    margin: 0;
    margin-right: 2px;
    padding: 0 16px;
    line-height: 38px;
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    height: 40px;
    color: #1890ff;
    background: #fff;
    border-color: #e8e8e8;
    border-bottom: 1px solid #fff;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active::before {
    border-top: 2px solid transparent;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-disabled {
    color: #1890ff;
    color: rgba(0, 0, 0, 0.25);
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-inactive {
    padding: 0;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-nav-wrap {
    margin-bottom: 0;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab .ant-tabs-close-x {
    width: 16px;
    height: 16px;
    height: 14px;
    margin-right: -5px;
    margin-left: 3px;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    vertical-align: middle;
    transition: all 0.3s;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab .ant-tabs-close-x:hover {
    color: rgba(0, 0, 0, 0.85);
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-content>.ant-tabs-tabpane,
  .ant-tabs.ant-tabs-editable-card .ant-tabs-card-content>.ant-tabs-tabpane {
    transition: none !important;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-content>.ant-tabs-tabpane-inactive,
  .ant-tabs.ant-tabs-editable-card .ant-tabs-card-content>.ant-tabs-tabpane-inactive {
    overflow: hidden;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab:hover .anticon-close {
    opacity: 1;
  }

  .ant-tabs-extra-content {
    line-height: 45px;
  }

  .ant-tabs-extra-content .ant-tabs-new-tab {
    position: relative;
    width: 20px;
    height: 20px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .ant-tabs-extra-content .ant-tabs-new-tab:hover {
    color: #1890ff;
    border-color: #1890ff;
  }

  .ant-tabs-extra-content .ant-tabs-new-tab svg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
  }

  .ant-tabs.ant-tabs-large .ant-tabs-extra-content {
    line-height: 56px;
  }

  .ant-tabs.ant-tabs-small .ant-tabs-extra-content {
    line-height: 37px;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-extra-content {
    line-height: 40px;
  }

  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-left-bar .ant-tabs-nav-container,
  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-right-bar .ant-tabs-nav-container {
    height: 100%;
  }

  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-left-bar .ant-tabs-tab,
  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-right-bar .ant-tabs-tab {
    margin-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;
  }

  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-left-bar .ant-tabs-tab-active,
  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-right-bar .ant-tabs-tab-active {
    padding-bottom: 4px;
  }

  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-left-bar .ant-tabs-tab:last-child,
  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-right-bar .ant-tabs-tab:last-child {
    margin-bottom: 8px;
  }

  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-left-bar .ant-tabs-new-tab,
  .ant-tabs-vertical.ant-tabs-card .ant-tabs-card-bar.ant-tabs-right-bar .ant-tabs-new-tab {
    width: 90%;
  }

  .ant-tabs-vertical.ant-tabs-card.ant-tabs-left .ant-tabs-card-bar.ant-tabs-left-bar .ant-tabs-nav-wrap {
    margin-right: 0;
  }

  .ant-tabs-vertical.ant-tabs-card.ant-tabs-left .ant-tabs-card-bar.ant-tabs-left-bar .ant-tabs-tab {
    margin-right: 1px;
    border-right: 0;
    border-radius: 4px 0 0 4px;
  }

  .ant-tabs-vertical.ant-tabs-card.ant-tabs-left .ant-tabs-card-bar.ant-tabs-left-bar .ant-tabs-tab-active {
    margin-right: -1px;
    padding-right: 18px;
  }

  .ant-tabs-vertical.ant-tabs-card.ant-tabs-right .ant-tabs-card-bar.ant-tabs-right-bar .ant-tabs-nav-wrap {
    margin-left: 0;
  }

  .ant-tabs-vertical.ant-tabs-card.ant-tabs-right .ant-tabs-card-bar.ant-tabs-right-bar .ant-tabs-tab {
    margin-left: 1px;
    border-left: 0;
    border-radius: 0 4px 4px 0;
  }

  .ant-tabs-vertical.ant-tabs-card.ant-tabs-right .ant-tabs-card-bar.ant-tabs-right-bar .ant-tabs-tab-active {
    margin-left: -1px;
    padding-left: 18px;
  }

  .ant-tabs .ant-tabs-card-bar.ant-tabs-bottom-bar .ant-tabs-tab {
    height: auto;
    border-top: 0;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 0 0 4px 4px;
  }

  .ant-tabs .ant-tabs-card-bar.ant-tabs-bottom-bar .ant-tabs-tab-active {
    padding-top: 1px;
    padding-bottom: 0;
    color: #1890ff;
  }

  .ant-tabs {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    overflow: hidden;
    zoom: 1;
  }

  .ant-tabs::after,
  .ant-tabs::before {
    display: table;
    content: "";
  }

  .ant-tabs::after {
    clear: both;
  }

  .ant-tabs-ink-bar {
    position: absolute;
    bottom: 1px;
    left: 0;
    z-index: 1;
    box-sizing: border-box;
    width: 0;
    height: 2px;
    background-color: #1890ff;
    transform-origin: 0 0;
  }

  .ant-tabs-bar {
    margin: 0 0 16px 0;
    border-bottom: 1px solid #e8e8e8;
    outline: none;
  }

  .ant-tabs-bar,
  .ant-tabs-nav-container {
    transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-tabs-nav-container {
    position: relative;
    box-sizing: border-box;
    margin-bottom: -1px;
    overflow: hidden;
    font-size: 14px;
    line-height: 1.5;
    white-space: nowrap;
    zoom: 1;
  }

  .ant-tabs-nav-container::after,
  .ant-tabs-nav-container::before {
    display: table;
    content: "";
  }

  .ant-tabs-nav-container::after {
    clear: both;
  }

  .ant-tabs-nav-container-scrolling {
    padding-right: 32px;
    padding-left: 32px;
  }

  .ant-tabs-bottom .ant-tabs-bottom-bar {
    margin-top: 16px;
    margin-bottom: 0;
    border-top: 1px solid #e8e8e8;
    border-bottom: none;
  }

  .ant-tabs-bottom .ant-tabs-bottom-bar .ant-tabs-ink-bar {
    top: 1px;
    bottom: auto;
  }

  .ant-tabs-bottom .ant-tabs-bottom-bar .ant-tabs-nav-container {
    margin-top: -1px;
    margin-bottom: 0;
  }

  .ant-tabs-tab-next,
  .ant-tabs-tab-prev {
    position: absolute;
    z-index: 2;
    width: 0;
    height: 100%;
    color: rgba(0, 0, 0, 0.45);
    text-align: center;
    background-color: transparent;
    border: 0;
    cursor: pointer;
    opacity: 0;
    transition: width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: none;
  }

  .ant-tabs-tab-next.ant-tabs-tab-arrow-show,
  .ant-tabs-tab-prev.ant-tabs-tab-arrow-show {
    width: 32px;
    height: 100%;
    opacity: 1;
    pointer-events: auto;
  }

  .ant-tabs-tab-next:hover,
  .ant-tabs-tab-prev:hover {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-tabs-tab-next-icon,
  .ant-tabs-tab-prev-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    font-weight: 700;
    font-style: normal;
    font-variant: normal;
    line-height: inherit;
    text-align: center;
    text-transform: none;
    transform: translate(-50%, -50%);
  }

  .ant-tabs-tab-next-icon-target,
  .ant-tabs-tab-prev-icon-target {
    display: block;
    display: inline-block;
    font-size: 12px;
    font-size: 10px\9;
    transform: scale(0.83333333) rotate(0deg);
  }

  :root .ant-tabs-tab-next-icon-target,
  :root .ant-tabs-tab-prev-icon-target {
    font-size: 12px;
  }

  .ant-tabs-tab-btn-disabled {
    cursor: not-allowed;
  }

  .ant-tabs-tab-btn-disabled,
  .ant-tabs-tab-btn-disabled:hover {
    color: rgba(0, 0, 0, 0.25);
  }

  .ant-tabs-tab-next {
    right: 2px;
  }

  .ant-tabs-tab-prev {
    left: 0;
  }

  :root .ant-tabs-tab-prev {
    filter: none;
  }

  .ant-tabs-nav-wrap {
    margin-bottom: -1px;
    overflow: hidden;
  }

  .ant-tabs-nav-scroll {
    overflow: hidden;
    white-space: nowrap;
  }

  .ant-tabs-nav {
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    margin: 0;
    padding-left: 0;
    list-style: none;
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-tabs-nav::after,
  .ant-tabs-nav::before {
    display: table;
    content: " ";
  }

  .ant-tabs-nav::after {
    clear: both;
  }

  .ant-tabs-nav .ant-tabs-tab {
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    height: 100%;
    margin: 0 32px 0 0;
    padding: 12px 16px;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-tabs-nav .ant-tabs-tab::before {
    position: absolute;
    top: -1px;
    left: 0;
    width: 100%;
    border-top: 2px solid transparent;
    border-radius: 4px 4px 0 0;
    transition: all 0.3s;
    content: "";
    pointer-events: none;
  }

  .ant-tabs-nav .ant-tabs-tab:last-child {
    margin-right: 0;
  }

  .ant-tabs-nav .ant-tabs-tab:hover {
    color: #40a9ff;
  }

  .ant-tabs-nav .ant-tabs-tab:active {
    color: #096dd9;
  }

  .ant-tabs-nav .ant-tabs-tab .anticon {
    margin-right: 8px;
  }

  .ant-tabs-nav .ant-tabs-tab-active {
    color: #1890ff;
    font-weight: 500;
  }

  .ant-tabs-nav .ant-tabs-tab-disabled,
  .ant-tabs-nav .ant-tabs-tab-disabled:hover {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  .ant-tabs .ant-tabs-large-bar .ant-tabs-nav-container {
    font-size: 16px;
  }

  .ant-tabs .ant-tabs-large-bar .ant-tabs-tab {
    padding: 16px;
  }

  .ant-tabs .ant-tabs-small-bar .ant-tabs-nav-container {
    font-size: 14px;
  }

  .ant-tabs .ant-tabs-small-bar .ant-tabs-tab {
    padding: 8px 16px;
  }

  .ant-tabs-content::before {
    display: block;
    overflow: hidden;
    content: "";
  }

  .ant-tabs .ant-tabs-bottom-content,
  .ant-tabs .ant-tabs-top-content {
    width: 100%;
  }

  .ant-tabs .ant-tabs-bottom-content>.ant-tabs-tabpane,
  .ant-tabs .ant-tabs-top-content>.ant-tabs-tabpane {
    flex-shrink: 0;
    width: 100%;
    -webkit-backface-visibility: hidden;
    opacity: 1;
    transition: opacity 0.45s;
  }

  .ant-tabs .ant-tabs-bottom-content>.ant-tabs-tabpane-inactive,
  .ant-tabs .ant-tabs-top-content>.ant-tabs-tabpane-inactive {
    height: 0;
    padding: 0 !important;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
  }

  .ant-tabs .ant-tabs-bottom-content>.ant-tabs-tabpane-inactive input,
  .ant-tabs .ant-tabs-top-content>.ant-tabs-tabpane-inactive input {
    visibility: hidden;
  }

  .ant-tabs .ant-tabs-bottom-content.ant-tabs-content-animated,
  .ant-tabs .ant-tabs-top-content.ant-tabs-content-animated {
    display: flex;
    flex-direction: row;
    transition: margin-left 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    will-change: margin-left;
  }

  .ant-tabs .ant-tabs-left-bar,
  .ant-tabs .ant-tabs-right-bar {
    height: 100%;
    border-bottom: 0;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-tab-arrow-show,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-tab-arrow-show {
    width: 100%;
    height: 32px;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-tab,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-tab {
    display: block;
    float: none;
    margin: 0 0 16px 0;
    padding: 8px 24px;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-tab:last-child,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-tab:last-child {
    margin-bottom: 0;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-extra-content,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-extra-content {
    text-align: center;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav-scroll,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav-scroll {
    width: auto;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav-container,
  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav-wrap,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav-container,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav-wrap {
    height: 100%;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav-container,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav-container {
    margin-bottom: 0;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav-container.ant-tabs-nav-container-scrolling,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav-container.ant-tabs-nav-container-scrolling {
    padding: 32px 0;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav-wrap,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav-wrap {
    margin-bottom: 0;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav {
    width: 100%;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-ink-bar,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-ink-bar {
    top: 0;
    bottom: auto;
    left: auto;
    width: 2px;
    height: 0;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-tab-next,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-tab-next {
    right: 0;
    bottom: 0;
    width: 100%;
    height: 32px;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-tab-prev,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-tab-prev {
    top: 0;
    width: 100%;
    height: 32px;
  }

  .ant-tabs .ant-tabs-left-content,
  .ant-tabs .ant-tabs-right-content {
    width: auto;
    margin-top: 0 !important;
    overflow: hidden;
  }

  .ant-tabs .ant-tabs-left-bar {
    float: left;
    margin-right: -1px;
    margin-bottom: 0;
    border-right: 1px solid #e8e8e8;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-tab {
    text-align: right;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav-container,
  .ant-tabs .ant-tabs-left-bar .ant-tabs-nav-wrap {
    margin-right: -1px;
  }

  .ant-tabs .ant-tabs-left-bar .ant-tabs-ink-bar {
    right: 1px;
  }

  .ant-tabs .ant-tabs-left-content {
    padding-left: 24px;
    border-left: 1px solid #e8e8e8;
  }

  .ant-tabs .ant-tabs-right-bar {
    float: right;
    margin-bottom: 0;
    margin-left: -1px;
    border-left: 1px solid #e8e8e8;
  }

  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav-container,
  .ant-tabs .ant-tabs-right-bar .ant-tabs-nav-wrap {
    margin-left: -1px;
  }

  .ant-tabs .ant-tabs-right-bar .ant-tabs-ink-bar {
    left: 1px;
  }

  .ant-tabs .ant-tabs-right-content {
    padding-right: 24px;
    border-right: 1px solid #e8e8e8;
  }

  .ant-tabs-bottom .ant-tabs-ink-bar-animated,
  .ant-tabs-top .ant-tabs-ink-bar-animated {
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), width 0.2s cubic-bezier(0.645, 0.045, 0.355, 1), left 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-tabs-left .ant-tabs-ink-bar-animated,
  .ant-tabs-right .ant-tabs-ink-bar-animated {
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), height 0.2s cubic-bezier(0.645, 0.045, 0.355, 1), top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  .ant-tabs-no-animation>.ant-tabs-content>.ant-tabs-content-animated,
  .no-flex>.ant-tabs-content>.ant-tabs-content-animated {
    margin-left: 0 !important;
    transform: none !important;
  }

  .ant-tabs-no-animation>.ant-tabs-content>.ant-tabs-tabpane-inactive,
  .no-flex>.ant-tabs-content>.ant-tabs-tabpane-inactive {
    height: 0;
    padding: 0 !important;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
  }

  .ant-tabs-no-animation>.ant-tabs-content>.ant-tabs-tabpane-inactive input,
  .no-flex>.ant-tabs-content>.ant-tabs-tabpane-inactive input {
    visibility: hidden;
  }

  .ant-tabs-left-content>.ant-tabs-content-animated,
  .ant-tabs-right-content>.ant-tabs-content-animated {
    margin-left: 0 !important;
    transform: none !important;
  }

  .ant-tabs-left-content>.ant-tabs-tabpane-inactive,
  .ant-tabs-right-content>.ant-tabs-tabpane-inactive {
    height: 0;
    padding: 0 !important;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
  }

  .ant-tabs-left-content>.ant-tabs-tabpane-inactive input,
  .ant-tabs-right-content>.ant-tabs-tabpane-inactive input {
    visibility: hidden;
  }

  .ant-switch {
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    min-width: 44px;
    height: 22px;
    line-height: 20px;
    vertical-align: middle;
    background-color: rgba(0, 0, 0, 0.25);
    border: 1px solid transparent;
    border-radius: 100px;
    cursor: pointer;
    transition: all 0.36s;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .ant-switch-inner {
    display: block;
    margin-right: 6px;
    margin-left: 24px;
    color: #fff;
    font-size: 12px;
  }

  .ant-switch-loading-icon,
  .ant-switch::after {
    position: absolute;
    top: 1px;
    left: 1px;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border-radius: 18px;
    cursor: pointer;
    transition: all 0.36s cubic-bezier(0.78, 0.14, 0.15, 0.86);
    content: " ";
  }

  .ant-switch::after {
    box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);
  }

  .ant-switch:not(.ant-switch-disabled):active::after,
  .ant-switch:not(.ant-switch-disabled):active::before {
    width: 24px;
  }

  .ant-switch-loading-icon {
    z-index: 1;
    display: none;
    font-size: 12px;
    background: transparent;
  }

  .ant-switch-loading-icon svg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
  }

  .ant-switch-loading .ant-switch-loading-icon {
    display: inline-block;
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-switch-checked.ant-switch-loading .ant-switch-loading-icon {
    color: #1890ff;
  }

  .ant-switch:focus {
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .ant-switch:focus:hover {
    box-shadow: none;
  }

  .ant-switch-small {
    min-width: 28px;
    height: 16px;
    line-height: 14px;
  }

  .ant-switch-small .ant-switch-inner {
    margin-right: 3px;
    margin-left: 18px;
    font-size: 12px;
  }

  .ant-switch-small::after {
    width: 12px;
    height: 12px;
  }

  .ant-switch-small:active::after,
  .ant-switch-small:active::before {
    width: 16px;
  }

  .ant-switch-small .ant-switch-loading-icon {
    width: 12px;
    height: 12px;
  }

  .ant-switch-small.ant-switch-checked .ant-switch-inner {
    margin-right: 18px;
    margin-left: 3px;
  }

  .ant-switch-small.ant-switch-checked .ant-switch-loading-icon {
    left: 100%;
    margin-left: -13px;
  }

  .ant-switch-small.ant-switch-loading .ant-switch-loading-icon {
    font-weight: 700;
    transform: scale(0.66667);
  }

  .ant-switch-checked {
    background-color: #1890ff;
  }

  .ant-switch-checked .ant-switch-inner {
    margin-right: 24px;
    margin-left: 6px;
  }

  .ant-switch-checked::after {
    left: 100%;
    margin-left: -1px;
    transform: translateX(-100%);
  }

  .ant-switch-checked .ant-switch-loading-icon {
    left: 100%;
    margin-left: -19px;
  }

  .ant-switch-disabled,
  .ant-switch-loading {
    cursor: not-allowed;
    opacity: 0.4;
  }

  .ant-switch-disabled *,
  .ant-switch-disabled::after,
  .ant-switch-disabled::before,
  .ant-switch-loading *,
  .ant-switch-loading::after,
  .ant-switch-loading::before {
    cursor: not-allowed;
  }

  @-webkit-keyframes AntSwitchSmallLoadingCircle {
    0% {
      transform: rotate(0deg) scale(0.66667);
      transform-origin: 50% 50%;
    }

    to {
      transform: rotate(1turn) scale(0.66667);
      transform-origin: 50% 50%;
    }
  }

  @keyframes AntSwitchSmallLoadingCircle {
    0% {
      transform: rotate(0deg) scale(0.66667);
      transform-origin: 50% 50%;
    }

    to {
      transform: rotate(1turn) scale(0.66667);
      transform-origin: 50% 50%;
    }
  }

  .le5le-topology .pen-event {
    padding: 12px;
  }

  .le5le-topology .pen-event .ant-tabs-bar {
    margin-bottom: 12px;
  }

  .le5le-topology .pen-event .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-nav-container {
    height: 30px;
  }

  .le5le-topology .pen-event .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    height: 30px;
    line-height: 28px;
    font-size: 12px;
  }

  .le5le-topology .pen-event .btn {
    width: 214px !important;
  }

  .le5le-topology .pen-event .btn-small {
    width: 192px !important;
  }

  .ant-slider {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    height: 12px;
    margin: 14px 6px 10px;
    padding: 4px 0;
    cursor: pointer;
    touch-action: none;
  }

  .ant-slider-vertical {
    width: 12px;
    height: 100%;
    margin: 6px 10px;
    padding: 0 4px;
  }

  .ant-slider-vertical .ant-slider-rail {
    width: 4px;
    height: 100%;
  }

  .ant-slider-vertical .ant-slider-track {
    width: 4px;
  }

  .ant-slider-vertical .ant-slider-handle {
    margin-top: -6px;
    margin-left: -5px;
  }

  .ant-slider-vertical .ant-slider-mark {
    top: 0;
    left: 12px;
    width: 18px;
    height: 100%;
  }

  .ant-slider-vertical .ant-slider-mark-text {
    left: 4px;
    white-space: nowrap;
  }

  .ant-slider-vertical .ant-slider-step {
    width: 4px;
    height: 100%;
  }

  .ant-slider-vertical .ant-slider-dot {
    top: auto;
    left: 2px;
    margin-bottom: -4px;
  }

  .ant-slider-tooltip .ant-tooltip-inner {
    min-width: unset;
  }

  .ant-slider-with-marks {
    margin-bottom: 28px;
  }

  .ant-slider-rail {
    width: 100%;
    background-color: #f5f5f5;
    border-radius: 2px;
  }

  .ant-slider-rail,
  .ant-slider-track {
    position: absolute;
    height: 4px;
    transition: background-color 0.3s;
  }

  .ant-slider-track {
    background-color: #91d5ff;
    border-radius: 4px;
  }

  .ant-slider-handle {
    position: absolute;
    width: 14px;
    height: 14px;
    margin-top: -5px;
    background-color: #fff;
    border: 2px solid #91d5ff;
    border-radius: 50%;
    box-shadow: 0;
    cursor: pointer;
    transition: border-color 0.3s, box-shadow 0.6s, transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  }

  .ant-slider-handle:focus {
    border-color: #46a6ff;
    outline: none;
    box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.2);
  }

  .ant-slider-handle.ant-tooltip-open {
    border-color: #1890ff;
  }

  .ant-slider:hover .ant-slider-rail {
    background-color: #e1e1e1;
  }

  .ant-slider:hover .ant-slider-track {
    background-color: #69c0ff;
  }

  .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
    border-color: #69c0ff;
  }

  .ant-slider-mark {
    position: absolute;
    top: 14px;
    left: 0;
    width: 100%;
    font-size: 14px;
  }

  .ant-slider-mark-text {
    position: absolute;
    display: inline-block;
    color: rgba(0, 0, 0, 0.45);
    text-align: center;
    word-break: keep-all;
    cursor: pointer;
  }

  .ant-slider-mark-text-active {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-slider-step {
    position: absolute;
    width: 100%;
    height: 4px;
    background: transparent;
  }

  .ant-slider-dot {
    position: absolute;
    top: -2px;
    width: 8px;
    height: 8px;
    background-color: #fff;
    border: 2px solid #e8e8e8;
    border-radius: 50%;
    cursor: pointer;
  }

  .ant-slider-dot,
  .ant-slider-dot:first-child,
  .ant-slider-dot:last-child {
    margin-left: -4px;
  }

  .ant-slider-dot-active {
    border-color: #8cc8ff;
  }

  .ant-slider-disabled {
    cursor: not-allowed;
  }

  .ant-slider-disabled .ant-slider-track {
    background-color: rgba(0, 0, 0, 0.25) !important;
  }

  .ant-slider-disabled .ant-slider-dot,
  .ant-slider-disabled .ant-slider-handle {
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.25) !important;
    box-shadow: none;
    cursor: not-allowed;
  }

  .ant-slider-disabled .ant-slider-dot,
  .ant-slider-disabled .ant-slider-mark-text {
    cursor: not-allowed !important;
  }

  .prop-modal.animations .content .title.sub {
    font-weight: 400 !important;
    color: #000 !important;
  }

  .prop-modal.animations .content .item img {
    height: 20px;
    margin-right: 8px;
    cursor: pointer;
  }

  .prop-modal.animations .buttons {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .prop-modal.animations .buttons.top {
    top: 48px;
  }

  .prop-modal.animations .images {
    position: absolute;
    left: 0;
    top: 0;
    width: 240px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    z-index: 1;
  }

  .le5le-topology .pen-animate .custom-animation {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    overflow-y: auto;
    background: #fff;
    z-index: 1;
  }

  .ant-tag {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    display: inline-block;
    height: auto;
    margin-right: 8px;
    padding: 0 7px;
    font-size: 12px;
    line-height: 20px;
    white-space: nowrap;
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: default;
    opacity: 1;
    transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .ant-tag:hover {
    opacity: 0.85;
  }

  .ant-tag,
  .ant-tag a,
  .ant-tag a:hover {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-tag>a:first-child:last-child {
    display: inline-block;
    margin: 0 -8px;
    padding: 0 8px;
  }

  .ant-tag .anticon-close {
    display: inline-block;
    font-size: 12px;
    font-size: 10px\9;
    transform: scale(0.83333333) rotate(0deg);
    margin-left: 3px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  :root .ant-tag .anticon-close {
    font-size: 12px;
  }

  .ant-tag .anticon-close:hover {
    color: rgba(0, 0, 0, 0.85);
  }

  .ant-tag-has-color {
    border-color: transparent;
  }

  .ant-tag-has-color,
  .ant-tag-has-color .anticon-close,
  .ant-tag-has-color .anticon-close:hover,
  .ant-tag-has-color a,
  .ant-tag-has-color a:hover {
    color: #fff;
  }

  .ant-tag-checkable {
    background-color: transparent;
    border-color: transparent;
  }

  .ant-tag-checkable:not(.ant-tag-checkable-checked):hover {
    color: #1890ff;
  }

  .ant-tag-checkable-checked,
  .ant-tag-checkable:active {
    color: #fff;
  }

  .ant-tag-checkable-checked {
    background-color: #1890ff;
  }

  .ant-tag-checkable:active {
    background-color: #096dd9;
  }

  .ant-tag-hidden {
    display: none;
  }

  .ant-tag-pink {
    color: #eb2f96;
    background: #fff0f6;
    border-color: #ffadd2;
  }

  .ant-tag-pink-inverse {
    color: #fff;
    background: #eb2f96;
    border-color: #eb2f96;
  }

  .ant-tag-magenta {
    color: #eb2f96;
    background: #fff0f6;
    border-color: #ffadd2;
  }

  .ant-tag-magenta-inverse {
    color: #fff;
    background: #eb2f96;
    border-color: #eb2f96;
  }

  .ant-tag-red {
    color: #f5222d;
    background: #fff1f0;
    border-color: #ffa39e;
  }

  .ant-tag-red-inverse {
    color: #fff;
    background: #f5222d;
    border-color: #f5222d;
  }

  .ant-tag-volcano {
    color: #fa541c;
    background: #fff2e8;
    border-color: #ffbb96;
  }

  .ant-tag-volcano-inverse {
    color: #fff;
    background: #fa541c;
    border-color: #fa541c;
  }

  .ant-tag-orange {
    color: #fa8c16;
    background: #fff7e6;
    border-color: #ffd591;
  }

  .ant-tag-orange-inverse {
    color: #fff;
    background: #fa8c16;
    border-color: #fa8c16;
  }

  .ant-tag-yellow {
    color: #fadb14;
    background: #feffe6;
    border-color: #fffb8f;
  }

  .ant-tag-yellow-inverse {
    color: #fff;
    background: #fadb14;
    border-color: #fadb14;
  }

  .ant-tag-gold {
    color: #faad14;
    background: #fffbe6;
    border-color: #ffe58f;
  }

  .ant-tag-gold-inverse {
    color: #fff;
    background: #faad14;
    border-color: #faad14;
  }

  .ant-tag-cyan {
    color: #13c2c2;
    background: #e6fffb;
    border-color: #87e8de;
  }

  .ant-tag-cyan-inverse {
    color: #fff;
    background: #13c2c2;
    border-color: #13c2c2;
  }

  .ant-tag-lime {
    color: #a0d911;
    background: #fcffe6;
    border-color: #eaff8f;
  }

  .ant-tag-lime-inverse {
    color: #fff;
    background: #a0d911;
    border-color: #a0d911;
  }

  .ant-tag-green {
    color: #52c41a;
    background: #f6ffed;
    border-color: #b7eb8f;
  }

  .ant-tag-green-inverse {
    color: #fff;
    background: #52c41a;
    border-color: #52c41a;
  }

  .ant-tag-blue {
    color: #1890ff;
    background: #e6f7ff;
    border-color: #91d5ff;
  }

  .ant-tag-blue-inverse {
    color: #fff;
    background: #1890ff;
    border-color: #1890ff;
  }

  .ant-tag-geekblue {
    color: #2f54eb;
    background: #f0f5ff;
    border-color: #adc6ff;
  }

  .ant-tag-geekblue-inverse {
    color: #fff;
    background: #2f54eb;
    border-color: #2f54eb;
  }

  .ant-tag-purple {
    color: #722ed1;
    background: #f9f0ff;
    border-color: #d3adf7;
  }

  .ant-tag-purple-inverse {
    color: #fff;
    background: #722ed1;
    border-color: #722ed1;
  }

  .ant-radio-group {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    display: inline-block;
  }

  .ant-radio-wrapper {
    margin: 0;
    margin-right: 8px;
  }

  .ant-radio,
  .ant-radio-wrapper {
    box-sizing: border-box;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    white-space: nowrap;
    cursor: pointer;
  }

  .ant-radio {
    margin: 0;
    line-height: 1;
    vertical-align: sub;
    outline: none;
  }

  .ant-radio-input:focus+.ant-radio-inner,
  .ant-radio-wrapper:hover .ant-radio,
  .ant-radio:hover .ant-radio-inner {
    border-color: #1890ff;
  }

  .ant-radio-input:focus+.ant-radio-inner {
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.08);
  }

  .ant-radio-checked::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #1890ff;
    border-radius: 50%;
    visibility: hidden;
    -webkit-animation: antradioeffect 0.36s ease-in-out;
    animation: antRadioEffect 0.36s ease-in-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    content: "";
  }

  .ant-radio-wrapper:hover .ant-radio::after,
  .ant-radio:hover::after {
    visibility: visible;
  }

  .ant-radio-inner {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: 16px;
    height: 16px;
    background-color: #fff;
    border-color: #d9d9d9;
    border-style: solid;
    border-width: 1px;
    border-radius: 100px;
    transition: all 0.3s;
  }

  .ant-radio-inner::after {
    position: absolute;
    top: 3px;
    left: 3px;
    display: table;
    width: 8px;
    height: 8px;
    background-color: #1890ff;
    border-top: 0;
    border-left: 0;
    border-radius: 8px;
    transform: scale(0);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
    content: " ";
  }

  .ant-radio-input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    cursor: pointer;
    opacity: 0;
  }

  .ant-radio-checked .ant-radio-inner {
    border-color: #1890ff;
  }

  .ant-radio-checked .ant-radio-inner::after {
    transform: scale(1);
    opacity: 1;
    transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  }

  .ant-radio-disabled .ant-radio-inner {
    background-color: #f5f5f5;
    border-color: #d9d9d9 !important;
    cursor: not-allowed;
  }

  .ant-radio-disabled .ant-radio-inner::after {
    background-color: rgba(0, 0, 0, 0.2);
  }

  .ant-radio-disabled .ant-radio-input {
    cursor: not-allowed;
  }

  .ant-radio-disabled+span {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  span.ant-radio+* {
    padding-right: 8px;
    padding-left: 8px;
  }

  .ant-radio-button-wrapper {
    position: relative;
    display: inline-block;
    height: 32px;
    margin: 0;
    padding: 0 15px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 30px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-top-width: 1.02px;
    border-left: 0;
    cursor: pointer;
    transition: color 0.3s, background 0.3s, border-color 0.3s;
  }

  .ant-radio-button-wrapper a {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-radio-button-wrapper>.ant-radio-button {
    display: block;
    width: 0;
    height: 0;
    margin-left: 0;
  }

  .ant-radio-group-large .ant-radio-button-wrapper {
    height: 40px;
    font-size: 16px;
    line-height: 38px;
  }

  .ant-radio-group-small .ant-radio-button-wrapper {
    height: 24px;
    padding: 0 7px;
    line-height: 22px;
  }

  .ant-radio-button-wrapper:not(:first-child)::before {
    position: absolute;
    top: 0;
    left: -1px;
    display: block;
    width: 1px;
    height: 100%;
    background-color: #d9d9d9;
    content: "";
  }

  .ant-radio-button-wrapper:first-child {
    border-left: 1px solid #d9d9d9;
    border-radius: 4px 0 0 4px;
  }

  .ant-radio-button-wrapper:last-child {
    border-radius: 0 4px 4px 0;
  }

  .ant-radio-button-wrapper:first-child:last-child {
    border-radius: 4px;
  }

  .ant-radio-button-wrapper:hover {
    position: relative;
    color: #1890ff;
  }

  .ant-radio-button-wrapper:focus-within {
    outline: 3px solid rgba(24, 144, 255, 0.06);
  }

  .ant-radio-button-wrapper .ant-radio-inner,
  .ant-radio-button-wrapper input[type=checkbox],
  .ant-radio-button-wrapper input[type=radio] {
    width: 0;
    height: 0;
    opacity: 0;
    pointer-events: none;
  }

  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    z-index: 1;
    color: #1890ff;
    background: #fff;
    border-color: #1890ff;
    box-shadow: -1px 0 0 0 #1890ff;
  }

  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
    background-color: #1890ff !important;
    opacity: 0.1;
  }

  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child {
    border-color: #1890ff;
    box-shadow: none !important;
  }

  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
    color: #40a9ff;
    border-color: #40a9ff;
    box-shadow: -1px 0 0 0 #40a9ff;
  }

  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {
    color: #096dd9;
    border-color: #096dd9;
    box-shadow: -1px 0 0 0 #096dd9;
  }

  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
    outline: 3px solid rgba(24, 144, 255, 0.06);
  }

  .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    color: #fff;
    background: #1890ff;
    border-color: #1890ff;
  }

  .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
    color: #fff;
    background: #40a9ff;
    border-color: #40a9ff;
  }

  .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {
    color: #fff;
    background: #096dd9;
    border-color: #096dd9;
  }

  .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
    outline: 3px solid rgba(24, 144, 255, 0.06);
  }

  .ant-radio-button-wrapper-disabled {
    cursor: not-allowed;
  }

  .ant-radio-button-wrapper-disabled,
  .ant-radio-button-wrapper-disabled:first-child,
  .ant-radio-button-wrapper-disabled:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
  }

  .ant-radio-button-wrapper-disabled:first-child {
    border-left-color: #d9d9d9;
  }

  .ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {
    color: #fff;
    background-color: #e6e6e6;
    border-color: #d9d9d9;
    box-shadow: none;
  }

  @-webkit-keyframes antRadioEffect {
    0% {
      transform: scale(1);
      opacity: 0.5;
    }

    to {
      transform: scale(1.6);
      opacity: 0;
    }
  }

  @keyframes antRadioEffect {
    0% {
      transform: scale(1);
      opacity: 0.5;
    }

    to {
      transform: scale(1.6);
      opacity: 0;
    }
  }

  @supports (-moz-appearance:meterbar) and (background-blend-mode:difference, normal) {
    .ant-radio {
      vertical-align: text-bottom;
    }
  }

  .ant-select-auto-complete {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
  }

  .ant-select-auto-complete.ant-select .ant-select-selection {
    border: 0;
    box-shadow: none;
  }

  .ant-select-auto-complete.ant-select .ant-select-selection__rendered {
    height: 100%;
    margin-right: 0;
    margin-left: 0;
    line-height: 32px;
  }

  .ant-select-auto-complete.ant-select .ant-select-selection__placeholder {
    margin-right: 12px;
    margin-left: 12px;
  }

  .ant-select-auto-complete.ant-select .ant-select-selection--single {
    height: auto;
  }

  .ant-select-auto-complete.ant-select .ant-select-search--inline {
    position: static;
    float: left;
  }

  .ant-select-auto-complete.ant-select-allow-clear .ant-select-selection:hover .ant-select-selection__rendered {
    margin-right: 0 !important;
  }

  .ant-select-auto-complete.ant-select .ant-input {
    height: 32px;
    line-height: 1.5;
    background: transparent;
    border-width: 1px;
  }

  .ant-select-auto-complete.ant-select .ant-input:focus,
  .ant-select-auto-complete.ant-select .ant-input:hover {
    border-color: #40a9ff;
    border-right-width: 1px !important;
  }

  .ant-select-auto-complete.ant-select .ant-input[disabled] {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 1;
    background-color: transparent;
  }

  .ant-select-auto-complete.ant-select .ant-input[disabled]:hover {
    border-color: #d9d9d9;
    border-right-width: 1px !important;
  }

  .ant-select-auto-complete.ant-select-lg .ant-select-selection__rendered {
    line-height: 40px;
  }

  .ant-select-auto-complete.ant-select-lg .ant-input {
    height: 40px;
    padding-top: 6px;
    padding-bottom: 6px;
  }

  .ant-select-auto-complete.ant-select-sm .ant-select-selection__rendered {
    line-height: 24px;
  }

  .ant-select-auto-complete.ant-select-sm .ant-input {
    height: 24px;
    padding-top: 1px;
    padding-bottom: 1px;
  }

  .ant-input-group>.ant-select-auto-complete .ant-select-search__field.ant-input-affix-wrapper {
    display: inline;
    float: none;
  }

  .le5le-topology .data-panel .tags {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 8px 0;
  }

  .le5le-topology .data-panel .tags .ant-tag {
    margin-top: 8px;
  }

  .le5le-topology .data-panel .small {
    margin-left: 8px;
  }

  .le5le-topology .data-panel .small .ant-checkbox+span {
    padding: 0;
    font-size: 12px;
    font-weight: 400;
  }

  .le5le-topology .data-panel .group .content>.flex.custom> :first-child {
    width: 40%;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full {
    display: flex;
    line-height: 26px;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .input {
    flex-grow: 1;
    padding: 0 8px;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .input .ant-checkbox-wrapper {
    padding: 0;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .input .ant-radio-group {
    max-width: 76px;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .input .ant-radio-group .ant-radio-wrapper>span:last-child {
    width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: text-top;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .input .ant-checkbox-group {
    white-space: nowrap;
    max-width: 76px;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .input .ant-checkbox-group .ant-checkbox-wrapper>span:last-child {
    width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: text-top;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .ant-select-selection--multiple {
    width: 100%;
    border: none;
    box-shadow: none;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .ant-select-selection--multiple .ant-select-selection__placeholder {
    margin-left: 0;
  }

  .le5le-topology .data-panel .group .content>.flex.custom .full .t-delete {
    margin-left: 4px;
    z-index: 10;
  }

  .le5le-topology .data-panel .buttons {
    display: flex;
    flex-wrap: wrap;
  }

  .le5le-topology .data-panel .buttons a {
    width: 40%;
    white-space: nowrap;
  }

  .le5le-topology .pen-props {
    height: 100%;
    box-sizing: border-box;
  }

  .le5le-topology .pen-props .ant-tabs {
    font-size: 12px;
  }

  .ant-dropdown {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: "tnum";
    position: absolute;
    top: -9999px;
    left: -9999px;
    z-index: 1050;
    display: block;
  }

  .ant-dropdown::before {
    position: absolute;
    top: -7px;
    right: 0;
    bottom: -7px;
    left: -7px;
    z-index: -9999;
    opacity: 0.0001;
    content: " ";
  }

  .ant-dropdown-wrap {
    position: relative;
  }

  .ant-dropdown-wrap .ant-btn>.anticon-down {
    display: inline-block;
    font-size: 12px;
    font-size: 10px\9;
    transform: scale(0.83333333) rotate(0deg);
  }

  :root .ant-dropdown-wrap .ant-btn>.anticon-down {
    font-size: 12px;
  }

  .ant-dropdown-wrap .anticon-down::before {
    transition: transform 0.2s;
  }

  .ant-dropdown-wrap-open .anticon-down::before {
    transform: rotate(180deg);
  }

  .ant-dropdown-hidden,
  .ant-dropdown-menu-hidden {
    display: none;
  }

  .ant-dropdown-menu {
    position: relative;
    margin: 0;
    padding: 4px 0;
    text-align: left;
    list-style-type: none;
    background-color: #fff;
    background-clip: padding-box;
    border-radius: 4px;
    outline: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    -webkit-transform: translateZ(0);
  }

  .ant-dropdown-menu-item-group-title {
    padding: 5px 12px;
    color: rgba(0, 0, 0, 0.45);
    transition: all 0.3s;
  }

  .ant-dropdown-menu-submenu-popup {
    position: absolute;
    z-index: 1050;
  }

  .ant-dropdown-menu-submenu-popup>.ant-dropdown-menu {
    transform-origin: 0 0;
  }

  .ant-dropdown-menu-submenu-popup li,
  .ant-dropdown-menu-submenu-popup ul {
    list-style: none;
  }

  .ant-dropdown-menu-submenu-popup ul {
    margin-right: 0.3em;
    margin-left: 0.3em;
    padding: 0;
  }

  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    clear: both;
    margin: 0;
    padding: 5px 12px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s;
  }

  .ant-dropdown-menu-item>.anticon:first-child,
  .ant-dropdown-menu-item>span>.anticon:first-child,
  .ant-dropdown-menu-submenu-title>.anticon:first-child,
  .ant-dropdown-menu-submenu-title>span>.anticon:first-child {
    min-width: 12px;
    margin-right: 8px;
    font-size: 12px;
  }

  .ant-dropdown-menu-item>a,
  .ant-dropdown-menu-submenu-title>a {
    display: block;
    margin: -5px -12px;
    padding: 5px 12px;
    color: rgba(0, 0, 0, 0.65);
    transition: all 0.3s;
  }

  .ant-dropdown-menu-item-selected,
  .ant-dropdown-menu-item-selected>a,
  .ant-dropdown-menu-submenu-title-selected,
  .ant-dropdown-menu-submenu-title-selected>a {
    color: #1890ff;
    background-color: #e6f7ff;
  }

  .ant-dropdown-menu-item:hover,
  .ant-dropdown-menu-submenu-title:hover {
    background-color: #e6f7ff;
  }

  .ant-dropdown-menu-item-disabled,
  .ant-dropdown-menu-submenu-title-disabled {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  .ant-dropdown-menu-item-disabled:hover,
  .ant-dropdown-menu-submenu-title-disabled:hover {
    color: rgba(0, 0, 0, 0.25);
    background-color: #fff;
    cursor: not-allowed;
  }

  .ant-dropdown-menu-item-divider,
  .ant-dropdown-menu-submenu-title-divider {
    height: 1px;
    margin: 4px 0;
    overflow: hidden;
    line-height: 0;
    background-color: #e8e8e8;
  }

  .ant-dropdown-menu-item .ant-dropdown-menu-submenu-arrow,
  .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow {
    position: absolute;
    right: 8px;
  }

  .ant-dropdown-menu-item .ant-dropdown-menu-submenu-arrow-icon,
  .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon {
    color: rgba(0, 0, 0, 0.45);
    font-style: normal;
    display: inline-block;
    font-size: 12px;
    font-size: 10px\9;
    transform: scale(0.83333333) rotate(0deg);
  }

  :root .ant-dropdown-menu-item .ant-dropdown-menu-submenu-arrow-icon,
  :root .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon {
    font-size: 12px;
  }

  .ant-dropdown-menu-item-group-list {
    margin: 0 8px;
    padding: 0;
    list-style: none;
  }

  .ant-dropdown-menu-submenu-title {
    padding-right: 26px;
  }

  .ant-dropdown-menu-submenu-vertical {
    position: relative;
  }

  .ant-dropdown-menu-submenu-vertical>.ant-dropdown-menu {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 100%;
    margin-left: 4px;
    transform-origin: 0 0;
  }

  .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title,
  .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon {
    color: rgba(0, 0, 0, 0.25);
    background-color: #fff;
    cursor: not-allowed;
  }

  .ant-dropdown-menu-submenu-selected .ant-dropdown-menu-submenu-title {
    color: #1890ff;
  }

  .ant-dropdown.slide-down-appear.slide-down-appear-active.ant-dropdown-placement-bottomCenter,
  .ant-dropdown.slide-down-appear.slide-down-appear-active.ant-dropdown-placement-bottomLeft,
  .ant-dropdown.slide-down-appear.slide-down-appear-active.ant-dropdown-placement-bottomRight,
  .ant-dropdown.slide-down-enter.slide-down-enter-active.ant-dropdown-placement-bottomCenter,
  .ant-dropdown.slide-down-enter.slide-down-enter-active.ant-dropdown-placement-bottomLeft,
  .ant-dropdown.slide-down-enter.slide-down-enter-active.ant-dropdown-placement-bottomRight {
    -webkit-animation-name: antslideupin;
    animation-name: antSlideUpIn;
  }

  .ant-dropdown.slide-up-appear.slide-up-appear-active.ant-dropdown-placement-topCenter,
  .ant-dropdown.slide-up-appear.slide-up-appear-active.ant-dropdown-placement-topLeft,
  .ant-dropdown.slide-up-appear.slide-up-appear-active.ant-dropdown-placement-topRight,
  .ant-dropdown.slide-up-enter.slide-up-enter-active.ant-dropdown-placement-topCenter,
  .ant-dropdown.slide-up-enter.slide-up-enter-active.ant-dropdown-placement-topLeft,
  .ant-dropdown.slide-up-enter.slide-up-enter-active.ant-dropdown-placement-topRight {
    -webkit-animation-name: antslidedownin;
    animation-name: antSlideDownIn;
  }

  .ant-dropdown.slide-down-leave.slide-down-leave-active.ant-dropdown-placement-bottomCenter,
  .ant-dropdown.slide-down-leave.slide-down-leave-active.ant-dropdown-placement-bottomLeft,
  .ant-dropdown.slide-down-leave.slide-down-leave-active.ant-dropdown-placement-bottomRight {
    -webkit-animation-name: antslideupout;
    animation-name: antSlideUpOut;
  }

  .ant-dropdown.slide-up-leave.slide-up-leave-active.ant-dropdown-placement-topCenter,
  .ant-dropdown.slide-up-leave.slide-up-leave-active.ant-dropdown-placement-topLeft,
  .ant-dropdown.slide-up-leave.slide-up-leave-active.ant-dropdown-placement-topRight {
    -webkit-animation-name: antslidedownout;
    animation-name: antSlideDownOut;
  }

  .ant-dropdown-link>.anticon.anticon-down,
  .ant-dropdown-trigger>.anticon.anticon-down {
    display: inline-block;
    font-size: 12px;
    font-size: 10px\9;
    transform: scale(0.83333333) rotate(0deg);
  }

  :root .ant-dropdown-link>.anticon.anticon-down,
  :root .ant-dropdown-trigger>.anticon.anticon-down {
    font-size: 12px;
  }

  .ant-dropdown-button {
    white-space: nowrap;
  }

  .ant-dropdown-button.ant-btn-group>.ant-btn:last-child:not(:first-child) {
    padding-right: 8px;
    padding-left: 8px;
  }

  .ant-dropdown-button .anticon.anticon-down {
    display: inline-block;
    font-size: 12px;
    font-size: 10px\9;
    transform: scale(0.83333333) rotate(0deg);
  }

  :root .ant-dropdown-button .anticon.anticon-down {
    font-size: 12px;
  }

  .ant-dropdown-menu-dark,
  .ant-dropdown-menu-dark .ant-dropdown-menu {
    background: #001529;
  }

  .ant-dropdown-menu-dark .ant-dropdown-menu-item,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item .ant-dropdown-menu-submenu-arrow::after,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item>a,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item>a .ant-dropdown-menu-submenu-arrow::after,
  .ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title,
  .ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow::after {
    color: hsla(0, 0%, 100%, 0.65);
  }

  .ant-dropdown-menu-dark .ant-dropdown-menu-item:hover,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item>a:hover,
  .ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title:hover {
    color: #fff;
    background: transparent;
  }

  .ant-dropdown-menu-dark .ant-dropdown-menu-item-selected,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item-selected:hover,
  .ant-dropdown-menu-dark .ant-dropdown-menu-item-selected>a {
    color: #fff;
    background: #1890ff;
  }

  .le5le-topology .pens-props {
    height: 100%;
    box-sizing: border-box;
  }

  .le5le-topology .pens-props .ant-tabs {
    font-size: 12px;
  }

  .le5le-topology .props {
    width: 240px;
    height: 100%;
    border-left: 1px solid #ddd;
    position: relative;
    overflow: hidden;
  }

  .le5le-topology .props .menu {
    width: 100%;
    display: flex;
    padding: 10px 11px 0;
    border-bottom: 1px solid #e5e5e5;
  }

  .le5le-topology .props .menu .item {
    padding: 4px 2px;
    margin-right: 12px;
    cursor: pointer;
    font-size: 12px;
    color: #262626;
    margin-bottom: -1px;
  }

  .le5le-topology .props .menu .item:hover {
    color: #1890ff;
  }

  .le5le-topology .props .menu .active {
    color: #1890ff;
    border-bottom: 2px solid #1890ff;
  }

  .le5le-topology .props .panel-content {
    height: calc(100% - 40px);
    overflow-y: auto;
  }

  .le5le-topology .props .panel-content .group {
    width: 100%;
    padding: 8px 12px;
    border-bottom: 1px solid #e5e5e5;
  }

  .le5le-topology .props .panel-content .group:last-child {
    border-bottom: none;
  }

  .le5le-topology .props .panel-content .group .content>.flex,
  .le5le-topology .props .panel-content .group>.flex {
    flex-wrap: wrap;
    margin: 4px 0;
    align-items: center;
  }

  .le5le-topology .props .panel-content .group .content>.flex.baseline,
  .le5le-topology .props .panel-content .group>.flex.baseline {
    align-items: baseline;
  }

  .le5le-topology .props .panel-content .group .content>.flex> :first-child,
  .le5le-topology .props .panel-content .group>.flex> :first-child {
    min-width: 40%;
    white-space: nowrap;
  }

  .le5le-topology .props .panel-content .group .content>.flex>.full,
  .le5le-topology .props .panel-content .group>.flex>.full {
    width: 60% !important;
  }

  .le5le-topology .props .panel-content .group .title {
    font-size: 12px;
    color: #262626;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .le5le-topology .props .panel-content .group .t-icon {
    font-size: 13px;
    font-weight: 400;
  }

  .le5le-topology .props .btn {
    width: 220px;
  }

  .le5le-topology .props .ant-collapse {
    background: transparent;
  }

  .le5le-topology .props .ant-collapse .ant-collapse-header {
    padding: 12px 10px;
    font-size: 12px;
    font-weight: 600;
  }

  .le5le-topology .props .ant-collapse .ant-collapse-header .ant-collapse-arrow {
    right: 10px;
  }

  .le5le-topology .props .ant-collapse .ant-collapse-header .ant-collapse-extra {
    margin-right: 20px;
  }

  .le5le-topology .props .ant-dropdown-trigger,
  .le5le-topology .props .ant-input,
  .le5le-topology .props .ant-input-number,
  .le5le-topology .props .ant-select {
    text-align: left;
    font-size: 12px;
    line-height: 24px;
    height: 26px;
    width: 100%;
    padding: 0 8px;
    border-radius: 2px;
    border: 1px solid transparent;
    box-shadow: none;
    transition: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .le5le-topology .props .ant-dropdown-trigger.border,
  .le5le-topology .props .ant-input-number.border,
  .le5le-topology .props .ant-input.border,
  .le5le-topology .props .ant-select.border {
    border: 1px solid #e5e5e5;
  }

  .le5le-topology .props .ant-dropdown-trigger:hover,
  .le5le-topology .props .ant-input-number:hover,
  .le5le-topology .props .ant-input:hover,
  .le5le-topology .props .ant-select:hover {
    border: 1px solid transparent !important;
    outline: 1px solid #1890ff;
  }

  .le5le-topology .props .ant-dropdown-trigger.hide,
  .le5le-topology .props .ant-input-number.hide,
  .le5le-topology .props .ant-input.hide,
  .le5le-topology .props .ant-select.hide {
    position: absolute;
    width: 0;
    height: 0;
    border: none;
  }

  .le5le-topology .props textarea.ant-input {
    height: inherit;
  }

  .le5le-topology .props .ant-select-auto-complete.ant-select .ant-select-search--inline {
    line-height: 1;
  }

  .le5le-topology .props .ant-select-auto-complete.ant-select .ant-input {
    border: none !important;
    outline: none;
    padding: 0;
  }

  .le5le-topology .props .ant-select {
    height: inherit;
  }

  .le5le-topology .props .ant-input-number input {
    padding: 0;
  }

  .le5le-topology .props .ant-checkbox-wrapper {
    padding: 0 8px;
  }

  .le5le-topology .props .ant-select-selection--single {
    height: 26px;
    width: 100%;
    padding: 0;
    border-radius: 2px;
    border: none;
    box-shadow: none;
    transition: none;
  }

  .le5le-topology .props .ant-select-selection__rendered {
    margin: 0;
  }

  .le5le-topology .props .ant-select-arrow {
    margin-right: 0;
    right: 0;
  }

  .le5le-topology .props .align {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-around;
  }

  .le5le-topology .props .align label {
    cursor: pointer;
  }

  .le5le-topology .props .ant-collapse-content-box {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    font-size: 12px;
    padding: 0 10px 20px !important;
  }

  .le5le-topology .props .ant-collapse-content-box>.item {
    width: 48%;
    margin-bottom: 8px !important;
    position: relative;
  }

  .le5le-topology .props .ant-collapse-content-box>.item .t-help-circle {
    font-size: 12px;
  }

  .le5le-topology .props .ant-collapse-content-box>.item>span {
    display: block;
    margin-bottom: 4px;
  }

  .le5le-topology .props .ant-collapse-content-box>.item label {
    display: inline-block;
    min-width: 60px;
  }

  .le5le-topology .props .ant-collapse-content-box>.item .ant-btn {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
  }

  .le5le-topology .props .ant-collapse-content-box>.flex {
    width: 100%;
  }

  .le5le-topology .props .ant-collapse-content-box>.flex label {
    width: 60px;
    margin-top: 10px;
  }

  .le5le-topology .props .ant-collapse-content-box>.flex .ant-switch {
    margin-top: 12px;
  }

  .le5le-topology .props .ant-collapse-content-box .background {
    display: flex;
    flex-direction: column;
  }

  .le5le-topology .props .ant-collapse-content-box .background .ant-select {
    width: 100% !important;
  }

  .le5le-topology .props .ant-collapse-borderless>.ant-collapse-item:last-child {
    border-bottom: none;
  }

  .le5le-topology .ant-dropdown-menu-item.line svg {
    height: 18px;
    width: 76px;
  }

  .le5le-topology .prop-modal {
    height: 100%;
  }

  .le5le-topology .prop-modal>.title {
    color: #262626;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    line-height: 1;
    padding: 8px;
    border-bottom: 1px solid #e5e5e5;
  }

  .le5le-topology .prop-modal>.title p {
    font-weight: 700;
    padding: 0 5px;
    margin: 0;
  }

  .le5le-topology .prop-modal>.content {
    height: calc(100% - 40px);
    padding-bottom: 20px;
    overflow-y: auto;
  }

  .le5le-topology {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    text-align: left;
  }

  .le5le-topology>.editor {
    flex-grow: 1;
    overflow: auto;
    display: flex;
  }

  .le5le-topology>.editor>* {
    flex-shrink: 0;
  }

  .le5le-topology>.editor .canvas {
    flex-grow: 1;
  }

  .le5le-topology::-webkit-scrollbar {
    width: 0.06rem;
    height: 0.1rem;
    background: transparent;
  }

  .le5le-topology::-webkit-scrollbar-corner {
    background-color: transparent;
  }

  .le5le-topology::-webkit-scrollbar-thumb {
    background-color: #dedee4;
    border-radius: 0.04rem;
  }

  .le5le-topology::-webkit-scrollbar-thumb:hover {
    background-color: #ccc;
  }

  .le5le-topology::-webkit-scrollbar-track {
    background-color: transparent !important;
  }

  .topology-code .ant-modal-body {
    padding: 0 !important;
    height: 400px;
    overflow: hidden;
  }


  .ant-select-dropdown-menu-item {
    font-size: 13px !important;
  }

  .ant-modal-body {
    max-height: 500px;
    overflow-y: scroll;
  }

  .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
    background-color: #fff !important;
  }
}
