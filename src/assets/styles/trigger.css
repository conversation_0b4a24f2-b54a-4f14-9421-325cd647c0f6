.trigger-content-wrapper ol,
.trigger-content-wrapper ul {
  display: block;
  list-style-type: decimal;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  padding-inline-start: 40px;
}

.trigger-content-wrapper ul {
  list-style-type: disc;
}

.trigger-content-wrapper ol li {
  list-style: decimal;
}

.trigger-content-wrapper ul li {
  list-style: disc;
}

.trigger-box .van-dialog{
  top:50%;
}

.trigger-content-wrapper p {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
}

.trigger-content-wrapper h1,
.trigger-content-wrapper h2,
.trigger-content-wrapper h3,
.trigger-content-wrapper h4,
.trigger-content-wrapper h5,
.trigger-content-wrapper h6 {
  display: block;
  font-weight: bold;
  margin-block-start: 0.67em;
  margin-block-end: 0.67em;
}

.trigger-content-wrapper h1 {
  font-size: 2em;
}

.trigger-content-wrapper h2 {
  font-size: 1.5em;
}

.trigger-content-wrapper h3 {
  font-size: 1.17em;
}

.trigger-content-wrapper h4 {
  font-size: 1em;
}

.trigger-content-wrapper h5 {
  font-size: 0.83em;
}

.trigger-content-wrapper h6 {
  font-size: 0.67em;
}

.trigger-content-wrapper a {
  color: #0000EE;
  text-decoration: underline;
}

.trigger-content-wrapper table {
  display: table;
  border-collapse: separate;
  border-spacing: 2px;
  border-color: gray;
}

.trigger-content-wrapper th,
.trigger-content-wrapper td {
  display: table-cell;
  padding: 1px;
}

.trigger-content-wrapper blockquote {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 40px;
  margin-inline-end: 40px;
}

.trigger-content-wrapper code,
.trigger-content-wrapper pre {
  font-family: monospace;
}

.trigger-content-wrapper pre {
  display: block;
  white-space: pre;
  margin-block-start: 1em;
  margin-block-end: 1em;
}
