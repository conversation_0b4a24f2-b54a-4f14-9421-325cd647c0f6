wrapper()
  //overflow: hidden
  position: absolute
  top: 0
  left: 0
  bottom: 0
  right: 0
  font-size: .32rem

itemBaseStyle()
  min-height: .98rem
  line-height: .98rem
  background-color: #fff
  padding: 0 .32rem
  font-size: .32rem
  .ipt
    text-align left !important
    color: #888
    font-size: .3rem

marginBottom20()
  margin-bottom: .2rem

timelineContent()
  background: #fff
  border-left: 1px solid #e5e5e5
  padding: 10px 0 10px 0.37rem
  margin: -5px 0 -10px 0.45rem

bg-image($url)
  background-image: url($url + ".png")
  @media (-webkit-min-device-pixel-ratio: 3),(min-device-pixel-ratio: 3)
    background-image: url($url + "@2x.png")

border-1px($color = rgba(229,229,229,1), $radius = 0PX, $style = solid)
  position: relative
  &::after
    content: ""
    pointer-events: none
    display: block
    position: absolute
    left: 0
    top: 0
    transform-origin: 0 0
    border-left: 1PX $style $color
    border-bottom: 1PX $style $color
    border-radius: $radius
    box-sizing border-box
    width 100%
    height 100%
    @media (min-resolution: 2dppx)
      width: 200%
      height: 200%
      border-radius: $radius * 2
      transform: scale(.5)
    @media (min-resolution: 3dppx)
      width: 300%
      height: 300%
      border-radius: $radius * 3
      transform: scale(.333)
border-bottom-1px($color = rgba(229,229,229,1), $radius = 0PX, $style = solid)
  position: relative
  &::after
    content: ""
    pointer-events: none
    display: block
    position: absolute
    left: 0
    top: 0
    transform-origin: 0 0
    border-bottom: 1PX $style $color
    border-radius: $radius
    box-sizing border-box
    width 100%
    height 100%
    @media (min-resolution: 2dppx)
      width: 200%
      height: 200%
      border-radius: $radius * 2
      transform: scale(.5)
    @media (min-resolution: 3dppx)
      width: 300%
      height: 300%
      border-radius: $radius * 3
      transform: scale(.333)
