/*
 * @Author: hedd
 * @Date: 2024-01-08 17:03:22
 * @LastEditTime: 2024-09-03 20:59:22
 * @FilePath: \ybs_h5\src\assets\utils\utils.js
 * @Description:
 */
import UAParser from "ua-parser-js";
import wx from "weixin-js-sdk";
import moment from "moment";
var getLocaltionInterval = null;
const uaParer = new UAParser();
class Utils {
  isIos() {
    return uaParer.getOS().name === "iOS";
  }
  isAndroid() {
    return uaParer.getOS().name === "Android";
  }
  // 是否是微信端。
  isWechat() {
    return uaParer.getBrowser().name === "WeChat";
  }
  treeToList(tree) {
    let res = []; // 用于存储递归结果（扁平数据）
    // 递归函数
    let fn = (source) => {
      source.forEach((el) => {
        if (el.children && el.children.length > 0) {
          fn(el.children); // 子级递归
          delete el.children;
          res.push(el);
        } else {
          res.push(el);
        }
      });
    };
    fn(tree);
    return res;
  }
  hasMenuPermission(menuList, permission) {
    // let menuList = JSON.parse(api.getPrefs({
    //     sync: true,
    //     key: 'allMenuForApp'
    // }))
    return menuList.some((v) => {
      return v.pathUrl === permission;
    });
  }
  commonBack(that) {
    if (uaParer.getBrowser().name === "WeChat") {
      // 如果是在微信端。
      that.$router.go(-1);
    } else {
      api.closeWin();
    }
  }
  // 返回事件（建议弃用，返回事件不稳定）
  apiCloudBack(that) {
    if (window.history.state.back) {
      //判断有没有上级路由；
      that.$router.go(-1); //返回上级路由
    } else {
      that.$YBS.apiCloudCloseFrame();
    }
  }
  // 返回至原生
  apiCloudCloseFrame() {
    let h5Mode = localStorage.getItem("h5Mode");
    // 判断当前页面是否在iframe中(同源地址)
    if (h5Mode === "iframe") {
      // 如果当前项目为h5内嵌iframe页面则关闭iframe
      window.parent.postMessage({ type: "closeIframe" }, "*");
    } else if (h5Mode === "apicloud") {
      // 如果当前项目为apicloud内嵌H5页面，返回至原生
      api.sendEvent({
        name: "closeH5Frame",
        extra: {},
      });
      api.closeFrame();
    } else {
      // h5Mode不存在或者不是iframe和apicloud时，默认尝试关闭frame
      try {
        if (typeof api !== 'undefined' && api && api.closeFrame) {
          api.sendEvent({
            name: "closeH5Frame",
            extra: {},
          });
          api.closeFrame();
        }
      } catch (error) {
        console.error('关闭窗口失败:', error);
      }
    }
  }
  // 监听右滑返回事件
  apiCloudEventKeyBack(callback) {
    try {
      api.addEventListener(
        {
          name: "keyback666",
        },
        function (ret, err) {
          callback();
        }
      );
    } catch (err) {
      console.error(err.message);
    }
  }
  // 请求APP权限
  reqPermission(perms, callback) {
    api.requestPermission(
      {
        list: perms,
        code: 100001,
      },
      function (ret, err) {
        if (callback) {
          callback(ret);
          return;
        }
      }
    );
  }
  // APP端是否有某一个权限
  hasPermission(perm) {
    var perms = new Array();
    perms.push(perm);
    var rets = api.hasPermission({
      list: perms,
    });
    if (rets.length == 1) {
      return rets[0].granted;
    }
    return false;
  }
  // 初始化百度地图获取位置
  initBmLocation(that) {
    clearInterval(getLocaltionInterval);
    getLocaltionInterval = null;
    var bmLocation = api.require("bmLocation");
    if (api.systemType == "android") {
      bmLocation.setAgreePrivacy({
        agree: true,
      });
      if (!that.$YBS.hasPermission("location")) {
        bmLocation.configManager({
          accuracy: "device_sensors",
          filter: 1,
          activityType: "automotiveNavigation",
          coordinateType: "GCJ02",
          locationTimeout: 10,
          reGeocodeTimeout: 10,
        });
        that.$YBS.reqPermission(["location"], function (ret) {
          setLocation(bmLocation);
        });
      } else {
        console.log(location + "location");
      }
    } else {
      bmLocation.getPermissionState(function (ret) {
        var sta = ret.code;
      });
    }
    bmLocation.configManager({
      accuracy: "device_sensors",
      filter: 1,
      activityType: "automotiveNavigation",
      coordinateType: "GCJ02",
      locationTimeout: 10,
      reGeocodeTimeout: 10,
    });
    setLocation(bmLocation);
    getLocaltionInterval = setInterval(function () {
      setLocation(bmLocation);
    }, 300000);
  }
  // 调用APP扫码 （获取设备信息
  scanCode() {
    var FNScanner = api.require("FNScanner");
    return new Promise(function (resolve, reject) {
      FNScanner.openScanner(
        {
          autorotation: false, //扫码页面是否自动旋转
          isAlbum: true, //是否隐藏相册按钮
        },
        function (ret, err) {
          if (ret.eventType == "success") {
            // 调用后端接口 参数为扫描成功的参数
            // window.location.href = "./page/detail.html?content="+ content;
            var sacnInfoArr = ret.content.split(",");
            const pattern = /^KJ,/;
            // alert(ret.content);
            if (sacnInfoArr[0] == "iAAS") {
              resolve(sacnInfoArr, "IAAS");
            } else if (sacnInfoArr[0] == "ihsp") {
              resolve(sacnInfoArr);
            } else if (sacnInfoArr[0] == "insp") {
              resolve(sacnInfoArr);
            } else if (sacnInfoArr[0] == "icms") {
              resolve(sacnInfoArr);
            } else if (sacnInfoArr.some((i) => i.includes("spaceCode"))) {
              const codes = ret.content.slice(ret.content.indexOf("?") + 1, ret.content.length).split("&");
              const spaceScanInfo = ["ihsp"];
              codes.forEach((i) => {
                if (i.indexOf("unitCode") != -1) {
                  spaceScanInfo[1] = i.slice(i.indexOf("=") + 1, i.length);
                } else if (i.indexOf("hospitalCode") != -1) {
                  spaceScanInfo[2] = i.slice(i.indexOf("=") + 1, i.length);
                } else if (i.indexOf("spaceCode") != -1) {
                  spaceScanInfo[3] = i.slice(i.lastIndexOf(",") + 1, i.length);
                }
              });
              let spaceCodePattern = /(?:\bspaceCode=)([^&]+)/;
              let spaceCodeMatch = ret.content.match(spaceCodePattern);
              if (spaceCodeMatch && spaceCodeMatch[1]) {
                if(spaceCodeMatch[1].split(',')[0] == 'KJ') {
                  spaceScanInfo[0] = 'ihsp'
                }
                if(spaceCodeMatch[1].split(',')[0] == 'ZC') {
                  spaceScanInfo[0] = 'iaas'
                }
                if(spaceCodeMatch[1].split(',')[0] == 'ZDY') {
                  spaceScanInfo[0] = 'insp'
                }
              }
              resolve(spaceScanInfo);
            } else if (pattern.test(ret.content)) {
              const loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
              const resolveData = ["ihsp", loginInfo.unitCode, loginInfo.hospitalCode, sacnInfoArr[2]];
              resolve(resolveData);
            } else {
              reject();
              // YBS.apiToast("无效的二维码,请检查二维码");
              console.log("无效的二维码,请检查二维码");
            }
          }
          // else {
          //   reject();
          // }
        }
      );
    });
  }
  scanCodeNew(testKJ = false) {
    var FNScanner = api.require("FNScanner");
    return new Promise(function (resolve, reject) {
      FNScanner.openScanner(
        {
          autorotation: false, //扫码页面是否自动旋转
          isAlbum: true, //是否隐藏相册按钮
        },
        function (ret, err) {
          if (ret.eventType == "success") {
            let newData = ret.content
            // 如果检验新版空间码
            if (testKJ) {
              // 定义正则表达式来匹配 URL
              let urlPattern = /^(https?:\/\/[^/]+)/;
              // 定义正则表达式来匹配参数 "spaceCode"
              let spaceCodePattern = /(?:\bspaceCode=)([^&]+)/;
              // 匹配 URL
              let urlMatch = newData.match(urlPattern);
              if (urlMatch) {
                let spaceCodeMatch = newData.match(spaceCodePattern);
                if (spaceCodeMatch) {
                  newData = spaceCodeMatch[1]; // 获取捕获组中的值
                  resolve(newData.split(","));
                }
              }
            }
            var sacnInfoArr = ret.content.split(",");
            resolve(sacnInfoArr);
          }
          // else {
          //   reject();
          // }
        }
      );
    });
  }
  // openCustomDialog(pageParam, callBack) {
  //   api.openFrame({
  //     name: "logout",
  //     url: "widget://html/my/dialog.html",
  //     rect: {
  //       x: 0,
  //       y: 0,
  //       w: api.frameWidth,
  //       h: api.winHeight,
  //     },
  //     // 固定两个参数 title cont
  //     pageParam: pageParam,
  //     bgColor: "rgba(0, 0, 0, 0.6)",
  //     bounces: false,
  //     softInputMode: "resize",
  //   });
  //   api.addEventListener(
  //     {
  //       name: "logoutTure",
  //     },
  //     function (ret, err) {
  //       if (ret) {
  //         callBack();
  //       } else {
  //         console.log(JSON.stringify(err));
  //       }
  //     }
  //   );
  // }
  openCustomDialog(pageParam, callBack) {
    api.openFrame({
      name: "logout",
      url: "widget://html/my/dialog.html",
      rect: {
        x: 0,
        y: 0,
        w: api.frameWidth,
        h: api.winHeight,
      },
      // 固定两个参数 title cont
      pageParam: pageParam,
      bgColor: "rgba(0, 0, 0, 0.6)",
      bounces: false,
      softInputMode: "resize",
    });
    api.addEventListener(
      {
        name: "logoutTure",
      },
      function (ret, err) {
        if (ret) {
          callBack();
        } else {
          console.log(JSON.stringify(err));
        }
      }
    );
  }
  _sum(m, n) {
    return Math.floor(Math.random() * (m - n) + n);
  }
  // 录音上传
  uploadOssFrequency(aliyunOSS, file, upLoadObj, short) {
    var frequencyName = new Date().getTime() + "-" + this._sum(1, 1000);
    let objectKey = upLoadObj.dir + "/" + frequencyName + ".amr";
    let params = {
      file,
      bucketName: "sinomis",
      objectKey,
      uploadType: 1,
    };
    return new Promise(function (resolve, reject) {
      aliyunOSS.upload(params, function (ret, err) {
        if (ret) {
          if (ret.oper == "complete") {
            var path = upLoadObj.dir + "/" + frequencyName + ".amr";
            resolve(path);
          }
        } else {
          reject(err);
        }
      });
    });
  }
  // 获取APP登录信息
  getAppUserInfo() {
    if (!this.isWechat()) {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo",
      });
      userInfo = JSON.parse(userInfo);
      const virtualToken = encodeURIComponent(userInfo.hospitalName);
      localStorage.setItem("token", virtualToken);
      localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      console.log(localStorage.getItem("loginInfo"), "ooo");
    }
  }
  formatDateH(date) {
    return moment(date).format("YYYY年MM月DD日");
  }
  formatDate(date) {
    return moment(date).format("YYYY-MM-DD");
  }
  formatDateTime(date) {
    return moment(date).format("YYYY-MM-DD HH:mm:ss");
  }
  formatDateTimeOther(date) {
    return moment(date).format("YYYY-MM-DD HH:mm");
  }
  getUniqueName() {
    return new Date().valueOf();
  }
  // 数组去重
  unique(arr, flag) {
    var arrCopy = [];
    for (var a = 0; a < arr.length; a++) {
      if (arrCopy.length == 0) {
        arrCopy.push(arr[a]);
      } else {
        var cks = true;
        for (var b = 0; b < arrCopy.length; b++) {
          console.log(arrCopy, b);
          if (arr[a][flag] == arrCopy[b][flag]) {
            cks = false;
            break;
          }
        }
        if (cks) {
          arrCopy.push(arr[a]);
        }
      }
    }
    return arrCopy;
  }
  // 两位小数
  inputFixTwo() {
    let oninput =
      "if(!(/(?!^0\\d)^(\\d{1,8}(\\.\\d{0,2})?)?$/.test(value))&&value.indexOf('.')>0){value=value.slice(0,10)}if(!(/(?!^0\\d)^(\\d{1,8}(\\.\\d{0,2})?)?$/.test(value))&&value.indexOf('.')<0){value=value.slice(0,8)}if(value.includes('-')){value=0}if(isNaN(value)) { value = '' } if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)} if(value < 0){value = 0} ";
    return oninput;
  }
  // 退出公众号
  exitWx() {
    //安卓手机
    document.addEventListener(
      "WeixinJSBridgeReady",
      function () {
        WeixinJSBridge.call("closeWindow");
      },
      false
    );
    //ios手机
    WeixinJSBridge.call("closeWindow");
  }

  /**
   * 将json串转换成树形结构
   * @param a 树dataList
   * @param idStr 树节点id string 'id'
   * @param pidStr 树parentId string '树parentId'
   * @param chindrenStr children string 'children'
   * @returns {Array}
   */
  transData(a, idStr, pidStr, chindrenStr, extraParameter) {
    let r = [],
      hash = {},
      id = idStr,
      pid = pidStr,
      children = chindrenStr,
      i = 0,
      j = 0,
      len = a.length;
    for (; i < len; i++) {
      hash[a[i][id]] = a[i];
    }
    for (; j < len; j++) {
      let aVal = a[j],
        hashVP = hash[aVal[pid]];
      if (hashVP) {
        !hashVP[children] && (hashVP[children] = []);
        hashVP[children].push(aVal);
      } else {
        r.push(aVal);
      }
      //            查找已部署节点id集
      if (extraParameter && aVal.state == "1") extraParameter.push(aVal.id);
    }
    return r;
  }
  // 获取东八时区
  obtainEast8Date() {
    var timezone = 8; //目标时区时间，东八区   东时区正数 西市区负数
    var offset_GMT = new Date().getTimezoneOffset(); // 本地时间和格林威治的时间差，单位为分钟
    var nowDate = new Date().getTime(); // 本地时间距 1970 年 1 月 1 日午夜（GMT 时间）之间的毫秒数
    var targetDate = new Date(nowDate + offset_GMT * 60 * 1000 + timezone * 60 * 60 * 1000);
    return targetDate;
  }
  // 获取缓存信息
  getlocalStorage() {
    return JSON.parse(localStorage.getItem("loginInfo"));
  }
  isObject(exp) {
    return Object.prototype.toString.call(exp) == "[object Object]";
  }
  /**
   * minio 图片前缀改为访问地址前缀
   * @params url === 全拼或半拼图片地址
   * @return 地址全拼
   * */
  imgUrlTranslation(url) {
    const baseAddress = localStorage.getItem("picPrefix");
    // const baseAddress = __PATH.FILEPREFIX;
    // 用户信息未返回可替换的基础地址，直接返回原地址
    if (!baseAddress || !url) return url
    // 定义一个正则表达式来匹配以http或https开头的ip或域名地址
    const fullAddressRegex = /^((https?|ftp):\/\/(?:\d+\.\d+\.\d+\.\d+(?::\d+)?|\w+\.\w+\.\w+(?::\d+)?))/
    // 使用正则表达式检查输入地址是否匹配完整地址格式
    const match = url.match(fullAddressRegex)
    if (match && match.length) {
      const lastBaseAddress = baseAddress[baseAddress.length - 1]
      // 如果匹配到完整地址，提取出半地址部分
      let halfAddress = url.replace(match[0], '')
      // 获取baseAddress的最后一个路径部分（如 'minio'）
      const basePathParts = baseAddress.split('/').filter(Boolean)
      const baseSuffix = basePathParts[basePathParts.length - 1]
      // 获取halfAddress的第一个路径部分
      const halfPathParts = halfAddress.split('/').filter(Boolean)
      // 如果baseAddress有后缀，并且halfAddress以这个后缀开头，则移除重复部分
      if (baseSuffix && halfPathParts[0] === baseSuffix) {
        halfPathParts.shift()
        halfAddress = '/' + halfPathParts.join('/')
      }
      if (lastBaseAddress === '/' && halfAddress[0] === '/') {
        // 如果半地址部分以/开头，则去掉/,否则会重复
        halfAddress = halfAddress.slice(1)
      }
      if (lastBaseAddress !== '/' && halfAddress[0] !== '/') {
        halfAddress = '/' + halfAddress
      }
      // 替换完整地址部分为新地址，同时保留半地址
      const fullAddress = baseAddress + halfAddress
      return fullAddress
    } else {
      // 如果没有匹配到完整地址，则拼接地址
      if (url[0] !== '/') {
        url = '/' + url
      }
      const fullAddress = baseAddress + url
      return fullAddress
    }
  }
  // 提示信息
  apiToast(msg) {
    if (typeof msg == "string") {
      utils.auiToast.text({
        title: msg,
      });
    } else if (typeof msg == "object") {
      utils.auiToast[msg.type || "text"]({
        title: msg.title || "",
        duration: msg.duration,
      });
    }
  }
  //将base64转换为blob
  dataURLtoBlob(dataurl) {
    var arr = dataurl.split(","),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  }

//将blob转换为file
  blobToFile(theBlob, fileName) {
    return new File([theBlob], fileName);
  }
}

let utils = new Utils();
utils.auiToast = {
  create: function (params, callback) {
    var self = this;
    var toastHtml = "";
    switch (params.type) {
      case "success":
        var iconHtml = '<i class="ybs-icon-comment icon-comment-success"></i>';
        break;
      case "warning":
        var iconHtml = '<i class="ybs-icon-comment icon-comment-warning"></i>';
        break;
      case "error":
        var iconHtml = '<i class="ybs-icon-comment icon-comment-error"></i>';
        break;
      case "custom":
        var iconHtml = params.html || "";
        break;
      case "loading":
        var iconHtml = '<div class="ybs-icon-comment icon-common-loading"></div>';
        break;
      case "text":
        var iconHtml = "";
        break;
    }
    var titleHtml = params.title ? '<div class="ybs-toast-content">' + params.title + "</div>" : "";
    toastHtml = '<div class="ybs-toast">' + iconHtml + titleHtml + "</div>";
    if (document.querySelector(".ybs-toast")) return;
    document.body.insertAdjacentHTML("beforeend", toastHtml);
    var duration = params.duration ? params.duration : "2000";
    self.show();
    if (params.type == "loading") {
      if (callback) {
        callback({
          status: "success",
        });
      }
    } else {
      setTimeout(function () {
        self.hide();
      }, duration);
    }
  },
  show: function () {
    var self = this;
    document.querySelector(".ybs-toast").style.display = "block";
    document.querySelector(".ybs-toast").style.marginTop = "-" + Math.round(document.querySelector(".ybs-toast").offsetHeight / 2) + "px";
    if (document.querySelector(".ybs-toast")) return;
  },
  hide: function () {
    var self = this;
    if (document.querySelector(".ybs-toast")) {
      document.querySelector(".ybs-toast").parentNode.removeChild(document.querySelector(".ybs-toast"));
    }
  },
  success: function (params = {}, callback) {
    var self = this;
    params.type = "success";
    return self.create(params, callback);
  },
  warning: function (params = {}, callback) {
    var self = this;
    params.type = "warning";
    return self.create(params, callback);
  },
  error: function (params = {}, callback) {
    var self = this;
    params.type = "error";
    return self.create(params, callback);
  },
  custom: function (params = {}, callback) {
    var self = this;
    params.type = "custom";
    return self.create(params, callback);
  },
  loading: function (params = {}, callback) {
    var self = this;
    params.type = "loading";
    return self.create(params, callback);
  },
  text: function (params = {}, callback) {
    var self = this;
    params.type = "text";
    return self.create(params, callback);
  },
};
// 权限申请页面参数
const permissionPageParamList = {
  storage: {
    title: '读取照片使用说明',
    cont: '用于选择、上传头像或巡检报修实勘图等场景'
  },
  camera: {
    title: "摄像机权限使用说明",
    cont: "用于扫描巡检码、空间码、拍照上传等场景",
  },
  microphone: {
    title: "录音权限使用说明",
    cont: "用于录制现场音频或语音记录等场景",
  }
}
// 监听设备权限 permission: 权限名称 pageParam: 权限申请页面参数 callback: 回调函数
utils.monitorDevicePermissions = function (permission, pageParam = {}, callback = () => {}) {
  console.log(permission + "权限申请");
  if (Object.keys(pageParam).length === 0) {
    pageParam = permissionPageParamList[permission];
  }
  if (!utils.hasPermission(permission)) {
    utils.openCustomDialog(pageParam, function() {
      utils.reqPermission([permission], function (ret) {
        if (ret && ret.list.length > 0 && ret.list[0].granted) {
          console.log(permission + "权限申请成功");
          callback()
        }
      });
    })
  } else {
    console.log(permission + "权限已获取");
  }
};
// 设置当前定位
function setLocation(bmLocation) {
  bmLocation.singleLocation(
    {
      reGeocode: true,
      netWorkState: false,
    },
    function (ret) {
      var sta = ret.status;
      if (sta) {
        var location = ret.reGeo.province == ret.reGeo.city ? ret.reGeo.city : ret.reGeo.province + ret.reGeo.city;
        location = location + ret.reGeo.district + ret.reGeo.street + ret.reGeo.streetNumber;
        localStorage.setItem("location", location);
        console.log(location + "location");
      } else {
        localStorage.removeItem("location");
      }
    }
  );
}
export default utils;
