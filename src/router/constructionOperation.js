// 施工作业
const constructionOperation = () => import("@/pages/constructionOperation/index.vue");
// 单位管理
const unitManagement = () => import("@/pages/constructionOperation/unitManagement.vue");
// 新增单位
const addUnit = () => import("@/pages/constructionOperation/addUnit.vue");
// 人员管理
const personnelManagement = () => import("@/pages/constructionOperation/personnelManagement.vue");
// 新增人员
const addPersonnel = () => import("@/pages/constructionOperation/addPersonnel.vue");
// 作业申请
const workOrderApply = () => import("@/pages/constructionOperation/workOrderApply.vue");
// 作业申请成功
const successPage = () => import("@/pages/constructionOperation/successPage.vue");
// 待办审批列表
const approvalList = () => import("@/pages/constructionOperation/approvalList.vue");
// 审批详情
const approvalDetails = () => import("@/pages/constructionOperation/approvalDetails.vue");
// 审批记录
const approvalRecord = () => import("@/pages/constructionOperation/approvalRecord.vue");
// 安全巡查
const safetyPatrol = () => import("@/pages/constructionOperation/inspection/patrolList.vue");
// 安全巡查详情
const safetyPatroDetail = () => import("@/pages/constructionOperation/inspection/patroDetail.vue");
// 随手查详情
const freePatroDetail = () => import("@/pages/constructionOperation/inspection/freePatroDetail.vue");
// 巡查记录
const patroRecode = () => import("@/pages/constructionOperation/inspection/patroRecode.vue");
// 作业台账
const workAccount = () => import("@/pages/constructionOperation/workAccount.vue");
// 作业台账详情
const workAccountDetails = () => import("@/pages/constructionOperation/workAccountDetails.vue");

export default [
    {
        path: "/constructionOperation",
        name: "constructionOperation",
        component: constructionOperation,
        meta: {
            title: "施工作业",
            isWrite: true
        }
    },
    {
        path: "/unitManagement",
        name: "unitManagement",
        component: unitManagement,
        meta: {
            title: "施工单位管理"
        }
    },
    {
        path: "/addUnit",
        name: "addUnit",
        component: addUnit,
        meta: {
            title: "施工单位新增"
        }
    },
    {
        path: "/personnelManagement",
        name: "personnelManagement",
        component: personnelManagement,
        meta: {
            title: "人员管理"
        }
    },
    {
        path: "/addPersonnel",
        name: "addPersonnel",
        component: addPersonnel,
        meta: {
            title: "人员新增"
        }
    },
    {
        path: "/workOrderApply",
        name: "workOrderApply",
        component: workOrderApply,
        meta: {
            title: "作业申请"
        }
    },
    {
        path: "/successPage",
        name: "successPage",
        component: successPage,
        meta: {
            title: "作业申请成功"
        }
    },
    {
        path: "/approvalList",
        name: "approvalList",
        component: approvalList,
        meta: {
            title: "待办审批列表"
        }
    },
    {
        path: "/conApprovalDetails",
        name: "conApprovalDetails",
        component: approvalDetails,
        meta: {
            title: "审批详情"
        }
    },
    {
        path: "/approvalRecord",
        name: "approvalRecord",
        component: approvalRecord,
        meta: {
            title: "审批记录"
        }
    },
    {
        path: "/safetyPatrol",
        name: "safetyPatrol",
        component: safetyPatrol,
        meta: {
            title: "施工巡检"
        }
    },
    {
        path: "/safetyPatroDetail",
        name: "safetyPatroDetail",
        component: safetyPatroDetail,
        meta: {
            title: "安全巡查"
        }
    },
    {
        path: "/freePatroDetail",
        name: "freePatroDetail",
        component: freePatroDetail,
        meta: {
            title: "随手查"
        }
    },
    {
        path: "/patroRecode",
        name: "patroRecode",
        component: patroRecode,
        meta: {
            title: "巡查记录"
        }
    },
    {
        path: "/workAccount",
        name: "workAccount",
        component: workAccount,
        meta: {
            title: "作业台账"
        }
    },
    {
        path: "/workAccountDetails",
        name: "workAccountDetails",
        component: workAccountDetails,
        meta: {
            title: "作业台账详情"
        }
    },
]