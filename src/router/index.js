/*
 * @Author: hedd
 * @Date: 2023-04-27 10:50:41
 * @LastEditTime: 2024-11-18 15:46:59
 * @FilePath: \ybs_h5\src\router\index.js
 * @Description:
 */
import Vue from "vue";
import Router from "vue-router";
import constructionOperation from "./constructionOperation"; // 施工作业模块页面
const HospList = () => import("@/pages/admin/hospital/HospList");
const HospListNew = () => import("@/pages/admin/hospitalNew/HospList");
const DeptList = () => import("@/pages/admin/department/DeptList");
const UnitDeptList = () => import("@/pages/admin/department/UnitDeptList");
const TeamList = () => import("@/pages/admin/teamList/TeamList");
const OutsourcedCoList = () => import("@/pages/admin/outsourcedCompany/OutsourcedCoList");

// import RepairWorkOrder from "@/pages/form/newForm/Repair";
const CleaningWorkOrder = () => import("@/pages/form/newForm/Cleaning");
const CleaningWorkOrderPro = () => import("@/pages/form/newForm/CleaningProNew");
const CustomTask = () => import("@/pages/form/newForm/customTask");
const customWater = () => import("@/pages/form/newForm/customWater");
const TransportWorkOrder = () => import("@/pages/form/newForm/Transport");
const ComplainWorkOrder = () => import("@/pages/form/newForm/Complain");
const Site = () => import("@/common/customize/search/SearchCompontent");
const SelectArea = () => import("@/pages/form/newForm/selectServiceArea/ServiceArea");
const SelectAreaNew = () => import("@/pages/form/newForm/selectServiceArea/ServiceAreaNew");
const ServiceMatters = () => import("@/pages/form/newForm/components/ServiceMatters");
const ScanningRepair = () => import("@/pages/form/newForm/ScanningRepair");
const parkingLotanagemen = () => import("@/pages/form/parkingLotanagemen/parkingLotanagemen");
const meterReadingManagement = () => import("@/pages/form/meterReadingManagement/meterReadingManagement");
const meterReadingDetailed = () => import("@/pages/form/meterReadingManagement/meterReadingDetailed");
const bedDetail = () => import("@/pages/form/bed/bedDetail");

const carApply = () => import("@/pages/form/officialCar/carApply");
const carMessage = () => import("@/pages/form/officialCar/carMessage");
const dateTime = () => import("@/pages/form/officialCar/dateTime");

const VieOderInfo = () => import("@/pages/form/formDetails/VieOder");
const CompletedInfo = () => import("@/pages/form/formDetails/Completed");
const Consumables = () => import("@/pages/form/formDetails/consumables/Consumables");
const DesignatePersonnel = () => import("@/pages/form/formDetails/designatePersonnel/DesignatePersonnel");

const PersonalCenter = () => import("@/pages/mine/PersonalCenter/Mine");
const MyOrder = () => import("@/pages/mine/myOrder/MyOrder");
const notCompleteOrderList = () => import("@/pages/mine/myOrder/notCompleteOrderList");

const MyWorkbench = () => import("@/pages/mine/MyWorkbench/components/OrderList");
const ServiceDepartment = () => import("@/pages/mine/MyWorkbench/components/ServiceDepartment");
const ServiceOfficetment = () => import("@/pages/mine/MyWorkbench/components/ServiceOfficetment");
const MyData = () => import("@/pages/mine/PersonalCenter/myData/MyData");
const EditInfoPage = () => import("@/pages/mine/PersonalCenter/myData/EditInfo");
const Calendar = () => import("@/common/customize/Calendar");
const ComplaintList = () => import("@/pages/mine/viewComplaint/ComplaintList");
const ComplainDetails = () => import("@/pages/mine/viewComplaint/ComplainDetails");
const Feedback = () => import("@/pages/mine/feedback/Feedback.vue");
const FeedbackDetails = () => import("@/pages/mine/feedback/FeedbackDetails.vue");
const Reply = () => import("@/pages/mine/feedback/Reply.vue");
const userRights = () => import("@/pages/mine/feedback/userRights.vue");

const ShowDetails = () => import("@/pages/mine/myOrder/details/MyOrderTaskDetails");
const ShowDeskDetails = () => import("@/pages/mine/MyWorkbench/details/MyWorkbenchFinishedTaskDetails");
const CancelPage = () => import("@/pages/mine/cancelOrder/CancelPage");
const CancelReason = () => import("@/pages/mine/cancelOrder/CancelReason");

/*临时用户B（关注公众号但没注册和登录）*/
const RepairAtWill = () => import("@/pages/form/newForm/RepairAtWill2");

const Protocol = () => import("@/pages/protocol/Protocol");
const ImproveInfo = () => import("@/pages/mine/PersonalCenter/myData/ImproveInfo");
const SetPwd = () => import("@/pages/mine/PersonalCenter/SetPwd");
const SetTel = () => import("@/pages/mine/PersonalCenter/SetTel");
const mainHome = () => import("@/pages/main/mainHome");
const YifeiSignature = () => import("@/pages/yifei/Signature");
const YifeiDetails = () => import("@/pages/yifei/Details");
const YifeiMonthly = () => import("@/pages/yifei/Monthly");

const YfNewSignature = () => import("@/pages/yfNew/SignatureNew");
const CollectionRecord = () => import("@/pages/yfNew/collectionRecord");

const OrderHandle = () => import("@/pages/form/formDetails/OrderHandle");

const OptionsTrouble = () => import("@/pages/completion/OptionsTrouble");
const Renewal = () => import("@/pages/completion/renewal");
const FaultCause = () => import("@/pages/completion/faultCause");
const ReviseOder = () => import("@/pages/form/formDetails/ReviseOder");
const SourcesDeptName = () => import("@/pages/form/formDetails/components/SourcesDeptName");
const deptSelectedPage = () => import("@/pages/form/newForm/deptSelectedPage");
const alarmAck = () => import("@/pages/form/formDetails/alarmAck");
const orderFinished = () => import("@/pages/form/formDetails/components/orderFinished");

// 设备管理
const equipmentManagement = () => import("@/pages/equipmentManagement/equipmentManagement");
const taskList = () => import("@/pages/equipmentManagement/taskList");
const taskDetail = () => import("@/pages/equipmentManagement/taskDetail");
const taskContent = () => import("@/pages/equipmentManagement/taskContent");
const taskResult = () => import("@/pages/equipmentManagement/taskResult");
const equipmentReportRepair = () => import("@/pages/equipmentManagement/equipmentReportRepair");
const hiddenTroubleReport = () => import("@/pages/equipmentManagement/hiddenTroubleReport");
const equipmentList = () => import("@/pages/equipmentManagement/equipmentList");
const deviceList = () => import("@/pages/equipmentManagement/deviceManagement/deviceList");
const deviceInfo = () => import("@/pages/equipmentManagement/deviceManagement/deviceInfo");
const deviceOverview = () => import("@/pages/equipmentManagement/deviceManagement/deviceOverview");
const inspectionFeedback = () => import("@/pages/equipmentManagement/feedback");
const serviceOverview = () => import("@/pages/form/newForm/serviceOverview");
const taskAnalysis = () => import("@/pages/equipmentManagement/taskAnalysis");
const completeTask = () => import("@/pages/equipmentManagement/completeTask");
const completeDetail = () => import("@/pages/equipmentManagement/components/completeDetail");
const assetsAnalysis = () => import("@/pages/equipmentManagement/assetsAnalysis");
const assetsTaskAnalysis = () => import("@/pages/equipmentManagement/assetsTaskAnalysis");

// 风险管理
const myBillApproval = () => import("@/pages/form/newForm/myBillApproval");
const approvalDetails = () => import("@/pages/form/newForm/approvalDetails");
const BillApproval = () => import("@/pages/form/newForm/BillApproval");
const cancelApplication = () => import("@/pages/form/newForm/cancelApplication");
const riskDetail = () => import("@/pages/riskManagement/riskDetail");
const orderIndex = () => import("@/pages/form/newForm/orderIndex");
const orderAcceptance = () => import("@/pages/form/newForm/orderAcceptance");
const acceptanceConfirm = () => import("@/pages/form/newForm/acceptanceConfirm");
const workOrderDetails = () => import("@/pages/form/newForm/workOrderDetails");
const workOrderBack = () => import("@/pages/form/newForm/workOrderBack");
const account = () => import("@/pages/riskManagement/account");
const accountDetail = () => import("@/pages/riskManagement/accountDetail");
const medicalWasteType = () => import("@/pages/dataCenter/medicalWaste/medicalWasteType");
const medicalWasteOption = () => import("@/pages/dataCenter/medicalWaste/medicalWasteOption");
const giveAnAlarmStatistics = () => import("@/pages/dataCenter/giveAnAlarm/giveAnAlarmStatistics");

// import giveAnAlarmList from "@/pages/dataCenter/giveAnAlarm/giveAnAlarmList"; // 告警统计
// 空间管理
const spaceManagement = () => import("@/pages/spaceManagement/spaceManagement");
const spaceLedger = () => import("@/pages/spaceManagement/spaceLedger");
const departmentLedger = () => import("@/pages/spaceManagement/departmentLedger");
const spaceStatistics = () => import("@/pages/spaceManagement/spaceStatistics");
const spaceEnergyType = () => import("@/pages/spaceManagement/spaceEnergyType");
const spaceDetails = () => import("@/pages/spaceManagement/spaceDetails");
const spatialChangeRecord = () => import("@/pages/spaceManagement/spatialChangeRecord");

const repairArea = () => import("@/pages/dataCenter/oneStop/repairArea");
const fixCost = () => import("@/pages/dataCenter/oneStop/fixCost");
const fixMatter = () => import("@/pages/dataCenter/oneStop/fixMatter");
const consumablesAnalisis = () => import("@/pages/dataCenter/oneStop/consumablesAnalisis");
const teamFixCount = () => import("@/pages/dataCenter/oneStop/teamFixCount");
const workOrderList = () => import("@/pages/dataCenter/oneStop/workOrderList");
const workOrderDetail = () => import("@/pages/dataCenter/oneStop/workOrderDetail");

// 教育培训
const educationalTraining = () => import("@/pages/educationalTraining/educationalTraining");
const questionnaireList = () => import("@/pages/educationalTraining/questionnaire/questionnaireList");
const replyQuestion = () => import("@/pages/educationalTraining/questionnaire/replyQuestion");

const todoItemsOrder = () => import("@/pages/todoItemsOrder/index");

const coldAndHot = () => import("@/pages/monitor/coldAndHot/index.vue");
const drainage = () => import("@/pages/monitor/drainage/index.vue");
const environmental = () => import("@/pages/monitor/environmental/index.vue");
const environmentalDetails = () => import("@/pages/monitor/environmental/details.vue");

const lighting = () => import("@/pages/monitor/lighting/index.vue");
const airConditioner = () => import("@/pages/monitor/airConditioner/index.vue");
const newAirConditioner = () => import("@/pages/monitor/newAirConditioner/index.vue");
const airCooledHeatPumps = () => import("@/pages/monitor/airCooledHeatPumps/index.vue");
const cEquipmentList = () => import("@/pages/monitor/newAirConditioner/equipmentList.vue");

const callThePolice = () => import("@/pages/monitor/airConditioner/callThePolice.vue");
const UPSmonitor = () => import("@/pages/monitor/UPSmonitor/index.vue");
const powerDistribution = () => import("@/pages/monitor/powerDistribution/index.vue");
const deviceOperating = () => import("@/pages/monitor/powerDistribution/deviceOperating.vue");
const powerDetails = () => import("@/pages/monitor/powerDistribution/powerDetails.vue");
const monitorGroup = () => import("@/pages/monitor/components/monitorGroup.vue");
const monitorEntityPage = () => import("@/pages/monitor/components/monitorEntityPage.vue");
const monitorRunList = () => import("@/pages/monitor/components/monitorEnity/monitorRunList.vue");
const abnormalList = () => import("@/pages/monitor/components/abnormalList/abnormalList.vue");

const boilerMonitor = () => import("@/pages/monitor/boiler/index.vue");
const steamSupply = () => import("@/pages/monitor/boiler/steamSupply.vue");
const boilerDetails = () => import("@/pages/monitor/boiler/boilerDetails.vue");

const securityCommonMonitor = () => import("@/pages/monitor/securityMonitor/commonMonitor/index.vue");
const electricitySafetyMonitor = () => import("@/pages/monitor/securityMonitor/electricitySafety/index.vue");
const monitorDetails = () => import("@/pages/monitor/securityMonitor/monitorDetails/index.vue");

// 数据中心--能耗
const energyType = () => import("@/pages/dataCenter/energy/energyType");
const energyCost = () => import("@/pages/dataCenter/energy/energyCost");

const linenstatistics = () => import("@/pages/dataCenter/linen/linenstatistics");

const oxygenStatistics = () => import("@/pages/dataCenter/oxygenStatistics");

const spaceApplication = () => import("@/pages/spaceManagement/spaceApplication/index.vue");
const addApplication = () => import("@/pages/spaceManagement/spaceApplication/addApplication.vue");
const applicationDetails = () => import("@/pages/spaceManagement/spaceApplication/applicationDetails.vue");

// 空间管理-空间清查
const spaceCheck = () => import("@/pages/spaceManagement/spaceCheck/index.vue");
const taskDetails = () => import("@/pages/spaceManagement/spaceCheck/taskDetails.vue");
const deptDetails = () => import("@/pages/spaceManagement/spaceCheck/deptDetails.vue");
const spaceEnroll = () => import("@/pages/spaceManagement/spaceCheck/spaceEnroll.vue");
const idleSpace = () => import("@/pages/spaceManagement/spaceCheck/idleSpace.vue");

const engineeringManagement = () => import("@/pages/engineeringManagement/engineeringManagement.vue");
const projectManagement = () => import("@/pages/engineeringManagement/projectManagement/projectManagement.vue");
const projectDetails = () => import("@/pages/engineeringManagement/projectManagement/projectDetails.vue");
const projectReport = () => import("@/pages/engineeringManagement/projectManagement/projectReport.vue");
const hiddenTroubleRectification = () => import("@/pages/engineeringManagement/hiddenTroubleRectification/hiddenTroubleRectification.vue");
const hiddenTroubleRectificationList = () => import("@/pages/engineeringManagement/hiddenTroubleRectification/hiddenTroubleRectificationList.vue");
const hiddenTroubleDetails = () => import("@/pages/engineeringManagement/hiddenTroubleRectification/hiddenTroubleDetails.vue");
const rectificationSheet = () => import("@/pages/engineeringManagement/hiddenTroubleRectification/rectificationSheet.vue");
const replySheet = () => import("@/pages/engineeringManagement/hiddenTroubleRectification/replySheet.vue");
const documentApproval = () => import("@/pages/engineeringManagement/examineApprove/documentApproval.vue");
const batchApprovalProgress = () => import("@/pages/engineeringManagement/examineApprove/batchApprovalProgress.vue");
const finalAcceptanceApproval = () => import("@/pages/engineeringManagement/examineApprove/finalAcceptanceApproval.vue");
const finalAcceptance = () => import("@/pages/engineeringManagement/examineApprove/finalAcceptance.vue");
const qualityRectification = () => import("@/pages/engineeringManagement/examineApprove/qualityRectification.vue");
const receiptPayment = () => import("@/pages/engineeringManagement/examineApprove/receiptPayment.vue");
const newOrder = () => import("@/pages/form/newForm/newOrder");

const problemContract = () => import("@/pages/engineeringManagement/examineApprove/problemContract.vue");
const advancePayment = () => import("@/pages/engineeringManagement/examineApprove/advancePayment.vue");
const qualityControl = () => import("@/pages/engineeringManagement/examineApprove/qualityControl.vue");
const intermediateAcceptance = () => import("@/pages/engineeringManagement/intermediateAcceptance/intermediateAcceptance.vue");
const intermediateAcceptanceList = () => import("@/pages/engineeringManagement/intermediateAcceptance/intermediateAcceptanceList.vue");

const intermediateAcceptanceAddandEdit = () => import("@/pages/engineeringManagement/intermediateAcceptance/intermediateAcceptanceAddandEdit.vue");
const intermediateAcceptanceAddandAdd = () => import("@/pages/engineeringManagement/intermediateAcceptance/intermediateAcceptanceAddandAdd.vue");
const intermediateAcceptanceAddandDetails = () => import("@/pages/engineeringManagement/intermediateAcceptance/intermediateAcceptanceAddandDetails.vue");

const selectDepartment = () => import("@/pages/engineeringManagement/intermediateAcceptance/selectDepartment.vue");
const contractManagement = () => import("@/pages/engineeringManagement/contractManagement/contractManagement.vue");
const contractManagementList = () => import("@/pages/engineeringManagement/contractManagement/contractManagementList.vue");
const contractManagementEdit = () => import("@/pages/engineeringManagement/contractManagement/contractManagementEdit.vue");
const problemContractMark = () => import("@/pages/engineeringManagement/contractManagement/problemContractMark.vue");

const qualityInspection = () => import("@/pages/engineeringManagement/qualityInspection/qualityInspection.vue");
const qualityInspectionList = () => import("@/pages/engineeringManagement/qualityInspection/qualityInspectionList.vue");
const qualityInspectionAdd = () => import("@/pages/engineeringManagement/qualityInspection/qualityInspectionAdd.vue");
const qualityInspectionEdit = () => import("@/pages/engineeringManagement/qualityInspection/qualityInspectionEdit.vue");
const qualityInspectionDetails = () => import("@/pages/engineeringManagement/qualityInspection/qualityInspectionDetails.vue");
const examineApprove = () => import("@/pages/engineeringManagement/examineApprove/examineApprove.vue");
const evaluation = () => import("@/pages/mine/MyWorkbench/details/evaluation.vue");
const drillTask = () => import("@/pages/drill/drillTask/index.vue");
const drillTaskDetail = () => import("@/pages/drill/drillTask/drillTaskDetail.vue");
const drillTaskAssess = () => import("@/pages/drill/drillTask/drillTaskAssess.vue");
const drillInform = () => import("@/pages/drill/taskInform/drillInform.vue");
const changeDrillInform = () => import("@/pages/drill/taskInform/changeDrillInform.vue");
const regularPlan = () => import("@/pages/drill/regularPlan/index.vue");
const planDetails = () => import("@/pages/drill/regularPlan/planDetails.vue");

const spaceDetail = () => import("@/pages/equipmentManagement/components/spaceDetail.vue");
const zdyPointDetail = () => import("@/pages/equipmentManagement/components/zdyPointDetail.vue");
const orderPage = () => import("@/pages/order/order.vue");
//扫描上传文件
const scanUploadFiles = () => import("@/pages/drill/scanFiles/index.vue");
// 报警中心
const alarmList = () => import("@/pages/alarmCenter/index.vue");
const screening = () => import("@/pages/alarmCenter/screening.vue");
const alarmDetails = () => import("@/pages/alarmCenter/alarmDetails.vue");
const dealPoliceSituation = () => import("@/pages/alarmCenter/dealPoliceSituation.vue");
const summaryAnalysis = () => import("@/pages/alarmCenter/summaryAnalysis.vue");
const alarmTaskList = () => import("@/pages/alarmCenter/alarmTaskList.vue");

// 空间设施登记
const SpaceDeviceReg = () => import("@/pages/spaceEquReg/index.vue");
const EquDetail = () => import("@/pages/spaceEquReg/equDetail.vue");
const EquItemDetail = () => import("@/pages/spaceEquReg/equItemDetail.vue");
const spaceFacilities = () => import("@/pages/spaceEquReg/spaceFacilities.vue");
const belongSpace = () => import("@/pages/spaceEquReg/belongSpace.vue");
const transitionRegister = () => import("@/pages/spaceEquReg/transitionRegister.vue");
const submitRecord = () => import("@/pages/spaceEquReg/submitRecord.vue");
const submitRecordDetail = () => import("@/pages/spaceEquReg/submitRecordDetail.vue");
const SelectEqu = () => import("@/pages/spaceEquReg/selectEqu.vue");
const SelectType = () => import("@/pages/spaceEquReg/selectType.vue");
const SelectTypes = () => import("@/pages/spaceEquReg/selectTypes.vue");
// 安防实验室教育培训
const laboratoryTraining = () => import("@/pages/laboratoryTraining/laboratoryTraining");
const courseList = () => import("@/pages/laboratoryTraining/openCurser/courseList");
const courseDetail = () => import("@/pages/laboratoryTraining/openCurser/courseDetail");
const courseVideo = () => import("@/pages/laboratoryTraining/openCurser/courseVideo");
const seeFile = () => import("@/pages/laboratoryTraining/openCurser/seeFile");
const trainIndex = () => import("@/pages/laboratoryTraining/train/trainIndex");
const trainDetail = () => import("@/pages/laboratoryTraining/train/trainDetail");
const signIndex = () => import("@/pages/laboratoryTraining/train/signIndex");
const courseIndex = () => import("@/pages/laboratoryTraining/myCourse/courseIndex");
const myTaskIndex = () => import("@/pages/laboratoryTraining/myTask/myTaskIndex");
const myTaskDetail = () => import("@/pages/laboratoryTraining/myTask/myTaskDetail");
const examinationIndex = () => import("@/pages/laboratoryTraining/examination/examinationIndex");
const examinationInDetail = () => import("@/pages/laboratoryTraining/examination/examinationInDetail");
const questionsList = () => import("@/pages/laboratoryTraining/examination/questionsList");
const examination = () => import("@/pages/laboratoryTraining/openCurser/examination");

const ReportType = () => import("@/pages/spaceEquReg/reportType.vue");
const EquRegResult = () => import("@/pages/spaceEquReg/equRegResult.vue");
Vue.use(Router);
//领用申请单
const applicationForm = () => import("@/pages/applicationForm/index.vue");
const selectMaterial = () => import("@/pages/applicationForm/selectMaterial.vue");
const submitDetails = () => import("@/pages/applicationForm/submitDetails.vue");
const myRequisition = () => import("@/pages/applicationForm/myRequisition.vue");
const myAccessRequest = () => import("@/pages/applicationForm/myAccessRequest.vue");
const myAccessRequestDetails = () => import("@/pages/applicationForm/myAccessRequestDetails.vue");
const cancelWarehousing = () => import("@/pages/applicationForm/cancelWarehousing.vue");
const arrival = () => import("@/pages/form/formDetails/arrival.vue");
const warehouseInventory = () => import("@/pages/warehouseInventory/index.vue");
const warehouseInventoryDetail = () => import("@/pages/warehouseInventory/detail.vue");
const warehouseInventoryReview = () => import("@/pages/warehouseInventory/review.vue");
export default new Router({
  mode: "history",
  base: "/ihcrsYBSApp/",
  routes: [
    {
      path: "*",
      name: "404",
      component: () => import("@/pages/404"),
      meta: {
        title: "404"
      }
    },
    {
      path: "/yfNewSignature",
      name: "yfNewSignature",
      component: YfNewSignature,
      meta: {
        title: "医废核对"
      }
    },
    {
      path: "/hospList",
      name: "HospList",
      component: HospList,
      meta: {
        title: "请选择医院"
      }
    },
    {
      path: "/hospListNew",
      name: "HospListNew",
      component: HospListNew,
      meta: {
        title: "请选择医院"
      }
    },
    {
      path: "/collectionRecord",
      name: "CollectionRecord",
      component: CollectionRecord,
      meta: {
        title: "科室收集记录"
      }
    },

    {
      path: "/teamList",
      name: "TeamList",
      component: TeamList,
      meta: {
        title: "请选择服务班组"
      }
    },
    {
      path: "/department",
      name: "DeptList",
      component: DeptList,
      meta: {
        title: "请选择部门"
      }
    },
    {
      path: "/UnitDeptList",
      name: "UnitDeptList",
      component: UnitDeptList,
      meta: {
        title: "请选择部门"
      }
    },
    {
      path: "/outsourcedCompany/:hospitalCode/:unitCode",
      name: "OutsourcedCoList",
      component: OutsourcedCoList,
      meta: {
        title: "请选择服务公司"
      }
    },
    {
      path: "/RepairAtWill",
      name: "RepairAtWill",
      component: RepairAtWill,
      meta: {
        title: "随手拍",
        auth: true, // 设置当前路由需要校验   不需要校验的路由就不用写了；
        keepAlive: true
      }
    },
    {
      path: "/repair",
      name: "CleaningWorkOrder",
      component: CleaningWorkOrder,
      meta: {
        title: "综合维修",
        auth: true, // 设置当前路由需要校验   不需要校验的路由就不用写了；
        keepAlive: true
      }
    },
    {
      path: "/repairPro",
      name: "CleaningWorkOrderPro",
      component: CleaningWorkOrderPro,
      meta: {
        title: "综合维修",
        auth: true, // 设置当前路由需要校验   不需要校验的路由就不用写了；
        keepAlive: true
      }
    },
    //CustomTask
    {
      path: "/task",
      name: "CustomTask",
      component: CustomTask,
      meta: {
        auth: true, // 设置当前路由需要校验   不需要校验的路由就不用写了；
        keepAlive: true
      }
    },
    {
      path: "/water",
      name: "customWater",
      component: customWater,
      meta: {
        auth: true, // 设置当前路由需要校验   不需要校验的路由就不用写了；
        keepAlive: true
      }
    },
    {
      path: "/cleaning",
      name: "CleaningWorkOrder",
      component: CleaningWorkOrder,
      meta: {
        title: "综合服务",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/scanningRepair",
      name: "ScanningRepair",
      component: ScanningRepair,
      meta: {
        title: "综合维修",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/parkingLotanagemen",
      name: "parkingLotanagemen",
      component: parkingLotanagemen,
      meta: {
        title: "停车场运营管理",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/bedDetail",
      name: "bedDetail",
      component: bedDetail,
      meta: {
        title: "床位当前入住人信息",
        auth: true,
        keepAlive: false,
        isWrite: true
      }
    },
    {
      path: "/meterReadingManagement",
      name: "meterReadingManagement",
      component: meterReadingManagement,
      meta: {
        title: "抄表管理",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/meterReadingDetailed",
      name: "meterReadingDetailed",
      component: meterReadingDetailed,
      meta: {
        title: "抄表管理详情",
        auth: true,
        keepAlive: false
      }
    },

    {
      path: "/service",
      name: "CleaningWorkOrder",
      component: CleaningWorkOrder,
      meta: {
        title: "应急保洁",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/transport",
      name: "TransportWorkOrder",
      component: TransportWorkOrder,
      meta: {
        title: "运送服务",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/selectArea",
      name: "SelectArea",
      component: SelectArea,
      meta: {
        title: "选择服务区域"
      }
    },
    {
      path: "/selectAreaNew",
      name: "SelectAreaNew",
      component: SelectAreaNew,
      meta: {
        title: "选择服务区域"
      }
    },
    {
      path: "/site",
      name: "Site",
      component: Site,
      meta: {
        title: "选择服务地点"
      }
    },
    {
      path: "/complain",
      name: "ComplainWorkOrder",
      component: ComplainWorkOrder,
      meta: {
        title: "后勤投诉",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/matterlists",
      name: "ServiceMatters",
      component: ServiceMatters,
      meta: {
        title: "服务事项",
        keepAlive: false
      }
    },
    {
      path: "/vieform",
      name: "VieOderInfo",
      component: VieOderInfo,
      meta: {
        title: "工单详情",
        auth: true,
        keepAlive: true
      }
    }, //ReviseOder
    {
      path: "/reviseOder",
      name: "ReviseOder",
      component: ReviseOder,
      meta: {
        title: "工单修改",
        keepAlive: true
      }
    },
    {
      path: "/completed",
      name: "CompletedInfo",
      component: CompletedInfo,
      meta: {
        title: "工单详情",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/consumables",
      name: "Consumables",
      component: Consumables,
      meta: {
        title: "请选择耗材"
      }
    },
    {
      path: "/personnel",
      name: "DesignatePersonnel",
      component: DesignatePersonnel,
      meta: {
        title: "指派人员列表"
      }
    },
    {
      path: "/protocol",
      name: "Protocol",
      component: Protocol,
      meta: {
        title: "用户隐私说明"
      }
    },
    {
      path: "/personalCenter",
      name: "PersonalCenter",
      component: PersonalCenter,
      meta: {
        title: "个人中心",
        auth: true
      }
    },
    {
      path: "/mydata",
      name: "MyData",
      component: MyData,
      meta: {
        title: "个人中心",
        keepAlive: true
      }
    },
    {
      path: "/edit",
      name: "EditInfoPage",
      component: EditInfoPage,
      meta: {
        title: "个人中心"
      }
    },
    {
      path: "/setPwd",
      name: "SetPwd",
      component: SetPwd,
      meta: {
        title: "设置密码"
      }
    },
    {
      path: "/setTel",
      name: "SetTel",
      component: SetTel,
      meta: {
        title: "设置手机号"
      }
    },
    {
      path: "/myorder",
      name: "MyOrder",
      component: MyOrder,
      meta: {
        title: "我的工单",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/notCompleteOrderList",
      name: "notCompleteOrderList",
      component: notCompleteOrderList,
      meta: {
        title: "未完工工单",
        auth: true,
        keepAlive: true
      }
    },

    {
      path: "/workbench",
      name: "MyWorkbench",
      component: MyWorkbench,
      meta: {
        title: "我的工单",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/serviceDepartment",
      name: "ServiceDepartment",
      component: ServiceDepartment,
      meta: {
        title: "工单筛选",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/serviceOfficetment",
      name: "ServiceOfficetment",
      component: ServiceOfficetment,
      meta: {
        title: "工单筛选",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/time",
      name: "Calendar",
      component: Calendar,
      meta: {
        title: "选择日期"
      }
    },
    {
      path: "/details",
      name: "ShowDetails",
      component: ShowDetails,
      meta: {
        title: "工单详情",
        auth: true,
        keepAlive: false // 需要缓存
      }
    },
    {
      path: "/deskDetails",
      name: "ShowDeskDetails",
      component: ShowDeskDetails,
      meta: {
        title: "工单详情",
        auth: true
      }
    },
    {
      path: "/complaintList",
      name: "ComplaintList",
      component: ComplaintList,
      meta: {
        title: "后勤投诉",
        keepAlive: true
      }
    },
    {
      path: "/complainDetails",
      name: "ComplainDetails",
      component: ComplainDetails,
      meta: {
        title: "工单详情",
        keepAlive: true
      }
    },
    {
      path: "/cancel",
      name: "CancelPage",
      component: CancelPage,
      meta: {
        title: "取消工单",
        keepAlive: true
      }
    },
    {
      path: "/reason",
      name: "CancelReason",
      component: CancelReason,
      meta: {
        title: "选择取消原因"
      }
    },
    {
      path: "/improveInfo",
      name: "ImproveInfo",
      component: ImproveInfo,
      meta: {
        title: "个人中心",
        keepAlive: true
      }
    },
    {
      path: "/mainHome",
      name: "mainHome",
      component: mainHome,
      meta: {
        title: "综合服务中心"
      }
    },
    {
      path: "/yifeiSignature",
      name: "YifeiSignature",
      component: YifeiSignature,
      meta: {
        title: "医废核对"
      }
    },
    {
      path: "/yifeiMonthly",
      name: "YifeiMonthly",
      component: YifeiMonthly,
      meta: {
        title: "医废月报"
      }
    },
    {
      path: "/yifeiDetails",
      name: "YifeiDetails",
      component: YifeiDetails,
      meta: {
        title: "医废详情"
      }
    },
    {
      path: "/OrderHandle",
      name: "OrderHandle",
      component: OrderHandle,
      meta: {
        title: "工单处理",
        // auth: true,
        keepAlive: true
      }
    },
    {
      path: "/optionsTrouble",
      name: "OptionsTrouble",
      component: OptionsTrouble,
      meta: {
        title: "选择故障原因"
      }
    },
    {
      path: "/renewal",
      name: "Renewal",
      component: Renewal,
      meta: {
        title: "更新"
      }
    },
    {
      path: "/faultCause",
      name: "FaultCause",
      component: FaultCause,
      meta: {
        title: "故障原因"
      }
    },
    {
      path: "/feedback",
      name: "Feedback",
      component: Feedback,
      meta: {
        title: "职工意见箱"
      }
    },
    {
      path: "/FeedbackDetails",
      name: "FeedbackDetails",
      component: FeedbackDetails,
      meta: {
        title: "职工意见箱",
        keepAlive: true
      }
    },
    {
      path: "/reply",
      name: "Reply",
      component: Reply,
      meta: {
        title: "职工意见箱"
      }
    },
    {
      path: "/userRights",
      name: "userRights",
      component: userRights,
      meta: {
        title: "用户须知与条款"
      }
    },
    {
      path: "/deptName",
      name: "SourcesDeptName",
      component: SourcesDeptName,
      meta: {
        title: "选择科室"
      }
    },
    {
      path: "/deptSelectedPage",
      name: "deptSelectedPage",
      component: deptSelectedPage,
      meta: {
        title: "选择科室"
      }
    },
    {
      path: "/carApply",
      name: "carApply",
      component: carApply,
      meta: {
        title: "公车预定"
      }
    },
    {
      path: "/carMessage",
      name: "carMessage",
      component: carMessage,
      meta: {
        title: "公车预定"
      }
    },
    {
      path: "/dateTime",
      name: "dateTime",
      component: dateTime,
      meta: {
        title: "公车预定"
      }
    },
    {
      path: "/alarmAck",
      name: "alarmAck",
      component: alarmAck,
      meta: {
        title: "工单详情"
      }
    },
    {
      path: "/orderFinished",
      name: "orderFinished",
      component: orderFinished,
      meta: {
        title: "工单详情"
      }
    },
    {
      path: "/equipmentManagement",
      name: "equipmentManagement",
      component: equipmentManagement,
      meta: {
        title: "设备管理"
      }
    },
    {
      path: "/taskList",
      name: "taskList",
      component: taskList,
      meta: {
        title: "任务列表"
      }
    },
    {
      path: "/taskDetail",
      name: "taskDetail",
      component: taskDetail,
      meta: {
        title: "任务详情"
      }
    },
    {
      path: "/taskContent",
      name: "taskContent",
      component: taskContent,
      meta: {
        title: "巡检内容"
      }
    },
    {
      path: "/taskResult",
      name: "taskResult",
      component: taskResult,
      meta: {
        title: "巡检结果"
      }
    },
    {
      path: "/inspectionFeedback",
      name: "inspectionFeedback",
      component: inspectionFeedback,
      meta: {
        title: "报修反馈"
      }
    },
    {
      path: "/taskAnalysis",
      name: "taskAnalysis",
      component: taskAnalysis,
      meta: {
        title: "任务统计",
        keepAlive: true
      }
    },
    {
      path: "/completeTask",
      name: "completeTask",
      component: completeTask,
      meta: {
        title: "已巡检任务"
      }
    },
    {
      path: "/completeDetail",
      name: "completeDetail",
      component: completeDetail,
      meta: {
        title: "任务详情"
      }
    },
    {
      path: "/equipmentReportRepair",
      name: "equipmentReportRepair",
      component: equipmentReportRepair,
      meta: {
        title: "设备报修",
        keepAlive: true
      }
    },
    {
      path: "/hiddenTroubleReport",
      name: "hiddenTroubleReport",
      component: hiddenTroubleReport,
      meta: {
        title: "隐患上报",
        auth: true, // 设置当前路由需要校验   不需要校验的路由就不用写了；
        keepAlive: true
      }
    },
    {
      path: "/equipmentList",
      name: "equipmentList",
      component: equipmentList,
      meta: {
        title: "列表"
      }
    },
    {
      path: "/deviceList",
      name: "deviceList",
      component: deviceList,
      meta: {
        title: "设备管理"
      }
    },
    {
      path: "/deviceInfo",
      name: "deviceInfo",
      component: deviceInfo,
      meta: {
        title: "设备详情"
      }
    },
    {
      path: "/deviceOverview",
      name: "deviceOverview",
      component: deviceOverview,
      meta: {
        title: "资产管理"
      }
    },
    {
      path: "/serviceOverview",
      name: "serviceOverview",
      component: serviceOverview,
      meta: {
        title: "服务总览"
      }
    },
    {
      path: "/riskDetail",
      name: "riskDetail",
      component: riskDetail,
      meta: {
        title: "风险详情"
      }
    },
    {
      path: "/orderIndex",
      name: "orderIndex",
      component: orderIndex,
      meta: {
        title: "工单管理"
      }
    },
    {
      path: "/orderAcceptance",
      name: "orderAcceptance",
      component: orderAcceptance,
      meta: {
        title: "工单验收"
      }
    },
    {
      path: "/acceptanceConfirm",
      name: "acceptanceConfirm",
      component: acceptanceConfirm,
      meta: {
        title: "验收确认"
      }
    },
    {
      path: "/myBillApproval",
      name: "myBillApproval",
      component: myBillApproval,
      meta: {
        title: "我的审批"
      }
    },
    {
      path: "/cancelApplication",
      name: "cancelApplication",
      component: cancelApplication,
      meta: {
        title: "取消挂单申请"
      }
    },
    {
      path: "/approvalDetails",
      name: "approvalDetails",
      component: approvalDetails,
      meta: {
        title: "审批详情"
      }
    },
    {
      path: "/BillApproval",
      name: "BillApproval",
      component: BillApproval,
      meta: {
        title: "挂单审批"
      }
    },
    {
      path: "/workOrderDetails",
      name: "workOrderDetails",
      component: workOrderDetails,
      meta: {
        title: "工单详情",
        auth: true // 设置当前路由需要校验   不需要校验的路由就不用写了；
        // keepAlive: true
      }
    },
    {
      // 工单回退
      path: "/workOrderBack",
      name: "workOrderBack",
      component: workOrderBack,
      meta: {
        title: "工单回退"
      }
    },
    {
      path: "/todoItemsOrder",
      name: "todoItemsOrder",
      component: todoItemsOrder,
      meta: {
        title: "待办事项"
      }
    },
    {
      path: "/account",
      name: "account",
      component: account,
      meta: {
        title: "重点台账"
      }
    },
    {
      path: "/accountDetail",
      name: "accountDetail",
      component: accountDetail,
      meta: {
        title: "台账详情"
      }
    },
    {
      path: "/medicalWasteType",
      name: "medicalWasteType",
      component: medicalWasteType,
      meta: {
        title: "医废类型分析"
      }
    },
    {
      path: "/medicalWasteOption",
      name: "medicalWasteOption",
      component: medicalWasteOption,
      meta: {
        title: "科室产废分析"
      }
    },
    {
      path: "/spaceManagement",
      name: "spaceManagement",
      component: spaceManagement,
      meta: {
        title: "空间管理"
      }
    },
    {
      path: "/spaceLedger",
      name: "spaceLedger",
      component: spaceLedger,
      meta: {
        title: "空间台账"
      }
    },
    {
      path: "/spaceDeviceReg",
      name: "spaceDeviceReg",
      component: SpaceDeviceReg,
      meta: {
        title: "空间设施登记"
      }
    },
    {
      path: "/equDetail",
      name: "equDetail",
      component: EquDetail,
      meta: {
        title: "设施登记"
      }
    },
    {
      path: "/equItemDetail",
      name: "equItemDetail",
      component: EquItemDetail,
      meta: {
        title: "设备详情"
      }
    },
    {
      path: "/selectType",
      name: "selectType",
      component: SelectType,
      meta: {
        title: "选择类型"
        // keepAlive: true // 需要缓存
      }
    },
    {
      path: "/selectTypes",
      name: "selectTypes",
      component: SelectTypes,
      meta: {
        title: "选择类型"
        // keepAlive: true // 需要缓存
      }
    },
    {
      path: "/selectEqu",
      name: "selectEqu",
      component: SelectEqu,
      meta: {
        title: "选择设备"
      }
    },

    {
      path: "/reportType",
      name: "reportType",
      component: ReportType,
      meta: {
        title: "上报设备分类"
      }
    },
    {
      path: "/equRegResult",
      name: "equRegResult",
      component: EquRegResult,
      meta: {
        title: "提交设施登记"
      }
    },
    {
      path: "/spaceFacilities",
      name: "spaceFacilities",
      component: spaceFacilities,
      meta: {
        title: "空间位置选择"
      }
    },
    {
      path: "/belongSpace",
      name: "belongSpace",
      component: belongSpace,
      meta: {
        title: "空间设施登记"
      }
    },
    {
      path: "/transitionRegister",
      name: "transitionRegister",
      component: transitionRegister,
      meta: {
        title: "变动记录"
      }
    },
    {
      path: "/submitRecord",
      name: "submitRecord",
      component: submitRecord,
      meta: {
        title: "提交记录"
      }
    },
    {
      path: "/submitRecordDetail",
      name: "submitRecordDetail",
      component: submitRecordDetail,
      meta: {
        title: "提交记录详情"
      }
    },

    {
      path: "/departmentLedger",
      name: "departmentLedger",
      component: departmentLedger,
      meta: {
        title: "部门台账"
      }
    },
    {
      path: "/spaceStatistics",
      name: "spaceStatistics",
      component: spaceStatistics,
      meta: {
        title: "空间统计"
      }
    },
    {
      path: "/spaceEnergyType",
      name: "spaceEnergyType",
      component: spaceEnergyType,
      meta: {
        title: "选择空间空能类型"
      }
    },
    {
      path: "/spaceDetails",
      name: "spaceDetails",
      component: spaceDetails,
      meta: {
        title: "空间详情"
      }
    },
    {
      path: "/spatialChangeRecord",
      name: "spatialChangeRecord",
      component: spatialChangeRecord,
      meta: {
        title: "空间变更记录"
      }
    },
    {
      path: "/repairArea",
      name: "repairArea",
      component: repairArea,
      meta: {
        title: "报修区域分析"
      }
    },
    {
      path: "/fixCost",
      name: "fixCost",
      component: fixCost,
      meta: {
        title: "维修成本分析"
      }
    },
    {
      path: "/fixMatter",
      name: "fixMatter",
      component: fixMatter,
      meta: {
        title: "维修事项分析"
      }
    },
    {
      path: "/consumablesAnalisis",
      name: "consumablesAnalisis",
      component: consumablesAnalisis,
      meta: {
        title: "维修耗材分析"
      }
    },
    {
      path: "/teamFixCount",
      name: "teamFixCount",
      component: teamFixCount,
      meta: {
        title: "班组维修量"
      }
    },
    {
      path: "/workOrderList",
      name: "workOrderList",
      component: workOrderList,
      meta: {
        title: "工单列表"
      }
    },
    {
      path: "/workOrderDetail",
      name: "workOrderDetail",
      component: workOrderDetail,
      meta: {
        title: "工单详情"
      }
    },
    {
      path: "/educationalTraining",
      name: "educationalTraining",
      component: educationalTraining,
      meta: {
        title: "教育培训"
      }
    },
    {
      path: "/questionnaireList",
      name: "questionnaireList",
      component: questionnaireList,
      meta: {
        title: "调查问卷"
      }
    },
    {
      path: "/replyQuestion",
      name: "replyQuestion",
      component: replyQuestion,
      meta: {
        title: "调查问卷",
        isWrite: true
      }
    },
    {
      path: "/coldAndHot",
      name: "coldAndHot",
      component: coldAndHot,
      meta: {
        title: "冷热源监测",
        keepAlive: true // 需要缓存
      }
    },
    {
      path: "/drainage",
      name: "drainage",
      component: drainage,
      meta: {
        title: "给排水监测",
        keepAlive: true // 需要缓存
      }
    },
    {
      path: "/energyType",
      name: "energyType",
      component: energyType,
      meta: {
        title: "电耗统计"
      }
    },
    {
      path: "/energyCost",
      name: "energyCost",
      component: energyCost,
      meta: {
        title: "费用统计"
      }
    },
    {
      path: "/oxygenStatistics",
      name: "oxygenStatistics",
      component: oxygenStatistics,
      meta: {
        title: "用氧统计"
      }
    },
    {
      path: "/environmental",
      name: "environmental",
      component: environmental,
      meta: {
        title: "环境监测"
      }
    },
    {
      path: "/environmentalDetails",
      name: "environmentalDetails",
      component: environmentalDetails,
      meta: {
        title: "环境监测"
      }
    },
    {
      path: "/lighting",
      name: "lighting",
      component: lighting,
      meta: {
        title: "照明监控"
      }
    },
    // {
    //   path: "/airConditioner",
    //   name: "airConditioner",
    //   component: airConditioner,
    //   meta: {
    //     title: "空调监测",
    //     keepAlive: true // 需要缓存
    //   }
    // },
    {
      path: "/airConditioner",
      name: "airConditioner",
      component: newAirConditioner,
      meta: {
        title: "空调末端",
        keepAlive: true // 需要缓存
      }
    },
    {
      path: "/airCooledHeatPumps",
      name: "airCooledHeatPumps",
      component: airCooledHeatPumps,
      meta: {
        title: "风冷热泵监测",
        keepAlive: true // 需要缓存
      }
    },
    {
      path: "/cEquipmentList",
      name: "cEquipmentList",
      component: cEquipmentList,
      meta: {
        title: "设备列表"
      }
    },
    {
      path: "/monitorGroup",
      name: "monitorGroup",
      component: monitorGroup,
      meta: {
        title: "运行分组"
      }
    },
    {
      path: "/monitorEntityPage",
      name: "monitorEntityPage",
      component: monitorEntityPage,
      meta: {
        title: "运行监测"
      }
    },
    {
      path: "/callThePolice",
      name: "callThePolice",
      component: callThePolice,
      meta: {
        title: "报警清单"
      }
    },
    {
      path: "/monitorRunList",
      name: "monitorRunList",
      component: monitorRunList,
      meta: {
        title: "运行清单"
      }
    },
    {
      path: "/abnormalList",
      name: "abnormalList",
      component: abnormalList,
      meta: {
        title: "故障清单"
      }
    },
    {
      path: "/UPSmonitor",
      name: "UPSmonitor",
      component: UPSmonitor,
      meta: {
        title: "UPS监测"
      }
    },
    {
      path: "/powerDistribution",
      name: "powerDistribution",
      component: powerDistribution,
      meta: {
        title: "配电监测",
        keepAlive: true // 需要缓存
      }
    },
    {
      path: "/deviceOperating",
      name: "deviceOperating",
      component: deviceOperating,
      meta: {
        title: "配电监测"
      }
    },
    {
      path: "/powerDetails",
      name: "powerDetails",
      component: powerDetails,
      meta: {
        title: "配电监测"
      }
    },
    {
      path: "/giveAnAlarmStatistics",
      name: "giveAnAlarmStatistics",
      component: giveAnAlarmStatistics,
      meta: {
        title: "告警统计",
        keepAlive: true // 需要缓存
      }
    },
    // {
    //   path: "/giveAnAlarmList",
    //   name: "giveAnAlarmList",
    //   component: giveAnAlarmList,
    //   meta: {
    //     title: "告警统计"
    //   }
    // },
    {
      path: "/linenstatistics",
      name: "linenstatistics",
      component: linenstatistics,
      meta: {
        title: "布草统计"
      }
    },
    {
      path: "/boilerMonitor",
      name: "boilerMonitor",
      component: boilerMonitor,
      meta: {
        title: "锅炉监测"
      }
    },
    {
      path: "/steamSupply",
      name: "steamSupply",
      component: steamSupply,
      meta: {
        title: "供汽监测"
      }
    },
    {
      path: "/boilerDetails",
      name: "boilerDetails",
      component: boilerDetails,
      meta: {
        title: "锅炉监测详情"
      }
    },
    {
      path: "/sensationMonitor",
      name: "sensationMonitor",
      component: securityCommonMonitor,
      meta: {
        title: "烟感温感"
      }
    },
    {
      path: "/fireProofMonitor",
      name: "fireProofMonitor",
      component: securityCommonMonitor,
      meta: {
        title: "防火门"
      }
    },
    {
      path: "/oneKeyMonitor",
      name: "oneKeyMonitor",
      component: securityCommonMonitor,
      meta: {
        title: "一键报警"
      }
    },
    {
      path: "/manualMonitor",
      name: "manualMonitor",
      component: securityCommonMonitor,
      meta: {
        title: "消防手报"
      }
    },
    {
      path: "/electricitySafetyMonitor",
      name: "electricitySafetyMonitor",
      component: electricitySafetyMonitor,
      meta: {
        title: "用电安全"
      }
    },
    {
      path: "/monitorDetails",
      name: "monitorDetails",
      component: monitorDetails,
      meta: {
        title: "监测设备详情"
      }
    },
    {
      path: "/spaceApplication",
      name: "spaceApplication",
      component: spaceApplication,
      meta: {
        title: "空间申请"
      }
    },
    {
      path: "/addApplication",
      name: "addApplication",
      component: addApplication,
      meta: {
        title: "空间申请"
      }
    },
    {
      path: "/applicationDetails",
      name: "applicationDetails",
      component: applicationDetails,
      meta: {
        title: "空间申请"
      }
    },
    {
      path: "/spaceCheck",
      name: "spaceCheck",
      component: spaceCheck,
      meta: {
        title: "空间清查"
      }
    },
    {
      path: "/taskDetails",
      name: "taskDetails",
      component: taskDetails,
      meta: {
        title: "清查任务详情"
      }
    },
    {
      path: "/deptDetails",
      name: "deptDetails",
      component: deptDetails,
      meta: {
        title: "科室清查详情"
      }
    },
    {
      path: "/spaceEnroll",
      name: "spaceEnroll",
      component: spaceEnroll,
      meta: {
        title: "空间登记"
      }
    },
    {
      path: "/idleSpace",
      name: "idleSpace",
      component: idleSpace,
      meta: {
        title: "空间台账"
      }
    },
    {
      path: "/engineeringManagement",
      name: "engineeringManagement",
      component: engineeringManagement,
      meta: {
        title: "工程管理"
      }
    },
    {
      path: "/projectManagement",
      name: "projectManagement",
      component: projectManagement,
      meta: {
        title: "项目管理"
      }
    },
    {
      path: "/projectDetails",
      name: "projectDetails",
      component: projectDetails,
      meta: {
        title: "项目详情"
      }
    },
    {
      path: "/projectReport",
      name: "projectReport",
      component: projectReport,
      meta: {
        title: "项目报表"
      }
    },
    {
      path: "/hiddenTroubleRectification",
      name: "hiddenTroubleRectification",
      component: hiddenTroubleRectification,
      meta: {
        title: "隐患整改"
      }
    },
    {
      path: "/hiddenTroubleRectificationList",
      name: "hiddenTroubleRectificationList",
      component: hiddenTroubleRectificationList,
      meta: {
        title: "隐患整改"
      }
    },
    {
      path: "/hiddenTroubleDetails",
      name: "hiddenTroubleDetails",
      component: hiddenTroubleDetails,
      meta: {
        title: "隐患整改详情"
      }
    },
    {
      path: "/rectificationSheet",
      name: "rectificationSheet",
      component: rectificationSheet,
      meta: {
        title: "整改单"
      }
    },
    {
      path: "/replySheet",
      name: "replySheet",
      component: replySheet,
      meta: {
        title: "回复单"
      }
    },

    {
      path: "/documentApproval",
      name: "documentApproval",
      component: documentApproval,
      meta: {
        title: "文件审批"
      }
    },
    {
      path: "/batchApprovalProgress",
      name: "batchApprovalProgress",
      component: batchApprovalProgress,
      meta: {
        title: "进度批量审批"
      }
    },
    {
      path: "/finalAcceptance",
      name: "finalAcceptance",
      component: finalAcceptance,
      meta: {
        title: "竣工验收"
      }
    },
    {
      path: "/finalAcceptanceApproval",
      name: "finalAcceptanceApproval",
      component: finalAcceptanceApproval,
      meta: {
        title: "竣工验收审批"
      }
    },
    {
      path: "/qualityRectification",
      name: "qualityRectification",
      component: qualityRectification,
      meta: {
        title: "质量整改"
      }
    },
    {
      path: "/receiptPayment",
      name: "receiptPayment",
      component: receiptPayment,
      meta: {
        title: "收付款填写"
      }
    },
    {
      path: "/newOrder",
      name: "newOrder",
      component: newOrder,
      meta: {
        title: "工单新建",
        keepAlive: true
      }
    },
    {
      path: "/problemContract",
      name: "problemContract",
      component: problemContract,
      meta: {
        title: "问题合同"
      }
    },
    {
      path: "/advancePayment",
      name: "advancePayment",
      component: advancePayment,
      meta: {
        title: "预收付款"
      }
    },
    {
      path: "/qualityControl",
      name: "qualityControl",
      component: qualityControl,
      meta: {
        title: "质量整改"
      }
    },

    {
      path: "/intermediateAcceptance",
      name: "intermediateAcceptance",
      component: intermediateAcceptance,
      meta: {
        title: "中间验收"
      }
    },
    {
      path: "/intermediateAcceptanceList",
      name: "intermediateAcceptanceList",
      component: intermediateAcceptanceList,
      meta: {
        title: "中间验收列表"
      }
    },
    {
      path: "/intermediateAcceptanceAddandEdit",
      name: "intermediateAcceptanceAddandEdit",
      component: intermediateAcceptanceAddandEdit,
      meta: {
        title: "中间验收编辑"
      }
    },
    {
      path: "/intermediateAcceptanceAddandAdd",
      name: "intermediateAcceptanceAddandAdd",
      component: intermediateAcceptanceAddandAdd,
      meta: {
        title: "中间验收新增"
      }
    },
    {
      path: "/intermediateAcceptanceAddandDetails",
      name: "intermediateAcceptanceAddandDetails",
      component: intermediateAcceptanceAddandDetails,
      meta: {
        title: "中间验收查看"
      }
    },
    {
      path: "/selectDepartment",
      name: "selectDepartment",
      component: selectDepartment,
      meta: {
        title: "选择部门/单位"
      }
    },
    {
      path: "/contractManagement",
      name: "contractManagement",
      component: contractManagement,
      meta: {
        title: "合同管理"
      }
    },
    {
      path: "/contractManagementList",
      name: "contractManagementList",
      component: contractManagementList,
      meta: {
        title: "合同管理列表"
      }
    },
    {
      path: "/contractManagementEdit",
      name: "contractManagementEdit",
      component: contractManagementEdit,
      meta: {
        title: "合同编辑"
      }
    },
    {
      path: "/problemContractMark",
      name: "problemContractMark",
      component: problemContractMark,
      meta: {
        title: "标记问题合同"
      }
    },
    {
      path: "/qualityInspection",
      name: "qualityInspection",
      component: qualityInspection,
      meta: {
        title: "质量检查"
      }
    },
    {
      path: "/qualityInspectionList",
      name: "qualityInspectionList",
      component: qualityInspectionList,
      meta: {
        title: "质量检查列表"
      }
    },
    {
      path: "/qualityInspectionAdd",
      name: "qualityInspectionAdd",
      component: qualityInspectionAdd,
      meta: {
        title: "添加质量检查"
      }
    },
    {
      path: "/qualityInspectionEdit",
      name: "qualityInspectionEdit",
      component: qualityInspectionEdit,
      meta: {
        title: "质量检查编辑"
      }
    },
    {
      path: "/qualityInspectionDetails",
      name: "qualityInspectionDetails",
      component: qualityInspectionDetails,
      meta: {
        title: "质量检查查看详情"
      }
    },
    {
      path: "/examineApprove",
      name: "examineApprove",
      component: examineApprove,
      meta: {
        title: "审批"
      }
    },
    {
      path: "/evaluation",
      name: "evaluation",
      component: evaluation,
      meta: {
        title: "工单评价"
      }
    },
    {
      path: "/drillTask",
      name: "drillTask",
      component: drillTask,
      meta: {
        title: "演练任务"
      }
    },
    {
      path: "/drillTaskDetail",
      name: "drillTaskDetail",
      component: drillTaskDetail,
      meta: {
        title: "任务详情"
      }
    },
    {
      path: "/drillTaskAssess",
      name: "drillTaskAssess",
      component: drillTaskAssess,
      meta: {
        title: "任务详情"
      }
    },
    {
      path: "/drillInform",
      name: "drillInform",
      component: drillInform,
      meta: {
        title: "演练通知"
      }
    },
    {
      path: "/changeDrillInform",
      name: "changeDrillInform",
      component: changeDrillInform,
      meta: {
        title: "更改演练通知"
      }
    },
    {
      path: "/regularPlan",
      name: "regularPlan",
      component: regularPlan,
      meta: {
        title: "常规预案"
      }
    },
    {
      path: "/planDetails",
      name: "planDetails",
      component: planDetails,
      meta: {
        title: "预案详情"
      }
    },
    {
      path: "/spaceDetail",
      name: "spaceDetail",
      component: spaceDetail,
      meta: {
        title: "空间详情"
      }
    },
    {
      path: "/zdyPointDetail",
      name: "zdyPointDetail",
      component: zdyPointDetail,
      meta: {
        title: "巡更点详情"
      }
    },
    {
      path: "/orderPage",
      name: "orderPage",
      component: orderPage,
      meta: {
        title: "订餐页面",
        isWrite: true
      }
    },
    {
      path: "/scanUploadFiles",
      name: "scanUploadFiles",
      component: scanUploadFiles,
      meta: {
        title: "扫描上传文件",
        isWrite: true
      }
    },
    {
      path: "/assetsAnalysis",
      name: "assetsAnalysis",
      component: assetsAnalysis,
      meta: {
        title: "设备分析",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/assetsTaskAnalysis",
      name: "assetsTaskAnalysis",
      component: assetsTaskAnalysis,
      meta: {
        title: "巡检保养统计",
        auth: true,
        keepAlive: true
      }
    },
    {
      path: "/alarmList",
      name: "alarmList",
      component: alarmList,
      meta: {
        title: "全部报警"
      }
    },
    {
      path: "/screening",
      name: "screening",
      component: screening,
      meta: {
        title: "报警筛选"
      }
    },
    {
      path: "/alarmDetails",
      name: "alarmDetails",
      component: alarmDetails,
      meta: {
        title: "报警详情"
      }
    },
    {
      path: "/dealPoliceSituation",
      name: "dealPoliceSituation",
      component: dealPoliceSituation,
      meta: {
        title: "处理警情"
      }
    },
    {
      path: "/summaryAnalysis",
      name: "summaryAnalysis",
      component: summaryAnalysis,
      meta: {
        title: "总结分析"
      }
    },
    {
      path: "/alarmTaskList",
      name: "alarmTaskList",
      component: alarmTaskList,
      meta: {
        title: "人物列表"
      }
    },
    {
      path: "/applicationForm",
      name: "applicationForm",
      component: applicationForm,
      meta: {
        title: "物料申请单",
        keepAlive: true
      }
    },
    {
      path: "/selectMaterial",
      name: "selectMaterial",
      component: selectMaterial,
      meta: {
        title: "选择物料"
      }

    },
    {
      path: "/submitDetails",
      name: "submitDetails",
      component: submitDetails,
      meta: {
        title: "提交详情"
      }
    },
    {
      path: "/myRequisition",
      name: "myRequisition",
      component: myRequisition,
      meta: {
        title: "我的领用"
      }
    },
    {
      path: "/arrival",
      name: "arrival",
      component: arrival,
      meta: {
        title: "确认到达"
      }
    },
    {
      path: "/myAccessRequest",
      name: "myAccessRequest",
      component: myAccessRequest,
      meta: {
        title: "我的出入库申请"
      }
    },
    {
      path: "/myAccessRequestDetails",
      name: "myAccessRequestDetails",
      component: myAccessRequestDetails,
      meta: {
        title: "我的出入库申请详情"
      }
    },
    {
      path: "/cancelWarehousing",
      name: "cancelWarehousing",
      component: cancelWarehousing,
      meta: {
        title: "取消出入库申请"
      }
    },
    {
      path: "/applicationForm/relatedExport",
      name: "relatedExport",
      component: () => import("@/pages/applicationForm/relatedExport.vue"),
      meta: {
        title: "选择关联出库单"
      }
    },
    {
      path: "/applicationForm/exportDetail",
      name: "exportDetail",
      component: () => import("@/pages/applicationForm/exportDetail.vue"),
      meta: {
        title: "出库单详情"
      }
    },
    {
      path: "/laboratoryTraining",
      name: "laboratoryTraining",
      component: laboratoryTraining,
      meta: {
        title: "教育培训"
      }
    },
    {
      path: "/courseList",
      name: "courseList",
      component: courseList,
      meta: {
        title: "公开课程"
      }
    },
    {
      path: "/courseDetail",
      name: "courseDetail",
      component: courseDetail,
      meta: {
        title: "课程详情"
      }
    },
    {
      path: "/courseVideo",
      name: "courseVideo",
      component: courseVideo,
      meta: {
        title: "视频"
      }
    },
    {
      path: "/seeFile",
      name: "seeFile",
      component: seeFile,
      meta: {
        title: "在线学习"
      }
    },
    {
      path: "/trainIndex",
      name: "trainIndex",
      component: trainIndex,
      meta: {
        title: "培训任务"
      }
    },
    {
      path: "/trainDetail",
      name: "trainDetail",
      component: trainDetail,
      meta: {
        title: "培训详情"
      }
    },
    {
      path: "/signIndex",
      name: "signIndex",
      component: signIndex,
      meta: {
        title: "培训签到"
      }
    },
    {
      path: "/courseIndex",
      name: "courseIndex",
      component: courseIndex,
      meta: {
        title: "我的课程"
      }
    },
    {
      path: "/myTaskIndex",
      name: "myTaskIndex",
      component: myTaskIndex,
      meta: {
        title: "我的任务"
      }
    },
    {
      path: "/myTaskDetail",
      name: "myTaskDetail",
      component: myTaskDetail,
      meta: {
        title: "任务详情"
      }
    },
    {
      path: "/examinationIndex",
      name: "examinationIndex",
      component: examinationIndex,
      meta: {
        title: "考试任务"
      }
    },
    {
      path: "/questionsList",
      name: "questionsList",
      component: questionsList,
      meta: {
        title: "在线考试"
      }
    },
    {
      path: "/examinationInDetail",
      name: "examinationInDetail",
      component: examinationInDetail,
      meta: {
        title: "考试详情"
      }
    },
    {
      path: "/examination",
      name: "examination",
      component: examination,
      meta: {
        title: "在线考试"
      }
    },
    {
      path: "/warehouseInventory",
      name: "warehouseInventory",
      component: warehouseInventory,
      meta: {
        title: "库房盘点"
      }
    },
    {
      path: "/warehouseInventoryDetail",
      name: "warehouseInventoryDetail",
      component: warehouseInventoryDetail,
      meta: {
        title: "盘点详情"
      }
    },
    {
      path: "/warehouseInventoryReview",
      name: "warehouseInventoryReview",
      component: warehouseInventoryReview,
      meta: {
        title: "盘点"
      }
    },
    ...constructionOperation,
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { x: 0, y: 0 };
    }
  }
});
