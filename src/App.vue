<template>
  <div id="app">
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive && apiReady"></router-view>
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive && apiReady"></router-view>

    <lg-preview></lg-preview>
  </div>
</template>

<script>
import { getBrowserInfo, pathTransfer } from "./common/lib/util";
import { mapMutations } from "vuex";
import axios from "axios";
export default {
  name: "App",
  data() {
    return {
      timer: "",
      apiReady: false,
      loginInfo: {},
    };
  },
  mounted() {
    $.toast.prototype.defaults.duration = 1500;
    let h5Mode = "";
    // 判断当前页面是否在iframe中(同源地址)
    if (window.self !== window.top) {
      h5Mode = "iframe";
    } else {
      try {
        // 判断是否为apicloud环境
        if (api.constructor === Object && Object.keys(api).length) {
          h5Mode = "apicloud";
        }
      } catch (error) {
        // 单独部署的h5
        h5Mode = "singleh5";
      }
    }
    console.log("h5Mode======>", h5Mode);
    this.$store.commit("setH5Mode", h5Mode);
    document.body.setAttribute("data-layout", h5Mode);
  },
  created() {
    this.timer = setInterval(() => {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
        this.getServiceAreaTreeData();
        this.getStaffInfo();
        clearInterval(this.timer);
      } else {
        // 判断是否为白名单
        this.apiReady = !!this.$route.meta.isWrite;
      }
    }, 100);
  },
  methods: {
    ...mapMutations(["setLoginInfo", "setStaffInfo"]),
    // 初始化获取服务地点
    getServiceAreaTreeData() {
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
      };
      this.$api.getGridTreeData(params).then((res) => {
        // console.log(res);
        this.$store.commit("setServiceAreaTreeData", res);
      });
    },
    getStaffInfo() {
      var oneLogisticInfo = {};
      if (localStorage.getItem("oneLogisticInfo")) {
        oneLogisticInfo = localStorage.getItem("oneLogisticInfo");
      } else {
        // h5调试下获取不到oneLogisticuserInfo，所以这里做个兼容、保证h5调试下也能正常登录
        oneLogisticInfo = JSON.stringify({
          h5DevDebug: true,
        });
      }
      // try {
      //   oneLogisticInfo = api.getPrefs({
      //     sync: true,
      //     key: "oneLogisticuserInfo"
      //   });
      // } catch (error) {
      //   // h5调试下获取不到oneLogisticuserInfo，所以这里做个兼容、保证h5调试下也能正常登录
      //   oneLogisticInfo = JSON.stringify({
      //     h5DevDebug: true
      //   })
      // }
      let staffInfo = oneLogisticInfo ? JSON.parse(oneLogisticInfo) : {};
      // 登录用户有一站式信息才调取登录接口
      if (Object.keys(staffInfo).length) {
        this.$api
          .getNewStaffInfoById({
            id: this.loginInfo.staffId,
          })
          .then((res) => {
            this.apiReady = true;
            const item = res;
            item.staffId = item.id;
            this.setStaffInfo(item);
          }).catch((err) => {
            this.apiReady = true;
          });
      } else {
        this.apiReady = true;
      }
      // Object.assign(staffInfo, {
      //   staffId: staffInfo.id
      // })
      // console.log(staffInfo.id,staffInfo.staffId, 'staffInfo');
      // this.setStaffInfo(staffInfo)
      // this.getServiceAreaTreeData();
    },
  },
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped>
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

* {
  touch-action: pan-y;
}

#app >>> .weui-btn_disabled.weui-btn_primary {
  background-color: $btnColorDis;
  border: 0;
}

#app >>> .weui-btn_primary {
  background-color: $btnColor;
}

#app >>> .weui-navbar__item.weui-bar__item--on {
  background-color: #fff;
  color: #000;
}

#app >>> .weui-navbar__item {
  color: #bbbbbb;
  background: #fff;
}

#app >>> .weui-cells_checkbox .weui-check:checked + .weui-icon-checked:before {
  color: $btnColor;
}

#app >>> .weui-btn_primary:not(.weui-btn_disabled):active {
  background-color: $btnColorDis;
}

#app >>> .weui-cell:before {
  border-color: #fff;
}

#app >>> .weui-dialog__btn {
  color: $color;
}

#app >>> .weui-btn:after {
  border: 1px solid rgba(0, 0, 0, 0);
}

#app >>> .weui-btn {
  border-radius: 2px;
}

#app >>> .weui-cells_radio .weui-check:checked+.weui-icon-checked:before {
  color: $color;
}

#app {
  height: 100%;
}

#app >>> textarea {
  padding: 5px 0;
  word-break: break-word;
}

#app >>> .cube-action-sheet-content {
  max-height: 300px;
  overflow: auto;
}
</style>
<style>
.van-overflow-hidden {
  overflow: visible !important;
  overflow-x: visible !important;
  overflow-y: visible !important;
}
</style>
