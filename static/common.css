#app .content-css {
  font-size: .3rem;
  color: #666;
  line-height: 1;
}
#app .time-wrapper .start,
#app .time-wrapper .end {
  width: 2.8rem;
  text-align: center;
}
#app .time-wrapper .start,
#app .time-wrapper .end,
#app .time-content .time-text {
  color: #666
}
#app .content-css {
  font-size: .3rem;
  color: #666;
  line-height: 1;
}
#app .time-wrapper .start,
#app .time-wrapper .end {
  width: 2.8rem;
  text-align: center;
}
#app .time-wrapper .start,
#app .time-wrapper .end,
#app .time-content .time-text {
  color: #666
}
button[disabled] {
  opacity : 1;
}
.no_allow_touch {
  background-color: rgb(211,211,211) !important;
}
.weui-actionsheet__cell {
  height: 50px;
  box-sizing: border-box;
  line-height: 50px;
  padding: 0 !important;
  text-align: center;
}

.weui-actionsheet__menu {
  max-height: 300px;
  overflow: auto;
}
body {
  width: 100%;
  height: 100%;
}
.desc{
    line-height: 28px !important;
}
.nut-radio input:checked,.nut-checkbox input:checked {
  background-color:#29BEBC !important;
  border-color: #29BEBC !important;
}
.nut-radio input::after,.nut-checkbox input::after {
  background-color: #29BEBC !important;
}
.nut-checkbox.nut-checkbox-size-base input {
  border-radius: 50%;
}
.nut-checkbox input::after {
  background: #29BEBC !important;
}
.nut-picker-cancel-btn{
  width: 60px;
  color: #888888 !important;
}
.nut-picker-confirm-btn {
  color: #29BEBC !important;
}