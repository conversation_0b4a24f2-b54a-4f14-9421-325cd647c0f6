"use strict";
const utils = require("./utils");
const webpack = require("webpack");
const config = require("../config");
const merge = require("webpack-merge");
const path = require("path");
const baseWebpackConfig = require("./webpack.base.conf");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const FriendlyErrorsPlugin = require("friendly-errors-webpack-plugin");
const portfinder = require("portfinder");

const HOST = process.env.HOST;
const PORT = process.env.PORT && Number(process.env.PORT);

const devWebpackConfig = merge(baseWebpackConfig, {
  module: {
    rules: utils.styleLoaders({
      sourceMap: config.dev.cssSourceMap,
      usePostCSS: true
    })
  },
  // cheap-module-eval-source-map is faster for development
  devtool: config.dev.devtool,

  // these devServer options should be customized in /config/index.js
  devServer: {
    inline: true,
    port: 8081,
    clientLogLevel: "warning",
    disableHostCheck: true,
    historyApiFallback: {
      rewrites: [
        {
          from: /.*/,
          to: path.posix.join(config.dev.assetsPublicPath, "index.html")
        }
      ]
    },
    hot: true,
    contentBase: false, // since we use CopyWebpackPlugin.
    compress: true,
    host: HOST || config.dev.host,
    port: PORT || config.dev.port,
    open: config.dev.autoOpenBrowser,
    overlay: config.dev.errorOverlay ? { warnings: false, errors: true } : false,
    publicPath: config.dev.assetsPublicPath,
    proxy: config.dev.proxyTable,
    quiet: true, // necessary for FriendlyErrorsPlugin
    watchOptions: {
      poll: config.dev.poll
    }
  },
  plugins: [
    new webpack.DefinePlugin({
      "process.env": require("../config/dev.env")
    }),
    new webpack.HotModuleReplacementPlugin(),
    new webpack.NamedModulesPlugin(), // HMR shows correct file names in console on update.
    new webpack.NoEmitOnErrorsPlugin(),
    // https://github.com/ampedandwired/html-webpack-plugin
    new HtmlWebpackPlugin({
      filename: "index.html",
      template: "index.html",
      inject: true,
      nodeEnv: "development"
    }),
    // copy custom static assets
    new CopyWebpackPlugin([
      {
        from: path.resolve(__dirname, "../static"),
        to: config.dev.assetsSubDirectory,
        ignore: [".*"]
      }
    ]),
    new webpack.DefinePlugin({
      BUILD_TIME: JSON.stringify(new Date().toLocaleString())
    })
  ]
});

module.exports = new Promise((resolve, reject) => {
  portfinder.basePort = process.env.PORT || config.dev.port;
  portfinder.getPort((err, port) => {
    if (err) {
      reject(err);
    } else {
      // publish the new Port, necessary for e2e tests
      process.env.PORT = port;
      // add port to devServer config
      devWebpackConfig.devServer.port = port;

      // 创建一个自定义插件来在 FriendlyErrorsPlugin 之后显示构建时间
      function CustomBuildTimePlugin() {}
      CustomBuildTimePlugin.prototype.apply = function (compiler) {
        compiler.plugin('done', function (stats) {
          const time = stats.endTime - stats.startTime;
          let timeMessage;

          if (time >= 60000) {
            const minutes = Math.floor(time / 60000);
            const seconds = Math.floor((time % 60000) / 1000);
            timeMessage = `✨ 编译成功，耗时 ${minutes}分${seconds}秒`;
          } else {
            const seconds = Math.floor(time / 1000);
            timeMessage = `✨ 编译成功，耗时 ${seconds}秒`;
          }

          // 延迟一点输出，确保在 FriendlyErrorsPlugin 之后
          setTimeout(() => {
            console.log(timeMessage);
          }, 100);
        });
      };

      // 移除原来的 BuildTimeFormatterPlugin
      devWebpackConfig.plugins = devWebpackConfig.plugins.filter(plugin =>
        plugin.constructor.name !== 'BuildTimeFormatterPlugin'
      );

      // Add FriendlyErrorsPlugin
      devWebpackConfig.plugins.push(
        new FriendlyErrorsPlugin({
          compilationSuccessInfo: {
            messages: [`Your application is running here: http://${devWebpackConfig.devServer.host}:${port}`]
          },
          onErrors: config.dev.notifyOnErrors ? utils.createNotifierCallback() : undefined
        })
      );

      // 添加自定义构建时间插件
      devWebpackConfig.plugins.push(new CustomBuildTimePlugin());

      resolve(devWebpackConfig);
    }
  });
});
