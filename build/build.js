'use strict'
require('./check-versions')()

process.env.NODE_ENV = 'production'

const ora = require('ora')
const rm = require('rimraf')
const path = require('path')
const chalk = require('chalk')
const webpack = require('webpack')
const config = require('../config')
const webpackConfig = require('./webpack.prod.conf')

const spinner = ora('building for production...')
spinner.start()

// 记录开始时间
const startTime = Date.now()

rm(path.join(config.build.assetsRoot, config.build.assetsSubDirectory), err => {
  if (err) throw err
  webpack(webpackConfig, (err, stats) => {
    spinner.stop()
    if (err) throw err

    // 计算构建时间
    const buildTime = Date.now() - startTime
    let timeMessage

    if (buildTime >= 60000) {
      const minutes = Math.floor(buildTime / 60000)
      const seconds = Math.floor((buildTime % 60000) / 1000)
      timeMessage = `${minutes}分${seconds}秒`
    } else {
      const seconds = Math.floor(buildTime / 1000)
      timeMessage = `${seconds}秒`
    }

    process.stdout.write(stats.toString({
      colors: true,
      modules: false,
      children: false, // If you are using ts-loader, setting this to true will make TypeScript errors show up during build.
      chunks: false,
      chunkModules: false
    }) + '\n\n')

    if (stats.hasErrors()) {
      console.log(chalk.red('  Build failed with errors.\n'))
      process.exit(1)
    }

    console.log(chalk.cyan('  Build complete.\n'))
    console.log(chalk.green(`  ✨ 打包成功，耗时 ${timeMessage}\n`))
  })
})
