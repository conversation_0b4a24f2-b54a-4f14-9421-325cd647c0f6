{"name": "wechat", "version": "2.3.0", "description": "", "author": "wd", "private": true, "scripts": {"dev": "webpack-dev-server --host 0.0.0.0 --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js"}, "transformModules": {"cube-ui": {"transform": "cube-ui/lib/${member}", "kebabCase": true, "style": {"ignore": ["create-api", "better-scroll", "locale"]}}}, "dependencies": {"@nutui/nutui": "2.2.10", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "better-scroll": "^1.12.6", "compressorjs": "^1.2.1", "cube-ui": "^1.12.8", "daterangepicker": "^3.0.3", "detector": "^2.5.0", "echarts": "^4.9.0", "element-ui": "^2.15.0", "es6-promise": "^4.2.8", "fastclick": "^1.0.6", "image-base64": "^1.0.2", "image-compressor.js": "^1.1.4", "iview": "^3.2.1", "jquery": "^3.3.1", "jquery-weui": "^1.2.1", "js-base64": "^2.5.0", "mint-ui": "^2.2.13", "moment": "^2.30.1", "node-sass": "^5.0.0", "qs": "^6.5.2", "resize-observer-polyfill": "^1.5.1", "sass-loader": "^6.0.3", "scss": "^0.2.4", "scss-loader": "^0.0.1", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "swiper": "^3.4.2", "ua-parser-js": "^0.7.23", "vant": "^2.12.39", "video.js": "^8.21.0", "vue": "^2.5.2", "vue-hot-reload-api": "^2.3.4", "vue-picture-preview": "^1.2.0", "vue-pull-to": "^0.1.6", "vue-router": "^3.0.1", "vue-video-player": "^5.0.1", "vuex": "^3.0.1", "weui": "^1.1.3"}, "devDependencies": {"@nutui/babel-plugin-separate-import": "^1.2.2", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.3", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-modules": "^0.1.1", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "compression-webpack-plugin": "^1.1.12", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "fs": "0.0.1-security", "html-webpack-plugin": "^2.30.1", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0", "webpack-post-compile-plugin": "^0.4.1", "webpack-transform-modules-plugin": "^0.3.5", "weixin-js-sdk": "^1.3.3"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}